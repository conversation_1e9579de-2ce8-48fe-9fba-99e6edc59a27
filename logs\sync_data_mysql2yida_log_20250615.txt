2025-06-15 01:30:33,395 - INFO - 使用默认增量同步（当天更新数据）
2025-06-15 01:30:33,395 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-15 01:30:33,395 - INFO - 查询参数: ('2025-06-15',)
2025-06-15 01:30:33,473 - INFO - MySQL查询成功，增量数据（日期: 2025-06-15），共获取 0 条记录
2025-06-15 01:30:33,473 - ERROR - 未获取到MySQL数据
2025-06-15 01:31:33,486 - INFO - 开始同步昨天与今天的销售数据: 2025-06-14 至 2025-06-15
2025-06-15 01:31:33,486 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-15 01:31:33,486 - INFO - 查询参数: ('2025-06-14', '2025-06-15')
2025-06-15 01:31:33,611 - INFO - MySQL查询成功，时间段: 2025-06-14 至 2025-06-15，共获取 98 条记录
2025-06-15 01:31:33,611 - INFO - 获取到 1 个日期需要处理: ['2025-06-14']
2025-06-15 01:31:33,611 - INFO - 开始处理日期: 2025-06-14
2025-06-15 01:31:33,611 - INFO - Request Parameters - Page 1:
2025-06-15 01:31:33,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 01:31:33,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 01:31:41,719 - ERROR - 处理日期 2025-06-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 747CE867-5B23-7217-9650-6A6310645BD3 Response: {'code': 'ServiceUnavailable', 'requestid': '747CE867-5B23-7217-9650-6A6310645BD3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 747CE867-5B23-7217-9650-6A6310645BD3)
2025-06-15 01:31:41,719 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-15 01:31:41,719 - INFO - 同步完成
2025-06-15 04:30:33,379 - INFO - 使用默认增量同步（当天更新数据）
2025-06-15 04:30:33,379 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-15 04:30:33,379 - INFO - 查询参数: ('2025-06-15',)
2025-06-15 04:30:33,504 - INFO - MySQL查询成功，增量数据（日期: 2025-06-15），共获取 6 条记录
2025-06-15 04:30:33,504 - INFO - 获取到 1 个日期需要处理: ['2025-06-14']
2025-06-15 04:30:33,504 - INFO - 开始处理日期: 2025-06-14
2025-06-15 04:30:33,504 - INFO - Request Parameters - Page 1:
2025-06-15 04:30:33,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 04:30:33,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 04:30:41,628 - ERROR - 处理日期 2025-06-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 142B1AD3-5E19-72A9-B15E-C2EDBC56390A Response: {'code': 'ServiceUnavailable', 'requestid': '142B1AD3-5E19-72A9-B15E-C2EDBC56390A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 142B1AD3-5E19-72A9-B15E-C2EDBC56390A)
2025-06-15 04:30:41,628 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-15 04:31:41,639 - INFO - 开始同步昨天与今天的销售数据: 2025-06-14 至 2025-06-15
2025-06-15 04:31:41,639 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-15 04:31:41,639 - INFO - 查询参数: ('2025-06-14', '2025-06-15')
2025-06-15 04:31:41,764 - INFO - MySQL查询成功，时间段: 2025-06-14 至 2025-06-15，共获取 128 条记录
2025-06-15 04:31:41,764 - INFO - 获取到 1 个日期需要处理: ['2025-06-14']
2025-06-15 04:31:41,764 - INFO - 开始处理日期: 2025-06-14
2025-06-15 04:31:41,764 - INFO - Request Parameters - Page 1:
2025-06-15 04:31:41,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 04:31:41,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 04:31:42,389 - INFO - Response - Page 1:
2025-06-15 04:31:42,389 - INFO - 第 1 页获取到 37 条记录
2025-06-15 04:31:42,889 - INFO - 查询完成，共获取到 37 条记录
2025-06-15 04:31:42,889 - INFO - 获取到 37 条表单数据
2025-06-15 04:31:42,889 - INFO - 当前日期 2025-06-14 有 122 条MySQL数据需要处理
2025-06-15 04:31:42,889 - INFO - 开始批量插入 85 条新记录
2025-06-15 04:31:43,170 - INFO - 批量插入响应状态码: 200
2025-06-15 04:31:43,170 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 20:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C59DE708-5062-7575-8BB8-8CF849D294D9', 'x-acs-trace-id': 'cba8ce0f0aca732a20af89accedd7ac0', 'etag': '23A2xEo4So5oFFuR1LhqstQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 04:31:43,170 - INFO - 批量插入响应体: {'result': ['FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMJD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMKD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMLD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMMD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMND', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMOD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMPD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMQD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMRD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMSD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMTD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMUD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMVD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMWD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMXD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMYD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMZD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM0E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM1E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM2E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM3E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM4E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM5E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM6E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM7E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM8E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM9E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMAE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMBE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMCE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMDE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMEE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMFE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMGE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMHE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMIE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMJE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMKE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMLE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMME', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMNE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMOE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMPE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMQE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMRE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMSE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMTE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMUE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMVE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMWE']}
2025-06-15 04:31:43,170 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-15 04:31:43,170 - INFO - 成功插入的数据ID: ['FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMJD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMKD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMLD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMMD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMND', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMOD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMPD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMQD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMRD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMSD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMTD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMUD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMVD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMWD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMXD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMYD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMZD', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM0E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM1E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM2E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM3E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM4E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM5E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM6E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM7E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM8E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBM9E', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMAE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMBE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMCE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMDE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMEE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMFE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMGE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMHE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMIE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMJE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMKE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMLE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMME', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMNE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMOE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMPE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMQE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMRE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMSE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMTE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMUE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMVE', 'FINST-OJ666W712R8WNNI398K7W4SB2AOL3ZXKZOWBMWE']
2025-06-15 04:31:48,419 - INFO - 批量插入响应状态码: 200
2025-06-15 04:31:48,419 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 20:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1692', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AEDBA517-EDF5-7671-A889-A9D5A792D2E4', 'x-acs-trace-id': 'c5b68c25faf087183c9becdc8ae9978a', 'etag': '1qxqExtVk99J8o2eZo1TrTQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 04:31:48,419 - INFO - 批量插入响应体: {'result': ['FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMGA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMHA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMIA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMJA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMKA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMLA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMMA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMNA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMOA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMPA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMQA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMRA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMSA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMTA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMUA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMVA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMWA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMXA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMYA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMZA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM0B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM1B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM2B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM3B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM4B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM5B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM6B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM7B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM8B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM9B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMAB', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMBB', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMCB', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMDB', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMEB']}
2025-06-15 04:31:48,419 - INFO - 批量插入表单数据成功，批次 2，共 35 条记录
2025-06-15 04:31:48,419 - INFO - 成功插入的数据ID: ['FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMGA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMHA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMIA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMJA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMKA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMLA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMMA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMNA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMOA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMPA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMQA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMRA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMSA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMTA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMUA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMVA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMWA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMXA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMYA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMZA', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM0B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM1B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM2B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM3B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM4B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM5B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM6B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM7B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM8B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBM9B', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMAB', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMBB', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMCB', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMDB', 'FINST-A17661C1AJ9W1ZU1633KWBI2JFVX3YZOZOWBMEB']
2025-06-15 04:31:53,435 - INFO - 批量插入完成，共 85 条记录
2025-06-15 04:31:53,435 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 85 条，错误: 0 条
2025-06-15 04:31:53,435 - INFO - 数据同步完成！更新: 0 条，插入: 85 条，错误: 0 条
2025-06-15 04:31:53,435 - INFO - 同步完成
2025-06-15 07:30:33,623 - INFO - 使用默认增量同步（当天更新数据）
2025-06-15 07:30:33,623 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-15 07:30:33,623 - INFO - 查询参数: ('2025-06-15',)
2025-06-15 07:30:33,748 - INFO - MySQL查询成功，增量数据（日期: 2025-06-15），共获取 6 条记录
2025-06-15 07:30:33,748 - INFO - 获取到 1 个日期需要处理: ['2025-06-14']
2025-06-15 07:30:33,748 - INFO - 开始处理日期: 2025-06-14
2025-06-15 07:30:33,748 - INFO - Request Parameters - Page 1:
2025-06-15 07:30:33,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 07:30:33,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 07:30:39,732 - INFO - Response - Page 1:
2025-06-15 07:30:39,732 - INFO - 第 1 页获取到 50 条记录
2025-06-15 07:30:40,232 - INFO - Request Parameters - Page 2:
2025-06-15 07:30:40,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 07:30:40,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 07:30:48,342 - ERROR - 处理日期 2025-06-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BC4A7326-3269-7A8B-900F-3748F589CF4E Response: {'code': 'ServiceUnavailable', 'requestid': 'BC4A7326-3269-7A8B-900F-3748F589CF4E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BC4A7326-3269-7A8B-900F-3748F589CF4E)
2025-06-15 07:30:48,342 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-15 07:31:48,357 - INFO - 开始同步昨天与今天的销售数据: 2025-06-14 至 2025-06-15
2025-06-15 07:31:48,357 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-15 07:31:48,357 - INFO - 查询参数: ('2025-06-14', '2025-06-15')
2025-06-15 07:31:48,482 - INFO - MySQL查询成功，时间段: 2025-06-14 至 2025-06-15，共获取 128 条记录
2025-06-15 07:31:48,482 - INFO - 获取到 1 个日期需要处理: ['2025-06-14']
2025-06-15 07:31:48,482 - INFO - 开始处理日期: 2025-06-14
2025-06-15 07:31:48,482 - INFO - Request Parameters - Page 1:
2025-06-15 07:31:48,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 07:31:48,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 07:31:49,232 - INFO - Response - Page 1:
2025-06-15 07:31:49,232 - INFO - 第 1 页获取到 50 条记录
2025-06-15 07:31:49,732 - INFO - Request Parameters - Page 2:
2025-06-15 07:31:49,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 07:31:49,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 07:31:50,419 - INFO - Response - Page 2:
2025-06-15 07:31:50,419 - INFO - 第 2 页获取到 50 条记录
2025-06-15 07:31:50,935 - INFO - Request Parameters - Page 3:
2025-06-15 07:31:50,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 07:31:50,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 07:31:51,497 - INFO - Response - Page 3:
2025-06-15 07:31:51,497 - INFO - 第 3 页获取到 22 条记录
2025-06-15 07:31:52,013 - INFO - 查询完成，共获取到 122 条记录
2025-06-15 07:31:52,013 - INFO - 获取到 122 条表单数据
2025-06-15 07:31:52,013 - INFO - 当前日期 2025-06-14 有 122 条MySQL数据需要处理
2025-06-15 07:31:52,013 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 07:31:52,013 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 07:31:52,013 - INFO - 同步完成
2025-06-15 10:30:33,614 - INFO - 使用默认增量同步（当天更新数据）
2025-06-15 10:30:33,614 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-15 10:30:33,614 - INFO - 查询参数: ('2025-06-15',)
2025-06-15 10:30:33,739 - INFO - MySQL查询成功，增量数据（日期: 2025-06-15），共获取 102 条记录
2025-06-15 10:30:33,739 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 10:30:33,739 - INFO - 开始处理日期: 2025-06-14
2025-06-15 10:30:33,739 - INFO - Request Parameters - Page 1:
2025-06-15 10:30:33,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 10:30:33,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 10:30:41,848 - ERROR - 处理日期 2025-06-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A14DDFC6-4314-7563-9D8D-F0EC79C02A02 Response: {'code': 'ServiceUnavailable', 'requestid': 'A14DDFC6-4314-7563-9D8D-F0EC79C02A02', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A14DDFC6-4314-7563-9D8D-F0EC79C02A02)
2025-06-15 10:30:41,848 - INFO - 开始处理日期: 2025-06-15
2025-06-15 10:30:41,848 - INFO - Request Parameters - Page 1:
2025-06-15 10:30:41,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 10:30:41,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 10:30:46,536 - INFO - Response - Page 1:
2025-06-15 10:30:46,536 - INFO - 查询完成，共获取到 0 条记录
2025-06-15 10:30:46,536 - INFO - 获取到 0 条表单数据
2025-06-15 10:30:46,536 - INFO - 当前日期 2025-06-15 有 4 条MySQL数据需要处理
2025-06-15 10:30:46,536 - INFO - 开始批量插入 4 条新记录
2025-06-15 10:30:46,708 - INFO - 批量插入响应状态码: 200
2025-06-15 10:30:46,708 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 02:30:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B3B436FB-2B5B-7DEB-8B42-C6CC1585252C', 'x-acs-trace-id': '0a07c7b16d31ff0cab94a82031371dcf', 'etag': '2as2QVFrQIgr0vDcWpGKoEQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 10:30:46,708 - INFO - 批量插入响应体: {'result': ['FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM3L', 'FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM4L', 'FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM5L', 'FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM6L']}
2025-06-15 10:30:46,708 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-15 10:30:46,708 - INFO - 成功插入的数据ID: ['FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM3L', 'FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM4L', 'FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM5L', 'FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM6L']
2025-06-15 10:30:51,723 - INFO - 批量插入完成，共 4 条记录
2025-06-15 10:30:51,723 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-06-15 10:30:51,723 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 1 条
2025-06-15 10:31:51,738 - INFO - 开始同步昨天与今天的销售数据: 2025-06-14 至 2025-06-15
2025-06-15 10:31:51,738 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-15 10:31:51,738 - INFO - 查询参数: ('2025-06-14', '2025-06-15')
2025-06-15 10:31:51,863 - INFO - MySQL查询成功，时间段: 2025-06-14 至 2025-06-15，共获取 412 条记录
2025-06-15 10:31:51,863 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 10:31:51,879 - INFO - 开始处理日期: 2025-06-14
2025-06-15 10:31:51,879 - INFO - Request Parameters - Page 1:
2025-06-15 10:31:51,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 10:31:51,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 10:31:52,535 - INFO - Response - Page 1:
2025-06-15 10:31:52,535 - INFO - 第 1 页获取到 50 条记录
2025-06-15 10:31:53,051 - INFO - Request Parameters - Page 2:
2025-06-15 10:31:53,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 10:31:53,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 10:31:58,723 - INFO - Response - Page 2:
2025-06-15 10:31:58,723 - INFO - 第 2 页获取到 50 条记录
2025-06-15 10:31:59,238 - INFO - Request Parameters - Page 3:
2025-06-15 10:31:59,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 10:31:59,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 10:31:59,801 - INFO - Response - Page 3:
2025-06-15 10:31:59,801 - INFO - 第 3 页获取到 22 条记录
2025-06-15 10:32:00,301 - INFO - 查询完成，共获取到 122 条记录
2025-06-15 10:32:00,301 - INFO - 获取到 122 条表单数据
2025-06-15 10:32:00,301 - INFO - 当前日期 2025-06-14 有 388 条MySQL数据需要处理
2025-06-15 10:32:00,301 - INFO - 开始批量插入 266 条新记录
2025-06-15 10:32:00,551 - INFO - 批量插入响应状态码: 200
2025-06-15 10:32:00,551 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 02:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C70944F7-E7C8-7309-8E0C-780B36936356', 'x-acs-trace-id': 'eecddcaf91e6055b79ab1dd4ab54ddea', 'etag': '2RQr3t6h/1DAL2RQoRD9vlw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 10:32:00,551 - INFO - 批量插入响应体: {'result': ['FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMPV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMQV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMRV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMSV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMTV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMUV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMVV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMWV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMXV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMYV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMZV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM0W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM1W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM2W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM3W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM4W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM5W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM6W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM7W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM8W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM9W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMAW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMBW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMCW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMDW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMEW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMFW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMGW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMHW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMIW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMJW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMKW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMLW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMMW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMNW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMOW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMPW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMQW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMRW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMSW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMTW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMUW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMVW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMWW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMXW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMYW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMZW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM0X', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM1X', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM2X']}
2025-06-15 10:32:00,551 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-15 10:32:00,551 - INFO - 成功插入的数据ID: ['FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMPV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMQV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMRV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMSV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMTV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMUV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMVV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMWV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMXV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMYV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMZV', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM0W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM1W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM2W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM3W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM4W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM5W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM6W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM7W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM8W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM9W', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMAW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMBW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMCW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMDW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMEW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMFW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMGW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMHW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMIW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMJW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMKW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMLW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMMW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMNW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMOW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMPW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMQW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMRW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMSW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMTW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMUW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMVW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMWW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMXW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMYW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBMZW', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM0X', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM1X', 'FINST-CJ966Q716L8WSV6R9UIWN83WKTHW39OTU1XBM2X']
2025-06-15 10:32:05,816 - INFO - 批量插入响应状态码: 200
2025-06-15 10:32:05,816 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 02:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '55FDD812-201F-7C69-B148-E7752AD14C78', 'x-acs-trace-id': '30c9d952373caa21a663b4cef776044b', 'etag': '2MfVZxydmUC6h5Ne6jO4Hww2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 10:32:05,816 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMQP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMRP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMSP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMTP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMUP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMVP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMWP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMXP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMYP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMZP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM0Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM1Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM2Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM3Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM4Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM5Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM6Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM7Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM8Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM9Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMAQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMBQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMCQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMDQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMEQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMFQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMGQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMHQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMIQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMJQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMKQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMLQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMMQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMNQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMOQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMPQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMQQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMRQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMSQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMTQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMUQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMVQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMWQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMXQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMYQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMZQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM0R', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM1R', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM2R', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM3R']}
2025-06-15 10:32:05,816 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-15 10:32:05,816 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMQP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMRP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMSP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMTP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMUP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMVP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMWP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMXP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMYP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMZP', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM0Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM1Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM2Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM3Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM4Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM5Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM6Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM7Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM8Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM9Q', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMAQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMBQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMCQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMDQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMEQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMFQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMGQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMHQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMIQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMJQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMKQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMLQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMMQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMNQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMOQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMPQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMQQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMRQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMSQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMTQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMUQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMVQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMWQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMXQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMYQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBMZQ', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM0R', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM1R', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM2R', 'FINST-7PF66CC1UG9WYPI9AWMAR5GHITT43LQXU1XBM3R']
2025-06-15 10:32:11,035 - INFO - 批量插入响应状态码: 200
2025-06-15 10:32:11,035 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 02:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5FD550C5-43B9-7B59-BDFC-1EDB7D7F12C8', 'x-acs-trace-id': '33ef07b5d686b21b9355838ad0657098', 'etag': '2AiI7RZq7JPyiuoG+Vy+0fA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 10:32:11,035 - INFO - 批量插入响应体: {'result': ['FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMD4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBME4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMF4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMG4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMH4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMI4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMJ4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMK4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBML4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMM4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMN4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMO4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMP4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMQ4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMR4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMS4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMT4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMU4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMV4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMW4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMX4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMY4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMZ4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM05', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM15', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM25', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM35', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM45', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM55', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM65', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM75', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM85', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM95', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMA5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMB5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMC5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMD5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBME5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMF5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMG5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMH5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMI5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMJ5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMK5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBML5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMM5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMN5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMO5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMP5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMQ5']}
2025-06-15 10:32:11,035 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-15 10:32:11,035 - INFO - 成功插入的数据ID: ['FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMD4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBME4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMF4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMG4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMH4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMI4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMJ4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMK4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBML4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMM4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMN4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMO4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMP4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMQ4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMR4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMS4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMT4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMU4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMV4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMW4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMX4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMY4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMZ4', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM05', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM15', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM25', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM35', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM45', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM55', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM65', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM75', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM85', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBM95', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMA5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMB5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMC5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMD5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBME5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMF5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMG5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMH5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMI5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMJ5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMK5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBML5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMM5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMN5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMO5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMP5', 'FINST-R1A66H91SO9WJHL27G0CA4SF9Z3L3VR1V1XBMQ5']
2025-06-15 10:32:16,285 - INFO - 批量插入响应状态码: 200
2025-06-15 10:32:16,285 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 02:32:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F02A4C23-800E-7BCC-B053-0EE72D078341', 'x-acs-trace-id': 'd0631d4a290bfed57e68217e7ae29636', 'etag': '2wPqwyABU9NiK6VpgGXMxOQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 10:32:16,285 - INFO - 批量插入响应体: {'result': ['FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMQN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMRN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMSN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMTN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMUN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMVN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMWN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMXN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMYN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMZN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM0O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM1O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM2O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM3O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM4O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM5O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM6O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM7O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM8O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM9O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMAO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMBO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMCO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMDO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMEO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMFO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMGO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMHO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMIO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMJO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMKO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMLO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMMO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMNO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMOO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMPO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMQO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMRO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMSO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMTO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMUO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMVO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMWO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMXO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMYO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMZO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM0P', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM1P', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM2P', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM3P']}
2025-06-15 10:32:16,285 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-15 10:32:16,285 - INFO - 成功插入的数据ID: ['FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMQN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMRN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMSN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMTN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMUN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMVN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMWN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMXN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMYN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMZN', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM0O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM1O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM2O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM3O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM4O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM5O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM6O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM7O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM8O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM9O', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMAO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMBO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMCO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMDO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMEO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMFO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMGO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMHO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMIO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMJO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMKO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMLO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMMO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMNO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMOO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMPO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMQO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMRO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMSO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMTO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMUO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMVO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMWO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMXO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMYO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBMZO', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM0P', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM1P', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM2P', 'FINST-74766M71XS9W4TZ3CMLCB8UK6S1X3DT5V1XBM3P']
2025-06-15 10:32:21,551 - INFO - 批量插入响应状态码: 200
2025-06-15 10:32:21,551 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 02:32:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1A48B0FB-20D9-7569-A069-B9E54A969D5A', 'x-acs-trace-id': 'a5ae4152cf1b49aff81c2cb313a3de07', 'etag': '21x1thkSjdo1toHXjRfUWEg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 10:32:21,551 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMOK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMPK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMQK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMRK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMSK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMTK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMUK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMVK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMWK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMXK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMYK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMZK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM0L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM1L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM2L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM3L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM4L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM5L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM6L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM7L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM8L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM9L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMAL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMBL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMCL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMDL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMEL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMFL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMGL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMHL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMIL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMJL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMKL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMLL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMML', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMNL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMOL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMPL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMQL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMRL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMSL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMTL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMUL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMVL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMWL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBMXL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBMYL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBMZL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBM0M', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBM1M']}
2025-06-15 10:32:21,551 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-06-15 10:32:21,551 - INFO - 成功插入的数据ID: ['FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMOK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMPK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMQK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMRK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMSK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMTK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMUK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMVK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMWK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMXK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMYK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMZK', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM0L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM1L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM2L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM3L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM4L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM5L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM6L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM7L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM8L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBM9L', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMAL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMBL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMCL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMDL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMEL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMFL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMGL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMHL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMIL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMJL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMKL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMLL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMML', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMNL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMOL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMPL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMQL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMRL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMSL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMTL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMUL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMVL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMWL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBMXL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBMYL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBMZL', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBM0M', 'FINST-XL866HB1BJ9WKUVMES65ECNLZSP73PV9V1XBM1M']
2025-06-15 10:32:26,723 - INFO - 批量插入响应状态码: 200
2025-06-15 10:32:26,723 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 02:32:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '780', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D8CBA1CC-BFC2-715D-8E24-278EC10DE92D', 'x-acs-trace-id': 'ad8a21b835d505f286abfbfe47a7b611', 'etag': '76xVVQti9Nnf6KDf0XTJlvg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 10:32:26,723 - INFO - 批量插入响应体: {'result': ['FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM1R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM2R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM3R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM4R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM5R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM6R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM7R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM8R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM9R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBMAR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBMBR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMCR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMDR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMER', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMFR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMGR']}
2025-06-15 10:32:26,723 - INFO - 批量插入表单数据成功，批次 6，共 16 条记录
2025-06-15 10:32:26,723 - INFO - 成功插入的数据ID: ['FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM1R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM2R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM3R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM4R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM5R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM6R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM7R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM8R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBM9R', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBMAR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3GVDV1XBMBR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMCR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMDR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMER', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMFR', 'FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMGR']
2025-06-15 10:32:31,738 - INFO - 批量插入完成，共 266 条记录
2025-06-15 10:32:31,738 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 266 条，错误: 0 条
2025-06-15 10:32:31,738 - INFO - 开始处理日期: 2025-06-15
2025-06-15 10:32:31,738 - INFO - Request Parameters - Page 1:
2025-06-15 10:32:31,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 10:32:31,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 10:32:32,207 - INFO - Response - Page 1:
2025-06-15 10:32:32,207 - INFO - 第 1 页获取到 4 条记录
2025-06-15 10:32:32,723 - INFO - 查询完成，共获取到 4 条记录
2025-06-15 10:32:32,723 - INFO - 获取到 4 条表单数据
2025-06-15 10:32:32,723 - INFO - 当前日期 2025-06-15 有 4 条MySQL数据需要处理
2025-06-15 10:32:32,723 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 10:32:32,723 - INFO - 数据同步完成！更新: 0 条，插入: 266 条，错误: 0 条
2025-06-15 10:32:32,723 - INFO - 同步完成
2025-06-15 13:30:33,589 - INFO - 使用默认增量同步（当天更新数据）
2025-06-15 13:30:33,589 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-15 13:30:33,589 - INFO - 查询参数: ('2025-06-15',)
2025-06-15 13:30:33,714 - INFO - MySQL查询成功，增量数据（日期: 2025-06-15），共获取 127 条记录
2025-06-15 13:30:33,714 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 13:30:33,714 - INFO - 开始处理日期: 2025-06-14
2025-06-15 13:30:33,714 - INFO - Request Parameters - Page 1:
2025-06-15 13:30:33,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:30:33,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:30:41,839 - ERROR - 处理日期 2025-06-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9764BBF8-0CC8-7C39-9DB9-8FF42C0114F3 Response: {'code': 'ServiceUnavailable', 'requestid': '9764BBF8-0CC8-7C39-9DB9-8FF42C0114F3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9764BBF8-0CC8-7C39-9DB9-8FF42C0114F3)
2025-06-15 13:30:41,839 - INFO - 开始处理日期: 2025-06-15
2025-06-15 13:30:41,839 - INFO - Request Parameters - Page 1:
2025-06-15 13:30:41,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:30:41,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:30:42,339 - INFO - Response - Page 1:
2025-06-15 13:30:42,339 - INFO - 第 1 页获取到 4 条记录
2025-06-15 13:30:42,854 - INFO - 查询完成，共获取到 4 条记录
2025-06-15 13:30:42,854 - INFO - 获取到 4 条表单数据
2025-06-15 13:30:42,854 - INFO - 当前日期 2025-06-15 有 5 条MySQL数据需要处理
2025-06-15 13:30:42,854 - INFO - 开始批量插入 1 条新记录
2025-06-15 13:30:43,010 - INFO - 批量插入响应状态码: 200
2025-06-15 13:30:43,010 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 05:30:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2F494FC4-E4AC-7731-BA9B-B89B17CF0FF6', 'x-acs-trace-id': '8ee259b567ccd1cdc53b836c8569275e', 'etag': '6Sz83wl459XV4GVCHamxxag0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 13:30:43,010 - INFO - 批量插入响应体: {'result': ['FINST-Q7866DD1WH9W710P83TUY97F26BU2F8N88XBM88']}
2025-06-15 13:30:43,010 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-15 13:30:43,010 - INFO - 成功插入的数据ID: ['FINST-Q7866DD1WH9W710P83TUY97F26BU2F8N88XBM88']
2025-06-15 13:30:48,026 - INFO - 批量插入完成，共 1 条记录
2025-06-15 13:30:48,026 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-15 13:30:48,026 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-06-15 13:31:48,041 - INFO - 开始同步昨天与今天的销售数据: 2025-06-14 至 2025-06-15
2025-06-15 13:31:48,041 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-15 13:31:48,041 - INFO - 查询参数: ('2025-06-14', '2025-06-15')
2025-06-15 13:31:48,166 - INFO - MySQL查询成功，时间段: 2025-06-14 至 2025-06-15，共获取 454 条记录
2025-06-15 13:31:48,166 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 13:31:48,182 - INFO - 开始处理日期: 2025-06-14
2025-06-15 13:31:48,182 - INFO - Request Parameters - Page 1:
2025-06-15 13:31:48,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:31:48,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:31:48,901 - INFO - Response - Page 1:
2025-06-15 13:31:48,901 - INFO - 第 1 页获取到 50 条记录
2025-06-15 13:31:49,416 - INFO - Request Parameters - Page 2:
2025-06-15 13:31:49,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:31:49,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:31:50,073 - INFO - Response - Page 2:
2025-06-15 13:31:50,073 - INFO - 第 2 页获取到 50 条记录
2025-06-15 13:31:50,588 - INFO - Request Parameters - Page 3:
2025-06-15 13:31:50,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:31:50,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:31:51,307 - INFO - Response - Page 3:
2025-06-15 13:31:51,307 - INFO - 第 3 页获取到 50 条记录
2025-06-15 13:31:51,807 - INFO - Request Parameters - Page 4:
2025-06-15 13:31:51,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:31:51,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:31:52,494 - INFO - Response - Page 4:
2025-06-15 13:31:52,494 - INFO - 第 4 页获取到 50 条记录
2025-06-15 13:31:53,010 - INFO - Request Parameters - Page 5:
2025-06-15 13:31:53,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:31:53,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:31:53,651 - INFO - Response - Page 5:
2025-06-15 13:31:53,651 - INFO - 第 5 页获取到 50 条记录
2025-06-15 13:31:54,151 - INFO - Request Parameters - Page 6:
2025-06-15 13:31:54,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:31:54,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:31:54,807 - INFO - Response - Page 6:
2025-06-15 13:31:54,807 - INFO - 第 6 页获取到 50 条记录
2025-06-15 13:31:55,322 - INFO - Request Parameters - Page 7:
2025-06-15 13:31:55,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:31:55,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:31:55,947 - INFO - Response - Page 7:
2025-06-15 13:31:55,947 - INFO - 第 7 页获取到 50 条记录
2025-06-15 13:31:56,447 - INFO - Request Parameters - Page 8:
2025-06-15 13:31:56,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:31:56,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:31:57,057 - INFO - Response - Page 8:
2025-06-15 13:31:57,057 - INFO - 第 8 页获取到 38 条记录
2025-06-15 13:31:57,557 - INFO - 查询完成，共获取到 388 条记录
2025-06-15 13:31:57,557 - INFO - 获取到 388 条表单数据
2025-06-15 13:31:57,557 - INFO - 当前日期 2025-06-14 有 429 条MySQL数据需要处理
2025-06-15 13:31:57,572 - INFO - 开始批量插入 41 条新记录
2025-06-15 13:31:57,807 - INFO - 批量插入响应状态码: 200
2025-06-15 13:31:57,807 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 05:31:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1980', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '222D5E07-7FC2-7483-9983-67D185510FDF', 'x-acs-trace-id': '9081899964e58d1db63d00f52130a087', 'etag': '1RpC11HPz0ps2SEM82A2JhA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 13:31:57,807 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMNI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMOI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMPI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMQI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMRI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMSI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMTI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMUI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMVI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMWI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMXI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMYI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMZI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM0J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM1J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM2J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM3J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM4J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM5J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM6J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM7J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM8J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM9J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMAJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMBJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMCJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMDJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMEJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMFJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMGJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMHJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMIJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMJJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMKJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMLJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMMJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMNJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMOJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMPJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMQJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMRJ']}
2025-06-15 13:31:57,807 - INFO - 批量插入表单数据成功，批次 1，共 41 条记录
2025-06-15 13:31:57,807 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMNI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMOI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMPI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMQI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMRI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMSI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMTI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMUI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMVI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMWI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMXI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMYI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMZI', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM0J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM1J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM2J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM3J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM4J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM5J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM6J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM7J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM8J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBM9J', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB28Y8A8XBMAJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMBJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMCJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMDJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMEJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMFJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMGJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMHJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMIJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMJJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMKJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMLJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMMJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMNJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMOJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMPJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMQJ', 'FINST-W4G66DA1JU9WWGAIBFKGWBO8XXGB29Y8A8XBMRJ']
2025-06-15 13:32:02,822 - INFO - 批量插入完成，共 41 条记录
2025-06-15 13:32:02,822 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 41 条，错误: 0 条
2025-06-15 13:32:02,822 - INFO - 开始处理日期: 2025-06-15
2025-06-15 13:32:02,822 - INFO - Request Parameters - Page 1:
2025-06-15 13:32:02,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 13:32:02,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 13:32:03,291 - INFO - Response - Page 1:
2025-06-15 13:32:03,291 - INFO - 第 1 页获取到 5 条记录
2025-06-15 13:32:03,807 - INFO - 查询完成，共获取到 5 条记录
2025-06-15 13:32:03,807 - INFO - 获取到 5 条表单数据
2025-06-15 13:32:03,807 - INFO - 当前日期 2025-06-15 有 5 条MySQL数据需要处理
2025-06-15 13:32:03,807 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 13:32:03,807 - INFO - 数据同步完成！更新: 0 条，插入: 41 条，错误: 0 条
2025-06-15 13:32:03,807 - INFO - 同步完成
2025-06-15 16:30:33,736 - INFO - 使用默认增量同步（当天更新数据）
2025-06-15 16:30:33,736 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-15 16:30:33,736 - INFO - 查询参数: ('2025-06-15',)
2025-06-15 16:30:33,876 - INFO - MySQL查询成功，增量数据（日期: 2025-06-15），共获取 128 条记录
2025-06-15 16:30:33,876 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 16:30:33,876 - INFO - 开始处理日期: 2025-06-14
2025-06-15 16:30:33,876 - INFO - Request Parameters - Page 1:
2025-06-15 16:30:33,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:33,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:39,204 - INFO - Response - Page 1:
2025-06-15 16:30:39,204 - INFO - 第 1 页获取到 50 条记录
2025-06-15 16:30:39,720 - INFO - Request Parameters - Page 2:
2025-06-15 16:30:39,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:39,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:40,485 - INFO - Response - Page 2:
2025-06-15 16:30:40,485 - INFO - 第 2 页获取到 50 条记录
2025-06-15 16:30:40,985 - INFO - Request Parameters - Page 3:
2025-06-15 16:30:40,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:40,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:41,735 - INFO - Response - Page 3:
2025-06-15 16:30:41,735 - INFO - 第 3 页获取到 50 条记录
2025-06-15 16:30:42,235 - INFO - Request Parameters - Page 4:
2025-06-15 16:30:42,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:42,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:42,923 - INFO - Response - Page 4:
2025-06-15 16:30:42,923 - INFO - 第 4 页获取到 50 条记录
2025-06-15 16:30:43,439 - INFO - Request Parameters - Page 5:
2025-06-15 16:30:43,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:43,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:44,048 - INFO - Response - Page 5:
2025-06-15 16:30:44,048 - INFO - 第 5 页获取到 50 条记录
2025-06-15 16:30:44,564 - INFO - Request Parameters - Page 6:
2025-06-15 16:30:44,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:44,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:45,204 - INFO - Response - Page 6:
2025-06-15 16:30:45,204 - INFO - 第 6 页获取到 50 条记录
2025-06-15 16:30:45,704 - INFO - Request Parameters - Page 7:
2025-06-15 16:30:45,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:45,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:46,298 - INFO - Response - Page 7:
2025-06-15 16:30:46,298 - INFO - 第 7 页获取到 50 条记录
2025-06-15 16:30:46,798 - INFO - Request Parameters - Page 8:
2025-06-15 16:30:46,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:46,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:47,454 - INFO - Response - Page 8:
2025-06-15 16:30:47,454 - INFO - 第 8 页获取到 50 条记录
2025-06-15 16:30:47,970 - INFO - Request Parameters - Page 9:
2025-06-15 16:30:47,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:47,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:48,579 - INFO - Response - Page 9:
2025-06-15 16:30:48,579 - INFO - 第 9 页获取到 29 条记录
2025-06-15 16:30:49,095 - INFO - 查询完成，共获取到 429 条记录
2025-06-15 16:30:49,095 - INFO - 获取到 429 条表单数据
2025-06-15 16:30:49,095 - INFO - 当前日期 2025-06-14 有 120 条MySQL数据需要处理
2025-06-15 16:30:49,095 - INFO - 开始批量插入 1 条新记录
2025-06-15 16:30:49,267 - INFO - 批量插入响应状态码: 200
2025-06-15 16:30:49,267 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 08:30:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F0E63642-4A38-752D-B06A-EAA94EB42F93', 'x-acs-trace-id': 'edd4c27a3e3c07ec7c57ac2f31ec433f', 'etag': '6kMVM0wqzgdsRjEen3aNjew0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 16:30:49,267 - INFO - 批量插入响应体: {'result': ['FINST-LLF66J71LT9WJEI29NL1Z86P19012DG9OEXBMQ6']}
2025-06-15 16:30:49,267 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-15 16:30:49,267 - INFO - 成功插入的数据ID: ['FINST-LLF66J71LT9WJEI29NL1Z86P19012DG9OEXBMQ6']
2025-06-15 16:30:54,282 - INFO - 批量插入完成，共 1 条记录
2025-06-15 16:30:54,282 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-15 16:30:54,282 - INFO - 开始处理日期: 2025-06-15
2025-06-15 16:30:54,282 - INFO - Request Parameters - Page 1:
2025-06-15 16:30:54,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:30:54,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:30:54,735 - INFO - Response - Page 1:
2025-06-15 16:30:54,735 - INFO - 第 1 页获取到 5 条记录
2025-06-15 16:30:55,251 - INFO - 查询完成，共获取到 5 条记录
2025-06-15 16:30:55,251 - INFO - 获取到 5 条表单数据
2025-06-15 16:30:55,251 - INFO - 当前日期 2025-06-15 有 5 条MySQL数据需要处理
2025-06-15 16:30:55,251 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 16:30:55,251 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-15 16:31:55,266 - INFO - 开始同步昨天与今天的销售数据: 2025-06-14 至 2025-06-15
2025-06-15 16:31:55,266 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-15 16:31:55,266 - INFO - 查询参数: ('2025-06-14', '2025-06-15')
2025-06-15 16:31:55,391 - INFO - MySQL查询成功，时间段: 2025-06-14 至 2025-06-15，共获取 455 条记录
2025-06-15 16:31:55,391 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 16:31:55,407 - INFO - 开始处理日期: 2025-06-14
2025-06-15 16:31:55,407 - INFO - Request Parameters - Page 1:
2025-06-15 16:31:55,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:31:55,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:31:56,063 - INFO - Response - Page 1:
2025-06-15 16:31:56,063 - INFO - 第 1 页获取到 50 条记录
2025-06-15 16:31:56,563 - INFO - Request Parameters - Page 2:
2025-06-15 16:31:56,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:31:56,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:04,704 - INFO - Response - Page 2:
2025-06-15 16:32:04,704 - INFO - 第 2 页获取到 50 条记录
2025-06-15 16:32:05,204 - INFO - Request Parameters - Page 3:
2025-06-15 16:32:05,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:32:05,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:05,954 - INFO - Response - Page 3:
2025-06-15 16:32:05,954 - INFO - 第 3 页获取到 50 条记录
2025-06-15 16:32:06,454 - INFO - Request Parameters - Page 4:
2025-06-15 16:32:06,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:32:06,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:07,094 - INFO - Response - Page 4:
2025-06-15 16:32:07,110 - INFO - 第 4 页获取到 50 条记录
2025-06-15 16:32:07,625 - INFO - Request Parameters - Page 5:
2025-06-15 16:32:07,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:32:07,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:08,297 - INFO - Response - Page 5:
2025-06-15 16:32:08,297 - INFO - 第 5 页获取到 50 条记录
2025-06-15 16:32:08,813 - INFO - Request Parameters - Page 6:
2025-06-15 16:32:08,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:32:08,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:09,485 - INFO - Response - Page 6:
2025-06-15 16:32:09,485 - INFO - 第 6 页获取到 50 条记录
2025-06-15 16:32:10,000 - INFO - Request Parameters - Page 7:
2025-06-15 16:32:10,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:32:10,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:10,610 - INFO - Response - Page 7:
2025-06-15 16:32:10,610 - INFO - 第 7 页获取到 50 条记录
2025-06-15 16:32:11,125 - INFO - Request Parameters - Page 8:
2025-06-15 16:32:11,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:32:11,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:11,782 - INFO - Response - Page 8:
2025-06-15 16:32:11,782 - INFO - 第 8 页获取到 50 条记录
2025-06-15 16:32:12,297 - INFO - Request Parameters - Page 9:
2025-06-15 16:32:12,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:32:12,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:12,938 - INFO - Response - Page 9:
2025-06-15 16:32:12,938 - INFO - 第 9 页获取到 30 条记录
2025-06-15 16:32:13,454 - INFO - 查询完成，共获取到 430 条记录
2025-06-15 16:32:13,454 - INFO - 获取到 430 条表单数据
2025-06-15 16:32:13,454 - INFO - 当前日期 2025-06-14 有 430 条MySQL数据需要处理
2025-06-15 16:32:13,469 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 16:32:13,469 - INFO - 开始处理日期: 2025-06-15
2025-06-15 16:32:13,469 - INFO - Request Parameters - Page 1:
2025-06-15 16:32:13,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 16:32:13,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 16:32:13,969 - INFO - Response - Page 1:
2025-06-15 16:32:13,969 - INFO - 第 1 页获取到 5 条记录
2025-06-15 16:32:14,469 - INFO - 查询完成，共获取到 5 条记录
2025-06-15 16:32:14,469 - INFO - 获取到 5 条表单数据
2025-06-15 16:32:14,469 - INFO - 当前日期 2025-06-15 有 5 条MySQL数据需要处理
2025-06-15 16:32:14,469 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 16:32:14,469 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 16:32:14,469 - INFO - 同步完成
2025-06-15 19:30:33,776 - INFO - 使用默认增量同步（当天更新数据）
2025-06-15 19:30:33,776 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-15 19:30:33,776 - INFO - 查询参数: ('2025-06-15',)
2025-06-15 19:30:33,901 - INFO - MySQL查询成功，增量数据（日期: 2025-06-15），共获取 131 条记录
2025-06-15 19:30:33,901 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 19:30:33,901 - INFO - 开始处理日期: 2025-06-14
2025-06-15 19:30:33,901 - INFO - Request Parameters - Page 1:
2025-06-15 19:30:33,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:33,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:42,027 - INFO - Response - Page 1:
2025-06-15 19:30:42,027 - INFO - 第 1 页获取到 50 条记录
2025-06-15 19:30:42,527 - INFO - Request Parameters - Page 2:
2025-06-15 19:30:42,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:42,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:43,184 - INFO - Response - Page 2:
2025-06-15 19:30:43,184 - INFO - 第 2 页获取到 50 条记录
2025-06-15 19:30:43,684 - INFO - Request Parameters - Page 3:
2025-06-15 19:30:43,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:43,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:44,402 - INFO - Response - Page 3:
2025-06-15 19:30:44,402 - INFO - 第 3 页获取到 50 条记录
2025-06-15 19:30:44,918 - INFO - Request Parameters - Page 4:
2025-06-15 19:30:44,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:44,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:50,122 - INFO - Response - Page 4:
2025-06-15 19:30:50,122 - INFO - 第 4 页获取到 50 条记录
2025-06-15 19:30:50,638 - INFO - Request Parameters - Page 5:
2025-06-15 19:30:50,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:50,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:51,278 - INFO - Response - Page 5:
2025-06-15 19:30:51,278 - INFO - 第 5 页获取到 50 条记录
2025-06-15 19:30:51,778 - INFO - Request Parameters - Page 6:
2025-06-15 19:30:51,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:51,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:52,466 - INFO - Response - Page 6:
2025-06-15 19:30:52,466 - INFO - 第 6 页获取到 50 条记录
2025-06-15 19:30:52,982 - INFO - Request Parameters - Page 7:
2025-06-15 19:30:52,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:52,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:53,700 - INFO - Response - Page 7:
2025-06-15 19:30:53,700 - INFO - 第 7 页获取到 50 条记录
2025-06-15 19:30:54,200 - INFO - Request Parameters - Page 8:
2025-06-15 19:30:54,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:54,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:54,841 - INFO - Response - Page 8:
2025-06-15 19:30:54,841 - INFO - 第 8 页获取到 50 条记录
2025-06-15 19:30:55,341 - INFO - Request Parameters - Page 9:
2025-06-15 19:30:55,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:30:55,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:30:55,951 - INFO - Response - Page 9:
2025-06-15 19:30:55,951 - INFO - 第 9 页获取到 30 条记录
2025-06-15 19:30:56,451 - INFO - 查询完成，共获取到 430 条记录
2025-06-15 19:30:56,451 - INFO - 获取到 430 条表单数据
2025-06-15 19:30:56,451 - INFO - 当前日期 2025-06-14 有 123 条MySQL数据需要处理
2025-06-15 19:30:56,451 - INFO - 开始批量插入 3 条新记录
2025-06-15 19:30:56,607 - INFO - 批量插入响应状态码: 200
2025-06-15 19:30:56,607 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 11:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BF3D70D5-DDEC-7D05-8CB9-BB30E54A9934', 'x-acs-trace-id': '29f32aa8826733d37135aec938936de4', 'etag': '19ezT1chYtT72KwsYeOTUcw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 19:30:56,607 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N91YS9WKXCBAKSOD6XJX48S24QY3LXBMO4', 'FINST-7PF66N91YS9WKXCBAKSOD6XJX48S24QY3LXBMP4', 'FINST-7PF66N91YS9WKXCBAKSOD6XJX48S24QY3LXBMQ4']}
2025-06-15 19:30:56,607 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-15 19:30:56,607 - INFO - 成功插入的数据ID: ['FINST-7PF66N91YS9WKXCBAKSOD6XJX48S24QY3LXBMO4', 'FINST-7PF66N91YS9WKXCBAKSOD6XJX48S24QY3LXBMP4', 'FINST-7PF66N91YS9WKXCBAKSOD6XJX48S24QY3LXBMQ4']
2025-06-15 19:31:01,623 - INFO - 批量插入完成，共 3 条记录
2025-06-15 19:31:01,623 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-06-15 19:31:01,623 - INFO - 开始处理日期: 2025-06-15
2025-06-15 19:31:01,623 - INFO - Request Parameters - Page 1:
2025-06-15 19:31:01,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:31:01,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:31:02,092 - INFO - Response - Page 1:
2025-06-15 19:31:02,092 - INFO - 第 1 页获取到 5 条记录
2025-06-15 19:31:02,608 - INFO - 查询完成，共获取到 5 条记录
2025-06-15 19:31:02,608 - INFO - 获取到 5 条表单数据
2025-06-15 19:31:02,608 - INFO - 当前日期 2025-06-15 有 5 条MySQL数据需要处理
2025-06-15 19:31:02,608 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 19:31:02,608 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 0 条
2025-06-15 19:32:02,630 - INFO - 开始同步昨天与今天的销售数据: 2025-06-14 至 2025-06-15
2025-06-15 19:32:02,630 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-15 19:32:02,630 - INFO - 查询参数: ('2025-06-14', '2025-06-15')
2025-06-15 19:32:02,755 - INFO - MySQL查询成功，时间段: 2025-06-14 至 2025-06-15，共获取 458 条记录
2025-06-15 19:32:02,755 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 19:32:02,771 - INFO - 开始处理日期: 2025-06-14
2025-06-15 19:32:02,771 - INFO - Request Parameters - Page 1:
2025-06-15 19:32:02,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:02,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:03,490 - INFO - Response - Page 1:
2025-06-15 19:32:03,490 - INFO - 第 1 页获取到 50 条记录
2025-06-15 19:32:03,990 - INFO - Request Parameters - Page 2:
2025-06-15 19:32:03,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:03,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:04,662 - INFO - Response - Page 2:
2025-06-15 19:32:04,662 - INFO - 第 2 页获取到 50 条记录
2025-06-15 19:32:05,177 - INFO - Request Parameters - Page 3:
2025-06-15 19:32:05,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:05,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:05,834 - INFO - Response - Page 3:
2025-06-15 19:32:05,834 - INFO - 第 3 页获取到 50 条记录
2025-06-15 19:32:06,349 - INFO - Request Parameters - Page 4:
2025-06-15 19:32:06,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:06,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:07,021 - INFO - Response - Page 4:
2025-06-15 19:32:07,021 - INFO - 第 4 页获取到 50 条记录
2025-06-15 19:32:07,537 - INFO - Request Parameters - Page 5:
2025-06-15 19:32:07,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:07,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:08,240 - INFO - Response - Page 5:
2025-06-15 19:32:08,240 - INFO - 第 5 页获取到 50 条记录
2025-06-15 19:32:08,756 - INFO - Request Parameters - Page 6:
2025-06-15 19:32:08,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:08,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:09,365 - INFO - Response - Page 6:
2025-06-15 19:32:09,365 - INFO - 第 6 页获取到 50 条记录
2025-06-15 19:32:09,881 - INFO - Request Parameters - Page 7:
2025-06-15 19:32:09,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:09,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:10,568 - INFO - Response - Page 7:
2025-06-15 19:32:10,568 - INFO - 第 7 页获取到 50 条记录
2025-06-15 19:32:11,084 - INFO - Request Parameters - Page 8:
2025-06-15 19:32:11,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:11,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:11,694 - INFO - Response - Page 8:
2025-06-15 19:32:11,694 - INFO - 第 8 页获取到 50 条记录
2025-06-15 19:32:12,209 - INFO - Request Parameters - Page 9:
2025-06-15 19:32:12,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:12,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:12,834 - INFO - Response - Page 9:
2025-06-15 19:32:12,834 - INFO - 第 9 页获取到 33 条记录
2025-06-15 19:32:13,350 - INFO - 查询完成，共获取到 433 条记录
2025-06-15 19:32:13,350 - INFO - 获取到 433 条表单数据
2025-06-15 19:32:13,350 - INFO - 当前日期 2025-06-14 有 433 条MySQL数据需要处理
2025-06-15 19:32:13,366 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 19:32:13,366 - INFO - 开始处理日期: 2025-06-15
2025-06-15 19:32:13,366 - INFO - Request Parameters - Page 1:
2025-06-15 19:32:13,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 19:32:13,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 19:32:13,850 - INFO - Response - Page 1:
2025-06-15 19:32:13,850 - INFO - 第 1 页获取到 5 条记录
2025-06-15 19:32:14,366 - INFO - 查询完成，共获取到 5 条记录
2025-06-15 19:32:14,366 - INFO - 获取到 5 条表单数据
2025-06-15 19:32:14,366 - INFO - 当前日期 2025-06-15 有 5 条MySQL数据需要处理
2025-06-15 19:32:14,366 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 19:32:14,366 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 19:32:14,366 - INFO - 同步完成
2025-06-15 22:30:34,016 - INFO - 使用默认增量同步（当天更新数据）
2025-06-15 22:30:34,016 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-15 22:30:34,016 - INFO - 查询参数: ('2025-06-15',)
2025-06-15 22:30:34,156 - INFO - MySQL查询成功，增量数据（日期: 2025-06-15），共获取 209 条记录
2025-06-15 22:30:34,156 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 22:30:34,156 - INFO - 开始处理日期: 2025-06-14
2025-06-15 22:30:34,156 - INFO - Request Parameters - Page 1:
2025-06-15 22:30:34,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:30:34,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:30:42,267 - ERROR - 处理日期 2025-06-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E5C375D3-077D-7F9C-A429-1FD198EF4719 Response: {'code': 'ServiceUnavailable', 'requestid': 'E5C375D3-077D-7F9C-A429-1FD198EF4719', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E5C375D3-077D-7F9C-A429-1FD198EF4719)
2025-06-15 22:30:42,267 - INFO - 开始处理日期: 2025-06-15
2025-06-15 22:30:42,267 - INFO - Request Parameters - Page 1:
2025-06-15 22:30:42,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:30:42,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:30:43,126 - INFO - Response - Page 1:
2025-06-15 22:30:43,126 - INFO - 第 1 页获取到 5 条记录
2025-06-15 22:30:43,626 - INFO - 查询完成，共获取到 5 条记录
2025-06-15 22:30:43,626 - INFO - 获取到 5 条表单数据
2025-06-15 22:30:43,626 - INFO - 当前日期 2025-06-15 有 79 条MySQL数据需要处理
2025-06-15 22:30:43,626 - INFO - 开始批量插入 74 条新记录
2025-06-15 22:30:43,876 - INFO - 批量插入响应状态码: 200
2025-06-15 22:30:43,876 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 14:30:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7F69118C-500F-7F23-852F-957C4414FBDF', 'x-acs-trace-id': '6af796ae30e287365148b1deb3f265bd', 'etag': '2V+T3dV56rF9Pl3bv2BkPWQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 22:30:43,876 - INFO - 批量插入响应体: {'result': ['FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMXC', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMYC', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMZC', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM0D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM1D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM2D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM3D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM4D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM5D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM6D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM7D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM8D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM9D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMAD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMBD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMCD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMDD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMED', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMFD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMGD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMHD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMID', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMJD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMKD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMLD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMMD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMND', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMOD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMPD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMQD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMRD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMSD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMTD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMUD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMVD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMWD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMXD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMYD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMZD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM0E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM1E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM2E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM3E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM4E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM5E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM6E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM7E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM8E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM9E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMAE']}
2025-06-15 22:30:43,876 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-15 22:30:43,876 - INFO - 成功插入的数据ID: ['FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMXC', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMYC', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMZC', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM0D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM1D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM2D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM3D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM4D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM5D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM6D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM7D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM8D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM9D', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMAD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMBD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMCD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMDD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMED', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMFD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMGD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMHD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMID', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMJD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMKD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMLD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMMD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMND', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMOD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMPD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMQD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMRD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMSD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMTD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMUD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMVD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMWD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMXD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMYD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMZD', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM0E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM1E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM2E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM3E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM4E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM5E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM6E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM7E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM8E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBM9E', 'FINST-EEC66XC1AKAWN7X96I3RJ6KXKMA23N95JRXBMAE']
2025-06-15 22:30:49,080 - INFO - 批量插入响应状态码: 200
2025-06-15 22:30:49,080 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 14:30:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2DF77ECC-2FCB-70B0-8C2A-7AE94E1D3946', 'x-acs-trace-id': '26fdefd3eb8c7ed1552db85936088f73', 'etag': '1w0g6pUl9fOecOdksVMNqmQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 22:30:49,080 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM4Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM5Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM6Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM7Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM8Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM9Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMAY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMBY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMCY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMDY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMEY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMFY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMGY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMHY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMIY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMJY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMKY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMLY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMMY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMNY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMOY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMPY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMQY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMRY']}
2025-06-15 22:30:49,080 - INFO - 批量插入表单数据成功，批次 2，共 24 条记录
2025-06-15 22:30:49,080 - INFO - 成功插入的数据ID: ['FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM4Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM5Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM6Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM7Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM8Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBM9Y', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMAY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMBY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMCY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMDY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMEY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMFY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMGY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMHY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMIY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMJY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMKY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMLY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMMY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMNY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMOY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMPY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMQY', 'FINST-XL866HB1QF8WS05B842Q19PUAZ4E3DA9JRXBMRY']
2025-06-15 22:30:54,096 - INFO - 批量插入完成，共 74 条记录
2025-06-15 22:30:54,096 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 74 条，错误: 0 条
2025-06-15 22:30:54,096 - INFO - 数据同步完成！更新: 0 条，插入: 74 条，错误: 1 条
2025-06-15 22:31:54,119 - INFO - 开始同步昨天与今天的销售数据: 2025-06-14 至 2025-06-15
2025-06-15 22:31:54,119 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-15 22:31:54,119 - INFO - 查询参数: ('2025-06-14', '2025-06-15')
2025-06-15 22:31:54,244 - INFO - MySQL查询成功，时间段: 2025-06-14 至 2025-06-15，共获取 540 条记录
2025-06-15 22:31:54,244 - INFO - 获取到 2 个日期需要处理: ['2025-06-14', '2025-06-15']
2025-06-15 22:31:54,259 - INFO - 开始处理日期: 2025-06-14
2025-06-15 22:31:54,259 - INFO - Request Parameters - Page 1:
2025-06-15 22:31:54,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:31:54,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:31:54,963 - INFO - Response - Page 1:
2025-06-15 22:31:54,963 - INFO - 第 1 页获取到 50 条记录
2025-06-15 22:31:55,463 - INFO - Request Parameters - Page 2:
2025-06-15 22:31:55,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:31:55,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:31:56,135 - INFO - Response - Page 2:
2025-06-15 22:31:56,135 - INFO - 第 2 页获取到 50 条记录
2025-06-15 22:31:56,635 - INFO - Request Parameters - Page 3:
2025-06-15 22:31:56,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:31:56,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:31:57,322 - INFO - Response - Page 3:
2025-06-15 22:31:57,322 - INFO - 第 3 页获取到 50 条记录
2025-06-15 22:31:57,838 - INFO - Request Parameters - Page 4:
2025-06-15 22:31:57,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:31:57,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:31:58,479 - INFO - Response - Page 4:
2025-06-15 22:31:58,479 - INFO - 第 4 页获取到 50 条记录
2025-06-15 22:31:58,994 - INFO - Request Parameters - Page 5:
2025-06-15 22:31:58,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:31:58,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:31:59,651 - INFO - Response - Page 5:
2025-06-15 22:31:59,651 - INFO - 第 5 页获取到 50 条记录
2025-06-15 22:32:00,151 - INFO - Request Parameters - Page 6:
2025-06-15 22:32:00,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:32:00,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:32:00,854 - INFO - Response - Page 6:
2025-06-15 22:32:00,854 - INFO - 第 6 页获取到 50 条记录
2025-06-15 22:32:01,354 - INFO - Request Parameters - Page 7:
2025-06-15 22:32:01,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:32:01,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:32:02,026 - INFO - Response - Page 7:
2025-06-15 22:32:02,026 - INFO - 第 7 页获取到 50 条记录
2025-06-15 22:32:02,526 - INFO - Request Parameters - Page 8:
2025-06-15 22:32:02,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:32:02,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:32:03,151 - INFO - Response - Page 8:
2025-06-15 22:32:03,151 - INFO - 第 8 页获取到 50 条记录
2025-06-15 22:32:03,667 - INFO - Request Parameters - Page 9:
2025-06-15 22:32:03,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:32:03,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:32:04,245 - INFO - Response - Page 9:
2025-06-15 22:32:04,245 - INFO - 第 9 页获取到 33 条记录
2025-06-15 22:32:04,745 - INFO - 查询完成，共获取到 433 条记录
2025-06-15 22:32:04,745 - INFO - 获取到 433 条表单数据
2025-06-15 22:32:04,745 - INFO - 当前日期 2025-06-14 有 433 条MySQL数据需要处理
2025-06-15 22:32:04,761 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 22:32:04,761 - INFO - 开始处理日期: 2025-06-15
2025-06-15 22:32:04,761 - INFO - Request Parameters - Page 1:
2025-06-15 22:32:04,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:32:04,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:32:05,354 - INFO - Response - Page 1:
2025-06-15 22:32:05,354 - INFO - 第 1 页获取到 50 条记录
2025-06-15 22:32:05,870 - INFO - Request Parameters - Page 2:
2025-06-15 22:32:05,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 22:32:05,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 22:32:06,401 - INFO - Response - Page 2:
2025-06-15 22:32:06,401 - INFO - 第 2 页获取到 29 条记录
2025-06-15 22:32:06,901 - INFO - 查询完成，共获取到 79 条记录
2025-06-15 22:32:06,901 - INFO - 获取到 79 条表单数据
2025-06-15 22:32:06,901 - INFO - 当前日期 2025-06-15 有 83 条MySQL数据需要处理
2025-06-15 22:32:06,901 - INFO - 开始批量插入 4 条新记录
2025-06-15 22:32:07,042 - INFO - 批量插入响应状态码: 200
2025-06-15 22:32:07,042 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 15 Jun 2025 14:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C880DB98-B403-7F61-9CFA-4F4CAB65A98B', 'x-acs-trace-id': 'b0f5ac541da66c0ee0468a029bc15d56', 'etag': '2eK0Z8xzax3PGKxP6bAm/+w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 22:32:07,042 - INFO - 批量插入响应体: {'result': ['FINST-OPC666D12G8WWVC8AYW6E93HM78T2ZFXKRXBMVI', 'FINST-OPC666D12G8WWVC8AYW6E93HM78T2ZFXKRXBMWI', 'FINST-OPC666D12G8WWVC8AYW6E93HM78T2ZFXKRXBMXI', 'FINST-OPC666D12G8WWVC8AYW6E93HM78T2ZFXKRXBMYI']}
2025-06-15 22:32:07,042 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-15 22:32:07,042 - INFO - 成功插入的数据ID: ['FINST-OPC666D12G8WWVC8AYW6E93HM78T2ZFXKRXBMVI', 'FINST-OPC666D12G8WWVC8AYW6E93HM78T2ZFXKRXBMWI', 'FINST-OPC666D12G8WWVC8AYW6E93HM78T2ZFXKRXBMXI', 'FINST-OPC666D12G8WWVC8AYW6E93HM78T2ZFXKRXBMYI']
2025-06-15 22:32:12,058 - INFO - 批量插入完成，共 4 条记录
2025-06-15 22:32:12,058 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-06-15 22:32:12,058 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 0 条
2025-06-15 22:32:12,058 - INFO - 同步完成
