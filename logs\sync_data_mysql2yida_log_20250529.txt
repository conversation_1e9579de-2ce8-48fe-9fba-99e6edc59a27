2025-05-29 00:30:33,743 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 00:30:33,743 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 00:30:33,743 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 00:30:33,821 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 0 条记录
2025-05-29 00:30:33,821 - ERROR - 未获取到MySQL数据
2025-05-29 00:31:33,837 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 00:31:33,837 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 00:31:33,837 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 00:31:33,899 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 63 条记录
2025-05-29 00:31:33,899 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 00:31:33,899 - INFO - 开始处理日期: 2025-05-28
2025-05-29 00:31:33,899 - INFO - Request Parameters - Page 1:
2025-05-29 00:31:33,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:31:33,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:31:42,024 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9A505EDA-10A4-7437-AE37-0DD0EC5AEF89 Response: {'code': 'ServiceUnavailable', 'requestid': '9A505EDA-10A4-7437-AE37-0DD0EC5AEF89', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9A505EDA-10A4-7437-AE37-0DD0EC5AEF89)
2025-05-29 00:31:42,024 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 00:31:42,024 - INFO - 同步完成
2025-05-29 01:30:33,704 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 01:30:33,704 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 01:30:33,719 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 01:30:33,782 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 0 条记录
2025-05-29 01:30:33,782 - ERROR - 未获取到MySQL数据
2025-05-29 01:31:33,797 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 01:31:33,797 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 01:31:33,797 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 01:31:33,860 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 63 条记录
2025-05-29 01:31:33,860 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 01:31:33,860 - INFO - 开始处理日期: 2025-05-28
2025-05-29 01:31:33,860 - INFO - Request Parameters - Page 1:
2025-05-29 01:31:33,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 01:31:33,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 01:31:41,985 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DDE1E457-7441-7D31-9195-F582240036E5 Response: {'code': 'ServiceUnavailable', 'requestid': 'DDE1E457-7441-7D31-9195-F582240036E5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DDE1E457-7441-7D31-9195-F582240036E5)
2025-05-29 01:31:41,985 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 01:31:41,985 - INFO - 同步完成
2025-05-29 02:30:33,961 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 02:30:33,961 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 02:30:33,961 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 02:30:34,024 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 0 条记录
2025-05-29 02:30:34,024 - ERROR - 未获取到MySQL数据
2025-05-29 02:31:34,039 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 02:31:34,039 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 02:31:34,039 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 02:31:34,101 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 63 条记录
2025-05-29 02:31:34,101 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 02:31:34,101 - INFO - 开始处理日期: 2025-05-28
2025-05-29 02:31:34,101 - INFO - Request Parameters - Page 1:
2025-05-29 02:31:34,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 02:31:34,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 02:31:42,211 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 10564F0D-BF60-77DA-98A2-861342E88EDE Response: {'code': 'ServiceUnavailable', 'requestid': '10564F0D-BF60-77DA-98A2-861342E88EDE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 10564F0D-BF60-77DA-98A2-861342E88EDE)
2025-05-29 02:31:42,211 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 02:31:42,211 - INFO - 同步完成
2025-05-29 03:30:33,906 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 03:30:33,906 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 03:30:33,906 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 03:30:33,984 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 0 条记录
2025-05-29 03:30:33,984 - ERROR - 未获取到MySQL数据
2025-05-29 03:31:34,000 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 03:31:34,000 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 03:31:34,000 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 03:31:34,062 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 63 条记录
2025-05-29 03:31:34,062 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 03:31:34,062 - INFO - 开始处理日期: 2025-05-28
2025-05-29 03:31:34,062 - INFO - Request Parameters - Page 1:
2025-05-29 03:31:34,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:31:34,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:31:42,187 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 500C64F7-5A2C-7D01-ACF6-B06FDC1453D8 Response: {'code': 'ServiceUnavailable', 'requestid': '500C64F7-5A2C-7D01-ACF6-B06FDC1453D8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 500C64F7-5A2C-7D01-ACF6-B06FDC1453D8)
2025-05-29 03:31:42,187 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 03:31:42,187 - INFO - 同步完成
2025-05-29 04:30:34,086 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 04:30:34,086 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 04:30:34,086 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 04:30:34,148 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 0 条记录
2025-05-29 04:30:34,148 - ERROR - 未获取到MySQL数据
2025-05-29 04:31:34,163 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 04:31:34,163 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 04:31:34,163 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 04:31:34,226 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 63 条记录
2025-05-29 04:31:34,226 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 04:31:34,226 - INFO - 开始处理日期: 2025-05-28
2025-05-29 04:31:34,226 - INFO - Request Parameters - Page 1:
2025-05-29 04:31:34,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 04:31:34,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 04:31:42,351 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E3BEA37D-F056-78F8-ADFE-09ED72CA14A0 Response: {'code': 'ServiceUnavailable', 'requestid': 'E3BEA37D-F056-78F8-ADFE-09ED72CA14A0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E3BEA37D-F056-78F8-ADFE-09ED72CA14A0)
2025-05-29 04:31:42,351 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 04:31:42,351 - INFO - 同步完成
2025-05-29 05:30:34,171 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 05:30:34,171 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 05:30:34,171 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 05:30:34,233 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 0 条记录
2025-05-29 05:30:34,233 - ERROR - 未获取到MySQL数据
2025-05-29 05:31:34,249 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 05:31:34,249 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 05:31:34,249 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 05:31:34,311 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 63 条记录
2025-05-29 05:31:34,311 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 05:31:34,311 - INFO - 开始处理日期: 2025-05-28
2025-05-29 05:31:34,311 - INFO - Request Parameters - Page 1:
2025-05-29 05:31:34,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 05:31:34,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 05:31:42,420 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 09DC5668-F74D-7067-9013-33A283087AE8 Response: {'code': 'ServiceUnavailable', 'requestid': '09DC5668-F74D-7067-9013-33A283087AE8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 09DC5668-F74D-7067-9013-33A283087AE8)
2025-05-29 05:31:42,420 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 05:31:42,420 - INFO - 同步完成
2025-05-29 06:30:33,772 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 06:30:33,772 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 06:30:33,772 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 06:30:33,834 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 0 条记录
2025-05-29 06:30:33,834 - ERROR - 未获取到MySQL数据
2025-05-29 06:31:33,850 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 06:31:33,850 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 06:31:33,850 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 06:31:33,912 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 63 条记录
2025-05-29 06:31:33,912 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 06:31:33,912 - INFO - 开始处理日期: 2025-05-28
2025-05-29 06:31:33,912 - INFO - Request Parameters - Page 1:
2025-05-29 06:31:33,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:31:33,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:31:42,021 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 955B2C69-2570-741F-9F6C-7C54BA23884C Response: {'code': 'ServiceUnavailable', 'requestid': '955B2C69-2570-741F-9F6C-7C54BA23884C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 955B2C69-2570-741F-9F6C-7C54BA23884C)
2025-05-29 06:31:42,021 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 06:31:42,021 - INFO - 同步完成
2025-05-29 07:30:34,216 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 07:30:34,216 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 07:30:34,216 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 07:30:34,279 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 5 条记录
2025-05-29 07:30:34,279 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 07:30:34,279 - INFO - 开始处理日期: 2025-05-28
2025-05-29 07:30:34,279 - INFO - Request Parameters - Page 1:
2025-05-29 07:30:34,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 07:30:34,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 07:30:42,435 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 479045A0-3F6C-7242-A95B-61C1E89BC5A2 Response: {'code': 'ServiceUnavailable', 'requestid': '479045A0-3F6C-7242-A95B-61C1E89BC5A2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 479045A0-3F6C-7242-A95B-61C1E89BC5A2)
2025-05-29 07:30:42,435 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 07:31:42,450 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 07:31:42,450 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 07:31:42,450 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 07:31:42,513 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 74 条记录
2025-05-29 07:31:42,513 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 07:31:42,513 - INFO - 开始处理日期: 2025-05-28
2025-05-29 07:31:42,513 - INFO - Request Parameters - Page 1:
2025-05-29 07:31:42,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 07:31:42,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 07:31:50,622 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F59A5EFF-C315-77EB-9CD8-7FFB90E9FE3B Response: {'code': 'ServiceUnavailable', 'requestid': 'F59A5EFF-C315-77EB-9CD8-7FFB90E9FE3B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F59A5EFF-C315-77EB-9CD8-7FFB90E9FE3B)
2025-05-29 07:31:50,622 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 07:31:50,622 - INFO - 同步完成
2025-05-29 08:30:33,724 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 08:30:33,724 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 08:30:33,724 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 08:30:33,802 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 13 条记录
2025-05-29 08:30:33,802 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 08:30:33,802 - INFO - 开始处理日期: 2025-05-28
2025-05-29 08:30:33,802 - INFO - Request Parameters - Page 1:
2025-05-29 08:30:33,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 08:30:33,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 08:30:41,911 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BFFD55FD-00FE-79F5-A0AE-5EA4D78C8AFD Response: {'code': 'ServiceUnavailable', 'requestid': 'BFFD55FD-00FE-79F5-A0AE-5EA4D78C8AFD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BFFD55FD-00FE-79F5-A0AE-5EA4D78C8AFD)
2025-05-29 08:30:41,911 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 08:31:41,927 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 08:31:41,927 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 08:31:41,927 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 08:31:41,989 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 82 条记录
2025-05-29 08:31:41,989 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 08:31:41,989 - INFO - 开始处理日期: 2025-05-28
2025-05-29 08:31:41,989 - INFO - Request Parameters - Page 1:
2025-05-29 08:31:41,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 08:31:41,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 08:31:50,098 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C0BCC74C-4449-7C69-9BE8-70BEB80C4E73 Response: {'code': 'ServiceUnavailable', 'requestid': 'C0BCC74C-4449-7C69-9BE8-70BEB80C4E73', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C0BCC74C-4449-7C69-9BE8-70BEB80C4E73)
2025-05-29 08:31:50,098 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 08:31:50,098 - INFO - 同步完成
2025-05-29 09:30:33,934 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 09:30:33,934 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 09:30:33,950 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 09:30:34,012 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 102 条记录
2025-05-29 09:30:34,012 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 09:30:34,012 - INFO - 开始处理日期: 2025-05-28
2025-05-29 09:30:34,012 - INFO - Request Parameters - Page 1:
2025-05-29 09:30:34,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:30:34,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:30:42,122 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B615A91C-0BB1-7939-BAB8-1C804A3F9913 Response: {'code': 'ServiceUnavailable', 'requestid': 'B615A91C-0BB1-7939-BAB8-1C804A3F9913', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B615A91C-0BB1-7939-BAB8-1C804A3F9913)
2025-05-29 09:30:42,122 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 09:31:42,137 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 09:31:42,137 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 09:31:42,137 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 09:31:42,215 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 427 条记录
2025-05-29 09:31:42,215 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 09:31:42,215 - INFO - 开始处理日期: 2025-05-28
2025-05-29 09:31:42,215 - INFO - Request Parameters - Page 1:
2025-05-29 09:31:42,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:31:42,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:31:50,324 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C3B3D10B-A9A3-7BB5-BFEB-D3FA920C3DEB Response: {'code': 'ServiceUnavailable', 'requestid': 'C3B3D10B-A9A3-7BB5-BFEB-D3FA920C3DEB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C3B3D10B-A9A3-7BB5-BFEB-D3FA920C3DEB)
2025-05-29 09:31:50,324 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 09:31:50,324 - INFO - 同步完成
2025-05-29 10:30:33,957 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 10:30:33,957 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 10:30:33,957 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 10:30:34,035 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 180 条记录
2025-05-29 10:30:34,035 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 10:30:34,035 - INFO - 开始处理日期: 2025-05-28
2025-05-29 10:30:34,035 - INFO - Request Parameters - Page 1:
2025-05-29 10:30:34,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 10:30:34,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 10:30:42,145 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A5E32387-B5F1-7CFA-BFBA-A89194D3EA8C Response: {'code': 'ServiceUnavailable', 'requestid': 'A5E32387-B5F1-7CFA-BFBA-A89194D3EA8C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A5E32387-B5F1-7CFA-BFBA-A89194D3EA8C)
2025-05-29 10:30:42,145 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 10:31:42,160 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 10:31:42,160 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 10:31:42,160 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 10:31:42,238 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 548 条记录
2025-05-29 10:31:42,238 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 10:31:42,238 - INFO - 开始处理日期: 2025-05-28
2025-05-29 10:31:42,238 - INFO - Request Parameters - Page 1:
2025-05-29 10:31:42,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 10:31:42,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 10:31:50,347 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2708349F-9467-7AD1-94B1-ABD420CA52A9 Response: {'code': 'ServiceUnavailable', 'requestid': '2708349F-9467-7AD1-94B1-ABD420CA52A9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2708349F-9467-7AD1-94B1-ABD420CA52A9)
2025-05-29 10:31:50,347 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 10:31:50,347 - INFO - 同步完成
2025-05-29 11:30:34,215 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 11:30:34,215 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 11:30:34,215 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 11:30:34,293 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 182 条记录
2025-05-29 11:30:34,293 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 11:30:34,293 - INFO - 开始处理日期: 2025-05-28
2025-05-29 11:30:34,293 - INFO - Request Parameters - Page 1:
2025-05-29 11:30:34,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 11:30:34,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 11:30:42,418 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7ECFA352-2337-750A-940B-B4AAF3FD9CF3 Response: {'code': 'ServiceUnavailable', 'requestid': '7ECFA352-2337-750A-940B-B4AAF3FD9CF3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7ECFA352-2337-750A-940B-B4AAF3FD9CF3)
2025-05-29 11:30:42,418 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 11:31:42,449 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 11:31:42,449 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 11:31:42,449 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 11:31:42,527 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 550 条记录
2025-05-29 11:31:42,527 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 11:31:42,527 - INFO - 开始处理日期: 2025-05-28
2025-05-29 11:31:42,527 - INFO - Request Parameters - Page 1:
2025-05-29 11:31:42,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 11:31:42,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 11:31:43,339 - INFO - Response - Page 1:
2025-05-29 11:31:43,339 - INFO - 第 1 页获取到 62 条记录
2025-05-29 11:31:43,542 - INFO - 查询完成，共获取到 62 条记录
2025-05-29 11:31:43,542 - INFO - 获取到 62 条表单数据
2025-05-29 11:31:43,542 - INFO - 当前日期 2025-05-28 有 550 条MySQL数据需要处理
2025-05-29 11:31:43,542 - INFO - 开始更新记录 - 表单实例ID: FINST-74766M71G8TVV3D19KGZSB01QHCI33ZK2E7BM7
2025-05-29 11:31:43,980 - INFO - 更新表单数据成功: FINST-74766M71G8TVV3D19KGZSB01QHCI33ZK2E7BM7
2025-05-29 11:31:43,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 455.21, 'new_value': 541.04}, {'field': 'offline_amount', 'old_value': 6069.6, 'new_value': 6239.9}, {'field': 'total_amount', 'old_value': 6524.81, 'new_value': 6780.94}, {'field': 'order_count', 'old_value': 44, 'new_value': 56}]
2025-05-29 11:31:43,995 - INFO - 开始批量插入 488 条新记录
2025-05-29 11:31:44,339 - INFO - 批量插入响应状态码: 200
2025-05-29 11:31:44,355 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 03:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8CEAF951-4ABA-79A6-83B6-C97E1CBE6595', 'x-acs-trace-id': '9cbf294d715d2e2725310e3e98cf2663', 'etag': '42IHMXuLrW05knn2dFzAbtg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 11:31:44,355 - INFO - 批量插入响应体: {'result': ['FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMQB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMRB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMSB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMTB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMUB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMVB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMWB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMXB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMYB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMZB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM0C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM1C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM2C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM3C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM4C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM5C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM6C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM7C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM8C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM9C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMAC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMBC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMCC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMDC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMEC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMFC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMGC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMHC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMIC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMJC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMKC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMLC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMMC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMNC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMOC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMPC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMQC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMRC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMSC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMTC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMUC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMVC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMWC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMXC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMYC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMZC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM0D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM1D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM2D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM3D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM4D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM5D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM6D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM7D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM8D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM9D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMAD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMBD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMCD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMDD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMED', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMFD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMGD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMHD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMID', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMJD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMKD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMLD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMMD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMND', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMOD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMPD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMQD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMRD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMSD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMTD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMUD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMVD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMWD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMXD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMYD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMZD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM0E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM1E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM2E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM3E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM4E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM5E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM6E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM7E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM8E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM9E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMAE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMBE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMCE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMDE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMEE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMFE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMGE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMHE']}
2025-05-29 11:31:44,355 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-29 11:31:44,355 - INFO - 成功插入的数据ID: ['FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMQB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMRB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMSB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMTB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMUB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMVB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMWB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMXB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMYB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMZB', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM0C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM1C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM2C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM3C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM4C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM5C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM6C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM7C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM8C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM9C', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMAC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMBC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMCC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMDC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMEC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMFC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMGC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMHC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMIC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMJC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMKC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMLC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMMC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMNC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMOC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMPC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMQC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMRC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMSC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMTC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMUC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMVC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMWC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMXC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMYC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMZC', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM0D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM1D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM2D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM3D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM4D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM5D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM6D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM7D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM8D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BM9D', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMAD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMBD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMCD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMDD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMED', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMFD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMGD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMHD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMID', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMJD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMKD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMLD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMMD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMND', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMOD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMPD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMQD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMRD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMSD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMTD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMUD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMVD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMWD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMXD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMYD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMZD', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM0E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM1E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM2E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM3E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM4E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM5E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM6E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM7E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM8E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM9E', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMAE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMBE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMCE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMDE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMEE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMFE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMGE', 'FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BMHE']
2025-05-29 11:31:49,636 - INFO - 批量插入响应状态码: 200
2025-05-29 11:31:49,636 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 03:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CAD00357-F8D9-754C-B2D5-ECDA986ABAD1', 'x-acs-trace-id': 'fc478fe29c4416b7018be4fd64eb46fc', 'etag': '40ZItjdJWfohQWB0QeMJ1bg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 11:31:49,636 - INFO - 批量插入响应体: {'result': ['FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM74', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM84', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM94', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMA4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMB4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMC4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMD4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BME4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMF4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMG4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMH4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMI4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMJ4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMK4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BML4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMM4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMN4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMO4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMP4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMQ4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMR4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMS4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMT4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMU4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMV4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMW4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMX4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMY4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMZ4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM05', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM15', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM25', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM35', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM45', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM55', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM65', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM75', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM85', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM95', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMA5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMB5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMC5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMD5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BME5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMF5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMG5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMH5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMI5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMJ5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMK5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BML5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMM5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMN5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMO5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMP5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMQ5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMR5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMS5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMT5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMU5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMV5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMW5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMX5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMY5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMZ5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM06', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM16', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM26', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM36', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM46', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM56', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM66', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM76', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM86', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM96', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMA6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMB6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMC6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMD6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BME6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMF6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMG6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMH6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMI6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMJ6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMK6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BML6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMM6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMN6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMO6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMP6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMQ6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMR6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMS6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMT6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMU6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMV6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMW6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMX6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMY6']}
2025-05-29 11:31:49,636 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-29 11:31:49,636 - INFO - 成功插入的数据ID: ['FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM74', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM84', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM94', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMA4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMB4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMC4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMD4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BME4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMF4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMG4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMH4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMI4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMJ4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMK4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BML4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMM4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMN4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMO4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMP4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMQ4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMR4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMS4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMT4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMU4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMV4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMW4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMX4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMY4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMZ4', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM05', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM15', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM25', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM35', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM45', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM55', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM65', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM75', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM85', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BM95', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMA5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMB5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMC5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMD5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BME5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMF5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMG5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMH5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV720DCIT8BMI5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMJ5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMK5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BML5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMM5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMN5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMO5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMP5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMQ5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMR5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMS5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMT5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMU5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMV5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMW5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMX5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMY5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMZ5', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM06', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM16', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM26', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM36', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM46', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM56', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM66', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM76', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM86', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BM96', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMA6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMB6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMC6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMD6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BME6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMF6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMG6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMH6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMI6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMJ6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMK6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BML6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMM6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMN6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMO6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMP6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMQ6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMR6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMS6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMT6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMU6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMV6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMW6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMX6', 'FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMY6']
2025-05-29 11:31:54,933 - INFO - 批量插入响应状态码: 200
2025-05-29 11:31:54,933 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 03:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4796', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '67B210AB-CB08-71ED-B0D8-A01B0E5D494F', 'x-acs-trace-id': '27a2574603e3426c81eff3b1a8bc2bcb', 'etag': '4u3DkgVg7RXq9WO9tDJ53Pw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 11:31:54,948 - INFO - 批量插入响应体: {'result': ['FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMK', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BML', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMM', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMN', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMO', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMP', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMQ', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMR', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMS', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMT', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMU', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMV', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMW', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMX', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMY', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMZ', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM01', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM11', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM21', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM31', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM41', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM51', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM61', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM71', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM81', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM91', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMA1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMB1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMC1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMD1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BME1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMF1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMG1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMH1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMI1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMJ1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMK1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BML1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMM1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMN1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMO1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMP1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMQ1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMR1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMS1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMT1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMU1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMV1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMW1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMX1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMY1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMZ1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM02', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM12', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM22', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM32', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM42', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM52', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM62', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM72', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM82', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM92', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMA2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMB2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMC2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMD2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BME2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMF2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMG2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMH2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMI2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMJ2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMK2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BML2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMM2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMN2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMO2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMP2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMQ2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMR2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMS2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMT2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMU2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMV2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMW2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMX2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMY2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMZ2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM03', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM13', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM23', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM33', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM43', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM53', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM63', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM73', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM83', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM93', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMA3', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMB3']}
2025-05-29 11:31:54,948 - INFO - 批量插入表单数据成功，批次 3，共 100 条记录
2025-05-29 11:31:54,948 - INFO - 成功插入的数据ID: ['FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMK', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BML', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMM', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMN', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMO', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMP', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMQ', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMR', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMS', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMT', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMU', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMV', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMW', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMX', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMY', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMZ', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM01', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM11', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM21', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM31', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM41', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM51', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM61', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM71', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM81', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BM91', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMA1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMB1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMC1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMD1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BME1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMF1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMG1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMH1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMI1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMJ1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMK1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BML1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMM1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMN1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMO1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMP1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMQ1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMR1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3BGGIT8BMS1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMT1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMU1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMV1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMW1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMX1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMY1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMZ1', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM02', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM12', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM22', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM32', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM42', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM52', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM62', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM72', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM82', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM92', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMA2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMB2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMC2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMD2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BME2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMF2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMG2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMH2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMI2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMJ2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMK2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BML2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMM2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMN2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMO2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMP2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMQ2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMR2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMS2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMT2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMU2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMV2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMW2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMX2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMY2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMZ2', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM03', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM13', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM23', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM33', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM43', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM53', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM63', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM73', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM83', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BM93', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMA3', 'FINST-MLF66PA1H4RVPJ3EBXR23AM4IYUN3CGGIT8BMB3']
2025-05-29 11:32:00,245 - INFO - 批量插入响应状态码: 200
2025-05-29 11:32:00,245 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 03:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B9306768-6C48-7EFD-B938-7D13B5392080', 'x-acs-trace-id': '6710b58bb5d28e766a8cd95c9001ef3c', 'etag': '4Wu7ZyrxEBoHmPoSyX+8XAg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 11:32:00,245 - INFO - 批量插入响应体: {'result': ['FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMU7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMV7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMW7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMX7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMY7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMZ7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM08', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM18', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM28', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM38', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM48', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM58', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM68', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM78', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM88', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM98', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMA8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMB8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMC8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMD8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BME8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMF8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMG8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMH8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMI8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMJ8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMK8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BML8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMM8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMN8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMO8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMP8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMQ8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMR8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMS8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMT8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMU8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMV8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMW8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMX8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMY8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMZ8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM09', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM19', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM29', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM39', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM49', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM59', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM69', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM79', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM89', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM99', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMA9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMB9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMC9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMD9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BME9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMF9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMG9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMH9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMI9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMJ9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMK9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BML9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMM9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMN9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMO9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMP9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMQ9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMR9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMS9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMT9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMU9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMV9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMW9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMX9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMY9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMZ9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM0A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM1A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM2A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM3A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM4A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM5A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM6A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM7A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM8A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM9A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMAA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMBA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMCA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMDA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMEA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMFA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMGA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMHA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMIA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMJA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMKA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMLA']}
2025-05-29 11:32:00,245 - INFO - 批量插入表单数据成功，批次 4，共 100 条记录
2025-05-29 11:32:00,245 - INFO - 成功插入的数据ID: ['FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMU7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMV7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMW7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMX7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMY7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMZ7', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM08', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM18', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM28', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM38', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM48', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM58', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM68', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM78', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM88', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM98', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMA8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMB8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMC8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMD8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BME8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMF8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMG8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMH8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMI8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMJ8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMK8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BML8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMM8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMN8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMO8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMP8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMQ8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMR8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMS8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMT8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMU8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMV8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMW8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMX8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMY8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMZ8', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM09', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM19', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM29', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM39', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM49', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM59', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM69', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM79', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM89', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM99', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMA9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMB9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMC9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMD9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BME9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMF9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMG9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMH9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMI9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMJ9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMK9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BML9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMM9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMN9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMO9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMP9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMQ9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMR9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMS9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMT9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMU9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMV9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMW9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMX9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMY9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMZ9', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM0A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM1A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM2A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM3A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM4A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM5A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM6A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM7A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM8A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM9A', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMAA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMBA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMCA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMDA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMEA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMFA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMGA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMHA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMIA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMJA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMKA', 'FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BMLA']
2025-05-29 11:32:05,527 - INFO - 批量插入响应状态码: 200
2025-05-29 11:32:05,527 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 03:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4236', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6531171E-B74D-728C-AB7A-D054D9D8E426', 'x-acs-trace-id': 'b876a780a24c7bc0e9b66516e1a7dc4e', 'etag': '4ANBNuPmqE7cFvjPJ1vPjvw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 11:32:05,527 - INFO - 批量插入响应体: {'result': ['FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMLO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMMO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMNO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMOO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMPO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMQO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMSO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMTO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMUO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMVO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMWO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMXO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMYO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMZO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM0P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM1P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM2P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM3P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM4P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM5P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM6P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM7P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM8P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM9P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMAP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMBP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMCP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMDP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMEP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMFP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMGP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMHP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMIP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMJP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMLP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMMP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMNP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMOP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMPP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMQP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMSP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMTP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMUP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMVP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMWP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMXP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMYP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMZP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM0Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM1Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM2Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM3Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM4Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM5Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM6Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM7Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM8Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM9Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMAQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMBQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMCQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMDQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMEQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMFQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMGQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMHQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMIQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMJQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMLQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMMQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMNQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMOQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMPQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMQQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMSQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMTQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMUQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMVQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMWQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMXQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMYQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMZQ']}
2025-05-29 11:32:05,527 - INFO - 批量插入表单数据成功，批次 5，共 88 条记录
2025-05-29 11:32:05,527 - INFO - 成功插入的数据ID: ['FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMLO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMMO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMNO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMOO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMPO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMQO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMSO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMTO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMUO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMVO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMWO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMXO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMYO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMZO', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM0P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM1P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM2P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM3P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM4P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM5P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM6P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM7P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM8P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM9P', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMAP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMBP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMCP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMDP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMEP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMFP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMGP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMHP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMIP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMJP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMLP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMMP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMNP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMOP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMPP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMQP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMSP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMTP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMUP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMVP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMWP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMXP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMYP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMZP', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM0Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM1Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM2Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM3Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM4Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM5Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM6Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM7Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM8Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM9Q', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMAQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMBQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMCQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMDQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMEQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMFQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMGQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMHQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMIQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMJQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMLQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMMQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMNQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMOQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMPQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMQQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMSQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMTQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMUQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMVQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMWQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMXQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMYQ', 'FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMZQ']
2025-05-29 11:32:10,542 - INFO - 批量插入完成，共 488 条记录
2025-05-29 11:32:10,542 - INFO - 日期 2025-05-28 处理完成 - 更新: 1 条，插入: 488 条，错误: 0 条
2025-05-29 11:32:10,542 - INFO - 数据同步完成！更新: 1 条，插入: 488 条，错误: 0 条
2025-05-29 11:32:10,542 - INFO - 同步完成
2025-05-29 12:30:33,894 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 12:30:33,894 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 12:30:33,894 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 12:30:33,972 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 187 条记录
2025-05-29 12:30:33,972 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 12:30:33,972 - INFO - 开始处理日期: 2025-05-28
2025-05-29 12:30:33,972 - INFO - Request Parameters - Page 1:
2025-05-29 12:30:33,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:30:33,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:30:42,081 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AB02EB87-7607-7CD8-8CAA-C63F1E83649F Response: {'code': 'ServiceUnavailable', 'requestid': 'AB02EB87-7607-7CD8-8CAA-C63F1E83649F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AB02EB87-7607-7CD8-8CAA-C63F1E83649F)
2025-05-29 12:30:42,081 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 12:31:42,096 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 12:31:42,096 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 12:31:42,096 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 12:31:42,175 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 553 条记录
2025-05-29 12:31:42,175 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 12:31:42,175 - INFO - 开始处理日期: 2025-05-28
2025-05-29 12:31:42,175 - INFO - Request Parameters - Page 1:
2025-05-29 12:31:42,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:31:42,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:31:50,300 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 18525738-1BD9-7C32-98EF-EB5A7B2FC2EB Response: {'code': 'ServiceUnavailable', 'requestid': '18525738-1BD9-7C32-98EF-EB5A7B2FC2EB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 18525738-1BD9-7C32-98EF-EB5A7B2FC2EB)
2025-05-29 12:31:50,300 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 12:31:50,300 - INFO - 同步完成
2025-05-29 13:30:33,932 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 13:30:33,932 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 13:30:33,932 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 13:30:34,010 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 188 条记录
2025-05-29 13:30:34,010 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 13:30:34,010 - INFO - 开始处理日期: 2025-05-28
2025-05-29 13:30:34,010 - INFO - Request Parameters - Page 1:
2025-05-29 13:30:34,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 13:30:34,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 13:30:42,120 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 99D3AD92-E2C8-7AE9-9315-9300DCB6648C Response: {'code': 'ServiceUnavailable', 'requestid': '99D3AD92-E2C8-7AE9-9315-9300DCB6648C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 99D3AD92-E2C8-7AE9-9315-9300DCB6648C)
2025-05-29 13:30:42,120 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 13:31:42,135 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 13:31:42,135 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 13:31:42,135 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 13:31:42,213 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 554 条记录
2025-05-29 13:31:42,213 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 13:31:42,213 - INFO - 开始处理日期: 2025-05-28
2025-05-29 13:31:42,213 - INFO - Request Parameters - Page 1:
2025-05-29 13:31:42,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 13:31:42,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 13:31:50,338 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 75C5D9B8-6969-72A6-94EE-************ Response: {'code': 'ServiceUnavailable', 'requestid': '75C5D9B8-6969-72A6-94EE-************', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 75C5D9B8-6969-72A6-94EE-************)
2025-05-29 13:31:50,338 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 13:31:50,338 - INFO - 同步完成
2025-05-29 14:30:34,205 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 14:30:34,205 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 14:30:34,205 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 14:30:34,283 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 193 条记录
2025-05-29 14:30:34,283 - INFO - 获取到 2 个日期需要处理: ['2025-05-27', '2025-05-28']
2025-05-29 14:30:34,283 - INFO - 开始处理日期: 2025-05-27
2025-05-29 14:30:34,283 - INFO - Request Parameters - Page 1:
2025-05-29 14:30:34,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 14:30:34,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 14:30:42,393 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5BA1C0E7-7B3C-7DD7-83C7-ACD486FA3292 Response: {'code': 'ServiceUnavailable', 'requestid': '5BA1C0E7-7B3C-7DD7-83C7-ACD486FA3292', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5BA1C0E7-7B3C-7DD7-83C7-ACD486FA3292)
2025-05-29 14:30:42,393 - INFO - 开始处理日期: 2025-05-28
2025-05-29 14:30:42,393 - INFO - Request Parameters - Page 1:
2025-05-29 14:30:42,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 14:30:42,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 14:30:42,518 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 0A664E9B-0230-77AF-B2A2-FF8327D4E648 Response: {'requestid': '0A664E9B-0230-77AF-B2A2-FF8327D4E648', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 0A664E9B-0230-77AF-B2A2-FF8327D4E648)
2025-05-29 14:30:42,518 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-29 14:31:42,533 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 14:31:42,533 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 14:31:42,533 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 14:31:42,611 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 558 条记录
2025-05-29 14:31:42,611 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 14:31:42,611 - INFO - 开始处理日期: 2025-05-28
2025-05-29 14:31:42,611 - INFO - Request Parameters - Page 1:
2025-05-29 14:31:42,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 14:31:42,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 14:31:50,736 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 86F9FD4E-C096-762A-BF0E-FCD2B033E9A8 Response: {'code': 'ServiceUnavailable', 'requestid': '86F9FD4E-C096-762A-BF0E-FCD2B033E9A8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 86F9FD4E-C096-762A-BF0E-FCD2B033E9A8)
2025-05-29 14:31:50,736 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 14:31:50,736 - INFO - 同步完成
2025-05-29 15:30:34,197 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 15:30:34,197 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 15:30:34,197 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 15:30:34,275 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 193 条记录
2025-05-29 15:30:34,275 - INFO - 获取到 2 个日期需要处理: ['2025-05-27', '2025-05-28']
2025-05-29 15:30:34,275 - INFO - 开始处理日期: 2025-05-27
2025-05-29 15:30:34,275 - INFO - Request Parameters - Page 1:
2025-05-29 15:30:34,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:30:34,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:30:42,400 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8548C021-88FC-7A47-A2E1-62D4F8192CFC Response: {'code': 'ServiceUnavailable', 'requestid': '8548C021-88FC-7A47-A2E1-62D4F8192CFC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8548C021-88FC-7A47-A2E1-62D4F8192CFC)
2025-05-29 15:30:42,400 - INFO - 开始处理日期: 2025-05-28
2025-05-29 15:30:42,400 - INFO - Request Parameters - Page 1:
2025-05-29 15:30:42,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:30:42,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:30:42,541 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 62CACB86-B2FE-7326-8414-A4ADB99BC279 Response: {'requestid': '62CACB86-B2FE-7326-8414-A4ADB99BC279', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 62CACB86-B2FE-7326-8414-A4ADB99BC279)
2025-05-29 15:30:42,541 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-29 15:31:42,556 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 15:31:42,556 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 15:31:42,556 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 15:31:42,634 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 558 条记录
2025-05-29 15:31:42,634 - INFO - 获取到 1 个日期需要处理: ['2025-05-28']
2025-05-29 15:31:42,634 - INFO - 开始处理日期: 2025-05-28
2025-05-29 15:31:42,634 - INFO - Request Parameters - Page 1:
2025-05-29 15:31:42,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:31:42,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:31:50,759 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4718DA4B-3E81-7C26-81FB-C9971086C342 Response: {'code': 'ServiceUnavailable', 'requestid': '4718DA4B-3E81-7C26-81FB-C9971086C342', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4718DA4B-3E81-7C26-81FB-C9971086C342)
2025-05-29 15:31:50,759 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-29 15:31:50,759 - INFO - 同步完成
2025-05-29 16:30:33,907 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 16:30:33,907 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 16:30:33,907 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 16:30:33,970 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 200 条记录
2025-05-29 16:30:33,970 - INFO - 获取到 3 个日期需要处理: ['2025-05-27', '2025-05-28', '2025-05-29']
2025-05-29 16:30:33,970 - INFO - 开始处理日期: 2025-05-27
2025-05-29 16:30:33,985 - INFO - Request Parameters - Page 1:
2025-05-29 16:30:33,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:30:33,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:30:42,095 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EF061E6B-1BCC-717A-9249-DCB703CAA7D2 Response: {'code': 'ServiceUnavailable', 'requestid': 'EF061E6B-1BCC-717A-9249-DCB703CAA7D2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EF061E6B-1BCC-717A-9249-DCB703CAA7D2)
2025-05-29 16:30:42,095 - INFO - 开始处理日期: 2025-05-28
2025-05-29 16:30:42,095 - INFO - Request Parameters - Page 1:
2025-05-29 16:30:42,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:30:42,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:30:42,235 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 3E5A5BA1-F42C-73E6-A33C-59F2EF6DC8AD Response: {'requestid': '3E5A5BA1-F42C-73E6-A33C-59F2EF6DC8AD', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 3E5A5BA1-F42C-73E6-A33C-59F2EF6DC8AD)
2025-05-29 16:30:42,235 - INFO - 开始处理日期: 2025-05-29
2025-05-29 16:30:42,235 - INFO - Request Parameters - Page 1:
2025-05-29 16:30:42,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:30:42,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:30:42,376 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 8E1C7A33-0278-74D9-98E8-33FFA05D4278 Response: {'requestid': '8E1C7A33-0278-74D9-98E8-33FFA05D4278', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 8E1C7A33-0278-74D9-98E8-33FFA05D4278)
2025-05-29 16:30:42,376 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-29 16:31:42,391 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 16:31:42,391 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 16:31:42,391 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 16:31:42,469 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 576 条记录
2025-05-29 16:31:42,469 - INFO - 获取到 2 个日期需要处理: ['2025-05-28', '2025-05-29']
2025-05-29 16:31:42,469 - INFO - 开始处理日期: 2025-05-28
2025-05-29 16:31:42,469 - INFO - Request Parameters - Page 1:
2025-05-29 16:31:42,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:31:42,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:31:43,485 - INFO - Response - Page 1:
2025-05-29 16:31:43,485 - INFO - 第 1 页获取到 100 条记录
2025-05-29 16:31:43,688 - INFO - Request Parameters - Page 2:
2025-05-29 16:31:43,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:31:43,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:31:44,501 - INFO - Response - Page 2:
2025-05-29 16:31:44,501 - INFO - 第 2 页获取到 100 条记录
2025-05-29 16:31:44,704 - INFO - Request Parameters - Page 3:
2025-05-29 16:31:44,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:31:44,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:31:45,516 - INFO - Response - Page 3:
2025-05-29 16:31:45,516 - INFO - 第 3 页获取到 100 条记录
2025-05-29 16:31:45,719 - INFO - Request Parameters - Page 4:
2025-05-29 16:31:45,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:31:45,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:31:46,485 - INFO - Response - Page 4:
2025-05-29 16:31:46,485 - INFO - 第 4 页获取到 100 条记录
2025-05-29 16:31:46,688 - INFO - Request Parameters - Page 5:
2025-05-29 16:31:46,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:31:46,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:31:47,516 - INFO - Response - Page 5:
2025-05-29 16:31:47,516 - INFO - 第 5 页获取到 100 条记录
2025-05-29 16:31:47,719 - INFO - Request Parameters - Page 6:
2025-05-29 16:31:47,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:31:47,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:31:48,422 - INFO - Response - Page 6:
2025-05-29 16:31:48,422 - INFO - 第 6 页获取到 50 条记录
2025-05-29 16:31:48,626 - INFO - 查询完成，共获取到 550 条记录
2025-05-29 16:31:48,626 - INFO - 获取到 550 条表单数据
2025-05-29 16:31:48,626 - INFO - 当前日期 2025-05-28 有 575 条MySQL数据需要处理
2025-05-29 16:31:48,626 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC1F7RV0C79AICCT5P6D3QQ3QRES97BM2M
2025-05-29 16:31:49,157 - INFO - 更新表单数据成功: FINST-AEF66BC1F7RV0C79AICCT5P6D3QQ3QRES97BM2M
2025-05-29 16:31:49,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79906.49, 'new_value': 179.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 68345.29}, {'field': 'total_amount', 'old_value': 79906.49, 'new_value': 68524.29}, {'field': 'order_count', 'old_value': 272, 'new_value': 280}]
2025-05-29 16:31:49,157 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC1F7RV0C79AICCT5P6D3QQ3QRES97BM3M
2025-05-29 16:31:49,579 - INFO - 更新表单数据成功: FINST-AEF66BC1F7RV0C79AICCT5P6D3QQ3QRES97BM3M
2025-05-29 16:31:49,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4099.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 55401.0, 'new_value': 76100.0}, {'field': 'total_amount', 'old_value': 59500.0, 'new_value': 76100.0}, {'field': 'order_count', 'old_value': 1383, 'new_value': 1314}]
2025-05-29 16:31:49,594 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKO
2025-05-29 16:31:50,110 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKO
2025-05-29 16:31:50,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7416.0, 'new_value': 5133.0}, {'field': 'total_amount', 'old_value': 7416.0, 'new_value': 5133.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 25}]
2025-05-29 16:31:50,110 - INFO - 开始批量插入 25 条新记录
2025-05-29 16:31:50,297 - INFO - 批量插入响应状态码: 200
2025-05-29 16:31:50,297 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 08:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1212', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '053F7E05-1AF7-711E-AE82-04456F53118A', 'x-acs-trace-id': 'cb67acfe279b29203cbe325c4a6dac68', 'etag': '19nkBnSkP4Do/t1vVneSe6Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 16:31:50,297 - INFO - 批量插入响应体: {'result': ['FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM12', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM22', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM32', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM42', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM52', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM62', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM72', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM82', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM92', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMA2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMB2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMC2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMD2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BME2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMF2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMG2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMH2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMI2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMJ2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMK2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BML2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2VU5849BMM2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2VU5849BMN2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2VU5849BMO2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2VU5849BMP2']}
2025-05-29 16:31:50,297 - INFO - 批量插入表单数据成功，批次 1，共 25 条记录
2025-05-29 16:31:50,297 - INFO - 成功插入的数据ID: ['FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM12', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM22', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM32', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM42', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM52', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM62', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM72', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM82', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BM92', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMA2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMB2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMC2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMD2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BME2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMF2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMG2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMH2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMI2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMJ2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BMK2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2UU5849BML2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2VU5849BMM2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2VU5849BMN2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2VU5849BMO2', 'FINST-3ME66E8146TV2MR4CN0PQDGVI8HX2VU5849BMP2']
2025-05-29 16:31:55,313 - INFO - 批量插入完成，共 25 条记录
2025-05-29 16:31:55,313 - INFO - 日期 2025-05-28 处理完成 - 更新: 3 条，插入: 25 条，错误: 0 条
2025-05-29 16:31:55,313 - INFO - 开始处理日期: 2025-05-29
2025-05-29 16:31:55,313 - INFO - Request Parameters - Page 1:
2025-05-29 16:31:55,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 16:31:55,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 16:31:55,829 - INFO - Response - Page 1:
2025-05-29 16:31:55,829 - INFO - 查询完成，共获取到 0 条记录
2025-05-29 16:31:55,829 - INFO - 获取到 0 条表单数据
2025-05-29 16:31:55,829 - INFO - 当前日期 2025-05-29 有 1 条MySQL数据需要处理
2025-05-29 16:31:55,829 - INFO - 开始批量插入 1 条新记录
2025-05-29 16:31:55,985 - INFO - 批量插入响应状态码: 200
2025-05-29 16:31:55,985 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 08:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D28816DD-575E-7DFB-B7B7-5E762555240F', 'x-acs-trace-id': 'aa68789462406eb1b1a536f6a8dae0c3', 'etag': '6jSB0f2ahfTQMfbKgmaQkCw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 16:31:55,985 - INFO - 批量插入响应体: {'result': ['FINST-OJ666W715GSVM19DCVUVR9C63FHX2P8A849BMD2']}
2025-05-29 16:31:55,985 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-29 16:31:55,985 - INFO - 成功插入的数据ID: ['FINST-OJ666W715GSVM19DCVUVR9C63FHX2P8A849BMD2']
2025-05-29 16:32:01,001 - INFO - 批量插入完成，共 1 条记录
2025-05-29 16:32:01,001 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-29 16:32:01,001 - INFO - 数据同步完成！更新: 3 条，插入: 26 条，错误: 0 条
2025-05-29 16:32:01,001 - INFO - 同步完成
2025-05-29 17:30:33,899 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 17:30:33,899 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 17:30:33,899 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 17:30:33,977 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 202 条记录
2025-05-29 17:30:33,977 - INFO - 获取到 4 个日期需要处理: ['2025-04-29', '2025-05-27', '2025-05-28', '2025-05-29']
2025-05-29 17:30:33,977 - INFO - 开始处理日期: 2025-04-29
2025-05-29 17:30:33,977 - INFO - Request Parameters - Page 1:
2025-05-29 17:30:33,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:30:33,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:30:42,102 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F2059AFE-3E8A-7213-AC13-15D6E2ED2FA3 Response: {'code': 'ServiceUnavailable', 'requestid': 'F2059AFE-3E8A-7213-AC13-15D6E2ED2FA3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F2059AFE-3E8A-7213-AC13-15D6E2ED2FA3)
2025-05-29 17:30:42,102 - INFO - 开始处理日期: 2025-05-27
2025-05-29 17:30:42,102 - INFO - Request Parameters - Page 1:
2025-05-29 17:30:42,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:30:42,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:30:50,258 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7589E73D-C0BF-73EC-9EF5-AEB2271ACABA Response: {'code': 'ServiceUnavailable', 'requestid': '7589E73D-C0BF-73EC-9EF5-AEB2271ACABA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7589E73D-C0BF-73EC-9EF5-AEB2271ACABA)
2025-05-29 17:30:50,258 - INFO - 开始处理日期: 2025-05-28
2025-05-29 17:30:50,258 - INFO - Request Parameters - Page 1:
2025-05-29 17:30:50,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:30:50,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:30:58,368 - ERROR - 处理日期 2025-05-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 582B8E4B-D931-7A38-B4B1-BDC0985E4944 Response: {'code': 'ServiceUnavailable', 'requestid': '582B8E4B-D931-7A38-B4B1-BDC0985E4944', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 582B8E4B-D931-7A38-B4B1-BDC0985E4944)
2025-05-29 17:30:58,368 - INFO - 开始处理日期: 2025-05-29
2025-05-29 17:30:58,368 - INFO - Request Parameters - Page 1:
2025-05-29 17:30:58,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:30:58,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:31:00,930 - INFO - Response - Page 1:
2025-05-29 17:31:00,930 - INFO - 第 1 页获取到 1 条记录
2025-05-29 17:31:01,133 - INFO - 查询完成，共获取到 1 条记录
2025-05-29 17:31:01,133 - INFO - 获取到 1 条表单数据
2025-05-29 17:31:01,133 - INFO - 当前日期 2025-05-29 有 1 条MySQL数据需要处理
2025-05-29 17:31:01,133 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 17:31:01,133 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-29 17:32:01,149 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 17:32:01,149 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 17:32:01,149 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 17:32:01,227 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 576 条记录
2025-05-29 17:32:01,227 - INFO - 获取到 2 个日期需要处理: ['2025-05-28', '2025-05-29']
2025-05-29 17:32:01,227 - INFO - 开始处理日期: 2025-05-28
2025-05-29 17:32:01,227 - INFO - Request Parameters - Page 1:
2025-05-29 17:32:01,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:32:01,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:32:02,133 - INFO - Response - Page 1:
2025-05-29 17:32:02,133 - INFO - 第 1 页获取到 100 条记录
2025-05-29 17:32:02,336 - INFO - Request Parameters - Page 2:
2025-05-29 17:32:02,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:32:02,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:32:03,086 - INFO - Response - Page 2:
2025-05-29 17:32:03,086 - INFO - 第 2 页获取到 100 条记录
2025-05-29 17:32:03,289 - INFO - Request Parameters - Page 3:
2025-05-29 17:32:03,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:32:03,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:32:04,164 - INFO - Response - Page 3:
2025-05-29 17:32:04,164 - INFO - 第 3 页获取到 100 条记录
2025-05-29 17:32:04,367 - INFO - Request Parameters - Page 4:
2025-05-29 17:32:04,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:32:04,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:32:05,336 - INFO - Response - Page 4:
2025-05-29 17:32:05,336 - INFO - 第 4 页获取到 100 条记录
2025-05-29 17:32:05,539 - INFO - Request Parameters - Page 5:
2025-05-29 17:32:05,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:32:05,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:32:06,430 - INFO - Response - Page 5:
2025-05-29 17:32:06,430 - INFO - 第 5 页获取到 100 条记录
2025-05-29 17:32:06,633 - INFO - Request Parameters - Page 6:
2025-05-29 17:32:06,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:32:06,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:32:07,461 - INFO - Response - Page 6:
2025-05-29 17:32:07,461 - INFO - 第 6 页获取到 75 条记录
2025-05-29 17:32:07,664 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 17:32:07,664 - INFO - 获取到 575 条表单数据
2025-05-29 17:32:07,664 - INFO - 当前日期 2025-05-28 有 575 条MySQL数据需要处理
2025-05-29 17:32:07,680 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM1Q
2025-05-29 17:32:08,180 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM1Q
2025-05-29 17:32:08,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15000.0, 'new_value': 9087.04}, {'field': 'total_amount', 'old_value': 15000.0, 'new_value': 9087.04}, {'field': 'order_count', 'old_value': 450, 'new_value': 409}]
2025-05-29 17:32:08,180 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM3Q
2025-05-29 17:32:08,680 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM3Q
2025-05-29 17:32:08,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3188.04}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3188.04}, {'field': 'order_count', 'old_value': 0, 'new_value': 99}]
2025-05-29 17:32:08,680 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM4Q
2025-05-29 17:32:09,164 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM4Q
2025-05-29 17:32:09,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2000.0, 'new_value': 996.0}, {'field': 'total_amount', 'old_value': 2000.0, 'new_value': 996.0}]
2025-05-29 17:32:09,164 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM5Q
2025-05-29 17:32:09,789 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM5Q
2025-05-29 17:32:09,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1200.0, 'new_value': 1284.0}, {'field': 'total_amount', 'old_value': 1200.0, 'new_value': 1284.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 119}]
2025-05-29 17:32:09,789 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM6Q
2025-05-29 17:32:10,242 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM6Q
2025-05-29 17:32:10,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20000.0, 'new_value': 6511.96}, {'field': 'total_amount', 'old_value': 20000.0, 'new_value': 6511.96}, {'field': 'order_count', 'old_value': 1, 'new_value': 21}]
2025-05-29 17:32:10,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM7Q
2025-05-29 17:32:10,695 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM7Q
2025-05-29 17:32:10,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200.0, 'new_value': 4999.0}, {'field': 'total_amount', 'old_value': 200.0, 'new_value': 4999.0}]
2025-05-29 17:32:10,695 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM8Q
2025-05-29 17:32:11,227 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM8Q
2025-05-29 17:32:11,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40000.0, 'new_value': 124500.0}, {'field': 'total_amount', 'old_value': 40000.0, 'new_value': 124500.0}]
2025-05-29 17:32:11,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMBQ
2025-05-29 17:32:11,727 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMBQ
2025-05-29 17:32:11,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-29 17:32:11,727 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMCQ
2025-05-29 17:32:12,242 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMCQ
2025-05-29 17:32:12,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-29 17:32:12,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMEQ
2025-05-29 17:32:12,648 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMEQ
2025-05-29 17:32:12,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4500.0, 'new_value': 3718.0}, {'field': 'total_amount', 'old_value': 4500.0, 'new_value': 3718.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 194}]
2025-05-29 17:32:12,648 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMFQ
2025-05-29 17:32:13,102 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMFQ
2025-05-29 17:32:13,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2200.0, 'new_value': 4854.0}, {'field': 'total_amount', 'old_value': 2200.0, 'new_value': 4854.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 39}]
2025-05-29 17:32:13,102 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMJQ
2025-05-29 17:32:13,586 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMJQ
2025-05-29 17:32:13,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-29 17:32:13,586 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKQ
2025-05-29 17:32:14,164 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMKQ
2025-05-29 17:32:14,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40000.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 40000.0, 'new_value': 50000.0}]
2025-05-29 17:32:14,164 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMMQ
2025-05-29 17:32:14,617 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMMQ
2025-05-29 17:32:14,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1600.0, 'new_value': 1471.0}, {'field': 'total_amount', 'old_value': 1600.0, 'new_value': 1471.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 138}]
2025-05-29 17:32:14,617 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMNQ
2025-05-29 17:32:15,070 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMNQ
2025-05-29 17:32:15,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 469.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 469.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 15}]
2025-05-29 17:32:15,070 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMOQ
2025-05-29 17:32:15,633 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMOQ
2025-05-29 17:32:15,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3400.0, 'new_value': 9425.11}, {'field': 'total_amount', 'old_value': 3400.0, 'new_value': 9425.11}, {'field': 'order_count', 'old_value': 1, 'new_value': 495}]
2025-05-29 17:32:15,633 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRQ
2025-05-29 17:32:16,086 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRQ
2025-05-29 17:32:16,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 12000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 12000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-29 17:32:16,086 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMSQ
2025-05-29 17:32:16,508 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMSQ
2025-05-29 17:32:16,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3329.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3329.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-05-29 17:32:16,508 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMUQ
2025-05-29 17:32:16,945 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMUQ
2025-05-29 17:32:16,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7764.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7764.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 5}]
2025-05-29 17:32:16,945 - INFO - 日期 2025-05-28 处理完成 - 更新: 19 条，插入: 0 条，错误: 0 条
2025-05-29 17:32:16,945 - INFO - 开始处理日期: 2025-05-29
2025-05-29 17:32:16,945 - INFO - Request Parameters - Page 1:
2025-05-29 17:32:16,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 17:32:16,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 17:32:17,477 - INFO - Response - Page 1:
2025-05-29 17:32:17,477 - INFO - 第 1 页获取到 1 条记录
2025-05-29 17:32:17,680 - INFO - 查询完成，共获取到 1 条记录
2025-05-29 17:32:17,680 - INFO - 获取到 1 条表单数据
2025-05-29 17:32:17,680 - INFO - 当前日期 2025-05-29 有 1 条MySQL数据需要处理
2025-05-29 17:32:17,680 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 17:32:17,680 - INFO - 数据同步完成！更新: 19 条，插入: 0 条，错误: 0 条
2025-05-29 17:32:17,680 - INFO - 同步完成
2025-05-29 18:30:34,310 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 18:30:34,310 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 18:30:34,310 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 18:30:34,385 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 202 条记录
2025-05-29 18:30:34,386 - INFO - 获取到 4 个日期需要处理: ['2025-04-29', '2025-05-27', '2025-05-28', '2025-05-29']
2025-05-29 18:30:34,388 - INFO - 开始处理日期: 2025-04-29
2025-05-29 18:30:34,390 - INFO - Request Parameters - Page 1:
2025-05-29 18:30:34,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:30:34,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:30:42,511 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 52C033E3-57CA-7C69-A4E2-C8D29553AD22 Response: {'code': 'ServiceUnavailable', 'requestid': '52C033E3-57CA-7C69-A4E2-C8D29553AD22', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 52C033E3-57CA-7C69-A4E2-C8D29553AD22)
2025-05-29 18:30:42,511 - INFO - 开始处理日期: 2025-05-27
2025-05-29 18:30:42,512 - INFO - Request Parameters - Page 1:
2025-05-29 18:30:42,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:30:42,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:30:50,630 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 19FC7113-07A6-7A77-92A1-5943F8A27CA6 Response: {'code': 'ServiceUnavailable', 'requestid': '19FC7113-07A6-7A77-92A1-5943F8A27CA6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 19FC7113-07A6-7A77-92A1-5943F8A27CA6)
2025-05-29 18:30:50,631 - INFO - 开始处理日期: 2025-05-28
2025-05-29 18:30:50,631 - INFO - Request Parameters - Page 1:
2025-05-29 18:30:50,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:30:50,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:30:58,775 - INFO - Response - Page 1:
2025-05-29 18:30:58,775 - INFO - 第 1 页获取到 100 条记录
2025-05-29 18:30:58,976 - INFO - Request Parameters - Page 2:
2025-05-29 18:30:58,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:30:58,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:30:59,892 - INFO - Response - Page 2:
2025-05-29 18:30:59,892 - INFO - 第 2 页获取到 100 条记录
2025-05-29 18:31:00,092 - INFO - Request Parameters - Page 3:
2025-05-29 18:31:00,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:31:00,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:31:00,887 - INFO - Response - Page 3:
2025-05-29 18:31:00,888 - INFO - 第 3 页获取到 100 条记录
2025-05-29 18:31:01,088 - INFO - Request Parameters - Page 4:
2025-05-29 18:31:01,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:31:01,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:31:01,886 - INFO - Response - Page 4:
2025-05-29 18:31:01,886 - INFO - 第 4 页获取到 100 条记录
2025-05-29 18:31:02,086 - INFO - Request Parameters - Page 5:
2025-05-29 18:31:02,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:31:02,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:31:02,903 - INFO - Response - Page 5:
2025-05-29 18:31:02,904 - INFO - 第 5 页获取到 100 条记录
2025-05-29 18:31:03,104 - INFO - Request Parameters - Page 6:
2025-05-29 18:31:03,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:31:03,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:31:03,867 - INFO - Response - Page 6:
2025-05-29 18:31:03,867 - INFO - 第 6 页获取到 75 条记录
2025-05-29 18:31:04,067 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 18:31:04,067 - INFO - 获取到 575 条表单数据
2025-05-29 18:31:04,077 - INFO - 当前日期 2025-05-28 有 198 条MySQL数据需要处理
2025-05-29 18:31:04,080 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 18:31:04,080 - INFO - 开始处理日期: 2025-05-29
2025-05-29 18:31:04,080 - INFO - Request Parameters - Page 1:
2025-05-29 18:31:04,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:31:04,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:31:04,576 - INFO - Response - Page 1:
2025-05-29 18:31:04,576 - INFO - 第 1 页获取到 1 条记录
2025-05-29 18:31:04,776 - INFO - 查询完成，共获取到 1 条记录
2025-05-29 18:31:04,776 - INFO - 获取到 1 条表单数据
2025-05-29 18:31:04,777 - INFO - 当前日期 2025-05-29 有 1 条MySQL数据需要处理
2025-05-29 18:31:04,777 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 18:31:04,777 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-29 18:32:04,778 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 18:32:04,778 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 18:32:04,778 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 18:32:04,864 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 576 条记录
2025-05-29 18:32:04,864 - INFO - 获取到 2 个日期需要处理: ['2025-05-28', '2025-05-29']
2025-05-29 18:32:04,869 - INFO - 开始处理日期: 2025-05-28
2025-05-29 18:32:04,869 - INFO - Request Parameters - Page 1:
2025-05-29 18:32:04,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:32:04,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:32:05,733 - INFO - Response - Page 1:
2025-05-29 18:32:05,733 - INFO - 第 1 页获取到 100 条记录
2025-05-29 18:32:05,933 - INFO - Request Parameters - Page 2:
2025-05-29 18:32:05,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:32:05,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:32:06,744 - INFO - Response - Page 2:
2025-05-29 18:32:06,745 - INFO - 第 2 页获取到 100 条记录
2025-05-29 18:32:06,945 - INFO - Request Parameters - Page 3:
2025-05-29 18:32:06,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:32:06,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:32:07,795 - INFO - Response - Page 3:
2025-05-29 18:32:07,795 - INFO - 第 3 页获取到 100 条记录
2025-05-29 18:32:07,996 - INFO - Request Parameters - Page 4:
2025-05-29 18:32:07,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:32:07,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:32:08,899 - INFO - Response - Page 4:
2025-05-29 18:32:08,900 - INFO - 第 4 页获取到 100 条记录
2025-05-29 18:32:09,100 - INFO - Request Parameters - Page 5:
2025-05-29 18:32:09,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:32:09,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:32:09,918 - INFO - Response - Page 5:
2025-05-29 18:32:09,919 - INFO - 第 5 页获取到 100 条记录
2025-05-29 18:32:10,120 - INFO - Request Parameters - Page 6:
2025-05-29 18:32:10,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:32:10,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:32:10,885 - INFO - Response - Page 6:
2025-05-29 18:32:10,885 - INFO - 第 6 页获取到 75 条记录
2025-05-29 18:32:11,085 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 18:32:11,085 - INFO - 获取到 575 条表单数据
2025-05-29 18:32:11,094 - INFO - 当前日期 2025-05-28 有 575 条MySQL数据需要处理
2025-05-29 18:32:11,104 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 18:32:11,104 - INFO - 开始处理日期: 2025-05-29
2025-05-29 18:32:11,104 - INFO - Request Parameters - Page 1:
2025-05-29 18:32:11,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:32:11,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:32:11,613 - INFO - Response - Page 1:
2025-05-29 18:32:11,613 - INFO - 第 1 页获取到 1 条记录
2025-05-29 18:32:11,813 - INFO - 查询完成，共获取到 1 条记录
2025-05-29 18:32:11,813 - INFO - 获取到 1 条表单数据
2025-05-29 18:32:11,814 - INFO - 当前日期 2025-05-29 有 1 条MySQL数据需要处理
2025-05-29 18:32:11,814 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 18:32:11,814 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 18:32:11,814 - INFO - 同步完成
2025-05-29 19:30:34,830 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 19:30:34,830 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 19:30:34,830 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 19:30:34,908 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 202 条记录
2025-05-29 19:30:34,908 - INFO - 获取到 4 个日期需要处理: ['2025-04-29', '2025-05-27', '2025-05-28', '2025-05-29']
2025-05-29 19:30:34,910 - INFO - 开始处理日期: 2025-04-29
2025-05-29 19:30:34,913 - INFO - Request Parameters - Page 1:
2025-05-29 19:30:34,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:34,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:43,033 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B1CD488D-76EE-7FB8-AA10-2391DFB7CB6D Response: {'code': 'ServiceUnavailable', 'requestid': 'B1CD488D-76EE-7FB8-AA10-2391DFB7CB6D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B1CD488D-76EE-7FB8-AA10-2391DFB7CB6D)
2025-05-29 19:30:43,033 - INFO - 开始处理日期: 2025-05-27
2025-05-29 19:30:43,034 - INFO - Request Parameters - Page 1:
2025-05-29 19:30:43,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:43,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:51,139 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8E045E98-4D1F-7676-A7E6-647F959D8EC2 Response: {'code': 'ServiceUnavailable', 'requestid': '8E045E98-4D1F-7676-A7E6-647F959D8EC2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8E045E98-4D1F-7676-A7E6-647F959D8EC2)
2025-05-29 19:30:51,139 - INFO - 开始处理日期: 2025-05-28
2025-05-29 19:30:51,139 - INFO - Request Parameters - Page 1:
2025-05-29 19:30:51,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:51,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:53,621 - INFO - Response - Page 1:
2025-05-29 19:30:53,621 - INFO - 第 1 页获取到 100 条记录
2025-05-29 19:30:53,821 - INFO - Request Parameters - Page 2:
2025-05-29 19:30:53,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:53,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:54,591 - INFO - Response - Page 2:
2025-05-29 19:30:54,591 - INFO - 第 2 页获取到 100 条记录
2025-05-29 19:30:54,791 - INFO - Request Parameters - Page 3:
2025-05-29 19:30:54,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:54,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:55,622 - INFO - Response - Page 3:
2025-05-29 19:30:55,623 - INFO - 第 3 页获取到 100 条记录
2025-05-29 19:30:55,823 - INFO - Request Parameters - Page 4:
2025-05-29 19:30:55,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:55,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:56,837 - INFO - Response - Page 4:
2025-05-29 19:30:56,837 - INFO - 第 4 页获取到 100 条记录
2025-05-29 19:30:57,037 - INFO - Request Parameters - Page 5:
2025-05-29 19:30:57,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:57,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:57,870 - INFO - Response - Page 5:
2025-05-29 19:30:57,870 - INFO - 第 5 页获取到 100 条记录
2025-05-29 19:30:58,071 - INFO - Request Parameters - Page 6:
2025-05-29 19:30:58,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:58,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:58,832 - INFO - Response - Page 6:
2025-05-29 19:30:58,832 - INFO - 第 6 页获取到 75 条记录
2025-05-29 19:30:59,033 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 19:30:59,033 - INFO - 获取到 575 条表单数据
2025-05-29 19:30:59,042 - INFO - 当前日期 2025-05-28 有 198 条MySQL数据需要处理
2025-05-29 19:30:59,045 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 19:30:59,046 - INFO - 开始处理日期: 2025-05-29
2025-05-29 19:30:59,046 - INFO - Request Parameters - Page 1:
2025-05-29 19:30:59,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:30:59,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:30:59,717 - INFO - Response - Page 1:
2025-05-29 19:30:59,717 - INFO - 第 1 页获取到 1 条记录
2025-05-29 19:30:59,918 - INFO - 查询完成，共获取到 1 条记录
2025-05-29 19:30:59,918 - INFO - 获取到 1 条表单数据
2025-05-29 19:30:59,918 - INFO - 当前日期 2025-05-29 有 1 条MySQL数据需要处理
2025-05-29 19:30:59,918 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 19:30:59,918 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-29 19:31:59,929 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 19:31:59,929 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 19:31:59,929 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 19:32:00,014 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 576 条记录
2025-05-29 19:32:00,014 - INFO - 获取到 2 个日期需要处理: ['2025-05-28', '2025-05-29']
2025-05-29 19:32:00,019 - INFO - 开始处理日期: 2025-05-28
2025-05-29 19:32:00,019 - INFO - Request Parameters - Page 1:
2025-05-29 19:32:00,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:32:00,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:32:00,945 - INFO - Response - Page 1:
2025-05-29 19:32:00,945 - INFO - 第 1 页获取到 100 条记录
2025-05-29 19:32:01,145 - INFO - Request Parameters - Page 2:
2025-05-29 19:32:01,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:32:01,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:32:01,944 - INFO - Response - Page 2:
2025-05-29 19:32:01,944 - INFO - 第 2 页获取到 100 条记录
2025-05-29 19:32:02,144 - INFO - Request Parameters - Page 3:
2025-05-29 19:32:02,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:32:02,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:32:03,068 - INFO - Response - Page 3:
2025-05-29 19:32:03,068 - INFO - 第 3 页获取到 100 条记录
2025-05-29 19:32:03,269 - INFO - Request Parameters - Page 4:
2025-05-29 19:32:03,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:32:03,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:32:04,100 - INFO - Response - Page 4:
2025-05-29 19:32:04,100 - INFO - 第 4 页获取到 100 条记录
2025-05-29 19:32:04,302 - INFO - Request Parameters - Page 5:
2025-05-29 19:32:04,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:32:04,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:32:05,171 - INFO - Response - Page 5:
2025-05-29 19:32:05,171 - INFO - 第 5 页获取到 100 条记录
2025-05-29 19:32:05,371 - INFO - Request Parameters - Page 6:
2025-05-29 19:32:05,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:32:05,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:32:06,148 - INFO - Response - Page 6:
2025-05-29 19:32:06,148 - INFO - 第 6 页获取到 75 条记录
2025-05-29 19:32:06,348 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 19:32:06,348 - INFO - 获取到 575 条表单数据
2025-05-29 19:32:06,359 - INFO - 当前日期 2025-05-28 有 575 条MySQL数据需要处理
2025-05-29 19:32:06,368 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 19:32:06,369 - INFO - 开始处理日期: 2025-05-29
2025-05-29 19:32:06,369 - INFO - Request Parameters - Page 1:
2025-05-29 19:32:06,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 19:32:06,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 19:32:06,875 - INFO - Response - Page 1:
2025-05-29 19:32:06,875 - INFO - 第 1 页获取到 1 条记录
2025-05-29 19:32:07,075 - INFO - 查询完成，共获取到 1 条记录
2025-05-29 19:32:07,075 - INFO - 获取到 1 条表单数据
2025-05-29 19:32:07,076 - INFO - 当前日期 2025-05-29 有 1 条MySQL数据需要处理
2025-05-29 19:32:07,076 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 19:32:07,076 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 19:32:07,076 - INFO - 同步完成
2025-05-29 20:30:34,256 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 20:30:34,257 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 20:30:34,257 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 20:30:34,334 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 203 条记录
2025-05-29 20:30:34,335 - INFO - 获取到 4 个日期需要处理: ['2025-04-29', '2025-05-27', '2025-05-28', '2025-05-29']
2025-05-29 20:30:34,336 - INFO - 开始处理日期: 2025-04-29
2025-05-29 20:30:34,339 - INFO - Request Parameters - Page 1:
2025-05-29 20:30:34,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:30:34,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:30:42,473 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3E9F20E1-7C65-7EAE-9374-7CC661CC4789 Response: {'code': 'ServiceUnavailable', 'requestid': '3E9F20E1-7C65-7EAE-9374-7CC661CC4789', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3E9F20E1-7C65-7EAE-9374-7CC661CC4789)
2025-05-29 20:30:42,473 - INFO - 开始处理日期: 2025-05-27
2025-05-29 20:30:42,473 - INFO - Request Parameters - Page 1:
2025-05-29 20:30:42,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:30:42,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:30:50,591 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4C5D6ADB-2D42-759E-A34A-3C8E956F815C Response: {'code': 'ServiceUnavailable', 'requestid': '4C5D6ADB-2D42-759E-A34A-3C8E956F815C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4C5D6ADB-2D42-759E-A34A-3C8E956F815C)
2025-05-29 20:30:50,592 - INFO - 开始处理日期: 2025-05-28
2025-05-29 20:30:50,592 - INFO - Request Parameters - Page 1:
2025-05-29 20:30:50,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:30:50,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:30:51,512 - INFO - Response - Page 1:
2025-05-29 20:30:51,512 - INFO - 第 1 页获取到 100 条记录
2025-05-29 20:30:51,712 - INFO - Request Parameters - Page 2:
2025-05-29 20:30:51,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:30:51,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:30:52,609 - INFO - Response - Page 2:
2025-05-29 20:30:52,610 - INFO - 第 2 页获取到 100 条记录
2025-05-29 20:30:52,810 - INFO - Request Parameters - Page 3:
2025-05-29 20:30:52,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:30:52,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:30:53,679 - INFO - Response - Page 3:
2025-05-29 20:30:53,679 - INFO - 第 3 页获取到 100 条记录
2025-05-29 20:30:53,881 - INFO - Request Parameters - Page 4:
2025-05-29 20:30:53,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:30:53,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:31:01,267 - INFO - Response - Page 4:
2025-05-29 20:31:01,267 - INFO - 第 4 页获取到 100 条记录
2025-05-29 20:31:01,467 - INFO - Request Parameters - Page 5:
2025-05-29 20:31:01,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:31:01,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:31:02,252 - INFO - Response - Page 5:
2025-05-29 20:31:02,253 - INFO - 第 5 页获取到 100 条记录
2025-05-29 20:31:02,453 - INFO - Request Parameters - Page 6:
2025-05-29 20:31:02,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:31:02,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:31:03,282 - INFO - Response - Page 6:
2025-05-29 20:31:03,283 - INFO - 第 6 页获取到 75 条记录
2025-05-29 20:31:03,484 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 20:31:03,484 - INFO - 获取到 575 条表单数据
2025-05-29 20:31:03,493 - INFO - 当前日期 2025-05-28 有 198 条MySQL数据需要处理
2025-05-29 20:31:03,496 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 20:31:03,496 - INFO - 开始处理日期: 2025-05-29
2025-05-29 20:31:03,496 - INFO - Request Parameters - Page 1:
2025-05-29 20:31:03,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:31:03,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:31:04,053 - INFO - Response - Page 1:
2025-05-29 20:31:04,053 - INFO - 第 1 页获取到 1 条记录
2025-05-29 20:31:04,253 - INFO - 查询完成，共获取到 1 条记录
2025-05-29 20:31:04,253 - INFO - 获取到 1 条表单数据
2025-05-29 20:31:04,254 - INFO - 当前日期 2025-05-29 有 2 条MySQL数据需要处理
2025-05-29 20:31:04,254 - INFO - 开始批量插入 1 条新记录
2025-05-29 20:31:04,427 - INFO - 批量插入响应状态码: 200
2025-05-29 20:31:04,427 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 12:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '75BEDAF7-6380-70FF-8DEA-C13BBC7C83D3', 'x-acs-trace-id': '494e4782b56036c6bb8576b26d59e566', 'etag': '643B9qgE+zR73I+fAylamCw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 20:31:04,427 - INFO - 批量插入响应体: {'result': ['FINST-CPC66T911AUV9PSAAZ1ROAJ76DVJ2QMSRC9BMQ2']}
2025-05-29 20:31:04,427 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-29 20:31:04,427 - INFO - 成功插入的数据ID: ['FINST-CPC66T911AUV9PSAAZ1ROAJ76DVJ2QMSRC9BMQ2']
2025-05-29 20:31:09,429 - INFO - 批量插入完成，共 1 条记录
2025-05-29 20:31:09,429 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-29 20:31:09,429 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-05-29 20:32:09,439 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 20:32:09,439 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 20:32:09,439 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 20:32:09,523 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 577 条记录
2025-05-29 20:32:09,523 - INFO - 获取到 2 个日期需要处理: ['2025-05-28', '2025-05-29']
2025-05-29 20:32:09,528 - INFO - 开始处理日期: 2025-05-28
2025-05-29 20:32:09,528 - INFO - Request Parameters - Page 1:
2025-05-29 20:32:09,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:32:09,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:32:10,427 - INFO - Response - Page 1:
2025-05-29 20:32:10,427 - INFO - 第 1 页获取到 100 条记录
2025-05-29 20:32:10,627 - INFO - Request Parameters - Page 2:
2025-05-29 20:32:10,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:32:10,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:32:11,746 - INFO - Response - Page 2:
2025-05-29 20:32:11,747 - INFO - 第 2 页获取到 100 条记录
2025-05-29 20:32:11,947 - INFO - Request Parameters - Page 3:
2025-05-29 20:32:11,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:32:11,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:32:12,815 - INFO - Response - Page 3:
2025-05-29 20:32:12,815 - INFO - 第 3 页获取到 100 条记录
2025-05-29 20:32:13,015 - INFO - Request Parameters - Page 4:
2025-05-29 20:32:13,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:32:13,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:32:13,784 - INFO - Response - Page 4:
2025-05-29 20:32:13,784 - INFO - 第 4 页获取到 100 条记录
2025-05-29 20:32:13,985 - INFO - Request Parameters - Page 5:
2025-05-29 20:32:13,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:32:13,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:32:14,813 - INFO - Response - Page 5:
2025-05-29 20:32:14,813 - INFO - 第 5 页获取到 100 条记录
2025-05-29 20:32:15,013 - INFO - Request Parameters - Page 6:
2025-05-29 20:32:15,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:32:15,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:32:15,763 - INFO - Response - Page 6:
2025-05-29 20:32:15,763 - INFO - 第 6 页获取到 75 条记录
2025-05-29 20:32:15,964 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 20:32:15,964 - INFO - 获取到 575 条表单数据
2025-05-29 20:32:15,973 - INFO - 当前日期 2025-05-28 有 575 条MySQL数据需要处理
2025-05-29 20:32:15,982 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 20:32:15,982 - INFO - 开始处理日期: 2025-05-29
2025-05-29 20:32:15,983 - INFO - Request Parameters - Page 1:
2025-05-29 20:32:15,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 20:32:15,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 20:32:16,498 - INFO - Response - Page 1:
2025-05-29 20:32:16,498 - INFO - 第 1 页获取到 2 条记录
2025-05-29 20:32:16,698 - INFO - 查询完成，共获取到 2 条记录
2025-05-29 20:32:16,698 - INFO - 获取到 2 条表单数据
2025-05-29 20:32:16,699 - INFO - 当前日期 2025-05-29 有 2 条MySQL数据需要处理
2025-05-29 20:32:16,699 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 20:32:16,699 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 20:32:16,699 - INFO - 同步完成
2025-05-29 21:30:34,190 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 21:30:34,190 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 21:30:34,191 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 21:30:34,266 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 206 条记录
2025-05-29 21:30:34,266 - INFO - 获取到 4 个日期需要处理: ['2025-04-29', '2025-05-27', '2025-05-28', '2025-05-29']
2025-05-29 21:30:34,268 - INFO - 开始处理日期: 2025-04-29
2025-05-29 21:30:34,270 - INFO - Request Parameters - Page 1:
2025-05-29 21:30:34,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:30:34,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:30:42,402 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4D32534E-3F1B-7198-A036-80869688AE82 Response: {'code': 'ServiceUnavailable', 'requestid': '4D32534E-3F1B-7198-A036-80869688AE82', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4D32534E-3F1B-7198-A036-80869688AE82)
2025-05-29 21:30:42,402 - INFO - 开始处理日期: 2025-05-27
2025-05-29 21:30:42,402 - INFO - Request Parameters - Page 1:
2025-05-29 21:30:42,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:30:42,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:30:50,440 - INFO - Response - Page 1:
2025-05-29 21:30:50,441 - INFO - 第 1 页获取到 100 条记录
2025-05-29 21:30:50,641 - INFO - Request Parameters - Page 2:
2025-05-29 21:30:50,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:30:50,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:30:58,759 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F1CD50A6-2741-74A3-8976-1710C275B307 Response: {'code': 'ServiceUnavailable', 'requestid': 'F1CD50A6-2741-74A3-8976-1710C275B307', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F1CD50A6-2741-74A3-8976-1710C275B307)
2025-05-29 21:30:58,760 - INFO - 开始处理日期: 2025-05-28
2025-05-29 21:30:58,760 - INFO - Request Parameters - Page 1:
2025-05-29 21:30:58,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:30:58,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:30:59,665 - INFO - Response - Page 1:
2025-05-29 21:30:59,665 - INFO - 第 1 页获取到 100 条记录
2025-05-29 21:30:59,865 - INFO - Request Parameters - Page 2:
2025-05-29 21:30:59,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:30:59,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:31:01,284 - INFO - Response - Page 2:
2025-05-29 21:31:01,284 - INFO - 第 2 页获取到 100 条记录
2025-05-29 21:31:01,485 - INFO - Request Parameters - Page 3:
2025-05-29 21:31:01,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:31:01,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:31:02,387 - INFO - Response - Page 3:
2025-05-29 21:31:02,388 - INFO - 第 3 页获取到 100 条记录
2025-05-29 21:31:02,588 - INFO - Request Parameters - Page 4:
2025-05-29 21:31:02,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:31:02,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:31:03,797 - INFO - Response - Page 4:
2025-05-29 21:31:03,797 - INFO - 第 4 页获取到 100 条记录
2025-05-29 21:31:03,998 - INFO - Request Parameters - Page 5:
2025-05-29 21:31:03,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:31:03,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:31:04,756 - INFO - Response - Page 5:
2025-05-29 21:31:04,756 - INFO - 第 5 页获取到 100 条记录
2025-05-29 21:31:04,956 - INFO - Request Parameters - Page 6:
2025-05-29 21:31:04,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:31:04,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:31:05,716 - INFO - Response - Page 6:
2025-05-29 21:31:05,716 - INFO - 第 6 页获取到 75 条记录
2025-05-29 21:31:05,916 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 21:31:05,916 - INFO - 获取到 575 条表单数据
2025-05-29 21:31:05,926 - INFO - 当前日期 2025-05-28 有 198 条MySQL数据需要处理
2025-05-29 21:31:05,930 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 21:31:05,930 - INFO - 开始处理日期: 2025-05-29
2025-05-29 21:31:05,930 - INFO - Request Parameters - Page 1:
2025-05-29 21:31:05,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:31:05,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:31:06,437 - INFO - Response - Page 1:
2025-05-29 21:31:06,437 - INFO - 第 1 页获取到 2 条记录
2025-05-29 21:31:06,637 - INFO - 查询完成，共获取到 2 条记录
2025-05-29 21:31:06,637 - INFO - 获取到 2 条表单数据
2025-05-29 21:31:06,638 - INFO - 当前日期 2025-05-29 有 5 条MySQL数据需要处理
2025-05-29 21:31:06,638 - INFO - 开始批量插入 3 条新记录
2025-05-29 21:31:06,811 - INFO - 批量插入响应状态码: 200
2025-05-29 21:31:06,811 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 13:31:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BE4DF2EA-5F6A-7A90-928C-250415B18CA0', 'x-acs-trace-id': '0d3e2d4e8b78943c720d9021dfac70c2', 'etag': '1kT/ra/ajgeWSqxhqv1vUQw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 21:31:06,811 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71L9TV36TR9EXNKBKH242F2MTZWE9BMTG', 'FINST-L5766E71L9TV36TR9EXNKBKH242F2MTZWE9BMUG', 'FINST-L5766E71L9TV36TR9EXNKBKH242F2MTZWE9BMVG']}
2025-05-29 21:31:06,811 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-29 21:31:06,811 - INFO - 成功插入的数据ID: ['FINST-L5766E71L9TV36TR9EXNKBKH242F2MTZWE9BMTG', 'FINST-L5766E71L9TV36TR9EXNKBKH242F2MTZWE9BMUG', 'FINST-L5766E71L9TV36TR9EXNKBKH242F2MTZWE9BMVG']
2025-05-29 21:31:11,812 - INFO - 批量插入完成，共 3 条记录
2025-05-29 21:31:11,812 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-29 21:31:11,812 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 2 条
2025-05-29 21:32:11,823 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 21:32:11,823 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 21:32:11,823 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 21:32:11,907 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 580 条记录
2025-05-29 21:32:11,908 - INFO - 获取到 2 个日期需要处理: ['2025-05-28', '2025-05-29']
2025-05-29 21:32:11,912 - INFO - 开始处理日期: 2025-05-28
2025-05-29 21:32:11,913 - INFO - Request Parameters - Page 1:
2025-05-29 21:32:11,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:32:11,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:32:12,781 - INFO - Response - Page 1:
2025-05-29 21:32:12,781 - INFO - 第 1 页获取到 100 条记录
2025-05-29 21:32:12,981 - INFO - Request Parameters - Page 2:
2025-05-29 21:32:12,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:32:12,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:32:13,803 - INFO - Response - Page 2:
2025-05-29 21:32:13,804 - INFO - 第 2 页获取到 100 条记录
2025-05-29 21:32:14,005 - INFO - Request Parameters - Page 3:
2025-05-29 21:32:14,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:32:14,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:32:14,833 - INFO - Response - Page 3:
2025-05-29 21:32:14,833 - INFO - 第 3 页获取到 100 条记录
2025-05-29 21:32:15,033 - INFO - Request Parameters - Page 4:
2025-05-29 21:32:15,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:32:15,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:32:15,847 - INFO - Response - Page 4:
2025-05-29 21:32:15,847 - INFO - 第 4 页获取到 100 条记录
2025-05-29 21:32:16,047 - INFO - Request Parameters - Page 5:
2025-05-29 21:32:16,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:32:16,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:32:16,811 - INFO - Response - Page 5:
2025-05-29 21:32:16,812 - INFO - 第 5 页获取到 100 条记录
2025-05-29 21:32:17,012 - INFO - Request Parameters - Page 6:
2025-05-29 21:32:17,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:32:17,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:32:17,778 - INFO - Response - Page 6:
2025-05-29 21:32:17,778 - INFO - 第 6 页获取到 75 条记录
2025-05-29 21:32:17,978 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 21:32:17,978 - INFO - 获取到 575 条表单数据
2025-05-29 21:32:17,989 - INFO - 当前日期 2025-05-28 有 575 条MySQL数据需要处理
2025-05-29 21:32:17,998 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 21:32:17,998 - INFO - 开始处理日期: 2025-05-29
2025-05-29 21:32:17,999 - INFO - Request Parameters - Page 1:
2025-05-29 21:32:17,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:32:17,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:32:18,536 - INFO - Response - Page 1:
2025-05-29 21:32:18,536 - INFO - 第 1 页获取到 5 条记录
2025-05-29 21:32:18,737 - INFO - 查询完成，共获取到 5 条记录
2025-05-29 21:32:18,737 - INFO - 获取到 5 条表单数据
2025-05-29 21:32:18,738 - INFO - 当前日期 2025-05-29 有 5 条MySQL数据需要处理
2025-05-29 21:32:18,738 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 21:32:18,738 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 21:32:18,738 - INFO - 同步完成
2025-05-29 22:30:33,945 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 22:30:33,945 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 22:30:33,945 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 22:30:34,021 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 213 条记录
2025-05-29 22:30:34,021 - INFO - 获取到 4 个日期需要处理: ['2025-04-29', '2025-05-27', '2025-05-28', '2025-05-29']
2025-05-29 22:30:34,023 - INFO - 开始处理日期: 2025-04-29
2025-05-29 22:30:34,026 - INFO - Request Parameters - Page 1:
2025-05-29 22:30:34,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:30:34,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:30:42,155 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B6E5B8D6-2184-768C-A77F-1C8F2175105A Response: {'code': 'ServiceUnavailable', 'requestid': 'B6E5B8D6-2184-768C-A77F-1C8F2175105A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B6E5B8D6-2184-768C-A77F-1C8F2175105A)
2025-05-29 22:30:42,155 - INFO - 开始处理日期: 2025-05-27
2025-05-29 22:30:42,156 - INFO - Request Parameters - Page 1:
2025-05-29 22:30:42,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:30:42,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:30:50,288 - INFO - Response - Page 1:
2025-05-29 22:30:50,288 - INFO - 第 1 页获取到 100 条记录
2025-05-29 22:30:50,488 - INFO - Request Parameters - Page 2:
2025-05-29 22:30:50,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:30:50,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:30:51,328 - INFO - Response - Page 2:
2025-05-29 22:30:51,328 - INFO - 第 2 页获取到 100 条记录
2025-05-29 22:30:51,529 - INFO - Request Parameters - Page 3:
2025-05-29 22:30:51,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:30:51,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:30:52,369 - INFO - Response - Page 3:
2025-05-29 22:30:52,369 - INFO - 第 3 页获取到 100 条记录
2025-05-29 22:30:52,570 - INFO - Request Parameters - Page 4:
2025-05-29 22:30:52,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:30:52,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:31:00,676 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5D7797AA-40B8-7ADF-AF24-BCC1296F279B Response: {'code': 'ServiceUnavailable', 'requestid': '5D7797AA-40B8-7ADF-AF24-BCC1296F279B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5D7797AA-40B8-7ADF-AF24-BCC1296F279B)
2025-05-29 22:31:00,677 - INFO - 开始处理日期: 2025-05-28
2025-05-29 22:31:00,677 - INFO - Request Parameters - Page 1:
2025-05-29 22:31:00,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:31:00,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:31:03,349 - INFO - Response - Page 1:
2025-05-29 22:31:03,349 - INFO - 第 1 页获取到 100 条记录
2025-05-29 22:31:03,549 - INFO - Request Parameters - Page 2:
2025-05-29 22:31:03,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:31:03,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:31:04,410 - INFO - Response - Page 2:
2025-05-29 22:31:04,411 - INFO - 第 2 页获取到 100 条记录
2025-05-29 22:31:04,611 - INFO - Request Parameters - Page 3:
2025-05-29 22:31:04,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:31:04,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:31:05,442 - INFO - Response - Page 3:
2025-05-29 22:31:05,443 - INFO - 第 3 页获取到 100 条记录
2025-05-29 22:31:05,643 - INFO - Request Parameters - Page 4:
2025-05-29 22:31:05,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:31:05,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:31:06,536 - INFO - Response - Page 4:
2025-05-29 22:31:06,537 - INFO - 第 4 页获取到 100 条记录
2025-05-29 22:31:06,737 - INFO - Request Parameters - Page 5:
2025-05-29 22:31:06,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:31:06,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:31:07,578 - INFO - Response - Page 5:
2025-05-29 22:31:07,579 - INFO - 第 5 页获取到 100 条记录
2025-05-29 22:31:07,779 - INFO - Request Parameters - Page 6:
2025-05-29 22:31:07,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:31:07,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:31:08,635 - INFO - Response - Page 6:
2025-05-29 22:31:08,635 - INFO - 第 6 页获取到 75 条记录
2025-05-29 22:31:08,836 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 22:31:08,836 - INFO - 获取到 575 条表单数据
2025-05-29 22:31:08,845 - INFO - 当前日期 2025-05-28 有 198 条MySQL数据需要处理
2025-05-29 22:31:08,849 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 22:31:08,849 - INFO - 开始处理日期: 2025-05-29
2025-05-29 22:31:08,849 - INFO - Request Parameters - Page 1:
2025-05-29 22:31:08,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:31:08,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:31:09,419 - INFO - Response - Page 1:
2025-05-29 22:31:09,419 - INFO - 第 1 页获取到 5 条记录
2025-05-29 22:31:09,619 - INFO - 查询完成，共获取到 5 条记录
2025-05-29 22:31:09,619 - INFO - 获取到 5 条表单数据
2025-05-29 22:31:09,620 - INFO - 当前日期 2025-05-29 有 12 条MySQL数据需要处理
2025-05-29 22:31:09,620 - INFO - 开始批量插入 7 条新记录
2025-05-29 22:31:09,767 - INFO - 批量插入响应状态码: 200
2025-05-29 22:31:09,767 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 14:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3D01A32C-66E7-7CBD-AEDA-2F4EC30DFD17', 'x-acs-trace-id': '38c9e0d62266c3bb99c32ad1ea0cc274', 'etag': '3Voj8Nadcq28pUiiGyoSzxQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 22:31:09,767 - INFO - 批量插入响应体: {'result': ['FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMM1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMN1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMO1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMP1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMQ1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMR1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMS1']}
2025-05-29 22:31:09,767 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-05-29 22:31:09,767 - INFO - 成功插入的数据ID: ['FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMM1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMN1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMO1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMP1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMQ1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMR1', 'FINST-FPB66VB1QFUV50OED3OCH92OT9IF3KG72H9BMS1']
2025-05-29 22:31:14,769 - INFO - 批量插入完成，共 7 条记录
2025-05-29 22:31:14,769 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-05-29 22:31:14,769 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 2 条
2025-05-29 22:32:14,779 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 22:32:14,779 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 22:32:14,779 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 22:32:14,864 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 587 条记录
2025-05-29 22:32:14,864 - INFO - 获取到 2 个日期需要处理: ['2025-05-28', '2025-05-29']
2025-05-29 22:32:14,869 - INFO - 开始处理日期: 2025-05-28
2025-05-29 22:32:14,869 - INFO - Request Parameters - Page 1:
2025-05-29 22:32:14,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:32:14,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:32:15,778 - INFO - Response - Page 1:
2025-05-29 22:32:15,778 - INFO - 第 1 页获取到 100 条记录
2025-05-29 22:32:15,979 - INFO - Request Parameters - Page 2:
2025-05-29 22:32:15,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:32:15,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:32:16,860 - INFO - Response - Page 2:
2025-05-29 22:32:16,860 - INFO - 第 2 页获取到 100 条记录
2025-05-29 22:32:17,060 - INFO - Request Parameters - Page 3:
2025-05-29 22:32:17,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:32:17,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:32:17,898 - INFO - Response - Page 3:
2025-05-29 22:32:17,898 - INFO - 第 3 页获取到 100 条记录
2025-05-29 22:32:18,099 - INFO - Request Parameters - Page 4:
2025-05-29 22:32:18,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:32:18,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:32:18,908 - INFO - Response - Page 4:
2025-05-29 22:32:18,908 - INFO - 第 4 页获取到 100 条记录
2025-05-29 22:32:19,108 - INFO - Request Parameters - Page 5:
2025-05-29 22:32:19,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:32:19,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:32:19,943 - INFO - Response - Page 5:
2025-05-29 22:32:19,943 - INFO - 第 5 页获取到 100 条记录
2025-05-29 22:32:20,144 - INFO - Request Parameters - Page 6:
2025-05-29 22:32:20,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:32:20,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:32:20,850 - INFO - Response - Page 6:
2025-05-29 22:32:20,850 - INFO - 第 6 页获取到 75 条记录
2025-05-29 22:32:21,050 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 22:32:21,050 - INFO - 获取到 575 条表单数据
2025-05-29 22:32:21,060 - INFO - 当前日期 2025-05-28 有 575 条MySQL数据需要处理
2025-05-29 22:32:21,069 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 22:32:21,070 - INFO - 开始处理日期: 2025-05-29
2025-05-29 22:32:21,070 - INFO - Request Parameters - Page 1:
2025-05-29 22:32:21,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 22:32:21,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 22:32:21,695 - INFO - Response - Page 1:
2025-05-29 22:32:21,695 - INFO - 第 1 页获取到 12 条记录
2025-05-29 22:32:21,895 - INFO - 查询完成，共获取到 12 条记录
2025-05-29 22:32:21,895 - INFO - 获取到 12 条表单数据
2025-05-29 22:32:21,896 - INFO - 当前日期 2025-05-29 有 12 条MySQL数据需要处理
2025-05-29 22:32:21,896 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 22:32:21,897 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 22:32:21,897 - INFO - 同步完成
2025-05-29 23:30:34,170 - INFO - 使用默认增量同步（当天更新数据）
2025-05-29 23:30:34,170 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 23:30:34,171 - INFO - 查询参数: ('2025-05-29',)
2025-05-29 23:30:34,249 - INFO - MySQL查询成功，增量数据（日期: 2025-05-29），共获取 233 条记录
2025-05-29 23:30:34,249 - INFO - 获取到 4 个日期需要处理: ['2025-04-29', '2025-05-27', '2025-05-28', '2025-05-29']
2025-05-29 23:30:34,251 - INFO - 开始处理日期: 2025-04-29
2025-05-29 23:30:34,254 - INFO - Request Parameters - Page 1:
2025-05-29 23:30:34,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:34,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:42,373 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AAC16EC4-487F-735F-85E7-C8672F04BC2A Response: {'code': 'ServiceUnavailable', 'requestid': 'AAC16EC4-487F-735F-85E7-C8672F04BC2A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AAC16EC4-487F-735F-85E7-C8672F04BC2A)
2025-05-29 23:30:42,373 - INFO - 开始处理日期: 2025-05-27
2025-05-29 23:30:42,373 - INFO - Request Parameters - Page 1:
2025-05-29 23:30:42,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:42,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:50,485 - ERROR - 处理日期 2025-05-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3EF3BD72-A191-702F-9274-5B670B080DB3 Response: {'code': 'ServiceUnavailable', 'requestid': '3EF3BD72-A191-702F-9274-5B670B080DB3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3EF3BD72-A191-702F-9274-5B670B080DB3)
2025-05-29 23:30:50,485 - INFO - 开始处理日期: 2025-05-28
2025-05-29 23:30:50,485 - INFO - Request Parameters - Page 1:
2025-05-29 23:30:50,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:50,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:53,106 - INFO - Response - Page 1:
2025-05-29 23:30:53,106 - INFO - 第 1 页获取到 100 条记录
2025-05-29 23:30:53,307 - INFO - Request Parameters - Page 2:
2025-05-29 23:30:53,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:53,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:54,530 - INFO - Response - Page 2:
2025-05-29 23:30:54,531 - INFO - 第 2 页获取到 100 条记录
2025-05-29 23:30:54,731 - INFO - Request Parameters - Page 3:
2025-05-29 23:30:54,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:54,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:55,649 - INFO - Response - Page 3:
2025-05-29 23:30:55,649 - INFO - 第 3 页获取到 100 条记录
2025-05-29 23:30:55,849 - INFO - Request Parameters - Page 4:
2025-05-29 23:30:55,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:55,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:56,684 - INFO - Response - Page 4:
2025-05-29 23:30:56,685 - INFO - 第 4 页获取到 100 条记录
2025-05-29 23:30:56,886 - INFO - Request Parameters - Page 5:
2025-05-29 23:30:56,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:56,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:57,722 - INFO - Response - Page 5:
2025-05-29 23:30:57,722 - INFO - 第 5 页获取到 100 条记录
2025-05-29 23:30:57,923 - INFO - Request Parameters - Page 6:
2025-05-29 23:30:57,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:57,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:58,698 - INFO - Response - Page 6:
2025-05-29 23:30:58,698 - INFO - 第 6 页获取到 75 条记录
2025-05-29 23:30:58,898 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 23:30:58,898 - INFO - 获取到 575 条表单数据
2025-05-29 23:30:58,908 - INFO - 当前日期 2025-05-28 有 198 条MySQL数据需要处理
2025-05-29 23:30:58,911 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 23:30:58,911 - INFO - 开始处理日期: 2025-05-29
2025-05-29 23:30:58,912 - INFO - Request Parameters - Page 1:
2025-05-29 23:30:58,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:30:58,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:30:59,606 - INFO - Response - Page 1:
2025-05-29 23:30:59,606 - INFO - 第 1 页获取到 12 条记录
2025-05-29 23:30:59,806 - INFO - 查询完成，共获取到 12 条记录
2025-05-29 23:30:59,806 - INFO - 获取到 12 条表单数据
2025-05-29 23:30:59,807 - INFO - 当前日期 2025-05-29 有 32 条MySQL数据需要处理
2025-05-29 23:30:59,807 - INFO - 开始批量插入 20 条新记录
2025-05-29 23:30:59,950 - INFO - 批量插入响应状态码: 200
2025-05-29 23:30:59,950 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 15:30:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '972', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '85618AC7-DC51-757F-AA7C-1937779F2422', 'x-acs-trace-id': '2cc58df78d198a385841db01b47a41e4', 'etag': '9XCE9wOT0mdpYMiFgDmBAqQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 23:30:59,950 - INFO - 批量插入响应体: {'result': ['FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMZB', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM0C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM1C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM2C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM3C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM4C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM5C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM6C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM7C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM8C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM9C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMAC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMBC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMCC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMDC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMEC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMFC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMGC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMHC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMIC']}
2025-05-29 23:30:59,950 - INFO - 批量插入表单数据成功，批次 1，共 20 条记录
2025-05-29 23:30:59,950 - INFO - 成功插入的数据ID: ['FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMZB', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM0C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM1C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM2C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM3C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM4C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM5C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM6C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM7C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM8C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BM9C', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMAC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMBC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMCC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMDC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMEC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMFC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMGC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMHC', 'FINST-MLF669B1BHTVQ22P6KE7Z93XFZGN3X477J9BMIC']
2025-05-29 23:31:04,951 - INFO - 批量插入完成，共 20 条记录
2025-05-29 23:31:04,951 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 20 条，错误: 0 条
2025-05-29 23:31:04,951 - INFO - 数据同步完成！更新: 0 条，插入: 20 条，错误: 2 条
2025-05-29 23:32:04,952 - INFO - 开始同步昨天与今天的销售数据: 2025-05-28 至 2025-05-29
2025-05-29 23:32:04,952 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-29 23:32:04,952 - INFO - 查询参数: ('2025-05-28', '2025-05-29')
2025-05-29 23:32:05,057 - INFO - MySQL查询成功，时间段: 2025-05-28 至 2025-05-29，共获取 610 条记录
2025-05-29 23:32:05,058 - INFO - 获取到 2 个日期需要处理: ['2025-05-28', '2025-05-29']
2025-05-29 23:32:05,063 - INFO - 开始处理日期: 2025-05-28
2025-05-29 23:32:05,063 - INFO - Request Parameters - Page 1:
2025-05-29 23:32:05,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:32:05,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:32:05,909 - INFO - Response - Page 1:
2025-05-29 23:32:05,909 - INFO - 第 1 页获取到 100 条记录
2025-05-29 23:32:06,109 - INFO - Request Parameters - Page 2:
2025-05-29 23:32:06,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:32:06,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:32:06,936 - INFO - Response - Page 2:
2025-05-29 23:32:06,936 - INFO - 第 2 页获取到 100 条记录
2025-05-29 23:32:07,136 - INFO - Request Parameters - Page 3:
2025-05-29 23:32:07,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:32:07,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:32:07,934 - INFO - Response - Page 3:
2025-05-29 23:32:07,934 - INFO - 第 3 页获取到 100 条记录
2025-05-29 23:32:08,134 - INFO - Request Parameters - Page 4:
2025-05-29 23:32:08,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:32:08,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:32:09,020 - INFO - Response - Page 4:
2025-05-29 23:32:09,021 - INFO - 第 4 页获取到 100 条记录
2025-05-29 23:32:09,221 - INFO - Request Parameters - Page 5:
2025-05-29 23:32:09,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:32:09,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:32:10,077 - INFO - Response - Page 5:
2025-05-29 23:32:10,077 - INFO - 第 5 页获取到 100 条记录
2025-05-29 23:32:10,277 - INFO - Request Parameters - Page 6:
2025-05-29 23:32:10,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:32:10,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:32:11,070 - INFO - Response - Page 6:
2025-05-29 23:32:11,071 - INFO - 第 6 页获取到 75 条记录
2025-05-29 23:32:11,271 - INFO - 查询完成，共获取到 575 条记录
2025-05-29 23:32:11,271 - INFO - 获取到 575 条表单数据
2025-05-29 23:32:11,281 - INFO - 当前日期 2025-05-28 有 578 条MySQL数据需要处理
2025-05-29 23:32:11,289 - INFO - 开始批量插入 3 条新记录
2025-05-29 23:32:11,441 - INFO - 批量插入响应状态码: 200
2025-05-29 23:32:11,441 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 15:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F439D5E8-987E-78F3-AEED-9124A4013D5D', 'x-acs-trace-id': '22d9ecc5a4f9ae3132aade5b5e5bb1f1', 'etag': '14NNEnKAGQn8ePOdgJ2Ikiw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 23:32:11,442 - INFO - 批量插入响应体: {'result': ['FINST-2PF662C1L9UVOXHN9MI6I6J9TZEA3QAQ8J9BMO6', 'FINST-2PF662C1L9UVOXHN9MI6I6J9TZEA3QAQ8J9BMP6', 'FINST-2PF662C1L9UVOXHN9MI6I6J9TZEA3QAQ8J9BMQ6']}
2025-05-29 23:32:11,442 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-29 23:32:11,442 - INFO - 成功插入的数据ID: ['FINST-2PF662C1L9UVOXHN9MI6I6J9TZEA3QAQ8J9BMO6', 'FINST-2PF662C1L9UVOXHN9MI6I6J9TZEA3QAQ8J9BMP6', 'FINST-2PF662C1L9UVOXHN9MI6I6J9TZEA3QAQ8J9BMQ6']
2025-05-29 23:32:16,443 - INFO - 批量插入完成，共 3 条记录
2025-05-29 23:32:16,443 - INFO - 日期 2025-05-28 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-29 23:32:16,443 - INFO - 开始处理日期: 2025-05-29
2025-05-29 23:32:16,443 - INFO - Request Parameters - Page 1:
2025-05-29 23:32:16,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 23:32:16,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 23:32:17,086 - INFO - Response - Page 1:
2025-05-29 23:32:17,086 - INFO - 第 1 页获取到 32 条记录
2025-05-29 23:32:17,286 - INFO - 查询完成，共获取到 32 条记录
2025-05-29 23:32:17,286 - INFO - 获取到 32 条表单数据
2025-05-29 23:32:17,287 - INFO - 当前日期 2025-05-29 有 32 条MySQL数据需要处理
2025-05-29 23:32:17,288 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 23:32:17,288 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 0 条
2025-05-29 23:32:17,289 - INFO - 同步完成
