2025-07-31 01:30:33,671 - INFO - 使用默认增量同步（当天更新数据）
2025-07-31 01:30:33,671 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-31 01:30:33,671 - INFO - 查询参数: ('2025-07-31',)
2025-07-31 01:30:33,827 - INFO - MySQL查询成功，增量数据（日期: 2025-07-31），共获取 1 条记录
2025-07-31 01:30:33,827 - INFO - 获取到 1 个日期需要处理: ['2025-07-30']
2025-07-31 01:30:33,827 - INFO - 开始处理日期: 2025-07-30
2025-07-31 01:30:33,827 - INFO - Request Parameters - Page 1:
2025-07-31 01:30:33,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 01:30:33,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 01:30:41,952 - ERROR - 处理日期 2025-07-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 48821AEF-37E6-73DA-AB58-022F1A458580 Response: {'code': 'ServiceUnavailable', 'requestid': '48821AEF-37E6-73DA-AB58-022F1A458580', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 48821AEF-37E6-73DA-AB58-022F1A458580)
2025-07-31 01:30:41,968 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-31 01:31:41,983 - INFO - 开始同步昨天与今天的销售数据: 2025-07-30 至 2025-07-31
2025-07-31 01:31:41,983 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-31 01:31:41,983 - INFO - 查询参数: ('2025-07-30', '2025-07-31')
2025-07-31 01:31:42,139 - INFO - MySQL查询成功，时间段: 2025-07-30 至 2025-07-31，共获取 96 条记录
2025-07-31 01:31:42,139 - INFO - 获取到 1 个日期需要处理: ['2025-07-30']
2025-07-31 01:31:42,139 - INFO - 开始处理日期: 2025-07-30
2025-07-31 01:31:42,139 - INFO - Request Parameters - Page 1:
2025-07-31 01:31:42,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 01:31:42,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 01:31:43,124 - INFO - Response - Page 1:
2025-07-31 01:31:43,124 - INFO - 第 1 页获取到 45 条记录
2025-07-31 01:31:43,624 - INFO - 查询完成，共获取到 45 条记录
2025-07-31 01:31:43,624 - INFO - 获取到 45 条表单数据
2025-07-31 01:31:43,624 - INFO - 当前日期 2025-07-30 有 95 条MySQL数据需要处理
2025-07-31 01:31:43,624 - INFO - 开始批量插入 50 条新记录
2025-07-31 01:31:43,858 - INFO - 批量插入响应状态码: 200
2025-07-31 01:31:43,858 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 17:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E00D9296-6CF1-7EC8-B35F-9354DD1B5E77', 'x-acs-trace-id': '6b23a8059f8ed2dfdbb8d9b9ff6c23e2', 'etag': '2lJTPUut8+Ly9xBEKkLXW4Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 01:31:43,858 - INFO - 批量插入响应体: {'result': ['FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMU3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMV3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMW3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMX3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMY3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMZ3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM04', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM14', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM24', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM34', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM44', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM54', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM64', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM74', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM84', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM94', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMA4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMB4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMC4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMD4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDME4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMF4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMG4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMH4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMI4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMJ4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMK4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDML4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMM4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMN4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMO4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMP4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMQ4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMR4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMS4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMT4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMU4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMV4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMW4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMX4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMY4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMZ4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM05', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM15', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM25', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM35', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM45', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM55', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM65', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM75']}
2025-07-31 01:31:43,858 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-31 01:31:43,858 - INFO - 成功插入的数据ID: ['FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMU3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMV3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMW3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMX3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMY3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMZ3', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM04', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM14', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM24', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM34', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM44', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM54', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM64', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM74', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM84', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM94', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMA4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMB4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMC4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMD4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDME4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMF4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMG4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMH4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMI4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMJ4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMK4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDML4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMM4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMN4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMO4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMP4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMQ4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMR4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMS4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMT4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMU4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMV4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMW4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMX4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMY4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDMZ4', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM05', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG30N9T8QDM15', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM25', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM35', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM45', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM55', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM65', 'FINST-FTF66M719ALX618D9G5PZ5JET9QG31N9T8QDM75']
2025-07-31 01:31:48,874 - INFO - 批量插入完成，共 50 条记录
2025-07-31 01:31:48,874 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 50 条，错误: 0 条
2025-07-31 01:31:48,874 - INFO - 数据同步完成！更新: 0 条，插入: 50 条，错误: 0 条
2025-07-31 01:31:48,874 - INFO - 同步完成
2025-07-31 04:30:33,644 - INFO - 使用默认增量同步（当天更新数据）
2025-07-31 04:30:33,644 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-31 04:30:33,644 - INFO - 查询参数: ('2025-07-31',)
2025-07-31 04:30:33,800 - INFO - MySQL查询成功，增量数据（日期: 2025-07-31），共获取 1 条记录
2025-07-31 04:30:33,800 - INFO - 获取到 1 个日期需要处理: ['2025-07-30']
2025-07-31 04:30:33,800 - INFO - 开始处理日期: 2025-07-30
2025-07-31 04:30:33,800 - INFO - Request Parameters - Page 1:
2025-07-31 04:30:33,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 04:30:33,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 04:30:41,925 - ERROR - 处理日期 2025-07-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D77200BA-EA4F-708F-9835-FC044D26A6F5 Response: {'code': 'ServiceUnavailable', 'requestid': 'D77200BA-EA4F-708F-9835-FC044D26A6F5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D77200BA-EA4F-708F-9835-FC044D26A6F5)
2025-07-31 04:30:41,925 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-31 04:31:41,940 - INFO - 开始同步昨天与今天的销售数据: 2025-07-30 至 2025-07-31
2025-07-31 04:31:41,940 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-31 04:31:41,940 - INFO - 查询参数: ('2025-07-30', '2025-07-31')
2025-07-31 04:31:42,097 - INFO - MySQL查询成功，时间段: 2025-07-30 至 2025-07-31，共获取 96 条记录
2025-07-31 04:31:42,097 - INFO - 获取到 1 个日期需要处理: ['2025-07-30']
2025-07-31 04:31:42,097 - INFO - 开始处理日期: 2025-07-30
2025-07-31 04:31:42,097 - INFO - Request Parameters - Page 1:
2025-07-31 04:31:42,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 04:31:42,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 04:31:42,862 - INFO - Response - Page 1:
2025-07-31 04:31:42,862 - INFO - 第 1 页获取到 50 条记录
2025-07-31 04:31:43,378 - INFO - Request Parameters - Page 2:
2025-07-31 04:31:43,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 04:31:43,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 04:31:44,097 - INFO - Response - Page 2:
2025-07-31 04:31:44,097 - INFO - 第 2 页获取到 45 条记录
2025-07-31 04:31:44,612 - INFO - 查询完成，共获取到 95 条记录
2025-07-31 04:31:44,612 - INFO - 获取到 95 条表单数据
2025-07-31 04:31:44,612 - INFO - 当前日期 2025-07-30 有 95 条MySQL数据需要处理
2025-07-31 04:31:44,612 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-31 04:31:44,612 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-31 04:31:44,612 - INFO - 同步完成
2025-07-31 07:30:33,850 - INFO - 使用默认增量同步（当天更新数据）
2025-07-31 07:30:33,850 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-31 07:30:33,850 - INFO - 查询参数: ('2025-07-31',)
2025-07-31 07:30:34,007 - INFO - MySQL查询成功，增量数据（日期: 2025-07-31），共获取 1 条记录
2025-07-31 07:30:34,007 - INFO - 获取到 1 个日期需要处理: ['2025-07-30']
2025-07-31 07:30:34,007 - INFO - 开始处理日期: 2025-07-30
2025-07-31 07:30:34,007 - INFO - Request Parameters - Page 1:
2025-07-31 07:30:34,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 07:30:34,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 07:30:42,132 - ERROR - 处理日期 2025-07-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 66CA6C68-E5CB-7307-982F-C77D13EA626D Response: {'code': 'ServiceUnavailable', 'requestid': '66CA6C68-E5CB-7307-982F-C77D13EA626D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 66CA6C68-E5CB-7307-982F-C77D13EA626D)
2025-07-31 07:30:42,132 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-31 07:31:42,147 - INFO - 开始同步昨天与今天的销售数据: 2025-07-30 至 2025-07-31
2025-07-31 07:31:42,147 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-31 07:31:42,147 - INFO - 查询参数: ('2025-07-30', '2025-07-31')
2025-07-31 07:31:42,303 - INFO - MySQL查询成功，时间段: 2025-07-30 至 2025-07-31，共获取 96 条记录
2025-07-31 07:31:42,303 - INFO - 获取到 1 个日期需要处理: ['2025-07-30']
2025-07-31 07:31:42,303 - INFO - 开始处理日期: 2025-07-30
2025-07-31 07:31:42,303 - INFO - Request Parameters - Page 1:
2025-07-31 07:31:42,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 07:31:42,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 07:31:50,428 - ERROR - 处理日期 2025-07-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 54FDD5DF-6A1F-7B0C-AF59-2FF5CFE1A130 Response: {'code': 'ServiceUnavailable', 'requestid': '54FDD5DF-6A1F-7B0C-AF59-2FF5CFE1A130', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 54FDD5DF-6A1F-7B0C-AF59-2FF5CFE1A130)
2025-07-31 07:31:50,428 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-31 07:31:50,428 - INFO - 同步完成
2025-07-31 10:30:33,660 - INFO - 使用默认增量同步（当天更新数据）
2025-07-31 10:30:33,660 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-31 10:30:33,660 - INFO - 查询参数: ('2025-07-31',)
2025-07-31 10:30:33,831 - INFO - MySQL查询成功，增量数据（日期: 2025-07-31），共获取 139 条记录
2025-07-31 10:30:33,831 - INFO - 获取到 3 个日期需要处理: ['2025-07-29', '2025-07-30', '2025-07-31']
2025-07-31 10:30:33,831 - INFO - 开始处理日期: 2025-07-29
2025-07-31 10:30:33,831 - INFO - Request Parameters - Page 1:
2025-07-31 10:30:33,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:30:33,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:30:41,956 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C065B571-73B2-74BB-8898-FEFAFC080B4B Response: {'code': 'ServiceUnavailable', 'requestid': 'C065B571-73B2-74BB-8898-FEFAFC080B4B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C065B571-73B2-74BB-8898-FEFAFC080B4B)
2025-07-31 10:30:41,956 - INFO - 开始处理日期: 2025-07-30
2025-07-31 10:30:41,956 - INFO - Request Parameters - Page 1:
2025-07-31 10:30:41,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:30:41,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:30:42,738 - INFO - Response - Page 1:
2025-07-31 10:30:42,738 - INFO - 第 1 页获取到 50 条记录
2025-07-31 10:30:43,238 - INFO - Request Parameters - Page 2:
2025-07-31 10:30:43,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:30:43,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:30:43,972 - INFO - Response - Page 2:
2025-07-31 10:30:43,972 - INFO - 第 2 页获取到 45 条记录
2025-07-31 10:30:44,488 - INFO - 查询完成，共获取到 95 条记录
2025-07-31 10:30:44,488 - INFO - 获取到 95 条表单数据
2025-07-31 10:30:44,488 - INFO - 当前日期 2025-07-30 有 136 条MySQL数据需要处理
2025-07-31 10:30:44,488 - INFO - 开始批量插入 135 条新记录
2025-07-31 10:30:44,753 - INFO - 批量插入响应状态码: 200
2025-07-31 10:30:44,753 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:30:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2381', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B13F1388-488F-7629-862F-6F96255872F3', 'x-acs-trace-id': 'c756103e6a4685fb3b95ce7d6df7709d', 'etag': '2mSf3GWlXomp4zKXg5n+KtA1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:30:44,753 - INFO - 批量插入响应体: {'result': ['FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM5', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM6', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM7', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM8', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM9', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMA', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMB', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMC', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMD', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDME', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMF', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMG', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMH', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMI', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMJ', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMK', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDML', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMM', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMN', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMO', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMP', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMQ', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMR', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMS', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMT', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMU', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMV', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMW', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMX', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMY', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMZ', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM01', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM11', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM21', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM31', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM41', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM51', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM61', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM71', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM81', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM91', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMA1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMB1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMC1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMD1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDME1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMF1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMG1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMH1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMI1']}
2025-07-31 10:30:44,753 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-31 10:30:44,753 - INFO - 成功插入的数据ID: ['FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM5', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM6', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM7', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM8', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM9', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMA', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMB', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMC', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMD', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDME', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMF', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMG', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMH', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMI', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMJ', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMK', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDML', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMM', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMN', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMO', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMP', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMQ', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMR', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMS', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMT', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMU', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMV', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMW', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMX', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMY', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMZ', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM01', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM11', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM21', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM31', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM41', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM51', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM61', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM71', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM81', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDM91', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMA1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMB1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMC1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMD1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDME1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMF1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMG1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMH1', 'FINST-4HD66HC14AMXOYG39JE3A5HY7XDX3X6G2SQDMI1']
2025-07-31 10:30:50,019 - INFO - 批量插入响应状态码: 200
2025-07-31 10:30:50,019 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:30:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BC2EF678-2DB6-733C-8184-3D13D0E29B94', 'x-acs-trace-id': '70bdc97259d5ef72f0d3d1f1328bfc74', 'etag': '2SZLWGnyGi8F1UZ+CLMBy/A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:30:50,019 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMA4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMB4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMC4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMD4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDME4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMF4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMG4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMH4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMI4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMJ4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMK4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDML4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMM4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMN4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMO4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMP4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMQ4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMR4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMS4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMT4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMU4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMV4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMW4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMX4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMY4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMZ4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDM05', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDM15', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDM25', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM35', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM45', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM55', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM65', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM75', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM85', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM95', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMA5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMB5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMC5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMD5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDME5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMF5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMG5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMH5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMI5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMJ5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMK5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDML5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMM5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMN5']}
2025-07-31 10:30:50,019 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-31 10:30:50,019 - INFO - 成功插入的数据ID: ['FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMA4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMB4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMC4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMD4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDME4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMF4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMG4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMH4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMI4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMJ4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMK4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDML4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMM4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMN4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMO4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMP4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMQ4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMR4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMS4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMT4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMU4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMV4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMW4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMX4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMY4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMZ4', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDM05', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDM15', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDM25', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM35', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM45', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM55', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM65', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM75', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM85', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDM95', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMA5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMB5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMC5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMD5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDME5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMF5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMG5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMH5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMI5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMJ5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMK5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDML5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMM5', 'FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83N9K2SQDMN5']
2025-07-31 10:30:55,300 - INFO - 批量插入响应状态码: 200
2025-07-31 10:30:55,300 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:30:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1692', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F1E15C0A-5C5E-739C-9954-ABDE7C88C5CA', 'x-acs-trace-id': '1ccc58b6120349cce4dc990d9a077db9', 'etag': '1mSN7cwYdexu1C0jKV/Tqsg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:30:55,300 - INFO - 批量插入响应体: {'result': ['FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMWR', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMXR', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMYR', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMZR', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM0S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM1S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM2S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM3S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM4S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM5S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM6S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM7S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM8S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM9S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMAS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMBS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMCS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMDS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMES', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMFS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMGS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMHS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMIS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMJS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMKS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMLS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMMS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMNS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMOS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMPS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMQS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMRS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMSS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMTS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMUS']}
2025-07-31 10:30:55,300 - INFO - 批量插入表单数据成功，批次 3，共 35 条记录
2025-07-31 10:30:55,300 - INFO - 成功插入的数据ID: ['FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMWR', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMXR', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMYR', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMZR', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM0S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM1S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM2S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM3S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM4S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM5S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM6S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM7S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM8S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDM9S', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMAS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMBS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMCS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMDS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMES', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMFS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMGS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMHS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMIS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMJS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMKS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMLS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMMS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMNS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMOS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMPS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMQS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMRS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMSS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMTS', 'FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMUS']
2025-07-31 10:31:00,316 - INFO - 批量插入完成，共 135 条记录
2025-07-31 10:31:00,316 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 135 条，错误: 0 条
2025-07-31 10:31:00,316 - INFO - 开始处理日期: 2025-07-31
2025-07-31 10:31:00,316 - INFO - Request Parameters - Page 1:
2025-07-31 10:31:00,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:31:00,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:31:08,441 - ERROR - 处理日期 2025-07-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DBEEEB83-711F-7FAE-AAA3-6340B3096FC3 Response: {'code': 'ServiceUnavailable', 'requestid': 'DBEEEB83-711F-7FAE-AAA3-6340B3096FC3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DBEEEB83-711F-7FAE-AAA3-6340B3096FC3)
2025-07-31 10:31:08,441 - INFO - 数据同步完成！更新: 0 条，插入: 135 条，错误: 2 条
2025-07-31 10:32:08,456 - INFO - 开始同步昨天与今天的销售数据: 2025-07-30 至 2025-07-31
2025-07-31 10:32:08,456 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-31 10:32:08,456 - INFO - 查询参数: ('2025-07-30', '2025-07-31')
2025-07-31 10:32:08,612 - INFO - MySQL查询成功，时间段: 2025-07-30 至 2025-07-31，共获取 487 条记录
2025-07-31 10:32:08,612 - INFO - 获取到 2 个日期需要处理: ['2025-07-30', '2025-07-31']
2025-07-31 10:32:08,628 - INFO - 开始处理日期: 2025-07-30
2025-07-31 10:32:08,628 - INFO - Request Parameters - Page 1:
2025-07-31 10:32:08,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:32:08,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:32:09,440 - INFO - Response - Page 1:
2025-07-31 10:32:09,440 - INFO - 第 1 页获取到 50 条记录
2025-07-31 10:32:09,956 - INFO - Request Parameters - Page 2:
2025-07-31 10:32:09,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:32:09,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:32:10,768 - INFO - Response - Page 2:
2025-07-31 10:32:10,768 - INFO - 第 2 页获取到 50 条记录
2025-07-31 10:32:11,268 - INFO - Request Parameters - Page 3:
2025-07-31 10:32:11,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:32:11,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:32:12,018 - INFO - Response - Page 3:
2025-07-31 10:32:12,018 - INFO - 第 3 页获取到 50 条记录
2025-07-31 10:32:12,534 - INFO - Request Parameters - Page 4:
2025-07-31 10:32:12,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:32:12,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:32:13,284 - INFO - Response - Page 4:
2025-07-31 10:32:13,284 - INFO - 第 4 页获取到 50 条记录
2025-07-31 10:32:13,799 - INFO - Request Parameters - Page 5:
2025-07-31 10:32:13,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:32:13,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:32:14,549 - INFO - Response - Page 5:
2025-07-31 10:32:14,549 - INFO - 第 5 页获取到 30 条记录
2025-07-31 10:32:15,065 - INFO - 查询完成，共获取到 230 条记录
2025-07-31 10:32:15,065 - INFO - 获取到 230 条表单数据
2025-07-31 10:32:15,065 - INFO - 当前日期 2025-07-30 有 474 条MySQL数据需要处理
2025-07-31 10:32:15,065 - INFO - 开始批量插入 244 条新记录
2025-07-31 10:32:15,315 - INFO - 批量插入响应状态码: 200
2025-07-31 10:32:15,315 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:32:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2376', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C800E7E8-2B1D-7006-8E1E-C578239422ED', 'x-acs-trace-id': '321aa9da5476e1af48ff2bec751dbab4', 'etag': '2FnKRxeBYLcQDzC2NIH8+Ug6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:32:15,315 - INFO - 批量插入响应体: {'result': ['FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM0', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM1', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM2', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM3', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM4', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM5', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM6', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM7', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM8', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM9', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMA', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMB', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMC', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMD', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDME', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMF', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMG', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMH', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMI', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMJ', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMK', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDML', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMM', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMN', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMO', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMP', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMQ', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMR', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMS', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMT', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMU', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMV', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMW', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMX', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMY', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMZ', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM01', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM11', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM21', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM31', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM41', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM51', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM61', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM71', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM81', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM91', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMA1', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMB1', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMC1', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMD1']}
2025-07-31 10:32:15,315 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-31 10:32:15,315 - INFO - 成功插入的数据ID: ['FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM0', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM1', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM2', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM3', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM4', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM5', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM6', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM7', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM8', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM9', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMA', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMB', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMC', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMD', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDME', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMF', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMG', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMH', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMI', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMJ', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMK', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDML', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMM', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMN', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMO', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMP', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMQ', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMR', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMS', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMT', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMU', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMV', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMW', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMX', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMY', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMZ', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM01', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM11', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM21', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM31', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM41', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM51', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM61', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM71', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM81', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDM91', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMA1', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMB1', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMC1', 'FINST-L5766E712CMXN05VCANENBMFZA193V2E4SQDMD1']
2025-07-31 10:32:20,565 - INFO - 批量插入响应状态码: 200
2025-07-31 10:32:20,565 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2378', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '178CC325-A804-7876-8B2F-FCE6FB537C91', 'x-acs-trace-id': '832cecf3fc926908746605aa029a6267', 'etag': '2mMn/7X6Lpm10U/k8G14mkg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:32:20,565 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM2', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM3', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM4', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM5', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM6', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM7', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM8', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM9', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMA', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMB', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMC', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMD', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDME', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMF', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMG', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMH', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMI', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMJ', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMK', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDML', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMM', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMN', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMO', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMP', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMQ', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMR', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMS', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMT', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMU', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMV', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMW', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMX', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMY', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMZ', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM01', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM11', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM21', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM31', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM41', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM51', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM61', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM71', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM81', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM91', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMA1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMB1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMC1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMD1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDME1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMF1']}
2025-07-31 10:32:20,565 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-31 10:32:20,565 - INFO - 成功插入的数据ID: ['FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM2', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM3', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM4', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM5', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3V4I4SQDM6', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM7', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM8', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM9', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMA', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMB', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMC', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMD', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDME', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMF', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMG', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMH', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMI', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMJ', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMK', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDML', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMM', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMN', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMO', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMP', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMQ', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMR', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMS', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMT', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMU', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMV', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMW', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMX', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMY', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMZ', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM01', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM11', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM21', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM31', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM41', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM51', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM61', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM71', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM81', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDM91', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMA1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMB1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMC1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMD1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDME1', 'FINST-L5766E71JBMXM0H168VW4BFRV5QB3W4I4SQDMF1']
2025-07-31 10:32:25,815 - INFO - 批量插入响应状态码: 200
2025-07-31 10:32:25,815 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:32:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1B16D26F-EB82-7043-81C3-E30E47557BB8', 'x-acs-trace-id': 'd61d023f2e108a4c01fade4232e3d33b', 'etag': '2uyZjjYmr8d06hiivWdr5Ww2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:32:25,815 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMAK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMBK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMCK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMDK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMEK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMFK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMGK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMHK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMIK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMJK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMKK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMLK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMMK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMNK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMOK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMPK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMQK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMRK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMSK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMTK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMUK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMVK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMWK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMXK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMYK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMZK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM0L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM1L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM2L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM3L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM4L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM5L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM6L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM7L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM8L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM9L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMAL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMBL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMCL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMDL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMEL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMFL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMGL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMHL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMIL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMJL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMKL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMLL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMML', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMNL']}
2025-07-31 10:32:25,815 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-31 10:32:25,815 - INFO - 成功插入的数据ID: ['FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMAK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMBK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMCK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMDK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMEK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMFK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMGK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMHK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMIK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMJK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMKK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMLK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMMK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMNK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMOK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMPK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMQK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMRK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMSK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMTK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMUK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMVK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMWK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMXK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMYK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMZK', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM0L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM1L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM2L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM3L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM4L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM5L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM6L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM7L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM8L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDM9L', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMAL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMBL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMCL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMDL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMEL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMFL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMGL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMHL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMIL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMJL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMKL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMLL', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMML', 'FINST-QZE668D1PEJX6KPU72WFP5F8WZDO3K6M4SQDMNL']
2025-07-31 10:32:31,081 - INFO - 批量插入响应状态码: 200
2025-07-31 10:32:31,081 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:32:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2387', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '18F6ADA7-BA7B-712F-A161-D4BF3121F0F6', 'x-acs-trace-id': '6072ae6caa95edf7e9528b2d53340953', 'etag': '2YShoBAgs/+vcoqybYwbY/g7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:32:31,081 - INFO - 批量插入响应体: {'result': ['FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMB', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMC', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMD', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDME', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMF', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMG', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMH', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMI', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMJ', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMK', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDML', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMM', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMN', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMO', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMP', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMQ', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMR', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMS', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMT', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMU', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMV', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMW', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMX', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMY', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMZ', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM01', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM11', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM21', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM31', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM41', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM51', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM61', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM71', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM81', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM91', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMA1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMB1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMC1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMD1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDME1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMF1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMG1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMH1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMI1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMJ1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMK1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDML1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMM1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMN1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMO1']}
2025-07-31 10:32:31,081 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-31 10:32:31,081 - INFO - 成功插入的数据ID: ['FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMB', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMC', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMD', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDME', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMF', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMG', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMH', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMI', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMJ', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMK', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDML', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMM', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMN', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMO', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMP', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMQ', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMR', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMS', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623U8Q4SQDMT', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMU', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMV', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMW', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMX', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMY', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMZ', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM01', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM11', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM21', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM31', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM41', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM51', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM61', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM71', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM81', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDM91', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMA1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMB1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMC1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMD1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDME1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMF1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMG1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMH1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMI1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMJ1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMK1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDML1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMM1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMN1', 'FINST-MKF66PA1K9MX7OUU51L6NDFM7H623V8Q4SQDMO1']
2025-07-31 10:32:36,331 - INFO - 批量插入响应状态码: 200
2025-07-31 10:32:36,331 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:32:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2089', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FB50D382-D678-7FC4-9443-A68990A64303', 'x-acs-trace-id': '909ec5d6269e59feebddc062b7651a8c', 'etag': '2xz8dDOxvMX2EBex1Xp/3sw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:32:36,331 - INFO - 批量插入响应体: {'result': ['FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM1', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM2', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM3', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM4', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM5', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM6', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM7', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM8', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM9', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMA', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMB', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMC', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMD', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDME', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMF', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMG', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMH', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMI', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMJ', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMK', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDML', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMM', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMN', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMO', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMP', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMQ', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMR', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMS', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMT', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMU', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMV', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMW', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMX', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMY', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMZ', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM01', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM11', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM21', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM31', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM41', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM51', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM61', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM71', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM81']}
2025-07-31 10:32:36,331 - INFO - 批量插入表单数据成功，批次 5，共 44 条记录
2025-07-31 10:32:36,331 - INFO - 成功插入的数据ID: ['FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM1', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM2', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM3', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM4', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM5', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM6', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM7', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM8', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM9', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMA', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMB', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMC', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMD', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDME', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMF', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMG', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMH', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMI', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMJ', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMK', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDML', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMM', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMN', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMO', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMP', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMQ', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMR', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMS', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMT', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMU', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMV', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMW', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMX', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMY', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDMZ', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM01', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM11', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM21', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM31', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM41', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM51', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM61', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM71', 'FINST-80B66291UBMX1HP3BDWECDTDKFFE28AU4SQDM81']
2025-07-31 10:32:41,346 - INFO - 批量插入完成，共 244 条记录
2025-07-31 10:32:41,346 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 244 条，错误: 0 条
2025-07-31 10:32:41,346 - INFO - 开始处理日期: 2025-07-31
2025-07-31 10:32:41,346 - INFO - Request Parameters - Page 1:
2025-07-31 10:32:41,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 10:32:41,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 10:32:41,846 - INFO - Response - Page 1:
2025-07-31 10:32:41,846 - INFO - 查询完成，共获取到 0 条记录
2025-07-31 10:32:41,846 - INFO - 获取到 0 条表单数据
2025-07-31 10:32:41,846 - INFO - 当前日期 2025-07-31 有 1 条MySQL数据需要处理
2025-07-31 10:32:41,846 - INFO - 开始批量插入 1 条新记录
2025-07-31 10:32:42,018 - INFO - 批量插入响应状态码: 200
2025-07-31 10:32:42,018 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 02:32:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3A223458-A0FE-7F56-998E-C7C367E97FDE', 'x-acs-trace-id': '25b7bbee6df88e5af3d8303b15f30217', 'etag': '59MqA5ELGSB7epP7UtuixVA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 10:32:42,034 - INFO - 批量插入响应体: {'result': ['FINST-F3G66Q618AMXD4NUA0TZ961S6AOX2WOY4SQDM5']}
2025-07-31 10:32:42,034 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-31 10:32:42,034 - INFO - 成功插入的数据ID: ['FINST-F3G66Q618AMXD4NUA0TZ961S6AOX2WOY4SQDM5']
2025-07-31 10:32:47,049 - INFO - 批量插入完成，共 1 条记录
2025-07-31 10:32:47,049 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-31 10:32:47,049 - INFO - 数据同步完成！更新: 0 条，插入: 245 条，错误: 0 条
2025-07-31 10:32:47,049 - INFO - 同步完成
2025-07-31 13:30:33,837 - INFO - 使用默认增量同步（当天更新数据）
2025-07-31 13:30:33,837 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-31 13:30:33,837 - INFO - 查询参数: ('2025-07-31',)
2025-07-31 13:30:33,993 - INFO - MySQL查询成功，增量数据（日期: 2025-07-31），共获取 145 条记录
2025-07-31 13:30:33,993 - INFO - 获取到 3 个日期需要处理: ['2025-07-29', '2025-07-30', '2025-07-31']
2025-07-31 13:30:33,993 - INFO - 开始处理日期: 2025-07-29
2025-07-31 13:30:34,009 - INFO - Request Parameters - Page 1:
2025-07-31 13:30:34,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:30:34,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:30:42,118 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 983A3F2D-A958-77E7-8FC7-BEF2297E2629 Response: {'code': 'ServiceUnavailable', 'requestid': '983A3F2D-A958-77E7-8FC7-BEF2297E2629', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 983A3F2D-A958-77E7-8FC7-BEF2297E2629)
2025-07-31 13:30:42,118 - INFO - 开始处理日期: 2025-07-30
2025-07-31 13:30:42,118 - INFO - Request Parameters - Page 1:
2025-07-31 13:30:42,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:30:42,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:30:42,962 - INFO - Response - Page 1:
2025-07-31 13:30:42,962 - INFO - 第 1 页获取到 50 条记录
2025-07-31 13:30:43,478 - INFO - Request Parameters - Page 2:
2025-07-31 13:30:43,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:30:43,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:30:51,571 - ERROR - 处理日期 2025-07-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2E78F6FD-455D-7D52-9A43-9EC28ACB6296 Response: {'code': 'ServiceUnavailable', 'requestid': '2E78F6FD-455D-7D52-9A43-9EC28ACB6296', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2E78F6FD-455D-7D52-9A43-9EC28ACB6296)
2025-07-31 13:30:51,571 - INFO - 开始处理日期: 2025-07-31
2025-07-31 13:30:51,571 - INFO - Request Parameters - Page 1:
2025-07-31 13:30:51,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:30:51,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:30:52,071 - INFO - Response - Page 1:
2025-07-31 13:30:52,071 - INFO - 第 1 页获取到 1 条记录
2025-07-31 13:30:52,571 - INFO - 查询完成，共获取到 1 条记录
2025-07-31 13:30:52,571 - INFO - 获取到 1 条表单数据
2025-07-31 13:30:52,571 - INFO - 当前日期 2025-07-31 有 1 条MySQL数据需要处理
2025-07-31 13:30:52,571 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-31 13:30:52,571 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-31 13:31:52,586 - INFO - 开始同步昨天与今天的销售数据: 2025-07-30 至 2025-07-31
2025-07-31 13:31:52,586 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-31 13:31:52,586 - INFO - 查询参数: ('2025-07-30', '2025-07-31')
2025-07-31 13:31:52,758 - INFO - MySQL查询成功，时间段: 2025-07-30 至 2025-07-31，共获取 512 条记录
2025-07-31 13:31:52,758 - INFO - 获取到 2 个日期需要处理: ['2025-07-30', '2025-07-31']
2025-07-31 13:31:52,758 - INFO - 开始处理日期: 2025-07-30
2025-07-31 13:31:52,758 - INFO - Request Parameters - Page 1:
2025-07-31 13:31:52,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:31:52,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:31:53,524 - INFO - Response - Page 1:
2025-07-31 13:31:53,524 - INFO - 第 1 页获取到 50 条记录
2025-07-31 13:31:54,040 - INFO - Request Parameters - Page 2:
2025-07-31 13:31:54,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:31:54,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:31:54,774 - INFO - Response - Page 2:
2025-07-31 13:31:54,774 - INFO - 第 2 页获取到 50 条记录
2025-07-31 13:31:55,290 - INFO - Request Parameters - Page 3:
2025-07-31 13:31:55,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:31:55,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:31:56,024 - INFO - Response - Page 3:
2025-07-31 13:31:56,024 - INFO - 第 3 页获取到 50 条记录
2025-07-31 13:31:56,524 - INFO - Request Parameters - Page 4:
2025-07-31 13:31:56,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:31:56,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:31:57,336 - INFO - Response - Page 4:
2025-07-31 13:31:57,336 - INFO - 第 4 页获取到 50 条记录
2025-07-31 13:31:57,852 - INFO - Request Parameters - Page 5:
2025-07-31 13:31:57,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:31:57,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:31:58,555 - INFO - Response - Page 5:
2025-07-31 13:31:58,555 - INFO - 第 5 页获取到 50 条记录
2025-07-31 13:31:59,071 - INFO - Request Parameters - Page 6:
2025-07-31 13:31:59,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:31:59,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:31:59,868 - INFO - Response - Page 6:
2025-07-31 13:31:59,868 - INFO - 第 6 页获取到 50 条记录
2025-07-31 13:32:00,368 - INFO - Request Parameters - Page 7:
2025-07-31 13:32:00,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:32:00,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:32:01,133 - INFO - Response - Page 7:
2025-07-31 13:32:01,133 - INFO - 第 7 页获取到 50 条记录
2025-07-31 13:32:01,633 - INFO - Request Parameters - Page 8:
2025-07-31 13:32:01,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:32:01,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:32:02,414 - INFO - Response - Page 8:
2025-07-31 13:32:02,414 - INFO - 第 8 页获取到 50 条记录
2025-07-31 13:32:02,930 - INFO - Request Parameters - Page 9:
2025-07-31 13:32:02,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:32:02,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:32:03,727 - INFO - Response - Page 9:
2025-07-31 13:32:03,727 - INFO - 第 9 页获取到 50 条记录
2025-07-31 13:32:04,243 - INFO - Request Parameters - Page 10:
2025-07-31 13:32:04,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:32:04,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:32:04,914 - INFO - Response - Page 10:
2025-07-31 13:32:04,914 - INFO - 第 10 页获取到 24 条记录
2025-07-31 13:32:05,430 - INFO - 查询完成，共获取到 474 条记录
2025-07-31 13:32:05,430 - INFO - 获取到 474 条表单数据
2025-07-31 13:32:05,430 - INFO - 当前日期 2025-07-30 有 498 条MySQL数据需要处理
2025-07-31 13:32:05,446 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMU4
2025-07-31 13:32:06,039 - INFO - 更新表单数据成功: FINST-AAG66KB1RAMXJVWX6I2QN7U9I5O83M9K2SQDMU4
2025-07-31 13:32:06,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000.0, 'new_value': 15943.62}, {'field': 'total_amount', 'old_value': 8000.0, 'new_value': 15943.62}, {'field': 'order_count', 'old_value': 120, 'new_value': 180}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/021e6386e39847cb9127a0c239791e2b.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=tAi%2F7dWKS%2BRkbBu0ikL4bYu1hzg%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/d1d62453019c431fbeecc91d29775325.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=TccJl3TS9i1jTQBga4FQrl3I5N4%3D'}]
2025-07-31 13:32:06,039 - INFO - 开始批量插入 24 条新记录
2025-07-31 13:32:06,258 - INFO - 批量插入响应状态码: 200
2025-07-31 13:32:06,258 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 05:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FCD1B5BA-853A-7C30-933E-14A4132D10D9', 'x-acs-trace-id': '81e4bd56e0ce7a24d4c8a4e8d216be67', 'etag': '18ShaFOv6YYjyP9gUl8gSjw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 13:32:06,258 - INFO - 批量插入响应体: {'result': ['FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMBZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMCZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMDZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMEZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMFZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMGZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMHZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMIZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMJZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMKZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMLZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMMZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMNZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMOZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMPZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMQZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMRZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMSZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMTZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMUZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMVZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMWZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMXZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMYZ']}
2025-07-31 13:32:06,258 - INFO - 批量插入表单数据成功，批次 1，共 24 条记录
2025-07-31 13:32:06,258 - INFO - 成功插入的数据ID: ['FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMBZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMCZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMDZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMEZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMFZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMGZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMHZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMIZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMJZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMKZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMLZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMMZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMNZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMOZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMPZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMQZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMRZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMSZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMTZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMUZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMVZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMWZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMXZ', 'FINST-V4G66WC1XDHX9I70DO1HA8O6EHZB3EHOJYQDMYZ']
2025-07-31 13:32:11,274 - INFO - 批量插入完成，共 24 条记录
2025-07-31 13:32:11,274 - INFO - 日期 2025-07-30 处理完成 - 更新: 1 条，插入: 24 条，错误: 0 条
2025-07-31 13:32:11,274 - INFO - 开始处理日期: 2025-07-31
2025-07-31 13:32:11,274 - INFO - Request Parameters - Page 1:
2025-07-31 13:32:11,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 13:32:11,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 13:32:11,727 - INFO - Response - Page 1:
2025-07-31 13:32:11,727 - INFO - 第 1 页获取到 1 条记录
2025-07-31 13:32:12,243 - INFO - 查询完成，共获取到 1 条记录
2025-07-31 13:32:12,243 - INFO - 获取到 1 条表单数据
2025-07-31 13:32:12,243 - INFO - 当前日期 2025-07-31 有 1 条MySQL数据需要处理
2025-07-31 13:32:12,243 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-31 13:32:12,243 - INFO - 数据同步完成！更新: 1 条，插入: 24 条，错误: 0 条
2025-07-31 13:32:12,243 - INFO - 同步完成
2025-07-31 16:30:33,800 - INFO - 使用默认增量同步（当天更新数据）
2025-07-31 16:30:33,800 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-31 16:30:33,800 - INFO - 查询参数: ('2025-07-31',)
2025-07-31 16:30:33,972 - INFO - MySQL查询成功，增量数据（日期: 2025-07-31），共获取 155 条记录
2025-07-31 16:30:33,972 - INFO - 获取到 3 个日期需要处理: ['2025-07-29', '2025-07-30', '2025-07-31']
2025-07-31 16:30:33,972 - INFO - 开始处理日期: 2025-07-29
2025-07-31 16:30:33,972 - INFO - Request Parameters - Page 1:
2025-07-31 16:30:33,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:30:33,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:30:42,112 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7C359473-D408-7214-865B-20D301ED4F1C Response: {'code': 'ServiceUnavailable', 'requestid': '7C359473-D408-7214-865B-20D301ED4F1C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7C359473-D408-7214-865B-20D301ED4F1C)
2025-07-31 16:30:42,112 - INFO - 开始处理日期: 2025-07-30
2025-07-31 16:30:42,112 - INFO - Request Parameters - Page 1:
2025-07-31 16:30:42,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:30:42,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:30:50,222 - ERROR - 处理日期 2025-07-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 814E3796-AF42-7DEE-A9EB-A38B5CA0BDE2 Response: {'code': 'ServiceUnavailable', 'requestid': '814E3796-AF42-7DEE-A9EB-A38B5CA0BDE2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 814E3796-AF42-7DEE-A9EB-A38B5CA0BDE2)
2025-07-31 16:30:50,222 - INFO - 开始处理日期: 2025-07-31
2025-07-31 16:30:50,222 - INFO - Request Parameters - Page 1:
2025-07-31 16:30:50,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:30:50,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:30:50,768 - INFO - Response - Page 1:
2025-07-31 16:30:50,768 - INFO - 第 1 页获取到 1 条记录
2025-07-31 16:30:51,268 - INFO - 查询完成，共获取到 1 条记录
2025-07-31 16:30:51,268 - INFO - 获取到 1 条表单数据
2025-07-31 16:30:51,268 - INFO - 当前日期 2025-07-31 有 2 条MySQL数据需要处理
2025-07-31 16:30:51,268 - INFO - 开始批量插入 1 条新记录
2025-07-31 16:30:51,409 - INFO - 批量插入响应状态码: 200
2025-07-31 16:30:51,409 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 08:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2E61FDEC-01D3-7652-9A48-7C29A36FD062', 'x-acs-trace-id': 'dc3ee7aa0e766b12954f8998bae1781b', 'etag': '6A/WU+BsavvYmXaQ9kOEofA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 16:30:51,425 - INFO - 批量插入响应体: {'result': ['FINST-B1D66U61Z8JX7S8ZA3TQ77MH69L33O4KX4RDMDA']}
2025-07-31 16:30:51,425 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-31 16:30:51,425 - INFO - 成功插入的数据ID: ['FINST-B1D66U61Z8JX7S8ZA3TQ77MH69L33O4KX4RDMDA']
2025-07-31 16:30:56,440 - INFO - 批量插入完成，共 1 条记录
2025-07-31 16:30:56,440 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-31 16:30:56,440 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-07-31 16:31:56,456 - INFO - 开始同步昨天与今天的销售数据: 2025-07-30 至 2025-07-31
2025-07-31 16:31:56,456 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-31 16:31:56,456 - INFO - 查询参数: ('2025-07-30', '2025-07-31')
2025-07-31 16:31:56,627 - INFO - MySQL查询成功，时间段: 2025-07-30 至 2025-07-31，共获取 534 条记录
2025-07-31 16:31:56,627 - INFO - 获取到 2 个日期需要处理: ['2025-07-30', '2025-07-31']
2025-07-31 16:31:56,627 - INFO - 开始处理日期: 2025-07-30
2025-07-31 16:31:56,627 - INFO - Request Parameters - Page 1:
2025-07-31 16:31:56,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:31:56,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:04,768 - INFO - Response - Page 1:
2025-07-31 16:32:04,768 - INFO - 第 1 页获取到 50 条记录
2025-07-31 16:32:05,268 - INFO - Request Parameters - Page 2:
2025-07-31 16:32:05,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:05,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:05,971 - INFO - Response - Page 2:
2025-07-31 16:32:05,971 - INFO - 第 2 页获取到 50 条记录
2025-07-31 16:32:06,487 - INFO - Request Parameters - Page 3:
2025-07-31 16:32:06,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:06,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:07,143 - INFO - Response - Page 3:
2025-07-31 16:32:07,143 - INFO - 第 3 页获取到 50 条记录
2025-07-31 16:32:07,659 - INFO - Request Parameters - Page 4:
2025-07-31 16:32:07,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:07,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:08,487 - INFO - Response - Page 4:
2025-07-31 16:32:08,487 - INFO - 第 4 页获取到 50 条记录
2025-07-31 16:32:08,987 - INFO - Request Parameters - Page 5:
2025-07-31 16:32:08,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:08,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:09,768 - INFO - Response - Page 5:
2025-07-31 16:32:09,768 - INFO - 第 5 页获取到 50 条记录
2025-07-31 16:32:10,268 - INFO - Request Parameters - Page 6:
2025-07-31 16:32:10,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:10,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:10,987 - INFO - Response - Page 6:
2025-07-31 16:32:10,987 - INFO - 第 6 页获取到 50 条记录
2025-07-31 16:32:11,502 - INFO - Request Parameters - Page 7:
2025-07-31 16:32:11,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:11,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:12,252 - INFO - Response - Page 7:
2025-07-31 16:32:12,252 - INFO - 第 7 页获取到 50 条记录
2025-07-31 16:32:12,768 - INFO - Request Parameters - Page 8:
2025-07-31 16:32:12,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:12,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:13,534 - INFO - Response - Page 8:
2025-07-31 16:32:13,534 - INFO - 第 8 页获取到 50 条记录
2025-07-31 16:32:14,034 - INFO - Request Parameters - Page 9:
2025-07-31 16:32:14,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:14,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:14,768 - INFO - Response - Page 9:
2025-07-31 16:32:14,768 - INFO - 第 9 页获取到 50 条记录
2025-07-31 16:32:15,284 - INFO - Request Parameters - Page 10:
2025-07-31 16:32:15,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:15,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:16,018 - INFO - Response - Page 10:
2025-07-31 16:32:16,018 - INFO - 第 10 页获取到 48 条记录
2025-07-31 16:32:16,534 - INFO - 查询完成，共获取到 498 条记录
2025-07-31 16:32:16,534 - INFO - 获取到 498 条表单数据
2025-07-31 16:32:16,534 - INFO - 当前日期 2025-07-30 有 517 条MySQL数据需要处理
2025-07-31 16:32:16,549 - INFO - 开始批量插入 19 条新记录
2025-07-31 16:32:16,752 - INFO - 批量插入响应状态码: 200
2025-07-31 16:32:16,752 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 08:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '924', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2456B671-E655-7F56-B5FE-9A7C7DE3F884', 'x-acs-trace-id': 'd343fa4801f9654a53ab124b150dd1ff', 'etag': '9zWKMSIXVPvUprCwipLw30w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 16:32:16,752 - INFO - 批量插入响应体: {'result': ['FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMT2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMU2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMV2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMW2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMX2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMY2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMZ2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM03', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM13', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM23', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM33', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM43', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM53', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM63', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM73', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM83', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM93', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMA3', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMB3']}
2025-07-31 16:32:16,752 - INFO - 批量插入表单数据成功，批次 1，共 19 条记录
2025-07-31 16:32:16,752 - INFO - 成功插入的数据ID: ['FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMT2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMU2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMV2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMW2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMX2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMY2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMZ2', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM03', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM13', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM23', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM33', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM43', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM53', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM63', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM73', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM83', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDM93', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMA3', 'FINST-IOC66G713PLXG69S8RM5T4152XWB36ZDZ4RDMB3']
2025-07-31 16:32:21,768 - INFO - 批量插入完成，共 19 条记录
2025-07-31 16:32:21,768 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 19 条，错误: 0 条
2025-07-31 16:32:21,768 - INFO - 开始处理日期: 2025-07-31
2025-07-31 16:32:21,768 - INFO - Request Parameters - Page 1:
2025-07-31 16:32:21,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 16:32:21,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 16:32:22,252 - INFO - Response - Page 1:
2025-07-31 16:32:22,252 - INFO - 第 1 页获取到 2 条记录
2025-07-31 16:32:22,752 - INFO - 查询完成，共获取到 2 条记录
2025-07-31 16:32:22,752 - INFO - 获取到 2 条表单数据
2025-07-31 16:32:22,752 - INFO - 当前日期 2025-07-31 有 2 条MySQL数据需要处理
2025-07-31 16:32:22,752 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-31 16:32:22,752 - INFO - 数据同步完成！更新: 0 条，插入: 19 条，错误: 0 条
2025-07-31 16:32:22,752 - INFO - 同步完成
2025-07-31 19:30:34,059 - INFO - 使用默认增量同步（当天更新数据）
2025-07-31 19:30:34,059 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-31 19:30:34,059 - INFO - 查询参数: ('2025-07-31',)
2025-07-31 19:30:34,231 - INFO - MySQL查询成功，增量数据（日期: 2025-07-31），共获取 181 条记录
2025-07-31 19:30:34,231 - INFO - 获取到 4 个日期需要处理: ['2025-07-28', '2025-07-29', '2025-07-30', '2025-07-31']
2025-07-31 19:30:34,231 - INFO - 开始处理日期: 2025-07-28
2025-07-31 19:30:34,231 - INFO - Request Parameters - Page 1:
2025-07-31 19:30:34,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:30:34,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:30:42,344 - ERROR - 处理日期 2025-07-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9998CA67-40C9-7E94-9318-6B15FD66A775 Response: {'code': 'ServiceUnavailable', 'requestid': '9998CA67-40C9-7E94-9318-6B15FD66A775', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9998CA67-40C9-7E94-9318-6B15FD66A775)
2025-07-31 19:30:42,344 - INFO - 开始处理日期: 2025-07-29
2025-07-31 19:30:42,344 - INFO - Request Parameters - Page 1:
2025-07-31 19:30:42,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:30:42,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:30:47,236 - INFO - Response - Page 1:
2025-07-31 19:30:47,236 - INFO - 第 1 页获取到 50 条记录
2025-07-31 19:30:47,752 - INFO - Request Parameters - Page 2:
2025-07-31 19:30:47,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:30:47,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:30:55,490 - INFO - Response - Page 2:
2025-07-31 19:30:55,490 - INFO - 第 2 页获取到 50 条记录
2025-07-31 19:30:56,006 - INFO - Request Parameters - Page 3:
2025-07-31 19:30:56,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:30:56,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:30:56,678 - INFO - Response - Page 3:
2025-07-31 19:30:56,678 - INFO - 第 3 页获取到 50 条记录
2025-07-31 19:30:57,193 - INFO - Request Parameters - Page 4:
2025-07-31 19:30:57,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:30:57,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:30:57,928 - INFO - Response - Page 4:
2025-07-31 19:30:57,928 - INFO - 第 4 页获取到 50 条记录
2025-07-31 19:30:58,444 - INFO - Request Parameters - Page 5:
2025-07-31 19:30:58,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:30:58,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:30:59,132 - INFO - Response - Page 5:
2025-07-31 19:30:59,132 - INFO - 第 5 页获取到 50 条记录
2025-07-31 19:30:59,648 - INFO - Request Parameters - Page 6:
2025-07-31 19:30:59,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:30:59,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:00,367 - INFO - Response - Page 6:
2025-07-31 19:31:00,367 - INFO - 第 6 页获取到 50 条记录
2025-07-31 19:31:00,882 - INFO - Request Parameters - Page 7:
2025-07-31 19:31:00,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:00,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:01,633 - INFO - Response - Page 7:
2025-07-31 19:31:01,633 - INFO - 第 7 页获取到 50 条记录
2025-07-31 19:31:02,133 - INFO - Request Parameters - Page 8:
2025-07-31 19:31:02,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:02,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:02,836 - INFO - Response - Page 8:
2025-07-31 19:31:02,836 - INFO - 第 8 页获取到 50 条记录
2025-07-31 19:31:03,337 - INFO - Request Parameters - Page 9:
2025-07-31 19:31:03,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:03,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:04,087 - INFO - Response - Page 9:
2025-07-31 19:31:04,087 - INFO - 第 9 页获取到 50 条记录
2025-07-31 19:31:04,603 - INFO - Request Parameters - Page 10:
2025-07-31 19:31:04,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:04,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:05,306 - INFO - Response - Page 10:
2025-07-31 19:31:05,322 - INFO - 第 10 页获取到 50 条记录
2025-07-31 19:31:05,838 - INFO - Request Parameters - Page 11:
2025-07-31 19:31:05,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:05,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:06,572 - INFO - Response - Page 11:
2025-07-31 19:31:06,572 - INFO - 第 11 页获取到 50 条记录
2025-07-31 19:31:07,088 - INFO - Request Parameters - Page 12:
2025-07-31 19:31:07,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:07,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:07,666 - INFO - Response - Page 12:
2025-07-31 19:31:07,666 - INFO - 第 12 页获取到 15 条记录
2025-07-31 19:31:08,167 - INFO - 查询完成，共获取到 565 条记录
2025-07-31 19:31:08,167 - INFO - 获取到 565 条表单数据
2025-07-31 19:31:08,167 - INFO - 当前日期 2025-07-29 有 2 条MySQL数据需要处理
2025-07-31 19:31:08,167 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMKU
2025-07-31 19:31:08,761 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMKU
2025-07-31 19:31:08,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-31 19:31:08,761 - INFO - 开始批量插入 1 条新记录
2025-07-31 19:31:08,932 - INFO - 批量插入响应状态码: 200
2025-07-31 19:31:08,932 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 11:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F8CDD9B5-A74B-7672-9849-A29BE5C350C7', 'x-acs-trace-id': 'e23802667454cf93800ece1eabfd113a', 'etag': '5puF7snwIFcFRxqQdVHbeTA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 19:31:08,932 - INFO - 批量插入响应体: {'result': ['FINST-5XA66LC15BLXOBELAA77S44ATK0537REDBRDMJ']}
2025-07-31 19:31:08,932 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-31 19:31:08,932 - INFO - 成功插入的数据ID: ['FINST-5XA66LC15BLXOBELAA77S44ATK0537REDBRDMJ']
2025-07-31 19:31:13,950 - INFO - 批量插入完成，共 1 条记录
2025-07-31 19:31:13,950 - INFO - 日期 2025-07-29 处理完成 - 更新: 1 条，插入: 1 条，错误: 0 条
2025-07-31 19:31:13,950 - INFO - 开始处理日期: 2025-07-30
2025-07-31 19:31:13,950 - INFO - Request Parameters - Page 1:
2025-07-31 19:31:13,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:13,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:14,638 - INFO - Response - Page 1:
2025-07-31 19:31:14,638 - INFO - 第 1 页获取到 50 条记录
2025-07-31 19:31:15,154 - INFO - Request Parameters - Page 2:
2025-07-31 19:31:15,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:15,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:15,920 - INFO - Response - Page 2:
2025-07-31 19:31:15,920 - INFO - 第 2 页获取到 50 条记录
2025-07-31 19:31:16,420 - INFO - Request Parameters - Page 3:
2025-07-31 19:31:16,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:16,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:17,108 - INFO - Response - Page 3:
2025-07-31 19:31:17,108 - INFO - 第 3 页获取到 50 条记录
2025-07-31 19:31:17,623 - INFO - Request Parameters - Page 4:
2025-07-31 19:31:17,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:17,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:18,311 - INFO - Response - Page 4:
2025-07-31 19:31:18,311 - INFO - 第 4 页获取到 50 条记录
2025-07-31 19:31:18,811 - INFO - Request Parameters - Page 5:
2025-07-31 19:31:18,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:18,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:19,468 - INFO - Response - Page 5:
2025-07-31 19:31:19,468 - INFO - 第 5 页获取到 50 条记录
2025-07-31 19:31:19,984 - INFO - Request Parameters - Page 6:
2025-07-31 19:31:19,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:19,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:20,672 - INFO - Response - Page 6:
2025-07-31 19:31:20,672 - INFO - 第 6 页获取到 50 条记录
2025-07-31 19:31:21,187 - INFO - Request Parameters - Page 7:
2025-07-31 19:31:21,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:21,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:21,906 - INFO - Response - Page 7:
2025-07-31 19:31:21,906 - INFO - 第 7 页获取到 50 条记录
2025-07-31 19:31:22,422 - INFO - Request Parameters - Page 8:
2025-07-31 19:31:22,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:22,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:23,157 - INFO - Response - Page 8:
2025-07-31 19:31:23,157 - INFO - 第 8 页获取到 50 条记录
2025-07-31 19:31:23,673 - INFO - Request Parameters - Page 9:
2025-07-31 19:31:23,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:23,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:24,360 - INFO - Response - Page 9:
2025-07-31 19:31:24,360 - INFO - 第 9 页获取到 50 条记录
2025-07-31 19:31:24,861 - INFO - Request Parameters - Page 10:
2025-07-31 19:31:24,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:24,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:25,564 - INFO - Response - Page 10:
2025-07-31 19:31:25,564 - INFO - 第 10 页获取到 50 条记录
2025-07-31 19:31:26,080 - INFO - Request Parameters - Page 11:
2025-07-31 19:31:26,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:26,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:26,658 - INFO - Response - Page 11:
2025-07-31 19:31:26,658 - INFO - 第 11 页获取到 17 条记录
2025-07-31 19:31:27,174 - INFO - 查询完成，共获取到 517 条记录
2025-07-31 19:31:27,174 - INFO - 获取到 517 条表单数据
2025-07-31 19:31:27,174 - INFO - 当前日期 2025-07-30 有 173 条MySQL数据需要处理
2025-07-31 19:31:27,174 - INFO - 开始批量插入 24 条新记录
2025-07-31 19:31:27,393 - INFO - 批量插入响应状态码: 200
2025-07-31 19:31:27,393 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 11:31:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D60981B-4DB1-703B-86CB-5957CF644A85', 'x-acs-trace-id': 'c047f4f03c77b0a1246a696ecb04fed0', 'etag': '1uNn8Q//eG51Z8vCHJZe/gQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 19:31:27,393 - INFO - 批量插入响应体: {'result': ['FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMN2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMO2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMP2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMQ2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMR2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMS2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMT2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMU2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMV2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMW2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMX2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMY2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMZ2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM03', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM13', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM23', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM33', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM43', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM53', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM63', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM73', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM83', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM93', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMA3']}
2025-07-31 19:31:27,393 - INFO - 批量插入表单数据成功，批次 1，共 24 条记录
2025-07-31 19:31:27,393 - INFO - 成功插入的数据ID: ['FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMN2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMO2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMP2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMQ2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMR2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMS2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMT2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMU2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMV2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMW2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMX2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMY2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMZ2', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM03', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM13', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM23', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM33', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM43', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM53', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM63', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM73', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM83', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDM93', 'FINST-RI7660914BMXJ1PCAVTLO90GLRYJ3UZSDBRDMA3']
2025-07-31 19:31:32,411 - INFO - 批量插入完成，共 24 条记录
2025-07-31 19:31:32,411 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 24 条，错误: 0 条
2025-07-31 19:31:32,411 - INFO - 开始处理日期: 2025-07-31
2025-07-31 19:31:32,411 - INFO - Request Parameters - Page 1:
2025-07-31 19:31:32,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:31:32,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:31:32,911 - INFO - Response - Page 1:
2025-07-31 19:31:32,911 - INFO - 第 1 页获取到 2 条记录
2025-07-31 19:31:33,427 - INFO - 查询完成，共获取到 2 条记录
2025-07-31 19:31:33,427 - INFO - 获取到 2 条表单数据
2025-07-31 19:31:33,427 - INFO - 当前日期 2025-07-31 有 2 条MySQL数据需要处理
2025-07-31 19:31:33,427 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-31 19:31:33,427 - INFO - 数据同步完成！更新: 1 条，插入: 25 条，错误: 1 条
2025-07-31 19:32:33,466 - INFO - 开始同步昨天与今天的销售数据: 2025-07-30 至 2025-07-31
2025-07-31 19:32:33,466 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-31 19:32:33,466 - INFO - 查询参数: ('2025-07-30', '2025-07-31')
2025-07-31 19:32:33,638 - INFO - MySQL查询成功，时间段: 2025-07-30 至 2025-07-31，共获取 560 条记录
2025-07-31 19:32:33,638 - INFO - 获取到 2 个日期需要处理: ['2025-07-30', '2025-07-31']
2025-07-31 19:32:33,638 - INFO - 开始处理日期: 2025-07-30
2025-07-31 19:32:33,638 - INFO - Request Parameters - Page 1:
2025-07-31 19:32:33,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:33,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:34,420 - INFO - Response - Page 1:
2025-07-31 19:32:34,420 - INFO - 第 1 页获取到 50 条记录
2025-07-31 19:32:34,935 - INFO - Request Parameters - Page 2:
2025-07-31 19:32:34,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:34,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:35,748 - INFO - Response - Page 2:
2025-07-31 19:32:35,748 - INFO - 第 2 页获取到 50 条记录
2025-07-31 19:32:36,264 - INFO - Request Parameters - Page 3:
2025-07-31 19:32:36,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:36,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:37,108 - INFO - Response - Page 3:
2025-07-31 19:32:37,108 - INFO - 第 3 页获取到 50 条记录
2025-07-31 19:32:37,608 - INFO - Request Parameters - Page 4:
2025-07-31 19:32:37,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:37,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:38,343 - INFO - Response - Page 4:
2025-07-31 19:32:38,343 - INFO - 第 4 页获取到 50 条记录
2025-07-31 19:32:38,859 - INFO - Request Parameters - Page 5:
2025-07-31 19:32:38,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:38,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:39,562 - INFO - Response - Page 5:
2025-07-31 19:32:39,562 - INFO - 第 5 页获取到 50 条记录
2025-07-31 19:32:40,078 - INFO - Request Parameters - Page 6:
2025-07-31 19:32:40,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:40,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:40,781 - INFO - Response - Page 6:
2025-07-31 19:32:40,781 - INFO - 第 6 页获取到 50 条记录
2025-07-31 19:32:41,297 - INFO - Request Parameters - Page 7:
2025-07-31 19:32:41,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:41,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:42,016 - INFO - Response - Page 7:
2025-07-31 19:32:42,016 - INFO - 第 7 页获取到 50 条记录
2025-07-31 19:32:42,532 - INFO - Request Parameters - Page 8:
2025-07-31 19:32:42,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:42,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:43,236 - INFO - Response - Page 8:
2025-07-31 19:32:43,236 - INFO - 第 8 页获取到 50 条记录
2025-07-31 19:32:43,736 - INFO - Request Parameters - Page 9:
2025-07-31 19:32:43,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:43,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:44,486 - INFO - Response - Page 9:
2025-07-31 19:32:44,486 - INFO - 第 9 页获取到 50 条记录
2025-07-31 19:32:45,002 - INFO - Request Parameters - Page 10:
2025-07-31 19:32:45,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:45,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:45,721 - INFO - Response - Page 10:
2025-07-31 19:32:45,721 - INFO - 第 10 页获取到 50 条记录
2025-07-31 19:32:46,237 - INFO - Request Parameters - Page 11:
2025-07-31 19:32:46,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:46,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:46,956 - INFO - Response - Page 11:
2025-07-31 19:32:46,956 - INFO - 第 11 页获取到 41 条记录
2025-07-31 19:32:47,456 - INFO - 查询完成，共获取到 541 条记录
2025-07-31 19:32:47,456 - INFO - 获取到 541 条表单数据
2025-07-31 19:32:47,456 - INFO - 当前日期 2025-07-30 有 543 条MySQL数据需要处理
2025-07-31 19:32:47,472 - INFO - 开始批量插入 2 条新记录
2025-07-31 19:32:47,628 - INFO - 批量插入响应状态码: 200
2025-07-31 19:32:47,628 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 11:32:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4A346ABF-5E2E-7C73-A73A-E52DF50B45BA', 'x-acs-trace-id': '9093a9d4e177fad101f451a233d6e294', 'etag': '16tYt7gzegtccEho4Ub9fcw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 19:32:47,628 - INFO - 批量插入响应体: {'result': ['FINST-SE766YC1KNMXKWRW9QM5F65C3NJ52SVIFBRDMB', 'FINST-SE766YC1KNMXKWRW9QM5F65C3NJ52SVIFBRDMC']}
2025-07-31 19:32:47,628 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-31 19:32:47,628 - INFO - 成功插入的数据ID: ['FINST-SE766YC1KNMXKWRW9QM5F65C3NJ52SVIFBRDMB', 'FINST-SE766YC1KNMXKWRW9QM5F65C3NJ52SVIFBRDMC']
2025-07-31 19:32:52,645 - INFO - 批量插入完成，共 2 条记录
2025-07-31 19:32:52,645 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-31 19:32:52,645 - INFO - 开始处理日期: 2025-07-31
2025-07-31 19:32:52,645 - INFO - Request Parameters - Page 1:
2025-07-31 19:32:52,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 19:32:52,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 19:32:53,114 - INFO - Response - Page 1:
2025-07-31 19:32:53,114 - INFO - 第 1 页获取到 2 条记录
2025-07-31 19:32:53,615 - INFO - 查询完成，共获取到 2 条记录
2025-07-31 19:32:53,615 - INFO - 获取到 2 条表单数据
2025-07-31 19:32:53,615 - INFO - 当前日期 2025-07-31 有 2 条MySQL数据需要处理
2025-07-31 19:32:53,615 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-31 19:32:53,615 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-07-31 19:32:53,615 - INFO - 同步完成
2025-07-31 22:30:33,870 - INFO - 使用默认增量同步（当天更新数据）
2025-07-31 22:30:33,870 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-31 22:30:33,870 - INFO - 查询参数: ('2025-07-31',)
2025-07-31 22:30:34,026 - INFO - MySQL查询成功，增量数据（日期: 2025-07-31），共获取 215 条记录
2025-07-31 22:30:34,026 - INFO - 获取到 4 个日期需要处理: ['2025-07-28', '2025-07-29', '2025-07-30', '2025-07-31']
2025-07-31 22:30:34,042 - INFO - 开始处理日期: 2025-07-28
2025-07-31 22:30:34,042 - INFO - Request Parameters - Page 1:
2025-07-31 22:30:34,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:34,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:30:42,170 - ERROR - 处理日期 2025-07-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C0D6AFE6-6006-7717-8C20-23A2DDF745EA Response: {'code': 'ServiceUnavailable', 'requestid': 'C0D6AFE6-6006-7717-8C20-23A2DDF745EA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C0D6AFE6-6006-7717-8C20-23A2DDF745EA)
2025-07-31 22:30:42,170 - INFO - 开始处理日期: 2025-07-29
2025-07-31 22:30:42,170 - INFO - Request Parameters - Page 1:
2025-07-31 22:30:42,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:42,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:30:42,920 - INFO - Response - Page 1:
2025-07-31 22:30:42,920 - INFO - 第 1 页获取到 50 条记录
2025-07-31 22:30:43,420 - INFO - Request Parameters - Page 2:
2025-07-31 22:30:43,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:43,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:30:51,533 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B77043AD-FC65-7F63-B23D-AB97A138A097 Response: {'code': 'ServiceUnavailable', 'requestid': 'B77043AD-FC65-7F63-B23D-AB97A138A097', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B77043AD-FC65-7F63-B23D-AB97A138A097)
2025-07-31 22:30:51,533 - INFO - 开始处理日期: 2025-07-30
2025-07-31 22:30:51,533 - INFO - Request Parameters - Page 1:
2025-07-31 22:30:51,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:51,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:30:53,831 - INFO - Response - Page 1:
2025-07-31 22:30:53,831 - INFO - 第 1 页获取到 50 条记录
2025-07-31 22:30:54,331 - INFO - Request Parameters - Page 2:
2025-07-31 22:30:54,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:54,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:30:55,081 - INFO - Response - Page 2:
2025-07-31 22:30:55,081 - INFO - 第 2 页获取到 50 条记录
2025-07-31 22:30:55,581 - INFO - Request Parameters - Page 3:
2025-07-31 22:30:55,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:55,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:30:56,363 - INFO - Response - Page 3:
2025-07-31 22:30:56,363 - INFO - 第 3 页获取到 50 条记录
2025-07-31 22:30:56,863 - INFO - Request Parameters - Page 4:
2025-07-31 22:30:56,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:56,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:30:57,598 - INFO - Response - Page 4:
2025-07-31 22:30:57,598 - INFO - 第 4 页获取到 50 条记录
2025-07-31 22:30:58,114 - INFO - Request Parameters - Page 5:
2025-07-31 22:30:58,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:58,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:30:58,864 - INFO - Response - Page 5:
2025-07-31 22:30:58,864 - INFO - 第 5 页获取到 50 条记录
2025-07-31 22:30:59,364 - INFO - Request Parameters - Page 6:
2025-07-31 22:30:59,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:30:59,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:31:00,083 - INFO - Response - Page 6:
2025-07-31 22:31:00,083 - INFO - 第 6 页获取到 50 条记录
2025-07-31 22:31:00,583 - INFO - Request Parameters - Page 7:
2025-07-31 22:31:00,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:31:00,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:31:01,365 - INFO - Response - Page 7:
2025-07-31 22:31:01,365 - INFO - 第 7 页获取到 50 条记录
2025-07-31 22:31:01,865 - INFO - Request Parameters - Page 8:
2025-07-31 22:31:01,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:31:01,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:31:02,662 - INFO - Response - Page 8:
2025-07-31 22:31:02,662 - INFO - 第 8 页获取到 50 条记录
2025-07-31 22:31:03,178 - INFO - Request Parameters - Page 9:
2025-07-31 22:31:03,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:31:03,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:31:03,928 - INFO - Response - Page 9:
2025-07-31 22:31:03,928 - INFO - 第 9 页获取到 50 条记录
2025-07-31 22:31:04,444 - INFO - Request Parameters - Page 10:
2025-07-31 22:31:04,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:31:04,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:31:05,163 - INFO - Response - Page 10:
2025-07-31 22:31:05,163 - INFO - 第 10 页获取到 50 条记录
2025-07-31 22:31:05,664 - INFO - Request Parameters - Page 11:
2025-07-31 22:31:05,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:31:05,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:31:06,383 - INFO - Response - Page 11:
2025-07-31 22:31:06,383 - INFO - 第 11 页获取到 43 条记录
2025-07-31 22:31:06,883 - INFO - 查询完成，共获取到 543 条记录
2025-07-31 22:31:06,883 - INFO - 获取到 543 条表单数据
2025-07-31 22:31:06,883 - INFO - 当前日期 2025-07-30 有 173 条MySQL数据需要处理
2025-07-31 22:31:06,883 - INFO - 开始更新记录 - 表单实例ID: FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMUS
2025-07-31 22:31:07,508 - INFO - 更新表单数据成功: FINST-68E66TC15BJXA4CNER3O06ICV53F3ECO2SQDMUS
2025-07-31 22:31:07,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3000.0, 'new_value': 396.0}, {'field': 'total_amount', 'old_value': 3000.0, 'new_value': 396.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 36}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/7442c58ef1414c6ca9b1868a90344db7.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=0rqKsKlO4JH3LIPa91fsGJmBmNw%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/5f3ba056477f470c8da29398bf357031.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Dw3LKWdqvW%2BWYqxpU2YDs5vYIsc%3D'}]
2025-07-31 22:31:07,508 - INFO - 日期 2025-07-30 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-31 22:31:07,508 - INFO - 开始处理日期: 2025-07-31
2025-07-31 22:31:07,508 - INFO - Request Parameters - Page 1:
2025-07-31 22:31:07,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:31:07,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:31:08,008 - INFO - Response - Page 1:
2025-07-31 22:31:08,008 - INFO - 第 1 页获取到 2 条记录
2025-07-31 22:31:08,524 - INFO - 查询完成，共获取到 2 条记录
2025-07-31 22:31:08,524 - INFO - 获取到 2 条表单数据
2025-07-31 22:31:08,524 - INFO - 当前日期 2025-07-31 有 35 条MySQL数据需要处理
2025-07-31 22:31:08,524 - INFO - 开始批量插入 33 条新记录
2025-07-31 22:31:08,774 - INFO - 批量插入响应状态码: 200
2025-07-31 22:31:08,774 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 14:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1629', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '44F3A7F3-1B6F-798F-969B-D63BD27059A6', 'x-acs-trace-id': '9e8dee76ce2e7cb945f95f8fcaaa7b53', 'etag': '1jTFfov3qcvZjr/SMLvsHkQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 22:31:08,774 - INFO - 批量插入响应体: {'result': ['FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMDA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMEA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMFA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMGA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMHA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMIA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMJA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMKA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMLA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMMA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMNA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMOA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMPA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMQA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMRA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMSA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMTA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMUA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMVA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMWA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMXA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMYA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMZA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM0B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM1B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM2B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM3B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM4B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM5B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM6B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM7B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM8B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM9B2']}
2025-07-31 22:31:08,774 - INFO - 批量插入表单数据成功，批次 1，共 33 条记录
2025-07-31 22:31:08,774 - INFO - 成功插入的数据ID: ['FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMDA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMEA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMFA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMGA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMHA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMIA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMJA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMKA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMLA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMMA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMNA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMOA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMPA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMQA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMRA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMSA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMTA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMUA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMVA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMWA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMXA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMYA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDMZA2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM0B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM1B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM2B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM3B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM4B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM5B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM6B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM7B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM8B2', 'FINST-KLF66HD18JHXMEAC9JIUKA73SQ0E3NNSSHRDM9B2']
2025-07-31 22:31:13,792 - INFO - 批量插入完成，共 33 条记录
2025-07-31 22:31:13,792 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 33 条，错误: 0 条
2025-07-31 22:31:13,792 - INFO - 数据同步完成！更新: 1 条，插入: 33 条，错误: 2 条
2025-07-31 22:32:13,831 - INFO - 开始同步昨天与今天的销售数据: 2025-07-30 至 2025-07-31
2025-07-31 22:32:13,831 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-31 22:32:13,831 - INFO - 查询参数: ('2025-07-30', '2025-07-31')
2025-07-31 22:32:14,003 - INFO - MySQL查询成功，时间段: 2025-07-30 至 2025-07-31，共获取 618 条记录
2025-07-31 22:32:14,003 - INFO - 获取到 2 个日期需要处理: ['2025-07-30', '2025-07-31']
2025-07-31 22:32:14,003 - INFO - 开始处理日期: 2025-07-30
2025-07-31 22:32:14,003 - INFO - Request Parameters - Page 1:
2025-07-31 22:32:14,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:14,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:14,800 - INFO - Response - Page 1:
2025-07-31 22:32:14,800 - INFO - 第 1 页获取到 50 条记录
2025-07-31 22:32:15,300 - INFO - Request Parameters - Page 2:
2025-07-31 22:32:15,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:15,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:16,098 - INFO - Response - Page 2:
2025-07-31 22:32:16,098 - INFO - 第 2 页获取到 50 条记录
2025-07-31 22:32:16,598 - INFO - Request Parameters - Page 3:
2025-07-31 22:32:16,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:16,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:17,301 - INFO - Response - Page 3:
2025-07-31 22:32:17,301 - INFO - 第 3 页获取到 50 条记录
2025-07-31 22:32:17,817 - INFO - Request Parameters - Page 4:
2025-07-31 22:32:17,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:17,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:18,583 - INFO - Response - Page 4:
2025-07-31 22:32:18,583 - INFO - 第 4 页获取到 50 条记录
2025-07-31 22:32:19,099 - INFO - Request Parameters - Page 5:
2025-07-31 22:32:19,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:19,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:19,849 - INFO - Response - Page 5:
2025-07-31 22:32:19,849 - INFO - 第 5 页获取到 50 条记录
2025-07-31 22:32:20,349 - INFO - Request Parameters - Page 6:
2025-07-31 22:32:20,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:20,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:21,100 - INFO - Response - Page 6:
2025-07-31 22:32:21,100 - INFO - 第 6 页获取到 50 条记录
2025-07-31 22:32:21,600 - INFO - Request Parameters - Page 7:
2025-07-31 22:32:21,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:21,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:22,272 - INFO - Response - Page 7:
2025-07-31 22:32:22,272 - INFO - 第 7 页获取到 50 条记录
2025-07-31 22:32:22,772 - INFO - Request Parameters - Page 8:
2025-07-31 22:32:22,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:22,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:23,476 - INFO - Response - Page 8:
2025-07-31 22:32:23,476 - INFO - 第 8 页获取到 50 条记录
2025-07-31 22:32:23,976 - INFO - Request Parameters - Page 9:
2025-07-31 22:32:23,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:23,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:24,679 - INFO - Response - Page 9:
2025-07-31 22:32:24,679 - INFO - 第 9 页获取到 50 条记录
2025-07-31 22:32:25,195 - INFO - Request Parameters - Page 10:
2025-07-31 22:32:25,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:25,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:25,977 - INFO - Response - Page 10:
2025-07-31 22:32:25,977 - INFO - 第 10 页获取到 50 条记录
2025-07-31 22:32:26,477 - INFO - Request Parameters - Page 11:
2025-07-31 22:32:26,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:26,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:27,133 - INFO - Response - Page 11:
2025-07-31 22:32:27,133 - INFO - 第 11 页获取到 43 条记录
2025-07-31 22:32:27,649 - INFO - 查询完成，共获取到 543 条记录
2025-07-31 22:32:27,649 - INFO - 获取到 543 条表单数据
2025-07-31 22:32:27,649 - INFO - 当前日期 2025-07-30 有 543 条MySQL数据需要处理
2025-07-31 22:32:27,665 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-31 22:32:27,665 - INFO - 开始处理日期: 2025-07-31
2025-07-31 22:32:27,665 - INFO - Request Parameters - Page 1:
2025-07-31 22:32:27,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-31 22:32:27,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-31 22:32:28,415 - INFO - Response - Page 1:
2025-07-31 22:32:28,415 - INFO - 第 1 页获取到 35 条记录
2025-07-31 22:32:28,931 - INFO - 查询完成，共获取到 35 条记录
2025-07-31 22:32:28,931 - INFO - 获取到 35 条表单数据
2025-07-31 22:32:28,931 - INFO - 当前日期 2025-07-31 有 59 条MySQL数据需要处理
2025-07-31 22:32:28,931 - INFO - 开始批量插入 24 条新记录
2025-07-31 22:32:29,134 - INFO - 批量插入响应状态码: 200
2025-07-31 22:32:29,134 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 14:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5C3CD2AC-E108-7619-A8E8-24604BEA5BD2', 'x-acs-trace-id': '55aa5621c002f5708a15e8f436a3ddcf', 'etag': '1DcZEUFdqXYYunIQusxdVtg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-31 22:32:29,134 - INFO - 批量插入响应体: {'result': ['FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMAS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMBS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMCS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMDS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMES', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMFS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMGS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMHS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMIS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMJS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMKS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMLS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMMS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMNS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMOS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMPS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMQS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMRS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMSS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMTS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMUS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMVS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMWS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMXS']}
2025-07-31 22:32:29,134 - INFO - 批量插入表单数据成功，批次 1，共 24 条记录
2025-07-31 22:32:29,134 - INFO - 成功插入的数据ID: ['FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMAS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMBS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMCS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMDS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMES', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMFS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMGS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMHS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMIS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMJS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMKS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMLS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMMS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMNS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMOS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMPS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMQS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMRS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP25NIUHRDMSS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMTS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMUS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMVS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMWS', 'FINST-MLF66JA1ZDJXH6AS6NJP29I6SNPP26NIUHRDMXS']
2025-07-31 22:32:34,152 - INFO - 批量插入完成，共 24 条记录
2025-07-31 22:32:34,152 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 24 条，错误: 0 条
2025-07-31 22:32:34,152 - INFO - 数据同步完成！更新: 0 条，插入: 24 条，错误: 0 条
2025-07-31 22:32:34,152 - INFO - 同步完成
