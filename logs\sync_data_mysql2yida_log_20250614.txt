2025-06-14 01:30:33,606 - INFO - 使用默认增量同步（当天更新数据）
2025-06-14 01:30:33,606 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-14 01:30:33,606 - INFO - 查询参数: ('2025-06-14',)
2025-06-14 01:30:33,685 - INFO - MySQL查询成功，增量数据（日期: 2025-06-14），共获取 0 条记录
2025-06-14 01:30:33,685 - ERROR - 未获取到MySQL数据
2025-06-14 01:31:33,700 - INFO - 开始同步昨天与今天的销售数据: 2025-06-13 至 2025-06-14
2025-06-14 01:31:33,700 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-14 01:31:33,700 - INFO - 查询参数: ('2025-06-13', '2025-06-14')
2025-06-14 01:31:33,809 - INFO - MySQL查询成功，时间段: 2025-06-13 至 2025-06-14，共获取 100 条记录
2025-06-14 01:31:33,809 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 01:31:33,825 - INFO - 开始处理日期: 2025-06-13
2025-06-14 01:31:33,825 - INFO - Request Parameters - Page 1:
2025-06-14 01:31:33,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 01:31:33,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 01:31:41,981 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DB9C17DA-20B1-75D0-A276-39618704B0A5 Response: {'code': 'ServiceUnavailable', 'requestid': 'DB9C17DA-20B1-75D0-A276-39618704B0A5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DB9C17DA-20B1-75D0-A276-39618704B0A5)
2025-06-14 01:31:41,981 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-14 01:31:41,981 - INFO - 同步完成
2025-06-14 04:30:33,722 - INFO - 使用默认增量同步（当天更新数据）
2025-06-14 04:30:33,722 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-14 04:30:33,722 - INFO - 查询参数: ('2025-06-14',)
2025-06-14 04:30:33,847 - INFO - MySQL查询成功，增量数据（日期: 2025-06-14），共获取 5 条记录
2025-06-14 04:30:33,847 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 04:30:33,847 - INFO - 开始处理日期: 2025-06-13
2025-06-14 04:30:33,847 - INFO - Request Parameters - Page 1:
2025-06-14 04:30:33,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 04:30:33,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 04:30:41,972 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 578468DA-6AB0-7A7A-BBB2-22FF65D567AA Response: {'code': 'ServiceUnavailable', 'requestid': '578468DA-6AB0-7A7A-BBB2-22FF65D567AA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 578468DA-6AB0-7A7A-BBB2-22FF65D567AA)
2025-06-14 04:30:41,972 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-14 04:31:41,987 - INFO - 开始同步昨天与今天的销售数据: 2025-06-13 至 2025-06-14
2025-06-14 04:31:41,987 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-14 04:31:41,987 - INFO - 查询参数: ('2025-06-13', '2025-06-14')
2025-06-14 04:31:42,112 - INFO - MySQL查询成功，时间段: 2025-06-13 至 2025-06-14，共获取 122 条记录
2025-06-14 04:31:42,112 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 04:31:42,112 - INFO - 开始处理日期: 2025-06-13
2025-06-14 04:31:42,112 - INFO - Request Parameters - Page 1:
2025-06-14 04:31:42,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 04:31:42,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 04:31:42,815 - INFO - Response - Page 1:
2025-06-14 04:31:42,815 - INFO - 第 1 页获取到 50 条记录
2025-06-14 04:31:43,315 - INFO - Request Parameters - Page 2:
2025-06-14 04:31:43,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 04:31:43,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 04:31:49,050 - INFO - Response - Page 2:
2025-06-14 04:31:49,050 - INFO - 第 2 页获取到 26 条记录
2025-06-14 04:31:49,550 - INFO - 查询完成，共获取到 76 条记录
2025-06-14 04:31:49,550 - INFO - 获取到 76 条表单数据
2025-06-14 04:31:49,550 - INFO - 当前日期 2025-06-13 有 117 条MySQL数据需要处理
2025-06-14 04:31:49,550 - INFO - 开始批量插入 41 条新记录
2025-06-14 04:31:49,784 - INFO - 批量插入响应状态码: 200
2025-06-14 04:31:49,784 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 13 Jun 2025 20:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1980', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EE7140B8-ABF8-78EA-9FD8-6942DA6237A3', 'x-acs-trace-id': '9f5ee93186710c6f4cf6d92edf93c591', 'etag': '16tGQ7SLbhLEUv1sgkY/MYA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 04:31:49,784 - INFO - 批量插入响应体: {'result': ['FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMTU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMUU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMVU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMWU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMXU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMYU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMZU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM0V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM1V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM2V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM3V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM4V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM5V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM6V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM7V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM8V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM9V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMAV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMBV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMCV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMDV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMEV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMFV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMGV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMHV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMIV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMJV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMKV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMLV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMMV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMNV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMOV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMPV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMQV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMRV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMSV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMTV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMUV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMVV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMWV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMXV']}
2025-06-14 04:31:49,784 - INFO - 批量插入表单数据成功，批次 1，共 41 条记录
2025-06-14 04:31:49,784 - INFO - 成功插入的数据ID: ['FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMTU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMUU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMVU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMWU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMXU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMYU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMZU', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM0V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM1V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM2V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM3V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM4V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM5V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM6V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM7V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM8V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBM9V', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMAV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMBV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMCV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMDV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMEV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMFV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMGV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMHV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMIV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3UKUJ9VBMJV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMKV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMLV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMMV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMNV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMOV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMPV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMQV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMRV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMSV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMTV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMUV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMVV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMWV', 'FINST-FPB66VB1ZP6WH1H9BC4WZ7NWQM0L3VKUJ9VBMXV']
2025-06-14 04:31:54,800 - INFO - 批量插入完成，共 41 条记录
2025-06-14 04:31:54,800 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 41 条，错误: 0 条
2025-06-14 04:31:54,800 - INFO - 数据同步完成！更新: 0 条，插入: 41 条，错误: 0 条
2025-06-14 04:31:54,800 - INFO - 同步完成
2025-06-14 07:30:33,557 - INFO - 使用默认增量同步（当天更新数据）
2025-06-14 07:30:33,572 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-14 07:30:33,572 - INFO - 查询参数: ('2025-06-14',)
2025-06-14 07:30:33,682 - INFO - MySQL查询成功，增量数据（日期: 2025-06-14），共获取 10 条记录
2025-06-14 07:30:33,682 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 07:30:33,682 - INFO - 开始处理日期: 2025-06-13
2025-06-14 07:30:33,697 - INFO - Request Parameters - Page 1:
2025-06-14 07:30:33,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 07:30:33,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 07:30:41,807 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 477CDC88-DC33-7F41-B970-E463C6882E8C Response: {'code': 'ServiceUnavailable', 'requestid': '477CDC88-DC33-7F41-B970-E463C6882E8C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 477CDC88-DC33-7F41-B970-E463C6882E8C)
2025-06-14 07:30:41,807 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-14 07:31:41,822 - INFO - 开始同步昨天与今天的销售数据: 2025-06-13 至 2025-06-14
2025-06-14 07:31:41,822 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-14 07:31:41,822 - INFO - 查询参数: ('2025-06-13', '2025-06-14')
2025-06-14 07:31:41,947 - INFO - MySQL查询成功，时间段: 2025-06-13 至 2025-06-14，共获取 183 条记录
2025-06-14 07:31:41,947 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 07:31:41,947 - INFO - 开始处理日期: 2025-06-13
2025-06-14 07:31:41,947 - INFO - Request Parameters - Page 1:
2025-06-14 07:31:41,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 07:31:41,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 07:31:47,275 - INFO - Response - Page 1:
2025-06-14 07:31:47,275 - INFO - 第 1 页获取到 50 条记录
2025-06-14 07:31:47,775 - INFO - Request Parameters - Page 2:
2025-06-14 07:31:47,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 07:31:47,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 07:31:48,431 - INFO - Response - Page 2:
2025-06-14 07:31:48,431 - INFO - 第 2 页获取到 50 条记录
2025-06-14 07:31:48,931 - INFO - Request Parameters - Page 3:
2025-06-14 07:31:48,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 07:31:48,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 07:31:49,509 - INFO - Response - Page 3:
2025-06-14 07:31:49,509 - INFO - 第 3 页获取到 17 条记录
2025-06-14 07:31:50,025 - INFO - 查询完成，共获取到 117 条记录
2025-06-14 07:31:50,025 - INFO - 获取到 117 条表单数据
2025-06-14 07:31:50,025 - INFO - 当前日期 2025-06-13 有 176 条MySQL数据需要处理
2025-06-14 07:31:50,025 - INFO - 开始批量插入 59 条新记录
2025-06-14 07:31:50,259 - INFO - 批量插入响应状态码: 200
2025-06-14 07:31:50,259 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 13 Jun 2025 23:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '40780A74-12CA-7B07-80AC-2384CBEBFD91', 'x-acs-trace-id': '541fc22b7305d1fdcd9eeb956a8ff21d', 'etag': '2uHGfB8YOU3SAJNIoFP/kbw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 07:31:50,259 - INFO - 批量插入响应体: {'result': ['FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMF2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMG2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMH2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMI2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMJ2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMK2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBML2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMM2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMN2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMO2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMP2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMQ2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMR2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMS2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMT2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMU2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMV2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMW2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMX2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMY2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMZ2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM03', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM13', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM23', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM33', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM43', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM53', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM63', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM73', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM83', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM93', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMA3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMB3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMC3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMD3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBME3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMF3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMG3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMH3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMI3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMJ3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMK3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBML3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMM3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMN3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMO3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMP3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMQ3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMR3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMS3']}
2025-06-14 07:31:50,259 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-14 07:31:50,259 - INFO - 成功插入的数据ID: ['FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMF2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMG2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMH2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMI2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMJ2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMK2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBML2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMM2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMN2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMO2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMP2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMQ2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMR2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMS2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMT2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMU2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMV2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMW2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMX2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMY2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMZ2', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM03', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM13', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM23', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM33', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM43', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM53', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM63', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM73', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM83', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBM93', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMA3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMB3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMC3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMD3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBME3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMF3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMG3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMH3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMI3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMJ3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMK3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBML3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMM3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMN3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMO3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMP3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMQ3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMR3', 'FINST-LLF66F71EM8WBVTXCQZVI8E26IYH2XBCZFVBMS3']
2025-06-14 07:31:55,415 - INFO - 批量插入响应状态码: 200
2025-06-14 07:31:55,415 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 13 Jun 2025 23:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6CAA39E8-1C7F-73C1-AA5B-E8F177674294', 'x-acs-trace-id': '24e30b0e4e8778591e71495014b18db3', 'etag': '43JlkRxLsrdXyA42eYPoCog4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 07:31:55,415 - INFO - 批量插入响应体: {'result': ['FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMCA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMDA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMEA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMFA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMGA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMHA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMIA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMJA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMKA']}
2025-06-14 07:31:55,415 - INFO - 批量插入表单数据成功，批次 2，共 9 条记录
2025-06-14 07:31:55,415 - INFO - 成功插入的数据ID: ['FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMCA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMDA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMEA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMFA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMGA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMHA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMIA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMJA', 'FINST-TKF66981YS9WDXY98MUEHD61PD0G39BGZFVBMKA']
2025-06-14 07:32:00,431 - INFO - 批量插入完成，共 59 条记录
2025-06-14 07:32:00,431 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 59 条，错误: 0 条
2025-06-14 07:32:00,431 - INFO - 数据同步完成！更新: 0 条，插入: 59 条，错误: 0 条
2025-06-14 07:32:00,431 - INFO - 同步完成
2025-06-14 10:30:33,563 - INFO - 使用默认增量同步（当天更新数据）
2025-06-14 10:30:33,563 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-14 10:30:33,579 - INFO - 查询参数: ('2025-06-14',)
2025-06-14 10:30:33,704 - INFO - MySQL查询成功，增量数据（日期: 2025-06-14），共获取 110 条记录
2025-06-14 10:30:33,704 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 10:30:33,704 - INFO - 开始处理日期: 2025-06-13
2025-06-14 10:30:33,704 - INFO - Request Parameters - Page 1:
2025-06-14 10:30:33,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 10:30:33,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 10:30:41,829 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ECCEF913-5186-7D91-B0F6-587FDA67F283 Response: {'code': 'ServiceUnavailable', 'requestid': 'ECCEF913-5186-7D91-B0F6-587FDA67F283', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ECCEF913-5186-7D91-B0F6-587FDA67F283)
2025-06-14 10:30:41,829 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-14 10:31:41,844 - INFO - 开始同步昨天与今天的销售数据: 2025-06-13 至 2025-06-14
2025-06-14 10:31:41,844 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-14 10:31:41,844 - INFO - 查询参数: ('2025-06-13', '2025-06-14')
2025-06-14 10:31:41,969 - INFO - MySQL查询成功，时间段: 2025-06-13 至 2025-06-14，共获取 426 条记录
2025-06-14 10:31:41,969 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 10:31:41,969 - INFO - 开始处理日期: 2025-06-13
2025-06-14 10:31:41,984 - INFO - Request Parameters - Page 1:
2025-06-14 10:31:41,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 10:31:41,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 10:31:42,625 - INFO - Response - Page 1:
2025-06-14 10:31:42,625 - INFO - 第 1 页获取到 50 条记录
2025-06-14 10:31:43,141 - INFO - Request Parameters - Page 2:
2025-06-14 10:31:43,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 10:31:43,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 10:31:43,781 - INFO - Response - Page 2:
2025-06-14 10:31:43,781 - INFO - 第 2 页获取到 50 条记录
2025-06-14 10:31:44,297 - INFO - Request Parameters - Page 3:
2025-06-14 10:31:44,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 10:31:44,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 10:31:45,063 - INFO - Response - Page 3:
2025-06-14 10:31:45,063 - INFO - 第 3 页获取到 50 条记录
2025-06-14 10:31:45,563 - INFO - Request Parameters - Page 4:
2025-06-14 10:31:45,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 10:31:45,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 10:31:46,156 - INFO - Response - Page 4:
2025-06-14 10:31:46,156 - INFO - 第 4 页获取到 26 条记录
2025-06-14 10:31:46,656 - INFO - 查询完成，共获取到 176 条记录
2025-06-14 10:31:46,656 - INFO - 获取到 176 条表单数据
2025-06-14 10:31:46,656 - INFO - 当前日期 2025-06-13 有 413 条MySQL数据需要处理
2025-06-14 10:31:46,656 - INFO - 开始批量插入 237 条新记录
2025-06-14 10:31:46,906 - INFO - 批量插入响应状态码: 200
2025-06-14 10:31:46,906 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 02:31:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BF0CDCCC-EFFA-7EDA-AD8C-A5099BB39168', 'x-acs-trace-id': '024d6c5bd1488a2b858bb18fed236384', 'etag': '2viFshJ4PAcbakv1MYp59EQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 10:31:46,906 - INFO - 批量插入响应体: {'result': ['FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMFD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMGD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMHD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMID', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMJD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMKD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMLD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMMD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMND', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMOD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMPD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMQD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMRD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMSD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMTD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMUD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMVD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMWD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMXD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMYD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMZD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM0E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM1E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM2E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM3E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM4E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM5E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM6E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM7E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM8E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM9E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMAE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMBE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMCE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMDE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMEE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMFE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMGE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMHE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMIE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMJE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMKE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMLE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMME', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMNE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMOE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMPE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMQE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMRE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMSE']}
2025-06-14 10:31:46,906 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-14 10:31:46,906 - INFO - 成功插入的数据ID: ['FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMFD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMGD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMHD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMID', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMJD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMKD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMLD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMMD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMND', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMOD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMPD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMQD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMRD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMSD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMTD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMUD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMVD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMWD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMXD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMYD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMZD', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM0E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM1E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM2E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM3E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM4E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM5E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM6E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM7E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM8E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBM9E', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMAE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMBE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMCE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMDE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMEE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMFE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMGE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMHE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMIE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMJE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMKE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMLE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMME', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMNE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMOE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMPE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMQE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMRE', 'FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMSE']
2025-06-14 10:31:52,125 - INFO - 批量插入响应状态码: 200
2025-06-14 10:31:52,125 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 02:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D853EE12-E54E-7483-9078-1C0F322DC6BC', 'x-acs-trace-id': 'fa50ca12bfaab48d1687e880431df77f', 'etag': '2dyZGPCIDzTOJiuvt4Qn4Tg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 10:31:52,125 - INFO - 批量插入响应体: {'result': ['FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMFH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMGH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMHH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMIH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMJH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMKH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMLH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMMH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMNH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMOH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMPH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMQH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMRH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMSH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMTH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMUH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMVH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMWH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMXH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMYH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMZH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM0I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM1I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM2I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM3I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM4I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM5I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM6I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM7I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM8I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM9I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMAI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMBI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMCI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMDI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMEI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMFI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMGI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMHI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMII', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMJI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMKI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMLI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMMI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMNI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMOI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMPI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMQI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMRI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMSI']}
2025-06-14 10:31:52,125 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-14 10:31:52,125 - INFO - 成功插入的数据ID: ['FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMFH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMGH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMHH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMIH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMJH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMKH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMLH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMMH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMNH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMOH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMPH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMQH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMRH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMSH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMTH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMUH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMVH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMWH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMXH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMYH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMZH', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM0I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM1I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM2I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM3I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM4I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM5I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM6I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM7I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM8I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBM9I', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMAI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMBI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMCI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMDI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMEI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMFI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMGI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMHI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMII', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMJI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMKI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMLI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMMI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMNI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMOI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMPI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMQI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMRI', 'FINST-U8966871YL7WGLF6B6GOOCEJGDZB2W5VEMVBMSI']
2025-06-14 10:31:57,359 - INFO - 批量插入响应状态码: 200
2025-06-14 10:31:57,359 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 02:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '97E84E91-2FB0-771A-8872-BDC10316FD7F', 'x-acs-trace-id': '242e2b84b371ec0e1bee4394fe9a7154', 'etag': '2DzJCdMLd3fQG/wmTQ688aQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 10:31:57,359 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMJ7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMK7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBML7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMM7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMN7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMO7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMP7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMQ7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMR7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMS7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMT7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMU7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMV7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMW7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMX7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMY7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMZ7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM08', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM18', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM28', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM38', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM48', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM58', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM68', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM78', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM88', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM98', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMA8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMB8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMC8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMD8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBME8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMF8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMG8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMH8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMI8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMJ8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMK8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBML8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMM8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMN8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMO8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMP8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMQ8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMR8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMS8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMT8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMU8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMV8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMW8']}
2025-06-14 10:31:57,359 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-14 10:31:57,359 - INFO - 成功插入的数据ID: ['FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMJ7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMK7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBML7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMM7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMN7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMO7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMP7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMQ7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMR7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMS7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMT7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMU7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMV7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMW7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMX7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMY7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW347ZEMVBMZ7', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM08', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM18', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM28', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM38', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM48', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM58', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM68', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM78', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM88', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBM98', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMA8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMB8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMC8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMD8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBME8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMF8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMG8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMH8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMI8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMJ8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMK8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBML8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMM8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMN8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMO8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMP8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMQ8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMR8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMS8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMT8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMU8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMV8', 'FINST-2PF66KD1AT9WFOKBFL5RKBAELDNW357ZEMVBMW8']
2025-06-14 10:32:02,594 - INFO - 批量插入响应状态码: 200
2025-06-14 10:32:02,594 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 02:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DDF324B6-A74A-7C6E-9D2E-5D3F72D3B7D8', 'x-acs-trace-id': 'c14c16dad0664f2f03b2f346d3a10b43', 'etag': '22vAxu7DB6IYOzqOBJJ1FLg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 10:32:02,594 - INFO - 批量插入响应体: {'result': ['FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMXG', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMYG', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMZG', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM0H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM1H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM2H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM3H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM4H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM5H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM6H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM7H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM8H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM9H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMAH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMBH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMCH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMDH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMEH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMFH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMGH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMHH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMIH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMJH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMKH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMLH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMMH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMNH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMOH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMPH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMQH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMRH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMSH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMTH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMUH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMVH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMWH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMXH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMYH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMZH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM0I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM1I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM2I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM3I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM4I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM5I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM6I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM7I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM8I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM9I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMAI']}
2025-06-14 10:32:02,594 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-14 10:32:02,594 - INFO - 成功插入的数据ID: ['FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMXG', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMYG', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMZG', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM0H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM1H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM2H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM3H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM4H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM5H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM6H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM7H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM8H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBM9H', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMAH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMBH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMCH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMDH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMEH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMFH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMGH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMHH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63F83FMVBMIH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMJH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMKH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMLH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMMH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMNH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMOH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMPH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMQH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMRH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMSH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMTH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMUH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMVH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMWH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMXH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMYH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMZH', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM0I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM1I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM2I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM3I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM4I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM5I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM6I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM7I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM8I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBM9I', 'FINST-Q7866DD1CM8W3NBB656DM5OQHNB63G83FMVBMAI']
2025-06-14 10:32:07,828 - INFO - 批量插入响应状态码: 200
2025-06-14 10:32:07,828 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 02:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1788', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8618AE52-0815-79AD-B548-0251E73BC57C', 'x-acs-trace-id': 'd0d08be2e306edfe137aee58deec7ce6', 'etag': '1CinSzVoT2iXlxXdNpm+mxA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 10:32:07,828 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMQC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMRC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMSC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMTC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMUC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMVC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMWC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMXC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMYC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMZC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM0D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM1D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM2D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM3D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM4D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM5D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM6D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM7D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM8D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM9D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMAD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMBD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMCD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMDD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMED', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMFD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMGD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMHD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMID', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMJD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMKD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMLD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMMD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMND', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMOD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMPD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMQD']}
2025-06-14 10:32:07,828 - INFO - 批量插入表单数据成功，批次 5，共 37 条记录
2025-06-14 10:32:07,828 - INFO - 成功插入的数据ID: ['FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMQC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMRC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMSC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMTC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMUC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMVC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMWC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMXC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMYC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMZC', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM0D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM1D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM2D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM3D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM4D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM5D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM6D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM7D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM8D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBM9D', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMAD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMBD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMCD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMDD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMED', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMFD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMGD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMHD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMID', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMJD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMKD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMLD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMMD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMND', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMOD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMPD', 'FINST-XMC66R91JH9W4NN1DBMXB5HCE5SP3Q97FMVBMQD']
2025-06-14 10:32:12,844 - INFO - 批量插入完成，共 237 条记录
2025-06-14 10:32:12,844 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 237 条，错误: 0 条
2025-06-14 10:32:12,844 - INFO - 数据同步完成！更新: 0 条，插入: 237 条，错误: 0 条
2025-06-14 10:32:12,844 - INFO - 同步完成
2025-06-14 13:30:33,569 - INFO - 使用默认增量同步（当天更新数据）
2025-06-14 13:30:33,569 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-14 13:30:33,569 - INFO - 查询参数: ('2025-06-14',)
2025-06-14 13:30:33,694 - INFO - MySQL查询成功，增量数据（日期: 2025-06-14），共获取 124 条记录
2025-06-14 13:30:33,694 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 13:30:33,694 - INFO - 开始处理日期: 2025-06-13
2025-06-14 13:30:33,694 - INFO - Request Parameters - Page 1:
2025-06-14 13:30:33,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:33,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:39,538 - INFO - Response - Page 1:
2025-06-14 13:30:39,538 - INFO - 第 1 页获取到 50 条记录
2025-06-14 13:30:40,053 - INFO - Request Parameters - Page 2:
2025-06-14 13:30:40,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:40,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:47,928 - INFO - Response - Page 2:
2025-06-14 13:30:47,928 - INFO - 第 2 页获取到 50 条记录
2025-06-14 13:30:48,444 - INFO - Request Parameters - Page 3:
2025-06-14 13:30:48,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:48,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:49,131 - INFO - Response - Page 3:
2025-06-14 13:30:49,131 - INFO - 第 3 页获取到 50 条记录
2025-06-14 13:30:49,631 - INFO - Request Parameters - Page 4:
2025-06-14 13:30:49,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:49,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:50,272 - INFO - Response - Page 4:
2025-06-14 13:30:50,272 - INFO - 第 4 页获取到 50 条记录
2025-06-14 13:30:50,788 - INFO - Request Parameters - Page 5:
2025-06-14 13:30:50,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:50,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:51,428 - INFO - Response - Page 5:
2025-06-14 13:30:51,428 - INFO - 第 5 页获取到 50 条记录
2025-06-14 13:30:51,928 - INFO - Request Parameters - Page 6:
2025-06-14 13:30:51,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:51,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:52,553 - INFO - Response - Page 6:
2025-06-14 13:30:52,553 - INFO - 第 6 页获取到 50 条记录
2025-06-14 13:30:53,053 - INFO - Request Parameters - Page 7:
2025-06-14 13:30:53,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:53,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:53,710 - INFO - Response - Page 7:
2025-06-14 13:30:53,710 - INFO - 第 7 页获取到 50 条记录
2025-06-14 13:30:54,225 - INFO - Request Parameters - Page 8:
2025-06-14 13:30:54,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:54,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:54,928 - INFO - Response - Page 8:
2025-06-14 13:30:54,928 - INFO - 第 8 页获取到 50 条记录
2025-06-14 13:30:55,444 - INFO - Request Parameters - Page 9:
2025-06-14 13:30:55,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:30:55,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:30:55,928 - INFO - Response - Page 9:
2025-06-14 13:30:55,928 - INFO - 第 9 页获取到 13 条记录
2025-06-14 13:30:56,444 - INFO - 查询完成，共获取到 413 条记录
2025-06-14 13:30:56,444 - INFO - 获取到 413 条表单数据
2025-06-14 13:30:56,444 - INFO - 当前日期 2025-06-13 有 123 条MySQL数据需要处理
2025-06-14 13:30:56,444 - INFO - 开始批量插入 14 条新记录
2025-06-14 13:30:56,616 - INFO - 批量插入响应状态码: 200
2025-06-14 13:30:56,616 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 05:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B4D114CD-AB3A-7435-9E4A-978F0CCA148C', 'x-acs-trace-id': 'd5597e454c3f2a481042691d54062c56', 'etag': '6SDxR10aQtsGEM8MeND3gyA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 13:30:56,616 - INFO - 批量插入响应体: {'result': ['FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMK1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBML1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMM1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMN1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMO1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMP1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMQ1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMR1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMS1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMT1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMU1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMV1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMW1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMX1']}
2025-06-14 13:30:56,616 - INFO - 批量插入表单数据成功，批次 1，共 14 条记录
2025-06-14 13:30:56,616 - INFO - 成功插入的数据ID: ['FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMK1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBML1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMM1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMN1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMO1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMP1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMQ1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMR1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMS1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMT1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMU1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMV1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMW1', 'FINST-HJ966H81XE8WU7C082MXW6MU5JGL3OP5TSVBMX1']
2025-06-14 13:31:01,631 - INFO - 批量插入完成，共 14 条记录
2025-06-14 13:31:01,631 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 14 条，错误: 0 条
2025-06-14 13:31:01,631 - INFO - 数据同步完成！更新: 0 条，插入: 14 条，错误: 0 条
2025-06-14 13:32:01,647 - INFO - 开始同步昨天与今天的销售数据: 2025-06-13 至 2025-06-14
2025-06-14 13:32:01,647 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-14 13:32:01,647 - INFO - 查询参数: ('2025-06-13', '2025-06-14')
2025-06-14 13:32:01,787 - INFO - MySQL查询成功，时间段: 2025-06-13 至 2025-06-14，共获取 480 条记录
2025-06-14 13:32:01,787 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 13:32:01,787 - INFO - 开始处理日期: 2025-06-13
2025-06-14 13:32:01,787 - INFO - Request Parameters - Page 1:
2025-06-14 13:32:01,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:01,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:02,443 - INFO - Response - Page 1:
2025-06-14 13:32:02,443 - INFO - 第 1 页获取到 50 条记录
2025-06-14 13:32:02,943 - INFO - Request Parameters - Page 2:
2025-06-14 13:32:02,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:02,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:03,600 - INFO - Response - Page 2:
2025-06-14 13:32:03,600 - INFO - 第 2 页获取到 50 条记录
2025-06-14 13:32:04,100 - INFO - Request Parameters - Page 3:
2025-06-14 13:32:04,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:04,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:04,725 - INFO - Response - Page 3:
2025-06-14 13:32:04,725 - INFO - 第 3 页获取到 50 条记录
2025-06-14 13:32:05,240 - INFO - Request Parameters - Page 4:
2025-06-14 13:32:05,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:05,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:05,834 - INFO - Response - Page 4:
2025-06-14 13:32:05,834 - INFO - 第 4 页获取到 50 条记录
2025-06-14 13:32:06,334 - INFO - Request Parameters - Page 5:
2025-06-14 13:32:06,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:06,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:06,959 - INFO - Response - Page 5:
2025-06-14 13:32:06,959 - INFO - 第 5 页获取到 50 条记录
2025-06-14 13:32:07,459 - INFO - Request Parameters - Page 6:
2025-06-14 13:32:07,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:07,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:08,115 - INFO - Response - Page 6:
2025-06-14 13:32:08,115 - INFO - 第 6 页获取到 50 条记录
2025-06-14 13:32:08,631 - INFO - Request Parameters - Page 7:
2025-06-14 13:32:08,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:08,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:09,334 - INFO - Response - Page 7:
2025-06-14 13:32:09,334 - INFO - 第 7 页获取到 50 条记录
2025-06-14 13:32:09,834 - INFO - Request Parameters - Page 8:
2025-06-14 13:32:09,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:09,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:10,459 - INFO - Response - Page 8:
2025-06-14 13:32:10,459 - INFO - 第 8 页获取到 50 条记录
2025-06-14 13:32:10,975 - INFO - Request Parameters - Page 9:
2025-06-14 13:32:10,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 13:32:10,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 13:32:11,584 - INFO - Response - Page 9:
2025-06-14 13:32:11,584 - INFO - 第 9 页获取到 27 条记录
2025-06-14 13:32:12,084 - INFO - 查询完成，共获取到 427 条记录
2025-06-14 13:32:12,084 - INFO - 获取到 427 条表单数据
2025-06-14 13:32:12,084 - INFO - 当前日期 2025-06-13 有 467 条MySQL数据需要处理
2025-06-14 13:32:12,100 - INFO - 开始批量插入 40 条新记录
2025-06-14 13:32:12,350 - INFO - 批量插入响应状态码: 200
2025-06-14 13:32:12,350 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 05:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1910', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '90609F02-0DFA-7CE8-9866-63D700CAA46B', 'x-acs-trace-id': '633156b2ddbf70932e7294f036ab5c2c', 'etag': '1qw5G1ZDLgfzjA4MoGnelyQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 13:32:12,350 - INFO - 批量插入响应体: {'result': ['FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBME', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMF', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMG', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMH', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMI', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMJ', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMK', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBML', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMM', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMN', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMO', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMP', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMQ', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMR', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMS', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMT', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMU', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMV', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMW', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMX', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMY', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMZ', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM01', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM11', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM21', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM31', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM41', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM51', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM61', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM71', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM81', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM91', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMA1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMB1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMC1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMD1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBME1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMF1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMG1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMH1']}
2025-06-14 13:32:12,350 - INFO - 批量插入表单数据成功，批次 1，共 40 条记录
2025-06-14 13:32:12,350 - INFO - 成功插入的数据ID: ['FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBME', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMF', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMG', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMH', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMI', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMJ', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMK', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBML', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMM', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMN', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMO', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMP', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMQ', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMR', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMS', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMT', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMU', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMV', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMW', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMX', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMY', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMZ', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM01', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM11', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM21', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM31', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM41', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM51', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM61', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM71', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM81', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBM91', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMA1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMB1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMC1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMD1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBME1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMF1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMG1', 'FINST-X3E66X818T9WMS3DCF56YCKZO60R3L5SUSVBMH1']
2025-06-14 13:32:17,365 - INFO - 批量插入完成，共 40 条记录
2025-06-14 13:32:17,365 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 40 条，错误: 0 条
2025-06-14 13:32:17,365 - INFO - 数据同步完成！更新: 0 条，插入: 40 条，错误: 0 条
2025-06-14 13:32:17,365 - INFO - 同步完成
2025-06-14 16:30:33,591 - INFO - 使用默认增量同步（当天更新数据）
2025-06-14 16:30:33,591 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-14 16:30:33,591 - INFO - 查询参数: ('2025-06-14',)
2025-06-14 16:30:33,716 - INFO - MySQL查询成功，增量数据（日期: 2025-06-14），共获取 132 条记录
2025-06-14 16:30:33,716 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 16:30:33,716 - INFO - 开始处理日期: 2025-06-13
2025-06-14 16:30:33,716 - INFO - Request Parameters - Page 1:
2025-06-14 16:30:33,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:30:33,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:30:39,169 - INFO - Response - Page 1:
2025-06-14 16:30:39,169 - INFO - 第 1 页获取到 50 条记录
2025-06-14 16:30:39,669 - INFO - Request Parameters - Page 2:
2025-06-14 16:30:39,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:30:39,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:30:47,794 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7DBB23A0-CFC4-7FC4-A68B-F58758050ED1 Response: {'code': 'ServiceUnavailable', 'requestid': '7DBB23A0-CFC4-7FC4-A68B-F58758050ED1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7DBB23A0-CFC4-7FC4-A68B-F58758050ED1)
2025-06-14 16:30:47,794 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-14 16:31:47,809 - INFO - 开始同步昨天与今天的销售数据: 2025-06-13 至 2025-06-14
2025-06-14 16:31:47,809 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-14 16:31:47,809 - INFO - 查询参数: ('2025-06-13', '2025-06-14')
2025-06-14 16:31:47,934 - INFO - MySQL查询成功，时间段: 2025-06-13 至 2025-06-14，共获取 488 条记录
2025-06-14 16:31:47,934 - INFO - 获取到 1 个日期需要处理: ['2025-06-13']
2025-06-14 16:31:47,949 - INFO - 开始处理日期: 2025-06-13
2025-06-14 16:31:47,949 - INFO - Request Parameters - Page 1:
2025-06-14 16:31:47,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:47,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:49,434 - INFO - Response - Page 1:
2025-06-14 16:31:49,434 - INFO - 第 1 页获取到 50 条记录
2025-06-14 16:31:49,949 - INFO - Request Parameters - Page 2:
2025-06-14 16:31:49,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:49,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:50,606 - INFO - Response - Page 2:
2025-06-14 16:31:50,606 - INFO - 第 2 页获取到 50 条记录
2025-06-14 16:31:51,121 - INFO - Request Parameters - Page 3:
2025-06-14 16:31:51,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:51,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:51,746 - INFO - Response - Page 3:
2025-06-14 16:31:51,746 - INFO - 第 3 页获取到 50 条记录
2025-06-14 16:31:52,262 - INFO - Request Parameters - Page 4:
2025-06-14 16:31:52,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:52,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:52,981 - INFO - Response - Page 4:
2025-06-14 16:31:52,981 - INFO - 第 4 页获取到 50 条记录
2025-06-14 16:31:53,496 - INFO - Request Parameters - Page 5:
2025-06-14 16:31:53,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:53,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:54,121 - INFO - Response - Page 5:
2025-06-14 16:31:54,121 - INFO - 第 5 页获取到 50 条记录
2025-06-14 16:31:54,621 - INFO - Request Parameters - Page 6:
2025-06-14 16:31:54,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:54,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:55,262 - INFO - Response - Page 6:
2025-06-14 16:31:55,262 - INFO - 第 6 页获取到 50 条记录
2025-06-14 16:31:55,777 - INFO - Request Parameters - Page 7:
2025-06-14 16:31:55,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:55,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:56,434 - INFO - Response - Page 7:
2025-06-14 16:31:56,434 - INFO - 第 7 页获取到 50 条记录
2025-06-14 16:31:56,949 - INFO - Request Parameters - Page 8:
2025-06-14 16:31:56,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:56,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:57,637 - INFO - Response - Page 8:
2025-06-14 16:31:57,637 - INFO - 第 8 页获取到 50 条记录
2025-06-14 16:31:58,137 - INFO - Request Parameters - Page 9:
2025-06-14 16:31:58,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:58,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:58,871 - INFO - Response - Page 9:
2025-06-14 16:31:58,871 - INFO - 第 9 页获取到 50 条记录
2025-06-14 16:31:59,371 - INFO - Request Parameters - Page 10:
2025-06-14 16:31:59,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 16:31:59,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 16:31:59,918 - INFO - Response - Page 10:
2025-06-14 16:31:59,918 - INFO - 第 10 页获取到 17 条记录
2025-06-14 16:32:00,434 - INFO - 查询完成，共获取到 467 条记录
2025-06-14 16:32:00,434 - INFO - 获取到 467 条表单数据
2025-06-14 16:32:00,434 - INFO - 当前日期 2025-06-13 有 474 条MySQL数据需要处理
2025-06-14 16:32:00,465 - INFO - 开始批量插入 7 条新记录
2025-06-14 16:32:00,637 - INFO - 批量插入响应状态码: 200
2025-06-14 16:32:00,637 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 08:32:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '868378E9-55B7-7786-90F7-AA8851DD9EAF', 'x-acs-trace-id': '0fb4c3c9b6e995bdbfb88a55a74c4d12', 'etag': '3Ocy7tMk5dus0/vt7xD2+rg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 16:32:00,637 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBM7J', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBM8J', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBM9J', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBMAJ', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBMBJ', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBMCJ', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBMDJ']}
2025-06-14 16:32:00,637 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-06-14 16:32:00,637 - INFO - 成功插入的数据ID: ['FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBM7J', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBM8J', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBM9J', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBMAJ', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBMBJ', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBMCJ', 'FINST-AAG66KB11O6WBTRMDF0GO471WWSU25I0AZVBMDJ']
2025-06-14 16:32:05,652 - INFO - 批量插入完成，共 7 条记录
2025-06-14 16:32:05,652 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-06-14 16:32:05,652 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 0 条
2025-06-14 16:32:05,652 - INFO - 同步完成
2025-06-14 19:30:34,147 - INFO - 使用默认增量同步（当天更新数据）
2025-06-14 19:30:34,147 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-14 19:30:34,147 - INFO - 查询参数: ('2025-06-14',)
2025-06-14 19:30:34,272 - INFO - MySQL查询成功，增量数据（日期: 2025-06-14），共获取 133 条记录
2025-06-14 19:30:34,272 - INFO - 获取到 2 个日期需要处理: ['2025-06-13', '2025-06-14']
2025-06-14 19:30:34,288 - INFO - 开始处理日期: 2025-06-13
2025-06-14 19:30:34,288 - INFO - Request Parameters - Page 1:
2025-06-14 19:30:34,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:30:34,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:30:42,396 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4E4DEFE3-D38F-7186-A7B9-70B5738FCB8A Response: {'code': 'ServiceUnavailable', 'requestid': '4E4DEFE3-D38F-7186-A7B9-70B5738FCB8A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4E4DEFE3-D38F-7186-A7B9-70B5738FCB8A)
2025-06-14 19:30:42,396 - INFO - 开始处理日期: 2025-06-14
2025-06-14 19:30:42,396 - INFO - Request Parameters - Page 1:
2025-06-14 19:30:42,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:30:42,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:30:47,771 - INFO - Response - Page 1:
2025-06-14 19:30:47,771 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 19:30:47,771 - INFO - 获取到 0 条表单数据
2025-06-14 19:30:47,771 - INFO - 当前日期 2025-06-14 有 1 条MySQL数据需要处理
2025-06-14 19:30:47,771 - INFO - 开始批量插入 1 条新记录
2025-06-14 19:30:48,005 - INFO - 批量插入响应状态码: 200
2025-06-14 19:30:48,005 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 11:30:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C97B177B-2F27-7351-9A90-1CA4DBCAB1FC', 'x-acs-trace-id': '308e1ad50eecbc345ae3a224d5dbc4ae', 'etag': '6vyAc40UoC7Us6b7mN106Jg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 19:30:48,005 - INFO - 批量插入响应体: {'result': ['FINST-1OC66A910P7W5JOGCLV0CDAW19VJ3IGXN5WBMKV']}
2025-06-14 19:30:48,005 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-14 19:30:48,005 - INFO - 成功插入的数据ID: ['FINST-1OC66A910P7W5JOGCLV0CDAW19VJ3IGXN5WBMKV']
2025-06-14 19:30:53,020 - INFO - 批量插入完成，共 1 条记录
2025-06-14 19:30:53,020 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-14 19:30:53,020 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-06-14 19:31:53,030 - INFO - 开始同步昨天与今天的销售数据: 2025-06-13 至 2025-06-14
2025-06-14 19:31:53,030 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-14 19:31:53,030 - INFO - 查询参数: ('2025-06-13', '2025-06-14')
2025-06-14 19:31:53,155 - INFO - MySQL查询成功，时间段: 2025-06-13 至 2025-06-14，共获取 489 条记录
2025-06-14 19:31:53,155 - INFO - 获取到 2 个日期需要处理: ['2025-06-13', '2025-06-14']
2025-06-14 19:31:53,171 - INFO - 开始处理日期: 2025-06-13
2025-06-14 19:31:53,171 - INFO - Request Parameters - Page 1:
2025-06-14 19:31:53,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:31:53,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:31:53,968 - INFO - Response - Page 1:
2025-06-14 19:31:53,968 - INFO - 第 1 页获取到 50 条记录
2025-06-14 19:31:54,468 - INFO - Request Parameters - Page 2:
2025-06-14 19:31:54,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:31:54,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:31:55,155 - INFO - Response - Page 2:
2025-06-14 19:31:55,155 - INFO - 第 2 页获取到 50 条记录
2025-06-14 19:31:55,671 - INFO - Request Parameters - Page 3:
2025-06-14 19:31:55,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:31:55,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:31:56,452 - INFO - Response - Page 3:
2025-06-14 19:31:56,452 - INFO - 第 3 页获取到 50 条记录
2025-06-14 19:31:56,967 - INFO - Request Parameters - Page 4:
2025-06-14 19:31:56,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:31:56,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:31:57,608 - INFO - Response - Page 4:
2025-06-14 19:31:57,608 - INFO - 第 4 页获取到 50 条记录
2025-06-14 19:31:58,123 - INFO - Request Parameters - Page 5:
2025-06-14 19:31:58,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:31:58,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:31:58,842 - INFO - Response - Page 5:
2025-06-14 19:31:58,842 - INFO - 第 5 页获取到 50 条记录
2025-06-14 19:31:59,358 - INFO - Request Parameters - Page 6:
2025-06-14 19:31:59,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:31:59,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:31:59,983 - INFO - Response - Page 6:
2025-06-14 19:31:59,983 - INFO - 第 6 页获取到 50 条记录
2025-06-14 19:32:00,498 - INFO - Request Parameters - Page 7:
2025-06-14 19:32:00,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:32:00,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:32:01,123 - INFO - Response - Page 7:
2025-06-14 19:32:01,123 - INFO - 第 7 页获取到 50 条记录
2025-06-14 19:32:01,623 - INFO - Request Parameters - Page 8:
2025-06-14 19:32:01,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:32:01,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:32:02,545 - INFO - Response - Page 8:
2025-06-14 19:32:02,545 - INFO - 第 8 页获取到 50 条记录
2025-06-14 19:32:03,061 - INFO - Request Parameters - Page 9:
2025-06-14 19:32:03,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:32:03,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:32:03,748 - INFO - Response - Page 9:
2025-06-14 19:32:03,748 - INFO - 第 9 页获取到 50 条记录
2025-06-14 19:32:04,248 - INFO - Request Parameters - Page 10:
2025-06-14 19:32:04,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:32:04,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:32:04,857 - INFO - Response - Page 10:
2025-06-14 19:32:04,857 - INFO - 第 10 页获取到 24 条记录
2025-06-14 19:32:05,373 - INFO - 查询完成，共获取到 474 条记录
2025-06-14 19:32:05,373 - INFO - 获取到 474 条表单数据
2025-06-14 19:32:05,373 - INFO - 当前日期 2025-06-13 有 474 条MySQL数据需要处理
2025-06-14 19:32:05,388 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 19:32:05,388 - INFO - 开始处理日期: 2025-06-14
2025-06-14 19:32:05,388 - INFO - Request Parameters - Page 1:
2025-06-14 19:32:05,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 19:32:05,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 19:32:05,951 - INFO - Response - Page 1:
2025-06-14 19:32:05,951 - INFO - 第 1 页获取到 1 条记录
2025-06-14 19:32:06,451 - INFO - 查询完成，共获取到 1 条记录
2025-06-14 19:32:06,451 - INFO - 获取到 1 条表单数据
2025-06-14 19:32:06,451 - INFO - 当前日期 2025-06-14 有 1 条MySQL数据需要处理
2025-06-14 19:32:06,451 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 19:32:06,451 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 19:32:06,451 - INFO - 同步完成
2025-06-14 22:30:33,311 - INFO - 使用默认增量同步（当天更新数据）
2025-06-14 22:30:33,311 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-14 22:30:33,311 - INFO - 查询参数: ('2025-06-14',)
2025-06-14 22:30:33,452 - INFO - MySQL查询成功，增量数据（日期: 2025-06-14），共获取 274 条记录
2025-06-14 22:30:33,452 - INFO - 获取到 33 个日期需要处理: ['2025-05-01', '2025-05-02', '2025-05-03', '2025-05-04', '2025-05-05', '2025-05-06', '2025-05-07', '2025-05-08', '2025-05-09', '2025-05-10', '2025-05-11', '2025-05-12', '2025-05-13', '2025-05-14', '2025-05-15', '2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20', '2025-05-21', '2025-05-22', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26', '2025-05-27', '2025-05-28', '2025-05-29', '2025-05-30', '2025-05-31', '2025-06-13', '2025-06-14']
2025-06-14 22:30:33,452 - INFO - 开始处理日期: 2025-05-01
2025-06-14 22:30:33,452 - INFO - Request Parameters - Page 1:
2025-06-14 22:30:33,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:33,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:41,576 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 811E9B7F-D1F7-7F53-BF78-DCC3B1B48E94 Response: {'code': 'ServiceUnavailable', 'requestid': '811E9B7F-D1F7-7F53-BF78-DCC3B1B48E94', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 811E9B7F-D1F7-7F53-BF78-DCC3B1B48E94)
2025-06-14 22:30:41,576 - INFO - 开始处理日期: 2025-05-02
2025-06-14 22:30:41,576 - INFO - Request Parameters - Page 1:
2025-06-14 22:30:41,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:41,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:42,216 - INFO - Response - Page 1:
2025-06-14 22:30:42,216 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:30:42,732 - INFO - Request Parameters - Page 2:
2025-06-14 22:30:42,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:42,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:43,388 - INFO - Response - Page 2:
2025-06-14 22:30:43,388 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:30:43,904 - INFO - Request Parameters - Page 3:
2025-06-14 22:30:43,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:43,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:49,606 - INFO - Response - Page 3:
2025-06-14 22:30:49,606 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:30:50,106 - INFO - Request Parameters - Page 4:
2025-06-14 22:30:50,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:50,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:50,809 - INFO - Response - Page 4:
2025-06-14 22:30:50,809 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:30:51,325 - INFO - Request Parameters - Page 5:
2025-06-14 22:30:51,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:51,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:51,950 - INFO - Response - Page 5:
2025-06-14 22:30:51,950 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:30:52,465 - INFO - Request Parameters - Page 6:
2025-06-14 22:30:52,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:52,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:53,153 - INFO - Response - Page 6:
2025-06-14 22:30:53,153 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:30:53,653 - INFO - Request Parameters - Page 7:
2025-06-14 22:30:53,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:53,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:54,278 - INFO - Response - Page 7:
2025-06-14 22:30:54,278 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:30:54,778 - INFO - Request Parameters - Page 8:
2025-06-14 22:30:54,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:54,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:55,449 - INFO - Response - Page 8:
2025-06-14 22:30:55,449 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:30:55,965 - INFO - Request Parameters - Page 9:
2025-06-14 22:30:55,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:55,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:56,606 - INFO - Response - Page 9:
2025-06-14 22:30:56,606 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:30:57,121 - INFO - Request Parameters - Page 10:
2025-06-14 22:30:57,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:57,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:57,762 - INFO - Response - Page 10:
2025-06-14 22:30:57,762 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:30:58,262 - INFO - Request Parameters - Page 11:
2025-06-14 22:30:58,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:58,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:30:58,933 - INFO - Response - Page 11:
2025-06-14 22:30:58,933 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:30:59,449 - INFO - Request Parameters - Page 12:
2025-06-14 22:30:59,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:30:59,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:00,074 - INFO - Response - Page 12:
2025-06-14 22:31:00,074 - INFO - 第 12 页获取到 50 条记录
2025-06-14 22:31:00,574 - INFO - Request Parameters - Page 13:
2025-06-14 22:31:00,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:00,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 13, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:01,105 - INFO - Response - Page 13:
2025-06-14 22:31:01,105 - INFO - 第 13 页获取到 10 条记录
2025-06-14 22:31:01,605 - INFO - 查询完成，共获取到 610 条记录
2025-06-14 22:31:01,605 - INFO - 获取到 610 条表单数据
2025-06-14 22:31:01,605 - INFO - 当前日期 2025-05-02 有 3 条MySQL数据需要处理
2025-06-14 22:31:01,605 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91MV2WZZV1BU3Q248OIR7X2IJW8WOBMAQ
2025-06-14 22:31:02,074 - INFO - 更新表单数据成功: FINST-VOC66Y91MV2WZZV1BU3Q248OIR7X2IJW8WOBMAQ
2025-06-14 22:31:02,074 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10659.42, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4718.0, 'new_value': 18657.42}, {'field': 'total_amount', 'old_value': 15377.42, 'new_value': 18657.42}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:02,074 - INFO - 开始更新记录 - 表单实例ID: FINST-PGC66MB1CQ5WMXM1F1RYNB367XWY29P89WOBMA
2025-06-14 22:31:02,511 - INFO - 更新表单数据成功: FINST-PGC66MB1CQ5WMXM1F1RYNB367XWY29P89WOBMA
2025-06-14 22:31:02,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2376.7, 'new_value': 2410.7}, {'field': 'total_amount', 'old_value': 2376.7, 'new_value': 2410.7}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:02,511 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1H64WBVGIATF9FDNO14ZB3YTG9WOBMBM
2025-06-14 22:31:03,011 - INFO - 更新表单数据成功: FINST-GRA66IC1H64WBVGIATF9FDNO14ZB3YTG9WOBMBM
2025-06-14 22:31:03,011 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 557.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 11619.0, 'new_value': 12176.37}, {'field': 'total_amount', 'old_value': 12176.0, 'new_value': 12176.37}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:03,011 - INFO - 日期 2025-05-02 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:31:03,011 - INFO - 开始处理日期: 2025-05-03
2025-06-14 22:31:03,011 - INFO - Request Parameters - Page 1:
2025-06-14 22:31:03,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:03,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:03,745 - INFO - Response - Page 1:
2025-06-14 22:31:03,745 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:31:04,245 - INFO - Request Parameters - Page 2:
2025-06-14 22:31:04,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:04,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:04,964 - INFO - Response - Page 2:
2025-06-14 22:31:04,964 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:31:05,464 - INFO - Request Parameters - Page 3:
2025-06-14 22:31:05,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:05,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:06,089 - INFO - Response - Page 3:
2025-06-14 22:31:06,089 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:31:06,605 - INFO - Request Parameters - Page 4:
2025-06-14 22:31:06,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:06,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:07,370 - INFO - Response - Page 4:
2025-06-14 22:31:07,370 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:31:07,870 - INFO - Request Parameters - Page 5:
2025-06-14 22:31:07,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:07,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:08,479 - INFO - Response - Page 5:
2025-06-14 22:31:08,479 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:31:08,995 - INFO - Request Parameters - Page 6:
2025-06-14 22:31:08,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:08,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:09,651 - INFO - Response - Page 6:
2025-06-14 22:31:09,651 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:31:10,167 - INFO - Request Parameters - Page 7:
2025-06-14 22:31:10,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:10,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:10,979 - INFO - Response - Page 7:
2025-06-14 22:31:10,979 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:31:11,479 - INFO - Request Parameters - Page 8:
2025-06-14 22:31:11,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:11,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:12,120 - INFO - Response - Page 8:
2025-06-14 22:31:12,120 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:31:12,635 - INFO - Request Parameters - Page 9:
2025-06-14 22:31:12,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:12,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:13,354 - INFO - Response - Page 9:
2025-06-14 22:31:13,354 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:31:13,870 - INFO - Request Parameters - Page 10:
2025-06-14 22:31:13,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:13,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:14,557 - INFO - Response - Page 10:
2025-06-14 22:31:14,557 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:31:15,073 - INFO - Request Parameters - Page 11:
2025-06-14 22:31:15,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:15,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:15,697 - INFO - Response - Page 11:
2025-06-14 22:31:15,697 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:31:16,213 - INFO - Request Parameters - Page 12:
2025-06-14 22:31:16,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:16,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:16,775 - INFO - Response - Page 12:
2025-06-14 22:31:16,775 - INFO - 第 12 页获取到 24 条记录
2025-06-14 22:31:17,291 - INFO - 查询完成，共获取到 574 条记录
2025-06-14 22:31:17,291 - INFO - 获取到 574 条表单数据
2025-06-14 22:31:17,291 - INFO - 当前日期 2025-05-03 有 3 条MySQL数据需要处理
2025-06-14 22:31:17,291 - INFO - 开始更新记录 - 表单实例ID: FINST-ORA66F81UM5WZNAT77PAA7JS491W3KBT9WOBM73
2025-06-14 22:31:17,697 - INFO - 更新表单数据成功: FINST-ORA66F81UM5WZNAT77PAA7JS491W3KBT9WOBM73
2025-06-14 22:31:17,697 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1874.33, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 23608.21, 'new_value': 25482.54}, {'field': 'total_amount', 'old_value': 23608.21, 'new_value': 25482.54}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:17,697 - INFO - 开始更新记录 - 表单实例ID: FINST-I6E66WA1TR4WZYQQCKR7Z56WECED2ICX9WOBM4Y
2025-06-14 22:31:18,135 - INFO - 更新表单数据成功: FINST-I6E66WA1TR4WZYQQCKR7Z56WECED2ICX9WOBM4Y
2025-06-14 22:31:18,135 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6841.86, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 12136.45, 'new_value': 23459.16}, {'field': 'total_amount', 'old_value': 18978.31, 'new_value': 23459.16}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:18,135 - INFO - 开始更新记录 - 表单实例ID: FINST-DUF66091VO5WXMLXEE40ODJR0TVE3UIDAWOBMJ2
2025-06-14 22:31:18,572 - INFO - 更新表单数据成功: FINST-DUF66091VO5WXMLXEE40ODJR0TVE3UIDAWOBMJ2
2025-06-14 22:31:18,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1344.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 11646.0, 'new_value': 12990.97}, {'field': 'total_amount', 'old_value': 12990.0, 'new_value': 12990.97}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:18,572 - INFO - 日期 2025-05-03 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:31:18,572 - INFO - 开始处理日期: 2025-05-04
2025-06-14 22:31:18,572 - INFO - Request Parameters - Page 1:
2025-06-14 22:31:18,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:18,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:19,228 - INFO - Response - Page 1:
2025-06-14 22:31:19,228 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:31:19,744 - INFO - Request Parameters - Page 2:
2025-06-14 22:31:19,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:19,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:20,369 - INFO - Response - Page 2:
2025-06-14 22:31:20,369 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:31:20,869 - INFO - Request Parameters - Page 3:
2025-06-14 22:31:20,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:20,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:21,478 - INFO - Response - Page 3:
2025-06-14 22:31:21,478 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:31:21,994 - INFO - Request Parameters - Page 4:
2025-06-14 22:31:21,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:21,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:22,634 - INFO - Response - Page 4:
2025-06-14 22:31:22,634 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:31:23,150 - INFO - Request Parameters - Page 5:
2025-06-14 22:31:23,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:23,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:23,806 - INFO - Response - Page 5:
2025-06-14 22:31:23,806 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:31:24,322 - INFO - Request Parameters - Page 6:
2025-06-14 22:31:24,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:24,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:24,993 - INFO - Response - Page 6:
2025-06-14 22:31:24,993 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:31:25,509 - INFO - Request Parameters - Page 7:
2025-06-14 22:31:25,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:25,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:26,212 - INFO - Response - Page 7:
2025-06-14 22:31:26,212 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:31:26,712 - INFO - Request Parameters - Page 8:
2025-06-14 22:31:26,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:26,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:27,384 - INFO - Response - Page 8:
2025-06-14 22:31:27,384 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:31:27,899 - INFO - Request Parameters - Page 9:
2025-06-14 22:31:27,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:27,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:28,524 - INFO - Response - Page 9:
2025-06-14 22:31:28,524 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:31:29,024 - INFO - Request Parameters - Page 10:
2025-06-14 22:31:29,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:29,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:29,696 - INFO - Response - Page 10:
2025-06-14 22:31:29,696 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:31:30,212 - INFO - Request Parameters - Page 11:
2025-06-14 22:31:30,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:30,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:30,805 - INFO - Response - Page 11:
2025-06-14 22:31:30,805 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:31:31,321 - INFO - Request Parameters - Page 12:
2025-06-14 22:31:31,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:31,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:31,946 - INFO - Response - Page 12:
2025-06-14 22:31:31,946 - INFO - 第 12 页获取到 31 条记录
2025-06-14 22:31:32,461 - INFO - 查询完成，共获取到 581 条记录
2025-06-14 22:31:32,461 - INFO - 获取到 581 条表单数据
2025-06-14 22:31:32,461 - INFO - 当前日期 2025-05-04 有 2 条MySQL数据需要处理
2025-06-14 22:31:32,461 - INFO - 开始更新记录 - 表单实例ID: FINST-DUF66091SN5WUMGV87WPL5IAOKPU2VBABWOBMN3
2025-06-14 22:31:32,930 - INFO - 更新表单数据成功: FINST-DUF66091SN5WUMGV87WPL5IAOKPU2VBABWOBMN3
2025-06-14 22:31:32,930 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7133.85, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 6771.0, 'new_value': 17582.85}, {'field': 'total_amount', 'old_value': 13904.85, 'new_value': 17582.85}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:32,930 - INFO - 开始更新记录 - 表单实例ID: FINST-YPE66RB1WC5WCKSQCW29OBK5G0MS2SKUBWOBMV3
2025-06-14 22:31:33,446 - INFO - 更新表单数据成功: FINST-YPE66RB1WC5WCKSQCW29OBK5G0MS2SKUBWOBMV3
2025-06-14 22:31:33,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 572.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 11631.0, 'new_value': 12204.37}, {'field': 'total_amount', 'old_value': 12203.0, 'new_value': 12204.37}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:33,446 - INFO - 日期 2025-05-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-06-14 22:31:33,446 - INFO - 开始处理日期: 2025-05-05
2025-06-14 22:31:33,446 - INFO - Request Parameters - Page 1:
2025-06-14 22:31:33,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:33,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:34,071 - INFO - Response - Page 1:
2025-06-14 22:31:34,071 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:31:34,586 - INFO - Request Parameters - Page 2:
2025-06-14 22:31:34,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:34,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:35,258 - INFO - Response - Page 2:
2025-06-14 22:31:35,258 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:31:35,774 - INFO - Request Parameters - Page 3:
2025-06-14 22:31:35,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:35,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:36,414 - INFO - Response - Page 3:
2025-06-14 22:31:36,414 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:31:36,930 - INFO - Request Parameters - Page 4:
2025-06-14 22:31:36,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:36,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:37,570 - INFO - Response - Page 4:
2025-06-14 22:31:37,570 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:31:38,086 - INFO - Request Parameters - Page 5:
2025-06-14 22:31:38,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:38,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:38,742 - INFO - Response - Page 5:
2025-06-14 22:31:38,742 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:31:39,242 - INFO - Request Parameters - Page 6:
2025-06-14 22:31:39,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:39,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:39,851 - INFO - Response - Page 6:
2025-06-14 22:31:39,851 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:31:40,367 - INFO - Request Parameters - Page 7:
2025-06-14 22:31:40,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:40,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:41,070 - INFO - Response - Page 7:
2025-06-14 22:31:41,070 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:31:41,570 - INFO - Request Parameters - Page 8:
2025-06-14 22:31:41,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:41,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:42,211 - INFO - Response - Page 8:
2025-06-14 22:31:42,211 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:31:42,726 - INFO - Request Parameters - Page 9:
2025-06-14 22:31:42,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:42,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:43,429 - INFO - Response - Page 9:
2025-06-14 22:31:43,429 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:31:43,945 - INFO - Request Parameters - Page 10:
2025-06-14 22:31:43,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:43,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:44,538 - INFO - Response - Page 10:
2025-06-14 22:31:44,538 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:31:45,038 - INFO - Request Parameters - Page 11:
2025-06-14 22:31:45,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:45,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:45,695 - INFO - Response - Page 11:
2025-06-14 22:31:45,695 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:31:46,195 - INFO - Request Parameters - Page 12:
2025-06-14 22:31:46,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:46,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:46,898 - INFO - Response - Page 12:
2025-06-14 22:31:46,898 - INFO - 第 12 页获取到 50 条记录
2025-06-14 22:31:47,413 - INFO - Request Parameters - Page 13:
2025-06-14 22:31:47,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:47,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 13, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:47,913 - INFO - Response - Page 13:
2025-06-14 22:31:47,913 - INFO - 第 13 页获取到 12 条记录
2025-06-14 22:31:48,429 - INFO - 查询完成，共获取到 612 条记录
2025-06-14 22:31:48,429 - INFO - 获取到 612 条表单数据
2025-06-14 22:31:48,429 - INFO - 当前日期 2025-05-05 有 3 条MySQL数据需要处理
2025-06-14 22:31:48,429 - INFO - 开始更新记录 - 表单实例ID: FINST-SI766181XP5WD90RCHGDKC6AX6YX23LPCWOBMB1
2025-06-14 22:31:48,866 - INFO - 更新表单数据成功: FINST-SI766181XP5WD90RCHGDKC6AX6YX23LPCWOBMB1
2025-06-14 22:31:48,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6203.98, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 8557.0, 'new_value': 17180.98}, {'field': 'total_amount', 'old_value': 14760.98, 'new_value': 17180.98}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:48,866 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071TB2W8QQ6A5BCC95W1V872ZZDDWOBMOX
2025-06-14 22:31:49,272 - INFO - 更新表单数据成功: FINST-XBF66071TB2W8QQ6A5BCC95W1V872ZZDDWOBMOX
2025-06-14 22:31:49,272 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 668.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 12339.0, 'new_value': 13007.9}, {'field': 'total_amount', 'old_value': 13007.0, 'new_value': 13007.9}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:49,272 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81WO5W5DICEG17G47YY53H3B5MDWOBM93
2025-06-14 22:31:49,741 - INFO - 更新表单数据成功: FINST-NU966I81WO5W5DICEG17G47YY53H3B5MDWOBM93
2025-06-14 22:31:49,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1783.0, 'new_value': 1713.6}, {'field': 'total_amount', 'old_value': 1783.0, 'new_value': 1713.6}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:31:49,741 - INFO - 日期 2025-05-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:31:49,741 - INFO - 开始处理日期: 2025-05-06
2025-06-14 22:31:49,741 - INFO - Request Parameters - Page 1:
2025-06-14 22:31:49,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:49,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:50,382 - INFO - Response - Page 1:
2025-06-14 22:31:50,382 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:31:50,897 - INFO - Request Parameters - Page 2:
2025-06-14 22:31:50,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:50,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:51,631 - INFO - Response - Page 2:
2025-06-14 22:31:51,631 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:31:52,131 - INFO - Request Parameters - Page 3:
2025-06-14 22:31:52,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:52,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:52,772 - INFO - Response - Page 3:
2025-06-14 22:31:52,772 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:31:53,288 - INFO - Request Parameters - Page 4:
2025-06-14 22:31:53,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:53,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:53,913 - INFO - Response - Page 4:
2025-06-14 22:31:53,913 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:31:54,428 - INFO - Request Parameters - Page 5:
2025-06-14 22:31:54,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:54,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:55,178 - INFO - Response - Page 5:
2025-06-14 22:31:55,178 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:31:55,678 - INFO - Request Parameters - Page 6:
2025-06-14 22:31:55,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:55,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:56,319 - INFO - Response - Page 6:
2025-06-14 22:31:56,319 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:31:56,819 - INFO - Request Parameters - Page 7:
2025-06-14 22:31:56,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:56,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:57,459 - INFO - Response - Page 7:
2025-06-14 22:31:57,459 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:31:57,959 - INFO - Request Parameters - Page 8:
2025-06-14 22:31:57,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:57,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:58,600 - INFO - Response - Page 8:
2025-06-14 22:31:58,600 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:31:59,115 - INFO - Request Parameters - Page 9:
2025-06-14 22:31:59,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:31:59,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:31:59,912 - INFO - Response - Page 9:
2025-06-14 22:31:59,912 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:32:00,412 - INFO - Request Parameters - Page 10:
2025-06-14 22:32:00,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:00,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:01,099 - INFO - Response - Page 10:
2025-06-14 22:32:01,099 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:32:01,615 - INFO - Request Parameters - Page 11:
2025-06-14 22:32:01,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:01,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:02,318 - INFO - Response - Page 11:
2025-06-14 22:32:02,318 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:32:02,818 - INFO - Request Parameters - Page 12:
2025-06-14 22:32:02,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:02,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:03,365 - INFO - Response - Page 12:
2025-06-14 22:32:03,365 - INFO - 第 12 页获取到 16 条记录
2025-06-14 22:32:03,880 - INFO - 查询完成，共获取到 566 条记录
2025-06-14 22:32:03,880 - INFO - 获取到 566 条表单数据
2025-06-14 22:32:03,880 - INFO - 当前日期 2025-05-06 有 4 条MySQL数据需要处理
2025-06-14 22:32:03,880 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC16P5WEDNFB95PP5L88NDX2KP2EWOBMC3
2025-06-14 22:32:04,287 - INFO - 更新表单数据成功: FINST-8LC66GC16P5WEDNFB95PP5L88NDX2KP2EWOBMC3
2025-06-14 22:32:04,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3803.72, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 26219.02, 'new_value': 30026.52}, {'field': 'total_amount', 'old_value': 30022.74, 'new_value': 30026.52}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:04,287 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC16P5WEDNFB95PP5L88NDX2LP2EWOBMD4
2025-06-14 22:32:04,755 - INFO - 更新表单数据成功: FINST-8LC66GC16P5WEDNFB95PP5L88NDX2LP2EWOBMD4
2025-06-14 22:32:04,755 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4062.58, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 791.0, 'new_value': 6713.58}, {'field': 'total_amount', 'old_value': 4853.58, 'new_value': 6713.58}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:04,755 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1BJ2W292T9770MDLX7ZOZ128VEWOBMG2
2025-06-14 22:32:05,286 - INFO - 更新表单数据成功: FINST-VFF66XA1BJ2W292T9770MDLX7ZOZ128VEWOBMG2
2025-06-14 22:32:05,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 680.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 7009.0, 'new_value': 7689.92}, {'field': 'total_amount', 'old_value': 7689.0, 'new_value': 7689.92}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:05,286 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1BJ2W292T9770MDLX7ZOZ128VEWOBMB3
2025-06-14 22:32:05,724 - INFO - 更新表单数据成功: FINST-VFF66XA1BJ2W292T9770MDLX7ZOZ128VEWOBMB3
2025-06-14 22:32:05,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1667.0, 'new_value': 1604.54}, {'field': 'total_amount', 'old_value': 1667.0, 'new_value': 1604.54}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:05,724 - INFO - 日期 2025-05-06 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-06-14 22:32:05,724 - INFO - 开始处理日期: 2025-05-07
2025-06-14 22:32:05,724 - INFO - Request Parameters - Page 1:
2025-06-14 22:32:05,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:05,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:06,349 - INFO - Response - Page 1:
2025-06-14 22:32:06,349 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:32:06,864 - INFO - Request Parameters - Page 2:
2025-06-14 22:32:06,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:06,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:07,489 - INFO - Response - Page 2:
2025-06-14 22:32:07,489 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:32:07,989 - INFO - Request Parameters - Page 3:
2025-06-14 22:32:07,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:07,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:08,614 - INFO - Response - Page 3:
2025-06-14 22:32:08,614 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:32:09,114 - INFO - Request Parameters - Page 4:
2025-06-14 22:32:09,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:09,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:09,802 - INFO - Response - Page 4:
2025-06-14 22:32:09,802 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:32:10,302 - INFO - Request Parameters - Page 5:
2025-06-14 22:32:10,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:10,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:10,973 - INFO - Response - Page 5:
2025-06-14 22:32:10,973 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:32:11,473 - INFO - Request Parameters - Page 6:
2025-06-14 22:32:11,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:11,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:12,098 - INFO - Response - Page 6:
2025-06-14 22:32:12,098 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:32:12,598 - INFO - Request Parameters - Page 7:
2025-06-14 22:32:12,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:12,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:13,254 - INFO - Response - Page 7:
2025-06-14 22:32:13,254 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:32:13,754 - INFO - Request Parameters - Page 8:
2025-06-14 22:32:13,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:13,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:14,442 - INFO - Response - Page 8:
2025-06-14 22:32:14,442 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:32:14,957 - INFO - Request Parameters - Page 9:
2025-06-14 22:32:14,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:14,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:15,582 - INFO - Response - Page 9:
2025-06-14 22:32:15,582 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:32:16,098 - INFO - Request Parameters - Page 10:
2025-06-14 22:32:16,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:16,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:16,723 - INFO - Response - Page 10:
2025-06-14 22:32:16,723 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:32:17,238 - INFO - Request Parameters - Page 11:
2025-06-14 22:32:17,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:17,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:17,910 - INFO - Response - Page 11:
2025-06-14 22:32:17,910 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:32:18,410 - INFO - Request Parameters - Page 12:
2025-06-14 22:32:18,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:18,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:18,926 - INFO - Response - Page 12:
2025-06-14 22:32:18,926 - INFO - 第 12 页获取到 13 条记录
2025-06-14 22:32:19,441 - INFO - 查询完成，共获取到 563 条记录
2025-06-14 22:32:19,441 - INFO - 获取到 563 条表单数据
2025-06-14 22:32:19,441 - INFO - 当前日期 2025-05-07 有 2 条MySQL数据需要处理
2025-06-14 22:32:19,441 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD1NQ2WHEZ6E3IV8DBOWOYV3BSFFWOBMZK
2025-06-14 22:32:19,926 - INFO - 更新表单数据成功: FINST-2PF66CD1NQ2WHEZ6E3IV8DBOWOYV3BSFFWOBMZK
2025-06-14 22:32:19,926 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3996.02, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 26269.1, 'new_value': 30155.47}, {'field': 'total_amount', 'old_value': 30265.12, 'new_value': 30155.47}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:19,926 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD1NQ2WHEZ6E3IV8DBOWOYV3BSFFWOBMXL
2025-06-14 22:32:20,363 - INFO - 更新表单数据成功: FINST-2PF66CD1NQ2WHEZ6E3IV8DBOWOYV3BSFFWOBMXL
2025-06-14 22:32:20,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3402.88, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2289.0, 'new_value': 7971.88}, {'field': 'total_amount', 'old_value': 5691.88, 'new_value': 7971.88}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:20,363 - INFO - 日期 2025-05-07 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-06-14 22:32:20,363 - INFO - 开始处理日期: 2025-05-08
2025-06-14 22:32:20,363 - INFO - Request Parameters - Page 1:
2025-06-14 22:32:20,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:20,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:21,035 - INFO - Response - Page 1:
2025-06-14 22:32:21,035 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:32:21,550 - INFO - Request Parameters - Page 2:
2025-06-14 22:32:21,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:21,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:22,363 - INFO - Response - Page 2:
2025-06-14 22:32:22,363 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:32:22,878 - INFO - Request Parameters - Page 3:
2025-06-14 22:32:22,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:22,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:23,519 - INFO - Response - Page 3:
2025-06-14 22:32:23,519 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:32:24,035 - INFO - Request Parameters - Page 4:
2025-06-14 22:32:24,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:24,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:24,628 - INFO - Response - Page 4:
2025-06-14 22:32:24,628 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:32:25,144 - INFO - Request Parameters - Page 5:
2025-06-14 22:32:25,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:25,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:25,738 - INFO - Response - Page 5:
2025-06-14 22:32:25,738 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:32:26,253 - INFO - Request Parameters - Page 6:
2025-06-14 22:32:26,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:26,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:26,894 - INFO - Response - Page 6:
2025-06-14 22:32:26,894 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:32:27,409 - INFO - Request Parameters - Page 7:
2025-06-14 22:32:27,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:27,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:28,081 - INFO - Response - Page 7:
2025-06-14 22:32:28,081 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:32:28,597 - INFO - Request Parameters - Page 8:
2025-06-14 22:32:28,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:28,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:29,300 - INFO - Response - Page 8:
2025-06-14 22:32:29,300 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:32:29,815 - INFO - Request Parameters - Page 9:
2025-06-14 22:32:29,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:29,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:30,472 - INFO - Response - Page 9:
2025-06-14 22:32:30,472 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:32:30,987 - INFO - Request Parameters - Page 10:
2025-06-14 22:32:30,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:30,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:31,659 - INFO - Response - Page 10:
2025-06-14 22:32:31,659 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:32:32,159 - INFO - Request Parameters - Page 11:
2025-06-14 22:32:32,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:32,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:32,753 - INFO - Response - Page 11:
2025-06-14 22:32:32,753 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:32:33,268 - INFO - Request Parameters - Page 12:
2025-06-14 22:32:33,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:33,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:33,784 - INFO - Response - Page 12:
2025-06-14 22:32:33,784 - INFO - 第 12 页获取到 12 条记录
2025-06-14 22:32:34,299 - INFO - 查询完成，共获取到 562 条记录
2025-06-14 22:32:34,299 - INFO - 获取到 562 条表单数据
2025-06-14 22:32:34,299 - INFO - 当前日期 2025-05-08 有 3 条MySQL数据需要处理
2025-06-14 22:32:34,299 - INFO - 开始更新记录 - 表单实例ID: FINST-VRA66VA17R2W7N9XE9QXW58DDV6W3ZWUGWOBMQ11
2025-06-14 22:32:34,737 - INFO - 更新表单数据成功: FINST-VRA66VA17R2W7N9XE9QXW58DDV6W3ZWUGWOBMQ11
2025-06-14 22:32:34,737 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3319.84, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 24137.05, 'new_value': 27559.24}, {'field': 'total_amount', 'old_value': 27456.89, 'new_value': 27559.24}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:34,737 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66F716A2WHA0I8UABT9QIB4U8380ZGWOBMZK
2025-06-14 22:32:35,080 - INFO - 更新表单数据成功: FINST-LLF66F716A2WHA0I8UABT9QIB4U8380ZGWOBMZK
2025-06-14 22:32:35,080 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2950.9, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3808.0, 'new_value': 9218.9}, {'field': 'total_amount', 'old_value': 6758.9, 'new_value': 9218.9}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:35,080 - INFO - 开始更新记录 - 表单实例ID: FINST-X2F66HC1JT2WFGOZB9FD55XX9PJ4378JHWOBMIO
2025-06-14 22:32:35,455 - INFO - 更新表单数据成功: FINST-X2F66HC1JT2WFGOZB9FD55XX9PJ4378JHWOBMIO
2025-06-14 22:32:35,455 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 608.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 9030.0, 'new_value': 9639.68}, {'field': 'total_amount', 'old_value': 9638.0, 'new_value': 9639.68}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:35,455 - INFO - 日期 2025-05-08 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:32:35,455 - INFO - 开始处理日期: 2025-05-09
2025-06-14 22:32:35,455 - INFO - Request Parameters - Page 1:
2025-06-14 22:32:35,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:35,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:36,143 - INFO - Response - Page 1:
2025-06-14 22:32:36,143 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:32:36,658 - INFO - Request Parameters - Page 2:
2025-06-14 22:32:36,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:36,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:37,315 - INFO - Response - Page 2:
2025-06-14 22:32:37,315 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:32:37,815 - INFO - Request Parameters - Page 3:
2025-06-14 22:32:37,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:37,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:38,455 - INFO - Response - Page 3:
2025-06-14 22:32:38,455 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:32:38,971 - INFO - Request Parameters - Page 4:
2025-06-14 22:32:38,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:38,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:39,596 - INFO - Response - Page 4:
2025-06-14 22:32:39,596 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:32:40,096 - INFO - Request Parameters - Page 5:
2025-06-14 22:32:40,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:40,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:40,814 - INFO - Response - Page 5:
2025-06-14 22:32:40,814 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:32:41,330 - INFO - Request Parameters - Page 6:
2025-06-14 22:32:41,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:41,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:41,955 - INFO - Response - Page 6:
2025-06-14 22:32:41,955 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:32:42,470 - INFO - Request Parameters - Page 7:
2025-06-14 22:32:42,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:42,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:43,095 - INFO - Response - Page 7:
2025-06-14 22:32:43,095 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:32:43,611 - INFO - Request Parameters - Page 8:
2025-06-14 22:32:43,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:43,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:44,283 - INFO - Response - Page 8:
2025-06-14 22:32:44,283 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:32:44,783 - INFO - Request Parameters - Page 9:
2025-06-14 22:32:44,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:44,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:45,439 - INFO - Response - Page 9:
2025-06-14 22:32:45,439 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:32:45,939 - INFO - Request Parameters - Page 10:
2025-06-14 22:32:45,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:45,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:46,626 - INFO - Response - Page 10:
2025-06-14 22:32:46,626 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:32:47,126 - INFO - Request Parameters - Page 11:
2025-06-14 22:32:47,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:47,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:47,782 - INFO - Response - Page 11:
2025-06-14 22:32:47,782 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:32:48,298 - INFO - Request Parameters - Page 12:
2025-06-14 22:32:48,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:48,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:48,954 - INFO - Response - Page 12:
2025-06-14 22:32:48,954 - INFO - 第 12 页获取到 43 条记录
2025-06-14 22:32:49,470 - INFO - 查询完成，共获取到 593 条记录
2025-06-14 22:32:49,470 - INFO - 获取到 593 条表单数据
2025-06-14 22:32:49,470 - INFO - 当前日期 2025-05-09 有 3 条MySQL数据需要处理
2025-06-14 22:32:49,470 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF662B12Q5WMFOP90L1E7HZE2FV2XWDIWOBM6
2025-06-14 22:32:49,907 - INFO - 更新表单数据成功: FINST-MLF662B12Q5WMFOP90L1E7HZE2FV2XWDIWOBM6
2025-06-14 22:32:49,907 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5137.71, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3691.0, 'new_value': 11076.71}, {'field': 'total_amount', 'old_value': 8828.71, 'new_value': 11076.71}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:49,907 - INFO - 开始更新记录 - 表单实例ID: FINST-5A9660811E5WRTY7EWSDQ8UA9DXW2ZYHIWOBMK2
2025-06-14 22:32:50,423 - INFO - 更新表单数据成功: FINST-5A9660811E5WRTY7EWSDQ8UA9DXW2ZYHIWOBMK2
2025-06-14 22:32:50,423 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3750.63, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 32986.1, 'new_value': 36766.73}, {'field': 'total_amount', 'old_value': 36736.73, 'new_value': 36766.73}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:50,423 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB15D2WFB4DAH16ZBUZJ34O25EAJWOBMNV
2025-06-14 22:32:50,891 - INFO - 更新表单数据成功: FINST-2K666OB15D2WFB4DAH16ZBUZJ34O25EAJWOBMNV
2025-06-14 22:32:50,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 368.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 12366.0, 'new_value': 12734.22}, {'field': 'total_amount', 'old_value': 12734.0, 'new_value': 12734.22}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:32:50,891 - INFO - 日期 2025-05-09 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:32:50,891 - INFO - 开始处理日期: 2025-05-10
2025-06-14 22:32:50,891 - INFO - Request Parameters - Page 1:
2025-06-14 22:32:50,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:50,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:51,532 - INFO - Response - Page 1:
2025-06-14 22:32:51,532 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:32:52,048 - INFO - Request Parameters - Page 2:
2025-06-14 22:32:52,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:52,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:52,672 - INFO - Response - Page 2:
2025-06-14 22:32:52,672 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:32:53,188 - INFO - Request Parameters - Page 3:
2025-06-14 22:32:53,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:53,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:53,860 - INFO - Response - Page 3:
2025-06-14 22:32:53,860 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:32:54,360 - INFO - Request Parameters - Page 4:
2025-06-14 22:32:54,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:54,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:55,000 - INFO - Response - Page 4:
2025-06-14 22:32:55,000 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:32:55,516 - INFO - Request Parameters - Page 5:
2025-06-14 22:32:55,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:55,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:56,203 - INFO - Response - Page 5:
2025-06-14 22:32:56,203 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:32:56,703 - INFO - Request Parameters - Page 6:
2025-06-14 22:32:56,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:56,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:57,281 - INFO - Response - Page 6:
2025-06-14 22:32:57,281 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:32:57,781 - INFO - Request Parameters - Page 7:
2025-06-14 22:32:57,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:57,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:58,438 - INFO - Response - Page 7:
2025-06-14 22:32:58,438 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:32:58,938 - INFO - Request Parameters - Page 8:
2025-06-14 22:32:58,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:32:58,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:32:59,656 - INFO - Response - Page 8:
2025-06-14 22:32:59,656 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:33:00,156 - INFO - Request Parameters - Page 9:
2025-06-14 22:33:00,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:00,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:00,875 - INFO - Response - Page 9:
2025-06-14 22:33:00,875 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:33:01,390 - INFO - Request Parameters - Page 10:
2025-06-14 22:33:01,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:01,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:02,031 - INFO - Response - Page 10:
2025-06-14 22:33:02,031 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:33:02,547 - INFO - Request Parameters - Page 11:
2025-06-14 22:33:02,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:02,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:03,125 - INFO - Response - Page 11:
2025-06-14 22:33:03,125 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:33:03,640 - INFO - Request Parameters - Page 12:
2025-06-14 22:33:03,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:03,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:04,281 - INFO - Response - Page 12:
2025-06-14 22:33:04,281 - INFO - 第 12 页获取到 37 条记录
2025-06-14 22:33:04,781 - INFO - 查询完成，共获取到 587 条记录
2025-06-14 22:33:04,781 - INFO - 获取到 587 条表单数据
2025-06-14 22:33:04,781 - INFO - 当前日期 2025-05-10 有 2 条MySQL数据需要处理
2025-06-14 22:33:04,781 - INFO - 开始更新记录 - 表单实例ID: FINST-L5766E718P2WFKZQFWQ699NJVO7029XUJWOBMXE
2025-06-14 22:33:05,203 - INFO - 更新表单数据成功: FINST-L5766E718P2WFKZQFWQ699NJVO7029XUJWOBMXE
2025-06-14 22:33:05,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7881.45, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 9666.0, 'new_value': 20131.45}, {'field': 'total_amount', 'old_value': 17547.45, 'new_value': 20131.45}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:05,203 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V716Q5WKF6EE5OUABPGXJQ02P2BKWOBMU1
2025-06-14 22:33:05,671 - INFO - 更新表单数据成功: FINST-3PF66V716Q5WKF6EE5OUABPGXJQ02P2BKWOBMU1
2025-06-14 22:33:05,671 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 344.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 15252.0, 'new_value': 15596.1}, {'field': 'total_amount', 'old_value': 15596.0, 'new_value': 15596.1}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:05,671 - INFO - 日期 2025-05-10 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-06-14 22:33:05,671 - INFO - 开始处理日期: 2025-05-11
2025-06-14 22:33:05,671 - INFO - Request Parameters - Page 1:
2025-06-14 22:33:05,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:05,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:06,327 - INFO - Response - Page 1:
2025-06-14 22:33:06,327 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:33:06,843 - INFO - Request Parameters - Page 2:
2025-06-14 22:33:06,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:06,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:07,468 - INFO - Response - Page 2:
2025-06-14 22:33:07,468 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:33:07,984 - INFO - Request Parameters - Page 3:
2025-06-14 22:33:07,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:07,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:08,608 - INFO - Response - Page 3:
2025-06-14 22:33:08,608 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:33:09,108 - INFO - Request Parameters - Page 4:
2025-06-14 22:33:09,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:09,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:09,718 - INFO - Response - Page 4:
2025-06-14 22:33:09,718 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:33:10,218 - INFO - Request Parameters - Page 5:
2025-06-14 22:33:10,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:10,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:10,858 - INFO - Response - Page 5:
2025-06-14 22:33:10,858 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:33:11,374 - INFO - Request Parameters - Page 6:
2025-06-14 22:33:11,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:11,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:11,968 - INFO - Response - Page 6:
2025-06-14 22:33:11,968 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:33:12,483 - INFO - Request Parameters - Page 7:
2025-06-14 22:33:12,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:12,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:13,155 - INFO - Response - Page 7:
2025-06-14 22:33:13,155 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:33:13,670 - INFO - Request Parameters - Page 8:
2025-06-14 22:33:13,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:13,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:14,295 - INFO - Response - Page 8:
2025-06-14 22:33:14,295 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:33:14,795 - INFO - Request Parameters - Page 9:
2025-06-14 22:33:14,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:14,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:15,452 - INFO - Response - Page 9:
2025-06-14 22:33:15,452 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:33:15,952 - INFO - Request Parameters - Page 10:
2025-06-14 22:33:15,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:15,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:16,608 - INFO - Response - Page 10:
2025-06-14 22:33:16,608 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:33:17,108 - INFO - Request Parameters - Page 11:
2025-06-14 22:33:17,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:17,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:17,779 - INFO - Response - Page 11:
2025-06-14 22:33:17,779 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:33:18,295 - INFO - Request Parameters - Page 12:
2025-06-14 22:33:18,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:18,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:18,811 - INFO - Response - Page 12:
2025-06-14 22:33:18,811 - INFO - 第 12 页获取到 17 条记录
2025-06-14 22:33:19,326 - INFO - 查询完成，共获取到 567 条记录
2025-06-14 22:33:19,326 - INFO - 获取到 567 条表单数据
2025-06-14 22:33:19,326 - INFO - 当前日期 2025-05-11 有 4 条MySQL数据需要处理
2025-06-14 22:33:19,326 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1TO5WF7DUDRKP7CSX2HUO36Y3LWOBMU
2025-06-14 22:33:19,764 - INFO - 更新表单数据成功: FINST-BD766BC1TO5WF7DUDRKP7CSX2HUO36Y3LWOBMU
2025-06-14 22:33:19,764 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9138.15, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 9613.0, 'new_value': 20500.15}, {'field': 'total_amount', 'old_value': 18751.15, 'new_value': 20500.15}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:19,764 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1TO5WF7DUDRKP7CSX2HUO36Y3LWOBML1
2025-06-14 22:33:20,170 - INFO - 更新表单数据成功: FINST-BD766BC1TO5WF7DUDRKP7CSX2HUO36Y3LWOBML1
2025-06-14 22:33:20,170 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 749.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 21680.0, 'new_value': 22429.04}, {'field': 'total_amount', 'old_value': 22429.0, 'new_value': 22429.04}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:20,170 - INFO - 开始更新记录 - 表单实例ID: FINST-XRF66A81BO5W1HP1EX4T244238Q23OZ7LWOBMP2
2025-06-14 22:33:20,607 - INFO - 更新表单数据成功: FINST-XRF66A81BO5W1HP1EX4T244238Q23OZ7LWOBMP2
2025-06-14 22:33:20,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8088.92, 'new_value': 8033.92}, {'field': 'total_amount', 'old_value': 8088.92, 'new_value': 8033.92}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:20,607 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB66071MO2WSJ0R6BM129DCBUS633BWLWOBMN2
2025-06-14 22:33:21,014 - INFO - 更新表单数据成功: FINST-ACB66071MO2WSJ0R6BM129DCBUS633BWLWOBMN2
2025-06-14 22:33:21,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1636.0, 'new_value': 1647.0}, {'field': 'total_amount', 'old_value': 1636.0, 'new_value': 1647.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:21,014 - INFO - 日期 2025-05-11 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-06-14 22:33:21,014 - INFO - 开始处理日期: 2025-05-12
2025-06-14 22:33:21,014 - INFO - Request Parameters - Page 1:
2025-06-14 22:33:21,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:21,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:21,654 - INFO - Response - Page 1:
2025-06-14 22:33:21,654 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:33:22,170 - INFO - Request Parameters - Page 2:
2025-06-14 22:33:22,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:22,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:22,763 - INFO - Response - Page 2:
2025-06-14 22:33:22,763 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:33:23,279 - INFO - Request Parameters - Page 3:
2025-06-14 22:33:23,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:23,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:23,904 - INFO - Response - Page 3:
2025-06-14 22:33:23,904 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:33:24,404 - INFO - Request Parameters - Page 4:
2025-06-14 22:33:24,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:24,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:24,998 - INFO - Response - Page 4:
2025-06-14 22:33:24,998 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:33:25,513 - INFO - Request Parameters - Page 5:
2025-06-14 22:33:25,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:25,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:26,185 - INFO - Response - Page 5:
2025-06-14 22:33:26,185 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:33:26,685 - INFO - Request Parameters - Page 6:
2025-06-14 22:33:26,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:26,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:27,419 - INFO - Response - Page 6:
2025-06-14 22:33:27,419 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:33:27,935 - INFO - Request Parameters - Page 7:
2025-06-14 22:33:27,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:27,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:28,528 - INFO - Response - Page 7:
2025-06-14 22:33:28,528 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:33:29,028 - INFO - Request Parameters - Page 8:
2025-06-14 22:33:29,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:29,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:29,653 - INFO - Response - Page 8:
2025-06-14 22:33:29,653 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:33:30,153 - INFO - Request Parameters - Page 9:
2025-06-14 22:33:30,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:30,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:30,856 - INFO - Response - Page 9:
2025-06-14 22:33:30,856 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:33:31,372 - INFO - Request Parameters - Page 10:
2025-06-14 22:33:31,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:31,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:31,981 - INFO - Response - Page 10:
2025-06-14 22:33:31,981 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:33:32,497 - INFO - Request Parameters - Page 11:
2025-06-14 22:33:32,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:32,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:33,122 - INFO - Response - Page 11:
2025-06-14 22:33:33,122 - INFO - 第 11 页获取到 47 条记录
2025-06-14 22:33:33,622 - INFO - 查询完成，共获取到 547 条记录
2025-06-14 22:33:33,622 - INFO - 获取到 547 条表单数据
2025-06-14 22:33:33,622 - INFO - 当前日期 2025-05-12 有 3 条MySQL数据需要处理
2025-06-14 22:33:33,622 - INFO - 开始更新记录 - 表单实例ID: FINST-XMC66R919C3WDD4OC4X5L8NF6SG92CZAMWOBMS01
2025-06-14 22:33:34,169 - INFO - 更新表单数据成功: FINST-XMC66R919C3WDD4OC4X5L8NF6SG92CZAMWOBMS01
2025-06-14 22:33:34,169 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3522.67, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1466.0, 'new_value': 7495.66}, {'field': 'total_amount', 'old_value': 4988.67, 'new_value': 7495.66}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:34,169 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA15Q5WSTHB9IUZA8S4HB0Z2G5NMWOBMJ
2025-06-14 22:33:34,637 - INFO - 更新表单数据成功: FINST-90D66XA15Q5WSTHB9IUZA8S4HB0Z2G5NMWOBMJ
2025-06-14 22:33:34,637 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3042.66, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 28474.0, 'new_value': 31436.16}, {'field': 'total_amount', 'old_value': 31516.66, 'new_value': 31436.16}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:34,637 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1NT2W52UVC2ILMAGFAR252NC3NWOBM3F
2025-06-14 22:33:35,153 - INFO - 更新表单数据成功: FINST-2K666OB1NT2W52UVC2ILMAGFAR252NC3NWOBM3F
2025-06-14 22:33:35,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2239.54, 'new_value': 2224.54}, {'field': 'total_amount', 'old_value': 2239.54, 'new_value': 2224.54}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:35,153 - INFO - 日期 2025-05-12 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:33:35,153 - INFO - 开始处理日期: 2025-05-13
2025-06-14 22:33:35,153 - INFO - Request Parameters - Page 1:
2025-06-14 22:33:35,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:35,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:35,856 - INFO - Response - Page 1:
2025-06-14 22:33:35,856 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:33:36,356 - INFO - Request Parameters - Page 2:
2025-06-14 22:33:36,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:36,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:37,059 - INFO - Response - Page 2:
2025-06-14 22:33:37,059 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:33:37,559 - INFO - Request Parameters - Page 3:
2025-06-14 22:33:37,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:37,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:38,184 - INFO - Response - Page 3:
2025-06-14 22:33:38,184 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:33:38,699 - INFO - Request Parameters - Page 4:
2025-06-14 22:33:38,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:38,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:39,355 - INFO - Response - Page 4:
2025-06-14 22:33:39,355 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:33:39,855 - INFO - Request Parameters - Page 5:
2025-06-14 22:33:39,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:39,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:40,512 - INFO - Response - Page 5:
2025-06-14 22:33:40,512 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:33:41,012 - INFO - Request Parameters - Page 6:
2025-06-14 22:33:41,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:41,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:41,683 - INFO - Response - Page 6:
2025-06-14 22:33:41,683 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:33:42,199 - INFO - Request Parameters - Page 7:
2025-06-14 22:33:42,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:42,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:42,902 - INFO - Response - Page 7:
2025-06-14 22:33:42,902 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:33:43,418 - INFO - Request Parameters - Page 8:
2025-06-14 22:33:43,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:43,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:43,980 - INFO - Response - Page 8:
2025-06-14 22:33:43,980 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:33:44,496 - INFO - Request Parameters - Page 9:
2025-06-14 22:33:44,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:44,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:45,136 - INFO - Response - Page 9:
2025-06-14 22:33:45,136 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:33:45,652 - INFO - Request Parameters - Page 10:
2025-06-14 22:33:45,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:45,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:46,339 - INFO - Response - Page 10:
2025-06-14 22:33:46,339 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:33:46,855 - INFO - Request Parameters - Page 11:
2025-06-14 22:33:46,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:46,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:47,542 - INFO - Response - Page 11:
2025-06-14 22:33:47,542 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:33:48,042 - INFO - Request Parameters - Page 12:
2025-06-14 22:33:48,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:48,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:48,573 - INFO - Response - Page 12:
2025-06-14 22:33:48,573 - INFO - 第 12 页获取到 17 条记录
2025-06-14 22:33:49,089 - INFO - 查询完成，共获取到 567 条记录
2025-06-14 22:33:49,089 - INFO - 获取到 567 条表单数据
2025-06-14 22:33:49,089 - INFO - 当前日期 2025-05-13 有 3 条MySQL数据需要处理
2025-06-14 22:33:49,089 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1JP5WM7D19MO3Y88AFZSL2XIAOWOBM51
2025-06-14 22:33:49,495 - INFO - 更新表单数据成功: FINST-7PF66MD1JP5WM7D19MO3Y88AFZSL2XIAOWOBM51
2025-06-14 22:33:49,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4673.68, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2399.0, 'new_value': 9892.68}, {'field': 'total_amount', 'old_value': 7072.68, 'new_value': 9892.68}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:49,495 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91AD5WPH8CCCG7T4YWHPRL2OLEOWOBME6
2025-06-14 22:33:49,948 - INFO - 更新表单数据成功: FINST-DOA66K91AD5WPH8CCCG7T4YWHPRL2OLEOWOBME6
2025-06-14 22:33:49,948 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 754.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 8978.0, 'new_value': 9732.92}, {'field': 'total_amount', 'old_value': 9732.0, 'new_value': 9732.92}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:49,948 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC1FD2WRD137PH735PPBEK430NIOWOBMTM
2025-06-14 22:33:50,401 - INFO - 更新表单数据成功: FINST-5XA66LC1FD2WRD137PH735PPBEK430NIOWOBMTM
2025-06-14 22:33:50,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1760.31, 'new_value': 1778.31}, {'field': 'total_amount', 'old_value': 1760.31, 'new_value': 1778.31}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:33:50,401 - INFO - 日期 2025-05-13 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:33:50,401 - INFO - 开始处理日期: 2025-05-14
2025-06-14 22:33:50,401 - INFO - Request Parameters - Page 1:
2025-06-14 22:33:50,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:50,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:51,073 - INFO - Response - Page 1:
2025-06-14 22:33:51,073 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:33:51,573 - INFO - Request Parameters - Page 2:
2025-06-14 22:33:51,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:51,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:52,292 - INFO - Response - Page 2:
2025-06-14 22:33:52,292 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:33:52,807 - INFO - Request Parameters - Page 3:
2025-06-14 22:33:52,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:52,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:53,432 - INFO - Response - Page 3:
2025-06-14 22:33:53,432 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:33:53,948 - INFO - Request Parameters - Page 4:
2025-06-14 22:33:53,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:53,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:54,542 - INFO - Response - Page 4:
2025-06-14 22:33:54,542 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:33:55,057 - INFO - Request Parameters - Page 5:
2025-06-14 22:33:55,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:55,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:55,666 - INFO - Response - Page 5:
2025-06-14 22:33:55,666 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:33:56,182 - INFO - Request Parameters - Page 6:
2025-06-14 22:33:56,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:56,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:56,776 - INFO - Response - Page 6:
2025-06-14 22:33:56,776 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:33:57,276 - INFO - Request Parameters - Page 7:
2025-06-14 22:33:57,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:57,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:57,901 - INFO - Response - Page 7:
2025-06-14 22:33:57,901 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:33:58,416 - INFO - Request Parameters - Page 8:
2025-06-14 22:33:58,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:58,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:33:59,072 - INFO - Response - Page 8:
2025-06-14 22:33:59,072 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:33:59,588 - INFO - Request Parameters - Page 9:
2025-06-14 22:33:59,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:33:59,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:00,244 - INFO - Response - Page 9:
2025-06-14 22:34:00,244 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:34:00,760 - INFO - Request Parameters - Page 10:
2025-06-14 22:34:00,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:00,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:01,385 - INFO - Response - Page 10:
2025-06-14 22:34:01,385 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:34:01,885 - INFO - Request Parameters - Page 11:
2025-06-14 22:34:01,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:01,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:02,572 - INFO - Response - Page 11:
2025-06-14 22:34:02,572 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:34:03,088 - INFO - Request Parameters - Page 12:
2025-06-14 22:34:03,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:03,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:03,650 - INFO - Response - Page 12:
2025-06-14 22:34:03,650 - INFO - 第 12 页获取到 17 条记录
2025-06-14 22:34:04,150 - INFO - 查询完成，共获取到 567 条记录
2025-06-14 22:34:04,150 - INFO - 获取到 567 条表单数据
2025-06-14 22:34:04,150 - INFO - 当前日期 2025-05-14 有 2 条MySQL数据需要处理
2025-06-14 22:34:04,150 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF662B15Q5WUU6IFLQ1E9CD0QND2XHIPWOBMP
2025-06-14 22:34:04,556 - INFO - 更新表单数据成功: FINST-MLF662B15Q5WUU6IFLQ1E9CD0QND2XHIPWOBMP
2025-06-14 22:34:04,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2257.36, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2167.0, 'new_value': 8809.36}, {'field': 'total_amount', 'old_value': 4424.36, 'new_value': 8809.36}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:04,556 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB10Q5WMBFJF0UY25RQUA072JMUPWOBMF3
2025-06-14 22:34:05,009 - INFO - 更新表单数据成功: FINST-AAG66KB10Q5WMBFJF0UY25RQUA072JMUPWOBMF3
2025-06-14 22:34:05,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 571.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 6313.0, 'new_value': 6884.88}, {'field': 'total_amount', 'old_value': 6884.0, 'new_value': 6884.88}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:05,009 - INFO - 日期 2025-05-14 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-06-14 22:34:05,009 - INFO - 开始处理日期: 2025-05-15
2025-06-14 22:34:05,009 - INFO - Request Parameters - Page 1:
2025-06-14 22:34:05,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:05,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:05,697 - INFO - Response - Page 1:
2025-06-14 22:34:05,697 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:34:06,212 - INFO - Request Parameters - Page 2:
2025-06-14 22:34:06,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:06,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:06,822 - INFO - Response - Page 2:
2025-06-14 22:34:06,822 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:34:07,322 - INFO - Request Parameters - Page 3:
2025-06-14 22:34:07,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:07,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:08,040 - INFO - Response - Page 3:
2025-06-14 22:34:08,040 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:34:08,556 - INFO - Request Parameters - Page 4:
2025-06-14 22:34:08,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:08,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:09,196 - INFO - Response - Page 4:
2025-06-14 22:34:09,196 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:34:09,712 - INFO - Request Parameters - Page 5:
2025-06-14 22:34:09,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:09,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:10,493 - INFO - Response - Page 5:
2025-06-14 22:34:10,493 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:34:11,009 - INFO - Request Parameters - Page 6:
2025-06-14 22:34:11,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:11,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:11,649 - INFO - Response - Page 6:
2025-06-14 22:34:11,649 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:34:12,149 - INFO - Request Parameters - Page 7:
2025-06-14 22:34:12,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:12,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:12,821 - INFO - Response - Page 7:
2025-06-14 22:34:12,837 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:34:13,337 - INFO - Request Parameters - Page 8:
2025-06-14 22:34:13,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:13,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:13,977 - INFO - Response - Page 8:
2025-06-14 22:34:13,977 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:34:14,493 - INFO - Request Parameters - Page 9:
2025-06-14 22:34:14,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:14,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:15,118 - INFO - Response - Page 9:
2025-06-14 22:34:15,118 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:34:15,618 - INFO - Request Parameters - Page 10:
2025-06-14 22:34:15,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:15,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:16,305 - INFO - Response - Page 10:
2025-06-14 22:34:16,305 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:34:16,805 - INFO - Request Parameters - Page 11:
2025-06-14 22:34:16,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:16,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:17,383 - INFO - Response - Page 11:
2025-06-14 22:34:17,383 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:34:17,883 - INFO - Request Parameters - Page 12:
2025-06-14 22:34:17,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:17,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:18,414 - INFO - Response - Page 12:
2025-06-14 22:34:18,414 - INFO - 第 12 页获取到 13 条记录
2025-06-14 22:34:18,930 - INFO - 查询完成，共获取到 563 条记录
2025-06-14 22:34:18,930 - INFO - 获取到 563 条表单数据
2025-06-14 22:34:18,930 - INFO - 当前日期 2025-05-15 有 3 条MySQL数据需要处理
2025-06-14 22:34:18,930 - INFO - 开始更新记录 - 表单实例ID: FINST-X2F66HC1U72WP6XDBQQL5D0SFGWF3ODJQWOBMQ5
2025-06-14 22:34:19,352 - INFO - 更新表单数据成功: FINST-X2F66HC1U72WP6XDBQQL5D0SFGWF3ODJQWOBMQ5
2025-06-14 22:34:19,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2897.21, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 33440.2, 'new_value': 36275.41}, {'field': 'total_amount', 'old_value': 36337.41, 'new_value': 36275.41}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:19,352 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66E91IP5W7U0SBMDNZ8D8UIWA2GKZQWOBM91
2025-06-14 22:34:19,789 - INFO - 更新表单数据成功: FINST-3PF66E91IP5W7U0SBMDNZ8D8UIWA2GKZQWOBM91
2025-06-14 22:34:19,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3074.61, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2799.0, 'new_value': 12780.65}, {'field': 'total_amount', 'old_value': 5873.61, 'new_value': 12780.65}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:19,789 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66BA1WO5W01AUB347YA8PUL6529O7RWOBMT1
2025-06-14 22:34:20,352 - INFO - 更新表单数据成功: FINST-OIF66BA1WO5W01AUB347YA8PUL6529O7RWOBMT1
2025-06-14 22:34:20,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 679.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 10485.0, 'new_value': 11164.51}, {'field': 'total_amount', 'old_value': 11164.0, 'new_value': 11164.51}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:20,352 - INFO - 日期 2025-05-15 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:34:20,352 - INFO - 开始处理日期: 2025-05-16
2025-06-14 22:34:20,352 - INFO - Request Parameters - Page 1:
2025-06-14 22:34:20,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:20,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:21,023 - INFO - Response - Page 1:
2025-06-14 22:34:21,023 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:34:21,539 - INFO - Request Parameters - Page 2:
2025-06-14 22:34:21,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:21,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:22,195 - INFO - Response - Page 2:
2025-06-14 22:34:22,195 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:34:22,711 - INFO - Request Parameters - Page 3:
2025-06-14 22:34:22,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:22,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:23,383 - INFO - Response - Page 3:
2025-06-14 22:34:23,383 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:34:23,898 - INFO - Request Parameters - Page 4:
2025-06-14 22:34:23,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:23,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:24,554 - INFO - Response - Page 4:
2025-06-14 22:34:24,554 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:34:25,070 - INFO - Request Parameters - Page 5:
2025-06-14 22:34:25,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:25,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:25,726 - INFO - Response - Page 5:
2025-06-14 22:34:25,726 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:34:26,226 - INFO - Request Parameters - Page 6:
2025-06-14 22:34:26,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:26,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:26,882 - INFO - Response - Page 6:
2025-06-14 22:34:26,882 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:34:27,398 - INFO - Request Parameters - Page 7:
2025-06-14 22:34:27,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:27,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:28,023 - INFO - Response - Page 7:
2025-06-14 22:34:28,023 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:34:28,538 - INFO - Request Parameters - Page 8:
2025-06-14 22:34:28,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:28,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:29,226 - INFO - Response - Page 8:
2025-06-14 22:34:29,226 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:34:29,741 - INFO - Request Parameters - Page 9:
2025-06-14 22:34:29,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:29,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:30,366 - INFO - Response - Page 9:
2025-06-14 22:34:30,366 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:34:30,882 - INFO - Request Parameters - Page 10:
2025-06-14 22:34:30,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:30,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:31,554 - INFO - Response - Page 10:
2025-06-14 22:34:31,569 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:34:32,085 - INFO - Request Parameters - Page 11:
2025-06-14 22:34:32,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:32,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:32,710 - INFO - Response - Page 11:
2025-06-14 22:34:32,710 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:34:33,225 - INFO - Request Parameters - Page 12:
2025-06-14 22:34:33,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:33,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:33,741 - INFO - Response - Page 12:
2025-06-14 22:34:33,741 - INFO - 第 12 页获取到 20 条记录
2025-06-14 22:34:34,256 - INFO - 查询完成，共获取到 570 条记录
2025-06-14 22:34:34,256 - INFO - 获取到 570 条表单数据
2025-06-14 22:34:34,256 - INFO - 当前日期 2025-05-16 有 4 条MySQL数据需要处理
2025-06-14 22:34:34,256 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K815T2W3HS098ZEM4X4VA8T2V33SWOBM801
2025-06-14 22:34:34,725 - INFO - 更新表单数据成功: FINST-VME66K815T2W3HS098ZEM4X4VA8T2V33SWOBM801
2025-06-14 22:34:34,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4205.37, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 37990.32, 'new_value': 42237.27}, {'field': 'total_amount', 'old_value': 42195.69, 'new_value': 42237.27}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:34,725 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y916U4WM782FVPR26NXH5VZ2167SWOBMGM
2025-06-14 22:34:35,241 - INFO - 更新表单数据成功: FINST-VOC66Y916U4WM782FVPR26NXH5VZ2167SWOBMGM
2025-06-14 22:34:35,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4017.63, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2258.0, 'new_value': 12815.63}, {'field': 'total_amount', 'old_value': 6275.63, 'new_value': 12815.63}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:35,241 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91AJ2WR7J5BIBNJ6EDOBGF3MHRSWOBM7B1
2025-06-14 22:34:35,631 - INFO - 更新表单数据成功: FINST-VOC66Y91AJ2WR7J5BIBNJ6EDOBGF3MHRSWOBM7B1
2025-06-14 22:34:35,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1395.6, 'new_value': 1406.6}, {'field': 'total_amount', 'old_value': 1395.6, 'new_value': 1406.6}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:35,631 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71Q82WNIC975BM074QX1Q92WKZSWOBM2A
2025-06-14 22:34:36,225 - INFO - 更新表单数据成功: FINST-00D66K71Q82WNIC975BM074QX1Q92WKZSWOBM2A
2025-06-14 22:34:36,225 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 683.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 11367.0, 'new_value': 12050.95}, {'field': 'total_amount', 'old_value': 12050.0, 'new_value': 12050.95}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:34:36,225 - INFO - 日期 2025-05-16 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-06-14 22:34:36,225 - INFO - 开始处理日期: 2025-05-17
2025-06-14 22:34:36,225 - INFO - Request Parameters - Page 1:
2025-06-14 22:34:36,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:36,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:36,866 - INFO - Response - Page 1:
2025-06-14 22:34:36,866 - INFO - 第 1 页获取到 49 条记录
2025-06-14 22:34:37,366 - INFO - 查询完成，共获取到 49 条记录
2025-06-14 22:34:37,366 - INFO - 获取到 49 条表单数据
2025-06-14 22:34:37,366 - INFO - 当前日期 2025-05-17 有 3 条MySQL数据需要处理
2025-06-14 22:34:37,366 - INFO - 开始批量插入 3 条新记录
2025-06-14 22:34:37,553 - INFO - 批量插入响应状态码: 200
2025-06-14 22:34:37,553 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:34:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D974155D-ECE3-7079-B4D5-9CE3D43DA237', 'x-acs-trace-id': '401d57eac18ded4730ad8a11247bd12e', 'etag': '1LyTULecLqPf0E3MSueb8iw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:34:37,553 - INFO - 批量插入响应体: {'result': ['FINST-SI766181MI8WRT1D8H9G87AT5JVG2LQC8CWBMVQ', 'FINST-SI766181MI8WRT1D8H9G87AT5JVG2LQC8CWBMWQ', 'FINST-SI766181MI8WRT1D8H9G87AT5JVG2LQC8CWBMXQ']}
2025-06-14 22:34:37,553 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-14 22:34:37,553 - INFO - 成功插入的数据ID: ['FINST-SI766181MI8WRT1D8H9G87AT5JVG2LQC8CWBMVQ', 'FINST-SI766181MI8WRT1D8H9G87AT5JVG2LQC8CWBMWQ', 'FINST-SI766181MI8WRT1D8H9G87AT5JVG2LQC8CWBMXQ']
2025-06-14 22:34:42,568 - INFO - 批量插入完成，共 3 条记录
2025-06-14 22:34:42,568 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-06-14 22:34:42,568 - INFO - 开始处理日期: 2025-05-18
2025-06-14 22:34:42,568 - INFO - Request Parameters - Page 1:
2025-06-14 22:34:42,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:42,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:43,177 - INFO - Response - Page 1:
2025-06-14 22:34:43,177 - INFO - 第 1 页获取到 46 条记录
2025-06-14 22:34:43,693 - INFO - 查询完成，共获取到 46 条记录
2025-06-14 22:34:43,693 - INFO - 获取到 46 条表单数据
2025-06-14 22:34:43,693 - INFO - 当前日期 2025-05-18 有 5 条MySQL数据需要处理
2025-06-14 22:34:43,693 - INFO - 开始批量插入 5 条新记录
2025-06-14 22:34:43,849 - INFO - 批量插入响应状态码: 200
2025-06-14 22:34:43,849 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:34:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9E9141F4-1FB5-7EBC-AE6D-6244EB8D73D5', 'x-acs-trace-id': 'bacfb2d9f714209d7f58d11604cb2fba', 'etag': '26nRdbJ0197MqlJiZNkgPQQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:34:43,849 - INFO - 批量插入响应体: {'result': ['FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM49', 'FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM59', 'FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM69', 'FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM79', 'FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM89']}
2025-06-14 22:34:43,849 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-06-14 22:34:43,849 - INFO - 成功插入的数据ID: ['FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM49', 'FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM59', 'FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM69', 'FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM79', 'FINST-737662B1XS9WD6UTEZ5ST7FDTELI2QLH8CWBM89']
2025-06-14 22:34:48,864 - INFO - 批量插入完成，共 5 条记录
2025-06-14 22:34:48,864 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-06-14 22:34:48,864 - INFO - 开始处理日期: 2025-05-19
2025-06-14 22:34:48,864 - INFO - Request Parameters - Page 1:
2025-06-14 22:34:48,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:48,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:49,458 - INFO - Response - Page 1:
2025-06-14 22:34:49,458 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:34:49,974 - INFO - Request Parameters - Page 2:
2025-06-14 22:34:49,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:49,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:50,567 - INFO - Response - Page 2:
2025-06-14 22:34:50,567 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:34:51,083 - INFO - Request Parameters - Page 3:
2025-06-14 22:34:51,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:51,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:51,755 - INFO - Response - Page 3:
2025-06-14 22:34:51,755 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:34:52,270 - INFO - Request Parameters - Page 4:
2025-06-14 22:34:52,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:52,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:52,973 - INFO - Response - Page 4:
2025-06-14 22:34:52,973 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:34:53,473 - INFO - Request Parameters - Page 5:
2025-06-14 22:34:53,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:53,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:54,114 - INFO - Response - Page 5:
2025-06-14 22:34:54,114 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:34:54,630 - INFO - Request Parameters - Page 6:
2025-06-14 22:34:54,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:54,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:55,301 - INFO - Response - Page 6:
2025-06-14 22:34:55,301 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:34:55,817 - INFO - Request Parameters - Page 7:
2025-06-14 22:34:55,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:55,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:56,504 - INFO - Response - Page 7:
2025-06-14 22:34:56,504 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:34:57,020 - INFO - Request Parameters - Page 8:
2025-06-14 22:34:57,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:57,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:57,660 - INFO - Response - Page 8:
2025-06-14 22:34:57,660 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:34:58,176 - INFO - Request Parameters - Page 9:
2025-06-14 22:34:58,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:58,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:58,832 - INFO - Response - Page 9:
2025-06-14 22:34:58,832 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:34:59,332 - INFO - Request Parameters - Page 10:
2025-06-14 22:34:59,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:34:59,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:34:59,973 - INFO - Response - Page 10:
2025-06-14 22:34:59,973 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:35:00,488 - INFO - Request Parameters - Page 11:
2025-06-14 22:35:00,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:00,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:01,176 - INFO - Response - Page 11:
2025-06-14 22:35:01,176 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:35:01,691 - INFO - Request Parameters - Page 12:
2025-06-14 22:35:01,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:01,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:02,238 - INFO - Response - Page 12:
2025-06-14 22:35:02,238 - INFO - 第 12 页获取到 17 条记录
2025-06-14 22:35:02,738 - INFO - 查询完成，共获取到 567 条记录
2025-06-14 22:35:02,738 - INFO - 获取到 567 条表单数据
2025-06-14 22:35:02,738 - INFO - 当前日期 2025-05-19 有 5 条MySQL数据需要处理
2025-06-14 22:35:02,738 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B1FN5WC3FI9SSPHAO5H3UX2BVKTWOBME
2025-06-14 22:35:03,144 - INFO - 更新表单数据成功: FINST-LR5668B1FN5WC3FI9SSPHAO5H3UX2BVKTWOBME
2025-06-14 22:35:03,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 840.0, 'new_value': 2158.0}, {'field': 'total_amount', 'old_value': 840.0, 'new_value': 2158.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:03,144 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66KD17C2W6EAB7W5M1BX3JXBT26ZOTWOBMN4
2025-06-14 22:35:03,613 - INFO - 更新表单数据成功: FINST-2PF66KD17C2W6EAB7W5M1BX3JXBT26ZOTWOBMN4
2025-06-14 22:35:03,613 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3375.42, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 23364.0, 'new_value': 26565.46}, {'field': 'total_amount', 'old_value': 26739.42, 'new_value': 26565.46}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:03,613 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66KD17C2W6EAB7W5M1BX3JXBT26ZOTWOBMG5
2025-06-14 22:35:04,129 - INFO - 更新表单数据成功: FINST-2PF66KD17C2W6EAB7W5M1BX3JXBT26ZOTWOBMG5
2025-06-14 22:35:04,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4099.86, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 667.0, 'new_value': 6746.86}, {'field': 'total_amount', 'old_value': 4766.86, 'new_value': 6746.86}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:04,129 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1QQ2WEJP8A66VJCCV5GB338ADUWOBMIN
2025-06-14 22:35:04,613 - INFO - 更新表单数据成功: FINST-1PF66VA1QQ2WEJP8A66VJCCV5GB338ADUWOBMIN
2025-06-14 22:35:04,613 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 200.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 8767.0, 'new_value': 8968.55}, {'field': 'total_amount', 'old_value': 8967.0, 'new_value': 8968.55}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:04,613 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81UP5WOE1G6QLLAAPK69AJ2WIPUWOBMI1
2025-06-14 22:35:05,128 - INFO - 更新表单数据成功: FINST-VME66K81UP5WOE1G6QLLAAPK69AJ2WIPUWOBMI1
2025-06-14 22:35:05,128 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1583.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 26960.62, 'new_value': 28545.22}, {'field': 'total_amount', 'old_value': 28544.22, 'new_value': 28545.22}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:05,128 - INFO - 日期 2025-05-19 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-14 22:35:05,128 - INFO - 开始处理日期: 2025-05-20
2025-06-14 22:35:05,128 - INFO - Request Parameters - Page 1:
2025-06-14 22:35:05,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:05,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:05,738 - INFO - Response - Page 1:
2025-06-14 22:35:05,753 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:35:06,253 - INFO - Request Parameters - Page 2:
2025-06-14 22:35:06,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:06,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:06,847 - INFO - Response - Page 2:
2025-06-14 22:35:06,847 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:35:07,347 - INFO - Request Parameters - Page 3:
2025-06-14 22:35:07,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:07,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:07,988 - INFO - Response - Page 3:
2025-06-14 22:35:07,988 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:35:08,503 - INFO - Request Parameters - Page 4:
2025-06-14 22:35:08,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:08,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:09,159 - INFO - Response - Page 4:
2025-06-14 22:35:09,159 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:35:09,675 - INFO - Request Parameters - Page 5:
2025-06-14 22:35:09,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:09,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:10,315 - INFO - Response - Page 5:
2025-06-14 22:35:10,315 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:35:10,831 - INFO - Request Parameters - Page 6:
2025-06-14 22:35:10,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:10,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:11,456 - INFO - Response - Page 6:
2025-06-14 22:35:11,456 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:35:11,956 - INFO - Request Parameters - Page 7:
2025-06-14 22:35:11,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:11,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:12,565 - INFO - Response - Page 7:
2025-06-14 22:35:12,581 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:35:13,096 - INFO - Request Parameters - Page 8:
2025-06-14 22:35:13,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:13,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:13,815 - INFO - Response - Page 8:
2025-06-14 22:35:13,815 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:35:14,331 - INFO - Request Parameters - Page 9:
2025-06-14 22:35:14,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:14,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:14,940 - INFO - Response - Page 9:
2025-06-14 22:35:14,940 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:35:15,456 - INFO - Request Parameters - Page 10:
2025-06-14 22:35:15,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:15,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:16,127 - INFO - Response - Page 10:
2025-06-14 22:35:16,127 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:35:16,643 - INFO - Request Parameters - Page 11:
2025-06-14 22:35:16,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:16,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:17,315 - INFO - Response - Page 11:
2025-06-14 22:35:17,315 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:35:17,830 - INFO - Request Parameters - Page 12:
2025-06-14 22:35:17,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:17,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:18,440 - INFO - Response - Page 12:
2025-06-14 22:35:18,440 - INFO - 第 12 页获取到 15 条记录
2025-06-14 22:35:18,940 - INFO - 查询完成，共获取到 565 条记录
2025-06-14 22:35:18,940 - INFO - 获取到 565 条表单数据
2025-06-14 22:35:18,940 - INFO - 当前日期 2025-05-20 有 5 条MySQL数据需要处理
2025-06-14 22:35:18,940 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1FC2WFC9NC1Y0D5JHGZSR2RL0VWOBMY3
2025-06-14 22:35:19,377 - INFO - 更新表单数据成功: FINST-7PF66MD1FC2WFC9NC1Y0D5JHGZSR2RL0VWOBMY3
2025-06-14 22:35:19,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2158.0, 'new_value': 1151.0}, {'field': 'total_amount', 'old_value': 2158.0, 'new_value': 1151.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:19,377 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BQ5WVZ1Y7BXSB6BPMKNM3PO8VWOBMX
2025-06-14 22:35:19,846 - INFO - 更新表单数据成功: FINST-W3B66L71BQ5WVZ1Y7BXSB6BPMKNM3PO8VWOBMX
2025-06-14 22:35:19,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2557.18, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 35135.64, 'new_value': 37951.78}, {'field': 'total_amount', 'old_value': 37692.82, 'new_value': 37951.78}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:19,846 - INFO - 开始更新记录 - 表单实例ID: FINST-5TD66N91IL2W7IIC6489IDEV4Z6F23RCVWOBMD7
2025-06-14 22:35:20,252 - INFO - 更新表单数据成功: FINST-5TD66N91IL2W7IIC6489IDEV4Z6F23RCVWOBMD7
2025-06-14 22:35:20,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6388.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 40125.85, 'new_value': 46014.45}, {'field': 'total_amount', 'old_value': 46514.45, 'new_value': 46014.45}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:20,252 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66O71HA2WMA1IDZHUQ40ALJGE38VKVWOBME01
2025-06-14 22:35:20,674 - INFO - 更新表单数据成功: FINST-3PF66O71HA2WMA1IDZHUQ40ALJGE38VKVWOBME01
2025-06-14 22:35:20,674 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6929.46, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2119.0, 'new_value': 14033.5}, {'field': 'total_amount', 'old_value': 9048.46, 'new_value': 14033.5}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:20,674 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D717V1WDUTRFVF9WBPIHHQB36ZSVWOBMSH
2025-06-14 22:35:21,127 - INFO - 更新表单数据成功: FINST-RNA66D717V1WDUTRFVF9WBPIHHQB36ZSVWOBMSH
2025-06-14 22:35:21,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 406.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 13651.0, 'new_value': 14058.28}, {'field': 'total_amount', 'old_value': 14057.0, 'new_value': 14058.28}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:21,127 - INFO - 日期 2025-05-20 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-14 22:35:21,127 - INFO - 开始处理日期: 2025-05-21
2025-06-14 22:35:21,127 - INFO - Request Parameters - Page 1:
2025-06-14 22:35:21,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:21,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:21,736 - INFO - Response - Page 1:
2025-06-14 22:35:21,736 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:35:22,252 - INFO - Request Parameters - Page 2:
2025-06-14 22:35:22,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:22,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:22,892 - INFO - Response - Page 2:
2025-06-14 22:35:22,892 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:35:23,392 - INFO - Request Parameters - Page 3:
2025-06-14 22:35:23,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:23,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:24,049 - INFO - Response - Page 3:
2025-06-14 22:35:24,049 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:35:24,564 - INFO - Request Parameters - Page 4:
2025-06-14 22:35:24,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:24,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:25,189 - INFO - Response - Page 4:
2025-06-14 22:35:25,189 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:35:25,705 - INFO - Request Parameters - Page 5:
2025-06-14 22:35:25,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:25,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:26,314 - INFO - Response - Page 5:
2025-06-14 22:35:26,314 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:35:26,830 - INFO - Request Parameters - Page 6:
2025-06-14 22:35:26,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:26,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:27,423 - INFO - Response - Page 6:
2025-06-14 22:35:27,423 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:35:27,939 - INFO - Request Parameters - Page 7:
2025-06-14 22:35:27,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:27,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:28,595 - INFO - Response - Page 7:
2025-06-14 22:35:28,595 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:35:29,095 - INFO - Request Parameters - Page 8:
2025-06-14 22:35:29,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:29,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:29,720 - INFO - Response - Page 8:
2025-06-14 22:35:29,720 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:35:30,220 - INFO - Request Parameters - Page 9:
2025-06-14 22:35:30,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:30,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:30,892 - INFO - Response - Page 9:
2025-06-14 22:35:30,892 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:35:31,407 - INFO - Request Parameters - Page 10:
2025-06-14 22:35:31,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:31,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:32,048 - INFO - Response - Page 10:
2025-06-14 22:35:32,048 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:35:32,548 - INFO - Request Parameters - Page 11:
2025-06-14 22:35:32,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:32,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:33,188 - INFO - Response - Page 11:
2025-06-14 22:35:33,188 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:35:33,704 - INFO - Request Parameters - Page 12:
2025-06-14 22:35:33,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:33,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:34,298 - INFO - Response - Page 12:
2025-06-14 22:35:34,298 - INFO - 第 12 页获取到 15 条记录
2025-06-14 22:35:34,798 - INFO - 查询完成，共获取到 565 条记录
2025-06-14 22:35:34,798 - INFO - 获取到 565 条表单数据
2025-06-14 22:35:34,798 - INFO - 当前日期 2025-05-21 有 4 条MySQL数据需要处理
2025-06-14 22:35:34,798 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB1RO5WNGSJ9QJNKAWGV6A920J0XWOBMN
2025-06-14 22:35:35,251 - INFO - 更新表单数据成功: FINST-3RE66ZB1RO5WNGSJ9QJNKAWGV6A920J0XWOBMN
2025-06-14 22:35:35,251 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3219.34, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1820.0, 'new_value': 9324.13}, {'field': 'total_amount', 'old_value': 5039.34, 'new_value': 9324.13}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:35,251 - INFO - 开始更新记录 - 表单实例ID: FINST-9EA669D10Q5W3OIICFV4D8G9MI3P3XNCXWOBMJ
2025-06-14 22:35:35,766 - INFO - 更新表单数据成功: FINST-9EA669D10Q5W3OIICFV4D8G9MI3P3XNCXWOBMJ
2025-06-14 22:35:35,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1302.0, 'new_value': 1215.14}, {'field': 'total_amount', 'old_value': 1302.0, 'new_value': 1215.14}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:35,766 - INFO - 开始更新记录 - 表单实例ID: FINST-9EA669D10Q5W3OIICFV4D8G9MI3P3XNCXWOBMH1
2025-06-14 22:35:36,204 - INFO - 更新表单数据成功: FINST-9EA669D10Q5W3OIICFV4D8G9MI3P3XNCXWOBMH1
2025-06-14 22:35:36,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 532.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 9608.0, 'new_value': 10141.18}, {'field': 'total_amount', 'old_value': 10140.0, 'new_value': 10141.18}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:36,204 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66J712B2WVIVJFLE1845FMPB03KPGXWOBM3P
2025-06-14 22:35:36,672 - INFO - 更新表单数据成功: FINST-LLF66J712B2WVIVJFLE1845FMPB03KPGXWOBM3P
2025-06-14 22:35:36,672 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 674.56, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 27047.12, 'new_value': 29273.56}, {'field': 'total_amount', 'old_value': 27721.68, 'new_value': 29273.56}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:36,672 - INFO - 日期 2025-05-21 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-06-14 22:35:36,672 - INFO - 开始处理日期: 2025-05-22
2025-06-14 22:35:36,672 - INFO - Request Parameters - Page 1:
2025-06-14 22:35:36,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:36,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:37,407 - INFO - Response - Page 1:
2025-06-14 22:35:37,407 - INFO - 第 1 页获取到 46 条记录
2025-06-14 22:35:37,907 - INFO - 查询完成，共获取到 46 条记录
2025-06-14 22:35:37,907 - INFO - 获取到 46 条表单数据
2025-06-14 22:35:37,907 - INFO - 当前日期 2025-05-22 有 3 条MySQL数据需要处理
2025-06-14 22:35:37,907 - INFO - 开始批量插入 3 条新记录
2025-06-14 22:35:38,110 - INFO - 批量插入响应状态码: 200
2025-06-14 22:35:38,110 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:35:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '49A84A01-2E7D-7793-94BD-B7BFECC80B66', 'x-acs-trace-id': '2b54e5a4c0d60fe5d4ba8d80ea26f32c', 'etag': '15bfOgvtPS9bxfk2qFwDwtQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:35:38,110 - INFO - 批量插入响应体: {'result': ['FINST-L8D665C13T9WWFWWC58W69P06JX42TGN9CWBM15', 'FINST-L8D665C13T9WWFWWC58W69P06JX42TGN9CWBM25', 'FINST-L8D665C13T9WWFWWC58W69P06JX42TGN9CWBM35']}
2025-06-14 22:35:38,110 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-14 22:35:38,110 - INFO - 成功插入的数据ID: ['FINST-L8D665C13T9WWFWWC58W69P06JX42TGN9CWBM15', 'FINST-L8D665C13T9WWFWWC58W69P06JX42TGN9CWBM25', 'FINST-L8D665C13T9WWFWWC58W69P06JX42TGN9CWBM35']
2025-06-14 22:35:43,125 - INFO - 批量插入完成，共 3 条记录
2025-06-14 22:35:43,125 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-06-14 22:35:43,125 - INFO - 开始处理日期: 2025-05-23
2025-06-14 22:35:43,125 - INFO - Request Parameters - Page 1:
2025-06-14 22:35:43,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:43,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:43,812 - INFO - Response - Page 1:
2025-06-14 22:35:43,812 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:35:44,328 - INFO - Request Parameters - Page 2:
2025-06-14 22:35:44,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:44,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:45,062 - INFO - Response - Page 2:
2025-06-14 22:35:45,062 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:35:45,578 - INFO - Request Parameters - Page 3:
2025-06-14 22:35:45,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:45,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:46,187 - INFO - Response - Page 3:
2025-06-14 22:35:46,187 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:35:46,703 - INFO - Request Parameters - Page 4:
2025-06-14 22:35:46,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:46,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:47,374 - INFO - Response - Page 4:
2025-06-14 22:35:47,374 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:35:47,890 - INFO - Request Parameters - Page 5:
2025-06-14 22:35:47,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:47,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:48,562 - INFO - Response - Page 5:
2025-06-14 22:35:48,562 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:35:49,077 - INFO - Request Parameters - Page 6:
2025-06-14 22:35:49,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:49,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:49,765 - INFO - Response - Page 6:
2025-06-14 22:35:49,765 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:35:50,280 - INFO - Request Parameters - Page 7:
2025-06-14 22:35:50,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:50,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:50,874 - INFO - Response - Page 7:
2025-06-14 22:35:50,874 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:35:51,390 - INFO - Request Parameters - Page 8:
2025-06-14 22:35:51,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:51,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:52,015 - INFO - Response - Page 8:
2025-06-14 22:35:52,015 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:35:52,515 - INFO - Request Parameters - Page 9:
2025-06-14 22:35:52,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:52,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:53,139 - INFO - Response - Page 9:
2025-06-14 22:35:53,139 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:35:53,655 - INFO - Request Parameters - Page 10:
2025-06-14 22:35:53,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:53,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:54,311 - INFO - Response - Page 10:
2025-06-14 22:35:54,311 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:35:54,811 - INFO - Request Parameters - Page 11:
2025-06-14 22:35:54,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:54,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:55,483 - INFO - Response - Page 11:
2025-06-14 22:35:55,483 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:35:55,999 - INFO - Request Parameters - Page 12:
2025-06-14 22:35:55,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:55,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:56,686 - INFO - Response - Page 12:
2025-06-14 22:35:56,686 - INFO - 第 12 页获取到 50 条记录
2025-06-14 22:35:57,186 - INFO - Request Parameters - Page 13:
2025-06-14 22:35:57,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:57,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 13, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:35:57,608 - INFO - Response - Page 13:
2025-06-14 22:35:57,608 - INFO - 第 13 页获取到 3 条记录
2025-06-14 22:35:58,123 - INFO - 查询完成，共获取到 603 条记录
2025-06-14 22:35:58,123 - INFO - 获取到 603 条表单数据
2025-06-14 22:35:58,123 - INFO - 当前日期 2025-05-23 有 4 条MySQL数据需要处理
2025-06-14 22:35:58,123 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B1VB2WLLBFCQ7CW4CVDC722DKBYWOBMSC
2025-06-14 22:35:58,608 - INFO - 更新表单数据成功: FINST-LR5668B1VB2WLLBFCQ7CW4CVDC722DKBYWOBMSC
2025-06-14 22:35:58,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6062.0, 'new_value': 5786.0}, {'field': 'total_amount', 'old_value': 6062.0, 'new_value': 5786.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:58,608 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1FO5W2XOPBVFY0B9OT2C633SNYWOBM31
2025-06-14 22:35:59,045 - INFO - 更新表单数据成功: FINST-7PF66BA1FO5W2XOPBVFY0B9OT2C633SNYWOBM31
2025-06-14 22:35:59,045 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6608.15, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 6036.0, 'new_value': 17262.88}, {'field': 'total_amount', 'old_value': 12644.15, 'new_value': 17262.88}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:59,045 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781NP5WM45EAGKS16MYHUYV3D18ZWOBM6
2025-06-14 22:35:59,467 - INFO - 更新表单数据成功: FINST-AI866781NP5WM45EAGKS16MYHUYV3D18ZWOBM6
2025-06-14 22:35:59,467 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4031.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 46888.68, 'new_value': 50960.6}, {'field': 'total_amount', 'old_value': 50919.68, 'new_value': 50960.6}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:59,467 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781NP5WM45EAGKS16MYHUYV3D18ZWOBM8
2025-06-14 22:35:59,951 - INFO - 更新表单数据成功: FINST-AI866781NP5WM45EAGKS16MYHUYV3D18ZWOBM8
2025-06-14 22:35:59,951 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 517.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 14781.0, 'new_value': 15299.01}, {'field': 'total_amount', 'old_value': 15298.0, 'new_value': 15299.01}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:35:59,951 - INFO - 日期 2025-05-23 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-06-14 22:35:59,951 - INFO - 开始处理日期: 2025-05-24
2025-06-14 22:35:59,951 - INFO - Request Parameters - Page 1:
2025-06-14 22:35:59,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:35:59,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:00,623 - INFO - Response - Page 1:
2025-06-14 22:36:00,623 - INFO - 第 1 页获取到 48 条记录
2025-06-14 22:36:01,139 - INFO - 查询完成，共获取到 48 条记录
2025-06-14 22:36:01,139 - INFO - 获取到 48 条表单数据
2025-06-14 22:36:01,139 - INFO - 当前日期 2025-05-24 有 3 条MySQL数据需要处理
2025-06-14 22:36:01,139 - INFO - 开始批量插入 3 条新记录
2025-06-14 22:36:01,279 - INFO - 批量插入响应状态码: 200
2025-06-14 22:36:01,279 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:36:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '707DDFD3-6683-779C-8193-6494BCE06C9B', 'x-acs-trace-id': '8b5195b2f4507854050862740452114c', 'etag': '17qRWVLhnCR3ChtcrqVgJbQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:36:01,279 - INFO - 批量插入响应体: {'result': ['FINST-A17661C1RIAWCVM39Z2UZCHMYK072QC5ACWBMX2', 'FINST-A17661C1RIAWCVM39Z2UZCHMYK072QC5ACWBMY2', 'FINST-A17661C1RIAWCVM39Z2UZCHMYK072QC5ACWBMZ2']}
2025-06-14 22:36:01,279 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-14 22:36:01,279 - INFO - 成功插入的数据ID: ['FINST-A17661C1RIAWCVM39Z2UZCHMYK072QC5ACWBMX2', 'FINST-A17661C1RIAWCVM39Z2UZCHMYK072QC5ACWBMY2', 'FINST-A17661C1RIAWCVM39Z2UZCHMYK072QC5ACWBMZ2']
2025-06-14 22:36:06,294 - INFO - 批量插入完成，共 3 条记录
2025-06-14 22:36:06,294 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-06-14 22:36:06,294 - INFO - 开始处理日期: 2025-05-25
2025-06-14 22:36:06,294 - INFO - Request Parameters - Page 1:
2025-06-14 22:36:06,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:06,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:06,904 - INFO - Response - Page 1:
2025-06-14 22:36:06,904 - INFO - 第 1 页获取到 45 条记录
2025-06-14 22:36:07,419 - INFO - 查询完成，共获取到 45 条记录
2025-06-14 22:36:07,419 - INFO - 获取到 45 条表单数据
2025-06-14 22:36:07,419 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-06-14 22:36:07,419 - INFO - 开始批量插入 2 条新记录
2025-06-14 22:36:07,591 - INFO - 批量插入响应状态码: 200
2025-06-14 22:36:07,591 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:36:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '61167927-48CF-7C31-95F5-5C98BD8B598B', 'x-acs-trace-id': '9f70c17bf503c5a49267e8ea57efb02c', 'etag': '1SwYkB+9wnwJosLB58mJM9Q8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:36:07,591 - INFO - 批量插入响应体: {'result': ['FINST-IOC66G71MU9WBF159GH7GAR9E5V82W7AACWBMFD', 'FINST-IOC66G71MU9WBF159GH7GAR9E5V82W7AACWBMGD']}
2025-06-14 22:36:07,591 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-14 22:36:07,591 - INFO - 成功插入的数据ID: ['FINST-IOC66G71MU9WBF159GH7GAR9E5V82W7AACWBMFD', 'FINST-IOC66G71MU9WBF159GH7GAR9E5V82W7AACWBMGD']
2025-06-14 22:36:12,606 - INFO - 批量插入完成，共 2 条记录
2025-06-14 22:36:12,606 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-14 22:36:12,606 - INFO - 开始处理日期: 2025-05-26
2025-06-14 22:36:12,606 - INFO - Request Parameters - Page 1:
2025-06-14 22:36:12,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:12,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:13,247 - INFO - Response - Page 1:
2025-06-14 22:36:13,247 - INFO - 第 1 页获取到 46 条记录
2025-06-14 22:36:13,747 - INFO - 查询完成，共获取到 46 条记录
2025-06-14 22:36:13,747 - INFO - 获取到 46 条表单数据
2025-06-14 22:36:13,747 - INFO - 当前日期 2025-05-26 有 4 条MySQL数据需要处理
2025-06-14 22:36:13,747 - INFO - 开始批量插入 4 条新记录
2025-06-14 22:36:13,903 - INFO - 批量插入响应状态码: 200
2025-06-14 22:36:13,903 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:36:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E7CB442B-9EAB-7ED7-9B44-7D8549255FAA', 'x-acs-trace-id': 'c311853f4175dfddec995a72a3a4daa5', 'etag': '2FbOIGZyxHuGYCDcKVbKY7A4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:36:13,903 - INFO - 批量插入响应体: {'result': ['FINST-AJF66F71TQ9WZRGABBC8Z57KR8243I3FACWBMXB', 'FINST-AJF66F71TQ9WZRGABBC8Z57KR8243I3FACWBMYB', 'FINST-AJF66F71TQ9WZRGABBC8Z57KR8243I3FACWBMZB', 'FINST-AJF66F71TQ9WZRGABBC8Z57KR8243I3FACWBM0C']}
2025-06-14 22:36:13,903 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-14 22:36:13,903 - INFO - 成功插入的数据ID: ['FINST-AJF66F71TQ9WZRGABBC8Z57KR8243I3FACWBMXB', 'FINST-AJF66F71TQ9WZRGABBC8Z57KR8243I3FACWBMYB', 'FINST-AJF66F71TQ9WZRGABBC8Z57KR8243I3FACWBMZB', 'FINST-AJF66F71TQ9WZRGABBC8Z57KR8243I3FACWBM0C']
2025-06-14 22:36:18,918 - INFO - 批量插入完成，共 4 条记录
2025-06-14 22:36:18,918 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-06-14 22:36:18,918 - INFO - 开始处理日期: 2025-05-27
2025-06-14 22:36:18,918 - INFO - Request Parameters - Page 1:
2025-06-14 22:36:18,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:18,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:19,590 - INFO - Response - Page 1:
2025-06-14 22:36:19,590 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:36:20,090 - INFO - Request Parameters - Page 2:
2025-06-14 22:36:20,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:20,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:20,746 - INFO - Response - Page 2:
2025-06-14 22:36:20,746 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:36:21,246 - INFO - Request Parameters - Page 3:
2025-06-14 22:36:21,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:21,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:21,855 - INFO - Response - Page 3:
2025-06-14 22:36:21,855 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:36:22,355 - INFO - Request Parameters - Page 4:
2025-06-14 22:36:22,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:22,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:23,012 - INFO - Response - Page 4:
2025-06-14 22:36:23,012 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:36:23,512 - INFO - Request Parameters - Page 5:
2025-06-14 22:36:23,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:23,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:24,137 - INFO - Response - Page 5:
2025-06-14 22:36:24,137 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:36:24,652 - INFO - Request Parameters - Page 6:
2025-06-14 22:36:24,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:24,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:25,324 - INFO - Response - Page 6:
2025-06-14 22:36:25,324 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:36:25,839 - INFO - Request Parameters - Page 7:
2025-06-14 22:36:25,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:25,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:26,480 - INFO - Response - Page 7:
2025-06-14 22:36:26,496 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:36:26,996 - INFO - Request Parameters - Page 8:
2025-06-14 22:36:26,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:26,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:27,667 - INFO - Response - Page 8:
2025-06-14 22:36:27,667 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:36:28,167 - INFO - Request Parameters - Page 9:
2025-06-14 22:36:28,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:28,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:28,792 - INFO - Response - Page 9:
2025-06-14 22:36:28,792 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:36:29,292 - INFO - Request Parameters - Page 10:
2025-06-14 22:36:29,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:29,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:29,917 - INFO - Response - Page 10:
2025-06-14 22:36:29,917 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:36:30,417 - INFO - Request Parameters - Page 11:
2025-06-14 22:36:30,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:30,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:31,073 - INFO - Response - Page 11:
2025-06-14 22:36:31,073 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:36:31,589 - INFO - Request Parameters - Page 12:
2025-06-14 22:36:31,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:31,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:32,151 - INFO - Response - Page 12:
2025-06-14 22:36:32,151 - INFO - 第 12 页获取到 15 条记录
2025-06-14 22:36:32,667 - INFO - 查询完成，共获取到 565 条记录
2025-06-14 22:36:32,667 - INFO - 获取到 565 条表单数据
2025-06-14 22:36:32,667 - INFO - 当前日期 2025-05-27 有 2 条MySQL数据需要处理
2025-06-14 22:36:32,667 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66PA1SD3WQL2DAMS308XIP7LZ2P3E0XOBMA61
2025-06-14 22:36:33,073 - INFO - 更新表单数据成功: FINST-MLF66PA1SD3WQL2DAMS308XIP7LZ2P3E0XOBMA61
2025-06-14 22:36:33,073 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 591.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 7715.0, 'new_value': 8306.57}, {'field': 'total_amount', 'old_value': 8306.0, 'new_value': 8306.57}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:36:33,073 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66PA1SD3WQL2DAMS308XIP7LZ2P3E0XOBMG61
2025-06-14 22:36:33,667 - INFO - 更新表单数据成功: FINST-MLF66PA1SD3WQL2DAMS308XIP7LZ2P3E0XOBMG61
2025-06-14 22:36:33,667 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4975.07, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 970.0, 'new_value': 8882.06}, {'field': 'total_amount', 'old_value': 5945.07, 'new_value': 8882.06}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:36:33,667 - INFO - 日期 2025-05-27 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-06-14 22:36:33,667 - INFO - 开始处理日期: 2025-05-28
2025-06-14 22:36:33,667 - INFO - Request Parameters - Page 1:
2025-06-14 22:36:33,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:33,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:34,323 - INFO - Response - Page 1:
2025-06-14 22:36:34,323 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:36:34,839 - INFO - Request Parameters - Page 2:
2025-06-14 22:36:34,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:34,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:35,495 - INFO - Response - Page 2:
2025-06-14 22:36:35,495 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:36:35,995 - INFO - Request Parameters - Page 3:
2025-06-14 22:36:35,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:35,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:36,620 - INFO - Response - Page 3:
2025-06-14 22:36:36,620 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:36:37,135 - INFO - Request Parameters - Page 4:
2025-06-14 22:36:37,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:37,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:37,745 - INFO - Response - Page 4:
2025-06-14 22:36:37,745 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:36:38,260 - INFO - Request Parameters - Page 5:
2025-06-14 22:36:38,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:38,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:38,932 - INFO - Response - Page 5:
2025-06-14 22:36:38,932 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:36:39,448 - INFO - Request Parameters - Page 6:
2025-06-14 22:36:39,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:39,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:40,119 - INFO - Response - Page 6:
2025-06-14 22:36:40,119 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:36:40,635 - INFO - Request Parameters - Page 7:
2025-06-14 22:36:40,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:40,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:41,307 - INFO - Response - Page 7:
2025-06-14 22:36:41,307 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:36:41,822 - INFO - Request Parameters - Page 8:
2025-06-14 22:36:41,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:41,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:42,463 - INFO - Response - Page 8:
2025-06-14 22:36:42,463 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:36:42,963 - INFO - Request Parameters - Page 9:
2025-06-14 22:36:42,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:42,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:43,619 - INFO - Response - Page 9:
2025-06-14 22:36:43,619 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:36:44,135 - INFO - Request Parameters - Page 10:
2025-06-14 22:36:44,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:44,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:44,791 - INFO - Response - Page 10:
2025-06-14 22:36:44,791 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:36:45,306 - INFO - Request Parameters - Page 11:
2025-06-14 22:36:45,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:45,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:45,931 - INFO - Response - Page 11:
2025-06-14 22:36:45,931 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:36:46,431 - INFO - Request Parameters - Page 12:
2025-06-14 22:36:46,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:46,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:46,884 - INFO - Response - Page 12:
2025-06-14 22:36:46,884 - INFO - 第 12 页获取到 14 条记录
2025-06-14 22:36:47,384 - INFO - 查询完成，共获取到 564 条记录
2025-06-14 22:36:47,384 - INFO - 获取到 564 条表单数据
2025-06-14 22:36:47,384 - INFO - 当前日期 2025-05-28 有 3 条MySQL数据需要处理
2025-06-14 22:36:47,384 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1GS4W5YBS7VEANBJQRYZ62B1N1XOBMJ61
2025-06-14 22:36:47,931 - INFO - 更新表单数据成功: FINST-V7966QC1GS4W5YBS7VEANBJQRYZ62B1N1XOBMJ61
2025-06-14 22:36:47,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5030.62, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2063.0, 'new_value': 9572.62}, {'field': 'total_amount', 'old_value': 7093.62, 'new_value': 9572.62}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:36:47,931 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA11R5W1WD9CPJ1X8LMH0WV2A4V1XOBM9
2025-06-14 22:36:48,384 - INFO - 更新表单数据成功: FINST-F7D66UA11R5W1WD9CPJ1X8LMH0WV2A4V1XOBM9
2025-06-14 22:36:48,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3135.39, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 32902.9, 'new_value': 35947.46}, {'field': 'total_amount', 'old_value': 36038.29, 'new_value': 35947.46}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:36:48,384 - INFO - 开始更新记录 - 表单实例ID: FINST-XO8662C10R5WNYJWAWUV54H8JQ752DB72XOBMF
2025-06-14 22:36:48,790 - INFO - 更新表单数据成功: FINST-XO8662C10R5WNYJWAWUV54H8JQ752DB72XOBMF
2025-06-14 22:36:48,790 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 428.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 12295.0, 'new_value': 12723.74}, {'field': 'total_amount', 'old_value': 12723.0, 'new_value': 12723.74}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:36:48,790 - INFO - 日期 2025-05-28 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:36:48,790 - INFO - 开始处理日期: 2025-05-29
2025-06-14 22:36:48,790 - INFO - Request Parameters - Page 1:
2025-06-14 22:36:48,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:48,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:49,447 - INFO - Response - Page 1:
2025-06-14 22:36:49,447 - INFO - 第 1 页获取到 46 条记录
2025-06-14 22:36:49,947 - INFO - 查询完成，共获取到 46 条记录
2025-06-14 22:36:49,947 - INFO - 获取到 46 条表单数据
2025-06-14 22:36:49,947 - INFO - 当前日期 2025-05-29 有 5 条MySQL数据需要处理
2025-06-14 22:36:49,947 - INFO - 开始批量插入 5 条新记录
2025-06-14 22:36:50,087 - INFO - 批量插入响应状态码: 200
2025-06-14 22:36:50,087 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:36:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '37B42395-9C59-7A08-B971-47C615CDD938', 'x-acs-trace-id': '09cc94b40ea21b1fa64ed65ac98a7caf', 'etag': '27InNX+MCeZbf32Gq6aJP5w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:36:50,087 - INFO - 批量插入响应体: {'result': ['FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBMZ8', 'FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBM09', 'FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBM19', 'FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBM29', 'FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBM39']}
2025-06-14 22:36:50,087 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-06-14 22:36:50,087 - INFO - 成功插入的数据ID: ['FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBMZ8', 'FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBM09', 'FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBM19', 'FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBM29', 'FINST-HJ966H81ZE8WALX7FANOTB253EGQ2N07BCWBM39']
2025-06-14 22:36:55,102 - INFO - 批量插入完成，共 5 条记录
2025-06-14 22:36:55,102 - INFO - 日期 2025-05-29 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-06-14 22:36:55,102 - INFO - 开始处理日期: 2025-05-30
2025-06-14 22:36:55,102 - INFO - Request Parameters - Page 1:
2025-06-14 22:36:55,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:55,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:55,727 - INFO - Response - Page 1:
2025-06-14 22:36:55,727 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:36:56,243 - INFO - Request Parameters - Page 2:
2025-06-14 22:36:56,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:56,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:56,915 - INFO - Response - Page 2:
2025-06-14 22:36:56,915 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:36:57,415 - INFO - Request Parameters - Page 3:
2025-06-14 22:36:57,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:57,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:58,008 - INFO - Response - Page 3:
2025-06-14 22:36:58,008 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:36:58,508 - INFO - Request Parameters - Page 4:
2025-06-14 22:36:58,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:58,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:36:59,149 - INFO - Response - Page 4:
2025-06-14 22:36:59,149 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:36:59,649 - INFO - Request Parameters - Page 5:
2025-06-14 22:36:59,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:36:59,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:00,446 - INFO - Response - Page 5:
2025-06-14 22:37:00,446 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:37:00,961 - INFO - Request Parameters - Page 6:
2025-06-14 22:37:00,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:00,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:01,617 - INFO - Response - Page 6:
2025-06-14 22:37:01,617 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:37:02,117 - INFO - Request Parameters - Page 7:
2025-06-14 22:37:02,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:02,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:02,711 - INFO - Response - Page 7:
2025-06-14 22:37:02,711 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:37:03,226 - INFO - Request Parameters - Page 8:
2025-06-14 22:37:03,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:03,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:03,867 - INFO - Response - Page 8:
2025-06-14 22:37:03,867 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:37:04,383 - INFO - Request Parameters - Page 9:
2025-06-14 22:37:04,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:04,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:05,054 - INFO - Response - Page 9:
2025-06-14 22:37:05,054 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:37:05,554 - INFO - Request Parameters - Page 10:
2025-06-14 22:37:05,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:05,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:06,289 - INFO - Response - Page 10:
2025-06-14 22:37:06,289 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:37:06,804 - INFO - Request Parameters - Page 11:
2025-06-14 22:37:06,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:06,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:07,445 - INFO - Response - Page 11:
2025-06-14 22:37:07,445 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:37:07,945 - INFO - Request Parameters - Page 12:
2025-06-14 22:37:07,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:07,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:08,554 - INFO - Response - Page 12:
2025-06-14 22:37:08,554 - INFO - 第 12 页获取到 23 条记录
2025-06-14 22:37:09,070 - INFO - 查询完成，共获取到 573 条记录
2025-06-14 22:37:09,070 - INFO - 获取到 573 条表单数据
2025-06-14 22:37:09,070 - INFO - 当前日期 2025-05-30 有 3 条MySQL数据需要处理
2025-06-14 22:37:09,070 - INFO - 开始更新记录 - 表单实例ID: FINST-07E66I91UB2WUAQDEUJEI750SYH432C23XOBMV71
2025-06-14 22:37:09,492 - INFO - 更新表单数据成功: FINST-07E66I91UB2WUAQDEUJEI750SYH432C23XOBMV71
2025-06-14 22:37:09,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6432.32, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2554.0, 'new_value': 11066.32}, {'field': 'total_amount', 'old_value': 8986.32, 'new_value': 11066.32}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:37:09,492 - INFO - 开始更新记录 - 表单实例ID: FINST-RI766091TQ5WKHXQDXQX84IZ8RPA3BQQ3XOBM6
2025-06-14 22:37:09,976 - INFO - 更新表单数据成功: FINST-RI766091TQ5WKHXQDXQX84IZ8RPA3BQQ3XOBM6
2025-06-14 22:37:09,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 548.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 9346.0, 'new_value': 9894.42}, {'field': 'total_amount', 'old_value': 9894.0, 'new_value': 9894.42}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:37:09,976 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1GT4WWBXH969LN9MUZLTH2RSU3XOBM1J
2025-06-14 22:37:10,460 - INFO - 更新表单数据成功: FINST-V7966QC1GT4WWBXH969LN9MUZLTH2RSU3XOBM1J
2025-06-14 22:37:10,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1937.0, 'new_value': 1901.5}, {'field': 'total_amount', 'old_value': 1937.0, 'new_value': 1901.5}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:37:10,460 - INFO - 日期 2025-05-30 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-14 22:37:10,460 - INFO - 开始处理日期: 2025-05-31
2025-06-14 22:37:10,460 - INFO - Request Parameters - Page 1:
2025-06-14 22:37:10,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:10,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:11,116 - INFO - Response - Page 1:
2025-06-14 22:37:11,116 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:37:11,632 - INFO - Request Parameters - Page 2:
2025-06-14 22:37:11,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:11,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:12,382 - INFO - Response - Page 2:
2025-06-14 22:37:12,382 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:37:12,897 - INFO - Request Parameters - Page 3:
2025-06-14 22:37:12,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:12,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:13,616 - INFO - Response - Page 3:
2025-06-14 22:37:13,616 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:37:14,132 - INFO - Request Parameters - Page 4:
2025-06-14 22:37:14,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:14,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:14,741 - INFO - Response - Page 4:
2025-06-14 22:37:14,741 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:37:15,241 - INFO - Request Parameters - Page 5:
2025-06-14 22:37:15,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:15,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:15,803 - INFO - Response - Page 5:
2025-06-14 22:37:15,803 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:37:16,319 - INFO - Request Parameters - Page 6:
2025-06-14 22:37:16,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:16,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:16,928 - INFO - Response - Page 6:
2025-06-14 22:37:16,928 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:37:17,444 - INFO - Request Parameters - Page 7:
2025-06-14 22:37:17,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:17,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:18,038 - INFO - Response - Page 7:
2025-06-14 22:37:18,038 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:37:18,538 - INFO - Request Parameters - Page 8:
2025-06-14 22:37:18,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:18,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:19,116 - INFO - Response - Page 8:
2025-06-14 22:37:19,116 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:37:19,631 - INFO - Request Parameters - Page 9:
2025-06-14 22:37:19,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:19,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:20,272 - INFO - Response - Page 9:
2025-06-14 22:37:20,272 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:37:20,787 - INFO - Request Parameters - Page 10:
2025-06-14 22:37:20,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:20,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:21,412 - INFO - Response - Page 10:
2025-06-14 22:37:21,412 - INFO - 第 10 页获取到 50 条记录
2025-06-14 22:37:21,928 - INFO - Request Parameters - Page 11:
2025-06-14 22:37:21,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:21,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:22,568 - INFO - Response - Page 11:
2025-06-14 22:37:22,568 - INFO - 第 11 页获取到 50 条记录
2025-06-14 22:37:23,084 - INFO - Request Parameters - Page 12:
2025-06-14 22:37:23,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:23,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:23,709 - INFO - Response - Page 12:
2025-06-14 22:37:23,709 - INFO - 第 12 页获取到 43 条记录
2025-06-14 22:37:24,224 - INFO - 查询完成，共获取到 593 条记录
2025-06-14 22:37:24,224 - INFO - 获取到 593 条表单数据
2025-06-14 22:37:24,224 - INFO - 当前日期 2025-05-31 有 5 条MySQL数据需要处理
2025-06-14 22:37:24,224 - INFO - 开始更新记录 - 表单实例ID: FINST-49866E71DQ5WAR0X6OZOW61S3LMH3DMR4XOBMR
2025-06-14 22:37:24,740 - INFO - 更新表单数据成功: FINST-49866E71DQ5WAR0X6OZOW61S3LMH3DMR4XOBMR
2025-06-14 22:37:24,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7726.35, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3343.0, 'new_value': 12616.77}, {'field': 'total_amount', 'old_value': 11069.35, 'new_value': 12616.77}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:37:24,740 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF662C1VP5WZXGBEWW4V6I9KF0K3GPZ4XOBM9
2025-06-14 22:37:25,209 - INFO - 更新表单数据成功: FINST-2PF662C1VP5WZXGBEWW4V6I9KF0K3GPZ4XOBM9
2025-06-14 22:37:25,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2851.21, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 58352.52, 'new_value': 60516.38}, {'field': 'total_amount', 'old_value': 61203.73, 'new_value': 60516.38}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:37:25,209 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91J82W451M994GBAD914CC2ER35XOBM7J1
2025-06-14 22:37:25,740 - INFO - 更新表单数据成功: FINST-1OC66A91J82W451M994GBAD914CC2ER35XOBM7J1
2025-06-14 22:37:25,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 471.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 11411.0, 'new_value': 11882.2}, {'field': 'total_amount', 'old_value': 11882.0, 'new_value': 11882.2}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:37:25,740 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1FC2WFC9NC1Y0D5JHGZSR2XT75XOBME5
2025-06-14 22:37:26,209 - INFO - 更新表单数据成功: FINST-7PF66MD1FC2WFC9NC1Y0D5JHGZSR2XT75XOBME5
2025-06-14 22:37:26,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 885.0, 'new_value': 844.0}, {'field': 'total_amount', 'old_value': 885.0, 'new_value': 844.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-14 22:37:26,209 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1DQ5WG01NF34IV5J9KQWA361K5XOBMR
2025-06-14 22:37:26,584 - INFO - 更新表单数据成功: FINST-1PF66VA1DQ5WG01NF34IV5J9KQWA361K5XOBMR
2025-06-14 22:37:26,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11693.0, 'new_value': 19169.0}, {'field': 'total_amount', 'old_value': 11693.0, 'new_value': 19169.0}]
2025-06-14 22:37:26,584 - INFO - 日期 2025-05-31 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-14 22:37:26,584 - INFO - 开始处理日期: 2025-06-13
2025-06-14 22:37:26,584 - INFO - Request Parameters - Page 1:
2025-06-14 22:37:26,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:26,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:27,240 - INFO - Response - Page 1:
2025-06-14 22:37:27,240 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:37:27,740 - INFO - Request Parameters - Page 2:
2025-06-14 22:37:27,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:27,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:28,427 - INFO - Response - Page 2:
2025-06-14 22:37:28,427 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:37:28,927 - INFO - Request Parameters - Page 3:
2025-06-14 22:37:28,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:28,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:29,568 - INFO - Response - Page 3:
2025-06-14 22:37:29,568 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:37:30,083 - INFO - Request Parameters - Page 4:
2025-06-14 22:37:30,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:30,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:30,739 - INFO - Response - Page 4:
2025-06-14 22:37:30,739 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:37:31,240 - INFO - Request Parameters - Page 5:
2025-06-14 22:37:31,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:31,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:31,864 - INFO - Response - Page 5:
2025-06-14 22:37:31,864 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:37:32,364 - INFO - Request Parameters - Page 6:
2025-06-14 22:37:32,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:32,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:33,021 - INFO - Response - Page 6:
2025-06-14 22:37:33,021 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:37:33,536 - INFO - Request Parameters - Page 7:
2025-06-14 22:37:33,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:33,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:34,130 - INFO - Response - Page 7:
2025-06-14 22:37:34,130 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:37:34,645 - INFO - Request Parameters - Page 8:
2025-06-14 22:37:34,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:34,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:35,302 - INFO - Response - Page 8:
2025-06-14 22:37:35,302 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:37:35,817 - INFO - Request Parameters - Page 9:
2025-06-14 22:37:35,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:35,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:36,505 - INFO - Response - Page 9:
2025-06-14 22:37:36,505 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:37:37,004 - INFO - Request Parameters - Page 10:
2025-06-14 22:37:37,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:37,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:37,583 - INFO - Response - Page 10:
2025-06-14 22:37:37,583 - INFO - 第 10 页获取到 24 条记录
2025-06-14 22:37:38,083 - INFO - 查询完成，共获取到 474 条记录
2025-06-14 22:37:38,083 - INFO - 获取到 474 条表单数据
2025-06-14 22:37:38,083 - INFO - 当前日期 2025-06-13 有 130 条MySQL数据需要处理
2025-06-14 22:37:38,083 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 22:37:38,083 - INFO - 开始处理日期: 2025-06-14
2025-06-14 22:37:38,083 - INFO - Request Parameters - Page 1:
2025-06-14 22:37:38,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:37:38,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:37:38,489 - INFO - Response - Page 1:
2025-06-14 22:37:38,489 - INFO - 第 1 页获取到 1 条记录
2025-06-14 22:37:39,004 - INFO - 查询完成，共获取到 1 条记录
2025-06-14 22:37:39,004 - INFO - 获取到 1 条表单数据
2025-06-14 22:37:39,004 - INFO - 当前日期 2025-06-14 有 37 条MySQL数据需要处理
2025-06-14 22:37:39,004 - INFO - 开始批量插入 36 条新记录
2025-06-14 22:37:39,239 - INFO - 批量插入响应状态码: 200
2025-06-14 22:37:39,239 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:37:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1740', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F36A40BC-7598-7049-B6B5-1C3C65D2D9EA', 'x-acs-trace-id': '77f7c42d05fa38bbe5678a03bef9a64c', 'etag': '1ZgTxbOGQCYFL8CLm4iMoQA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:37:39,239 - INFO - 批量插入响应体: {'result': ['FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM02', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM12', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM22', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM32', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM42', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM52', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM62', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM72', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM82', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM92', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMA2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMB2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMC2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMD2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBME2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMF2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMG2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMH2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMI2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMJ2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMK2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBML2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMM2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMN2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMO2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMP2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMQ2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMR2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMS2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMT2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMU2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMV2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMW2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMX2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMY2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMZ2']}
2025-06-14 22:37:39,239 - INFO - 批量插入表单数据成功，批次 1，共 36 条记录
2025-06-14 22:37:39,239 - INFO - 成功插入的数据ID: ['FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM02', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM12', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM22', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM32', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM42', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM52', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM62', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM72', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM82', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBM92', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMA2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMB2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMC2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMD2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBME2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMF2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMG2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMH2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMI2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMJ2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMK2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBML2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMM2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMN2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMO2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMP2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMQ2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMR2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMS2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMT2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMU2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMV2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMW2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMX2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMY2', 'FINST-XO8662C1MJAWTJ9Y9DS6DBUXEXEP3XX8CCWBMZ2']
2025-06-14 22:37:44,254 - INFO - 批量插入完成，共 36 条记录
2025-06-14 22:37:44,254 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 36 条，错误: 0 条
2025-06-14 22:37:44,254 - INFO - 数据同步完成！更新: 75 条，插入: 61 条，错误: 1 条
2025-06-14 22:38:44,264 - INFO - 开始同步昨天与今天的销售数据: 2025-06-13 至 2025-06-14
2025-06-14 22:38:44,264 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-14 22:38:44,264 - INFO - 查询参数: ('2025-06-13', '2025-06-14')
2025-06-14 22:38:44,389 - INFO - MySQL查询成功，时间段: 2025-06-13 至 2025-06-14，共获取 530 条记录
2025-06-14 22:38:44,389 - INFO - 获取到 2 个日期需要处理: ['2025-06-13', '2025-06-14']
2025-06-14 22:38:44,404 - INFO - 开始处理日期: 2025-06-13
2025-06-14 22:38:44,404 - INFO - Request Parameters - Page 1:
2025-06-14 22:38:44,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:44,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:45,060 - INFO - Response - Page 1:
2025-06-14 22:38:45,076 - INFO - 第 1 页获取到 50 条记录
2025-06-14 22:38:45,592 - INFO - Request Parameters - Page 2:
2025-06-14 22:38:45,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:45,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:46,263 - INFO - Response - Page 2:
2025-06-14 22:38:46,263 - INFO - 第 2 页获取到 50 条记录
2025-06-14 22:38:46,779 - INFO - Request Parameters - Page 3:
2025-06-14 22:38:46,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:46,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:47,466 - INFO - Response - Page 3:
2025-06-14 22:38:47,466 - INFO - 第 3 页获取到 50 条记录
2025-06-14 22:38:47,966 - INFO - Request Parameters - Page 4:
2025-06-14 22:38:47,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:47,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:48,560 - INFO - Response - Page 4:
2025-06-14 22:38:48,560 - INFO - 第 4 页获取到 50 条记录
2025-06-14 22:38:49,076 - INFO - Request Parameters - Page 5:
2025-06-14 22:38:49,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:49,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:49,747 - INFO - Response - Page 5:
2025-06-14 22:38:49,747 - INFO - 第 5 页获取到 50 条记录
2025-06-14 22:38:50,263 - INFO - Request Parameters - Page 6:
2025-06-14 22:38:50,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:50,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:50,919 - INFO - Response - Page 6:
2025-06-14 22:38:50,919 - INFO - 第 6 页获取到 50 条记录
2025-06-14 22:38:51,435 - INFO - Request Parameters - Page 7:
2025-06-14 22:38:51,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:51,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:52,060 - INFO - Response - Page 7:
2025-06-14 22:38:52,060 - INFO - 第 7 页获取到 50 条记录
2025-06-14 22:38:52,560 - INFO - Request Parameters - Page 8:
2025-06-14 22:38:52,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:52,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:53,185 - INFO - Response - Page 8:
2025-06-14 22:38:53,185 - INFO - 第 8 页获取到 50 条记录
2025-06-14 22:38:53,700 - INFO - Request Parameters - Page 9:
2025-06-14 22:38:53,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:53,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:54,356 - INFO - Response - Page 9:
2025-06-14 22:38:54,356 - INFO - 第 9 页获取到 50 条记录
2025-06-14 22:38:54,872 - INFO - Request Parameters - Page 10:
2025-06-14 22:38:54,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:38:54,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:38:55,403 - INFO - Response - Page 10:
2025-06-14 22:38:55,403 - INFO - 第 10 页获取到 24 条记录
2025-06-14 22:38:55,919 - INFO - 查询完成，共获取到 474 条记录
2025-06-14 22:38:55,919 - INFO - 获取到 474 条表单数据
2025-06-14 22:38:55,919 - INFO - 当前日期 2025-06-13 有 476 条MySQL数据需要处理
2025-06-14 22:38:55,934 - INFO - 开始批量插入 2 条新记录
2025-06-14 22:38:56,091 - INFO - 批量插入响应状态码: 200
2025-06-14 22:38:56,091 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 14:38:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4E217BE9-BCAF-71B8-8DAE-E7D326CB4E9D', 'x-acs-trace-id': '36713025634d20e8783fc0b69ab91c55', 'etag': '1naTP44YqXFDxVshkD4VVIA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-14 22:38:56,091 - INFO - 批量插入响应体: {'result': ['FINST-Z7B66WA14T9WQUSZAMFJ78DA14TT3X8WDCWBMQJ', 'FINST-Z7B66WA14T9WQUSZAMFJ78DA14TT3X8WDCWBMRJ']}
2025-06-14 22:38:56,091 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-14 22:38:56,091 - INFO - 成功插入的数据ID: ['FINST-Z7B66WA14T9WQUSZAMFJ78DA14TT3X8WDCWBMQJ', 'FINST-Z7B66WA14T9WQUSZAMFJ78DA14TT3X8WDCWBMRJ']
2025-06-14 22:39:01,106 - INFO - 批量插入完成，共 2 条记录
2025-06-14 22:39:01,106 - INFO - 日期 2025-06-13 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-14 22:39:01,106 - INFO - 开始处理日期: 2025-06-14
2025-06-14 22:39:01,106 - INFO - Request Parameters - Page 1:
2025-06-14 22:39:01,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 22:39:01,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 22:39:01,637 - INFO - Response - Page 1:
2025-06-14 22:39:01,637 - INFO - 第 1 页获取到 37 条记录
2025-06-14 22:39:02,137 - INFO - 查询完成，共获取到 37 条记录
2025-06-14 22:39:02,137 - INFO - 获取到 37 条表单数据
2025-06-14 22:39:02,137 - INFO - 当前日期 2025-06-14 有 37 条MySQL数据需要处理
2025-06-14 22:39:02,137 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 22:39:02,137 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-06-14 22:39:02,137 - INFO - 同步完成
