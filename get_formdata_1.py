# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys
import logging
import json
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

# 设置日志级别为 WARNING，这样就不会显示 DEBUG 信息
logging.getLogger("alibabacloud_credentials").setLevel(logging.WARNING)

from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from get_token import token

# 配置信息
CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P'
}

class YidaFormDataClient:
    def __init__(self):
        # 配置日志文件
        current_date = datetime.now().strftime('%Y%m%d')
        logging.basicConfig(
            filename=f'yida_form_log_{current_date}.txt',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            encoding='utf-8'
        )
        
        self.access_token = token.get_token()
        self.client = self._create_client()

    def _create_client(self) -> dingtalkyida_1_0Client:
        """
        初始化宜搭客户端
        
        Returns:
            dingtalkyida_1_0Client: 宜搭客户端实例
        """
        config = open_api_models.Config(
            protocol='https',
            region_id='central'
        )
        return dingtalkyida_1_0Client(config)

    def get_form_data(self, page_size: int = 10, search_condition: Optional[List[Dict]] = None) -> List[Dict[str, Any]]:
        """
        获取宜搭表单数据
        
        Args:
            page_size: 每页数据条数，默认100
            search_condition: 查询条件，可选
            
        Returns:
            List[Dict[str, Any]]: 表单数据列表
        """
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    form_uuid=CONFIG['FORM_UUID'],
                    search_condition=json.dumps(search_condition) if search_condition else None,
                    system_token=CONFIG['SYSTEM_TOKEN'],
                    page_size=page_size,
                    user_id=CONFIG['USER_ID'],
                    app_type=CONFIG['APP_TYPE']
                )
                
                # 记录请求参数
                logging.info(f"Request Parameters - Page {current_page}:")
                logging.info(f"Headers: {headers}")
                logging.info(f"Request: {request}")
                
                result = self.client.search_form_data_second_generation_no_table_field_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 记录响应结果
                logging.info(f"Response - Page {current_page}:")
                # logging.info(f"Result: {result.body}")
                
                if not result or not result.body or not result.body.data:
                    break
                
                # 提取指定字段
                for item in result.body.data:
                    filtered_data = {
                        'formInstanceId': item.form_instance_id,
                        'formData': item.form_data
                    }
                    all_data.append(filtered_data)
                
                logging.info(f"第 {current_page} 页获取到 {len(result.body.data)} 条记录")
                
                # 如果获取的数据少于页大小，说明已经是最后一页
                if len(result.body.data) < page_size:
                    break
                    
                current_page += 1
            
            logging.info(f"查询完成，共获取到 {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            error_msg = f"获取表单数据失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            raise Exception(error_msg)

def main():
    try:
        client = YidaFormDataClient()
        
        # 获取昨天的日期
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        
        # 构建日期筛选条件
        start_timestamp = int(datetime.strptime(yesterday, '%Y%m%d').timestamp() * 1000)
        end_timestamp = start_timestamp + 86399000  # 加上23:59:59的毫秒数
        
        search_condition = [{
            "key": "dateField_m9dkdkoz",  # 销售日期字段
            "value": [start_timestamp, end_timestamp],
            "type": "DOUBLE",
            "operator": "between",
            "componentName": "DateField"
        }]
        
        # 获取表单数据
        form_data_list = client.get_form_data(search_condition=search_condition)
        
        if not form_data_list:
            print("未获取到表单数据")
            return
            
        print(f"成功获取到 {len(form_data_list)} 条记录")
        for form_data in form_data_list:
            print(form_data)
            
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()