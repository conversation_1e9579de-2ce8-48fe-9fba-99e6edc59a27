2025-04-30 00:00:03,691 - INFO - =================使用默认全量同步=============
2025-04-30 00:00:04,849 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-30 00:00:04,849 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-30 00:00:04,880 - INFO - 开始处理日期: 2025-01
2025-04-30 00:00:04,880 - INFO - Request Parameters - Page 1:
2025-04-30 00:00:04,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:04,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:06,116 - INFO - Response - Page 1:
2025-04-30 00:00:06,319 - INFO - 第 1 页获取到 100 条记录
2025-04-30 00:00:06,319 - INFO - Request Parameters - Page 2:
2025-04-30 00:00:06,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:06,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:06,851 - INFO - Response - Page 2:
2025-04-30 00:00:07,054 - INFO - 第 2 页获取到 100 条记录
2025-04-30 00:00:07,054 - INFO - Request Parameters - Page 3:
2025-04-30 00:00:07,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:07,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:07,617 - INFO - Response - Page 3:
2025-04-30 00:00:07,820 - INFO - 第 3 页获取到 100 条记录
2025-04-30 00:00:07,820 - INFO - Request Parameters - Page 4:
2025-04-30 00:00:07,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:07,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:08,321 - INFO - Response - Page 4:
2025-04-30 00:00:08,524 - INFO - 第 4 页获取到 100 条记录
2025-04-30 00:00:08,524 - INFO - Request Parameters - Page 5:
2025-04-30 00:00:08,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:08,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:09,165 - INFO - Response - Page 5:
2025-04-30 00:00:09,369 - INFO - 第 5 页获取到 100 条记录
2025-04-30 00:00:09,369 - INFO - Request Parameters - Page 6:
2025-04-30 00:00:09,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:09,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:09,916 - INFO - Response - Page 6:
2025-04-30 00:00:10,119 - INFO - 第 6 页获取到 100 条记录
2025-04-30 00:00:10,119 - INFO - Request Parameters - Page 7:
2025-04-30 00:00:10,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:10,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:10,588 - INFO - Response - Page 7:
2025-04-30 00:00:10,792 - INFO - 第 7 页获取到 82 条记录
2025-04-30 00:00:10,792 - INFO - 查询完成，共获取到 682 条记录
2025-04-30 00:00:10,792 - INFO - 获取到 682 条表单数据
2025-04-30 00:00:10,792 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-30 00:00:10,807 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 00:00:10,807 - INFO - 开始处理日期: 2025-02
2025-04-30 00:00:10,807 - INFO - Request Parameters - Page 1:
2025-04-30 00:00:10,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:10,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:11,292 - INFO - Response - Page 1:
2025-04-30 00:00:11,496 - INFO - 第 1 页获取到 100 条记录
2025-04-30 00:00:11,496 - INFO - Request Parameters - Page 2:
2025-04-30 00:00:11,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:11,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:11,996 - INFO - Response - Page 2:
2025-04-30 00:00:12,199 - INFO - 第 2 页获取到 100 条记录
2025-04-30 00:00:12,199 - INFO - Request Parameters - Page 3:
2025-04-30 00:00:12,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:12,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:12,715 - INFO - Response - Page 3:
2025-04-30 00:00:12,919 - INFO - 第 3 页获取到 100 条记录
2025-04-30 00:00:12,919 - INFO - Request Parameters - Page 4:
2025-04-30 00:00:12,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:12,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:13,404 - INFO - Response - Page 4:
2025-04-30 00:00:13,607 - INFO - 第 4 页获取到 100 条记录
2025-04-30 00:00:13,607 - INFO - Request Parameters - Page 5:
2025-04-30 00:00:13,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:13,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:14,045 - INFO - Response - Page 5:
2025-04-30 00:00:14,248 - INFO - 第 5 页获取到 100 条记录
2025-04-30 00:00:14,248 - INFO - Request Parameters - Page 6:
2025-04-30 00:00:14,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:14,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:14,686 - INFO - Response - Page 6:
2025-04-30 00:00:14,889 - INFO - 第 6 页获取到 100 条记录
2025-04-30 00:00:14,889 - INFO - Request Parameters - Page 7:
2025-04-30 00:00:14,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:14,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:15,359 - INFO - Response - Page 7:
2025-04-30 00:00:15,562 - INFO - 第 7 页获取到 70 条记录
2025-04-30 00:00:15,562 - INFO - 查询完成，共获取到 670 条记录
2025-04-30 00:00:15,562 - INFO - 获取到 670 条表单数据
2025-04-30 00:00:15,562 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-30 00:00:15,577 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 00:00:15,577 - INFO - 开始处理日期: 2025-03
2025-04-30 00:00:15,577 - INFO - Request Parameters - Page 1:
2025-04-30 00:00:15,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:15,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:16,062 - INFO - Response - Page 1:
2025-04-30 00:00:16,266 - INFO - 第 1 页获取到 100 条记录
2025-04-30 00:00:16,266 - INFO - Request Parameters - Page 2:
2025-04-30 00:00:16,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:16,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:16,766 - INFO - Response - Page 2:
2025-04-30 00:00:16,969 - INFO - 第 2 页获取到 100 条记录
2025-04-30 00:00:16,969 - INFO - Request Parameters - Page 3:
2025-04-30 00:00:16,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:16,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:17,486 - INFO - Response - Page 3:
2025-04-30 00:00:17,689 - INFO - 第 3 页获取到 100 条记录
2025-04-30 00:00:17,689 - INFO - Request Parameters - Page 4:
2025-04-30 00:00:17,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:17,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:18,158 - INFO - Response - Page 4:
2025-04-30 00:00:18,361 - INFO - 第 4 页获取到 100 条记录
2025-04-30 00:00:18,361 - INFO - Request Parameters - Page 5:
2025-04-30 00:00:18,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:18,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:18,877 - INFO - Response - Page 5:
2025-04-30 00:00:19,081 - INFO - 第 5 页获取到 100 条记录
2025-04-30 00:00:19,081 - INFO - Request Parameters - Page 6:
2025-04-30 00:00:19,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:19,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:19,503 - INFO - Response - Page 6:
2025-04-30 00:00:19,706 - INFO - 第 6 页获取到 100 条记录
2025-04-30 00:00:19,706 - INFO - Request Parameters - Page 7:
2025-04-30 00:00:19,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:19,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:20,191 - INFO - Response - Page 7:
2025-04-30 00:00:20,394 - INFO - 第 7 页获取到 61 条记录
2025-04-30 00:00:20,394 - INFO - 查询完成，共获取到 661 条记录
2025-04-30 00:00:20,394 - INFO - 获取到 661 条表单数据
2025-04-30 00:00:20,394 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-30 00:00:20,410 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 00:00:20,410 - INFO - 开始处理日期: 2025-04
2025-04-30 00:00:20,410 - INFO - Request Parameters - Page 1:
2025-04-30 00:00:20,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:20,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:20,958 - INFO - Response - Page 1:
2025-04-30 00:00:21,161 - INFO - 第 1 页获取到 100 条记录
2025-04-30 00:00:21,161 - INFO - Request Parameters - Page 2:
2025-04-30 00:00:21,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:21,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:21,583 - INFO - Response - Page 2:
2025-04-30 00:00:21,786 - INFO - 第 2 页获取到 100 条记录
2025-04-30 00:00:21,786 - INFO - Request Parameters - Page 3:
2025-04-30 00:00:21,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:21,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:22,381 - INFO - Response - Page 3:
2025-04-30 00:00:22,584 - INFO - 第 3 页获取到 100 条记录
2025-04-30 00:00:22,584 - INFO - Request Parameters - Page 4:
2025-04-30 00:00:22,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:22,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:23,084 - INFO - Response - Page 4:
2025-04-30 00:00:23,288 - INFO - 第 4 页获取到 100 条记录
2025-04-30 00:00:23,288 - INFO - Request Parameters - Page 5:
2025-04-30 00:00:23,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:23,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:23,741 - INFO - Response - Page 5:
2025-04-30 00:00:23,945 - INFO - 第 5 页获取到 100 条记录
2025-04-30 00:00:23,945 - INFO - Request Parameters - Page 6:
2025-04-30 00:00:23,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:23,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:24,414 - INFO - Response - Page 6:
2025-04-30 00:00:24,617 - INFO - 第 6 页获取到 100 条记录
2025-04-30 00:00:24,617 - INFO - Request Parameters - Page 7:
2025-04-30 00:00:24,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 00:00:24,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 00:00:24,946 - INFO - Response - Page 7:
2025-04-30 00:00:25,149 - INFO - 第 7 页获取到 27 条记录
2025-04-30 00:00:25,149 - INFO - 查询完成，共获取到 627 条记录
2025-04-30 00:00:25,149 - INFO - 获取到 627 条表单数据
2025-04-30 00:00:25,149 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-30 00:00:25,149 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-04-30 00:00:25,587 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-04-30 00:00:25,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8060.0, 'new_value': 11840.0}, {'field': 'total_amount', 'old_value': 73460.0, 'new_value': 77240.0}, {'field': 'order_count', 'old_value': 746, 'new_value': 781}]
2025-04-30 00:00:25,587 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-04-30 00:00:25,978 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-04-30 00:00:25,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4687.35, 'new_value': 4930.35}, {'field': 'offline_amount', 'old_value': 57350.7, 'new_value': 58258.7}, {'field': 'total_amount', 'old_value': 62038.05, 'new_value': 63189.05}, {'field': 'order_count', 'old_value': 3939, 'new_value': 3944}]
2025-04-30 00:00:25,978 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-04-30 00:00:26,384 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-04-30 00:00:26,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149345.0, 'new_value': 159925.0}, {'field': 'total_amount', 'old_value': 204517.0, 'new_value': 215097.0}, {'field': 'order_count', 'old_value': 4002, 'new_value': 4193}]
2025-04-30 00:00:26,384 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-04-30 00:00:26,791 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-04-30 00:00:26,791 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1014019.0, 'new_value': 1052713.0}, {'field': 'total_amount', 'old_value': 1014019.0, 'new_value': 1052713.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 170}]
2025-04-30 00:00:26,791 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-04-30 00:00:27,229 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-04-30 00:00:27,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75493.21, 'new_value': 77705.98}, {'field': 'offline_amount', 'old_value': 108151.53, 'new_value': 112982.0}, {'field': 'total_amount', 'old_value': 183644.74, 'new_value': 190687.98}, {'field': 'order_count', 'old_value': 6380, 'new_value': 6612}]
2025-04-30 00:00:27,229 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-04-30 00:00:27,651 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-04-30 00:00:27,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113644.9, 'new_value': 117277.9}, {'field': 'offline_amount', 'old_value': 182245.25, 'new_value': 190184.25}, {'field': 'total_amount', 'old_value': 295890.15, 'new_value': 307462.15}, {'field': 'order_count', 'old_value': 6448, 'new_value': 6777}]
2025-04-30 00:00:27,651 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-04-30 00:00:28,074 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-04-30 00:00:28,074 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68711.51, 'new_value': 71032.03}, {'field': 'offline_amount', 'old_value': 718716.97, 'new_value': 750958.57}, {'field': 'total_amount', 'old_value': 787428.48, 'new_value': 821990.6}, {'field': 'order_count', 'old_value': 3210, 'new_value': 3333}]
2025-04-30 00:00:28,074 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-04-30 00:00:28,527 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-04-30 00:00:28,527 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23094.71, 'new_value': 23360.25}, {'field': 'offline_amount', 'old_value': 247294.2, 'new_value': 252172.19}, {'field': 'total_amount', 'old_value': 270388.91, 'new_value': 275532.44}, {'field': 'order_count', 'old_value': 1307, 'new_value': 1336}]
2025-04-30 00:00:28,527 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-04-30 00:00:28,949 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-04-30 00:00:28,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14604.3, 'new_value': 14610.3}, {'field': 'total_amount', 'old_value': 14604.3, 'new_value': 14610.3}, {'field': 'order_count', 'old_value': 163, 'new_value': 164}]
2025-04-30 00:00:28,949 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-04-30 00:00:29,465 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-04-30 00:00:29,465 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41429.63, 'new_value': 42502.54}, {'field': 'offline_amount', 'old_value': 303427.98, 'new_value': 311205.58}, {'field': 'total_amount', 'old_value': 344857.61, 'new_value': 353708.12}, {'field': 'order_count', 'old_value': 2396, 'new_value': 2470}]
2025-04-30 00:00:29,465 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-04-30 00:00:29,966 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-04-30 00:00:29,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105044.2, 'new_value': 106837.2}, {'field': 'total_amount', 'old_value': 105044.2, 'new_value': 106837.2}, {'field': 'order_count', 'old_value': 206, 'new_value': 211}]
2025-04-30 00:00:29,966 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-04-30 00:00:30,404 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-04-30 00:00:30,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1746393.89, 'new_value': 1797836.59}, {'field': 'total_amount', 'old_value': 1746393.89, 'new_value': 1797836.59}, {'field': 'order_count', 'old_value': 13855, 'new_value': 14405}]
2025-04-30 00:00:30,404 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-04-30 00:00:30,842 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-04-30 00:00:30,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 301977.04, 'new_value': 328902.04}, {'field': 'total_amount', 'old_value': 301977.04, 'new_value': 328902.04}, {'field': 'order_count', 'old_value': 211, 'new_value': 229}]
2025-04-30 00:00:30,842 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-04-30 00:00:31,280 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-04-30 00:00:31,280 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40342.07, 'new_value': 41677.18}, {'field': 'offline_amount', 'old_value': 536582.89, 'new_value': 547686.84}, {'field': 'total_amount', 'old_value': 576924.96, 'new_value': 589364.02}, {'field': 'order_count', 'old_value': 5139, 'new_value': 5249}]
2025-04-30 00:00:31,280 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MW9
2025-04-30 00:00:31,702 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MW9
2025-04-30 00:00:31,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62865.0, 'new_value': 63039.0}, {'field': 'total_amount', 'old_value': 62865.0, 'new_value': 63039.0}, {'field': 'order_count', 'old_value': 243, 'new_value': 245}]
2025-04-30 00:00:31,702 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-04-30 00:00:32,077 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-04-30 00:00:32,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7291.3, 'new_value': 7491.1}, {'field': 'offline_amount', 'old_value': 41305.0, 'new_value': 41805.0}, {'field': 'total_amount', 'old_value': 48596.3, 'new_value': 49296.1}, {'field': 'order_count', 'old_value': 510, 'new_value': 513}]
2025-04-30 00:00:32,077 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7A
2025-04-30 00:00:32,484 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7A
2025-04-30 00:00:32,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4609.27, 'new_value': 10034.11}, {'field': 'offline_amount', 'old_value': 88616.0, 'new_value': 92914.0}, {'field': 'total_amount', 'old_value': 93225.27, 'new_value': 102948.11}, {'field': 'order_count', 'old_value': 78, 'new_value': 81}]
2025-04-30 00:00:32,484 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-04-30 00:00:32,875 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-04-30 00:00:32,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 330492.27, 'new_value': 343786.27}, {'field': 'total_amount', 'old_value': 330492.27, 'new_value': 343786.27}, {'field': 'order_count', 'old_value': 1684, 'new_value': 1733}]
2025-04-30 00:00:32,875 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-04-30 00:00:33,282 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-04-30 00:00:33,282 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48267.0, 'new_value': 49066.0}, {'field': 'total_amount', 'old_value': 54704.8, 'new_value': 55503.8}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-04-30 00:00:33,282 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-04-30 00:00:33,719 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-04-30 00:00:33,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1493.0}, {'field': 'total_amount', 'old_value': 77019.3, 'new_value': 78512.3}, {'field': 'order_count', 'old_value': 213, 'new_value': 217}]
2025-04-30 00:00:33,719 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-04-30 00:00:34,110 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-04-30 00:00:34,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48410.0, 'new_value': 50914.0}, {'field': 'total_amount', 'old_value': 49040.0, 'new_value': 51544.0}, {'field': 'order_count', 'old_value': 188, 'new_value': 195}]
2025-04-30 00:00:34,110 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-04-30 00:00:34,517 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-04-30 00:00:34,517 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40656.92, 'new_value': 41252.92}, {'field': 'offline_amount', 'old_value': 332099.85, 'new_value': 340023.71}, {'field': 'total_amount', 'old_value': 372756.77, 'new_value': 381276.63}, {'field': 'order_count', 'old_value': 691, 'new_value': 702}]
2025-04-30 00:00:34,517 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-04-30 00:00:34,892 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-04-30 00:00:34,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33675.2, 'new_value': 34329.2}, {'field': 'total_amount', 'old_value': 33675.2, 'new_value': 34329.2}, {'field': 'order_count', 'old_value': 141, 'new_value': 147}]
2025-04-30 00:00:34,892 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-04-30 00:00:35,346 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-04-30 00:00:35,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40179.0, 'new_value': 40623.8}, {'field': 'total_amount', 'old_value': 40576.1, 'new_value': 41020.9}, {'field': 'order_count', 'old_value': 127, 'new_value': 130}]
2025-04-30 00:00:35,346 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-04-30 00:00:35,784 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-04-30 00:00:35,784 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 247712.17, 'new_value': 265104.11}, {'field': 'offline_amount', 'old_value': 158004.0, 'new_value': 158241.6}, {'field': 'total_amount', 'old_value': 405716.17, 'new_value': 423345.71}, {'field': 'order_count', 'old_value': 169, 'new_value': 177}]
2025-04-30 00:00:35,784 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-04-30 00:00:36,253 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-04-30 00:00:36,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54215.8, 'new_value': 54245.7}, {'field': 'total_amount', 'old_value': 54215.8, 'new_value': 54245.7}, {'field': 'order_count', 'old_value': 295, 'new_value': 296}]
2025-04-30 00:00:36,253 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-04-30 00:00:36,722 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-04-30 00:00:36,722 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91402.49, 'new_value': 93584.88}, {'field': 'offline_amount', 'old_value': 176992.98, 'new_value': 180573.63}, {'field': 'total_amount', 'old_value': 268395.47, 'new_value': 274158.51}, {'field': 'order_count', 'old_value': 3767, 'new_value': 3829}]
2025-04-30 00:00:36,722 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO5
2025-04-30 00:00:37,191 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO5
2025-04-30 00:00:37,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5650.2, 'new_value': 5860.9}, {'field': 'total_amount', 'old_value': 31450.2, 'new_value': 31660.9}, {'field': 'order_count', 'old_value': 85, 'new_value': 88}]
2025-04-30 00:00:37,191 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-04-30 00:00:37,582 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-04-30 00:00:37,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36468.0, 'new_value': 39778.0}, {'field': 'total_amount', 'old_value': 37620.0, 'new_value': 40930.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 88}]
2025-04-30 00:00:37,582 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-04-30 00:00:38,005 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-04-30 00:00:38,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51656.82, 'new_value': 52864.82}, {'field': 'total_amount', 'old_value': 51656.82, 'new_value': 52864.82}, {'field': 'order_count', 'old_value': 220, 'new_value': 222}]
2025-04-30 00:00:38,005 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-04-30 00:00:38,458 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-04-30 00:00:38,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2000.0, 'new_value': 2150.0}, {'field': 'offline_amount', 'old_value': 22542.51, 'new_value': 23056.67}, {'field': 'total_amount', 'old_value': 24542.51, 'new_value': 25206.67}, {'field': 'order_count', 'old_value': 470, 'new_value': 482}]
2025-04-30 00:00:38,458 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-04-30 00:00:38,865 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-04-30 00:00:38,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237716.0, 'new_value': 241218.0}, {'field': 'total_amount', 'old_value': 242896.2, 'new_value': 246398.2}, {'field': 'order_count', 'old_value': 2764, 'new_value': 2812}]
2025-04-30 00:00:38,865 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-04-30 00:00:39,303 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-04-30 00:00:39,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 232300.0, 'new_value': 250120.0}, {'field': 'total_amount', 'old_value': 232300.0, 'new_value': 250120.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 69}]
2025-04-30 00:00:39,303 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-04-30 00:00:39,741 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-04-30 00:00:39,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 241138.61, 'new_value': 245500.81}, {'field': 'offline_amount', 'old_value': 88253.62, 'new_value': 89201.12}, {'field': 'total_amount', 'old_value': 329392.23, 'new_value': 334701.93}, {'field': 'order_count', 'old_value': 629, 'new_value': 643}]
2025-04-30 00:00:39,741 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-04-30 00:00:40,147 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-04-30 00:00:40,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122630.6, 'new_value': 126563.4}, {'field': 'total_amount', 'old_value': 126271.9, 'new_value': 130204.7}, {'field': 'order_count', 'old_value': 574, 'new_value': 595}]
2025-04-30 00:00:40,147 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-04-30 00:00:40,632 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-04-30 00:00:40,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6003.0, 'new_value': 55891.0}, {'field': 'total_amount', 'old_value': 420544.0, 'new_value': 470432.0}, {'field': 'order_count', 'old_value': 225, 'new_value': 231}]
2025-04-30 00:00:40,632 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-04-30 00:00:41,039 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-04-30 00:00:41,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69136.0, 'new_value': 70979.0}, {'field': 'total_amount', 'old_value': 69136.0, 'new_value': 70979.0}, {'field': 'order_count', 'old_value': 173, 'new_value': 178}]
2025-04-30 00:00:41,039 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-04-30 00:00:41,492 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-04-30 00:00:41,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243109.0, 'new_value': 252221.0}, {'field': 'total_amount', 'old_value': 267984.0, 'new_value': 277096.0}, {'field': 'order_count', 'old_value': 5440, 'new_value': 5446}]
2025-04-30 00:00:41,492 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML6
2025-04-30 00:00:41,977 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML6
2025-04-30 00:00:41,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104075.0, 'new_value': 107474.0}, {'field': 'total_amount', 'old_value': 104075.0, 'new_value': 107474.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 3418}]
2025-04-30 00:00:41,977 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-04-30 00:00:42,415 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-04-30 00:00:42,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28906.0, 'new_value': 33336.0}, {'field': 'total_amount', 'old_value': 28906.0, 'new_value': 33336.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 52}]
2025-04-30 00:00:42,415 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-04-30 00:00:42,978 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-04-30 00:00:42,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64244.0, 'new_value': 67233.0}, {'field': 'total_amount', 'old_value': 64244.0, 'new_value': 67233.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 106}]
2025-04-30 00:00:42,978 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-04-30 00:00:43,385 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-04-30 00:00:43,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204740.0, 'new_value': 207028.0}, {'field': 'total_amount', 'old_value': 204740.0, 'new_value': 207028.0}, {'field': 'order_count', 'old_value': 283, 'new_value': 288}]
2025-04-30 00:00:43,385 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-04-30 00:00:43,838 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-04-30 00:00:43,838 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 185688.89, 'new_value': 189857.41}, {'field': 'offline_amount', 'old_value': 419186.67, 'new_value': 431186.67}, {'field': 'total_amount', 'old_value': 604875.56, 'new_value': 621044.08}, {'field': 'order_count', 'old_value': 2773, 'new_value': 2924}]
2025-04-30 00:00:43,838 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-04-30 00:00:44,245 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-04-30 00:00:44,245 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 236696.52, 'new_value': 245298.08}, {'field': 'offline_amount', 'old_value': 578445.01, 'new_value': 592983.86}, {'field': 'total_amount', 'old_value': 815141.53, 'new_value': 838281.94}, {'field': 'order_count', 'old_value': 4916, 'new_value': 5047}]
2025-04-30 00:00:44,245 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-04-30 00:00:44,730 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-04-30 00:00:44,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24232.1, 'new_value': 25051.1}, {'field': 'total_amount', 'old_value': 27733.2, 'new_value': 28552.2}, {'field': 'order_count', 'old_value': 319, 'new_value': 323}]
2025-04-30 00:00:44,730 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-04-30 00:00:45,246 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-04-30 00:00:45,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 601076.34, 'new_value': 624494.77}, {'field': 'total_amount', 'old_value': 683944.85, 'new_value': 707363.28}, {'field': 'order_count', 'old_value': 5042, 'new_value': 5237}]
2025-04-30 00:00:45,246 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-04-30 00:00:45,606 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-04-30 00:00:45,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 734027.0, 'new_value': 752724.0}, {'field': 'total_amount', 'old_value': 734027.0, 'new_value': 752724.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 82}]
2025-04-30 00:00:45,606 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-04-30 00:00:46,075 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-04-30 00:00:46,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1184282.0, 'new_value': 1218171.0}, {'field': 'total_amount', 'old_value': 1184282.0, 'new_value': 1218171.0}, {'field': 'order_count', 'old_value': 1249, 'new_value': 1282}]
2025-04-30 00:00:46,075 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-04-30 00:00:46,513 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-04-30 00:00:46,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 608153.0, 'new_value': 613815.0}, {'field': 'total_amount', 'old_value': 608153.0, 'new_value': 613815.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 101}]
2025-04-30 00:00:46,513 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-04-30 00:00:46,966 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-04-30 00:00:46,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80993.5, 'new_value': 86791.5}, {'field': 'total_amount', 'old_value': 105236.24, 'new_value': 111034.24}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-04-30 00:00:46,966 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-04-30 00:00:47,373 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-04-30 00:00:47,373 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97064.8, 'new_value': 99440.8}, {'field': 'offline_amount', 'old_value': 55141.84, 'new_value': 56844.64}, {'field': 'total_amount', 'old_value': 152206.64, 'new_value': 156285.44}, {'field': 'order_count', 'old_value': 1008, 'new_value': 1034}]
2025-04-30 00:00:47,373 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-04-30 00:00:47,842 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-04-30 00:00:47,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250759.19, 'new_value': 256936.85}, {'field': 'total_amount', 'old_value': 250759.19, 'new_value': 256936.85}, {'field': 'order_count', 'old_value': 1528, 'new_value': 1570}]
2025-04-30 00:00:47,842 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-04-30 00:00:48,249 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-04-30 00:00:48,249 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 242245.05, 'new_value': 249430.75}, {'field': 'offline_amount', 'old_value': 57439.67, 'new_value': 58387.63}, {'field': 'total_amount', 'old_value': 299684.72, 'new_value': 307818.38}, {'field': 'order_count', 'old_value': 1202, 'new_value': 1230}]
2025-04-30 00:00:48,249 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-04-30 00:00:48,671 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-04-30 00:00:48,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205037.11, 'new_value': 207447.01}, {'field': 'total_amount', 'old_value': 212549.41, 'new_value': 214959.31}, {'field': 'order_count', 'old_value': 398, 'new_value': 403}]
2025-04-30 00:00:48,671 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-04-30 00:00:49,109 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-04-30 00:00:49,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 551851.28, 'new_value': 586613.28}, {'field': 'total_amount', 'old_value': 557548.28, 'new_value': 592310.28}, {'field': 'order_count', 'old_value': 417, 'new_value': 434}]
2025-04-30 00:00:49,109 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-04-30 00:00:49,578 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-04-30 00:00:49,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51616.93, 'new_value': 53318.5}, {'field': 'offline_amount', 'old_value': 41553.93, 'new_value': 42312.04}, {'field': 'total_amount', 'old_value': 93170.86, 'new_value': 95630.54}, {'field': 'order_count', 'old_value': 7050, 'new_value': 7242}]
2025-04-30 00:00:49,578 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-04-30 00:00:49,985 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-04-30 00:00:49,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 682559.0, 'new_value': 687862.0}, {'field': 'total_amount', 'old_value': 682559.0, 'new_value': 687862.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 152}]
2025-04-30 00:00:49,985 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-04-30 00:00:50,454 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-04-30 00:00:50,454 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125033.66, 'new_value': 129300.56}, {'field': 'offline_amount', 'old_value': 121224.05, 'new_value': 127825.55}, {'field': 'total_amount', 'old_value': 246257.71, 'new_value': 257126.11}, {'field': 'order_count', 'old_value': 5593, 'new_value': 5818}]
2025-04-30 00:00:50,454 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-04-30 00:00:50,892 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-04-30 00:00:50,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244410.0, 'new_value': 249252.32}, {'field': 'total_amount', 'old_value': 244410.0, 'new_value': 249252.32}, {'field': 'order_count', 'old_value': 1021, 'new_value': 1052}]
2025-04-30 00:00:50,892 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-04-30 00:00:51,298 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-04-30 00:00:51,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100329.0, 'new_value': 104311.0}, {'field': 'offline_amount', 'old_value': 119043.0, 'new_value': 123335.0}, {'field': 'total_amount', 'old_value': 219372.0, 'new_value': 227646.0}, {'field': 'order_count', 'old_value': 5633, 'new_value': 5834}]
2025-04-30 00:00:51,298 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-04-30 00:00:51,721 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-04-30 00:00:51,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13440000.0, 'new_value': 14040000.0}, {'field': 'total_amount', 'old_value': 13440001.0, 'new_value': 14040001.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 71}]
2025-04-30 00:00:51,736 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPJ
2025-04-30 00:00:52,174 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPJ
2025-04-30 00:00:52,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22516.0, 'new_value': 23414.0}, {'field': 'total_amount', 'old_value': 38787.0, 'new_value': 39685.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-04-30 00:00:52,174 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXJ
2025-04-30 00:00:52,534 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXJ
2025-04-30 00:00:52,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47926.0, 'new_value': 49486.0}, {'field': 'total_amount', 'old_value': 47926.0, 'new_value': 49486.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 118}]
2025-04-30 00:00:52,534 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-04-30 00:00:53,003 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-04-30 00:00:53,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 302709.36, 'new_value': 310522.4}, {'field': 'offline_amount', 'old_value': 1517846.8, 'new_value': 1553957.29}, {'field': 'total_amount', 'old_value': 1820556.16, 'new_value': 1864479.69}, {'field': 'order_count', 'old_value': 9357, 'new_value': 9588}]
2025-04-30 00:00:53,019 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-04-30 00:00:53,441 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-04-30 00:00:53,441 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147991.71, 'new_value': 151957.92}, {'field': 'total_amount', 'old_value': 147991.71, 'new_value': 151957.92}, {'field': 'order_count', 'old_value': 12127, 'new_value': 12537}]
2025-04-30 00:00:53,441 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH7
2025-04-30 00:00:53,848 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH7
2025-04-30 00:00:53,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68714.9, 'new_value': 77548.9}, {'field': 'total_amount', 'old_value': 68714.9, 'new_value': 77548.9}, {'field': 'order_count', 'old_value': 22, 'new_value': 25}]
2025-04-30 00:00:53,863 - INFO - 日期 2025-04 处理完成 - 更新: 66 条，插入: 0 条，错误: 0 条
2025-04-30 00:00:53,863 - INFO - 数据同步完成！更新: 66 条，插入: 0 条，错误: 0 条
2025-04-30 00:00:53,863 - INFO - =================同步完成====================
2025-04-30 03:00:03,808 - INFO - =================使用默认全量同步=============
2025-04-30 03:00:04,949 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-30 03:00:04,949 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-30 03:00:04,981 - INFO - 开始处理日期: 2025-01
2025-04-30 03:00:04,981 - INFO - Request Parameters - Page 1:
2025-04-30 03:00:04,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:04,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:05,903 - INFO - Response - Page 1:
2025-04-30 03:00:06,107 - INFO - 第 1 页获取到 100 条记录
2025-04-30 03:00:06,107 - INFO - Request Parameters - Page 2:
2025-04-30 03:00:06,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:06,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:06,857 - INFO - Response - Page 2:
2025-04-30 03:00:07,061 - INFO - 第 2 页获取到 100 条记录
2025-04-30 03:00:07,061 - INFO - Request Parameters - Page 3:
2025-04-30 03:00:07,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:07,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:07,671 - INFO - Response - Page 3:
2025-04-30 03:00:07,874 - INFO - 第 3 页获取到 100 条记录
2025-04-30 03:00:07,874 - INFO - Request Parameters - Page 4:
2025-04-30 03:00:07,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:07,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:08,359 - INFO - Response - Page 4:
2025-04-30 03:00:08,562 - INFO - 第 4 页获取到 100 条记录
2025-04-30 03:00:08,562 - INFO - Request Parameters - Page 5:
2025-04-30 03:00:08,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:08,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:09,047 - INFO - Response - Page 5:
2025-04-30 03:00:09,250 - INFO - 第 5 页获取到 100 条记录
2025-04-30 03:00:09,250 - INFO - Request Parameters - Page 6:
2025-04-30 03:00:09,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:09,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:09,813 - INFO - Response - Page 6:
2025-04-30 03:00:10,017 - INFO - 第 6 页获取到 100 条记录
2025-04-30 03:00:10,017 - INFO - Request Parameters - Page 7:
2025-04-30 03:00:10,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:10,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:10,501 - INFO - Response - Page 7:
2025-04-30 03:00:10,705 - INFO - 第 7 页获取到 82 条记录
2025-04-30 03:00:10,705 - INFO - 查询完成，共获取到 682 条记录
2025-04-30 03:00:10,705 - INFO - 获取到 682 条表单数据
2025-04-30 03:00:10,705 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-30 03:00:10,720 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 03:00:10,720 - INFO - 开始处理日期: 2025-02
2025-04-30 03:00:10,720 - INFO - Request Parameters - Page 1:
2025-04-30 03:00:10,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:10,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:11,205 - INFO - Response - Page 1:
2025-04-30 03:00:11,408 - INFO - 第 1 页获取到 100 条记录
2025-04-30 03:00:11,408 - INFO - Request Parameters - Page 2:
2025-04-30 03:00:11,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:11,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:11,862 - INFO - Response - Page 2:
2025-04-30 03:00:12,065 - INFO - 第 2 页获取到 100 条记录
2025-04-30 03:00:12,065 - INFO - Request Parameters - Page 3:
2025-04-30 03:00:12,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:12,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:12,503 - INFO - Response - Page 3:
2025-04-30 03:00:12,707 - INFO - 第 3 页获取到 100 条记录
2025-04-30 03:00:12,707 - INFO - Request Parameters - Page 4:
2025-04-30 03:00:12,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:12,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:13,207 - INFO - Response - Page 4:
2025-04-30 03:00:13,410 - INFO - 第 4 页获取到 100 条记录
2025-04-30 03:00:13,410 - INFO - Request Parameters - Page 5:
2025-04-30 03:00:13,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:13,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:13,911 - INFO - Response - Page 5:
2025-04-30 03:00:14,114 - INFO - 第 5 页获取到 100 条记录
2025-04-30 03:00:14,114 - INFO - Request Parameters - Page 6:
2025-04-30 03:00:14,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:14,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:14,630 - INFO - Response - Page 6:
2025-04-30 03:00:14,834 - INFO - 第 6 页获取到 100 条记录
2025-04-30 03:00:14,834 - INFO - Request Parameters - Page 7:
2025-04-30 03:00:14,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:14,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:15,256 - INFO - Response - Page 7:
2025-04-30 03:00:15,459 - INFO - 第 7 页获取到 70 条记录
2025-04-30 03:00:15,459 - INFO - 查询完成，共获取到 670 条记录
2025-04-30 03:00:15,459 - INFO - 获取到 670 条表单数据
2025-04-30 03:00:15,459 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-30 03:00:15,475 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 03:00:15,475 - INFO - 开始处理日期: 2025-03
2025-04-30 03:00:15,475 - INFO - Request Parameters - Page 1:
2025-04-30 03:00:15,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:15,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:16,210 - INFO - Response - Page 1:
2025-04-30 03:00:16,413 - INFO - 第 1 页获取到 100 条记录
2025-04-30 03:00:16,413 - INFO - Request Parameters - Page 2:
2025-04-30 03:00:16,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:16,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:16,851 - INFO - Response - Page 2:
2025-04-30 03:00:17,054 - INFO - 第 2 页获取到 100 条记录
2025-04-30 03:00:17,054 - INFO - Request Parameters - Page 3:
2025-04-30 03:00:17,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:17,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:17,539 - INFO - Response - Page 3:
2025-04-30 03:00:17,743 - INFO - 第 3 页获取到 100 条记录
2025-04-30 03:00:17,743 - INFO - Request Parameters - Page 4:
2025-04-30 03:00:17,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:17,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:18,290 - INFO - Response - Page 4:
2025-04-30 03:00:18,493 - INFO - 第 4 页获取到 100 条记录
2025-04-30 03:00:18,493 - INFO - Request Parameters - Page 5:
2025-04-30 03:00:18,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:18,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:18,916 - INFO - Response - Page 5:
2025-04-30 03:00:19,119 - INFO - 第 5 页获取到 100 条记录
2025-04-30 03:00:19,119 - INFO - Request Parameters - Page 6:
2025-04-30 03:00:19,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:19,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:19,588 - INFO - Response - Page 6:
2025-04-30 03:00:19,791 - INFO - 第 6 页获取到 100 条记录
2025-04-30 03:00:19,791 - INFO - Request Parameters - Page 7:
2025-04-30 03:00:19,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:19,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:20,229 - INFO - Response - Page 7:
2025-04-30 03:00:20,433 - INFO - 第 7 页获取到 61 条记录
2025-04-30 03:00:20,433 - INFO - 查询完成，共获取到 661 条记录
2025-04-30 03:00:20,433 - INFO - 获取到 661 条表单数据
2025-04-30 03:00:20,433 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-30 03:00:20,448 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 03:00:20,448 - INFO - 开始处理日期: 2025-04
2025-04-30 03:00:20,448 - INFO - Request Parameters - Page 1:
2025-04-30 03:00:20,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:20,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:20,949 - INFO - Response - Page 1:
2025-04-30 03:00:21,152 - INFO - 第 1 页获取到 100 条记录
2025-04-30 03:00:21,152 - INFO - Request Parameters - Page 2:
2025-04-30 03:00:21,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:21,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:21,684 - INFO - Response - Page 2:
2025-04-30 03:00:21,887 - INFO - 第 2 页获取到 100 条记录
2025-04-30 03:00:21,887 - INFO - Request Parameters - Page 3:
2025-04-30 03:00:21,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:21,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:22,309 - INFO - Response - Page 3:
2025-04-30 03:00:22,513 - INFO - 第 3 页获取到 100 条记录
2025-04-30 03:00:22,513 - INFO - Request Parameters - Page 4:
2025-04-30 03:00:22,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:22,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:22,982 - INFO - Response - Page 4:
2025-04-30 03:00:23,185 - INFO - 第 4 页获取到 100 条记录
2025-04-30 03:00:23,185 - INFO - Request Parameters - Page 5:
2025-04-30 03:00:23,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:23,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:23,639 - INFO - Response - Page 5:
2025-04-30 03:00:23,842 - INFO - 第 5 页获取到 100 条记录
2025-04-30 03:00:23,842 - INFO - Request Parameters - Page 6:
2025-04-30 03:00:23,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:23,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:24,374 - INFO - Response - Page 6:
2025-04-30 03:00:24,577 - INFO - 第 6 页获取到 100 条记录
2025-04-30 03:00:24,577 - INFO - Request Parameters - Page 7:
2025-04-30 03:00:24,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 03:00:24,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 03:00:24,905 - INFO - Response - Page 7:
2025-04-30 03:00:25,109 - INFO - 第 7 页获取到 27 条记录
2025-04-30 03:00:25,109 - INFO - 查询完成，共获取到 627 条记录
2025-04-30 03:00:25,109 - INFO - 获取到 627 条表单数据
2025-04-30 03:00:25,109 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-30 03:00:25,124 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 03:00:25,124 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 03:00:25,124 - INFO - =================同步完成====================
2025-04-30 06:00:03,736 - INFO - =================使用默认全量同步=============
2025-04-30 06:00:04,893 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-30 06:00:04,893 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-30 06:00:04,909 - INFO - 开始处理日期: 2025-01
2025-04-30 06:00:04,909 - INFO - Request Parameters - Page 1:
2025-04-30 06:00:04,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:04,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:05,707 - INFO - Response - Page 1:
2025-04-30 06:00:05,910 - INFO - 第 1 页获取到 100 条记录
2025-04-30 06:00:05,910 - INFO - Request Parameters - Page 2:
2025-04-30 06:00:05,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:05,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:06,614 - INFO - Response - Page 2:
2025-04-30 06:00:06,817 - INFO - 第 2 页获取到 100 条记录
2025-04-30 06:00:06,817 - INFO - Request Parameters - Page 3:
2025-04-30 06:00:06,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:06,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:07,333 - INFO - Response - Page 3:
2025-04-30 06:00:07,536 - INFO - 第 3 页获取到 100 条记录
2025-04-30 06:00:07,536 - INFO - Request Parameters - Page 4:
2025-04-30 06:00:07,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:07,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:08,006 - INFO - Response - Page 4:
2025-04-30 06:00:08,209 - INFO - 第 4 页获取到 100 条记录
2025-04-30 06:00:08,209 - INFO - Request Parameters - Page 5:
2025-04-30 06:00:08,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:08,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:08,678 - INFO - Response - Page 5:
2025-04-30 06:00:08,881 - INFO - 第 5 页获取到 100 条记录
2025-04-30 06:00:08,881 - INFO - Request Parameters - Page 6:
2025-04-30 06:00:08,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:08,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:09,382 - INFO - Response - Page 6:
2025-04-30 06:00:09,585 - INFO - 第 6 页获取到 100 条记录
2025-04-30 06:00:09,585 - INFO - Request Parameters - Page 7:
2025-04-30 06:00:09,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:09,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:10,070 - INFO - Response - Page 7:
2025-04-30 06:00:10,273 - INFO - 第 7 页获取到 82 条记录
2025-04-30 06:00:10,273 - INFO - 查询完成，共获取到 682 条记录
2025-04-30 06:00:10,273 - INFO - 获取到 682 条表单数据
2025-04-30 06:00:10,273 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-30 06:00:10,289 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 06:00:10,289 - INFO - 开始处理日期: 2025-02
2025-04-30 06:00:10,289 - INFO - Request Parameters - Page 1:
2025-04-30 06:00:10,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:10,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:10,821 - INFO - Response - Page 1:
2025-04-30 06:00:11,024 - INFO - 第 1 页获取到 100 条记录
2025-04-30 06:00:11,024 - INFO - Request Parameters - Page 2:
2025-04-30 06:00:11,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:11,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:11,540 - INFO - Response - Page 2:
2025-04-30 06:00:11,744 - INFO - 第 2 页获取到 100 条记录
2025-04-30 06:00:11,744 - INFO - Request Parameters - Page 3:
2025-04-30 06:00:11,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:11,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:12,181 - INFO - Response - Page 3:
2025-04-30 06:00:12,385 - INFO - 第 3 页获取到 100 条记录
2025-04-30 06:00:12,385 - INFO - Request Parameters - Page 4:
2025-04-30 06:00:12,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:12,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:12,854 - INFO - Response - Page 4:
2025-04-30 06:00:13,057 - INFO - 第 4 页获取到 100 条记录
2025-04-30 06:00:13,057 - INFO - Request Parameters - Page 5:
2025-04-30 06:00:13,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:13,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:13,667 - INFO - Response - Page 5:
2025-04-30 06:00:13,870 - INFO - 第 5 页获取到 100 条记录
2025-04-30 06:00:13,870 - INFO - Request Parameters - Page 6:
2025-04-30 06:00:13,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:13,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:14,434 - INFO - Response - Page 6:
2025-04-30 06:00:14,637 - INFO - 第 6 页获取到 100 条记录
2025-04-30 06:00:14,637 - INFO - Request Parameters - Page 7:
2025-04-30 06:00:14,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:14,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:15,122 - INFO - Response - Page 7:
2025-04-30 06:00:15,325 - INFO - 第 7 页获取到 70 条记录
2025-04-30 06:00:15,325 - INFO - 查询完成，共获取到 670 条记录
2025-04-30 06:00:15,325 - INFO - 获取到 670 条表单数据
2025-04-30 06:00:15,325 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-30 06:00:15,341 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 06:00:15,341 - INFO - 开始处理日期: 2025-03
2025-04-30 06:00:15,341 - INFO - Request Parameters - Page 1:
2025-04-30 06:00:15,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:15,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:15,763 - INFO - Response - Page 1:
2025-04-30 06:00:15,966 - INFO - 第 1 页获取到 100 条记录
2025-04-30 06:00:15,966 - INFO - Request Parameters - Page 2:
2025-04-30 06:00:15,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:15,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:16,420 - INFO - Response - Page 2:
2025-04-30 06:00:16,623 - INFO - 第 2 页获取到 100 条记录
2025-04-30 06:00:16,623 - INFO - Request Parameters - Page 3:
2025-04-30 06:00:16,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:16,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:17,124 - INFO - Response - Page 3:
2025-04-30 06:00:17,342 - INFO - 第 3 页获取到 100 条记录
2025-04-30 06:00:17,342 - INFO - Request Parameters - Page 4:
2025-04-30 06:00:17,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:17,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:17,796 - INFO - Response - Page 4:
2025-04-30 06:00:17,999 - INFO - 第 4 页获取到 100 条记录
2025-04-30 06:00:17,999 - INFO - Request Parameters - Page 5:
2025-04-30 06:00:17,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:17,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:18,422 - INFO - Response - Page 5:
2025-04-30 06:00:18,625 - INFO - 第 5 页获取到 100 条记录
2025-04-30 06:00:18,625 - INFO - Request Parameters - Page 6:
2025-04-30 06:00:18,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:18,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:19,141 - INFO - Response - Page 6:
2025-04-30 06:00:19,344 - INFO - 第 6 页获取到 100 条记录
2025-04-30 06:00:19,344 - INFO - Request Parameters - Page 7:
2025-04-30 06:00:19,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:19,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:19,829 - INFO - Response - Page 7:
2025-04-30 06:00:20,032 - INFO - 第 7 页获取到 61 条记录
2025-04-30 06:00:20,032 - INFO - 查询完成，共获取到 661 条记录
2025-04-30 06:00:20,032 - INFO - 获取到 661 条表单数据
2025-04-30 06:00:20,032 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-30 06:00:20,048 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 06:00:20,048 - INFO - 开始处理日期: 2025-04
2025-04-30 06:00:20,048 - INFO - Request Parameters - Page 1:
2025-04-30 06:00:20,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:20,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:20,580 - INFO - Response - Page 1:
2025-04-30 06:00:20,783 - INFO - 第 1 页获取到 100 条记录
2025-04-30 06:00:20,783 - INFO - Request Parameters - Page 2:
2025-04-30 06:00:20,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:20,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:21,331 - INFO - Response - Page 2:
2025-04-30 06:00:21,534 - INFO - 第 2 页获取到 100 条记录
2025-04-30 06:00:21,534 - INFO - Request Parameters - Page 3:
2025-04-30 06:00:21,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:21,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:22,034 - INFO - Response - Page 3:
2025-04-30 06:00:22,238 - INFO - 第 3 页获取到 100 条记录
2025-04-30 06:00:22,238 - INFO - Request Parameters - Page 4:
2025-04-30 06:00:22,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:22,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:22,738 - INFO - Response - Page 4:
2025-04-30 06:00:22,941 - INFO - 第 4 页获取到 100 条记录
2025-04-30 06:00:22,941 - INFO - Request Parameters - Page 5:
2025-04-30 06:00:22,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:22,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:23,442 - INFO - Response - Page 5:
2025-04-30 06:00:23,645 - INFO - 第 5 页获取到 100 条记录
2025-04-30 06:00:23,645 - INFO - Request Parameters - Page 6:
2025-04-30 06:00:23,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:23,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:24,161 - INFO - Response - Page 6:
2025-04-30 06:00:24,365 - INFO - 第 6 页获取到 100 条记录
2025-04-30 06:00:24,365 - INFO - Request Parameters - Page 7:
2025-04-30 06:00:24,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:00:24,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:00:24,771 - INFO - Response - Page 7:
2025-04-30 06:00:24,975 - INFO - 第 7 页获取到 27 条记录
2025-04-30 06:00:24,975 - INFO - 查询完成，共获取到 627 条记录
2025-04-30 06:00:24,975 - INFO - 获取到 627 条表单数据
2025-04-30 06:00:24,975 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-30 06:00:24,990 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-04-30 06:00:25,428 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-04-30 06:00:25,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116897.0, 'new_value': 120676.0}, {'field': 'total_amount', 'old_value': 118789.0, 'new_value': 122568.0}, {'field': 'order_count', 'old_value': 574, 'new_value': 594}]
2025-04-30 06:00:25,428 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-04-30 06:00:25,428 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-04-30 06:00:25,428 - INFO - =================同步完成====================
2025-04-30 09:00:03,680 - INFO - =================使用默认全量同步=============
2025-04-30 09:00:04,822 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-30 09:00:04,822 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-30 09:00:04,837 - INFO - 开始处理日期: 2025-01
2025-04-30 09:00:04,837 - INFO - Request Parameters - Page 1:
2025-04-30 09:00:04,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:04,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:05,745 - INFO - Response - Page 1:
2025-04-30 09:00:05,948 - INFO - 第 1 页获取到 100 条记录
2025-04-30 09:00:05,948 - INFO - Request Parameters - Page 2:
2025-04-30 09:00:05,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:05,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:06,417 - INFO - Response - Page 2:
2025-04-30 09:00:06,620 - INFO - 第 2 页获取到 100 条记录
2025-04-30 09:00:06,620 - INFO - Request Parameters - Page 3:
2025-04-30 09:00:06,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:06,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:07,152 - INFO - Response - Page 3:
2025-04-30 09:00:07,355 - INFO - 第 3 页获取到 100 条记录
2025-04-30 09:00:07,355 - INFO - Request Parameters - Page 4:
2025-04-30 09:00:07,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:07,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:08,059 - INFO - Response - Page 4:
2025-04-30 09:00:08,263 - INFO - 第 4 页获取到 100 条记录
2025-04-30 09:00:08,263 - INFO - Request Parameters - Page 5:
2025-04-30 09:00:08,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:08,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:08,763 - INFO - Response - Page 5:
2025-04-30 09:00:08,966 - INFO - 第 5 页获取到 100 条记录
2025-04-30 09:00:08,966 - INFO - Request Parameters - Page 6:
2025-04-30 09:00:08,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:08,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:09,467 - INFO - Response - Page 6:
2025-04-30 09:00:09,670 - INFO - 第 6 页获取到 100 条记录
2025-04-30 09:00:09,670 - INFO - Request Parameters - Page 7:
2025-04-30 09:00:09,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:09,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:10,108 - INFO - Response - Page 7:
2025-04-30 09:00:10,311 - INFO - 第 7 页获取到 82 条记录
2025-04-30 09:00:10,311 - INFO - 查询完成，共获取到 682 条记录
2025-04-30 09:00:10,311 - INFO - 获取到 682 条表单数据
2025-04-30 09:00:10,311 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-30 09:00:10,327 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 09:00:10,327 - INFO - 开始处理日期: 2025-02
2025-04-30 09:00:10,327 - INFO - Request Parameters - Page 1:
2025-04-30 09:00:10,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:10,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:10,906 - INFO - Response - Page 1:
2025-04-30 09:00:11,109 - INFO - 第 1 页获取到 100 条记录
2025-04-30 09:00:11,109 - INFO - Request Parameters - Page 2:
2025-04-30 09:00:11,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:11,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:11,594 - INFO - Response - Page 2:
2025-04-30 09:00:11,797 - INFO - 第 2 页获取到 100 条记录
2025-04-30 09:00:11,797 - INFO - Request Parameters - Page 3:
2025-04-30 09:00:11,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:11,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:12,298 - INFO - Response - Page 3:
2025-04-30 09:00:12,501 - INFO - 第 3 页获取到 100 条记录
2025-04-30 09:00:12,501 - INFO - Request Parameters - Page 4:
2025-04-30 09:00:12,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:12,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:12,986 - INFO - Response - Page 4:
2025-04-30 09:00:13,189 - INFO - 第 4 页获取到 100 条记录
2025-04-30 09:00:13,189 - INFO - Request Parameters - Page 5:
2025-04-30 09:00:13,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:13,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:13,689 - INFO - Response - Page 5:
2025-04-30 09:00:13,893 - INFO - 第 5 页获取到 100 条记录
2025-04-30 09:00:13,893 - INFO - Request Parameters - Page 6:
2025-04-30 09:00:13,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:13,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:14,409 - INFO - Response - Page 6:
2025-04-30 09:00:14,612 - INFO - 第 6 页获取到 100 条记录
2025-04-30 09:00:14,612 - INFO - Request Parameters - Page 7:
2025-04-30 09:00:14,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:14,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:15,050 - INFO - Response - Page 7:
2025-04-30 09:00:15,253 - INFO - 第 7 页获取到 70 条记录
2025-04-30 09:00:15,253 - INFO - 查询完成，共获取到 670 条记录
2025-04-30 09:00:15,253 - INFO - 获取到 670 条表单数据
2025-04-30 09:00:15,253 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-30 09:00:15,269 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 09:00:15,269 - INFO - 开始处理日期: 2025-03
2025-04-30 09:00:15,269 - INFO - Request Parameters - Page 1:
2025-04-30 09:00:15,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:15,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:15,738 - INFO - Response - Page 1:
2025-04-30 09:00:15,942 - INFO - 第 1 页获取到 100 条记录
2025-04-30 09:00:15,942 - INFO - Request Parameters - Page 2:
2025-04-30 09:00:15,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:15,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:16,395 - INFO - Response - Page 2:
2025-04-30 09:00:16,598 - INFO - 第 2 页获取到 100 条记录
2025-04-30 09:00:16,598 - INFO - Request Parameters - Page 3:
2025-04-30 09:00:16,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:16,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:17,146 - INFO - Response - Page 3:
2025-04-30 09:00:17,349 - INFO - 第 3 页获取到 100 条记录
2025-04-30 09:00:17,349 - INFO - Request Parameters - Page 4:
2025-04-30 09:00:17,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:17,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:17,771 - INFO - Response - Page 4:
2025-04-30 09:00:17,990 - INFO - 第 4 页获取到 100 条记录
2025-04-30 09:00:17,990 - INFO - Request Parameters - Page 5:
2025-04-30 09:00:17,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:17,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:18,428 - INFO - Response - Page 5:
2025-04-30 09:00:18,632 - INFO - 第 5 页获取到 100 条记录
2025-04-30 09:00:18,632 - INFO - Request Parameters - Page 6:
2025-04-30 09:00:18,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:18,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:19,195 - INFO - Response - Page 6:
2025-04-30 09:00:19,398 - INFO - 第 6 页获取到 100 条记录
2025-04-30 09:00:19,398 - INFO - Request Parameters - Page 7:
2025-04-30 09:00:19,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:19,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:19,883 - INFO - Response - Page 7:
2025-04-30 09:00:20,086 - INFO - 第 7 页获取到 61 条记录
2025-04-30 09:00:20,086 - INFO - 查询完成，共获取到 661 条记录
2025-04-30 09:00:20,086 - INFO - 获取到 661 条表单数据
2025-04-30 09:00:20,086 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-30 09:00:20,102 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 09:00:20,102 - INFO - 开始处理日期: 2025-04
2025-04-30 09:00:20,102 - INFO - Request Parameters - Page 1:
2025-04-30 09:00:20,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:20,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:20,696 - INFO - Response - Page 1:
2025-04-30 09:00:20,899 - INFO - 第 1 页获取到 100 条记录
2025-04-30 09:00:20,899 - INFO - Request Parameters - Page 2:
2025-04-30 09:00:20,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:20,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:21,337 - INFO - Response - Page 2:
2025-04-30 09:00:21,541 - INFO - 第 2 页获取到 100 条记录
2025-04-30 09:00:21,541 - INFO - Request Parameters - Page 3:
2025-04-30 09:00:21,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:21,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:22,010 - INFO - Response - Page 3:
2025-04-30 09:00:22,213 - INFO - 第 3 页获取到 100 条记录
2025-04-30 09:00:22,213 - INFO - Request Parameters - Page 4:
2025-04-30 09:00:22,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:22,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:22,682 - INFO - Response - Page 4:
2025-04-30 09:00:22,886 - INFO - 第 4 页获取到 100 条记录
2025-04-30 09:00:22,886 - INFO - Request Parameters - Page 5:
2025-04-30 09:00:22,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:22,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:23,464 - INFO - Response - Page 5:
2025-04-30 09:00:23,668 - INFO - 第 5 页获取到 100 条记录
2025-04-30 09:00:23,668 - INFO - Request Parameters - Page 6:
2025-04-30 09:00:23,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:23,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:24,137 - INFO - Response - Page 6:
2025-04-30 09:00:24,340 - INFO - 第 6 页获取到 100 条记录
2025-04-30 09:00:24,340 - INFO - Request Parameters - Page 7:
2025-04-30 09:00:24,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:00:24,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:00:24,762 - INFO - Response - Page 7:
2025-04-30 09:00:24,966 - INFO - 第 7 页获取到 27 条记录
2025-04-30 09:00:24,966 - INFO - 查询完成，共获取到 627 条记录
2025-04-30 09:00:24,966 - INFO - 获取到 627 条表单数据
2025-04-30 09:00:24,966 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-30 09:00:24,966 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-04-30 09:00:25,372 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-04-30 09:00:25,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42516.28, 'new_value': 44053.68}, {'field': 'total_amount', 'old_value': 42723.28, 'new_value': 44260.68}, {'field': 'order_count', 'old_value': 5427, 'new_value': 5645}]
2025-04-30 09:00:25,372 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-04-30 09:00:25,810 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-04-30 09:00:25,810 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17217.54, 'new_value': 17821.85}, {'field': 'offline_amount', 'old_value': 46109.55, 'new_value': 48002.4}, {'field': 'total_amount', 'old_value': 63327.09, 'new_value': 65824.25}, {'field': 'order_count', 'old_value': 3367, 'new_value': 3497}]
2025-04-30 09:00:25,810 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-04-30 09:00:26,248 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-04-30 09:00:26,248 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'total_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 249}]
2025-04-30 09:00:26,248 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-04-30 09:00:26,749 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-04-30 09:00:26,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'total_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 153}]
2025-04-30 09:00:26,749 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-04-30 09:00:27,280 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-04-30 09:00:27,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1800000.0, 'new_value': 1850000.0}, {'field': 'total_amount', 'old_value': 1800000.0, 'new_value': 1850000.0}, {'field': 'order_count', 'old_value': 353, 'new_value': 354}]
2025-04-30 09:00:27,280 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-04-30 09:00:27,781 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-04-30 09:00:27,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1900000.0, 'new_value': 1950000.0}, {'field': 'total_amount', 'old_value': 1900000.0, 'new_value': 1950000.0}, {'field': 'order_count', 'old_value': 496, 'new_value': 497}]
2025-04-30 09:00:27,781 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-04-30 09:00:28,250 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-04-30 09:00:28,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289275.0, 'new_value': 314275.0}, {'field': 'total_amount', 'old_value': 289275.0, 'new_value': 314275.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 107}]
2025-04-30 09:00:28,250 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MG3
2025-04-30 09:00:28,735 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MG3
2025-04-30 09:00:28,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55160.0, 'new_value': 71159.0}, {'field': 'total_amount', 'old_value': 80160.0, 'new_value': 96159.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-04-30 09:00:28,735 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-04-30 09:00:29,188 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-04-30 09:00:29,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198154.0, 'new_value': 204414.0}, {'field': 'total_amount', 'old_value': 198154.0, 'new_value': 204414.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-04-30 09:00:29,188 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-04-30 09:00:29,548 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-04-30 09:00:29,548 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20906.9, 'new_value': 21962.9}, {'field': 'offline_amount', 'old_value': 30969.35, 'new_value': 31803.35}, {'field': 'total_amount', 'old_value': 51876.25, 'new_value': 53766.25}, {'field': 'order_count', 'old_value': 2485, 'new_value': 2584}]
2025-04-30 09:00:29,548 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-04-30 09:00:30,033 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-04-30 09:00:30,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105172.0, 'new_value': 109192.0}, {'field': 'total_amount', 'old_value': 105172.0, 'new_value': 109192.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 130}]
2025-04-30 09:00:30,033 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-04-30 09:00:30,455 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-04-30 09:00:30,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115425.0, 'new_value': 119037.0}, {'field': 'total_amount', 'old_value': 115425.0, 'new_value': 119037.0}, {'field': 'order_count', 'old_value': 6353, 'new_value': 6559}]
2025-04-30 09:00:30,455 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-04-30 09:00:30,846 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-04-30 09:00:30,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67258.43, 'new_value': 69209.72}, {'field': 'offline_amount', 'old_value': 136513.35, 'new_value': 143240.35}, {'field': 'total_amount', 'old_value': 203771.78, 'new_value': 212450.07}, {'field': 'order_count', 'old_value': 2237, 'new_value': 2338}]
2025-04-30 09:00:30,846 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-04-30 09:00:31,393 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-04-30 09:00:31,393 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64612.0, 'new_value': 65085.0}, {'field': 'offline_amount', 'old_value': 121805.85, 'new_value': 122451.85}, {'field': 'total_amount', 'old_value': 186417.85, 'new_value': 187536.85}, {'field': 'order_count', 'old_value': 252, 'new_value': 259}]
2025-04-30 09:00:31,393 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-04-30 09:00:31,831 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-04-30 09:00:31,831 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71145.0, 'new_value': 73426.0}, {'field': 'total_amount', 'old_value': 71145.0, 'new_value': 73426.0}, {'field': 'order_count', 'old_value': 1303, 'new_value': 1347}]
2025-04-30 09:00:31,831 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-04-30 09:00:32,457 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-04-30 09:00:32,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159562.0, 'new_value': 166188.0}, {'field': 'offline_amount', 'old_value': 56777.67, 'new_value': 58279.67}, {'field': 'total_amount', 'old_value': 216339.67, 'new_value': 224467.67}, {'field': 'order_count', 'old_value': 1465, 'new_value': 1516}]
2025-04-30 09:00:32,457 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-30 09:00:32,895 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-30 09:00:32,895 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6786.7, 'new_value': 7130.7}, {'field': 'offline_amount', 'old_value': 47437.2, 'new_value': 48594.1}, {'field': 'total_amount', 'old_value': 54223.9, 'new_value': 55724.8}, {'field': 'order_count', 'old_value': 559, 'new_value': 568}]
2025-04-30 09:00:32,895 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-04-30 09:00:33,317 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-04-30 09:00:33,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9762.92, 'new_value': 10054.56}, {'field': 'offline_amount', 'old_value': 228882.99, 'new_value': 233512.14}, {'field': 'total_amount', 'old_value': 238645.91, 'new_value': 243566.7}, {'field': 'order_count', 'old_value': 1299, 'new_value': 1345}]
2025-04-30 09:00:33,317 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-04-30 09:00:33,818 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-04-30 09:00:33,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106998.16, 'new_value': 112203.68}, {'field': 'total_amount', 'old_value': 106998.16, 'new_value': 112203.68}, {'field': 'order_count', 'old_value': 2838, 'new_value': 2956}]
2025-04-30 09:00:33,818 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-04-30 09:00:34,381 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-04-30 09:00:34,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100.0, 'new_value': 150.0}, {'field': 'offline_amount', 'old_value': 42555.0, 'new_value': 43633.0}, {'field': 'total_amount', 'old_value': 42655.0, 'new_value': 43783.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 135}]
2025-04-30 09:00:34,381 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-04-30 09:00:34,850 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-04-30 09:00:34,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40193.31, 'new_value': 42173.82}, {'field': 'total_amount', 'old_value': 40193.31, 'new_value': 42173.82}, {'field': 'order_count', 'old_value': 1486, 'new_value': 1543}]
2025-04-30 09:00:34,850 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-04-30 09:00:35,303 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-04-30 09:00:35,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12729.85, 'new_value': 13297.54}, {'field': 'offline_amount', 'old_value': 197204.65, 'new_value': 202343.59}, {'field': 'total_amount', 'old_value': 209934.5, 'new_value': 215641.13}, {'field': 'order_count', 'old_value': 2254, 'new_value': 2319}]
2025-04-30 09:00:35,303 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-04-30 09:00:35,773 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-04-30 09:00:35,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 193489.1, 'new_value': 197164.1}, {'field': 'offline_amount', 'old_value': 71819.36, 'new_value': 73627.36}, {'field': 'total_amount', 'old_value': 265308.46, 'new_value': 270791.46}, {'field': 'order_count', 'old_value': 1918, 'new_value': 1967}]
2025-04-30 09:00:35,773 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-04-30 09:00:36,211 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-04-30 09:00:36,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92274.0, 'new_value': 95293.0}, {'field': 'total_amount', 'old_value': 92274.0, 'new_value': 95293.0}, {'field': 'order_count', 'old_value': 812, 'new_value': 838}]
2025-04-30 09:00:36,211 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-04-30 09:00:36,820 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-04-30 09:00:36,820 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1899.79, 'new_value': 1914.95}, {'field': 'offline_amount', 'old_value': 105810.34, 'new_value': 110077.88}, {'field': 'total_amount', 'old_value': 107710.13, 'new_value': 111992.83}, {'field': 'order_count', 'old_value': 2700, 'new_value': 2769}]
2025-04-30 09:00:36,820 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-04-30 09:00:37,368 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-04-30 09:00:37,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22531.4, 'new_value': 23095.4}, {'field': 'offline_amount', 'old_value': 51075.87, 'new_value': 51848.49}, {'field': 'total_amount', 'old_value': 73607.27, 'new_value': 74943.89}, {'field': 'order_count', 'old_value': 864, 'new_value': 887}]
2025-04-30 09:00:37,368 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-04-30 09:00:37,868 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-04-30 09:00:37,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150822.02, 'new_value': 157928.89}, {'field': 'total_amount', 'old_value': 150822.02, 'new_value': 157928.89}, {'field': 'order_count', 'old_value': 1762, 'new_value': 1830}]
2025-04-30 09:00:37,868 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-04-30 09:00:38,384 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-04-30 09:00:38,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221481.5, 'new_value': 225471.6}, {'field': 'total_amount', 'old_value': 221481.5, 'new_value': 225471.6}, {'field': 'order_count', 'old_value': 2743, 'new_value': 2827}]
2025-04-30 09:00:38,384 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-04-30 09:00:38,854 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-04-30 09:00:38,854 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5992.0, 'new_value': 6076.0}, {'field': 'offline_amount', 'old_value': 25321.0, 'new_value': 32586.0}, {'field': 'total_amount', 'old_value': 31313.0, 'new_value': 38662.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 65}]
2025-04-30 09:00:38,854 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-04-30 09:00:39,307 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-04-30 09:00:39,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 507074.95, 'new_value': 524412.95}, {'field': 'total_amount', 'old_value': 507074.95, 'new_value': 524412.95}, {'field': 'order_count', 'old_value': 2265, 'new_value': 2321}]
2025-04-30 09:00:39,307 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-04-30 09:00:39,698 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-04-30 09:00:39,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130901.4, 'new_value': 136285.4}, {'field': 'total_amount', 'old_value': 157214.21, 'new_value': 162598.21}, {'field': 'order_count', 'old_value': 3833, 'new_value': 3968}]
2025-04-30 09:00:39,698 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-30 09:00:40,214 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-30 09:00:40,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25792.58, 'new_value': 26054.98}, {'field': 'total_amount', 'old_value': 25792.58, 'new_value': 26054.98}, {'field': 'order_count', 'old_value': 155, 'new_value': 158}]
2025-04-30 09:00:40,214 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MH9
2025-04-30 09:00:40,637 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MH9
2025-04-30 09:00:40,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27443.56, 'new_value': 27706.96}, {'field': 'total_amount', 'old_value': 27443.56, 'new_value': 27706.96}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-04-30 09:00:40,637 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-04-30 09:00:41,200 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-04-30 09:00:41,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194747.0, 'new_value': 199644.0}, {'field': 'total_amount', 'old_value': 194747.0, 'new_value': 199644.0}, {'field': 'order_count', 'old_value': 379, 'new_value': 397}]
2025-04-30 09:00:41,200 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-30 09:00:41,700 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-30 09:00:41,700 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 149669.97, 'new_value': 153319.37}, {'field': 'offline_amount', 'old_value': 140995.31, 'new_value': 144636.31}, {'field': 'total_amount', 'old_value': 290665.28, 'new_value': 297955.68}, {'field': 'order_count', 'old_value': 917, 'new_value': 940}]
2025-04-30 09:00:41,700 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-04-30 09:00:42,247 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-04-30 09:00:42,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41642.0, 'new_value': 46719.0}, {'field': 'total_amount', 'old_value': 41642.0, 'new_value': 46719.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 151}]
2025-04-30 09:00:42,247 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-30 09:00:42,764 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-30 09:00:42,764 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94168.0, 'new_value': 97341.0}, {'field': 'offline_amount', 'old_value': 217287.0, 'new_value': 219223.0}, {'field': 'total_amount', 'old_value': 311455.0, 'new_value': 316564.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 301}]
2025-04-30 09:00:42,764 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-04-30 09:00:43,280 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-04-30 09:00:43,280 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45238.8, 'new_value': 47634.8}, {'field': 'offline_amount', 'old_value': 481752.0, 'new_value': 531302.0}, {'field': 'total_amount', 'old_value': 526990.8, 'new_value': 578936.8}, {'field': 'order_count', 'old_value': 69, 'new_value': 71}]
2025-04-30 09:00:43,280 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-04-30 09:00:43,811 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-04-30 09:00:43,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 284452.3, 'new_value': 294028.1}, {'field': 'total_amount', 'old_value': 284452.3, 'new_value': 294028.1}, {'field': 'order_count', 'old_value': 1111, 'new_value': 1146}]
2025-04-30 09:00:43,811 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-04-30 09:00:44,234 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-04-30 09:00:44,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32903.63, 'new_value': 35752.89}, {'field': 'total_amount', 'old_value': 119965.27, 'new_value': 122814.53}, {'field': 'order_count', 'old_value': 493, 'new_value': 506}]
2025-04-30 09:00:44,234 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-04-30 09:00:44,687 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-04-30 09:00:44,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223429.0, 'new_value': 238027.0}, {'field': 'total_amount', 'old_value': 240526.0, 'new_value': 255124.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-04-30 09:00:44,687 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-30 09:00:45,219 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-30 09:00:45,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25303.76, 'new_value': 25541.59}, {'field': 'total_amount', 'old_value': 25303.76, 'new_value': 25541.59}, {'field': 'order_count', 'old_value': 261, 'new_value': 264}]
2025-04-30 09:00:45,219 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5A
2025-04-30 09:00:45,719 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5A
2025-04-30 09:00:45,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20007.0, 'new_value': 20345.0}, {'field': 'total_amount', 'old_value': 20007.0, 'new_value': 20345.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-04-30 09:00:45,719 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-04-30 09:00:46,157 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-04-30 09:00:46,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 194219.19, 'new_value': 200547.56}, {'field': 'offline_amount', 'old_value': 157994.08, 'new_value': 165835.68}, {'field': 'total_amount', 'old_value': 352213.27, 'new_value': 366383.24}, {'field': 'order_count', 'old_value': 3084, 'new_value': 174200}]
2025-04-30 09:00:46,157 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-04-30 09:00:46,626 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-04-30 09:00:46,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148400.23, 'new_value': 152982.52}, {'field': 'offline_amount', 'old_value': 32023.7, 'new_value': 33339.12}, {'field': 'total_amount', 'old_value': 180423.93, 'new_value': 186321.64}, {'field': 'order_count', 'old_value': 740, 'new_value': 762}]
2025-04-30 09:00:46,626 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-04-30 09:00:47,096 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-04-30 09:00:47,096 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94004.21, 'new_value': 98007.69}, {'field': 'total_amount', 'old_value': 94004.21, 'new_value': 98007.69}, {'field': 'order_count', 'old_value': 421, 'new_value': 430}]
2025-04-30 09:00:47,096 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-04-30 09:00:47,502 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-04-30 09:00:47,502 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 195817.04, 'new_value': 204594.24}, {'field': 'offline_amount', 'old_value': 376519.66, 'new_value': 384569.23}, {'field': 'total_amount', 'old_value': 572336.7, 'new_value': 589163.47}, {'field': 'order_count', 'old_value': 4351, 'new_value': 4487}]
2025-04-30 09:00:47,502 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-04-30 09:00:48,003 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-04-30 09:00:48,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55817.11, 'new_value': 57054.51}, {'field': 'offline_amount', 'old_value': 344182.27, 'new_value': 353443.42}, {'field': 'total_amount', 'old_value': 399999.38, 'new_value': 410497.93}, {'field': 'order_count', 'old_value': 2724, 'new_value': 2797}]
2025-04-30 09:00:48,003 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-04-30 09:00:48,456 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-04-30 09:00:48,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11454.5, 'new_value': 11613.5}, {'field': 'offline_amount', 'old_value': 15010.7, 'new_value': 15733.7}, {'field': 'total_amount', 'old_value': 26465.2, 'new_value': 27347.2}, {'field': 'order_count', 'old_value': 80, 'new_value': 83}]
2025-04-30 09:00:48,456 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-04-30 09:00:48,832 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-04-30 09:00:48,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22960.0, 'new_value': 25435.0}, {'field': 'total_amount', 'old_value': 22960.0, 'new_value': 25435.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 72}]
2025-04-30 09:00:48,832 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMA
2025-04-30 09:00:49,332 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMA
2025-04-30 09:00:49,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115596.0, 'new_value': 120402.0}, {'field': 'total_amount', 'old_value': 177849.0, 'new_value': 182655.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-04-30 09:00:49,332 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOA
2025-04-30 09:00:49,801 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOA
2025-04-30 09:00:49,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8627.0, 'new_value': 9300.0}, {'field': 'total_amount', 'old_value': 8627.0, 'new_value': 9300.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-30 09:00:49,801 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-04-30 09:00:50,317 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-04-30 09:00:50,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43780.36, 'new_value': 45154.13}, {'field': 'total_amount', 'old_value': 43780.36, 'new_value': 45154.13}, {'field': 'order_count', 'old_value': 190, 'new_value': 196}]
2025-04-30 09:00:50,317 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-04-30 09:00:50,771 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-04-30 09:00:50,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72062.25, 'new_value': 73723.2}, {'field': 'total_amount', 'old_value': 72062.25, 'new_value': 73723.2}, {'field': 'order_count', 'old_value': 2007, 'new_value': 2059}]
2025-04-30 09:00:50,771 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-04-30 09:00:51,318 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-04-30 09:00:51,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267036.93, 'new_value': 272429.72}, {'field': 'total_amount', 'old_value': 267069.93, 'new_value': 272462.72}, {'field': 'order_count', 'old_value': 2015, 'new_value': 2067}]
2025-04-30 09:00:51,318 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-04-30 09:00:51,881 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-04-30 09:00:51,881 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 384320.0, 'new_value': 400468.0}, {'field': 'offline_amount', 'old_value': 20305.0, 'new_value': 20588.0}, {'field': 'total_amount', 'old_value': 404625.0, 'new_value': 421056.0}, {'field': 'order_count', 'old_value': 3767, 'new_value': 3889}]
2025-04-30 09:00:51,881 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-04-30 09:00:52,319 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-04-30 09:00:52,319 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126306.19, 'new_value': 132159.49}, {'field': 'offline_amount', 'old_value': 291617.35, 'new_value': 295785.13}, {'field': 'total_amount', 'old_value': 417923.54, 'new_value': 427944.62}, {'field': 'order_count', 'old_value': 3855, 'new_value': 4003}]
2025-04-30 09:00:52,319 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-04-30 09:00:52,835 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-04-30 09:00:52,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179384.34, 'new_value': 185181.94}, {'field': 'total_amount', 'old_value': 179384.34, 'new_value': 185181.94}, {'field': 'order_count', 'old_value': 831, 'new_value': 860}]
2025-04-30 09:00:52,835 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-04-30 09:00:53,305 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-04-30 09:00:53,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60242.0, 'new_value': 62828.0}, {'field': 'total_amount', 'old_value': 60639.0, 'new_value': 63225.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 103}]
2025-04-30 09:00:53,305 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-04-30 09:00:53,727 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-04-30 09:00:53,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105065.0, 'new_value': 106332.0}, {'field': 'total_amount', 'old_value': 105065.0, 'new_value': 106332.0}, {'field': 'order_count', 'old_value': 541, 'new_value': 549}]
2025-04-30 09:00:53,727 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-04-30 09:00:54,102 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-04-30 09:00:54,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 248832.0, 'new_value': 258252.0}, {'field': 'total_amount', 'old_value': 248832.0, 'new_value': 258252.0}, {'field': 'order_count', 'old_value': 20736, 'new_value': 21521}]
2025-04-30 09:00:54,102 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-04-30 09:00:54,540 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-04-30 09:00:54,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1686387.68, 'new_value': 1752400.18}, {'field': 'total_amount', 'old_value': 1686387.68, 'new_value': 1752400.18}, {'field': 'order_count', 'old_value': 3545, 'new_value': 3656}]
2025-04-30 09:00:54,540 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-04-30 09:00:54,978 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-04-30 09:00:54,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7719.8, 'new_value': 7972.8}, {'field': 'offline_amount', 'old_value': 20242.13, 'new_value': 20877.11}, {'field': 'total_amount', 'old_value': 27961.93, 'new_value': 28849.91}, {'field': 'order_count', 'old_value': 929, 'new_value': 959}]
2025-04-30 09:00:54,978 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBB
2025-04-30 09:00:55,432 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBB
2025-04-30 09:00:55,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45626.56, 'new_value': 50462.66}, {'field': 'total_amount', 'old_value': 45626.56, 'new_value': 50462.66}, {'field': 'order_count', 'old_value': 41, 'new_value': 45}]
2025-04-30 09:00:55,432 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-04-30 09:00:55,854 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-04-30 09:00:55,854 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 491170.1, 'new_value': 506136.1}, {'field': 'offline_amount', 'old_value': 110968.0, 'new_value': 113258.0}, {'field': 'total_amount', 'old_value': 602138.1, 'new_value': 619394.1}, {'field': 'order_count', 'old_value': 795, 'new_value': 816}]
2025-04-30 09:00:55,854 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-04-30 09:00:56,307 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-04-30 09:00:56,323 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116080.6, 'new_value': 120080.6}, {'field': 'offline_amount', 'old_value': 17157.1, 'new_value': 17747.9}, {'field': 'total_amount', 'old_value': 133237.7, 'new_value': 137828.5}, {'field': 'order_count', 'old_value': 390, 'new_value': 404}]
2025-04-30 09:00:56,323 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-04-30 09:00:56,855 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-04-30 09:00:56,855 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24731.25, 'new_value': 25657.07}, {'field': 'offline_amount', 'old_value': 40744.14, 'new_value': 41997.14}, {'field': 'total_amount', 'old_value': 65475.39, 'new_value': 67654.21}, {'field': 'order_count', 'old_value': 2741, 'new_value': 2827}]
2025-04-30 09:00:56,855 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-04-30 09:00:57,308 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-04-30 09:00:57,308 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7548.0, 'new_value': 7647.0}, {'field': 'offline_amount', 'old_value': 29646.2, 'new_value': 30166.6}, {'field': 'total_amount', 'old_value': 37194.2, 'new_value': 37813.6}, {'field': 'order_count', 'old_value': 1418, 'new_value': 1448}]
2025-04-30 09:00:57,308 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-04-30 09:00:57,793 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-04-30 09:00:57,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 398904.49, 'new_value': 405529.77}, {'field': 'total_amount', 'old_value': 398905.49, 'new_value': 405530.77}, {'field': 'order_count', 'old_value': 654, 'new_value': 667}]
2025-04-30 09:00:57,793 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-04-30 09:00:58,262 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-04-30 09:00:58,262 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 256077.99, 'new_value': 256810.29}, {'field': 'offline_amount', 'old_value': 74602.27, 'new_value': 77362.69}, {'field': 'total_amount', 'old_value': 330680.26, 'new_value': 334172.98}, {'field': 'order_count', 'old_value': 2293, 'new_value': 2343}]
2025-04-30 09:00:58,262 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM5
2025-04-30 09:00:58,685 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM5
2025-04-30 09:00:58,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103133.0, 'new_value': 105225.0}, {'field': 'total_amount', 'old_value': 103133.0, 'new_value': 105225.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-04-30 09:00:58,685 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-04-30 09:00:59,107 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-04-30 09:00:59,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160290.41, 'new_value': 166490.41}, {'field': 'total_amount', 'old_value': 160290.41, 'new_value': 166490.41}, {'field': 'order_count', 'old_value': 5659, 'new_value': 5887}]
2025-04-30 09:00:59,107 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-04-30 09:00:59,576 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-04-30 09:00:59,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14000.52, 'new_value': 14690.32}, {'field': 'offline_amount', 'old_value': 513503.14, 'new_value': 529399.92}, {'field': 'total_amount', 'old_value': 527503.66, 'new_value': 544090.24}, {'field': 'order_count', 'old_value': 2130, 'new_value': 2202}]
2025-04-30 09:00:59,576 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-04-30 09:01:00,077 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-04-30 09:01:00,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29668.59, 'new_value': 30447.69}, {'field': 'offline_amount', 'old_value': 53662.66, 'new_value': 55689.1}, {'field': 'total_amount', 'old_value': 83331.25, 'new_value': 86136.79}, {'field': 'order_count', 'old_value': 2970, 'new_value': 3074}]
2025-04-30 09:01:00,077 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-04-30 09:01:00,546 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-04-30 09:01:00,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60596.75, 'new_value': 111307.55}, {'field': 'total_amount', 'old_value': 60596.75, 'new_value': 111307.55}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-04-30 09:01:00,546 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-04-30 09:01:00,968 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-04-30 09:01:00,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84358.48, 'new_value': 86024.48}, {'field': 'total_amount', 'old_value': 93557.98, 'new_value': 95223.98}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-04-30 09:01:00,968 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-30 09:01:01,547 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-30 09:01:01,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 675731.02, 'new_value': 696433.02}, {'field': 'total_amount', 'old_value': 675731.02, 'new_value': 696433.02}, {'field': 'order_count', 'old_value': 878, 'new_value': 908}]
2025-04-30 09:01:01,562 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB6
2025-04-30 09:01:01,969 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB6
2025-04-30 09:01:01,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7381.0, 'new_value': 7555.0}, {'field': 'total_amount', 'old_value': 7381.0, 'new_value': 7555.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 125}]
2025-04-30 09:01:01,969 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-04-30 09:01:02,376 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-04-30 09:01:02,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131182.09, 'new_value': 134791.19}, {'field': 'total_amount', 'old_value': 131182.09, 'new_value': 134791.19}, {'field': 'order_count', 'old_value': 3811, 'new_value': 3925}]
2025-04-30 09:01:02,376 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH6
2025-04-30 09:01:02,845 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH6
2025-04-30 09:01:02,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26163.0, 'new_value': 27553.0}, {'field': 'total_amount', 'old_value': 26163.0, 'new_value': 27553.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-04-30 09:01:02,845 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-04-30 09:01:03,330 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-04-30 09:01:03,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8288.2, 'new_value': 8882.2}, {'field': 'total_amount', 'old_value': 74774.3, 'new_value': 75368.3}, {'field': 'order_count', 'old_value': 67, 'new_value': 68}]
2025-04-30 09:01:03,330 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-04-30 09:01:03,846 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-04-30 09:01:03,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161498.39, 'new_value': 167538.41}, {'field': 'total_amount', 'old_value': 161498.39, 'new_value': 167538.41}, {'field': 'order_count', 'old_value': 5713, 'new_value': 5930}]
2025-04-30 09:01:03,846 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-04-30 09:01:04,424 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-04-30 09:01:04,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55727.45, 'new_value': 57021.2}, {'field': 'total_amount', 'old_value': 55727.45, 'new_value': 57021.2}, {'field': 'order_count', 'old_value': 2566, 'new_value': 2641}]
2025-04-30 09:01:04,424 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA7
2025-04-30 09:01:04,909 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA7
2025-04-30 09:01:04,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19031.27, 'new_value': 19230.27}, {'field': 'total_amount', 'old_value': 21821.27, 'new_value': 22020.27}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-04-30 09:01:04,909 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-04-30 09:01:05,410 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-04-30 09:01:05,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59391.6, 'new_value': 62662.6}, {'field': 'total_amount', 'old_value': 86021.4, 'new_value': 89292.4}, {'field': 'order_count', 'old_value': 103, 'new_value': 105}]
2025-04-30 09:01:05,410 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-04-30 09:01:05,973 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-04-30 09:01:05,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44303.5, 'new_value': 47447.93}, {'field': 'total_amount', 'old_value': 44493.5, 'new_value': 47637.93}, {'field': 'order_count', 'old_value': 431, 'new_value': 451}]
2025-04-30 09:01:05,973 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-04-30 09:01:06,426 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-04-30 09:01:06,426 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134196.0, 'new_value': 138883.0}, {'field': 'offline_amount', 'old_value': 113641.0, 'new_value': 115942.0}, {'field': 'total_amount', 'old_value': 247837.0, 'new_value': 254825.0}, {'field': 'order_count', 'old_value': 9408, 'new_value': 9543}]
2025-04-30 09:01:06,426 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-04-30 09:01:06,817 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-04-30 09:01:06,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208296.96, 'new_value': 214887.4}, {'field': 'total_amount', 'old_value': 208296.96, 'new_value': 214887.4}, {'field': 'order_count', 'old_value': 678, 'new_value': 696}]
2025-04-30 09:01:06,817 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-04-30 09:01:07,239 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-04-30 09:01:07,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 308695.0, 'new_value': 319241.5}, {'field': 'total_amount', 'old_value': 308695.0, 'new_value': 319241.5}, {'field': 'order_count', 'old_value': 8920, 'new_value': 9233}]
2025-04-30 09:01:07,255 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-30 09:01:07,646 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-30 09:01:07,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116723.37, 'new_value': 124012.97}, {'field': 'total_amount', 'old_value': 242824.57, 'new_value': 250114.17}, {'field': 'order_count', 'old_value': 6703, 'new_value': 6893}]
2025-04-30 09:01:07,646 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-04-30 09:01:08,084 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-04-30 09:01:08,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49447.8, 'new_value': 50705.1}, {'field': 'total_amount', 'old_value': 49447.8, 'new_value': 50705.1}, {'field': 'order_count', 'old_value': 67, 'new_value': 69}]
2025-04-30 09:01:08,084 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-04-30 09:01:08,491 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-04-30 09:01:08,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48249.87, 'new_value': 49933.09}, {'field': 'offline_amount', 'old_value': 56907.44, 'new_value': 57812.41}, {'field': 'total_amount', 'old_value': 105157.31, 'new_value': 107745.5}, {'field': 'order_count', 'old_value': 3774, 'new_value': 3832}]
2025-04-30 09:01:08,491 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-04-30 09:01:08,913 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-04-30 09:01:08,913 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86004.95, 'new_value': 89297.34}, {'field': 'offline_amount', 'old_value': 125482.77, 'new_value': 129930.93}, {'field': 'total_amount', 'old_value': 211487.72, 'new_value': 219228.27}, {'field': 'order_count', 'old_value': 8505, 'new_value': 8804}]
2025-04-30 09:01:08,913 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-04-30 09:01:09,366 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-04-30 09:01:09,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112336.0, 'new_value': 114917.0}, {'field': 'total_amount', 'old_value': 113083.0, 'new_value': 115664.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-04-30 09:01:09,366 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-04-30 09:01:09,804 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-04-30 09:01:09,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 405039.02, 'new_value': 417578.15}, {'field': 'total_amount', 'old_value': 443364.8, 'new_value': 455903.93}, {'field': 'order_count', 'old_value': 1823, 'new_value': 1867}]
2025-04-30 09:01:09,804 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC8
2025-04-30 09:01:10,446 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC8
2025-04-30 09:01:10,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11082.0, 'new_value': 11577.0}, {'field': 'total_amount', 'old_value': 21613.0, 'new_value': 22108.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 102}]
2025-04-30 09:01:10,446 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-30 09:01:10,884 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-30 09:01:10,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 513212.75, 'new_value': 526466.83}, {'field': 'total_amount', 'old_value': 532276.25, 'new_value': 545530.33}, {'field': 'order_count', 'old_value': 2070, 'new_value': 2119}]
2025-04-30 09:01:10,884 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-30 09:01:11,321 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-30 09:01:11,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75791.0, 'new_value': 79976.0}, {'field': 'total_amount', 'old_value': 82210.0, 'new_value': 86395.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 55}]
2025-04-30 09:01:11,321 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCH
2025-04-30 09:01:11,791 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCH
2025-04-30 09:01:11,791 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128013.5, 'new_value': 140613.5}, {'field': 'total_amount', 'old_value': 130413.5, 'new_value': 143013.5}]
2025-04-30 09:01:11,791 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-04-30 09:01:12,260 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-04-30 09:01:12,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59426.0, 'new_value': 60169.0}, {'field': 'total_amount', 'old_value': 59426.0, 'new_value': 60169.0}, {'field': 'order_count', 'old_value': 3910, 'new_value': 3933}]
2025-04-30 09:01:12,260 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-04-30 09:01:12,713 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-04-30 09:01:12,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112185.0, 'new_value': 114863.0}, {'field': 'total_amount', 'old_value': 115543.0, 'new_value': 118221.0}, {'field': 'order_count', 'old_value': 197, 'new_value': 198}]
2025-04-30 09:01:12,713 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-04-30 09:01:13,136 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-04-30 09:01:13,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 359883.0, 'new_value': 378337.0}, {'field': 'total_amount', 'old_value': 359883.0, 'new_value': 378337.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 145}]
2025-04-30 09:01:13,136 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-04-30 09:01:13,589 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-04-30 09:01:13,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16426.6, 'new_value': 16989.6}, {'field': 'total_amount', 'old_value': 16748.6, 'new_value': 17311.6}, {'field': 'order_count', 'old_value': 169, 'new_value': 174}]
2025-04-30 09:01:13,589 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-04-30 09:01:14,027 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-04-30 09:01:14,027 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52600.14, 'new_value': 54700.26}, {'field': 'offline_amount', 'old_value': 452819.39, 'new_value': 462431.82}, {'field': 'total_amount', 'old_value': 505419.53, 'new_value': 517132.08}, {'field': 'order_count', 'old_value': 2274, 'new_value': 2333}]
2025-04-30 09:01:14,027 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-04-30 09:01:14,512 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-04-30 09:01:14,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26717.0, 'new_value': 27026.0}, {'field': 'total_amount', 'old_value': 27920.8, 'new_value': 28229.8}, {'field': 'order_count', 'old_value': 285, 'new_value': 288}]
2025-04-30 09:01:14,528 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-04-30 09:01:14,934 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-04-30 09:01:14,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100054.98, 'new_value': 107981.98}, {'field': 'total_amount', 'old_value': 102352.98, 'new_value': 110279.98}, {'field': 'order_count', 'old_value': 48, 'new_value': 52}]
2025-04-30 09:01:14,934 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-04-30 09:01:15,372 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-04-30 09:01:15,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252821.11, 'new_value': 259750.11}, {'field': 'total_amount', 'old_value': 257587.11, 'new_value': 264516.11}, {'field': 'order_count', 'old_value': 3665, 'new_value': 3776}]
2025-04-30 09:01:15,372 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-04-30 09:01:15,857 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-04-30 09:01:15,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32211.0, 'new_value': 33909.0}, {'field': 'total_amount', 'old_value': 32211.0, 'new_value': 33909.0}, {'field': 'order_count', 'old_value': 201, 'new_value': 211}]
2025-04-30 09:01:15,857 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-04-30 09:01:16,326 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-04-30 09:01:16,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77649.53, 'new_value': 80922.03}, {'field': 'offline_amount', 'old_value': 1382556.67, 'new_value': 1424500.49}, {'field': 'total_amount', 'old_value': 1460206.2, 'new_value': 1505422.52}, {'field': 'order_count', 'old_value': 10285, 'new_value': 10687}]
2025-04-30 09:01:16,326 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-04-30 09:01:16,764 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-04-30 09:01:16,764 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126746.0, 'new_value': 130478.0}, {'field': 'total_amount', 'old_value': 126746.0, 'new_value': 130478.0}, {'field': 'order_count', 'old_value': 568, 'new_value': 588}]
2025-04-30 09:01:16,764 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-04-30 09:01:17,202 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-04-30 09:01:17,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 607416.0, 'new_value': 628144.0}, {'field': 'total_amount', 'old_value': 618454.0, 'new_value': 639182.0}, {'field': 'order_count', 'old_value': 530, 'new_value': 544}]
2025-04-30 09:01:17,202 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKI
2025-04-30 09:01:17,624 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKI
2025-04-30 09:01:17,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12758.0, 'new_value': 13494.0}, {'field': 'total_amount', 'old_value': 12758.0, 'new_value': 13494.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-04-30 09:01:17,624 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-04-30 09:01:18,078 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-04-30 09:01:18,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259215.3, 'new_value': 261658.1}, {'field': 'total_amount', 'old_value': 259215.3, 'new_value': 261658.1}, {'field': 'order_count', 'old_value': 5541, 'new_value': 5592}]
2025-04-30 09:01:18,078 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-04-30 09:01:18,437 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-04-30 09:01:18,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44582.38, 'new_value': 45895.09}, {'field': 'offline_amount', 'old_value': 1106491.51, 'new_value': 1148291.16}, {'field': 'total_amount', 'old_value': 1151073.89, 'new_value': 1194186.25}, {'field': 'order_count', 'old_value': 5659, 'new_value': 5838}]
2025-04-30 09:01:18,437 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-04-30 09:01:18,875 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-04-30 09:01:18,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140193.59, 'new_value': 146595.8}, {'field': 'total_amount', 'old_value': 163460.97, 'new_value': 169863.18}, {'field': 'order_count', 'old_value': 12627, 'new_value': 12927}]
2025-04-30 09:01:18,875 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-04-30 09:01:19,454 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-04-30 09:01:19,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30132.0, 'new_value': 30889.0}, {'field': 'total_amount', 'old_value': 30132.0, 'new_value': 30889.0}, {'field': 'order_count', 'old_value': 154, 'new_value': 158}]
2025-04-30 09:01:19,454 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7J
2025-04-30 09:01:19,939 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7J
2025-04-30 09:01:19,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90708.0, 'new_value': 94604.0}, {'field': 'total_amount', 'old_value': 90708.0, 'new_value': 94604.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-04-30 09:01:19,939 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-04-30 09:01:20,361 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-04-30 09:01:20,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48659.56, 'new_value': 50218.58}, {'field': 'total_amount', 'old_value': 48659.56, 'new_value': 50218.58}, {'field': 'order_count', 'old_value': 756, 'new_value': 778}]
2025-04-30 09:01:20,361 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-04-30 09:01:20,846 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-04-30 09:01:20,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84899.75, 'new_value': 88620.5}, {'field': 'total_amount', 'old_value': 84899.75, 'new_value': 88620.5}, {'field': 'order_count', 'old_value': 295, 'new_value': 312}]
2025-04-30 09:01:20,846 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-04-30 09:01:21,253 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-04-30 09:01:21,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1064403.0, 'new_value': 1101201.0}, {'field': 'total_amount', 'old_value': 1064403.0, 'new_value': 1101201.0}, {'field': 'order_count', 'old_value': 4524, 'new_value': 4677}]
2025-04-30 09:01:21,253 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-04-30 09:01:21,737 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-04-30 09:01:21,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248286.38, 'new_value': 248584.38}, {'field': 'total_amount', 'old_value': 248286.38, 'new_value': 248584.38}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-04-30 09:01:21,737 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-04-30 09:01:22,207 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-04-30 09:01:22,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110158.0, 'new_value': 116058.0}, {'field': 'total_amount', 'old_value': 110158.0, 'new_value': 116058.0}, {'field': 'order_count', 'old_value': 298, 'new_value': 316}]
2025-04-30 09:01:22,207 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTJ
2025-04-30 09:01:22,613 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTJ
2025-04-30 09:01:22,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119502.0, 'new_value': 129390.0}, {'field': 'total_amount', 'old_value': 119502.0, 'new_value': 129390.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-04-30 09:01:22,613 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-04-30 09:01:23,192 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-04-30 09:01:23,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76038.0, 'new_value': 77145.2}, {'field': 'total_amount', 'old_value': 76038.0, 'new_value': 77145.2}, {'field': 'order_count', 'old_value': 2215, 'new_value': 2249}]
2025-04-30 09:01:23,192 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-04-30 09:01:23,645 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-04-30 09:01:23,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 277632.18, 'new_value': 285686.53}, {'field': 'total_amount', 'old_value': 277632.18, 'new_value': 285686.53}, {'field': 'order_count', 'old_value': 1994, 'new_value': 2042}]
2025-04-30 09:01:23,645 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M3
2025-04-30 09:01:24,083 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M3
2025-04-30 09:01:24,083 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7224.0, 'new_value': 7299.0}, {'field': 'offline_amount', 'old_value': 3608.5, 'new_value': 3703.0}, {'field': 'total_amount', 'old_value': 10832.5, 'new_value': 11002.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 96}]
2025-04-30 09:01:24,083 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-04-30 09:01:24,537 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-04-30 09:01:24,537 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114261.0, 'new_value': 117966.7}, {'field': 'total_amount', 'old_value': 114261.0, 'new_value': 117966.7}, {'field': 'order_count', 'old_value': 556, 'new_value': 576}]
2025-04-30 09:01:24,537 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-30 09:01:25,037 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-30 09:01:25,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189992.36, 'new_value': 194231.86}, {'field': 'total_amount', 'old_value': 189992.36, 'new_value': 194231.86}, {'field': 'order_count', 'old_value': 781, 'new_value': 799}]
2025-04-30 09:01:25,037 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-30 09:01:25,507 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-30 09:01:25,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189605.3, 'new_value': 194656.4}, {'field': 'total_amount', 'old_value': 189605.3, 'new_value': 194656.4}, {'field': 'order_count', 'old_value': 905, 'new_value': 929}]
2025-04-30 09:01:25,507 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-04-30 09:01:25,960 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-04-30 09:01:25,960 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17359.77, 'new_value': 17876.27}, {'field': 'offline_amount', 'old_value': 332585.75, 'new_value': 344011.15}, {'field': 'total_amount', 'old_value': 349945.52, 'new_value': 361887.42}, {'field': 'order_count', 'old_value': 17769, 'new_value': 18443}]
2025-04-30 09:01:25,960 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-04-30 09:01:26,320 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-04-30 09:01:26,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176621.0, 'new_value': 184347.0}, {'field': 'total_amount', 'old_value': 176621.0, 'new_value': 184347.0}, {'field': 'order_count', 'old_value': 12682, 'new_value': 13249}]
2025-04-30 09:01:26,320 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-04-30 09:01:26,789 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-04-30 09:01:26,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49617.73, 'new_value': 51389.25}, {'field': 'offline_amount', 'old_value': 48581.32, 'new_value': 50494.45}, {'field': 'total_amount', 'old_value': 98199.05, 'new_value': 101883.7}, {'field': 'order_count', 'old_value': 5195, 'new_value': 5366}]
2025-04-30 09:01:26,789 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-04-30 09:01:27,196 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-04-30 09:01:27,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157888.0, 'new_value': 169868.0}, {'field': 'total_amount', 'old_value': 170206.46, 'new_value': 182186.46}, {'field': 'order_count', 'old_value': 88, 'new_value': 89}]
2025-04-30 09:01:27,196 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-04-30 09:01:27,680 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-04-30 09:01:27,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25171.0, 'new_value': 26431.0}, {'field': 'offline_amount', 'old_value': 141625.0, 'new_value': 146070.0}, {'field': 'total_amount', 'old_value': 166796.0, 'new_value': 172501.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-04-30 09:01:27,680 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-04-30 09:01:28,134 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-04-30 09:01:28,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54998.0, 'new_value': 57305.9}, {'field': 'total_amount', 'old_value': 54998.0, 'new_value': 57305.9}, {'field': 'order_count', 'old_value': 299, 'new_value': 311}]
2025-04-30 09:01:28,134 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-04-30 09:01:28,588 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-04-30 09:01:28,588 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51475.0, 'new_value': 51538.0}, {'field': 'offline_amount', 'old_value': 47332.12, 'new_value': 50175.12}, {'field': 'total_amount', 'old_value': 98807.12, 'new_value': 101713.12}, {'field': 'order_count', 'old_value': 350, 'new_value': 364}]
2025-04-30 09:01:28,588 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-04-30 09:01:29,041 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-04-30 09:01:29,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2231.41, 'new_value': 2472.31}, {'field': 'offline_amount', 'old_value': 91534.89, 'new_value': 93889.39}, {'field': 'total_amount', 'old_value': 93766.3, 'new_value': 96361.7}, {'field': 'order_count', 'old_value': 491, 'new_value': 512}]
2025-04-30 09:01:29,041 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-04-30 09:01:29,526 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-04-30 09:01:29,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119827.95, 'new_value': 123315.84}, {'field': 'offline_amount', 'old_value': 136391.52, 'new_value': 140869.09}, {'field': 'total_amount', 'old_value': 256219.47, 'new_value': 264184.93}, {'field': 'order_count', 'old_value': 6582, 'new_value': 6793}]
2025-04-30 09:01:29,526 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-04-30 09:01:29,979 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-04-30 09:01:29,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41942.31, 'new_value': 42891.01}, {'field': 'offline_amount', 'old_value': 100446.0, 'new_value': 103477.0}, {'field': 'total_amount', 'old_value': 142388.31, 'new_value': 146368.01}, {'field': 'order_count', 'old_value': 1777, 'new_value': 1831}]
2025-04-30 09:01:29,979 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-04-30 09:01:30,542 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-04-30 09:01:30,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1831.38, 'new_value': 2149.38}, {'field': 'offline_amount', 'old_value': 70665.83, 'new_value': 72295.25}, {'field': 'total_amount', 'old_value': 72497.21, 'new_value': 74444.63}, {'field': 'order_count', 'old_value': 642, 'new_value': 672}]
2025-04-30 09:01:30,542 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-04-30 09:01:30,965 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-04-30 09:01:30,965 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11951.0, 'new_value': 12009.0}, {'field': 'offline_amount', 'old_value': 85439.0, 'new_value': 88641.0}, {'field': 'total_amount', 'old_value': 97390.0, 'new_value': 100650.0}, {'field': 'order_count', 'old_value': 719, 'new_value': 744}]
2025-04-30 09:01:30,965 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-04-30 09:01:31,497 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-04-30 09:01:31,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257323.3, 'new_value': 263205.3}, {'field': 'total_amount', 'old_value': 257323.3, 'new_value': 263205.3}, {'field': 'order_count', 'old_value': 1162, 'new_value': 1200}]
2025-04-30 09:01:31,497 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-04-30 09:01:31,934 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-04-30 09:01:31,934 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147146.0, 'new_value': 154827.0}, {'field': 'total_amount', 'old_value': 231945.0, 'new_value': 239626.0}, {'field': 'order_count', 'old_value': 527, 'new_value': 544}]
2025-04-30 09:01:31,934 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-04-30 09:01:32,372 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-04-30 09:01:32,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 169026.0, 'new_value': 176922.0}, {'field': 'offline_amount', 'old_value': 241332.0, 'new_value': 246323.0}, {'field': 'total_amount', 'old_value': 410358.0, 'new_value': 423245.0}, {'field': 'order_count', 'old_value': 953, 'new_value': 985}]
2025-04-30 09:01:32,372 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-04-30 09:01:32,857 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-04-30 09:01:32,857 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12240.0, 'new_value': 12994.0}, {'field': 'offline_amount', 'old_value': 174740.0, 'new_value': 194066.0}, {'field': 'total_amount', 'old_value': 186980.0, 'new_value': 207060.0}, {'field': 'order_count', 'old_value': 312, 'new_value': 337}]
2025-04-30 09:01:32,857 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD1
2025-04-30 09:01:33,311 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD1
2025-04-30 09:01:33,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16643.0, 'new_value': 19229.0}, {'field': 'total_amount', 'old_value': 16643.0, 'new_value': 19229.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-04-30 09:01:33,311 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-04-30 09:01:33,702 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-04-30 09:01:33,702 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57923.38, 'new_value': 59390.41}, {'field': 'offline_amount', 'old_value': 71118.51, 'new_value': 73774.47}, {'field': 'total_amount', 'old_value': 129041.89, 'new_value': 133164.88}, {'field': 'order_count', 'old_value': 6528, 'new_value': 6747}]
2025-04-30 09:01:33,702 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-04-30 09:01:34,280 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-04-30 09:01:34,280 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3610.0, 'new_value': 3720.0}, {'field': 'offline_amount', 'old_value': 44420.0, 'new_value': 46160.0}, {'field': 'total_amount', 'old_value': 48030.0, 'new_value': 49880.0}, {'field': 'order_count', 'old_value': 650, 'new_value': 688}]
2025-04-30 09:01:34,280 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-30 09:01:34,812 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-30 09:01:34,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25996.9, 'new_value': 26771.9}, {'field': 'total_amount', 'old_value': 25996.9, 'new_value': 26771.9}, {'field': 'order_count', 'old_value': 135, 'new_value': 140}]
2025-04-30 09:01:34,812 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-04-30 09:01:35,250 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-04-30 09:01:35,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 366861.0, 'new_value': 380868.0}, {'field': 'total_amount', 'old_value': 377651.0, 'new_value': 391658.0}, {'field': 'order_count', 'old_value': 300, 'new_value': 306}]
2025-04-30 09:01:35,250 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-04-30 09:01:35,704 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-04-30 09:01:35,704 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13373.57, 'new_value': 13442.17}, {'field': 'offline_amount', 'old_value': 31870.0, 'new_value': 34328.0}, {'field': 'total_amount', 'old_value': 45243.57, 'new_value': 47770.17}, {'field': 'order_count', 'old_value': 279, 'new_value': 285}]
2025-04-30 09:01:35,704 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-04-30 09:01:36,235 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-04-30 09:01:36,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4183940.0, 'new_value': 4867940.0}, {'field': 'total_amount', 'old_value': 5765391.0, 'new_value': 6449391.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-04-30 09:01:36,235 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY1
2025-04-30 09:01:36,704 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY1
2025-04-30 09:01:36,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7488.0, 'new_value': 9176.0}, {'field': 'total_amount', 'old_value': 15388.0, 'new_value': 17076.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-04-30 09:01:36,704 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-04-30 09:01:37,064 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-04-30 09:01:37,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128885.43, 'new_value': 132797.6}, {'field': 'total_amount', 'old_value': 128885.43, 'new_value': 132797.6}, {'field': 'order_count', 'old_value': 9166, 'new_value': 9447}]
2025-04-30 09:01:37,064 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-04-30 09:01:37,533 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-04-30 09:01:37,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 679036.73, 'new_value': 705465.03}, {'field': 'total_amount', 'old_value': 679036.73, 'new_value': 705465.03}, {'field': 'order_count', 'old_value': 3802, 'new_value': 3891}]
2025-04-30 09:01:37,533 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-04-30 09:01:38,096 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-04-30 09:01:38,096 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44436.0, 'new_value': 46416.0}, {'field': 'total_amount', 'old_value': 44436.0, 'new_value': 46416.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-30 09:01:38,096 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9M92
2025-04-30 09:01:38,566 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9M92
2025-04-30 09:01:38,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61039.0, 'new_value': 62019.0}, {'field': 'total_amount', 'old_value': 61039.0, 'new_value': 62019.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-04-30 09:01:38,566 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-04-30 09:01:39,019 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-04-30 09:01:39,019 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28195.3, 'new_value': 28921.7}, {'field': 'offline_amount', 'old_value': 77607.9, 'new_value': 80131.1}, {'field': 'total_amount', 'old_value': 105803.2, 'new_value': 109052.8}, {'field': 'order_count', 'old_value': 4434, 'new_value': 4577}]
2025-04-30 09:01:39,019 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-04-30 09:01:39,457 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-04-30 09:01:39,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11507.8, 'new_value': 11547.4}, {'field': 'offline_amount', 'old_value': 34160.54, 'new_value': 35480.83}, {'field': 'total_amount', 'old_value': 45668.34, 'new_value': 47028.23}, {'field': 'order_count', 'old_value': 469, 'new_value': 480}]
2025-04-30 09:01:39,457 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-04-30 09:01:40,004 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-04-30 09:01:40,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 398870.0, 'new_value': 427730.0}, {'field': 'total_amount', 'old_value': 398870.0, 'new_value': 427730.0}, {'field': 'order_count', 'old_value': 154, 'new_value': 163}]
2025-04-30 09:01:40,004 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-04-30 09:01:40,411 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-04-30 09:01:40,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306784.5, 'new_value': 339948.0}, {'field': 'total_amount', 'old_value': 306784.5, 'new_value': 339948.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 105}]
2025-04-30 09:01:40,411 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-04-30 09:01:40,833 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-04-30 09:01:40,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106457.0, 'new_value': 111397.0}, {'field': 'offline_amount', 'old_value': 1270917.0, 'new_value': 1313577.0}, {'field': 'total_amount', 'old_value': 1377374.0, 'new_value': 1424974.0}, {'field': 'order_count', 'old_value': 32084, 'new_value': 33308}]
2025-04-30 09:01:40,833 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M07
2025-04-30 09:01:41,271 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M07
2025-04-30 09:01:41,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55269.0, 'new_value': 70400.0}, {'field': 'total_amount', 'old_value': 62732.0, 'new_value': 77863.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 47}]
2025-04-30 09:01:41,271 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-04-30 09:01:41,740 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-04-30 09:01:41,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 370924.0, 'new_value': 373974.0}, {'field': 'total_amount', 'old_value': 382086.0, 'new_value': 385136.0}, {'field': 'order_count', 'old_value': 8580, 'new_value': 8650}]
2025-04-30 09:01:41,740 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-04-30 09:01:42,178 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-04-30 09:01:42,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51959.0, 'new_value': 52534.0}, {'field': 'total_amount', 'old_value': 51959.0, 'new_value': 52534.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-04-30 09:01:42,178 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-04-30 09:01:42,632 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-04-30 09:01:42,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 240282.39, 'new_value': 246926.19}, {'field': 'offline_amount', 'old_value': 523741.05, 'new_value': 538741.05}, {'field': 'total_amount', 'old_value': 764023.44, 'new_value': 785667.24}, {'field': 'order_count', 'old_value': 1734, 'new_value': 1784}]
2025-04-30 09:01:42,632 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-30 09:01:43,070 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-30 09:01:43,070 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 208803.25, 'new_value': 216293.23}, {'field': 'offline_amount', 'old_value': 176550.93, 'new_value': 180065.07}, {'field': 'total_amount', 'old_value': 385354.18, 'new_value': 396358.3}, {'field': 'order_count', 'old_value': 1292, 'new_value': 1329}]
2025-04-30 09:01:43,070 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-30 09:01:43,539 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-30 09:01:43,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173121.0, 'new_value': 225351.0}, {'field': 'total_amount', 'old_value': 173121.0, 'new_value': 225351.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 19}]
2025-04-30 09:01:43,539 - INFO - 日期 2025-04 处理完成 - 更新: 168 条，插入: 0 条，错误: 0 条
2025-04-30 09:01:43,539 - INFO - 数据同步完成！更新: 168 条，插入: 0 条，错误: 0 条
2025-04-30 09:01:43,539 - INFO - =================同步完成====================
2025-04-30 12:00:03,765 - INFO - =================使用默认全量同步=============
2025-04-30 12:00:04,891 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-30 12:00:04,891 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-30 12:00:04,922 - INFO - 开始处理日期: 2025-01
2025-04-30 12:00:04,922 - INFO - Request Parameters - Page 1:
2025-04-30 12:00:04,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:04,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:05,735 - INFO - Response - Page 1:
2025-04-30 12:00:05,939 - INFO - 第 1 页获取到 100 条记录
2025-04-30 12:00:05,939 - INFO - Request Parameters - Page 2:
2025-04-30 12:00:05,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:05,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:06,580 - INFO - Response - Page 2:
2025-04-30 12:00:06,783 - INFO - 第 2 页获取到 100 条记录
2025-04-30 12:00:06,783 - INFO - Request Parameters - Page 3:
2025-04-30 12:00:06,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:06,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:07,284 - INFO - Response - Page 3:
2025-04-30 12:00:07,487 - INFO - 第 3 页获取到 100 条记录
2025-04-30 12:00:07,487 - INFO - Request Parameters - Page 4:
2025-04-30 12:00:07,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:07,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:08,003 - INFO - Response - Page 4:
2025-04-30 12:00:08,207 - INFO - 第 4 页获取到 100 条记录
2025-04-30 12:00:08,207 - INFO - Request Parameters - Page 5:
2025-04-30 12:00:08,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:08,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:08,738 - INFO - Response - Page 5:
2025-04-30 12:00:08,942 - INFO - 第 5 页获取到 100 条记录
2025-04-30 12:00:08,942 - INFO - Request Parameters - Page 6:
2025-04-30 12:00:08,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:08,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:09,426 - INFO - Response - Page 6:
2025-04-30 12:00:09,630 - INFO - 第 6 页获取到 100 条记录
2025-04-30 12:00:09,630 - INFO - Request Parameters - Page 7:
2025-04-30 12:00:09,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:09,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:10,146 - INFO - Response - Page 7:
2025-04-30 12:00:10,349 - INFO - 第 7 页获取到 82 条记录
2025-04-30 12:00:10,349 - INFO - 查询完成，共获取到 682 条记录
2025-04-30 12:00:10,349 - INFO - 获取到 682 条表单数据
2025-04-30 12:00:10,349 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-30 12:00:10,365 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 12:00:10,365 - INFO - 开始处理日期: 2025-02
2025-04-30 12:00:10,365 - INFO - Request Parameters - Page 1:
2025-04-30 12:00:10,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:10,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:10,881 - INFO - Response - Page 1:
2025-04-30 12:00:11,084 - INFO - 第 1 页获取到 100 条记录
2025-04-30 12:00:11,084 - INFO - Request Parameters - Page 2:
2025-04-30 12:00:11,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:11,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:11,553 - INFO - Response - Page 2:
2025-04-30 12:00:11,757 - INFO - 第 2 页获取到 100 条记录
2025-04-30 12:00:11,757 - INFO - Request Parameters - Page 3:
2025-04-30 12:00:11,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:11,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:12,195 - INFO - Response - Page 3:
2025-04-30 12:00:12,398 - INFO - 第 3 页获取到 100 条记录
2025-04-30 12:00:12,398 - INFO - Request Parameters - Page 4:
2025-04-30 12:00:12,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:12,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:12,898 - INFO - Response - Page 4:
2025-04-30 12:00:13,102 - INFO - 第 4 页获取到 100 条记录
2025-04-30 12:00:13,102 - INFO - Request Parameters - Page 5:
2025-04-30 12:00:13,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:13,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:13,633 - INFO - Response - Page 5:
2025-04-30 12:00:13,837 - INFO - 第 5 页获取到 100 条记录
2025-04-30 12:00:13,837 - INFO - Request Parameters - Page 6:
2025-04-30 12:00:13,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:13,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:14,337 - INFO - Response - Page 6:
2025-04-30 12:00:14,541 - INFO - 第 6 页获取到 100 条记录
2025-04-30 12:00:14,541 - INFO - Request Parameters - Page 7:
2025-04-30 12:00:14,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:14,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:15,010 - INFO - Response - Page 7:
2025-04-30 12:00:15,213 - INFO - 第 7 页获取到 70 条记录
2025-04-30 12:00:15,213 - INFO - 查询完成，共获取到 670 条记录
2025-04-30 12:00:15,213 - INFO - 获取到 670 条表单数据
2025-04-30 12:00:15,213 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-30 12:00:15,229 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 12:00:15,229 - INFO - 开始处理日期: 2025-03
2025-04-30 12:00:15,229 - INFO - Request Parameters - Page 1:
2025-04-30 12:00:15,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:15,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:15,776 - INFO - Response - Page 1:
2025-04-30 12:00:15,979 - INFO - 第 1 页获取到 100 条记录
2025-04-30 12:00:15,979 - INFO - Request Parameters - Page 2:
2025-04-30 12:00:15,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:15,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:16,449 - INFO - Response - Page 2:
2025-04-30 12:00:16,652 - INFO - 第 2 页获取到 100 条记录
2025-04-30 12:00:16,652 - INFO - Request Parameters - Page 3:
2025-04-30 12:00:16,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:16,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:17,152 - INFO - Response - Page 3:
2025-04-30 12:00:17,356 - INFO - 第 3 页获取到 100 条记录
2025-04-30 12:00:17,356 - INFO - Request Parameters - Page 4:
2025-04-30 12:00:17,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:17,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:17,856 - INFO - Response - Page 4:
2025-04-30 12:00:18,059 - INFO - 第 4 页获取到 100 条记录
2025-04-30 12:00:18,059 - INFO - Request Parameters - Page 5:
2025-04-30 12:00:18,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:18,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:18,513 - INFO - Response - Page 5:
2025-04-30 12:00:18,716 - INFO - 第 5 页获取到 100 条记录
2025-04-30 12:00:18,716 - INFO - Request Parameters - Page 6:
2025-04-30 12:00:18,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:18,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:19,232 - INFO - Response - Page 6:
2025-04-30 12:00:19,436 - INFO - 第 6 页获取到 100 条记录
2025-04-30 12:00:19,436 - INFO - Request Parameters - Page 7:
2025-04-30 12:00:19,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:19,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:19,842 - INFO - Response - Page 7:
2025-04-30 12:00:20,046 - INFO - 第 7 页获取到 61 条记录
2025-04-30 12:00:20,046 - INFO - 查询完成，共获取到 661 条记录
2025-04-30 12:00:20,046 - INFO - 获取到 661 条表单数据
2025-04-30 12:00:20,046 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-30 12:00:20,061 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 12:00:20,061 - INFO - 开始处理日期: 2025-04
2025-04-30 12:00:20,061 - INFO - Request Parameters - Page 1:
2025-04-30 12:00:20,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:20,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:20,531 - INFO - Response - Page 1:
2025-04-30 12:00:20,734 - INFO - 第 1 页获取到 100 条记录
2025-04-30 12:00:20,734 - INFO - Request Parameters - Page 2:
2025-04-30 12:00:20,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:20,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:21,172 - INFO - Response - Page 2:
2025-04-30 12:00:21,375 - INFO - 第 2 页获取到 100 条记录
2025-04-30 12:00:21,375 - INFO - Request Parameters - Page 3:
2025-04-30 12:00:21,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:21,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:21,876 - INFO - Response - Page 3:
2025-04-30 12:00:22,079 - INFO - 第 3 页获取到 100 条记录
2025-04-30 12:00:22,079 - INFO - Request Parameters - Page 4:
2025-04-30 12:00:22,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:22,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:22,595 - INFO - Response - Page 4:
2025-04-30 12:00:22,798 - INFO - 第 4 页获取到 100 条记录
2025-04-30 12:00:22,798 - INFO - Request Parameters - Page 5:
2025-04-30 12:00:22,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:22,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:23,299 - INFO - Response - Page 5:
2025-04-30 12:00:23,502 - INFO - 第 5 页获取到 100 条记录
2025-04-30 12:00:23,502 - INFO - Request Parameters - Page 6:
2025-04-30 12:00:23,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:23,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:23,987 - INFO - Response - Page 6:
2025-04-30 12:00:24,190 - INFO - 第 6 页获取到 100 条记录
2025-04-30 12:00:24,190 - INFO - Request Parameters - Page 7:
2025-04-30 12:00:24,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:00:24,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:00:24,628 - INFO - Response - Page 7:
2025-04-30 12:00:24,831 - INFO - 第 7 页获取到 27 条记录
2025-04-30 12:00:24,831 - INFO - 查询完成，共获取到 627 条记录
2025-04-30 12:00:24,831 - INFO - 获取到 627 条表单数据
2025-04-30 12:00:24,831 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-30 12:00:24,831 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-30 12:00:25,316 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-30 12:00:25,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 264953.9, 'new_value': 274013.9}, {'field': 'total_amount', 'old_value': 420530.9, 'new_value': 429590.9}, {'field': 'order_count', 'old_value': 583, 'new_value': 604}]
2025-04-30 12:00:25,316 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-04-30 12:00:25,801 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-04-30 12:00:25,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89287.17, 'new_value': 92716.66}, {'field': 'offline_amount', 'old_value': 233303.0, 'new_value': 240453.9}, {'field': 'total_amount', 'old_value': 322590.17, 'new_value': 333170.56}, {'field': 'order_count', 'old_value': 11498, 'new_value': 11888}]
2025-04-30 12:00:25,801 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-04-30 12:00:26,255 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-04-30 12:00:26,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148478.84, 'new_value': 153546.27}, {'field': 'total_amount', 'old_value': 148478.84, 'new_value': 153546.27}, {'field': 'order_count', 'old_value': 775, 'new_value': 803}]
2025-04-30 12:00:26,255 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-04-30 12:00:26,755 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-04-30 12:00:26,755 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31783.71, 'new_value': 33920.71}, {'field': 'offline_amount', 'old_value': 16365.98, 'new_value': 16836.98}, {'field': 'total_amount', 'old_value': 48149.69, 'new_value': 50757.69}, {'field': 'order_count', 'old_value': 2143, 'new_value': 2267}]
2025-04-30 12:00:26,755 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MI3
2025-04-30 12:00:27,209 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MI3
2025-04-30 12:00:27,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243336.0, 'new_value': 247336.0}, {'field': 'total_amount', 'old_value': 243336.0, 'new_value': 247336.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-04-30 12:00:27,209 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-04-30 12:00:27,787 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-04-30 12:00:27,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7210.73, 'new_value': 7862.73}, {'field': 'offline_amount', 'old_value': 26264.41, 'new_value': 26916.41}, {'field': 'total_amount', 'old_value': 33475.14, 'new_value': 34779.14}, {'field': 'order_count', 'old_value': 5250, 'new_value': 5902}]
2025-04-30 12:00:27,787 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-04-30 12:00:28,272 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-04-30 12:00:28,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 570995.0, 'new_value': 584136.0}, {'field': 'total_amount', 'old_value': 657188.0, 'new_value': 670329.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 109}]
2025-04-30 12:00:28,272 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-30 12:00:28,726 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-30 12:00:28,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14145.03, 'new_value': 14634.66}, {'field': 'offline_amount', 'old_value': 52837.35, 'new_value': 54946.31}, {'field': 'total_amount', 'old_value': 66982.38, 'new_value': 69580.97}, {'field': 'order_count', 'old_value': 1280, 'new_value': 1334}]
2025-04-30 12:00:28,726 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY3
2025-04-30 12:00:29,195 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY3
2025-04-30 12:00:29,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48797.84, 'new_value': 50796.56}, {'field': 'total_amount', 'old_value': 48797.84, 'new_value': 50796.56}, {'field': 'order_count', 'old_value': 9779, 'new_value': 10189}]
2025-04-30 12:00:29,195 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-04-30 12:00:29,680 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-04-30 12:00:29,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79633.0, 'new_value': 83133.0}, {'field': 'total_amount', 'old_value': 145808.0, 'new_value': 149308.0}, {'field': 'order_count', 'old_value': 2087, 'new_value': 2157}]
2025-04-30 12:00:29,680 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M34
2025-04-30 12:00:30,180 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M34
2025-04-30 12:00:30,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23247.82, 'new_value': 23875.82}, {'field': 'total_amount', 'old_value': 23303.62, 'new_value': 23931.62}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-04-30 12:00:30,180 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M74
2025-04-30 12:00:30,649 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M74
2025-04-30 12:00:30,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58469.24, 'new_value': 63771.06}, {'field': 'total_amount', 'old_value': 58469.24, 'new_value': 63771.06}, {'field': 'order_count', 'old_value': 3252, 'new_value': 3659}]
2025-04-30 12:00:30,649 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-04-30 12:00:31,119 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-04-30 12:00:31,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97512.3, 'new_value': 99492.3}, {'field': 'total_amount', 'old_value': 97512.3, 'new_value': 99492.3}, {'field': 'order_count', 'old_value': 633, 'new_value': 651}]
2025-04-30 12:00:31,119 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-04-30 12:00:31,525 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-04-30 12:00:31,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 689674.9, 'new_value': 844452.1}, {'field': 'total_amount', 'old_value': 689674.9, 'new_value': 844452.1}, {'field': 'order_count', 'old_value': 88, 'new_value': 98}]
2025-04-30 12:00:31,525 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP4
2025-04-30 12:00:32,088 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP4
2025-04-30 12:00:32,088 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120936.0, 'new_value': 138801.0}, {'field': 'offline_amount', 'old_value': 133138.0, 'new_value': 133237.0}, {'field': 'total_amount', 'old_value': 254074.0, 'new_value': 272038.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 75}]
2025-04-30 12:00:32,088 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-04-30 12:00:32,604 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-04-30 12:00:32,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90590.7, 'new_value': 93115.53}, {'field': 'offline_amount', 'old_value': 862142.77, 'new_value': 892544.07}, {'field': 'total_amount', 'old_value': 952733.47, 'new_value': 985659.6}, {'field': 'order_count', 'old_value': 4211, 'new_value': 4310}]
2025-04-30 12:00:32,604 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU4
2025-04-30 12:00:33,027 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU4
2025-04-30 12:00:33,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44049.4, 'new_value': 49444.3}, {'field': 'total_amount', 'old_value': 44129.2, 'new_value': 49524.1}, {'field': 'order_count', 'old_value': 272, 'new_value': 300}]
2025-04-30 12:00:33,027 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MW4
2025-04-30 12:00:33,480 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MW4
2025-04-30 12:00:33,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84313.71, 'new_value': 86328.26}, {'field': 'total_amount', 'old_value': 87383.04, 'new_value': 89397.59}, {'field': 'order_count', 'old_value': 5188, 'new_value': 5310}]
2025-04-30 12:00:33,480 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-04-30 12:00:34,106 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-04-30 12:00:34,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 683870.24, 'new_value': 708686.06}, {'field': 'total_amount', 'old_value': 683870.24, 'new_value': 708686.06}, {'field': 'order_count', 'old_value': 4901, 'new_value': 5123}]
2025-04-30 12:00:34,106 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M25
2025-04-30 12:00:34,528 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M25
2025-04-30 12:00:34,528 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18542.0, 'new_value': 18851.0}, {'field': 'offline_amount', 'old_value': 252699.0, 'new_value': 285927.0}, {'field': 'total_amount', 'old_value': 271241.0, 'new_value': 304778.0}, {'field': 'order_count', 'old_value': 241, 'new_value': 261}]
2025-04-30 12:00:34,528 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M35
2025-04-30 12:00:34,982 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M35
2025-04-30 12:00:34,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92745.0, 'new_value': 104475.0}, {'field': 'total_amount', 'old_value': 92745.0, 'new_value': 104475.0}, {'field': 'order_count', 'old_value': 5310, 'new_value': 6088}]
2025-04-30 12:00:34,997 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-04-30 12:00:35,435 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-04-30 12:00:35,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61645.86, 'new_value': 64262.27}, {'field': 'offline_amount', 'old_value': 638805.0, 'new_value': 658850.97}, {'field': 'total_amount', 'old_value': 700450.86, 'new_value': 723113.24}, {'field': 'order_count', 'old_value': 2275, 'new_value': 2349}]
2025-04-30 12:00:35,435 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M85
2025-04-30 12:00:35,889 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M85
2025-04-30 12:00:35,889 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42977.0, 'new_value': 45276.0}, {'field': 'total_amount', 'old_value': 90457.0, 'new_value': 92756.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-04-30 12:00:35,904 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-04-30 12:00:36,358 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-04-30 12:00:36,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222655.14, 'new_value': 230643.02}, {'field': 'total_amount', 'old_value': 222655.14, 'new_value': 230643.02}, {'field': 'order_count', 'old_value': 1145, 'new_value': 1190}]
2025-04-30 12:00:36,358 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-04-30 12:00:36,843 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-04-30 12:00:36,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40846.0, 'new_value': 48114.0}, {'field': 'total_amount', 'old_value': 44853.0, 'new_value': 52121.0}, {'field': 'order_count', 'old_value': 215, 'new_value': 247}]
2025-04-30 12:00:36,843 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-04-30 12:00:37,437 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-04-30 12:00:37,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66796.28, 'new_value': 76822.23}, {'field': 'offline_amount', 'old_value': 48812.6, 'new_value': 54453.29}, {'field': 'total_amount', 'old_value': 115608.88, 'new_value': 131275.52}, {'field': 'order_count', 'old_value': 3963, 'new_value': 4487}]
2025-04-30 12:00:37,437 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-04-30 12:00:37,906 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-04-30 12:00:37,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146442.65, 'new_value': 155182.78}, {'field': 'total_amount', 'old_value': 146442.65, 'new_value': 155182.78}, {'field': 'order_count', 'old_value': 263, 'new_value': 285}]
2025-04-30 12:00:37,906 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK5
2025-04-30 12:00:38,297 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK5
2025-04-30 12:00:38,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22408.0, 'new_value': 23041.0}, {'field': 'total_amount', 'old_value': 27730.0, 'new_value': 28363.0}, {'field': 'order_count', 'old_value': 174, 'new_value': 178}]
2025-04-30 12:00:38,297 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML5
2025-04-30 12:00:38,766 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML5
2025-04-30 12:00:38,766 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2176.73, 'new_value': 9843.29}, {'field': 'offline_amount', 'old_value': 139779.99, 'new_value': 153384.98}, {'field': 'total_amount', 'old_value': 141956.72, 'new_value': 163228.27}, {'field': 'order_count', 'old_value': 650, 'new_value': 748}]
2025-04-30 12:00:38,766 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-04-30 12:00:39,204 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-04-30 12:00:39,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 229927.0, 'new_value': 241773.0}, {'field': 'total_amount', 'old_value': 242428.0, 'new_value': 254274.0}, {'field': 'order_count', 'old_value': 1307, 'new_value': 1357}]
2025-04-30 12:00:39,204 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-30 12:00:39,658 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-30 12:00:39,658 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25224.42, 'new_value': 27136.27}, {'field': 'offline_amount', 'old_value': 42536.14, 'new_value': 44925.82}, {'field': 'total_amount', 'old_value': 67760.56, 'new_value': 72062.09}, {'field': 'order_count', 'old_value': 2779, 'new_value': 2935}]
2025-04-30 12:00:39,658 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9ME9
2025-04-30 12:00:40,080 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9ME9
2025-04-30 12:00:40,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51768.0, 'new_value': 56242.0}, {'field': 'total_amount', 'old_value': 51768.0, 'new_value': 56242.0}, {'field': 'order_count', 'old_value': 920, 'new_value': 942}]
2025-04-30 12:00:40,080 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-30 12:00:40,518 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-30 12:00:40,518 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219651.16, 'new_value': 225268.06}, {'field': 'total_amount', 'old_value': 219651.16, 'new_value': 225268.06}, {'field': 'order_count', 'old_value': 340, 'new_value': 347}]
2025-04-30 12:00:40,518 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9ML9
2025-04-30 12:00:41,050 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9ML9
2025-04-30 12:00:41,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6126.5, 'new_value': 6983.5}, {'field': 'total_amount', 'old_value': 6513.5, 'new_value': 7370.5}, {'field': 'order_count', 'old_value': 30, 'new_value': 33}]
2025-04-30 12:00:41,050 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MO9
2025-04-30 12:00:41,535 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MO9
2025-04-30 12:00:41,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100329.42, 'new_value': 106156.24}, {'field': 'total_amount', 'old_value': 100329.42, 'new_value': 106156.24}, {'field': 'order_count', 'old_value': 184, 'new_value': 195}]
2025-04-30 12:00:41,535 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-30 12:00:41,988 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-30 12:00:41,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 409021.87, 'new_value': 423411.22}, {'field': 'total_amount', 'old_value': 410484.87, 'new_value': 424874.22}, {'field': 'order_count', 'old_value': 4944, 'new_value': 5120}]
2025-04-30 12:00:41,988 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-04-30 12:00:42,489 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-04-30 12:00:42,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 175842.78, 'new_value': 192502.8}, {'field': 'offline_amount', 'old_value': 25018.11, 'new_value': 26114.11}, {'field': 'total_amount', 'old_value': 200860.89, 'new_value': 218616.91}, {'field': 'order_count', 'old_value': 5675, 'new_value': 6267}]
2025-04-30 12:00:42,489 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-04-30 12:00:42,958 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-04-30 12:00:42,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9736.5, 'new_value': 10913.4}, {'field': 'offline_amount', 'old_value': 101343.9, 'new_value': 103634.9}, {'field': 'total_amount', 'old_value': 111080.4, 'new_value': 114548.3}, {'field': 'order_count', 'old_value': 149, 'new_value': 159}]
2025-04-30 12:00:42,958 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-04-30 12:00:43,443 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-04-30 12:00:43,443 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177086.3, 'new_value': 183342.3}, {'field': 'offline_amount', 'old_value': 705455.94, 'new_value': 729311.55}, {'field': 'total_amount', 'old_value': 882542.24, 'new_value': 912653.85}, {'field': 'order_count', 'old_value': 2000, 'new_value': 2086}]
2025-04-30 12:00:43,443 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-04-30 12:00:44,037 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-04-30 12:00:44,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20957.07, 'new_value': 21270.03}, {'field': 'offline_amount', 'old_value': 388954.3, 'new_value': 396503.1}, {'field': 'total_amount', 'old_value': 409911.37, 'new_value': 417773.13}, {'field': 'order_count', 'old_value': 2944, 'new_value': 3000}]
2025-04-30 12:00:44,037 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-04-30 12:00:44,506 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-04-30 12:00:44,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78346.95, 'new_value': 79932.72}, {'field': 'total_amount', 'old_value': 79928.29, 'new_value': 81514.06}, {'field': 'order_count', 'old_value': 3157, 'new_value': 3218}]
2025-04-30 12:00:44,506 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-04-30 12:00:44,960 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-04-30 12:00:44,960 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110649.41, 'new_value': 114003.11}, {'field': 'offline_amount', 'old_value': 319889.23, 'new_value': 328938.46}, {'field': 'total_amount', 'old_value': 430538.64, 'new_value': 442941.57}, {'field': 'order_count', 'old_value': 11109, 'new_value': 11456}]
2025-04-30 12:00:44,960 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNA
2025-04-30 12:00:45,554 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNA
2025-04-30 12:00:45,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216883.0, 'new_value': 246883.0}, {'field': 'total_amount', 'old_value': 220198.0, 'new_value': 250198.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 50}]
2025-04-30 12:00:45,554 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-04-30 12:00:46,023 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-04-30 12:00:46,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55717.1, 'new_value': 57515.1}, {'field': 'total_amount', 'old_value': 55717.1, 'new_value': 57515.1}, {'field': 'order_count', 'old_value': 452, 'new_value': 467}]
2025-04-30 12:00:46,023 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-04-30 12:00:46,492 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-04-30 12:00:46,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40526.67, 'new_value': 44405.34}, {'field': 'offline_amount', 'old_value': 42929.16, 'new_value': 46763.02}, {'field': 'total_amount', 'old_value': 83455.83, 'new_value': 91168.36}, {'field': 'order_count', 'old_value': 2961, 'new_value': 3245}]
2025-04-30 12:00:46,492 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-04-30 12:00:46,961 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-04-30 12:00:46,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15466.86, 'new_value': 15977.68}, {'field': 'total_amount', 'old_value': 23650.93, 'new_value': 24161.75}, {'field': 'order_count', 'old_value': 89, 'new_value': 92}]
2025-04-30 12:00:46,961 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-04-30 12:00:47,384 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-04-30 12:00:47,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43633.18, 'new_value': 44309.68}, {'field': 'offline_amount', 'old_value': 353230.28, 'new_value': 362545.54}, {'field': 'total_amount', 'old_value': 396863.46, 'new_value': 406855.22}, {'field': 'order_count', 'old_value': 3525, 'new_value': 3642}]
2025-04-30 12:00:47,384 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-04-30 12:00:47,869 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-04-30 12:00:47,869 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36993.0, 'new_value': 38369.0}, {'field': 'total_amount', 'old_value': 36993.0, 'new_value': 38369.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 126}]
2025-04-30 12:00:47,869 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9B
2025-04-30 12:00:48,369 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9B
2025-04-30 12:00:48,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90531.0, 'new_value': 95331.0}, {'field': 'total_amount', 'old_value': 90531.0, 'new_value': 95331.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-04-30 12:00:48,369 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-04-30 12:00:48,791 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-04-30 12:00:48,791 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64286.45, 'new_value': 66733.54}, {'field': 'offline_amount', 'old_value': 84397.82, 'new_value': 85420.82}, {'field': 'total_amount', 'old_value': 148684.27, 'new_value': 152154.36}, {'field': 'order_count', 'old_value': 6809, 'new_value': 6848}]
2025-04-30 12:00:48,807 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-04-30 12:00:49,339 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-04-30 12:00:49,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86338.0, 'new_value': 86960.0}, {'field': 'total_amount', 'old_value': 88919.0, 'new_value': 89541.0}, {'field': 'order_count', 'old_value': 360, 'new_value': 364}]
2025-04-30 12:00:49,339 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-04-30 12:00:49,886 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-04-30 12:00:49,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25502.6, 'new_value': 25921.8}, {'field': 'offline_amount', 'old_value': 60014.3, 'new_value': 61208.3}, {'field': 'total_amount', 'old_value': 85516.9, 'new_value': 87130.1}, {'field': 'order_count', 'old_value': 235, 'new_value': 245}]
2025-04-30 12:00:49,886 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJB
2025-04-30 12:00:50,371 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJB
2025-04-30 12:00:50,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51688.0, 'new_value': 54709.0}, {'field': 'total_amount', 'old_value': 51688.0, 'new_value': 54709.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 71}]
2025-04-30 12:00:50,371 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-04-30 12:00:50,903 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-04-30 12:00:50,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29047.5, 'new_value': 29906.5}, {'field': 'total_amount', 'old_value': 29047.5, 'new_value': 29906.5}, {'field': 'order_count', 'old_value': 136, 'new_value': 141}]
2025-04-30 12:00:50,903 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPB
2025-04-30 12:00:51,341 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPB
2025-04-30 12:00:51,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 312731.0, 'new_value': 325657.0}, {'field': 'total_amount', 'old_value': 441351.0, 'new_value': 454277.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 94}]
2025-04-30 12:00:51,341 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRB
2025-04-30 12:00:51,747 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRB
2025-04-30 12:00:51,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 291271.0, 'new_value': 317241.0}, {'field': 'total_amount', 'old_value': 520421.0, 'new_value': 546391.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 120}]
2025-04-30 12:00:51,747 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTB
2025-04-30 12:00:52,201 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTB
2025-04-30 12:00:52,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243759.0, 'new_value': 285094.0}, {'field': 'total_amount', 'old_value': 442659.0, 'new_value': 483994.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 109}]
2025-04-30 12:00:52,201 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-04-30 12:00:52,670 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-04-30 12:00:52,670 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58316.28, 'new_value': 60050.16}, {'field': 'offline_amount', 'old_value': 75142.68, 'new_value': 77084.15}, {'field': 'total_amount', 'old_value': 133458.96, 'new_value': 137134.31}, {'field': 'order_count', 'old_value': 5286, 'new_value': 5441}]
2025-04-30 12:00:52,670 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXB
2025-04-30 12:00:53,155 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXB
2025-04-30 12:00:53,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79966.52, 'new_value': 86713.68}, {'field': 'offline_amount', 'old_value': 113483.92, 'new_value': 124149.87}, {'field': 'total_amount', 'old_value': 193450.44, 'new_value': 210863.55}, {'field': 'order_count', 'old_value': 6705, 'new_value': 7361}]
2025-04-30 12:00:53,155 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML5
2025-04-30 12:00:53,593 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML5
2025-04-30 12:00:53,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87971.0, 'new_value': 90061.0}, {'field': 'total_amount', 'old_value': 87971.0, 'new_value': 90061.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-04-30 12:00:53,593 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT5
2025-04-30 12:00:54,171 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT5
2025-04-30 12:00:54,171 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15507.95, 'new_value': 16026.85}, {'field': 'offline_amount', 'old_value': 162371.06, 'new_value': 167244.26}, {'field': 'total_amount', 'old_value': 177879.01, 'new_value': 183271.11}, {'field': 'order_count', 'old_value': 4391, 'new_value': 4530}]
2025-04-30 12:00:54,171 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-04-30 12:00:54,609 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-04-30 12:00:54,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29293.0, 'new_value': 29906.0}, {'field': 'total_amount', 'old_value': 29293.0, 'new_value': 29906.0}, {'field': 'order_count', 'old_value': 279, 'new_value': 286}]
2025-04-30 12:00:54,609 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-04-30 12:00:55,063 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-04-30 12:00:55,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64907.67, 'new_value': 67755.14}, {'field': 'offline_amount', 'old_value': 72621.17, 'new_value': 75046.64}, {'field': 'total_amount', 'old_value': 137528.84, 'new_value': 142801.78}, {'field': 'order_count', 'old_value': 6031, 'new_value': 6263}]
2025-04-30 12:00:55,063 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-04-30 12:00:55,501 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-04-30 12:00:55,501 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57087.89, 'new_value': 59513.35}, {'field': 'offline_amount', 'old_value': 25239.33, 'new_value': 25688.97}, {'field': 'total_amount', 'old_value': 82327.22, 'new_value': 85202.32}, {'field': 'order_count', 'old_value': 3366, 'new_value': 3490}]
2025-04-30 12:00:55,501 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-04-30 12:00:56,001 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-04-30 12:00:56,001 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10633.06, 'new_value': 11012.76}, {'field': 'offline_amount', 'old_value': 83987.81, 'new_value': 86873.18}, {'field': 'total_amount', 'old_value': 94620.87, 'new_value': 97885.94}, {'field': 'order_count', 'old_value': 2592, 'new_value': 2667}]
2025-04-30 12:00:56,001 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-04-30 12:00:56,408 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-04-30 12:00:56,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350588.61, 'new_value': 362389.26}, {'field': 'total_amount', 'old_value': 350588.61, 'new_value': 362389.26}, {'field': 'order_count', 'old_value': 9139, 'new_value': 9468}]
2025-04-30 12:00:56,408 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-04-30 12:00:57,065 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-04-30 12:00:57,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36756.83, 'new_value': 39731.68}, {'field': 'offline_amount', 'old_value': 285132.2, 'new_value': 304842.0}, {'field': 'total_amount', 'old_value': 321889.03, 'new_value': 344573.68}, {'field': 'order_count', 'old_value': 9731, 'new_value': 10419}]
2025-04-30 12:00:57,065 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-04-30 12:00:57,503 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-04-30 12:00:57,503 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43629.0, 'new_value': 45049.0}, {'field': 'total_amount', 'old_value': 43629.0, 'new_value': 45049.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 114}]
2025-04-30 12:00:57,503 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS6
2025-04-30 12:00:58,081 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS6
2025-04-30 12:00:58,081 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25959.41, 'new_value': 28525.48}, {'field': 'offline_amount', 'old_value': 57366.18, 'new_value': 60811.55}, {'field': 'total_amount', 'old_value': 83325.59, 'new_value': 89337.03}, {'field': 'order_count', 'old_value': 734, 'new_value': 789}]
2025-04-30 12:00:58,081 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-04-30 12:00:58,535 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-04-30 12:00:58,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 431999.0, 'new_value': 444368.0}, {'field': 'total_amount', 'old_value': 431999.0, 'new_value': 444368.0}, {'field': 'order_count', 'old_value': 12526, 'new_value': 12920}]
2025-04-30 12:00:58,535 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY6
2025-04-30 12:00:59,020 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY6
2025-04-30 12:00:59,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6226.08, 'new_value': 6464.08}, {'field': 'total_amount', 'old_value': 6226.08, 'new_value': 6464.08}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-04-30 12:00:59,020 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M07
2025-04-30 12:00:59,520 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M07
2025-04-30 12:00:59,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95044.3, 'new_value': 104538.0}, {'field': 'total_amount', 'old_value': 101293.4, 'new_value': 110787.1}, {'field': 'order_count', 'old_value': 2517, 'new_value': 2776}]
2025-04-30 12:00:59,520 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-04-30 12:00:59,989 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-04-30 12:00:59,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33002.1, 'new_value': 33952.1}, {'field': 'total_amount', 'old_value': 33002.1, 'new_value': 33952.1}, {'field': 'order_count', 'old_value': 329, 'new_value': 333}]
2025-04-30 12:00:59,989 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-04-30 12:01:00,458 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-04-30 12:01:00,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97089.4, 'new_value': 100909.95}, {'field': 'offline_amount', 'old_value': 66196.14, 'new_value': 67839.24}, {'field': 'total_amount', 'old_value': 163285.54, 'new_value': 168749.19}, {'field': 'order_count', 'old_value': 8830, 'new_value': 8859}]
2025-04-30 12:01:00,458 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-04-30 12:01:00,943 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-04-30 12:01:00,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41377.16, 'new_value': 42700.16}, {'field': 'offline_amount', 'old_value': 255560.64, 'new_value': 262714.46}, {'field': 'total_amount', 'old_value': 296937.8, 'new_value': 305414.62}, {'field': 'order_count', 'old_value': 7234, 'new_value': 7403}]
2025-04-30 12:01:00,943 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-04-30 12:01:01,459 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-04-30 12:01:01,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9634.82, 'new_value': 9785.41}, {'field': 'offline_amount', 'old_value': 14815.57, 'new_value': 15085.61}, {'field': 'total_amount', 'old_value': 24450.39, 'new_value': 24871.02}, {'field': 'order_count', 'old_value': 1634, 'new_value': 1675}]
2025-04-30 12:01:01,459 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-04-30 12:01:01,897 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-04-30 12:01:01,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 296804.26, 'new_value': 306928.96}, {'field': 'total_amount', 'old_value': 312563.06, 'new_value': 322687.76}, {'field': 'order_count', 'old_value': 13082, 'new_value': 13479}]
2025-04-30 12:01:01,897 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-04-30 12:01:02,288 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-04-30 12:01:02,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 557461.67, 'new_value': 591037.77}, {'field': 'total_amount', 'old_value': 557461.67, 'new_value': 591037.77}, {'field': 'order_count', 'old_value': 5638, 'new_value': 5962}]
2025-04-30 12:01:02,288 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-04-30 12:01:02,726 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-04-30 12:01:02,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8578.0, 'new_value': 8898.0}, {'field': 'total_amount', 'old_value': 8579.0, 'new_value': 8899.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-04-30 12:01:02,726 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-04-30 12:01:03,227 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-04-30 12:01:03,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47352.0, 'new_value': 48579.0}, {'field': 'total_amount', 'old_value': 51028.0, 'new_value': 52255.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 94}]
2025-04-30 12:01:03,227 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-04-30 12:01:03,665 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-04-30 12:01:03,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121471.0, 'new_value': 122952.0}, {'field': 'total_amount', 'old_value': 121471.0, 'new_value': 122952.0}, {'field': 'order_count', 'old_value': 3870, 'new_value': 3917}]
2025-04-30 12:01:03,665 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-04-30 12:01:04,134 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-04-30 12:01:04,134 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 297703.74, 'new_value': 297977.94}, {'field': 'offline_amount', 'old_value': 16577.86, 'new_value': 25118.7}, {'field': 'total_amount', 'old_value': 314281.6, 'new_value': 323096.64}, {'field': 'order_count', 'old_value': 11204, 'new_value': 11501}]
2025-04-30 12:01:04,134 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-04-30 12:01:04,556 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-04-30 12:01:04,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9825.8, 'new_value': 10651.8}, {'field': 'total_amount', 'old_value': 18683.4, 'new_value': 19509.4}, {'field': 'order_count', 'old_value': 163, 'new_value': 168}]
2025-04-30 12:01:04,556 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-04-30 12:01:05,025 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-04-30 12:01:05,025 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207268.0, 'new_value': 216610.0}, {'field': 'total_amount', 'old_value': 207268.0, 'new_value': 216610.0}, {'field': 'order_count', 'old_value': 245, 'new_value': 263}]
2025-04-30 12:01:05,025 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-04-30 12:01:05,463 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-04-30 12:01:05,463 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 588715.09, 'new_value': 604713.48}, {'field': 'total_amount', 'old_value': 588715.09, 'new_value': 604713.48}, {'field': 'order_count', 'old_value': 11557, 'new_value': 11808}]
2025-04-30 12:01:05,463 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-04-30 12:01:05,932 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-04-30 12:01:05,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138974.6, 'new_value': 144551.9}, {'field': 'total_amount', 'old_value': 138974.6, 'new_value': 144551.9}, {'field': 'order_count', 'old_value': 258, 'new_value': 269}]
2025-04-30 12:01:05,932 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-04-30 12:01:06,401 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-04-30 12:01:06,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42222.0, 'new_value': 43284.0}, {'field': 'total_amount', 'old_value': 42222.0, 'new_value': 43284.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 140}]
2025-04-30 12:01:06,401 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV7
2025-04-30 12:01:06,839 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV7
2025-04-30 12:01:06,839 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86198.27, 'new_value': 95167.42}, {'field': 'offline_amount', 'old_value': 281804.95, 'new_value': 315236.93}, {'field': 'total_amount', 'old_value': 368003.22, 'new_value': 410404.35}, {'field': 'order_count', 'old_value': 17729, 'new_value': 19653}]
2025-04-30 12:01:06,839 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-04-30 12:01:07,293 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-04-30 12:01:07,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81798.17, 'new_value': 83631.55}, {'field': 'total_amount', 'old_value': 81798.17, 'new_value': 83631.55}, {'field': 'order_count', 'old_value': 2397, 'new_value': 2448}]
2025-04-30 12:01:07,293 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-04-30 12:01:07,793 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-04-30 12:01:07,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162070.67, 'new_value': 167984.35}, {'field': 'offline_amount', 'old_value': 451940.2, 'new_value': 467693.09}, {'field': 'total_amount', 'old_value': 614010.87, 'new_value': 635677.44}, {'field': 'order_count', 'old_value': 3135, 'new_value': 3198}]
2025-04-30 12:01:07,793 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-04-30 12:01:08,278 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-04-30 12:01:08,278 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 152286.73, 'new_value': 154960.83}, {'field': 'offline_amount', 'old_value': 120145.92, 'new_value': 122374.63}, {'field': 'total_amount', 'old_value': 272432.65, 'new_value': 277335.46}, {'field': 'order_count', 'old_value': 2755, 'new_value': 2807}]
2025-04-30 12:01:08,278 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-04-30 12:01:08,779 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-04-30 12:01:08,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61985.3, 'new_value': 65598.5}, {'field': 'offline_amount', 'old_value': 550785.7, 'new_value': 558277.7}, {'field': 'total_amount', 'old_value': 612771.0, 'new_value': 623876.2}, {'field': 'order_count', 'old_value': 1341, 'new_value': 1426}]
2025-04-30 12:01:08,779 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-04-30 12:01:09,232 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-04-30 12:01:09,232 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216992.26, 'new_value': 221237.33}, {'field': 'total_amount', 'old_value': 216992.26, 'new_value': 221237.33}, {'field': 'order_count', 'old_value': 4517, 'new_value': 4610}]
2025-04-30 12:01:09,232 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-04-30 12:01:09,670 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-04-30 12:01:09,670 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41713.9, 'new_value': 43396.7}, {'field': 'total_amount', 'old_value': 193692.95, 'new_value': 195375.75}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-04-30 12:01:09,670 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-04-30 12:01:10,108 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-04-30 12:01:10,108 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80139.72, 'new_value': 83122.01}, {'field': 'offline_amount', 'old_value': 44109.4, 'new_value': 45097.76}, {'field': 'total_amount', 'old_value': 124249.12, 'new_value': 128219.77}, {'field': 'order_count', 'old_value': 6379, 'new_value': 6645}]
2025-04-30 12:01:10,108 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-04-30 12:01:10,546 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-04-30 12:01:10,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368417.17, 'new_value': 379338.97}, {'field': 'total_amount', 'old_value': 368417.17, 'new_value': 379338.97}, {'field': 'order_count', 'old_value': 16049, 'new_value': 16535}]
2025-04-30 12:01:10,546 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-04-30 12:01:11,015 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-04-30 12:01:11,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 628107.82, 'new_value': 645880.38}, {'field': 'total_amount', 'old_value': 628107.82, 'new_value': 645880.38}, {'field': 'order_count', 'old_value': 6759, 'new_value': 7033}]
2025-04-30 12:01:11,015 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-04-30 12:01:11,469 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-04-30 12:01:11,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9003.27, 'new_value': 9714.72}, {'field': 'offline_amount', 'old_value': 36074.98, 'new_value': 36458.38}, {'field': 'total_amount', 'old_value': 45078.25, 'new_value': 46173.1}, {'field': 'order_count', 'old_value': 2029, 'new_value': 2095}]
2025-04-30 12:01:11,469 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-04-30 12:01:11,969 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-04-30 12:01:11,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9819.01, 'new_value': 10299.27}, {'field': 'offline_amount', 'old_value': 65784.13, 'new_value': 67806.31}, {'field': 'total_amount', 'old_value': 75603.14, 'new_value': 78105.58}, {'field': 'order_count', 'old_value': 3062, 'new_value': 3155}]
2025-04-30 12:01:11,969 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-04-30 12:01:12,376 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-04-30 12:01:12,376 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 530.21, 'new_value': 620.72}, {'field': 'offline_amount', 'old_value': 710208.47, 'new_value': 727803.67}, {'field': 'total_amount', 'old_value': 710738.68, 'new_value': 728424.39}, {'field': 'order_count', 'old_value': 1699, 'new_value': 1755}]
2025-04-30 12:01:12,376 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-04-30 12:01:12,829 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-04-30 12:01:12,829 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203581.0, 'new_value': 208943.0}, {'field': 'total_amount', 'old_value': 203581.0, 'new_value': 208943.0}, {'field': 'order_count', 'old_value': 631, 'new_value': 655}]
2025-04-30 12:01:12,829 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-04-30 12:01:13,283 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-04-30 12:01:13,283 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32054.9, 'new_value': 33137.9}, {'field': 'offline_amount', 'old_value': 202991.77, 'new_value': 208529.17}, {'field': 'total_amount', 'old_value': 235046.67, 'new_value': 241667.07}, {'field': 'order_count', 'old_value': 7870, 'new_value': 8103}]
2025-04-30 12:01:13,283 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUH
2025-04-30 12:01:13,752 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUH
2025-04-30 12:01:13,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41929.0, 'new_value': 45729.0}, {'field': 'total_amount', 'old_value': 41929.0, 'new_value': 45729.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-04-30 12:01:13,752 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-04-30 12:01:14,268 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-04-30 12:01:14,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260242.56, 'new_value': 269022.56}, {'field': 'total_amount', 'old_value': 260242.56, 'new_value': 269022.56}, {'field': 'order_count', 'old_value': 1443, 'new_value': 1493}]
2025-04-30 12:01:14,268 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-04-30 12:01:14,706 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-04-30 12:01:14,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 611890.45, 'new_value': 626662.45}, {'field': 'total_amount', 'old_value': 611890.45, 'new_value': 626662.45}, {'field': 'order_count', 'old_value': 2065, 'new_value': 2125}]
2025-04-30 12:01:14,706 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-04-30 12:01:15,128 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-04-30 12:01:15,128 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99150.02, 'new_value': 101891.35}, {'field': 'offline_amount', 'old_value': 204063.37, 'new_value': 211000.03}, {'field': 'total_amount', 'old_value': 303213.39, 'new_value': 312891.38}, {'field': 'order_count', 'old_value': 9383, 'new_value': 9727}]
2025-04-30 12:01:15,128 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-04-30 12:01:15,598 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-04-30 12:01:15,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2084.6, 'new_value': 2521.42}, {'field': 'offline_amount', 'old_value': 28345.91, 'new_value': 28875.51}, {'field': 'total_amount', 'old_value': 30430.51, 'new_value': 31396.93}, {'field': 'order_count', 'old_value': 1237, 'new_value': 1264}]
2025-04-30 12:01:15,598 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-04-30 12:01:16,004 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-04-30 12:01:16,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 868439.09, 'new_value': 895106.53}, {'field': 'total_amount', 'old_value': 868439.09, 'new_value': 895106.53}, {'field': 'order_count', 'old_value': 5998, 'new_value': 6215}]
2025-04-30 12:01:16,004 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-30 12:01:16,505 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-30 12:01:16,505 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168448.0, 'new_value': 174943.0}, {'field': 'offline_amount', 'old_value': 125276.0, 'new_value': 130390.0}, {'field': 'total_amount', 'old_value': 293724.0, 'new_value': 305333.0}, {'field': 'order_count', 'old_value': 11407, 'new_value': 11737}]
2025-04-30 12:01:16,505 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-04-30 12:01:16,989 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-04-30 12:01:16,989 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50397.52, 'new_value': 51904.2}, {'field': 'offline_amount', 'old_value': 188758.28, 'new_value': 192598.57}, {'field': 'total_amount', 'old_value': 239155.8, 'new_value': 244502.77}, {'field': 'order_count', 'old_value': 4704, 'new_value': 4839}]
2025-04-30 12:01:16,989 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHI
2025-04-30 12:01:17,474 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHI
2025-04-30 12:01:17,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17468.0, 'new_value': 20768.0}, {'field': 'total_amount', 'old_value': 17497.0, 'new_value': 20797.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-04-30 12:01:17,474 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-04-30 12:01:17,850 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-04-30 12:01:17,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241558.92, 'new_value': 249214.58}, {'field': 'total_amount', 'old_value': 241558.92, 'new_value': 249214.58}, {'field': 'order_count', 'old_value': 1861, 'new_value': 1925}]
2025-04-30 12:01:17,850 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMI
2025-04-30 12:01:18,335 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMI
2025-04-30 12:01:18,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35960.0, 'new_value': 47960.0}, {'field': 'total_amount', 'old_value': 35960.0, 'new_value': 47960.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-04-30 12:01:18,335 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-04-30 12:01:18,835 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-04-30 12:01:18,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28808.0, 'new_value': 29904.0}, {'field': 'total_amount', 'old_value': 28808.0, 'new_value': 29904.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 150}]
2025-04-30 12:01:18,835 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQI
2025-04-30 12:01:19,257 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQI
2025-04-30 12:01:19,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225959.85, 'new_value': 245555.35}, {'field': 'total_amount', 'old_value': 225959.85, 'new_value': 245555.35}, {'field': 'order_count', 'old_value': 9889, 'new_value': 10774}]
2025-04-30 12:01:19,273 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-04-30 12:01:19,664 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-04-30 12:01:19,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114347.81, 'new_value': 119083.41}, {'field': 'total_amount', 'old_value': 125864.81, 'new_value': 130600.41}, {'field': 'order_count', 'old_value': 990, 'new_value': 1013}]
2025-04-30 12:01:19,664 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-04-30 12:01:20,086 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-04-30 12:01:20,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1844796.0, 'new_value': 1900926.0}, {'field': 'total_amount', 'old_value': 1844796.0, 'new_value': 1900926.0}, {'field': 'order_count', 'old_value': 7469, 'new_value': 7691}]
2025-04-30 12:01:20,086 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-04-30 12:01:20,540 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-04-30 12:01:20,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69165.0, 'new_value': 71285.0}, {'field': 'total_amount', 'old_value': 69175.0, 'new_value': 71295.0}, {'field': 'order_count', 'old_value': 261, 'new_value': 269}]
2025-04-30 12:01:20,540 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-30 12:01:20,946 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-30 12:01:20,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100.2, 'new_value': 175.0}, {'field': 'offline_amount', 'old_value': 38053.24, 'new_value': 40230.24}, {'field': 'total_amount', 'old_value': 38153.44, 'new_value': 40405.24}, {'field': 'order_count', 'old_value': 305, 'new_value': 317}]
2025-04-30 12:01:20,946 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1J
2025-04-30 12:01:21,400 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1J
2025-04-30 12:01:21,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1074974.0, 'new_value': 1205467.0}, {'field': 'offline_amount', 'old_value': 531390.0, 'new_value': 572922.0}, {'field': 'total_amount', 'old_value': 1606364.0, 'new_value': 1778389.0}, {'field': 'order_count', 'old_value': 1338, 'new_value': 1517}]
2025-04-30 12:01:21,400 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-04-30 12:01:21,916 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-04-30 12:01:21,916 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100.0, 'new_value': 332.8}, {'field': 'total_amount', 'old_value': 132738.81, 'new_value': 132971.61}, {'field': 'order_count', 'old_value': 80, 'new_value': 81}]
2025-04-30 12:01:21,916 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-04-30 12:01:22,338 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-04-30 12:01:22,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13327.0, 'new_value': 13795.0}, {'field': 'total_amount', 'old_value': 13327.0, 'new_value': 13795.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-04-30 12:01:22,338 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-04-30 12:01:22,823 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-04-30 12:01:22,823 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89528.9, 'new_value': 90658.4}, {'field': 'offline_amount', 'old_value': 166584.3, 'new_value': 168874.9}, {'field': 'total_amount', 'old_value': 256113.2, 'new_value': 259533.3}, {'field': 'order_count', 'old_value': 4616, 'new_value': 4684}]
2025-04-30 12:01:22,823 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-04-30 12:01:23,277 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-04-30 12:01:23,277 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5235.0, 'new_value': 5555.0}, {'field': 'offline_amount', 'old_value': 24789.0, 'new_value': 25457.0}, {'field': 'total_amount', 'old_value': 30024.0, 'new_value': 31012.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 157}]
2025-04-30 12:01:23,277 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-04-30 12:01:23,683 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-04-30 12:01:23,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173844.75, 'new_value': 178188.17}, {'field': 'total_amount', 'old_value': 173844.75, 'new_value': 178188.17}, {'field': 'order_count', 'old_value': 7638, 'new_value': 7831}]
2025-04-30 12:01:23,683 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-04-30 12:01:24,121 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-04-30 12:01:24,121 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84247.0, 'new_value': 86197.0}, {'field': 'total_amount', 'old_value': 115944.0, 'new_value': 117894.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 154}]
2025-04-30 12:01:24,121 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-04-30 12:01:24,559 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-04-30 12:01:24,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 988316.4, 'new_value': 1016756.4}, {'field': 'total_amount', 'old_value': 988316.4, 'new_value': 1016756.4}, {'field': 'order_count', 'old_value': 1257, 'new_value': 1308}]
2025-04-30 12:01:24,559 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-04-30 12:01:24,997 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-04-30 12:01:24,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91489.3, 'new_value': 93671.6}, {'field': 'total_amount', 'old_value': 91489.3, 'new_value': 93671.6}, {'field': 'order_count', 'old_value': 302, 'new_value': 315}]
2025-04-30 12:01:24,997 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-04-30 12:01:25,497 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-04-30 12:01:25,497 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10763.22, 'new_value': 10875.49}, {'field': 'offline_amount', 'old_value': 419220.43, 'new_value': 430593.23}, {'field': 'total_amount', 'old_value': 429983.65, 'new_value': 441468.72}, {'field': 'order_count', 'old_value': 18105, 'new_value': 18588}]
2025-04-30 12:01:25,497 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-04-30 12:01:25,888 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-04-30 12:01:25,888 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 224078.15, 'new_value': 232066.47}, {'field': 'offline_amount', 'old_value': 43622.17, 'new_value': 44197.77}, {'field': 'total_amount', 'old_value': 267700.32, 'new_value': 276264.24}, {'field': 'order_count', 'old_value': 12731, 'new_value': 13026}]
2025-04-30 12:01:25,888 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-30 12:01:26,342 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-30 12:01:26,342 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58916.03, 'new_value': 61232.93}, {'field': 'offline_amount', 'old_value': 56157.63, 'new_value': 57999.09}, {'field': 'total_amount', 'old_value': 115073.66, 'new_value': 119232.02}, {'field': 'order_count', 'old_value': 2880, 'new_value': 2982}]
2025-04-30 12:01:26,342 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-04-30 12:01:26,811 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-04-30 12:01:26,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145931.0, 'new_value': 149439.0}, {'field': 'total_amount', 'old_value': 145931.0, 'new_value': 149439.0}, {'field': 'order_count', 'old_value': 2552, 'new_value': 2600}]
2025-04-30 12:01:26,811 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-04-30 12:01:27,265 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-04-30 12:01:27,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1764704.12, 'new_value': 1833667.3}, {'field': 'offline_amount', 'old_value': 284675.0, 'new_value': 291631.0}, {'field': 'total_amount', 'old_value': 2049379.12, 'new_value': 2125298.3}, {'field': 'order_count', 'old_value': 9902, 'new_value': 10212}]
2025-04-30 12:01:27,265 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-04-30 12:01:27,734 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-04-30 12:01:27,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3064.64, 'new_value': 3110.88}, {'field': 'offline_amount', 'old_value': 15350.76, 'new_value': 15611.28}, {'field': 'total_amount', 'old_value': 18415.4, 'new_value': 18722.16}, {'field': 'order_count', 'old_value': 664, 'new_value': 675}]
2025-04-30 12:01:27,734 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-04-30 12:01:28,219 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-04-30 12:01:28,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101458.71, 'new_value': 104825.84}, {'field': 'total_amount', 'old_value': 143599.25, 'new_value': 146966.38}, {'field': 'order_count', 'old_value': 4128, 'new_value': 4230}]
2025-04-30 12:01:28,219 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M5
2025-04-30 12:01:28,719 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M5
2025-04-30 12:01:28,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59871.0, 'new_value': 74425.0}, {'field': 'total_amount', 'old_value': 59871.0, 'new_value': 74425.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-04-30 12:01:28,719 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-04-30 12:01:29,126 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-04-30 12:01:29,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41780.92, 'new_value': 45152.92}, {'field': 'total_amount', 'old_value': 74417.3, 'new_value': 77789.3}, {'field': 'order_count', 'old_value': 2745, 'new_value': 2859}]
2025-04-30 12:01:29,126 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-04-30 12:01:29,548 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-04-30 12:01:29,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78859.0, 'new_value': 94559.0}, {'field': 'total_amount', 'old_value': 98859.0, 'new_value': 114559.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-04-30 12:01:29,548 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF
2025-04-30 12:01:30,017 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF
2025-04-30 12:01:30,017 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19023.0, 'new_value': 21151.9}, {'field': 'offline_amount', 'old_value': 205243.4, 'new_value': 233886.2}, {'field': 'total_amount', 'old_value': 224266.4, 'new_value': 255038.1}, {'field': 'order_count', 'old_value': 6962, 'new_value': 7801}]
2025-04-30 12:01:30,017 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-04-30 12:01:30,471 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-04-30 12:01:30,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1083491.0, 'new_value': 1127869.0}, {'field': 'total_amount', 'old_value': 1083656.0, 'new_value': 1128034.0}, {'field': 'order_count', 'old_value': 1269, 'new_value': 1313}]
2025-04-30 12:01:30,471 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-04-30 12:01:30,956 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-04-30 12:01:30,956 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 484315.42, 'new_value': 496934.26}, {'field': 'total_amount', 'old_value': 484315.42, 'new_value': 496934.26}, {'field': 'order_count', 'old_value': 3766, 'new_value': 3891}]
2025-04-30 12:01:30,956 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-04-30 12:01:31,440 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-04-30 12:01:31,440 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81587.7, 'new_value': 85608.28}, {'field': 'offline_amount', 'old_value': 993150.75, 'new_value': 1026207.32}, {'field': 'total_amount', 'old_value': 1043709.83, 'new_value': 1080786.98}, {'field': 'order_count', 'old_value': 4279, 'new_value': 4467}]
2025-04-30 12:01:31,440 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-04-30 12:01:31,847 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-04-30 12:01:31,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31269.92, 'new_value': 34597.62}, {'field': 'total_amount', 'old_value': 31269.92, 'new_value': 34597.62}, {'field': 'order_count', 'old_value': 1015, 'new_value': 1070}]
2025-04-30 12:01:31,847 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-04-30 12:01:32,269 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-04-30 12:01:32,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3848.0, 'new_value': 4064.0}, {'field': 'offline_amount', 'old_value': 21356.8, 'new_value': 21771.6}, {'field': 'total_amount', 'old_value': 25204.8, 'new_value': 25835.6}, {'field': 'order_count', 'old_value': 919, 'new_value': 938}]
2025-04-30 12:01:32,269 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX
2025-04-30 12:01:32,739 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX
2025-04-30 12:01:32,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19297.9, 'new_value': 23713.2}, {'field': 'total_amount', 'old_value': 19297.9, 'new_value': 23713.2}, {'field': 'order_count', 'old_value': 128, 'new_value': 144}]
2025-04-30 12:01:32,739 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-04-30 12:01:33,176 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-04-30 12:01:33,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280713.0, 'new_value': 294018.0}, {'field': 'total_amount', 'old_value': 280714.0, 'new_value': 294019.0}, {'field': 'order_count', 'old_value': 439, 'new_value': 462}]
2025-04-30 12:01:33,176 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-04-30 12:01:33,646 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-04-30 12:01:33,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76827.0, 'new_value': 79014.0}, {'field': 'offline_amount', 'old_value': 339604.0, 'new_value': 351417.0}, {'field': 'total_amount', 'old_value': 416431.0, 'new_value': 430431.0}, {'field': 'order_count', 'old_value': 1667, 'new_value': 1727}]
2025-04-30 12:01:33,646 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-04-30 12:01:34,115 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-04-30 12:01:34,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 563233.4, 'new_value': 595268.3}, {'field': 'total_amount', 'old_value': 563233.4, 'new_value': 595268.3}, {'field': 'order_count', 'old_value': 19775, 'new_value': 19782}]
2025-04-30 12:01:34,115 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-04-30 12:01:34,662 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-04-30 12:01:34,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90263.35, 'new_value': 93952.65}, {'field': 'offline_amount', 'old_value': 265402.43, 'new_value': 274937.11}, {'field': 'total_amount', 'old_value': 355665.78, 'new_value': 368889.76}, {'field': 'order_count', 'old_value': 2437, 'new_value': 2548}]
2025-04-30 12:01:34,662 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-04-30 12:01:35,069 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-04-30 12:01:35,069 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 220556.24, 'new_value': 226361.14}, {'field': 'offline_amount', 'old_value': 45415.99, 'new_value': 46208.26}, {'field': 'total_amount', 'old_value': 265972.23, 'new_value': 272569.4}, {'field': 'order_count', 'old_value': 14243, 'new_value': 14622}]
2025-04-30 12:01:35,069 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-04-30 12:01:35,507 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-04-30 12:01:35,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216487.0, 'new_value': 227346.0}, {'field': 'total_amount', 'old_value': 232361.0, 'new_value': 243220.0}, {'field': 'order_count', 'old_value': 327, 'new_value': 345}]
2025-04-30 12:01:35,507 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-04-30 12:01:36,054 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-04-30 12:01:36,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163077.9, 'new_value': 179172.9}, {'field': 'total_amount', 'old_value': 163077.9, 'new_value': 179172.9}, {'field': 'order_count', 'old_value': 17351, 'new_value': 19119}]
2025-04-30 12:01:36,054 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ1
2025-04-30 12:01:36,508 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ1
2025-04-30 12:01:36,508 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5625.0, 'new_value': 5985.0}, {'field': 'offline_amount', 'old_value': 28567.0, 'new_value': 29747.0}, {'field': 'total_amount', 'old_value': 34192.0, 'new_value': 35732.0}, {'field': 'order_count', 'old_value': 852, 'new_value': 898}]
2025-04-30 12:01:36,508 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK1
2025-04-30 12:01:36,914 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK1
2025-04-30 12:01:36,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94103.22, 'new_value': 98081.88}, {'field': 'total_amount', 'old_value': 94103.22, 'new_value': 98081.88}, {'field': 'order_count', 'old_value': 8456, 'new_value': 8771}]
2025-04-30 12:01:36,914 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM1
2025-04-30 12:01:37,430 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM1
2025-04-30 12:01:37,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210941.21, 'new_value': 217997.05}, {'field': 'total_amount', 'old_value': 210941.21, 'new_value': 217997.05}, {'field': 'order_count', 'old_value': 15428, 'new_value': 16044}]
2025-04-30 12:01:37,430 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-04-30 12:01:37,947 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-04-30 12:01:37,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51983.8, 'new_value': 57983.8}, {'field': 'total_amount', 'old_value': 58650.28, 'new_value': 64650.28}, {'field': 'order_count', 'old_value': 106, 'new_value': 116}]
2025-04-30 12:01:37,947 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-04-30 12:01:38,384 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-04-30 12:01:38,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246322.0, 'new_value': 256797.0}, {'field': 'total_amount', 'old_value': 246322.0, 'new_value': 256797.0}, {'field': 'order_count', 'old_value': 26568, 'new_value': 27780}]
2025-04-30 12:01:38,384 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-04-30 12:01:38,854 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-04-30 12:01:38,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229024.81, 'new_value': 240313.48}, {'field': 'total_amount', 'old_value': 234025.82, 'new_value': 245314.49}, {'field': 'order_count', 'old_value': 4070, 'new_value': 4233}]
2025-04-30 12:01:38,854 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS1
2025-04-30 12:01:39,260 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS1
2025-04-30 12:01:39,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22105.45, 'new_value': 23428.98}, {'field': 'offline_amount', 'old_value': 32201.46, 'new_value': 33069.11}, {'field': 'total_amount', 'old_value': 54306.91, 'new_value': 56498.09}, {'field': 'order_count', 'old_value': 2696, 'new_value': 2798}]
2025-04-30 12:01:39,260 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT1
2025-04-30 12:01:39,729 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT1
2025-04-30 12:01:39,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207257.0, 'new_value': 214228.0}, {'field': 'total_amount', 'old_value': 210775.0, 'new_value': 217746.0}, {'field': 'order_count', 'old_value': 29540, 'new_value': 29717}]
2025-04-30 12:01:39,729 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-04-30 12:01:40,183 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-04-30 12:01:40,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 734626.41, 'new_value': 742317.41}, {'field': 'total_amount', 'old_value': 734626.41, 'new_value': 742317.41}, {'field': 'order_count', 'old_value': 652, 'new_value': 660}]
2025-04-30 12:01:40,183 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-04-30 12:01:40,621 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-04-30 12:01:40,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100422.0, 'new_value': 103247.0}, {'field': 'total_amount', 'old_value': 100422.0, 'new_value': 103247.0}, {'field': 'order_count', 'old_value': 6890, 'new_value': 7119}]
2025-04-30 12:01:40,621 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-04-30 12:01:41,012 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-04-30 12:01:41,012 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117872.37, 'new_value': 125283.62}, {'field': 'total_amount', 'old_value': 189451.88, 'new_value': 196863.13}, {'field': 'order_count', 'old_value': 12420, 'new_value': 12921}]
2025-04-30 12:01:41,012 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-04-30 12:01:41,465 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-04-30 12:01:41,465 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60440.32, 'new_value': 64062.19}, {'field': 'total_amount', 'old_value': 96409.34, 'new_value': 100031.21}, {'field': 'order_count', 'old_value': 6591, 'new_value': 6849}]
2025-04-30 12:01:41,465 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-04-30 12:01:41,950 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-04-30 12:01:41,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 362603.0, 'new_value': 376739.0}, {'field': 'total_amount', 'old_value': 362603.0, 'new_value': 376739.0}, {'field': 'order_count', 'old_value': 8042, 'new_value': 8405}]
2025-04-30 12:01:41,950 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-04-30 12:01:42,419 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-04-30 12:01:42,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310036.31, 'new_value': 319969.93}, {'field': 'total_amount', 'old_value': 310036.31, 'new_value': 319969.93}, {'field': 'order_count', 'old_value': 901, 'new_value': 928}]
2025-04-30 12:01:42,419 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-04-30 12:01:42,873 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-04-30 12:01:42,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 455596.71, 'new_value': 466663.06}, {'field': 'total_amount', 'old_value': 458080.29, 'new_value': 469146.64}, {'field': 'order_count', 'old_value': 7150, 'new_value': 7346}]
2025-04-30 12:01:42,873 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-04-30 12:01:43,452 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-04-30 12:01:43,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15501053.0, 'new_value': 15977328.0}, {'field': 'total_amount', 'old_value': 15501053.0, 'new_value': 15977328.0}, {'field': 'order_count', 'old_value': 45945, 'new_value': 47387}]
2025-04-30 12:01:43,452 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-04-30 12:01:43,937 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-04-30 12:01:43,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1189537.64, 'new_value': 1230845.94}, {'field': 'total_amount', 'old_value': 1189537.64, 'new_value': 1230845.94}, {'field': 'order_count', 'old_value': 3637, 'new_value': 3766}]
2025-04-30 12:01:43,937 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-04-30 12:01:44,374 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-04-30 12:01:44,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1125466.32, 'new_value': 1165655.12}, {'field': 'total_amount', 'old_value': 1125466.32, 'new_value': 1165655.12}, {'field': 'order_count', 'old_value': 3892, 'new_value': 4039}]
2025-04-30 12:01:44,374 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-04-30 12:01:44,859 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-04-30 12:01:44,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 764703.9, 'new_value': 792020.55}, {'field': 'total_amount', 'old_value': 764703.9, 'new_value': 792020.55}, {'field': 'order_count', 'old_value': 3743, 'new_value': 3891}]
2025-04-30 12:01:44,859 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-04-30 12:01:45,360 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-04-30 12:01:45,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76505.31, 'new_value': 79217.38}, {'field': 'total_amount', 'old_value': 76505.31, 'new_value': 79217.38}, {'field': 'order_count', 'old_value': 1275, 'new_value': 1321}]
2025-04-30 12:01:45,360 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-04-30 12:01:45,813 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-04-30 12:01:45,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18653.57, 'new_value': 19555.61}, {'field': 'offline_amount', 'old_value': 35863.06, 'new_value': 37188.3}, {'field': 'total_amount', 'old_value': 54516.63, 'new_value': 56743.91}, {'field': 'order_count', 'old_value': 2573, 'new_value': 2688}]
2025-04-30 12:01:45,813 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-04-30 12:01:46,267 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-04-30 12:01:46,267 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25560.24, 'new_value': 26771.38}, {'field': 'offline_amount', 'old_value': 19494.55, 'new_value': 20152.55}, {'field': 'total_amount', 'old_value': 45054.79, 'new_value': 46923.93}, {'field': 'order_count', 'old_value': 2335, 'new_value': 2390}]
2025-04-30 12:01:46,267 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-04-30 12:01:46,736 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-04-30 12:01:46,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314433.0, 'new_value': 328329.0}, {'field': 'total_amount', 'old_value': 314433.0, 'new_value': 328329.0}, {'field': 'order_count', 'old_value': 12525, 'new_value': 12659}]
2025-04-30 12:01:46,736 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-04-30 12:01:47,190 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-04-30 12:01:47,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 769280.15, 'new_value': 795317.56}, {'field': 'total_amount', 'old_value': 769280.15, 'new_value': 795317.56}, {'field': 'order_count', 'old_value': 6368, 'new_value': 6642}]
2025-04-30 12:01:47,190 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-04-30 12:01:47,753 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-04-30 12:01:47,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134484.41, 'new_value': 137644.93}, {'field': 'total_amount', 'old_value': 134484.41, 'new_value': 137644.93}, {'field': 'order_count', 'old_value': 3499, 'new_value': 3594}]
2025-04-30 12:01:47,753 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-04-30 12:01:48,175 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-04-30 12:01:48,175 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48280.0, 'new_value': 61280.0}, {'field': 'total_amount', 'old_value': 57880.0, 'new_value': 70880.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-04-30 12:01:48,175 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD6
2025-04-30 12:01:48,675 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD6
2025-04-30 12:01:48,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15489.0, 'new_value': 16165.0}, {'field': 'total_amount', 'old_value': 15489.0, 'new_value': 16165.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-04-30 12:01:48,675 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-04-30 12:01:49,113 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-04-30 12:01:49,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4871110.57, 'new_value': 5061209.7}, {'field': 'total_amount', 'old_value': 4871110.57, 'new_value': 5061209.7}, {'field': 'order_count', 'old_value': 144336, 'new_value': 147835}]
2025-04-30 12:01:49,129 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-04-30 12:01:49,567 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-04-30 12:01:49,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1349812.0, 'new_value': 1388149.0}, {'field': 'total_amount', 'old_value': 1349812.0, 'new_value': 1388149.0}, {'field': 'order_count', 'old_value': 5694, 'new_value': 5884}]
2025-04-30 12:01:49,567 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-04-30 12:01:50,020 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-04-30 12:01:50,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50633.0, 'new_value': 52224.0}, {'field': 'total_amount', 'old_value': 50633.0, 'new_value': 52224.0}, {'field': 'order_count', 'old_value': 318, 'new_value': 326}]
2025-04-30 12:01:50,020 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-04-30 12:01:50,505 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-04-30 12:01:50,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261387.0, 'new_value': 271457.0}, {'field': 'total_amount', 'old_value': 261390.0, 'new_value': 271460.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 72}]
2025-04-30 12:01:50,505 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-04-30 12:01:51,037 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-04-30 12:01:51,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 795917.97, 'new_value': 823848.67}, {'field': 'total_amount', 'old_value': 831986.95, 'new_value': 859917.65}, {'field': 'order_count', 'old_value': 2120, 'new_value': 2183}]
2025-04-30 12:01:51,037 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-04-30 12:01:51,553 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-04-30 12:01:51,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385282.0, 'new_value': 393582.0}, {'field': 'total_amount', 'old_value': 385282.0, 'new_value': 393582.0}, {'field': 'order_count', 'old_value': 701, 'new_value': 721}]
2025-04-30 12:01:51,553 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-04-30 12:01:52,007 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-04-30 12:01:52,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63164.39, 'new_value': 65099.39}, {'field': 'total_amount', 'old_value': 63164.39, 'new_value': 65099.39}, {'field': 'order_count', 'old_value': 1071, 'new_value': 1109}]
2025-04-30 12:01:52,007 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-04-30 12:01:52,632 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-04-30 12:01:52,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18524.08, 'new_value': 19066.08}, {'field': 'offline_amount', 'old_value': 283289.0, 'new_value': 292644.0}, {'field': 'total_amount', 'old_value': 301813.08, 'new_value': 311710.08}, {'field': 'order_count', 'old_value': 1485, 'new_value': 1547}]
2025-04-30 12:01:52,632 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS6
2025-04-30 12:01:53,117 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS6
2025-04-30 12:01:53,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148467.0, 'new_value': 198467.0}, {'field': 'total_amount', 'old_value': 148467.0, 'new_value': 198467.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-04-30 12:01:53,117 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-04-30 12:01:53,555 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-04-30 12:01:53,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100706.51, 'new_value': 104120.18}, {'field': 'offline_amount', 'old_value': 356420.74, 'new_value': 368769.54}, {'field': 'total_amount', 'old_value': 457127.25, 'new_value': 472889.72}, {'field': 'order_count', 'old_value': 3406, 'new_value': 3543}]
2025-04-30 12:01:53,555 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU6
2025-04-30 12:01:53,977 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU6
2025-04-30 12:01:53,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147653.96, 'new_value': 159764.96}, {'field': 'total_amount', 'old_value': 147653.96, 'new_value': 159764.96}, {'field': 'order_count', 'old_value': 3472, 'new_value': 3782}]
2025-04-30 12:01:53,977 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-04-30 12:01:54,478 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-04-30 12:01:54,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 625260.16, 'new_value': 642575.14}, {'field': 'total_amount', 'old_value': 625260.16, 'new_value': 642575.14}, {'field': 'order_count', 'old_value': 5182, 'new_value': 5320}]
2025-04-30 12:01:54,478 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX6
2025-04-30 12:01:54,916 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX6
2025-04-30 12:01:54,916 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25593.9, 'new_value': 30982.9}, {'field': 'offline_amount', 'old_value': 108046.0, 'new_value': 117458.0}, {'field': 'total_amount', 'old_value': 133639.9, 'new_value': 148440.9}, {'field': 'order_count', 'old_value': 2741, 'new_value': 2999}]
2025-04-30 12:01:54,916 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M27
2025-04-30 12:01:55,353 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M27
2025-04-30 12:01:55,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180275.0, 'new_value': 180475.0}, {'field': 'total_amount', 'old_value': 279247.0, 'new_value': 279447.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 70}]
2025-04-30 12:01:55,353 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-04-30 12:01:55,870 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-04-30 12:01:55,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142824.2, 'new_value': 147297.5}, {'field': 'total_amount', 'old_value': 142824.2, 'new_value': 147297.5}, {'field': 'order_count', 'old_value': 3370, 'new_value': 3500}]
2025-04-30 12:01:55,870 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M67
2025-04-30 12:01:56,323 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M67
2025-04-30 12:01:56,323 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110452.4, 'new_value': 115326.9}, {'field': 'offline_amount', 'old_value': 29450.9, 'new_value': 30374.4}, {'field': 'total_amount', 'old_value': 139903.3, 'new_value': 145701.3}, {'field': 'order_count', 'old_value': 12121, 'new_value': 12567}]
2025-04-30 12:01:56,323 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-04-30 12:01:56,792 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-04-30 12:01:56,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76305.62, 'new_value': 80486.62}, {'field': 'total_amount', 'old_value': 76305.62, 'new_value': 80486.62}, {'field': 'order_count', 'old_value': 194, 'new_value': 212}]
2025-04-30 12:01:56,792 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-04-30 12:01:57,324 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-04-30 12:01:57,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 387315.6, 'new_value': 398630.8}, {'field': 'total_amount', 'old_value': 387315.6, 'new_value': 398630.8}, {'field': 'order_count', 'old_value': 1568, 'new_value': 1618}]
2025-04-30 12:01:57,324 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-04-30 12:01:57,825 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-04-30 12:01:57,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44393.7, 'new_value': 45994.93}, {'field': 'total_amount', 'old_value': 44393.7, 'new_value': 45994.93}, {'field': 'order_count', 'old_value': 2060, 'new_value': 2136}]
2025-04-30 12:01:57,825 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-04-30 12:01:58,262 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-04-30 12:01:58,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24087.0, 'new_value': 24687.0}, {'field': 'total_amount', 'old_value': 24087.0, 'new_value': 24687.0}, {'field': 'order_count', 'old_value': 230, 'new_value': 235}]
2025-04-30 12:01:58,262 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-04-30 12:01:58,732 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-04-30 12:01:58,732 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128571.78, 'new_value': 131069.32}, {'field': 'offline_amount', 'old_value': 241629.99, 'new_value': 248494.63}, {'field': 'total_amount', 'old_value': 370201.77, 'new_value': 379563.95}, {'field': 'order_count', 'old_value': 4370, 'new_value': 4472}]
2025-04-30 12:01:58,732 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK7
2025-04-30 12:01:59,248 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK7
2025-04-30 12:01:59,248 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8974.2, 'new_value': 9504.1}, {'field': 'offline_amount', 'old_value': 64347.0, 'new_value': 64846.0}, {'field': 'total_amount', 'old_value': 73321.2, 'new_value': 74350.1}, {'field': 'order_count', 'old_value': 50, 'new_value': 59}]
2025-04-30 12:01:59,248 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-04-30 12:01:59,654 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-04-30 12:01:59,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132049.0, 'new_value': 138405.0}, {'field': 'total_amount', 'old_value': 132049.0, 'new_value': 138405.0}, {'field': 'order_count', 'old_value': 1300, 'new_value': 1357}]
2025-04-30 12:01:59,654 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-04-30 12:02:00,186 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-04-30 12:02:00,186 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 318493.58, 'new_value': 329175.62}, {'field': 'offline_amount', 'old_value': 270869.22, 'new_value': 277983.89}, {'field': 'total_amount', 'old_value': 589362.8, 'new_value': 607159.51}, {'field': 'order_count', 'old_value': 16327, 'new_value': 16850}]
2025-04-30 12:02:00,186 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-04-30 12:02:00,702 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-04-30 12:02:00,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169327.0, 'new_value': 171435.0}, {'field': 'total_amount', 'old_value': 178734.0, 'new_value': 180842.0}, {'field': 'order_count', 'old_value': 712, 'new_value': 739}]
2025-04-30 12:02:00,702 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-04-30 12:02:01,140 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-04-30 12:02:01,140 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77878.35, 'new_value': 90353.3}, {'field': 'offline_amount', 'old_value': 82787.3, 'new_value': 93129.58}, {'field': 'total_amount', 'old_value': 160665.65, 'new_value': 183482.88}, {'field': 'order_count', 'old_value': 6650, 'new_value': 7570}]
2025-04-30 12:02:01,140 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-30 12:02:01,609 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-30 12:02:01,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159621.0, 'new_value': 167065.0}, {'field': 'total_amount', 'old_value': 177435.0, 'new_value': 184879.0}, {'field': 'order_count', 'old_value': 766, 'new_value': 803}]
2025-04-30 12:02:01,609 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-04-30 12:02:02,063 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-04-30 12:02:02,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35583.06, 'new_value': 40596.16}, {'field': 'offline_amount', 'old_value': 353427.71, 'new_value': 359986.38}, {'field': 'total_amount', 'old_value': 389010.77, 'new_value': 400582.54}, {'field': 'order_count', 'old_value': 4514, 'new_value': 4653}]
2025-04-30 12:02:02,063 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-04-30 12:02:02,563 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-04-30 12:02:02,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15441788.04, 'new_value': 15993280.47}, {'field': 'total_amount', 'old_value': 15441788.04, 'new_value': 15993280.47}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-04-30 12:02:02,563 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-04-30 12:02:03,033 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-04-30 12:02:03,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103654.0, 'new_value': 116430.0}, {'field': 'total_amount', 'old_value': 144095.0, 'new_value': 156871.0}, {'field': 'order_count', 'old_value': 972, 'new_value': 1058}]
2025-04-30 12:02:03,033 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-30 12:02:03,470 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-30 12:02:03,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37161.4, 'new_value': 43617.59}, {'field': 'total_amount', 'old_value': 37161.4, 'new_value': 43617.59}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-30 12:02:03,470 - INFO - 开始更新记录 - 表单实例ID: FINST-NLF66581Q4VURCQVEAQGU8UJUISX2V9JF4Z9MPF1
2025-04-30 12:02:03,955 - INFO - 更新表单数据成功: FINST-NLF66581Q4VURCQVEAQGU8UJUISX2V9JF4Z9MPF1
2025-04-30 12:02:03,955 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7333.76, 'new_value': 14639.0}, {'field': 'offline_amount', 'old_value': 10242.91, 'new_value': 26698.25}, {'field': 'total_amount', 'old_value': 17576.67, 'new_value': 41337.25}, {'field': 'order_count', 'old_value': 414, 'new_value': 1038}]
2025-04-30 12:02:03,955 - INFO - 日期 2025-04 处理完成 - 更新: 211 条，插入: 0 条，错误: 0 条
2025-04-30 12:02:03,955 - INFO - 数据同步完成！更新: 211 条，插入: 0 条，错误: 0 条
2025-04-30 12:02:03,955 - INFO - =================同步完成====================
2025-04-30 15:00:01,851 - INFO - =================使用默认全量同步=============
2025-04-30 15:00:03,007 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-30 15:00:03,007 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-30 15:00:03,023 - INFO - 开始处理日期: 2025-01
2025-04-30 15:00:03,023 - INFO - Request Parameters - Page 1:
2025-04-30 15:00:03,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:03,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:04,007 - INFO - Response - Page 1:
2025-04-30 15:00:04,210 - INFO - 第 1 页获取到 100 条记录
2025-04-30 15:00:04,210 - INFO - Request Parameters - Page 2:
2025-04-30 15:00:04,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:04,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:04,913 - INFO - Response - Page 2:
2025-04-30 15:00:05,116 - INFO - 第 2 页获取到 100 条记录
2025-04-30 15:00:05,116 - INFO - Request Parameters - Page 3:
2025-04-30 15:00:05,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:05,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:05,695 - INFO - Response - Page 3:
2025-04-30 15:00:05,898 - INFO - 第 3 页获取到 100 条记录
2025-04-30 15:00:05,898 - INFO - Request Parameters - Page 4:
2025-04-30 15:00:05,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:05,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:06,366 - INFO - Response - Page 4:
2025-04-30 15:00:06,570 - INFO - 第 4 页获取到 100 条记录
2025-04-30 15:00:06,570 - INFO - Request Parameters - Page 5:
2025-04-30 15:00:06,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:06,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:07,038 - INFO - Response - Page 5:
2025-04-30 15:00:07,241 - INFO - 第 5 页获取到 100 条记录
2025-04-30 15:00:07,241 - INFO - Request Parameters - Page 6:
2025-04-30 15:00:07,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:07,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:07,773 - INFO - Response - Page 6:
2025-04-30 15:00:07,976 - INFO - 第 6 页获取到 100 条记录
2025-04-30 15:00:07,976 - INFO - Request Parameters - Page 7:
2025-04-30 15:00:07,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:07,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:08,382 - INFO - Response - Page 7:
2025-04-30 15:00:08,585 - INFO - 第 7 页获取到 82 条记录
2025-04-30 15:00:08,585 - INFO - 查询完成，共获取到 682 条记录
2025-04-30 15:00:08,585 - INFO - 获取到 682 条表单数据
2025-04-30 15:00:08,585 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-30 15:00:08,601 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 15:00:08,601 - INFO - 开始处理日期: 2025-02
2025-04-30 15:00:08,601 - INFO - Request Parameters - Page 1:
2025-04-30 15:00:08,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:08,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:09,023 - INFO - Response - Page 1:
2025-04-30 15:00:09,226 - INFO - 第 1 页获取到 100 条记录
2025-04-30 15:00:09,226 - INFO - Request Parameters - Page 2:
2025-04-30 15:00:09,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:09,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:09,710 - INFO - Response - Page 2:
2025-04-30 15:00:09,913 - INFO - 第 2 页获取到 100 条记录
2025-04-30 15:00:09,913 - INFO - Request Parameters - Page 3:
2025-04-30 15:00:09,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:09,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:10,382 - INFO - Response - Page 3:
2025-04-30 15:00:10,585 - INFO - 第 3 页获取到 100 条记录
2025-04-30 15:00:10,585 - INFO - Request Parameters - Page 4:
2025-04-30 15:00:10,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:10,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:11,038 - INFO - Response - Page 4:
2025-04-30 15:00:11,241 - INFO - 第 4 页获取到 100 条记录
2025-04-30 15:00:11,241 - INFO - Request Parameters - Page 5:
2025-04-30 15:00:11,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:11,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:11,663 - INFO - Response - Page 5:
2025-04-30 15:00:11,866 - INFO - 第 5 页获取到 100 条记录
2025-04-30 15:00:11,866 - INFO - Request Parameters - Page 6:
2025-04-30 15:00:11,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:11,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:12,319 - INFO - Response - Page 6:
2025-04-30 15:00:12,523 - INFO - 第 6 页获取到 100 条记录
2025-04-30 15:00:12,523 - INFO - Request Parameters - Page 7:
2025-04-30 15:00:12,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:12,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:13,023 - INFO - Response - Page 7:
2025-04-30 15:00:13,226 - INFO - 第 7 页获取到 70 条记录
2025-04-30 15:00:13,226 - INFO - 查询完成，共获取到 670 条记录
2025-04-30 15:00:13,226 - INFO - 获取到 670 条表单数据
2025-04-30 15:00:13,226 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-30 15:00:13,241 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 15:00:13,241 - INFO - 开始处理日期: 2025-03
2025-04-30 15:00:13,241 - INFO - Request Parameters - Page 1:
2025-04-30 15:00:13,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:13,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:13,710 - INFO - Response - Page 1:
2025-04-30 15:00:13,913 - INFO - 第 1 页获取到 100 条记录
2025-04-30 15:00:13,913 - INFO - Request Parameters - Page 2:
2025-04-30 15:00:13,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:13,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:14,413 - INFO - Response - Page 2:
2025-04-30 15:00:14,616 - INFO - 第 2 页获取到 100 条记录
2025-04-30 15:00:14,616 - INFO - Request Parameters - Page 3:
2025-04-30 15:00:14,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:14,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:15,148 - INFO - Response - Page 3:
2025-04-30 15:00:15,351 - INFO - 第 3 页获取到 100 条记录
2025-04-30 15:00:15,351 - INFO - Request Parameters - Page 4:
2025-04-30 15:00:15,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:15,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:15,851 - INFO - Response - Page 4:
2025-04-30 15:00:16,054 - INFO - 第 4 页获取到 100 条记录
2025-04-30 15:00:16,054 - INFO - Request Parameters - Page 5:
2025-04-30 15:00:16,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:16,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:16,663 - INFO - Response - Page 5:
2025-04-30 15:00:16,866 - INFO - 第 5 页获取到 100 条记录
2025-04-30 15:00:16,866 - INFO - Request Parameters - Page 6:
2025-04-30 15:00:16,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:16,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:17,398 - INFO - Response - Page 6:
2025-04-30 15:00:17,601 - INFO - 第 6 页获取到 100 条记录
2025-04-30 15:00:17,601 - INFO - Request Parameters - Page 7:
2025-04-30 15:00:17,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:17,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:18,054 - INFO - Response - Page 7:
2025-04-30 15:00:18,257 - INFO - 第 7 页获取到 61 条记录
2025-04-30 15:00:18,257 - INFO - 查询完成，共获取到 661 条记录
2025-04-30 15:00:18,257 - INFO - 获取到 661 条表单数据
2025-04-30 15:00:18,257 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-30 15:00:18,273 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 15:00:18,273 - INFO - 开始处理日期: 2025-04
2025-04-30 15:00:18,273 - INFO - Request Parameters - Page 1:
2025-04-30 15:00:18,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:18,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:18,882 - INFO - Response - Page 1:
2025-04-30 15:00:19,085 - INFO - 第 1 页获取到 100 条记录
2025-04-30 15:00:19,085 - INFO - Request Parameters - Page 2:
2025-04-30 15:00:19,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:19,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:19,585 - INFO - Response - Page 2:
2025-04-30 15:00:19,788 - INFO - 第 2 页获取到 100 条记录
2025-04-30 15:00:19,788 - INFO - Request Parameters - Page 3:
2025-04-30 15:00:19,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:19,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:20,273 - INFO - Response - Page 3:
2025-04-30 15:00:20,476 - INFO - 第 3 页获取到 100 条记录
2025-04-30 15:00:20,476 - INFO - Request Parameters - Page 4:
2025-04-30 15:00:20,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:20,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:20,991 - INFO - Response - Page 4:
2025-04-30 15:00:21,194 - INFO - 第 4 页获取到 100 条记录
2025-04-30 15:00:21,194 - INFO - Request Parameters - Page 5:
2025-04-30 15:00:21,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:21,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:21,710 - INFO - Response - Page 5:
2025-04-30 15:00:21,913 - INFO - 第 5 页获取到 100 条记录
2025-04-30 15:00:21,913 - INFO - Request Parameters - Page 6:
2025-04-30 15:00:21,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:21,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:22,429 - INFO - Response - Page 6:
2025-04-30 15:00:22,632 - INFO - 第 6 页获取到 100 条记录
2025-04-30 15:00:22,632 - INFO - Request Parameters - Page 7:
2025-04-30 15:00:22,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:00:22,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:00:23,007 - INFO - Response - Page 7:
2025-04-30 15:00:23,210 - INFO - 第 7 页获取到 27 条记录
2025-04-30 15:00:23,210 - INFO - 查询完成，共获取到 627 条记录
2025-04-30 15:00:23,210 - INFO - 获取到 627 条表单数据
2025-04-30 15:00:23,210 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-30 15:00:23,210 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-04-30 15:00:23,632 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-04-30 15:00:23,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28333.74, 'new_value': 29668.94}, {'field': 'offline_amount', 'old_value': 21768.6, 'new_value': 23095.6}, {'field': 'total_amount', 'old_value': 50102.34, 'new_value': 52764.54}, {'field': 'order_count', 'old_value': 251, 'new_value': 265}]
2025-04-30 15:00:23,632 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-30 15:00:24,163 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-30 15:00:24,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134124.98, 'new_value': 159264.65}, {'field': 'total_amount', 'old_value': 745629.05, 'new_value': 770768.72}, {'field': 'order_count', 'old_value': 2449, 'new_value': 2528}]
2025-04-30 15:00:24,163 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-04-30 15:00:24,773 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-04-30 15:00:24,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46398.99, 'new_value': 47807.95}, {'field': 'offline_amount', 'old_value': 39622.0, 'new_value': 43345.0}, {'field': 'total_amount', 'old_value': 86020.99, 'new_value': 91152.95}, {'field': 'order_count', 'old_value': 1144, 'new_value': 1199}]
2025-04-30 15:00:24,773 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-04-30 15:00:25,132 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-04-30 15:00:25,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138907.28, 'new_value': 140598.28}, {'field': 'total_amount', 'old_value': 138907.28, 'new_value': 140598.28}, {'field': 'order_count', 'old_value': 126, 'new_value': 128}]
2025-04-30 15:00:25,148 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-04-30 15:00:25,632 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-04-30 15:00:25,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225270.0, 'new_value': 234961.0}, {'field': 'total_amount', 'old_value': 225270.0, 'new_value': 234961.0}, {'field': 'order_count', 'old_value': 7979, 'new_value': 8311}]
2025-04-30 15:00:25,632 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-04-30 15:00:26,038 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-04-30 15:00:26,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353788.0, 'new_value': 369556.0}, {'field': 'total_amount', 'old_value': 353788.0, 'new_value': 369556.0}, {'field': 'order_count', 'old_value': 7193, 'new_value': 7549}]
2025-04-30 15:00:26,038 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-04-30 15:00:26,476 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-04-30 15:00:26,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224448.75, 'new_value': 234435.07}, {'field': 'total_amount', 'old_value': 224448.75, 'new_value': 234435.07}, {'field': 'order_count', 'old_value': 15936, 'new_value': 16654}]
2025-04-30 15:00:26,476 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-04-30 15:00:26,991 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-04-30 15:00:26,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 301221.91, 'new_value': 313460.91}, {'field': 'total_amount', 'old_value': 301221.91, 'new_value': 313460.91}, {'field': 'order_count', 'old_value': 6406, 'new_value': 6694}]
2025-04-30 15:00:26,991 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-04-30 15:00:27,398 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-04-30 15:00:27,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233140.63, 'new_value': 241852.32}, {'field': 'total_amount', 'old_value': 233140.63, 'new_value': 241852.32}, {'field': 'order_count', 'old_value': 8663, 'new_value': 8961}]
2025-04-30 15:00:27,398 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-04-30 15:00:27,882 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-04-30 15:00:27,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87192.0, 'new_value': 91263.0}, {'field': 'offline_amount', 'old_value': 150635.0, 'new_value': 155610.0}, {'field': 'total_amount', 'old_value': 237827.0, 'new_value': 246873.0}, {'field': 'order_count', 'old_value': 5720, 'new_value': 5946}]
2025-04-30 15:00:27,882 - INFO - 日期 2025-04 处理完成 - 更新: 10 条，插入: 0 条，错误: 0 条
2025-04-30 15:00:27,882 - INFO - 数据同步完成！更新: 10 条，插入: 0 条，错误: 0 条
2025-04-30 15:00:27,882 - INFO - =================同步完成====================
2025-04-30 18:00:01,957 - INFO - =================使用默认全量同步=============
2025-04-30 18:00:03,098 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-30 18:00:03,098 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-30 18:00:03,114 - INFO - 开始处理日期: 2025-01
2025-04-30 18:00:03,129 - INFO - Request Parameters - Page 1:
2025-04-30 18:00:03,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:03,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:04,004 - INFO - Response - Page 1:
2025-04-30 18:00:04,207 - INFO - 第 1 页获取到 100 条记录
2025-04-30 18:00:04,207 - INFO - Request Parameters - Page 2:
2025-04-30 18:00:04,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:04,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:04,863 - INFO - Response - Page 2:
2025-04-30 18:00:05,067 - INFO - 第 2 页获取到 100 条记录
2025-04-30 18:00:05,067 - INFO - Request Parameters - Page 3:
2025-04-30 18:00:05,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:05,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:05,535 - INFO - Response - Page 3:
2025-04-30 18:00:05,739 - INFO - 第 3 页获取到 100 条记录
2025-04-30 18:00:05,739 - INFO - Request Parameters - Page 4:
2025-04-30 18:00:05,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:05,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:06,285 - INFO - Response - Page 4:
2025-04-30 18:00:06,488 - INFO - 第 4 页获取到 100 条记录
2025-04-30 18:00:06,488 - INFO - Request Parameters - Page 5:
2025-04-30 18:00:06,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:06,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:06,973 - INFO - Response - Page 5:
2025-04-30 18:00:07,176 - INFO - 第 5 页获取到 100 条记录
2025-04-30 18:00:07,176 - INFO - Request Parameters - Page 6:
2025-04-30 18:00:07,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:07,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:07,692 - INFO - Response - Page 6:
2025-04-30 18:00:07,895 - INFO - 第 6 页获取到 100 条记录
2025-04-30 18:00:07,895 - INFO - Request Parameters - Page 7:
2025-04-30 18:00:07,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:07,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:08,332 - INFO - Response - Page 7:
2025-04-30 18:00:08,535 - INFO - 第 7 页获取到 82 条记录
2025-04-30 18:00:08,535 - INFO - 查询完成，共获取到 682 条记录
2025-04-30 18:00:08,535 - INFO - 获取到 682 条表单数据
2025-04-30 18:00:08,535 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-30 18:00:08,551 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 18:00:08,551 - INFO - 开始处理日期: 2025-02
2025-04-30 18:00:08,551 - INFO - Request Parameters - Page 1:
2025-04-30 18:00:08,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:08,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:09,004 - INFO - Response - Page 1:
2025-04-30 18:00:09,207 - INFO - 第 1 页获取到 100 条记录
2025-04-30 18:00:09,207 - INFO - Request Parameters - Page 2:
2025-04-30 18:00:09,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:09,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:09,692 - INFO - Response - Page 2:
2025-04-30 18:00:09,895 - INFO - 第 2 页获取到 100 条记录
2025-04-30 18:00:09,895 - INFO - Request Parameters - Page 3:
2025-04-30 18:00:09,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:09,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:10,488 - INFO - Response - Page 3:
2025-04-30 18:00:10,692 - INFO - 第 3 页获取到 100 条记录
2025-04-30 18:00:10,692 - INFO - Request Parameters - Page 4:
2025-04-30 18:00:10,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:10,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:11,207 - INFO - Response - Page 4:
2025-04-30 18:00:11,410 - INFO - 第 4 页获取到 100 条记录
2025-04-30 18:00:11,410 - INFO - Request Parameters - Page 5:
2025-04-30 18:00:11,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:11,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:11,942 - INFO - Response - Page 5:
2025-04-30 18:00:12,145 - INFO - 第 5 页获取到 100 条记录
2025-04-30 18:00:12,145 - INFO - Request Parameters - Page 6:
2025-04-30 18:00:12,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:12,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:12,613 - INFO - Response - Page 6:
2025-04-30 18:00:12,817 - INFO - 第 6 页获取到 100 条记录
2025-04-30 18:00:12,817 - INFO - Request Parameters - Page 7:
2025-04-30 18:00:12,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:12,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:13,254 - INFO - Response - Page 7:
2025-04-30 18:00:13,457 - INFO - 第 7 页获取到 70 条记录
2025-04-30 18:00:13,457 - INFO - 查询完成，共获取到 670 条记录
2025-04-30 18:00:13,457 - INFO - 获取到 670 条表单数据
2025-04-30 18:00:13,457 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-30 18:00:13,473 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 18:00:13,473 - INFO - 开始处理日期: 2025-03
2025-04-30 18:00:13,473 - INFO - Request Parameters - Page 1:
2025-04-30 18:00:13,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:13,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:14,035 - INFO - Response - Page 1:
2025-04-30 18:00:14,238 - INFO - 第 1 页获取到 100 条记录
2025-04-30 18:00:14,238 - INFO - Request Parameters - Page 2:
2025-04-30 18:00:14,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:14,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:14,660 - INFO - Response - Page 2:
2025-04-30 18:00:14,863 - INFO - 第 2 页获取到 100 条记录
2025-04-30 18:00:14,863 - INFO - Request Parameters - Page 3:
2025-04-30 18:00:14,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:14,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:15,317 - INFO - Response - Page 3:
2025-04-30 18:00:15,520 - INFO - 第 3 页获取到 100 条记录
2025-04-30 18:00:15,520 - INFO - Request Parameters - Page 4:
2025-04-30 18:00:15,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:15,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:16,020 - INFO - Response - Page 4:
2025-04-30 18:00:16,223 - INFO - 第 4 页获取到 100 条记录
2025-04-30 18:00:16,223 - INFO - Request Parameters - Page 5:
2025-04-30 18:00:16,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:16,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:16,645 - INFO - Response - Page 5:
2025-04-30 18:00:16,848 - INFO - 第 5 页获取到 100 条记录
2025-04-30 18:00:16,848 - INFO - Request Parameters - Page 6:
2025-04-30 18:00:16,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:16,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:17,301 - INFO - Response - Page 6:
2025-04-30 18:00:17,504 - INFO - 第 6 页获取到 100 条记录
2025-04-30 18:00:17,504 - INFO - Request Parameters - Page 7:
2025-04-30 18:00:17,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:17,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:17,957 - INFO - Response - Page 7:
2025-04-30 18:00:18,160 - INFO - 第 7 页获取到 61 条记录
2025-04-30 18:00:18,160 - INFO - 查询完成，共获取到 661 条记录
2025-04-30 18:00:18,160 - INFO - 获取到 661 条表单数据
2025-04-30 18:00:18,160 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-30 18:00:18,176 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 18:00:18,176 - INFO - 开始处理日期: 2025-04
2025-04-30 18:00:18,176 - INFO - Request Parameters - Page 1:
2025-04-30 18:00:18,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:18,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:18,645 - INFO - Response - Page 1:
2025-04-30 18:00:18,848 - INFO - 第 1 页获取到 100 条记录
2025-04-30 18:00:18,848 - INFO - Request Parameters - Page 2:
2025-04-30 18:00:18,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:18,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:19,363 - INFO - Response - Page 2:
2025-04-30 18:00:19,567 - INFO - 第 2 页获取到 100 条记录
2025-04-30 18:00:19,567 - INFO - Request Parameters - Page 3:
2025-04-30 18:00:19,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:19,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:20,035 - INFO - Response - Page 3:
2025-04-30 18:00:20,238 - INFO - 第 3 页获取到 100 条记录
2025-04-30 18:00:20,238 - INFO - Request Parameters - Page 4:
2025-04-30 18:00:20,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:20,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:20,707 - INFO - Response - Page 4:
2025-04-30 18:00:20,910 - INFO - 第 4 页获取到 100 条记录
2025-04-30 18:00:20,910 - INFO - Request Parameters - Page 5:
2025-04-30 18:00:20,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:20,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:21,363 - INFO - Response - Page 5:
2025-04-30 18:00:21,567 - INFO - 第 5 页获取到 100 条记录
2025-04-30 18:00:21,567 - INFO - Request Parameters - Page 6:
2025-04-30 18:00:21,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:21,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:22,051 - INFO - Response - Page 6:
2025-04-30 18:00:22,254 - INFO - 第 6 页获取到 100 条记录
2025-04-30 18:00:22,254 - INFO - Request Parameters - Page 7:
2025-04-30 18:00:22,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:00:22,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:00:22,645 - INFO - Response - Page 7:
2025-04-30 18:00:22,848 - INFO - 第 7 页获取到 27 条记录
2025-04-30 18:00:22,848 - INFO - 查询完成，共获取到 627 条记录
2025-04-30 18:00:22,848 - INFO - 获取到 627 条表单数据
2025-04-30 18:00:22,848 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-30 18:00:22,848 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-04-30 18:00:23,520 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-04-30 18:00:23,520 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 392278.0, 'new_value': 403636.0}, {'field': 'offline_amount', 'old_value': 690323.0, 'new_value': 692994.0}, {'field': 'total_amount', 'old_value': 1082601.0, 'new_value': 1096630.0}, {'field': 'order_count', 'old_value': 1036, 'new_value': 1054}]
2025-04-30 18:00:23,520 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-30 18:00:23,988 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-30 18:00:23,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 696433.02, 'new_value': 683266.0}, {'field': 'total_amount', 'old_value': 696433.02, 'new_value': 683266.0}]
2025-04-30 18:00:24,004 - INFO - 日期 2025-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-04-30 18:00:24,004 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 0 条
2025-04-30 18:00:24,004 - INFO - =================同步完成====================
2025-04-30 21:00:02,001 - INFO - =================使用默认全量同步=============
2025-04-30 21:00:03,142 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-30 21:00:03,142 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-30 21:00:03,173 - INFO - 开始处理日期: 2025-01
2025-04-30 21:00:03,173 - INFO - Request Parameters - Page 1:
2025-04-30 21:00:03,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:03,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:04,079 - INFO - Response - Page 1:
2025-04-30 21:00:04,282 - INFO - 第 1 页获取到 100 条记录
2025-04-30 21:00:04,282 - INFO - Request Parameters - Page 2:
2025-04-30 21:00:04,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:04,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:04,751 - INFO - Response - Page 2:
2025-04-30 21:00:04,954 - INFO - 第 2 页获取到 100 条记录
2025-04-30 21:00:04,954 - INFO - Request Parameters - Page 3:
2025-04-30 21:00:04,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:04,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:05,517 - INFO - Response - Page 3:
2025-04-30 21:00:05,720 - INFO - 第 3 页获取到 100 条记录
2025-04-30 21:00:05,720 - INFO - Request Parameters - Page 4:
2025-04-30 21:00:05,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:05,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:06,235 - INFO - Response - Page 4:
2025-04-30 21:00:06,438 - INFO - 第 4 页获取到 100 条记录
2025-04-30 21:00:06,438 - INFO - Request Parameters - Page 5:
2025-04-30 21:00:06,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:06,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:06,907 - INFO - Response - Page 5:
2025-04-30 21:00:07,110 - INFO - 第 5 页获取到 100 条记录
2025-04-30 21:00:07,110 - INFO - Request Parameters - Page 6:
2025-04-30 21:00:07,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:07,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:07,595 - INFO - Response - Page 6:
2025-04-30 21:00:07,798 - INFO - 第 6 页获取到 100 条记录
2025-04-30 21:00:07,798 - INFO - Request Parameters - Page 7:
2025-04-30 21:00:07,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:07,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:08,282 - INFO - Response - Page 7:
2025-04-30 21:00:08,485 - INFO - 第 7 页获取到 82 条记录
2025-04-30 21:00:08,485 - INFO - 查询完成，共获取到 682 条记录
2025-04-30 21:00:08,485 - INFO - 获取到 682 条表单数据
2025-04-30 21:00:08,485 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-30 21:00:08,501 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 21:00:08,501 - INFO - 开始处理日期: 2025-02
2025-04-30 21:00:08,501 - INFO - Request Parameters - Page 1:
2025-04-30 21:00:08,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:08,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:09,110 - INFO - Response - Page 1:
2025-04-30 21:00:09,313 - INFO - 第 1 页获取到 100 条记录
2025-04-30 21:00:09,313 - INFO - Request Parameters - Page 2:
2025-04-30 21:00:09,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:09,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:09,782 - INFO - Response - Page 2:
2025-04-30 21:00:09,985 - INFO - 第 2 页获取到 100 条记录
2025-04-30 21:00:09,985 - INFO - Request Parameters - Page 3:
2025-04-30 21:00:09,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:09,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:10,470 - INFO - Response - Page 3:
2025-04-30 21:00:10,673 - INFO - 第 3 页获取到 100 条记录
2025-04-30 21:00:10,673 - INFO - Request Parameters - Page 4:
2025-04-30 21:00:10,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:10,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:11,141 - INFO - Response - Page 4:
2025-04-30 21:00:11,345 - INFO - 第 4 页获取到 100 条记录
2025-04-30 21:00:11,345 - INFO - Request Parameters - Page 5:
2025-04-30 21:00:11,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:11,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:11,845 - INFO - Response - Page 5:
2025-04-30 21:00:12,048 - INFO - 第 5 页获取到 100 条记录
2025-04-30 21:00:12,048 - INFO - Request Parameters - Page 6:
2025-04-30 21:00:12,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:12,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:12,485 - INFO - Response - Page 6:
2025-04-30 21:00:12,688 - INFO - 第 6 页获取到 100 条记录
2025-04-30 21:00:12,688 - INFO - Request Parameters - Page 7:
2025-04-30 21:00:12,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:12,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:13,126 - INFO - Response - Page 7:
2025-04-30 21:00:13,329 - INFO - 第 7 页获取到 70 条记录
2025-04-30 21:00:13,329 - INFO - 查询完成，共获取到 670 条记录
2025-04-30 21:00:13,329 - INFO - 获取到 670 条表单数据
2025-04-30 21:00:13,329 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-30 21:00:13,345 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 21:00:13,345 - INFO - 开始处理日期: 2025-03
2025-04-30 21:00:13,345 - INFO - Request Parameters - Page 1:
2025-04-30 21:00:13,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:13,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:13,798 - INFO - Response - Page 1:
2025-04-30 21:00:14,001 - INFO - 第 1 页获取到 100 条记录
2025-04-30 21:00:14,001 - INFO - Request Parameters - Page 2:
2025-04-30 21:00:14,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:14,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:14,470 - INFO - Response - Page 2:
2025-04-30 21:00:14,673 - INFO - 第 2 页获取到 100 条记录
2025-04-30 21:00:14,673 - INFO - Request Parameters - Page 3:
2025-04-30 21:00:14,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:14,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:15,126 - INFO - Response - Page 3:
2025-04-30 21:00:15,329 - INFO - 第 3 页获取到 100 条记录
2025-04-30 21:00:15,329 - INFO - Request Parameters - Page 4:
2025-04-30 21:00:15,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:15,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:15,891 - INFO - Response - Page 4:
2025-04-30 21:00:16,095 - INFO - 第 4 页获取到 100 条记录
2025-04-30 21:00:16,095 - INFO - Request Parameters - Page 5:
2025-04-30 21:00:16,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:16,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:16,579 - INFO - Response - Page 5:
2025-04-30 21:00:16,782 - INFO - 第 5 页获取到 100 条记录
2025-04-30 21:00:16,782 - INFO - Request Parameters - Page 6:
2025-04-30 21:00:16,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:16,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:17,251 - INFO - Response - Page 6:
2025-04-30 21:00:17,454 - INFO - 第 6 页获取到 100 条记录
2025-04-30 21:00:17,454 - INFO - Request Parameters - Page 7:
2025-04-30 21:00:17,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:17,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:17,876 - INFO - Response - Page 7:
2025-04-30 21:00:18,079 - INFO - 第 7 页获取到 61 条记录
2025-04-30 21:00:18,079 - INFO - 查询完成，共获取到 661 条记录
2025-04-30 21:00:18,079 - INFO - 获取到 661 条表单数据
2025-04-30 21:00:18,079 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-30 21:00:18,095 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 21:00:18,095 - INFO - 开始处理日期: 2025-04
2025-04-30 21:00:18,095 - INFO - Request Parameters - Page 1:
2025-04-30 21:00:18,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:18,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:18,626 - INFO - Response - Page 1:
2025-04-30 21:00:18,829 - INFO - 第 1 页获取到 100 条记录
2025-04-30 21:00:18,829 - INFO - Request Parameters - Page 2:
2025-04-30 21:00:18,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:18,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:19,345 - INFO - Response - Page 2:
2025-04-30 21:00:19,548 - INFO - 第 2 页获取到 100 条记录
2025-04-30 21:00:19,548 - INFO - Request Parameters - Page 3:
2025-04-30 21:00:19,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:19,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:20,016 - INFO - Response - Page 3:
2025-04-30 21:00:20,220 - INFO - 第 3 页获取到 100 条记录
2025-04-30 21:00:20,220 - INFO - Request Parameters - Page 4:
2025-04-30 21:00:20,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:20,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:20,751 - INFO - Response - Page 4:
2025-04-30 21:00:20,954 - INFO - 第 4 页获取到 100 条记录
2025-04-30 21:00:20,954 - INFO - Request Parameters - Page 5:
2025-04-30 21:00:20,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:20,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:21,563 - INFO - Response - Page 5:
2025-04-30 21:00:21,766 - INFO - 第 5 页获取到 100 条记录
2025-04-30 21:00:21,766 - INFO - Request Parameters - Page 6:
2025-04-30 21:00:21,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:21,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:22,423 - INFO - Response - Page 6:
2025-04-30 21:00:22,626 - INFO - 第 6 页获取到 100 条记录
2025-04-30 21:00:22,626 - INFO - Request Parameters - Page 7:
2025-04-30 21:00:22,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:00:22,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:00:23,016 - INFO - Response - Page 7:
2025-04-30 21:00:23,220 - INFO - 第 7 页获取到 27 条记录
2025-04-30 21:00:23,220 - INFO - 查询完成，共获取到 627 条记录
2025-04-30 21:00:23,220 - INFO - 获取到 627 条表单数据
2025-04-30 21:00:23,220 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-30 21:00:23,235 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-04-30 21:00:23,766 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-04-30 21:00:23,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190676.14, 'new_value': 196938.21}, {'field': 'total_amount', 'old_value': 190676.14, 'new_value': 196938.21}, {'field': 'order_count', 'old_value': 21099, 'new_value': 21837}]
2025-04-30 21:00:23,766 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-04-30 21:00:24,235 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-04-30 21:00:24,235 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3726692.53, 'new_value': 3825720.74}, {'field': 'total_amount', 'old_value': 3726692.53, 'new_value': 3825720.74}, {'field': 'order_count', 'old_value': 6474, 'new_value': 6633}]
2025-04-30 21:00:24,235 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-30 21:00:24,720 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-30 21:00:24,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125406.0, 'new_value': 131841.0}, {'field': 'total_amount', 'old_value': 131008.0, 'new_value': 137443.0}, {'field': 'order_count', 'old_value': 455, 'new_value': 476}]
2025-04-30 21:00:24,720 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I718UXUFQYW74TY65GTGPXF3UKTQW0AML7
2025-04-30 21:00:25,220 - INFO - 更新表单数据成功: FINST-2FD66I718UXUFQYW74TY65GTGPXF3UKTQW0AML7
2025-04-30 21:00:25,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13337.0, 'new_value': 16200.0}, {'field': 'offline_amount', 'old_value': 8750.0, 'new_value': 10738.0}, {'field': 'total_amount', 'old_value': 22087.0, 'new_value': 26938.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 169}]
2025-04-30 21:00:25,220 - INFO - 日期 2025-04 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-04-30 21:00:25,220 - INFO - 数据同步完成！更新: 4 条，插入: 0 条，错误: 0 条
2025-04-30 21:00:25,220 - INFO - =================同步完成====================
