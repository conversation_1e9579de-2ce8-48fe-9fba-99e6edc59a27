2025-05-01 00:00:04,006 - INFO - =================使用默认全量同步=============
2025-05-01 00:00:05,164 - INFO - MySQL查询成功，共获取 2640 条记录
2025-05-01 00:00:05,179 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-05-01 00:00:05,195 - INFO - 开始处理日期: 2025-01
2025-05-01 00:00:05,195 - INFO - Request Parameters - Page 1:
2025-05-01 00:00:05,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:05,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:06,259 - INFO - Response - Page 1:
2025-05-01 00:00:06,462 - INFO - 第 1 页获取到 100 条记录
2025-05-01 00:00:06,462 - INFO - Request Parameters - Page 2:
2025-05-01 00:00:06,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:06,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:06,978 - INFO - Response - Page 2:
2025-05-01 00:00:07,181 - INFO - 第 2 页获取到 100 条记录
2025-05-01 00:00:07,181 - INFO - Request Parameters - Page 3:
2025-05-01 00:00:07,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:07,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:07,635 - INFO - Response - Page 3:
2025-05-01 00:00:07,838 - INFO - 第 3 页获取到 100 条记录
2025-05-01 00:00:07,838 - INFO - Request Parameters - Page 4:
2025-05-01 00:00:07,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:07,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:08,401 - INFO - Response - Page 4:
2025-05-01 00:00:08,605 - INFO - 第 4 页获取到 100 条记录
2025-05-01 00:00:08,605 - INFO - Request Parameters - Page 5:
2025-05-01 00:00:08,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:08,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:09,074 - INFO - Response - Page 5:
2025-05-01 00:00:09,277 - INFO - 第 5 页获取到 100 条记录
2025-05-01 00:00:09,277 - INFO - Request Parameters - Page 6:
2025-05-01 00:00:09,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:09,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:09,793 - INFO - Response - Page 6:
2025-05-01 00:00:09,997 - INFO - 第 6 页获取到 100 条记录
2025-05-01 00:00:09,997 - INFO - Request Parameters - Page 7:
2025-05-01 00:00:09,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:09,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:10,513 - INFO - Response - Page 7:
2025-05-01 00:00:10,716 - INFO - 第 7 页获取到 82 条记录
2025-05-01 00:00:10,716 - INFO - 查询完成，共获取到 682 条记录
2025-05-01 00:00:10,716 - INFO - 获取到 682 条表单数据
2025-05-01 00:00:10,716 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-01 00:00:10,732 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 00:00:10,732 - INFO - 开始处理日期: 2025-02
2025-05-01 00:00:10,732 - INFO - Request Parameters - Page 1:
2025-05-01 00:00:10,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:10,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:11,217 - INFO - Response - Page 1:
2025-05-01 00:00:11,420 - INFO - 第 1 页获取到 100 条记录
2025-05-01 00:00:11,420 - INFO - Request Parameters - Page 2:
2025-05-01 00:00:11,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:11,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:11,952 - INFO - Response - Page 2:
2025-05-01 00:00:12,155 - INFO - 第 2 页获取到 100 条记录
2025-05-01 00:00:12,155 - INFO - Request Parameters - Page 3:
2025-05-01 00:00:12,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:12,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:12,624 - INFO - Response - Page 3:
2025-05-01 00:00:12,828 - INFO - 第 3 页获取到 100 条记录
2025-05-01 00:00:12,828 - INFO - Request Parameters - Page 4:
2025-05-01 00:00:12,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:12,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:13,375 - INFO - Response - Page 4:
2025-05-01 00:00:13,578 - INFO - 第 4 页获取到 100 条记录
2025-05-01 00:00:13,578 - INFO - Request Parameters - Page 5:
2025-05-01 00:00:13,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:13,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:14,220 - INFO - Response - Page 5:
2025-05-01 00:00:14,423 - INFO - 第 5 页获取到 100 条记录
2025-05-01 00:00:14,423 - INFO - Request Parameters - Page 6:
2025-05-01 00:00:14,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:14,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:14,892 - INFO - Response - Page 6:
2025-05-01 00:00:15,095 - INFO - 第 6 页获取到 100 条记录
2025-05-01 00:00:15,095 - INFO - Request Parameters - Page 7:
2025-05-01 00:00:15,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:15,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:15,565 - INFO - Response - Page 7:
2025-05-01 00:00:15,768 - INFO - 第 7 页获取到 70 条记录
2025-05-01 00:00:15,768 - INFO - 查询完成，共获取到 670 条记录
2025-05-01 00:00:15,768 - INFO - 获取到 670 条表单数据
2025-05-01 00:00:15,768 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-01 00:00:15,784 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 00:00:15,784 - INFO - 开始处理日期: 2025-03
2025-05-01 00:00:15,784 - INFO - Request Parameters - Page 1:
2025-05-01 00:00:15,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:15,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:16,284 - INFO - Response - Page 1:
2025-05-01 00:00:16,487 - INFO - 第 1 页获取到 100 条记录
2025-05-01 00:00:16,487 - INFO - Request Parameters - Page 2:
2025-05-01 00:00:16,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:16,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:17,019 - INFO - Response - Page 2:
2025-05-01 00:00:17,222 - INFO - 第 2 页获取到 100 条记录
2025-05-01 00:00:17,222 - INFO - Request Parameters - Page 3:
2025-05-01 00:00:17,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:17,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:17,660 - INFO - Response - Page 3:
2025-05-01 00:00:17,879 - INFO - 第 3 页获取到 100 条记录
2025-05-01 00:00:17,879 - INFO - Request Parameters - Page 4:
2025-05-01 00:00:17,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:17,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:18,364 - INFO - Response - Page 4:
2025-05-01 00:00:18,568 - INFO - 第 4 页获取到 100 条记录
2025-05-01 00:00:18,568 - INFO - Request Parameters - Page 5:
2025-05-01 00:00:18,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:18,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:19,068 - INFO - Response - Page 5:
2025-05-01 00:00:19,271 - INFO - 第 5 页获取到 100 条记录
2025-05-01 00:00:19,271 - INFO - Request Parameters - Page 6:
2025-05-01 00:00:19,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:19,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:19,756 - INFO - Response - Page 6:
2025-05-01 00:00:19,960 - INFO - 第 6 页获取到 100 条记录
2025-05-01 00:00:19,960 - INFO - Request Parameters - Page 7:
2025-05-01 00:00:19,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:19,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:20,444 - INFO - Response - Page 7:
2025-05-01 00:00:20,648 - INFO - 第 7 页获取到 61 条记录
2025-05-01 00:00:20,648 - INFO - 查询完成，共获取到 661 条记录
2025-05-01 00:00:20,648 - INFO - 获取到 661 条表单数据
2025-05-01 00:00:20,648 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-01 00:00:20,663 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 00:00:20,663 - INFO - 开始处理日期: 2025-04
2025-05-01 00:00:20,663 - INFO - Request Parameters - Page 1:
2025-05-01 00:00:20,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:20,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:21,117 - INFO - Response - Page 1:
2025-05-01 00:00:21,320 - INFO - 第 1 页获取到 100 条记录
2025-05-01 00:00:21,320 - INFO - Request Parameters - Page 2:
2025-05-01 00:00:21,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:21,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:21,821 - INFO - Response - Page 2:
2025-05-01 00:00:22,024 - INFO - 第 2 页获取到 100 条记录
2025-05-01 00:00:22,024 - INFO - Request Parameters - Page 3:
2025-05-01 00:00:22,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:22,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:22,446 - INFO - Response - Page 3:
2025-05-01 00:00:22,650 - INFO - 第 3 页获取到 100 条记录
2025-05-01 00:00:22,650 - INFO - Request Parameters - Page 4:
2025-05-01 00:00:22,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:22,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:23,103 - INFO - Response - Page 4:
2025-05-01 00:00:23,307 - INFO - 第 4 页获取到 100 条记录
2025-05-01 00:00:23,307 - INFO - Request Parameters - Page 5:
2025-05-01 00:00:23,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:23,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:23,838 - INFO - Response - Page 5:
2025-05-01 00:00:24,042 - INFO - 第 5 页获取到 100 条记录
2025-05-01 00:00:24,042 - INFO - Request Parameters - Page 6:
2025-05-01 00:00:24,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:24,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:24,495 - INFO - Response - Page 6:
2025-05-01 00:00:24,699 - INFO - 第 6 页获取到 100 条记录
2025-05-01 00:00:24,699 - INFO - Request Parameters - Page 7:
2025-05-01 00:00:24,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:00:24,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:00:25,121 - INFO - Response - Page 7:
2025-05-01 00:00:25,324 - INFO - 第 7 页获取到 27 条记录
2025-05-01 00:00:25,324 - INFO - 查询完成，共获取到 627 条记录
2025-05-01 00:00:25,324 - INFO - 获取到 627 条表单数据
2025-05-01 00:00:25,324 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-01 00:00:25,324 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-05-01 00:00:25,762 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-05-01 00:00:25,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'total_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'order_count', 'old_value': 249, 'new_value': 250}]
2025-05-01 00:00:25,762 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-05-01 00:00:26,153 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-05-01 00:00:26,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'total_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'order_count', 'old_value': 153, 'new_value': 154}]
2025-05-01 00:00:26,153 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-05-01 00:00:26,528 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-05-01 00:00:26,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1850000.0, 'new_value': 1900000.0}, {'field': 'total_amount', 'old_value': 1850000.0, 'new_value': 1900000.0}, {'field': 'order_count', 'old_value': 354, 'new_value': 355}]
2025-05-01 00:00:26,528 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-05-01 00:00:26,951 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-05-01 00:00:26,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1950000.0, 'new_value': 2000000.0}, {'field': 'total_amount', 'old_value': 1950000.0, 'new_value': 2000000.0}, {'field': 'order_count', 'old_value': 497, 'new_value': 498}]
2025-05-01 00:00:26,951 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-05-01 00:00:27,326 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-05-01 00:00:27,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26876.95, 'new_value': 27084.95}, {'field': 'total_amount', 'old_value': 26876.95, 'new_value': 27084.95}, {'field': 'order_count', 'old_value': 105, 'new_value': 108}]
2025-05-01 00:00:27,326 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-05-01 00:00:27,702 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-05-01 00:00:27,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65400.0, 'new_value': 68300.0}, {'field': 'total_amount', 'old_value': 77240.0, 'new_value': 80140.0}, {'field': 'order_count', 'old_value': 781, 'new_value': 806}]
2025-05-01 00:00:27,702 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-05-01 00:00:28,108 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-05-01 00:00:28,108 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4930.35, 'new_value': 5107.35}, {'field': 'offline_amount', 'old_value': 58258.7, 'new_value': 58294.7}, {'field': 'total_amount', 'old_value': 63189.05, 'new_value': 63402.05}, {'field': 'order_count', 'old_value': 3944, 'new_value': 3947}]
2025-05-01 00:00:28,108 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-05-01 00:00:28,562 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-05-01 00:00:28,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55172.0, 'new_value': 59364.0}, {'field': 'offline_amount', 'old_value': 159925.0, 'new_value': 163170.0}, {'field': 'total_amount', 'old_value': 215097.0, 'new_value': 222534.0}, {'field': 'order_count', 'old_value': 4193, 'new_value': 4365}]
2025-05-01 00:00:28,562 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-05-01 00:00:29,015 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-05-01 00:00:29,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6872.55, 'new_value': 7154.55}, {'field': 'total_amount', 'old_value': 6872.55, 'new_value': 7154.55}, {'field': 'order_count', 'old_value': 943, 'new_value': 945}]
2025-05-01 00:00:29,015 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-05-01 00:00:29,453 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-05-01 00:00:29,453 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10054.56, 'new_value': 10527.62}, {'field': 'offline_amount', 'old_value': 233512.14, 'new_value': 248079.33}, {'field': 'total_amount', 'old_value': 243566.7, 'new_value': 258606.95}, {'field': 'order_count', 'old_value': 1345, 'new_value': 1412}]
2025-05-01 00:00:29,453 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-05-01 00:00:29,876 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-05-01 00:00:29,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150.0, 'new_value': 250.0}, {'field': 'offline_amount', 'old_value': 43633.0, 'new_value': 44749.0}, {'field': 'total_amount', 'old_value': 43783.0, 'new_value': 44999.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 139}]
2025-05-01 00:00:29,876 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-05-01 00:00:30,220 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-05-01 00:00:30,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77705.98, 'new_value': 79618.81}, {'field': 'offline_amount', 'old_value': 112982.0, 'new_value': 116897.59}, {'field': 'total_amount', 'old_value': 190687.98, 'new_value': 196516.4}, {'field': 'order_count', 'old_value': 6612, 'new_value': 6837}]
2025-05-01 00:00:30,220 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-05-01 00:00:30,611 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-05-01 00:00:30,611 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117277.9, 'new_value': 120428.9}, {'field': 'offline_amount', 'old_value': 190184.25, 'new_value': 199390.25}, {'field': 'total_amount', 'old_value': 307462.15, 'new_value': 319819.15}, {'field': 'order_count', 'old_value': 6777, 'new_value': 9091}]
2025-05-01 00:00:30,611 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-05-01 00:00:31,142 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-05-01 00:00:31,142 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71032.03, 'new_value': 72904.1}, {'field': 'offline_amount', 'old_value': 750958.57, 'new_value': 780131.14}, {'field': 'total_amount', 'old_value': 821990.6, 'new_value': 853035.24}, {'field': 'order_count', 'old_value': 3333, 'new_value': 3491}]
2025-05-01 00:00:31,142 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-05-01 00:00:31,612 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-05-01 00:00:31,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95293.0, 'new_value': 98531.0}, {'field': 'total_amount', 'old_value': 95293.0, 'new_value': 98531.0}, {'field': 'order_count', 'old_value': 838, 'new_value': 869}]
2025-05-01 00:00:31,612 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-05-01 00:00:32,034 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-05-01 00:00:32,034 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1914.95, 'new_value': 2171.3}, {'field': 'offline_amount', 'old_value': 110077.88, 'new_value': 115811.13}, {'field': 'total_amount', 'old_value': 111992.83, 'new_value': 117982.43}, {'field': 'order_count', 'old_value': 2769, 'new_value': 2929}]
2025-05-01 00:00:32,034 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-05-01 00:00:32,456 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-05-01 00:00:32,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29668.94, 'new_value': 30508.04}, {'field': 'offline_amount', 'old_value': 23095.6, 'new_value': 23789.6}, {'field': 'total_amount', 'old_value': 52764.54, 'new_value': 54297.64}, {'field': 'order_count', 'old_value': 265, 'new_value': 275}]
2025-05-01 00:00:32,456 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-05-01 00:00:32,941 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-05-01 00:00:32,941 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23360.25, 'new_value': 23850.37}, {'field': 'offline_amount', 'old_value': 252172.19, 'new_value': 258270.76}, {'field': 'total_amount', 'old_value': 275532.44, 'new_value': 282121.13}, {'field': 'order_count', 'old_value': 1336, 'new_value': 1363}]
2025-05-01 00:00:32,941 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-05-01 00:00:33,379 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-05-01 00:00:33,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14610.3, 'new_value': 14947.5}, {'field': 'total_amount', 'old_value': 14610.3, 'new_value': 14947.5}, {'field': 'order_count', 'old_value': 164, 'new_value': 169}]
2025-05-01 00:00:33,379 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-05-01 00:00:33,786 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-05-01 00:00:33,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42502.54, 'new_value': 43622.63}, {'field': 'offline_amount', 'old_value': 311205.58, 'new_value': 321666.78}, {'field': 'total_amount', 'old_value': 353708.12, 'new_value': 365289.41}, {'field': 'order_count', 'old_value': 2470, 'new_value': 2564}]
2025-05-01 00:00:33,786 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-05-01 00:00:34,161 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-05-01 00:00:34,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106837.2, 'new_value': 110963.4}, {'field': 'total_amount', 'old_value': 106837.2, 'new_value': 110963.4}, {'field': 'order_count', 'old_value': 211, 'new_value': 221}]
2025-05-01 00:00:34,161 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-05-01 00:00:34,693 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-05-01 00:00:34,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1797836.59, 'new_value': 1860742.64}, {'field': 'total_amount', 'old_value': 1797836.59, 'new_value': 1860742.64}, {'field': 'order_count', 'old_value': 14405, 'new_value': 14907}]
2025-05-01 00:00:34,693 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-05-01 00:00:35,052 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-05-01 00:00:35,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328902.04, 'new_value': 373446.04}, {'field': 'total_amount', 'old_value': 328902.04, 'new_value': 373446.04}, {'field': 'order_count', 'old_value': 229, 'new_value': 235}]
2025-05-01 00:00:35,052 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9ML9
2025-05-01 00:00:35,443 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9ML9
2025-05-01 00:00:35,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6983.5, 'new_value': 7478.5}, {'field': 'total_amount', 'old_value': 7370.5, 'new_value': 7865.5}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-05-01 00:00:35,443 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-05-01 00:00:35,897 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-05-01 00:00:35,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34016.1, 'new_value': 36224.1}, {'field': 'offline_amount', 'old_value': 51.0, 'new_value': 54.0}, {'field': 'total_amount', 'old_value': 34067.1, 'new_value': 36278.1}, {'field': 'order_count', 'old_value': 71, 'new_value': 76}]
2025-05-01 00:00:35,897 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-05-01 00:00:36,225 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-05-01 00:00:36,225 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41677.18, 'new_value': 43238.25}, {'field': 'offline_amount', 'old_value': 547686.84, 'new_value': 565766.83}, {'field': 'total_amount', 'old_value': 589364.02, 'new_value': 609005.08}, {'field': 'order_count', 'old_value': 5249, 'new_value': 5434}]
2025-05-01 00:00:36,225 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MW9
2025-05-01 00:00:36,679 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MW9
2025-05-01 00:00:36,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63039.0, 'new_value': 64721.0}, {'field': 'total_amount', 'old_value': 63039.0, 'new_value': 64721.0}, {'field': 'order_count', 'old_value': 245, 'new_value': 253}]
2025-05-01 00:00:36,679 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-05-01 00:00:37,133 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-05-01 00:00:37,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47807.95, 'new_value': 50322.95}, {'field': 'offline_amount', 'old_value': 43345.0, 'new_value': 45553.0}, {'field': 'total_amount', 'old_value': 91152.95, 'new_value': 95875.95}, {'field': 'order_count', 'old_value': 1199, 'new_value': 1257}]
2025-05-01 00:00:37,133 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-05-01 00:00:37,617 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-05-01 00:00:37,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41805.0, 'new_value': 43373.0}, {'field': 'total_amount', 'old_value': 49296.1, 'new_value': 50864.1}, {'field': 'order_count', 'old_value': 513, 'new_value': 517}]
2025-05-01 00:00:37,617 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7A
2025-05-01 00:00:38,055 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7A
2025-05-01 00:00:38,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92914.0, 'new_value': 98962.0}, {'field': 'total_amount', 'old_value': 102948.11, 'new_value': 108996.11}, {'field': 'order_count', 'old_value': 81, 'new_value': 83}]
2025-05-01 00:00:38,055 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-05-01 00:00:38,540 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-05-01 00:00:38,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 204594.24, 'new_value': 212385.84}, {'field': 'offline_amount', 'old_value': 384569.23, 'new_value': 396839.54}, {'field': 'total_amount', 'old_value': 589163.47, 'new_value': 609225.38}, {'field': 'order_count', 'old_value': 4487, 'new_value': 4660}]
2025-05-01 00:00:38,540 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-05-01 00:00:38,947 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-05-01 00:00:38,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57054.51, 'new_value': 58609.01}, {'field': 'offline_amount', 'old_value': 353443.42, 'new_value': 367532.15}, {'field': 'total_amount', 'old_value': 410497.93, 'new_value': 426141.16}, {'field': 'order_count', 'old_value': 2797, 'new_value': 2898}]
2025-05-01 00:00:38,947 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-05-01 00:00:39,400 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-05-01 00:00:39,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11613.5, 'new_value': 11712.5}, {'field': 'offline_amount', 'old_value': 15733.7, 'new_value': 17433.7}, {'field': 'total_amount', 'old_value': 27347.2, 'new_value': 29146.2}, {'field': 'order_count', 'old_value': 83, 'new_value': 89}]
2025-05-01 00:00:39,400 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJA
2025-05-01 00:00:39,791 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJA
2025-05-01 00:00:39,791 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 411874.16, 'new_value': 434630.16}, {'field': 'total_amount', 'old_value': 411874.16, 'new_value': 434630.16}, {'field': 'order_count', 'old_value': 76, 'new_value': 79}]
2025-05-01 00:00:39,791 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOA
2025-05-01 00:00:40,182 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOA
2025-05-01 00:00:40,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9300.0, 'new_value': 9799.0}, {'field': 'total_amount', 'old_value': 9300.0, 'new_value': 9799.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-01 00:00:40,182 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-05-01 00:00:40,636 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-05-01 00:00:40,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 343786.27, 'new_value': 357386.9}, {'field': 'total_amount', 'old_value': 343786.27, 'new_value': 357386.9}, {'field': 'order_count', 'old_value': 1733, 'new_value': 1802}]
2025-05-01 00:00:40,636 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-05-01 00:00:41,043 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-05-01 00:00:41,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45154.13, 'new_value': 47448.96}, {'field': 'total_amount', 'old_value': 45154.13, 'new_value': 47448.96}, {'field': 'order_count', 'old_value': 196, 'new_value': 205}]
2025-05-01 00:00:41,043 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-05-01 00:00:41,465 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-05-01 00:00:41,465 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6437.8, 'new_value': 7112.8}, {'field': 'offline_amount', 'old_value': 49066.0, 'new_value': 50774.0}, {'field': 'total_amount', 'old_value': 55503.8, 'new_value': 57886.8}, {'field': 'order_count', 'old_value': 65, 'new_value': 70}]
2025-05-01 00:00:41,465 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-05-01 00:00:41,934 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-05-01 00:00:41,934 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106332.0, 'new_value': 109210.8}, {'field': 'total_amount', 'old_value': 106332.0, 'new_value': 109210.8}, {'field': 'order_count', 'old_value': 549, 'new_value': 567}]
2025-05-01 00:00:41,934 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-05-01 00:00:42,325 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-05-01 00:00:42,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77019.3, 'new_value': 78472.3}, {'field': 'total_amount', 'old_value': 78512.3, 'new_value': 79965.3}, {'field': 'order_count', 'old_value': 217, 'new_value': 221}]
2025-05-01 00:00:42,325 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-05-01 00:00:42,747 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-05-01 00:00:42,747 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66733.54, 'new_value': 69004.64}, {'field': 'offline_amount', 'old_value': 85420.82, 'new_value': 87116.12}, {'field': 'total_amount', 'old_value': 152154.36, 'new_value': 156120.76}, {'field': 'order_count', 'old_value': 6848, 'new_value': 6899}]
2025-05-01 00:00:42,747 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-05-01 00:00:43,185 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-05-01 00:00:43,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50914.0, 'new_value': 51578.0}, {'field': 'total_amount', 'old_value': 51544.0, 'new_value': 52208.0}, {'field': 'order_count', 'old_value': 195, 'new_value': 197}]
2025-05-01 00:00:43,185 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-05-01 00:00:43,576 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-05-01 00:00:43,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41252.92, 'new_value': 41780.92}, {'field': 'offline_amount', 'old_value': 340023.71, 'new_value': 345836.89}, {'field': 'total_amount', 'old_value': 381276.63, 'new_value': 387617.81}, {'field': 'order_count', 'old_value': 702, 'new_value': 711}]
2025-05-01 00:00:43,576 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-05-01 00:00:44,030 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-05-01 00:00:44,030 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34329.2, 'new_value': 34615.2}, {'field': 'total_amount', 'old_value': 34329.2, 'new_value': 34615.2}, {'field': 'order_count', 'old_value': 147, 'new_value': 149}]
2025-05-01 00:00:44,030 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-05-01 00:00:44,374 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-05-01 00:00:44,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29906.5, 'new_value': 30979.0}, {'field': 'total_amount', 'old_value': 29906.5, 'new_value': 30979.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 146}]
2025-05-01 00:00:44,374 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-05-01 00:00:44,843 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-05-01 00:00:44,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158241.6, 'new_value': 161019.6}, {'field': 'total_amount', 'old_value': 423345.71, 'new_value': 426123.71}, {'field': 'order_count', 'old_value': 177, 'new_value': 184}]
2025-05-01 00:00:44,843 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-05-01 00:00:45,297 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-05-01 00:00:45,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54245.7, 'new_value': 55645.7}, {'field': 'total_amount', 'old_value': 54245.7, 'new_value': 55645.7}, {'field': 'order_count', 'old_value': 296, 'new_value': 303}]
2025-05-01 00:00:45,297 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-05-01 00:00:45,750 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-05-01 00:00:45,750 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93584.88, 'new_value': 96826.48}, {'field': 'offline_amount', 'old_value': 180573.63, 'new_value': 187395.51}, {'field': 'total_amount', 'old_value': 274158.51, 'new_value': 284221.99}, {'field': 'order_count', 'old_value': 3829, 'new_value': 3954}]
2025-05-01 00:00:45,750 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO5
2025-05-01 00:00:46,188 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO5
2025-05-01 00:00:46,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25800.0, 'new_value': 29300.0}, {'field': 'total_amount', 'old_value': 31660.9, 'new_value': 35160.9}, {'field': 'order_count', 'old_value': 88, 'new_value': 89}]
2025-05-01 00:00:46,188 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-05-01 00:00:46,611 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-05-01 00:00:46,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39778.0, 'new_value': 40734.0}, {'field': 'total_amount', 'old_value': 40930.0, 'new_value': 41886.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 92}]
2025-05-01 00:00:46,611 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-05-01 00:00:47,095 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-05-01 00:00:47,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52864.82, 'new_value': 54862.82}, {'field': 'total_amount', 'old_value': 52864.82, 'new_value': 54862.82}, {'field': 'order_count', 'old_value': 222, 'new_value': 224}]
2025-05-01 00:00:47,095 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-05-01 00:00:47,518 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-05-01 00:00:47,518 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29906.0, 'new_value': 30895.0}, {'field': 'total_amount', 'old_value': 29906.0, 'new_value': 30895.0}, {'field': 'order_count', 'old_value': 286, 'new_value': 295}]
2025-05-01 00:00:47,518 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-05-01 00:00:47,909 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-05-01 00:00:47,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2150.0, 'new_value': 3150.0}, {'field': 'offline_amount', 'old_value': 23056.67, 'new_value': 23731.38}, {'field': 'total_amount', 'old_value': 25206.67, 'new_value': 26881.38}, {'field': 'order_count', 'old_value': 482, 'new_value': 506}]
2025-05-01 00:00:47,909 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-05-01 00:00:48,362 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-05-01 00:00:48,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241218.0, 'new_value': 252215.5}, {'field': 'total_amount', 'old_value': 246398.2, 'new_value': 257395.7}, {'field': 'order_count', 'old_value': 2812, 'new_value': 2921}]
2025-05-01 00:00:48,362 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-05-01 00:00:48,785 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-05-01 00:00:48,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250120.0, 'new_value': 255646.0}, {'field': 'total_amount', 'old_value': 250120.0, 'new_value': 255646.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 72}]
2025-05-01 00:00:48,785 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-05-01 00:00:49,176 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-05-01 00:00:49,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111307.55, 'new_value': 126694.35}, {'field': 'total_amount', 'old_value': 111307.55, 'new_value': 126694.35}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-01 00:00:49,176 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-05-01 00:00:49,567 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-05-01 00:00:49,567 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 245500.81, 'new_value': 251087.73}, {'field': 'offline_amount', 'old_value': 89201.12, 'new_value': 89501.12}, {'field': 'total_amount', 'old_value': 334701.93, 'new_value': 340588.85}, {'field': 'order_count', 'old_value': 643, 'new_value': 656}]
2025-05-01 00:00:49,567 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-05-01 00:00:50,005 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-05-01 00:00:50,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86024.48, 'new_value': 86869.48}, {'field': 'total_amount', 'old_value': 95223.98, 'new_value': 96068.98}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-05-01 00:00:50,005 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-05-01 00:00:50,411 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-05-01 00:00:50,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67755.14, 'new_value': 70455.14}, {'field': 'offline_amount', 'old_value': 75046.64, 'new_value': 77346.64}, {'field': 'total_amount', 'old_value': 142801.78, 'new_value': 147801.78}, {'field': 'order_count', 'old_value': 6263, 'new_value': 6497}]
2025-05-01 00:00:50,411 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA6
2025-05-01 00:00:50,849 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA6
2025-05-01 00:00:50,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57889.5, 'new_value': 94609.5}, {'field': 'total_amount', 'old_value': 57889.5, 'new_value': 94609.5}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-01 00:00:50,849 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-05-01 00:00:51,209 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-05-01 00:00:51,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55891.0, 'new_value': 55911.0}, {'field': 'offline_amount', 'old_value': 414541.0, 'new_value': 419451.0}, {'field': 'total_amount', 'old_value': 470432.0, 'new_value': 475362.0}, {'field': 'order_count', 'old_value': 231, 'new_value': 235}]
2025-05-01 00:00:51,209 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-05-01 00:00:51,647 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-05-01 00:00:51,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70979.0, 'new_value': 77514.0}, {'field': 'total_amount', 'old_value': 70979.0, 'new_value': 77514.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 191}]
2025-05-01 00:00:51,647 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-05-01 00:00:52,053 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-05-01 00:00:52,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252221.0, 'new_value': 262484.0}, {'field': 'total_amount', 'old_value': 277096.0, 'new_value': 287359.0}, {'field': 'order_count', 'old_value': 5446, 'new_value': 5454}]
2025-05-01 00:00:52,053 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML6
2025-05-01 00:00:52,476 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML6
2025-05-01 00:00:52,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107474.0, 'new_value': 112072.0}, {'field': 'total_amount', 'old_value': 107474.0, 'new_value': 112072.0}, {'field': 'order_count', 'old_value': 3418, 'new_value': 3420}]
2025-05-01 00:00:52,476 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-05-01 00:00:52,898 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-05-01 00:00:52,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33336.0, 'new_value': 33605.0}, {'field': 'total_amount', 'old_value': 33336.0, 'new_value': 33605.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-05-01 00:00:52,898 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-05-01 00:00:53,320 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-05-01 00:00:53,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8882.2, 'new_value': 9991.2}, {'field': 'offline_amount', 'old_value': 66486.1, 'new_value': 70486.1}, {'field': 'total_amount', 'old_value': 75368.3, 'new_value': 80477.3}, {'field': 'order_count', 'old_value': 68, 'new_value': 70}]
2025-05-01 00:00:53,320 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-05-01 00:00:53,852 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-05-01 00:00:53,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67233.0, 'new_value': 68963.0}, {'field': 'total_amount', 'old_value': 67233.0, 'new_value': 68963.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 109}]
2025-05-01 00:00:53,852 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-05-01 00:00:54,274 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-05-01 00:00:54,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207028.0, 'new_value': 215568.0}, {'field': 'total_amount', 'old_value': 207028.0, 'new_value': 215568.0}, {'field': 'order_count', 'old_value': 288, 'new_value': 302}]
2025-05-01 00:00:54,274 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-05-01 00:00:54,728 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-05-01 00:00:54,728 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 189857.41, 'new_value': 195262.85}, {'field': 'offline_amount', 'old_value': 431186.67, 'new_value': 446186.67}, {'field': 'total_amount', 'old_value': 621044.08, 'new_value': 641449.52}, {'field': 'order_count', 'old_value': 2924, 'new_value': 3093}]
2025-05-01 00:00:54,728 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY6
2025-05-01 00:00:55,150 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY6
2025-05-01 00:00:55,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6464.08, 'new_value': 6520.08}, {'field': 'total_amount', 'old_value': 6464.08, 'new_value': 6520.08}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-01 00:00:55,150 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-05-01 00:00:55,573 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-05-01 00:00:55,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25051.1, 'new_value': 26813.1}, {'field': 'total_amount', 'old_value': 28552.2, 'new_value': 30314.2}, {'field': 'order_count', 'old_value': 323, 'new_value': 328}]
2025-05-01 00:00:55,573 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-05-01 00:00:55,979 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-05-01 00:00:55,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33952.1, 'new_value': 34783.6}, {'field': 'total_amount', 'old_value': 33952.1, 'new_value': 34783.6}, {'field': 'order_count', 'old_value': 333, 'new_value': 338}]
2025-05-01 00:00:55,979 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-05-01 00:00:56,401 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-05-01 00:00:56,401 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42700.16, 'new_value': 44762.89}, {'field': 'offline_amount', 'old_value': 262714.46, 'new_value': 275654.16}, {'field': 'total_amount', 'old_value': 305414.62, 'new_value': 320417.05}, {'field': 'order_count', 'old_value': 7403, 'new_value': 7734}]
2025-05-01 00:00:56,401 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-05-01 00:00:56,839 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-05-01 00:00:56,839 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9785.41, 'new_value': 10079.3}, {'field': 'offline_amount', 'old_value': 15085.61, 'new_value': 15490.93}, {'field': 'total_amount', 'old_value': 24871.02, 'new_value': 25570.23}, {'field': 'order_count', 'old_value': 1675, 'new_value': 1730}]
2025-05-01 00:00:56,839 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-05-01 00:00:57,293 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-05-01 00:00:57,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48579.0, 'new_value': 52441.0}, {'field': 'total_amount', 'old_value': 52255.0, 'new_value': 56117.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 97}]
2025-05-01 00:00:57,293 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA7
2025-05-01 00:00:57,715 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA7
2025-05-01 00:00:57,715 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19230.27, 'new_value': 19230.47}, {'field': 'total_amount', 'old_value': 22020.27, 'new_value': 22020.47}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-05-01 00:00:57,715 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-05-01 00:00:58,106 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-05-01 00:00:58,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 624494.77, 'new_value': 654164.35}, {'field': 'total_amount', 'old_value': 707363.28, 'new_value': 737032.86}, {'field': 'order_count', 'old_value': 5237, 'new_value': 5468}]
2025-05-01 00:00:58,106 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-05-01 00:00:58,513 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-05-01 00:00:58,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 752724.0, 'new_value': 773351.0}, {'field': 'total_amount', 'old_value': 752724.0, 'new_value': 773351.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 84}]
2025-05-01 00:00:58,513 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-05-01 00:00:58,966 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-05-01 00:00:58,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1218171.0, 'new_value': 1269366.0}, {'field': 'total_amount', 'old_value': 1218171.0, 'new_value': 1269366.0}, {'field': 'order_count', 'old_value': 1282, 'new_value': 1330}]
2025-05-01 00:00:58,966 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-05-01 00:00:59,389 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-05-01 00:00:59,389 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 297977.94, 'new_value': 310900.64}, {'field': 'offline_amount', 'old_value': 25118.7, 'new_value': 25702.9}, {'field': 'total_amount', 'old_value': 323096.64, 'new_value': 336603.54}, {'field': 'order_count', 'old_value': 11501, 'new_value': 11928}]
2025-05-01 00:00:59,389 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-05-01 00:00:59,827 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-05-01 00:00:59,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144551.9, 'new_value': 149727.1}, {'field': 'total_amount', 'old_value': 144551.9, 'new_value': 149727.1}, {'field': 'order_count', 'old_value': 269, 'new_value': 280}]
2025-05-01 00:00:59,827 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-05-01 00:01:00,265 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-05-01 00:01:00,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 613815.0, 'new_value': 638910.0}, {'field': 'total_amount', 'old_value': 613815.0, 'new_value': 638910.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 106}]
2025-05-01 00:01:00,265 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-05-01 00:01:00,687 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-05-01 00:01:00,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86791.5, 'new_value': 100860.5}, {'field': 'total_amount', 'old_value': 111034.24, 'new_value': 125103.24}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-05-01 00:01:00,687 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-05-01 00:01:01,109 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-05-01 00:01:01,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99440.8, 'new_value': 103492.8}, {'field': 'offline_amount', 'old_value': 56844.64, 'new_value': 58238.04}, {'field': 'total_amount', 'old_value': 156285.44, 'new_value': 161730.84}, {'field': 'order_count', 'old_value': 1034, 'new_value': 1073}]
2025-05-01 00:01:01,109 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-05-01 00:01:01,547 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-05-01 00:01:01,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83631.55, 'new_value': 86287.87}, {'field': 'total_amount', 'old_value': 83631.55, 'new_value': 86287.87}, {'field': 'order_count', 'old_value': 2448, 'new_value': 2524}]
2025-05-01 00:01:01,547 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-05-01 00:01:02,032 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-05-01 00:01:02,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221237.33, 'new_value': 231992.62}, {'field': 'total_amount', 'old_value': 221237.33, 'new_value': 231992.62}, {'field': 'order_count', 'old_value': 4610, 'new_value': 4809}]
2025-05-01 00:01:02,032 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-05-01 00:01:02,376 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-05-01 00:01:02,376 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83122.01, 'new_value': 88240.52}, {'field': 'offline_amount', 'old_value': 45097.76, 'new_value': 46313.16}, {'field': 'total_amount', 'old_value': 128219.77, 'new_value': 134553.68}, {'field': 'order_count', 'old_value': 6645, 'new_value': 6974}]
2025-05-01 00:01:02,376 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-05-01 00:01:02,861 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-05-01 00:01:02,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256936.85, 'new_value': 269907.65}, {'field': 'total_amount', 'old_value': 256936.85, 'new_value': 269907.65}, {'field': 'order_count', 'old_value': 1570, 'new_value': 1643}]
2025-05-01 00:01:02,861 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-05-01 00:01:03,252 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-05-01 00:01:03,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 249430.75, 'new_value': 259195.3}, {'field': 'offline_amount', 'old_value': 58387.63, 'new_value': 60444.63}, {'field': 'total_amount', 'old_value': 307818.38, 'new_value': 319639.93}, {'field': 'order_count', 'old_value': 1230, 'new_value': 1275}]
2025-05-01 00:01:03,252 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-05-01 00:01:03,705 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-05-01 00:01:03,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207447.01, 'new_value': 214700.91}, {'field': 'total_amount', 'old_value': 214959.31, 'new_value': 222213.21}, {'field': 'order_count', 'old_value': 403, 'new_value': 419}]
2025-05-01 00:01:03,705 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-05-01 00:01:04,253 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-05-01 00:01:04,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208943.0, 'new_value': 218295.0}, {'field': 'total_amount', 'old_value': 208943.0, 'new_value': 218295.0}, {'field': 'order_count', 'old_value': 655, 'new_value': 687}]
2025-05-01 00:01:04,253 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-05-01 00:01:04,722 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-05-01 00:01:04,722 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33137.9, 'new_value': 34118.56}, {'field': 'offline_amount', 'old_value': 208529.17, 'new_value': 215417.17}, {'field': 'total_amount', 'old_value': 241667.07, 'new_value': 249535.73}, {'field': 'order_count', 'old_value': 8103, 'new_value': 8339}]
2025-05-01 00:01:04,722 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-05-01 00:01:05,223 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-05-01 00:01:05,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60169.0, 'new_value': 62872.0}, {'field': 'total_amount', 'old_value': 60169.0, 'new_value': 62872.0}, {'field': 'order_count', 'old_value': 3933, 'new_value': 4017}]
2025-05-01 00:01:05,223 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-05-01 00:01:05,598 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-05-01 00:01:05,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53318.5, 'new_value': 54793.08}, {'field': 'offline_amount', 'old_value': 42312.04, 'new_value': 43901.04}, {'field': 'total_amount', 'old_value': 95630.54, 'new_value': 98694.12}, {'field': 'order_count', 'old_value': 7242, 'new_value': 7443}]
2025-05-01 00:01:05,598 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-05-01 00:01:05,989 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-05-01 00:01:05,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259750.11, 'new_value': 273383.11}, {'field': 'total_amount', 'old_value': 264516.11, 'new_value': 278149.11}, {'field': 'order_count', 'old_value': 3776, 'new_value': 3976}]
2025-05-01 00:01:05,989 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-05-01 00:01:06,443 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-05-01 00:01:06,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 687862.0, 'new_value': 697101.0}, {'field': 'total_amount', 'old_value': 687862.0, 'new_value': 697101.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 158}]
2025-05-01 00:01:06,443 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-05-01 00:01:06,927 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-05-01 00:01:06,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 626662.45, 'new_value': 656768.75}, {'field': 'total_amount', 'old_value': 626662.45, 'new_value': 656768.75}, {'field': 'order_count', 'old_value': 2125, 'new_value': 2202}]
2025-05-01 00:01:06,927 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-05-01 00:01:07,365 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-05-01 00:01:07,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33909.0, 'new_value': 35489.0}, {'field': 'total_amount', 'old_value': 33909.0, 'new_value': 35489.0}, {'field': 'order_count', 'old_value': 211, 'new_value': 221}]
2025-05-01 00:01:07,365 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-05-01 00:01:07,819 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-05-01 00:01:07,819 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80922.03, 'new_value': 83747.03}, {'field': 'offline_amount', 'old_value': 1424500.49, 'new_value': 1468600.12}, {'field': 'total_amount', 'old_value': 1505422.52, 'new_value': 1552347.15}, {'field': 'order_count', 'old_value': 10687, 'new_value': 11090}]
2025-05-01 00:01:07,819 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMI
2025-05-01 00:01:08,226 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMI
2025-05-01 00:01:08,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47960.0, 'new_value': 55140.0}, {'field': 'total_amount', 'old_value': 47960.0, 'new_value': 55140.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-01 00:01:08,226 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-05-01 00:01:08,695 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-05-01 00:01:08,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29904.0, 'new_value': 30733.0}, {'field': 'total_amount', 'old_value': 29904.0, 'new_value': 30733.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 154}]
2025-05-01 00:01:08,695 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-05-01 00:01:09,133 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-05-01 00:01:09,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45895.09, 'new_value': 47670.6}, {'field': 'offline_amount', 'old_value': 1148291.16, 'new_value': 1188272.69}, {'field': 'total_amount', 'old_value': 1194186.25, 'new_value': 1235943.29}, {'field': 'order_count', 'old_value': 5838, 'new_value': 6065}]
2025-05-01 00:01:09,133 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-05-01 00:01:09,571 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-05-01 00:01:09,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249252.32, 'new_value': 257167.36}, {'field': 'total_amount', 'old_value': 249252.32, 'new_value': 257167.36}, {'field': 'order_count', 'old_value': 1052, 'new_value': 1093}]
2025-05-01 00:01:09,571 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-05-01 00:01:10,024 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-05-01 00:01:10,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104311.0, 'new_value': 107445.0}, {'field': 'offline_amount', 'old_value': 123335.0, 'new_value': 127357.0}, {'field': 'total_amount', 'old_value': 227646.0, 'new_value': 234802.0}, {'field': 'order_count', 'old_value': 5834, 'new_value': 6031}]
2025-05-01 00:01:10,040 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-05-01 00:01:10,478 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-05-01 00:01:10,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88620.5, 'new_value': 90206.3}, {'field': 'total_amount', 'old_value': 88620.5, 'new_value': 90206.3}, {'field': 'order_count', 'old_value': 312, 'new_value': 321}]
2025-05-01 00:01:10,478 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-05-01 00:01:10,900 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-05-01 00:01:10,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1016756.4, 'new_value': 1070381.7}, {'field': 'total_amount', 'old_value': 1016756.4, 'new_value': 1070381.7}, {'field': 'order_count', 'old_value': 1308, 'new_value': 1395}]
2025-05-01 00:01:10,900 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-05-01 00:01:11,354 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-05-01 00:01:11,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93671.6, 'new_value': 98774.5}, {'field': 'total_amount', 'old_value': 93671.6, 'new_value': 98774.5}, {'field': 'order_count', 'old_value': 315, 'new_value': 326}]
2025-05-01 00:01:11,354 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPJ
2025-05-01 00:01:11,745 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPJ
2025-05-01 00:01:11,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23414.0, 'new_value': 26412.0}, {'field': 'total_amount', 'old_value': 39685.0, 'new_value': 42683.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-01 00:01:11,745 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-05-01 00:01:12,151 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-05-01 00:01:12,151 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17876.27, 'new_value': 18789.68}, {'field': 'offline_amount', 'old_value': 344011.15, 'new_value': 358118.52}, {'field': 'total_amount', 'old_value': 361887.42, 'new_value': 376908.2}, {'field': 'order_count', 'old_value': 18443, 'new_value': 19187}]
2025-05-01 00:01:12,151 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-05-01 00:01:12,589 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-05-01 00:01:12,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 310522.4, 'new_value': 321278.96}, {'field': 'offline_amount', 'old_value': 1553957.29, 'new_value': 1604346.94}, {'field': 'total_amount', 'old_value': 1864479.69, 'new_value': 1925625.9}, {'field': 'order_count', 'old_value': 9588, 'new_value': 9892}]
2025-05-01 00:01:12,589 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-05-01 00:01:13,027 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-05-01 00:01:13,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294018.0, 'new_value': 311870.0}, {'field': 'total_amount', 'old_value': 294019.0, 'new_value': 311871.0}, {'field': 'order_count', 'old_value': 462, 'new_value': 489}]
2025-05-01 00:01:13,027 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-05-01 00:01:13,465 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-05-01 00:01:13,465 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 595268.3, 'new_value': 617363.3}, {'field': 'total_amount', 'old_value': 595268.3, 'new_value': 617363.3}]
2025-05-01 00:01:13,481 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-05-01 00:01:13,840 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-05-01 00:01:13,840 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79217.38, 'new_value': 81156.08}, {'field': 'total_amount', 'old_value': 79217.38, 'new_value': 81156.08}, {'field': 'order_count', 'old_value': 1321, 'new_value': 1359}]
2025-05-01 00:01:13,840 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-05-01 00:01:14,310 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-05-01 00:01:14,310 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328329.0, 'new_value': 340944.0}, {'field': 'total_amount', 'old_value': 328329.0, 'new_value': 340944.0}, {'field': 'order_count', 'old_value': 12659, 'new_value': 12779}]
2025-05-01 00:01:14,310 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS6
2025-05-01 00:01:14,716 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS6
2025-05-01 00:01:14,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198467.0, 'new_value': 248467.0}, {'field': 'total_amount', 'old_value': 198467.0, 'new_value': 248467.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-01 00:01:14,716 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-05-01 00:01:15,123 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-05-01 00:01:15,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 398630.8, 'new_value': 415061.4}, {'field': 'total_amount', 'old_value': 398630.8, 'new_value': 415061.4}, {'field': 'order_count', 'old_value': 1618, 'new_value': 1674}]
2025-05-01 00:01:15,123 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-05-01 00:01:15,530 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-05-01 00:01:15,530 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151957.92, 'new_value': 157658.18}, {'field': 'total_amount', 'old_value': 151957.92, 'new_value': 157658.18}, {'field': 'order_count', 'old_value': 12537, 'new_value': 12947}]
2025-05-01 00:01:15,530 - INFO - 日期 2025-04 处理完成 - 更新: 117 条，插入: 0 条，错误: 0 条
2025-05-01 00:01:15,530 - INFO - 数据同步完成！更新: 117 条，插入: 0 条，错误: 0 条
2025-05-01 00:01:15,530 - INFO - =================同步完成====================
2025-05-01 03:00:03,917 - INFO - =================使用默认全量同步=============
2025-05-01 03:00:05,059 - INFO - MySQL查询成功，共获取 2640 条记录
2025-05-01 03:00:05,059 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-05-01 03:00:05,074 - INFO - 开始处理日期: 2025-01
2025-05-01 03:00:05,074 - INFO - Request Parameters - Page 1:
2025-05-01 03:00:05,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:05,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:05,935 - INFO - Response - Page 1:
2025-05-01 03:00:06,138 - INFO - 第 1 页获取到 100 条记录
2025-05-01 03:00:06,138 - INFO - Request Parameters - Page 2:
2025-05-01 03:00:06,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:06,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:06,826 - INFO - Response - Page 2:
2025-05-01 03:00:07,029 - INFO - 第 2 页获取到 100 条记录
2025-05-01 03:00:07,029 - INFO - Request Parameters - Page 3:
2025-05-01 03:00:07,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:07,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:07,577 - INFO - Response - Page 3:
2025-05-01 03:00:07,780 - INFO - 第 3 页获取到 100 条记录
2025-05-01 03:00:07,780 - INFO - Request Parameters - Page 4:
2025-05-01 03:00:07,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:07,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:08,281 - INFO - Response - Page 4:
2025-05-01 03:00:08,484 - INFO - 第 4 页获取到 100 条记录
2025-05-01 03:00:08,484 - INFO - Request Parameters - Page 5:
2025-05-01 03:00:08,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:08,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:09,047 - INFO - Response - Page 5:
2025-05-01 03:00:09,250 - INFO - 第 5 页获取到 100 条记录
2025-05-01 03:00:09,250 - INFO - Request Parameters - Page 6:
2025-05-01 03:00:09,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:09,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:09,767 - INFO - Response - Page 6:
2025-05-01 03:00:09,970 - INFO - 第 6 页获取到 100 条记录
2025-05-01 03:00:09,970 - INFO - Request Parameters - Page 7:
2025-05-01 03:00:09,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:09,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:10,439 - INFO - Response - Page 7:
2025-05-01 03:00:10,642 - INFO - 第 7 页获取到 82 条记录
2025-05-01 03:00:10,642 - INFO - 查询完成，共获取到 682 条记录
2025-05-01 03:00:10,642 - INFO - 获取到 682 条表单数据
2025-05-01 03:00:10,642 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-01 03:00:10,658 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 03:00:10,658 - INFO - 开始处理日期: 2025-02
2025-05-01 03:00:10,658 - INFO - Request Parameters - Page 1:
2025-05-01 03:00:10,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:10,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:11,096 - INFO - Response - Page 1:
2025-05-01 03:00:11,299 - INFO - 第 1 页获取到 100 条记录
2025-05-01 03:00:11,299 - INFO - Request Parameters - Page 2:
2025-05-01 03:00:11,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:11,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:11,768 - INFO - Response - Page 2:
2025-05-01 03:00:11,972 - INFO - 第 2 页获取到 100 条记录
2025-05-01 03:00:11,972 - INFO - Request Parameters - Page 3:
2025-05-01 03:00:11,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:11,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:12,457 - INFO - Response - Page 3:
2025-05-01 03:00:12,660 - INFO - 第 3 页获取到 100 条记录
2025-05-01 03:00:12,660 - INFO - Request Parameters - Page 4:
2025-05-01 03:00:12,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:12,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:13,129 - INFO - Response - Page 4:
2025-05-01 03:00:13,333 - INFO - 第 4 页获取到 100 条记录
2025-05-01 03:00:13,333 - INFO - Request Parameters - Page 5:
2025-05-01 03:00:13,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:13,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:13,786 - INFO - Response - Page 5:
2025-05-01 03:00:13,989 - INFO - 第 5 页获取到 100 条记录
2025-05-01 03:00:13,989 - INFO - Request Parameters - Page 6:
2025-05-01 03:00:13,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:13,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:14,459 - INFO - Response - Page 6:
2025-05-01 03:00:14,662 - INFO - 第 6 页获取到 100 条记录
2025-05-01 03:00:14,662 - INFO - Request Parameters - Page 7:
2025-05-01 03:00:14,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:14,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:15,100 - INFO - Response - Page 7:
2025-05-01 03:00:15,303 - INFO - 第 7 页获取到 70 条记录
2025-05-01 03:00:15,303 - INFO - 查询完成，共获取到 670 条记录
2025-05-01 03:00:15,303 - INFO - 获取到 670 条表单数据
2025-05-01 03:00:15,303 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-01 03:00:15,319 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 03:00:15,319 - INFO - 开始处理日期: 2025-03
2025-05-01 03:00:15,319 - INFO - Request Parameters - Page 1:
2025-05-01 03:00:15,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:15,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:15,804 - INFO - Response - Page 1:
2025-05-01 03:00:16,007 - INFO - 第 1 页获取到 100 条记录
2025-05-01 03:00:16,007 - INFO - Request Parameters - Page 2:
2025-05-01 03:00:16,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:16,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:16,445 - INFO - Response - Page 2:
2025-05-01 03:00:16,648 - INFO - 第 2 页获取到 100 条记录
2025-05-01 03:00:16,648 - INFO - Request Parameters - Page 3:
2025-05-01 03:00:16,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:16,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:17,117 - INFO - Response - Page 3:
2025-05-01 03:00:17,321 - INFO - 第 3 页获取到 100 条记录
2025-05-01 03:00:17,321 - INFO - Request Parameters - Page 4:
2025-05-01 03:00:17,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:17,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:17,806 - INFO - Response - Page 4:
2025-05-01 03:00:18,009 - INFO - 第 4 页获取到 100 条记录
2025-05-01 03:00:18,009 - INFO - Request Parameters - Page 5:
2025-05-01 03:00:18,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:18,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:18,509 - INFO - Response - Page 5:
2025-05-01 03:00:18,713 - INFO - 第 5 页获取到 100 条记录
2025-05-01 03:00:18,713 - INFO - Request Parameters - Page 6:
2025-05-01 03:00:18,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:18,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:19,245 - INFO - Response - Page 6:
2025-05-01 03:00:19,448 - INFO - 第 6 页获取到 100 条记录
2025-05-01 03:00:19,448 - INFO - Request Parameters - Page 7:
2025-05-01 03:00:19,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:19,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:19,855 - INFO - Response - Page 7:
2025-05-01 03:00:20,058 - INFO - 第 7 页获取到 61 条记录
2025-05-01 03:00:20,058 - INFO - 查询完成，共获取到 661 条记录
2025-05-01 03:00:20,058 - INFO - 获取到 661 条表单数据
2025-05-01 03:00:20,058 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-01 03:00:20,074 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 03:00:20,074 - INFO - 开始处理日期: 2025-04
2025-05-01 03:00:20,074 - INFO - Request Parameters - Page 1:
2025-05-01 03:00:20,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:20,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:20,574 - INFO - Response - Page 1:
2025-05-01 03:00:20,777 - INFO - 第 1 页获取到 100 条记录
2025-05-01 03:00:20,777 - INFO - Request Parameters - Page 2:
2025-05-01 03:00:20,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:20,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:21,247 - INFO - Response - Page 2:
2025-05-01 03:00:21,450 - INFO - 第 2 页获取到 100 条记录
2025-05-01 03:00:21,450 - INFO - Request Parameters - Page 3:
2025-05-01 03:00:21,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:21,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:21,966 - INFO - Response - Page 3:
2025-05-01 03:00:22,169 - INFO - 第 3 页获取到 100 条记录
2025-05-01 03:00:22,169 - INFO - Request Parameters - Page 4:
2025-05-01 03:00:22,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:22,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:22,623 - INFO - Response - Page 4:
2025-05-01 03:00:22,826 - INFO - 第 4 页获取到 100 条记录
2025-05-01 03:00:22,826 - INFO - Request Parameters - Page 5:
2025-05-01 03:00:22,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:22,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:23,358 - INFO - Response - Page 5:
2025-05-01 03:00:23,561 - INFO - 第 5 页获取到 100 条记录
2025-05-01 03:00:23,561 - INFO - Request Parameters - Page 6:
2025-05-01 03:00:23,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:23,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:24,077 - INFO - Response - Page 6:
2025-05-01 03:00:24,281 - INFO - 第 6 页获取到 100 条记录
2025-05-01 03:00:24,281 - INFO - Request Parameters - Page 7:
2025-05-01 03:00:24,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:00:24,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:00:24,640 - INFO - Response - Page 7:
2025-05-01 03:00:24,844 - INFO - 第 7 页获取到 27 条记录
2025-05-01 03:00:24,844 - INFO - 查询完成，共获取到 627 条记录
2025-05-01 03:00:24,844 - INFO - 获取到 627 条表单数据
2025-05-01 03:00:24,844 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-01 03:00:24,844 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-05-01 03:00:25,329 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-05-01 03:00:25,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92716.66, 'new_value': 96250.07}, {'field': 'offline_amount', 'old_value': 240453.9, 'new_value': 249266.05}, {'field': 'total_amount', 'old_value': 333170.56, 'new_value': 345516.12}, {'field': 'order_count', 'old_value': 11888, 'new_value': 12357}]
2025-05-01 03:00:25,329 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-05-01 03:00:25,798 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-05-01 03:00:25,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99492.3, 'new_value': 102672.3}, {'field': 'total_amount', 'old_value': 99492.3, 'new_value': 102672.3}, {'field': 'order_count', 'old_value': 651, 'new_value': 668}]
2025-05-01 03:00:25,798 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO4
2025-05-01 03:00:26,236 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO4
2025-05-01 03:00:26,236 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10197.0, 'new_value': 10757.0}, {'field': 'total_amount', 'old_value': 10197.0, 'new_value': 10757.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-01 03:00:26,236 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-05-01 03:00:26,642 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-05-01 03:00:26,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103634.9, 'new_value': 105614.5}, {'field': 'total_amount', 'old_value': 114548.3, 'new_value': 116527.9}, {'field': 'order_count', 'old_value': 159, 'new_value': 167}]
2025-05-01 03:00:26,642 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-05-01 03:00:27,143 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-05-01 03:00:27,143 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79932.72, 'new_value': 82325.86}, {'field': 'total_amount', 'old_value': 81514.06, 'new_value': 83907.2}, {'field': 'order_count', 'old_value': 3218, 'new_value': 3326}]
2025-05-01 03:00:27,143 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-05-01 03:00:27,706 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-05-01 03:00:27,706 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114003.11, 'new_value': 117982.3}, {'field': 'offline_amount', 'old_value': 328938.46, 'new_value': 338590.61}, {'field': 'total_amount', 'old_value': 442941.57, 'new_value': 456572.91}, {'field': 'order_count', 'old_value': 11456, 'new_value': 11841}]
2025-05-01 03:00:27,706 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-05-01 03:00:28,128 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-05-01 03:00:28,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57515.1, 'new_value': 58415.9}, {'field': 'total_amount', 'old_value': 57515.1, 'new_value': 58415.9}, {'field': 'order_count', 'old_value': 467, 'new_value': 474}]
2025-05-01 03:00:28,128 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-05-01 03:00:28,629 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-05-01 03:00:28,629 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8184.07, 'new_value': 8342.07}, {'field': 'total_amount', 'old_value': 24161.75, 'new_value': 24319.75}, {'field': 'order_count', 'old_value': 92, 'new_value': 94}]
2025-05-01 03:00:28,629 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-05-01 03:00:29,082 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-05-01 03:00:29,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44309.68, 'new_value': 45434.88}, {'field': 'offline_amount', 'old_value': 362545.54, 'new_value': 376724.91}, {'field': 'total_amount', 'old_value': 406855.22, 'new_value': 422159.79}, {'field': 'order_count', 'old_value': 3642, 'new_value': 3790}]
2025-05-01 03:00:29,082 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-05-01 03:00:29,520 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-05-01 03:00:29,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38369.0, 'new_value': 40005.0}, {'field': 'total_amount', 'old_value': 38369.0, 'new_value': 40005.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 132}]
2025-05-01 03:00:29,520 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-05-01 03:00:30,021 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-05-01 03:00:30,021 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60050.16, 'new_value': 61674.29}, {'field': 'offline_amount', 'old_value': 77084.15, 'new_value': 79399.22}, {'field': 'total_amount', 'old_value': 137134.31, 'new_value': 141073.51}, {'field': 'order_count', 'old_value': 5441, 'new_value': 5596}]
2025-05-01 03:00:30,021 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML5
2025-05-01 03:00:30,443 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML5
2025-05-01 03:00:30,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90061.0, 'new_value': 95061.0}, {'field': 'total_amount', 'old_value': 90061.0, 'new_value': 95061.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-05-01 03:00:30,443 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-05-01 03:00:30,818 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-05-01 03:00:30,818 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59513.35, 'new_value': 62687.07}, {'field': 'offline_amount', 'old_value': 25688.97, 'new_value': 26179.67}, {'field': 'total_amount', 'old_value': 85202.32, 'new_value': 88866.74}, {'field': 'order_count', 'old_value': 3490, 'new_value': 3620}]
2025-05-01 03:00:30,834 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-05-01 03:00:31,366 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-05-01 03:00:31,366 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11012.76, 'new_value': 11440.36}, {'field': 'offline_amount', 'old_value': 86873.18, 'new_value': 90017.55}, {'field': 'total_amount', 'old_value': 97885.94, 'new_value': 101457.91}, {'field': 'order_count', 'old_value': 2667, 'new_value': 2777}]
2025-05-01 03:00:31,366 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-05-01 03:00:31,788 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-05-01 03:00:31,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45049.0, 'new_value': 46484.0}, {'field': 'total_amount', 'old_value': 45049.0, 'new_value': 46484.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 117}]
2025-05-01 03:00:31,788 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-05-01 03:00:32,210 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-05-01 03:00:32,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100909.95, 'new_value': 104996.88}, {'field': 'offline_amount', 'old_value': 67839.24, 'new_value': 70345.67}, {'field': 'total_amount', 'old_value': 168749.19, 'new_value': 175342.55}, {'field': 'order_count', 'old_value': 8859, 'new_value': 9232}]
2025-05-01 03:00:32,210 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-05-01 03:00:32,695 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-05-01 03:00:32,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306928.96, 'new_value': 317920.53}, {'field': 'total_amount', 'old_value': 322687.76, 'new_value': 333679.33}, {'field': 'order_count', 'old_value': 13479, 'new_value': 13963}]
2025-05-01 03:00:32,695 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-05-01 03:00:33,118 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-05-01 03:00:33,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10651.8, 'new_value': 10823.8}, {'field': 'total_amount', 'old_value': 19509.4, 'new_value': 19681.4}, {'field': 'order_count', 'old_value': 168, 'new_value': 173}]
2025-05-01 03:00:33,118 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-05-01 03:00:33,618 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-05-01 03:00:33,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216610.0, 'new_value': 229278.0}, {'field': 'total_amount', 'old_value': 216610.0, 'new_value': 229278.0}, {'field': 'order_count', 'old_value': 263, 'new_value': 264}]
2025-05-01 03:00:33,618 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-05-01 03:00:34,040 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-05-01 03:00:34,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151979.05, 'new_value': 153032.05}, {'field': 'total_amount', 'old_value': 195375.75, 'new_value': 196428.75}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-05-01 03:00:34,040 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-05-01 03:00:34,447 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-05-01 03:00:34,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 379338.97, 'new_value': 392558.9}, {'field': 'total_amount', 'old_value': 379338.97, 'new_value': 392558.9}, {'field': 'order_count', 'old_value': 16535, 'new_value': 17076}]
2025-05-01 03:00:34,447 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-05-01 03:00:34,916 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-05-01 03:00:34,916 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9714.72, 'new_value': 10507.62}, {'field': 'offline_amount', 'old_value': 36458.38, 'new_value': 37357.28}, {'field': 'total_amount', 'old_value': 46173.1, 'new_value': 47864.9}, {'field': 'order_count', 'old_value': 2095, 'new_value': 2175}]
2025-05-01 03:00:34,916 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-05-01 03:00:35,338 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-05-01 03:00:35,338 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10299.27, 'new_value': 10677.23}, {'field': 'offline_amount', 'old_value': 67806.31, 'new_value': 69783.31}, {'field': 'total_amount', 'old_value': 78105.58, 'new_value': 80460.54}, {'field': 'order_count', 'old_value': 3155, 'new_value': 3246}]
2025-05-01 03:00:35,338 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-05-01 03:00:35,745 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-05-01 03:00:35,745 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54793.08, 'new_value': 54878.42}, {'field': 'offline_amount', 'old_value': 43901.04, 'new_value': 43883.54}, {'field': 'total_amount', 'old_value': 98694.12, 'new_value': 98761.96}]
2025-05-01 03:00:35,745 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-05-01 03:00:36,152 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-05-01 03:00:36,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101891.35, 'new_value': 105186.38}, {'field': 'offline_amount', 'old_value': 211000.03, 'new_value': 218758.91}, {'field': 'total_amount', 'old_value': 312891.38, 'new_value': 323945.29}, {'field': 'order_count', 'old_value': 9727, 'new_value': 10056}]
2025-05-01 03:00:36,152 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-05-01 03:00:36,652 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-05-01 03:00:36,652 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2521.42, 'new_value': 2996.42}, {'field': 'offline_amount', 'old_value': 28875.51, 'new_value': 29802.61}, {'field': 'total_amount', 'old_value': 31396.93, 'new_value': 32799.03}, {'field': 'order_count', 'old_value': 1264, 'new_value': 1301}]
2025-05-01 03:00:36,652 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-05-01 03:00:37,090 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-05-01 03:00:37,090 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51904.2, 'new_value': 53911.79}, {'field': 'offline_amount', 'old_value': 192598.57, 'new_value': 198678.81}, {'field': 'total_amount', 'old_value': 244502.77, 'new_value': 252590.6}, {'field': 'order_count', 'old_value': 4839, 'new_value': 5008}]
2025-05-01 03:00:37,090 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-05-01 03:00:37,528 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-05-01 03:00:37,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249214.58, 'new_value': 258638.04}, {'field': 'total_amount', 'old_value': 249214.58, 'new_value': 258638.04}, {'field': 'order_count', 'old_value': 1925, 'new_value': 2001}]
2025-05-01 03:00:37,528 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-05-01 03:00:37,919 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-05-01 03:00:37,919 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129300.56, 'new_value': 134637.86}, {'field': 'offline_amount', 'old_value': 127825.55, 'new_value': 133542.95}, {'field': 'total_amount', 'old_value': 257126.11, 'new_value': 268180.81}, {'field': 'order_count', 'old_value': 5818, 'new_value': 6074}]
2025-05-01 03:00:37,919 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-05-01 03:00:38,357 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-05-01 03:00:38,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1900926.0, 'new_value': 1979137.0}, {'field': 'total_amount', 'old_value': 1900926.0, 'new_value': 1979137.0}, {'field': 'order_count', 'old_value': 7691, 'new_value': 7979}]
2025-05-01 03:00:38,357 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWI
2025-05-01 03:00:38,795 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWI
2025-05-01 03:00:38,795 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3943.0, 'new_value': 8181.0}, {'field': 'total_amount', 'old_value': 3943.0, 'new_value': 8181.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-01 03:00:38,811 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-05-01 03:00:39,233 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-05-01 03:00:39,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13795.0, 'new_value': 14520.0}, {'field': 'total_amount', 'old_value': 13795.0, 'new_value': 14520.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-05-01 03:00:39,233 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-05-01 03:00:39,686 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-05-01 03:00:39,686 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90658.4, 'new_value': 93561.5}, {'field': 'offline_amount', 'old_value': 168874.9, 'new_value': 186546.5}, {'field': 'total_amount', 'old_value': 259533.3, 'new_value': 280108.0}, {'field': 'order_count', 'old_value': 4684, 'new_value': 5095}]
2025-05-01 03:00:39,686 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-05-01 03:00:40,109 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-05-01 03:00:40,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178188.17, 'new_value': 183332.94}, {'field': 'total_amount', 'old_value': 178188.17, 'new_value': 183332.94}, {'field': 'order_count', 'old_value': 7831, 'new_value': 8052}]
2025-05-01 03:00:40,109 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-05-01 03:00:40,531 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-05-01 03:00:40,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10875.49, 'new_value': 11457.0}, {'field': 'offline_amount', 'old_value': 430593.23, 'new_value': 447484.43}, {'field': 'total_amount', 'old_value': 441468.72, 'new_value': 458941.43}, {'field': 'order_count', 'old_value': 18588, 'new_value': 19300}]
2025-05-01 03:00:40,531 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-05-01 03:00:40,985 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-05-01 03:00:40,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149439.0, 'new_value': 153664.0}, {'field': 'total_amount', 'old_value': 149439.0, 'new_value': 153664.0}, {'field': 'order_count', 'old_value': 2600, 'new_value': 2695}]
2025-05-01 03:00:40,985 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-05-01 03:00:41,360 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-05-01 03:00:41,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3110.88, 'new_value': 3151.23}, {'field': 'offline_amount', 'old_value': 15611.28, 'new_value': 16084.94}, {'field': 'total_amount', 'old_value': 18722.16, 'new_value': 19236.17}, {'field': 'order_count', 'old_value': 675, 'new_value': 698}]
2025-05-01 03:00:41,360 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-05-01 03:00:41,782 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-05-01 03:00:41,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104825.84, 'new_value': 112742.03}, {'field': 'total_amount', 'old_value': 146966.38, 'new_value': 154882.57}, {'field': 'order_count', 'old_value': 4230, 'new_value': 4430}]
2025-05-01 03:00:41,782 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-05-01 03:00:42,236 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-05-01 03:00:42,236 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4064.0, 'new_value': 4339.0}, {'field': 'offline_amount', 'old_value': 21771.6, 'new_value': 22150.6}, {'field': 'total_amount', 'old_value': 25835.6, 'new_value': 26489.6}, {'field': 'order_count', 'old_value': 938, 'new_value': 956}]
2025-05-01 03:00:42,236 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M81
2025-05-01 03:00:42,611 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M81
2025-05-01 03:00:42,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47429.17, 'new_value': 49751.41}, {'field': 'total_amount', 'old_value': 47429.17, 'new_value': 49751.41}, {'field': 'order_count', 'old_value': 2643, 'new_value': 2777}]
2025-05-01 03:00:42,611 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-05-01 03:00:43,049 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-05-01 03:00:43,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 466663.06, 'new_value': 484181.6}, {'field': 'total_amount', 'old_value': 469146.64, 'new_value': 486665.18}, {'field': 'order_count', 'old_value': 7346, 'new_value': 7588}]
2025-05-01 03:00:43,049 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-05-01 03:00:43,518 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-05-01 03:00:43,518 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19555.61, 'new_value': 20649.53}, {'field': 'offline_amount', 'old_value': 37188.3, 'new_value': 38480.62}, {'field': 'total_amount', 'old_value': 56743.91, 'new_value': 59130.15}, {'field': 'order_count', 'old_value': 2688, 'new_value': 2817}]
2025-05-01 03:00:43,518 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-05-01 03:00:43,972 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-05-01 03:00:43,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137644.93, 'new_value': 142457.7}, {'field': 'total_amount', 'old_value': 137644.93, 'new_value': 142457.7}, {'field': 'order_count', 'old_value': 3594, 'new_value': 3728}]
2025-05-01 03:00:43,972 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-05-01 03:00:44,410 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-05-01 03:00:44,410 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131069.32, 'new_value': 135842.43}, {'field': 'offline_amount', 'old_value': 248494.63, 'new_value': 256074.49}, {'field': 'total_amount', 'old_value': 379563.95, 'new_value': 391916.92}, {'field': 'order_count', 'old_value': 4472, 'new_value': 4635}]
2025-05-01 03:00:44,410 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML7
2025-05-01 03:00:44,863 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML7
2025-05-01 03:00:44,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174322.0, 'new_value': 181122.0}, {'field': 'total_amount', 'old_value': 177142.0, 'new_value': 183942.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-05-01 03:00:44,863 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-05-01 03:00:45,223 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-05-01 03:00:45,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 246926.19, 'new_value': 255770.65}, {'field': 'offline_amount', 'old_value': 538741.05, 'new_value': 558741.05}, {'field': 'total_amount', 'old_value': 785667.24, 'new_value': 814511.7}, {'field': 'order_count', 'old_value': 1784, 'new_value': 1848}]
2025-05-01 03:00:45,223 - INFO - 日期 2025-04 处理完成 - 更新: 46 条，插入: 0 条，错误: 0 条
2025-05-01 03:00:45,223 - INFO - 数据同步完成！更新: 46 条，插入: 0 条，错误: 0 条
2025-05-01 03:00:45,223 - INFO - =================同步完成====================
2025-05-01 06:00:03,753 - INFO - =================使用默认全量同步=============
2025-05-01 06:00:04,895 - INFO - MySQL查询成功，共获取 2640 条记录
2025-05-01 06:00:04,895 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-05-01 06:00:04,910 - INFO - 开始处理日期: 2025-01
2025-05-01 06:00:04,926 - INFO - Request Parameters - Page 1:
2025-05-01 06:00:04,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:04,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:05,677 - INFO - Response - Page 1:
2025-05-01 06:00:05,880 - INFO - 第 1 页获取到 100 条记录
2025-05-01 06:00:05,880 - INFO - Request Parameters - Page 2:
2025-05-01 06:00:05,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:05,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:06,584 - INFO - Response - Page 2:
2025-05-01 06:00:06,787 - INFO - 第 2 页获取到 100 条记录
2025-05-01 06:00:06,787 - INFO - Request Parameters - Page 3:
2025-05-01 06:00:06,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:06,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:07,225 - INFO - Response - Page 3:
2025-05-01 06:00:07,428 - INFO - 第 3 页获取到 100 条记录
2025-05-01 06:00:07,428 - INFO - Request Parameters - Page 4:
2025-05-01 06:00:07,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:07,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:07,913 - INFO - Response - Page 4:
2025-05-01 06:00:08,117 - INFO - 第 4 页获取到 100 条记录
2025-05-01 06:00:08,117 - INFO - Request Parameters - Page 5:
2025-05-01 06:00:08,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:08,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:08,539 - INFO - Response - Page 5:
2025-05-01 06:00:08,758 - INFO - 第 5 页获取到 100 条记录
2025-05-01 06:00:08,758 - INFO - Request Parameters - Page 6:
2025-05-01 06:00:08,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:08,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:09,227 - INFO - Response - Page 6:
2025-05-01 06:00:09,430 - INFO - 第 6 页获取到 100 条记录
2025-05-01 06:00:09,430 - INFO - Request Parameters - Page 7:
2025-05-01 06:00:09,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:09,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:09,931 - INFO - Response - Page 7:
2025-05-01 06:00:10,134 - INFO - 第 7 页获取到 82 条记录
2025-05-01 06:00:10,134 - INFO - 查询完成，共获取到 682 条记录
2025-05-01 06:00:10,134 - INFO - 获取到 682 条表单数据
2025-05-01 06:00:10,134 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-01 06:00:10,150 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 06:00:10,150 - INFO - 开始处理日期: 2025-02
2025-05-01 06:00:10,150 - INFO - Request Parameters - Page 1:
2025-05-01 06:00:10,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:10,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:10,666 - INFO - Response - Page 1:
2025-05-01 06:00:10,869 - INFO - 第 1 页获取到 100 条记录
2025-05-01 06:00:10,869 - INFO - Request Parameters - Page 2:
2025-05-01 06:00:10,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:10,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:11,385 - INFO - Response - Page 2:
2025-05-01 06:00:11,589 - INFO - 第 2 页获取到 100 条记录
2025-05-01 06:00:11,589 - INFO - Request Parameters - Page 3:
2025-05-01 06:00:11,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:11,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:12,058 - INFO - Response - Page 3:
2025-05-01 06:00:12,261 - INFO - 第 3 页获取到 100 条记录
2025-05-01 06:00:12,261 - INFO - Request Parameters - Page 4:
2025-05-01 06:00:12,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:12,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:12,777 - INFO - Response - Page 4:
2025-05-01 06:00:12,981 - INFO - 第 4 页获取到 100 条记录
2025-05-01 06:00:12,981 - INFO - Request Parameters - Page 5:
2025-05-01 06:00:12,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:12,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:13,419 - INFO - Response - Page 5:
2025-05-01 06:00:13,622 - INFO - 第 5 页获取到 100 条记录
2025-05-01 06:00:13,622 - INFO - Request Parameters - Page 6:
2025-05-01 06:00:13,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:13,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:14,154 - INFO - Response - Page 6:
2025-05-01 06:00:14,357 - INFO - 第 6 页获取到 100 条记录
2025-05-01 06:00:14,357 - INFO - Request Parameters - Page 7:
2025-05-01 06:00:14,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:14,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:14,811 - INFO - Response - Page 7:
2025-05-01 06:00:15,014 - INFO - 第 7 页获取到 70 条记录
2025-05-01 06:00:15,014 - INFO - 查询完成，共获取到 670 条记录
2025-05-01 06:00:15,014 - INFO - 获取到 670 条表单数据
2025-05-01 06:00:15,014 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-01 06:00:15,030 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 06:00:15,030 - INFO - 开始处理日期: 2025-03
2025-05-01 06:00:15,030 - INFO - Request Parameters - Page 1:
2025-05-01 06:00:15,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:15,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:15,546 - INFO - Response - Page 1:
2025-05-01 06:00:15,749 - INFO - 第 1 页获取到 100 条记录
2025-05-01 06:00:15,749 - INFO - Request Parameters - Page 2:
2025-05-01 06:00:15,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:15,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:16,250 - INFO - Response - Page 2:
2025-05-01 06:00:16,453 - INFO - 第 2 页获取到 100 条记录
2025-05-01 06:00:16,453 - INFO - Request Parameters - Page 3:
2025-05-01 06:00:16,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:16,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:16,938 - INFO - Response - Page 3:
2025-05-01 06:00:17,141 - INFO - 第 3 页获取到 100 条记录
2025-05-01 06:00:17,141 - INFO - Request Parameters - Page 4:
2025-05-01 06:00:17,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:17,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:17,892 - INFO - Response - Page 4:
2025-05-01 06:00:18,095 - INFO - 第 4 页获取到 100 条记录
2025-05-01 06:00:18,095 - INFO - Request Parameters - Page 5:
2025-05-01 06:00:18,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:18,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:18,580 - INFO - Response - Page 5:
2025-05-01 06:00:18,783 - INFO - 第 5 页获取到 100 条记录
2025-05-01 06:00:18,783 - INFO - Request Parameters - Page 6:
2025-05-01 06:00:18,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:18,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:19,268 - INFO - Response - Page 6:
2025-05-01 06:00:19,471 - INFO - 第 6 页获取到 100 条记录
2025-05-01 06:00:19,471 - INFO - Request Parameters - Page 7:
2025-05-01 06:00:19,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:19,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:19,894 - INFO - Response - Page 7:
2025-05-01 06:00:20,097 - INFO - 第 7 页获取到 61 条记录
2025-05-01 06:00:20,097 - INFO - 查询完成，共获取到 661 条记录
2025-05-01 06:00:20,097 - INFO - 获取到 661 条表单数据
2025-05-01 06:00:20,097 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-01 06:00:20,113 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 06:00:20,113 - INFO - 开始处理日期: 2025-04
2025-05-01 06:00:20,113 - INFO - Request Parameters - Page 1:
2025-05-01 06:00:20,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:20,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:20,582 - INFO - Response - Page 1:
2025-05-01 06:00:20,785 - INFO - 第 1 页获取到 100 条记录
2025-05-01 06:00:20,785 - INFO - Request Parameters - Page 2:
2025-05-01 06:00:20,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:20,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:21,254 - INFO - Response - Page 2:
2025-05-01 06:00:21,458 - INFO - 第 2 页获取到 100 条记录
2025-05-01 06:00:21,458 - INFO - Request Parameters - Page 3:
2025-05-01 06:00:21,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:21,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:21,927 - INFO - Response - Page 3:
2025-05-01 06:00:22,130 - INFO - 第 3 页获取到 100 条记录
2025-05-01 06:00:22,130 - INFO - Request Parameters - Page 4:
2025-05-01 06:00:22,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:22,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:22,615 - INFO - Response - Page 4:
2025-05-01 06:00:22,818 - INFO - 第 4 页获取到 100 条记录
2025-05-01 06:00:22,818 - INFO - Request Parameters - Page 5:
2025-05-01 06:00:22,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:22,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:23,382 - INFO - Response - Page 5:
2025-05-01 06:00:23,585 - INFO - 第 5 页获取到 100 条记录
2025-05-01 06:00:23,585 - INFO - Request Parameters - Page 6:
2025-05-01 06:00:23,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:23,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:24,070 - INFO - Response - Page 6:
2025-05-01 06:00:24,273 - INFO - 第 6 页获取到 100 条记录
2025-05-01 06:00:24,273 - INFO - Request Parameters - Page 7:
2025-05-01 06:00:24,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:00:24,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:00:24,633 - INFO - Response - Page 7:
2025-05-01 06:00:24,836 - INFO - 第 7 页获取到 27 条记录
2025-05-01 06:00:24,836 - INFO - 查询完成，共获取到 627 条记录
2025-05-01 06:00:24,836 - INFO - 获取到 627 条表单数据
2025-05-01 06:00:24,836 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-01 06:00:24,852 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 06:00:24,852 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 06:00:24,852 - INFO - =================同步完成====================
2025-05-01 09:00:01,853 - INFO - =================使用默认全量同步=============
2025-05-01 09:00:03,010 - INFO - MySQL查询成功，共获取 2640 条记录
2025-05-01 09:00:03,010 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-05-01 09:00:03,041 - INFO - 开始处理日期: 2025-01
2025-05-01 09:00:03,041 - INFO - Request Parameters - Page 1:
2025-05-01 09:00:03,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:03,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:04,010 - INFO - Response - Page 1:
2025-05-01 09:00:04,213 - INFO - 第 1 页获取到 100 条记录
2025-05-01 09:00:04,213 - INFO - Request Parameters - Page 2:
2025-05-01 09:00:04,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:04,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:04,869 - INFO - Response - Page 2:
2025-05-01 09:00:05,072 - INFO - 第 2 页获取到 100 条记录
2025-05-01 09:00:05,072 - INFO - Request Parameters - Page 3:
2025-05-01 09:00:05,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:05,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:05,603 - INFO - Response - Page 3:
2025-05-01 09:00:05,806 - INFO - 第 3 页获取到 100 条记录
2025-05-01 09:00:05,806 - INFO - Request Parameters - Page 4:
2025-05-01 09:00:05,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:05,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:06,291 - INFO - Response - Page 4:
2025-05-01 09:00:06,494 - INFO - 第 4 页获取到 100 条记录
2025-05-01 09:00:06,494 - INFO - Request Parameters - Page 5:
2025-05-01 09:00:06,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:06,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:07,041 - INFO - Response - Page 5:
2025-05-01 09:00:07,244 - INFO - 第 5 页获取到 100 条记录
2025-05-01 09:00:07,244 - INFO - Request Parameters - Page 6:
2025-05-01 09:00:07,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:07,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:07,728 - INFO - Response - Page 6:
2025-05-01 09:00:07,931 - INFO - 第 6 页获取到 100 条记录
2025-05-01 09:00:07,931 - INFO - Request Parameters - Page 7:
2025-05-01 09:00:07,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:07,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:08,416 - INFO - Response - Page 7:
2025-05-01 09:00:08,619 - INFO - 第 7 页获取到 82 条记录
2025-05-01 09:00:08,619 - INFO - 查询完成，共获取到 682 条记录
2025-05-01 09:00:08,619 - INFO - 获取到 682 条表单数据
2025-05-01 09:00:08,619 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-01 09:00:08,634 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 09:00:08,634 - INFO - 开始处理日期: 2025-02
2025-05-01 09:00:08,634 - INFO - Request Parameters - Page 1:
2025-05-01 09:00:08,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:08,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:09,150 - INFO - Response - Page 1:
2025-05-01 09:00:09,353 - INFO - 第 1 页获取到 100 条记录
2025-05-01 09:00:09,353 - INFO - Request Parameters - Page 2:
2025-05-01 09:00:09,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:09,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:09,869 - INFO - Response - Page 2:
2025-05-01 09:00:10,072 - INFO - 第 2 页获取到 100 条记录
2025-05-01 09:00:10,072 - INFO - Request Parameters - Page 3:
2025-05-01 09:00:10,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:10,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:10,572 - INFO - Response - Page 3:
2025-05-01 09:00:10,775 - INFO - 第 3 页获取到 100 条记录
2025-05-01 09:00:10,775 - INFO - Request Parameters - Page 4:
2025-05-01 09:00:10,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:10,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:11,228 - INFO - Response - Page 4:
2025-05-01 09:00:11,431 - INFO - 第 4 页获取到 100 条记录
2025-05-01 09:00:11,431 - INFO - Request Parameters - Page 5:
2025-05-01 09:00:11,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:11,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:11,869 - INFO - Response - Page 5:
2025-05-01 09:00:12,072 - INFO - 第 5 页获取到 100 条记录
2025-05-01 09:00:12,072 - INFO - Request Parameters - Page 6:
2025-05-01 09:00:12,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:12,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:12,619 - INFO - Response - Page 6:
2025-05-01 09:00:12,822 - INFO - 第 6 页获取到 100 条记录
2025-05-01 09:00:12,822 - INFO - Request Parameters - Page 7:
2025-05-01 09:00:12,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:12,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:13,275 - INFO - Response - Page 7:
2025-05-01 09:00:13,478 - INFO - 第 7 页获取到 70 条记录
2025-05-01 09:00:13,478 - INFO - 查询完成，共获取到 670 条记录
2025-05-01 09:00:13,478 - INFO - 获取到 670 条表单数据
2025-05-01 09:00:13,478 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-01 09:00:13,494 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 09:00:13,494 - INFO - 开始处理日期: 2025-03
2025-05-01 09:00:13,494 - INFO - Request Parameters - Page 1:
2025-05-01 09:00:13,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:13,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:14,025 - INFO - Response - Page 1:
2025-05-01 09:00:14,228 - INFO - 第 1 页获取到 100 条记录
2025-05-01 09:00:14,228 - INFO - Request Parameters - Page 2:
2025-05-01 09:00:14,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:14,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:14,666 - INFO - Response - Page 2:
2025-05-01 09:00:14,869 - INFO - 第 2 页获取到 100 条记录
2025-05-01 09:00:14,869 - INFO - Request Parameters - Page 3:
2025-05-01 09:00:14,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:14,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:15,353 - INFO - Response - Page 3:
2025-05-01 09:00:15,556 - INFO - 第 3 页获取到 100 条记录
2025-05-01 09:00:15,556 - INFO - Request Parameters - Page 4:
2025-05-01 09:00:15,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:15,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:15,994 - INFO - Response - Page 4:
2025-05-01 09:00:16,197 - INFO - 第 4 页获取到 100 条记录
2025-05-01 09:00:16,197 - INFO - Request Parameters - Page 5:
2025-05-01 09:00:16,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:16,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:16,697 - INFO - Response - Page 5:
2025-05-01 09:00:16,900 - INFO - 第 5 页获取到 100 条记录
2025-05-01 09:00:16,900 - INFO - Request Parameters - Page 6:
2025-05-01 09:00:16,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:16,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:17,416 - INFO - Response - Page 6:
2025-05-01 09:00:17,619 - INFO - 第 6 页获取到 100 条记录
2025-05-01 09:00:17,619 - INFO - Request Parameters - Page 7:
2025-05-01 09:00:17,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:17,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:18,119 - INFO - Response - Page 7:
2025-05-01 09:00:18,322 - INFO - 第 7 页获取到 61 条记录
2025-05-01 09:00:18,322 - INFO - 查询完成，共获取到 661 条记录
2025-05-01 09:00:18,322 - INFO - 获取到 661 条表单数据
2025-05-01 09:00:18,322 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-01 09:00:18,338 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 09:00:18,338 - INFO - 开始处理日期: 2025-04
2025-05-01 09:00:18,338 - INFO - Request Parameters - Page 1:
2025-05-01 09:00:18,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:18,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:18,853 - INFO - Response - Page 1:
2025-05-01 09:00:19,056 - INFO - 第 1 页获取到 100 条记录
2025-05-01 09:00:19,056 - INFO - Request Parameters - Page 2:
2025-05-01 09:00:19,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:19,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:19,588 - INFO - Response - Page 2:
2025-05-01 09:00:19,791 - INFO - 第 2 页获取到 100 条记录
2025-05-01 09:00:19,791 - INFO - Request Parameters - Page 3:
2025-05-01 09:00:19,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:19,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:20,259 - INFO - Response - Page 3:
2025-05-01 09:00:20,463 - INFO - 第 3 页获取到 100 条记录
2025-05-01 09:00:20,463 - INFO - Request Parameters - Page 4:
2025-05-01 09:00:20,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:20,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:20,916 - INFO - Response - Page 4:
2025-05-01 09:00:21,119 - INFO - 第 4 页获取到 100 条记录
2025-05-01 09:00:21,119 - INFO - Request Parameters - Page 5:
2025-05-01 09:00:21,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:21,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:21,681 - INFO - Response - Page 5:
2025-05-01 09:00:21,884 - INFO - 第 5 页获取到 100 条记录
2025-05-01 09:00:21,884 - INFO - Request Parameters - Page 6:
2025-05-01 09:00:21,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:21,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:22,384 - INFO - Response - Page 6:
2025-05-01 09:00:22,588 - INFO - 第 6 页获取到 100 条记录
2025-05-01 09:00:22,588 - INFO - Request Parameters - Page 7:
2025-05-01 09:00:22,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:00:22,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:00:22,900 - INFO - Response - Page 7:
2025-05-01 09:00:23,103 - INFO - 第 7 页获取到 27 条记录
2025-05-01 09:00:23,103 - INFO - 查询完成，共获取到 627 条记录
2025-05-01 09:00:23,103 - INFO - 获取到 627 条表单数据
2025-05-01 09:00:23,103 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-01 09:00:23,103 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-05-01 09:00:23,556 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-05-01 09:00:23,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44053.68, 'new_value': 45805.5}, {'field': 'total_amount', 'old_value': 44260.68, 'new_value': 46012.5}, {'field': 'order_count', 'old_value': 5645, 'new_value': 5881}]
2025-05-01 09:00:23,556 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-05-01 09:00:24,009 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-05-01 09:00:24,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 274013.9, 'new_value': 292654.9}, {'field': 'total_amount', 'old_value': 429590.9, 'new_value': 448231.9}, {'field': 'order_count', 'old_value': 604, 'new_value': 638}]
2025-05-01 09:00:24,009 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-05-01 09:00:24,494 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-05-01 09:00:24,494 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17821.85, 'new_value': 18325.23}, {'field': 'offline_amount', 'old_value': 48002.4, 'new_value': 49321.49}, {'field': 'total_amount', 'old_value': 65824.25, 'new_value': 67646.72}, {'field': 'order_count', 'old_value': 3497, 'new_value': 3592}]
2025-05-01 09:00:24,494 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-05-01 09:00:24,931 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-05-01 09:00:24,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204414.0, 'new_value': 210674.0}, {'field': 'total_amount', 'old_value': 204414.0, 'new_value': 210674.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-01 09:00:24,931 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-05-01 09:00:25,400 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-05-01 09:00:25,400 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1052713.0, 'new_value': 1083243.0}, {'field': 'total_amount', 'old_value': 1052713.0, 'new_value': 1083243.0}, {'field': 'order_count', 'old_value': 170, 'new_value': 180}]
2025-05-01 09:00:25,400 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-05-01 09:00:25,837 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-05-01 09:00:25,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112203.68, 'new_value': 116819.01}, {'field': 'total_amount', 'old_value': 112203.68, 'new_value': 116819.01}, {'field': 'order_count', 'old_value': 2956, 'new_value': 3086}]
2025-05-01 09:00:25,837 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-05-01 09:00:26,259 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-05-01 09:00:26,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23095.4, 'new_value': 23411.4}, {'field': 'offline_amount', 'old_value': 51848.49, 'new_value': 53605.29}, {'field': 'total_amount', 'old_value': 74943.89, 'new_value': 77016.69}, {'field': 'order_count', 'old_value': 887, 'new_value': 927}]
2025-05-01 09:00:26,275 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-05-01 09:00:26,837 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-05-01 09:00:26,837 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6076.0, 'new_value': 6691.0}, {'field': 'offline_amount', 'old_value': 32586.0, 'new_value': 34276.0}, {'field': 'total_amount', 'old_value': 38662.0, 'new_value': 40967.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 71}]
2025-05-01 09:00:26,837 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-05-01 09:00:27,322 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-05-01 09:00:27,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 524412.95, 'new_value': 537847.15}, {'field': 'total_amount', 'old_value': 524412.95, 'new_value': 537847.15}, {'field': 'order_count', 'old_value': 2321, 'new_value': 2362}]
2025-05-01 09:00:27,322 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-05-01 09:00:27,806 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-05-01 09:00:27,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136285.4, 'new_value': 143418.4}, {'field': 'total_amount', 'old_value': 162598.21, 'new_value': 169731.21}, {'field': 'order_count', 'old_value': 3968, 'new_value': 4136}]
2025-05-01 09:00:27,806 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-05-01 09:00:28,244 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-05-01 09:00:28,244 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26054.98, 'new_value': 26293.94}, {'field': 'total_amount', 'old_value': 26054.98, 'new_value': 26293.94}, {'field': 'order_count', 'old_value': 158, 'new_value': 160}]
2025-05-01 09:00:28,244 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-05-01 09:00:28,697 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-05-01 09:00:28,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199644.0, 'new_value': 204059.0}, {'field': 'total_amount', 'old_value': 199644.0, 'new_value': 204059.0}, {'field': 'order_count', 'old_value': 397, 'new_value': 409}]
2025-05-01 09:00:28,697 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-05-01 09:00:29,166 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-05-01 09:00:29,166 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97341.0, 'new_value': 99021.0}, {'field': 'offline_amount', 'old_value': 219223.0, 'new_value': 228753.0}, {'field': 'total_amount', 'old_value': 316564.0, 'new_value': 327774.0}, {'field': 'order_count', 'old_value': 301, 'new_value': 309}]
2025-05-01 09:00:29,166 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-05-01 09:00:29,572 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-05-01 09:00:29,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47634.8, 'new_value': 50233.8}, {'field': 'offline_amount', 'old_value': 531302.0, 'new_value': 582299.0}, {'field': 'total_amount', 'old_value': 578936.8, 'new_value': 632532.8}, {'field': 'order_count', 'old_value': 71, 'new_value': 77}]
2025-05-01 09:00:29,572 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-05-01 09:00:30,009 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-05-01 09:00:30,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294028.1, 'new_value': 309046.8}, {'field': 'total_amount', 'old_value': 294028.1, 'new_value': 309046.8}, {'field': 'order_count', 'old_value': 1146, 'new_value': 1196}]
2025-05-01 09:00:30,009 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-05-01 09:00:30,478 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-05-01 09:00:30,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35752.89, 'new_value': 35891.57}, {'field': 'offline_amount', 'old_value': 87061.64, 'new_value': 93198.44}, {'field': 'total_amount', 'old_value': 122814.53, 'new_value': 129090.01}, {'field': 'order_count', 'old_value': 506, 'new_value': 536}]
2025-05-01 09:00:30,478 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-05-01 09:00:30,931 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-05-01 09:00:30,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238027.0, 'new_value': 253724.0}, {'field': 'total_amount', 'old_value': 255124.0, 'new_value': 270821.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-05-01 09:00:30,931 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5A
2025-05-01 09:00:31,337 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5A
2025-05-01 09:00:31,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20345.0, 'new_value': 22813.0}, {'field': 'total_amount', 'old_value': 20345.0, 'new_value': 22813.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 83}]
2025-05-01 09:00:31,337 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-05-01 09:00:31,947 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-05-01 09:00:31,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 200547.56, 'new_value': 209289.46}, {'field': 'offline_amount', 'old_value': 165835.68, 'new_value': 170857.18}, {'field': 'total_amount', 'old_value': 366383.24, 'new_value': 380146.64}, {'field': 'order_count', 'old_value': 174200, 'new_value': 174307}]
2025-05-01 09:00:31,947 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-05-01 09:00:32,478 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-05-01 09:00:32,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98007.69, 'new_value': 102441.19}, {'field': 'total_amount', 'old_value': 98007.69, 'new_value': 102441.19}, {'field': 'order_count', 'old_value': 430, 'new_value': 443}]
2025-05-01 09:00:32,478 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-05-01 09:00:32,947 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-05-01 09:00:32,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 4649.4}, {'field': 'total_amount', 'old_value': 73723.2, 'new_value': 78372.6}, {'field': 'order_count', 'old_value': 2059, 'new_value': 2110}]
2025-05-01 09:00:32,947 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-05-01 09:00:33,478 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-05-01 09:00:33,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 400468.0, 'new_value': 411563.9}, {'field': 'offline_amount', 'old_value': 20588.0, 'new_value': 20828.0}, {'field': 'total_amount', 'old_value': 421056.0, 'new_value': 432391.9}, {'field': 'order_count', 'old_value': 3889, 'new_value': 3992}]
2025-05-01 09:00:33,478 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-05-01 09:00:33,978 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-05-01 09:00:33,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185181.94, 'new_value': 190546.47}, {'field': 'total_amount', 'old_value': 185181.94, 'new_value': 190546.47}, {'field': 'order_count', 'old_value': 860, 'new_value': 890}]
2025-05-01 09:00:33,978 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYA
2025-05-01 09:00:34,478 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYA
2025-05-01 09:00:34,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23567.0, 'new_value': 26198.0}, {'field': 'total_amount', 'old_value': 23567.0, 'new_value': 26198.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 26}]
2025-05-01 09:00:34,478 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-05-01 09:00:34,884 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-05-01 09:00:34,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62828.0, 'new_value': 64586.0}, {'field': 'total_amount', 'old_value': 63225.0, 'new_value': 64983.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 107}]
2025-05-01 09:00:34,900 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-05-01 09:00:35,400 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-05-01 09:00:35,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 258252.0, 'new_value': 267852.0}, {'field': 'total_amount', 'old_value': 258252.0, 'new_value': 267852.0}, {'field': 'order_count', 'old_value': 21521, 'new_value': 22321}]
2025-05-01 09:00:35,416 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-05-01 09:00:35,837 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-05-01 09:00:35,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1752400.18, 'new_value': 1866888.6}, {'field': 'total_amount', 'old_value': 1752400.18, 'new_value': 1866888.6}, {'field': 'order_count', 'old_value': 3656, 'new_value': 3836}]
2025-05-01 09:00:35,837 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-05-01 09:00:36,447 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-05-01 09:00:36,447 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7972.8, 'new_value': 8151.4}, {'field': 'offline_amount', 'old_value': 20877.11, 'new_value': 21218.8}, {'field': 'total_amount', 'old_value': 28849.91, 'new_value': 29370.2}, {'field': 'order_count', 'old_value': 959, 'new_value': 981}]
2025-05-01 09:00:36,447 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-05-01 09:00:36,884 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-05-01 09:00:36,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120080.6, 'new_value': 123321.65}, {'field': 'total_amount', 'old_value': 137828.5, 'new_value': 141069.55}, {'field': 'order_count', 'old_value': 404, 'new_value': 416}]
2025-05-01 09:00:36,884 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-05-01 09:00:37,353 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-05-01 09:00:37,353 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25657.07, 'new_value': 26802.45}, {'field': 'offline_amount', 'old_value': 41997.14, 'new_value': 43404.14}, {'field': 'total_amount', 'old_value': 67654.21, 'new_value': 70206.59}, {'field': 'order_count', 'old_value': 2827, 'new_value': 2932}]
2025-05-01 09:00:37,353 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-05-01 09:00:37,837 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-05-01 09:00:37,837 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7647.0, 'new_value': 7897.0}, {'field': 'offline_amount', 'old_value': 30166.6, 'new_value': 31013.9}, {'field': 'total_amount', 'old_value': 37813.6, 'new_value': 38910.9}, {'field': 'order_count', 'old_value': 1448, 'new_value': 1499}]
2025-05-01 09:00:37,837 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-05-01 09:00:38,275 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-05-01 09:00:38,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 405529.77, 'new_value': 511483.65}, {'field': 'total_amount', 'old_value': 405530.77, 'new_value': 511484.65}, {'field': 'order_count', 'old_value': 667, 'new_value': 734}]
2025-05-01 09:00:38,291 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-05-01 09:00:38,744 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-05-01 09:00:38,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166490.41, 'new_value': 172083.41}, {'field': 'total_amount', 'old_value': 166490.41, 'new_value': 172083.41}, {'field': 'order_count', 'old_value': 5887, 'new_value': 6096}]
2025-05-01 09:00:38,744 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-05-01 09:00:39,166 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-05-01 09:00:39,166 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14690.32, 'new_value': 15459.98}, {'field': 'offline_amount', 'old_value': 529399.92, 'new_value': 549499.2}, {'field': 'total_amount', 'old_value': 544090.24, 'new_value': 564959.18}, {'field': 'order_count', 'old_value': 2202, 'new_value': 2302}]
2025-05-01 09:00:39,166 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-05-01 09:00:39,603 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-05-01 09:00:39,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30447.69, 'new_value': 31353.3}, {'field': 'offline_amount', 'old_value': 55689.1, 'new_value': 57388.52}, {'field': 'total_amount', 'old_value': 86136.79, 'new_value': 88741.82}, {'field': 'order_count', 'old_value': 3074, 'new_value': 3165}]
2025-05-01 09:00:39,603 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-05-01 09:00:40,072 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-05-01 09:00:40,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 683266.0, 'new_value': 702860.0}, {'field': 'total_amount', 'old_value': 683266.0, 'new_value': 702860.0}, {'field': 'order_count', 'old_value': 908, 'new_value': 936}]
2025-05-01 09:00:40,072 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-05-01 09:00:40,462 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-05-01 09:00:40,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70455.14, 'new_value': 71541.03}, {'field': 'offline_amount', 'old_value': 77346.64, 'new_value': 77357.67}, {'field': 'total_amount', 'old_value': 147801.78, 'new_value': 148898.7}, {'field': 'order_count', 'old_value': 6497, 'new_value': 6570}]
2025-05-01 09:00:40,462 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-05-01 09:00:40,947 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-05-01 09:00:40,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134791.19, 'new_value': 140882.79}, {'field': 'total_amount', 'old_value': 134791.19, 'new_value': 140882.79}, {'field': 'order_count', 'old_value': 3925, 'new_value': 4091}]
2025-05-01 09:00:40,962 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-05-01 09:00:41,431 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-05-01 09:00:41,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 245298.08, 'new_value': 256606.73}, {'field': 'offline_amount', 'old_value': 592983.86, 'new_value': 620457.38}, {'field': 'total_amount', 'old_value': 838281.94, 'new_value': 877064.11}, {'field': 'order_count', 'old_value': 5047, 'new_value': 5276}]
2025-05-01 09:00:41,431 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-05-01 09:00:41,916 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-05-01 09:00:41,916 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57021.2, 'new_value': 59160.0}, {'field': 'total_amount', 'old_value': 57021.2, 'new_value': 59160.0}, {'field': 'order_count', 'old_value': 2641, 'new_value': 2744}]
2025-05-01 09:00:41,916 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-05-01 09:00:42,384 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-05-01 09:00:42,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26629.8, 'new_value': 28266.6}, {'field': 'offline_amount', 'old_value': 62662.6, 'new_value': 65222.6}, {'field': 'total_amount', 'old_value': 89292.4, 'new_value': 93489.2}, {'field': 'order_count', 'old_value': 105, 'new_value': 107}]
2025-05-01 09:00:42,384 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-05-01 09:00:42,853 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-05-01 09:00:42,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47447.93, 'new_value': 48924.66}, {'field': 'total_amount', 'old_value': 47637.93, 'new_value': 49114.66}, {'field': 'order_count', 'old_value': 451, 'new_value': 469}]
2025-05-01 09:00:42,853 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-05-01 09:00:43,306 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-05-01 09:00:43,306 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138883.0, 'new_value': 144106.0}, {'field': 'offline_amount', 'old_value': 115942.0, 'new_value': 120536.0}, {'field': 'total_amount', 'old_value': 254825.0, 'new_value': 264642.0}, {'field': 'order_count', 'old_value': 9543, 'new_value': 9675}]
2025-05-01 09:00:43,306 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-05-01 09:00:43,728 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-05-01 09:00:43,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 319241.5, 'new_value': 332809.2}, {'field': 'total_amount', 'old_value': 319241.5, 'new_value': 332809.2}, {'field': 'order_count', 'old_value': 9233, 'new_value': 9604}]
2025-05-01 09:00:43,728 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-05-01 09:00:44,197 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-05-01 09:00:44,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124012.97, 'new_value': 132320.77}, {'field': 'total_amount', 'old_value': 250114.17, 'new_value': 258421.97}, {'field': 'order_count', 'old_value': 6893, 'new_value': 7111}]
2025-05-01 09:00:44,197 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-05-01 09:00:44,650 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-05-01 09:00:44,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50705.1, 'new_value': 54593.6}, {'field': 'total_amount', 'old_value': 50705.1, 'new_value': 54593.6}, {'field': 'order_count', 'old_value': 69, 'new_value': 73}]
2025-05-01 09:00:44,650 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-05-01 09:00:45,244 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-05-01 09:00:45,244 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49933.09, 'new_value': 52047.6}, {'field': 'offline_amount', 'old_value': 57812.41, 'new_value': 60848.86}, {'field': 'total_amount', 'old_value': 107745.5, 'new_value': 112896.46}, {'field': 'order_count', 'old_value': 3832, 'new_value': 3917}]
2025-05-01 09:00:45,244 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-05-01 09:00:45,759 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-05-01 09:00:45,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89297.34, 'new_value': 91878.67}, {'field': 'offline_amount', 'old_value': 129930.93, 'new_value': 135715.1}, {'field': 'total_amount', 'old_value': 219228.27, 'new_value': 227593.77}, {'field': 'order_count', 'old_value': 8804, 'new_value': 9145}]
2025-05-01 09:00:45,759 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-05-01 09:00:46,259 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-05-01 09:00:46,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 417578.15, 'new_value': 435957.12}, {'field': 'total_amount', 'old_value': 455903.93, 'new_value': 474282.9}, {'field': 'order_count', 'old_value': 1867, 'new_value': 1930}]
2025-05-01 09:00:46,259 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-05-01 09:00:46,728 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-05-01 09:00:46,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 526466.83, 'new_value': 543051.21}, {'field': 'total_amount', 'old_value': 545530.33, 'new_value': 562114.71}, {'field': 'order_count', 'old_value': 2119, 'new_value': 2171}]
2025-05-01 09:00:46,728 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCH
2025-05-01 09:00:47,166 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCH
2025-05-01 09:00:47,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140613.5, 'new_value': 151413.5}, {'field': 'total_amount', 'old_value': 143013.5, 'new_value': 153813.5}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-01 09:00:47,166 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-05-01 09:00:47,666 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-05-01 09:00:47,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16989.6, 'new_value': 17424.6}, {'field': 'total_amount', 'old_value': 17311.6, 'new_value': 17746.6}, {'field': 'order_count', 'old_value': 174, 'new_value': 181}]
2025-05-01 09:00:47,666 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-05-01 09:00:48,119 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-05-01 09:00:48,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 586613.28, 'new_value': 596877.28}, {'field': 'total_amount', 'old_value': 592310.28, 'new_value': 602574.28}, {'field': 'order_count', 'old_value': 434, 'new_value': 463}]
2025-05-01 09:00:48,119 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-05-01 09:00:48,650 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-05-01 09:00:48,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107981.98, 'new_value': 115927.98}, {'field': 'total_amount', 'old_value': 110279.98, 'new_value': 118225.98}, {'field': 'order_count', 'old_value': 52, 'new_value': 56}]
2025-05-01 09:00:48,650 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-05-01 09:00:49,165 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-05-01 09:00:49,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 628144.0, 'new_value': 655685.0}, {'field': 'total_amount', 'old_value': 639182.0, 'new_value': 666723.0}, {'field': 'order_count', 'old_value': 544, 'new_value': 562}]
2025-05-01 09:00:49,165 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKI
2025-05-01 09:00:49,634 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKI
2025-05-01 09:00:49,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13494.0, 'new_value': 14690.0}, {'field': 'total_amount', 'old_value': 13494.0, 'new_value': 14690.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 41}]
2025-05-01 09:00:49,634 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-05-01 09:00:50,087 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-05-01 09:00:50,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119083.41, 'new_value': 121475.51}, {'field': 'total_amount', 'old_value': 130600.41, 'new_value': 132992.51}, {'field': 'order_count', 'old_value': 1013, 'new_value': 1050}]
2025-05-01 09:00:50,087 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-05-01 09:00:50,587 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-05-01 09:00:50,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146595.8, 'new_value': 153979.5}, {'field': 'total_amount', 'old_value': 169863.18, 'new_value': 177246.88}, {'field': 'order_count', 'old_value': 12927, 'new_value': 13327}]
2025-05-01 09:00:50,587 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-05-01 09:00:51,072 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-05-01 09:00:51,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132638.81, 'new_value': 138821.33}, {'field': 'total_amount', 'old_value': 132971.61, 'new_value': 139154.13}, {'field': 'order_count', 'old_value': 81, 'new_value': 84}]
2025-05-01 09:00:51,072 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-05-01 09:00:51,478 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-05-01 09:00:51,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30889.0, 'new_value': 31069.0}, {'field': 'total_amount', 'old_value': 30889.0, 'new_value': 31069.0}, {'field': 'order_count', 'old_value': 158, 'new_value': 159}]
2025-05-01 09:00:51,478 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-05-01 09:00:51,884 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-05-01 09:00:51,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86197.0, 'new_value': 86877.0}, {'field': 'offline_amount', 'old_value': 31697.0, 'new_value': 33517.0}, {'field': 'total_amount', 'old_value': 117894.0, 'new_value': 120394.0}, {'field': 'order_count', 'old_value': 154, 'new_value': 158}]
2025-05-01 09:00:51,884 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-05-01 09:00:52,337 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-05-01 09:00:52,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1101201.0, 'new_value': 1133251.0}, {'field': 'total_amount', 'old_value': 1101201.0, 'new_value': 1133251.0}, {'field': 'order_count', 'old_value': 4677, 'new_value': 4842}]
2025-05-01 09:00:52,337 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-05-01 09:00:52,681 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-05-01 09:00:52,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116058.0, 'new_value': 127189.0}, {'field': 'total_amount', 'old_value': 116058.0, 'new_value': 127189.0}, {'field': 'order_count', 'old_value': 316, 'new_value': 336}]
2025-05-01 09:00:52,681 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-05-01 09:00:53,103 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-05-01 09:00:53,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77145.2, 'new_value': 81115.1}, {'field': 'total_amount', 'old_value': 77145.2, 'new_value': 81115.1}, {'field': 'order_count', 'old_value': 2249, 'new_value': 2356}]
2025-05-01 09:00:53,103 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-05-01 09:00:53,634 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-05-01 09:00:53,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117966.7, 'new_value': 121483.3}, {'field': 'total_amount', 'old_value': 117966.7, 'new_value': 121483.3}, {'field': 'order_count', 'old_value': 576, 'new_value': 594}]
2025-05-01 09:00:53,634 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-05-01 09:00:54,040 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-05-01 09:00:54,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 7637.5}, {'field': 'total_amount', 'old_value': 194231.86, 'new_value': 201869.36}, {'field': 'order_count', 'old_value': 799, 'new_value': 835}]
2025-05-01 09:00:54,040 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-05-01 09:00:54,494 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-05-01 09:00:54,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45152.92, 'new_value': 49335.92}, {'field': 'total_amount', 'old_value': 77789.3, 'new_value': 81972.3}, {'field': 'order_count', 'old_value': 2859, 'new_value': 3013}]
2025-05-01 09:00:54,494 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-05-01 09:00:55,072 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-05-01 09:00:55,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194656.4, 'new_value': 202412.9}, {'field': 'total_amount', 'old_value': 194656.4, 'new_value': 202412.9}, {'field': 'order_count', 'old_value': 929, 'new_value': 970}]
2025-05-01 09:00:55,072 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-05-01 09:00:55,540 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-05-01 09:00:55,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184347.0, 'new_value': 193066.0}, {'field': 'total_amount', 'old_value': 184347.0, 'new_value': 193066.0}, {'field': 'order_count', 'old_value': 13249, 'new_value': 13814}]
2025-05-01 09:00:55,540 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-05-01 09:00:56,009 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-05-01 09:00:56,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51389.25, 'new_value': 53946.22}, {'field': 'offline_amount', 'old_value': 50494.45, 'new_value': 51946.25}, {'field': 'total_amount', 'old_value': 101883.7, 'new_value': 105892.47}, {'field': 'order_count', 'old_value': 5366, 'new_value': 5578}]
2025-05-01 09:00:56,009 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-05-01 09:00:56,494 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-05-01 09:00:56,494 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12318.46, 'new_value': 12783.8}, {'field': 'offline_amount', 'old_value': 169868.0, 'new_value': 180969.0}, {'field': 'total_amount', 'old_value': 182186.46, 'new_value': 193752.8}, {'field': 'order_count', 'old_value': 89, 'new_value': 93}]
2025-05-01 09:00:56,494 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-05-01 09:00:56,962 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-05-01 09:00:56,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146070.0, 'new_value': 149941.0}, {'field': 'total_amount', 'old_value': 172501.0, 'new_value': 176372.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 66}]
2025-05-01 09:00:56,962 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-05-01 09:00:57,478 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-05-01 09:00:57,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34597.62, 'new_value': 35850.82}, {'field': 'total_amount', 'old_value': 34597.62, 'new_value': 35850.82}, {'field': 'order_count', 'old_value': 1070, 'new_value': 1125}]
2025-05-01 09:00:57,478 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-05-01 09:00:57,915 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-05-01 09:00:57,915 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51538.0, 'new_value': 53544.7}, {'field': 'total_amount', 'old_value': 101713.12, 'new_value': 103719.82}, {'field': 'order_count', 'old_value': 364, 'new_value': 375}]
2025-05-01 09:00:57,915 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-05-01 09:00:58,447 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-05-01 09:00:58,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93889.39, 'new_value': 99125.47}, {'field': 'total_amount', 'old_value': 96361.7, 'new_value': 101597.78}, {'field': 'order_count', 'old_value': 512, 'new_value': 539}]
2025-05-01 09:00:58,447 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR
2025-05-01 09:00:58,884 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR
2025-05-01 09:00:58,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51962.0, 'new_value': 60602.0}, {'field': 'total_amount', 'old_value': 51962.0, 'new_value': 60602.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 58}]
2025-05-01 09:00:58,884 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-05-01 09:00:59,384 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-05-01 09:00:59,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 123315.84, 'new_value': 127321.35}, {'field': 'offline_amount', 'old_value': 140869.09, 'new_value': 144772.3}, {'field': 'total_amount', 'old_value': 264184.93, 'new_value': 272093.65}, {'field': 'order_count', 'old_value': 6793, 'new_value': 6989}]
2025-05-01 09:00:59,384 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-05-01 09:00:59,853 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-05-01 09:00:59,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42891.01, 'new_value': 45472.66}, {'field': 'offline_amount', 'old_value': 103477.0, 'new_value': 109332.0}, {'field': 'total_amount', 'old_value': 146368.01, 'new_value': 154804.66}, {'field': 'order_count', 'old_value': 1831, 'new_value': 1917}]
2025-05-01 09:00:59,853 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-05-01 09:01:00,415 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-05-01 09:01:00,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2149.38, 'new_value': 2606.38}, {'field': 'offline_amount', 'old_value': 72295.25, 'new_value': 76215.76}, {'field': 'total_amount', 'old_value': 74444.63, 'new_value': 78822.14}, {'field': 'order_count', 'old_value': 672, 'new_value': 704}]
2025-05-01 09:01:00,415 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-05-01 09:01:00,947 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-05-01 09:01:00,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12009.0, 'new_value': 12067.0}, {'field': 'offline_amount', 'old_value': 88641.0, 'new_value': 92588.0}, {'field': 'total_amount', 'old_value': 100650.0, 'new_value': 104655.0}, {'field': 'order_count', 'old_value': 744, 'new_value': 767}]
2025-05-01 09:01:00,947 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-05-01 09:01:01,478 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-05-01 09:01:01,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263205.3, 'new_value': 272506.62}, {'field': 'total_amount', 'old_value': 263205.3, 'new_value': 272506.62}, {'field': 'order_count', 'old_value': 1200, 'new_value': 1246}]
2025-05-01 09:01:01,478 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-05-01 09:01:01,915 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-05-01 09:01:01,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84799.0, 'new_value': 95339.0}, {'field': 'total_amount', 'old_value': 239626.0, 'new_value': 250166.0}, {'field': 'order_count', 'old_value': 544, 'new_value': 574}]
2025-05-01 09:01:01,915 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-05-01 09:01:02,337 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-05-01 09:01:02,337 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176922.0, 'new_value': 182442.0}, {'field': 'offline_amount', 'old_value': 246323.0, 'new_value': 263845.0}, {'field': 'total_amount', 'old_value': 423245.0, 'new_value': 446287.0}, {'field': 'order_count', 'old_value': 985, 'new_value': 1029}]
2025-05-01 09:01:02,337 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-05-01 09:01:02,759 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-05-01 09:01:02,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 226361.14, 'new_value': 233458.04}, {'field': 'offline_amount', 'old_value': 46208.26, 'new_value': 47240.44}, {'field': 'total_amount', 'old_value': 272569.4, 'new_value': 280698.48}, {'field': 'order_count', 'old_value': 14622, 'new_value': 15062}]
2025-05-01 09:01:02,759 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-05-01 09:01:03,197 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-05-01 09:01:03,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227346.0, 'new_value': 244331.0}, {'field': 'total_amount', 'old_value': 243220.0, 'new_value': 260205.0}, {'field': 'order_count', 'old_value': 345, 'new_value': 365}]
2025-05-01 09:01:03,197 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-05-01 09:01:03,681 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-05-01 09:01:03,681 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12994.0, 'new_value': 14419.4}, {'field': 'offline_amount', 'old_value': 194066.0, 'new_value': 225556.0}, {'field': 'total_amount', 'old_value': 207060.0, 'new_value': 239975.4}, {'field': 'order_count', 'old_value': 337, 'new_value': 362}]
2025-05-01 09:01:03,681 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-05-01 09:01:04,337 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-05-01 09:01:04,337 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59390.41, 'new_value': 61399.64}, {'field': 'offline_amount', 'old_value': 73774.47, 'new_value': 75896.07}, {'field': 'total_amount', 'old_value': 133164.88, 'new_value': 137295.71}, {'field': 'order_count', 'old_value': 6747, 'new_value': 6966}]
2025-05-01 09:01:04,337 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-05-01 09:01:04,759 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-05-01 09:01:04,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26771.9, 'new_value': 28441.18}, {'field': 'total_amount', 'old_value': 26771.9, 'new_value': 28441.18}, {'field': 'order_count', 'old_value': 140, 'new_value': 147}]
2025-05-01 09:01:04,759 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-05-01 09:01:05,197 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-05-01 09:01:05,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240313.48, 'new_value': 248506.01}, {'field': 'total_amount', 'old_value': 245314.49, 'new_value': 253507.02}, {'field': 'order_count', 'old_value': 4233, 'new_value': 4403}]
2025-05-01 09:01:05,197 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-05-01 09:01:05,697 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-05-01 09:01:05,697 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13442.17, 'new_value': 13571.97}, {'field': 'offline_amount', 'old_value': 34328.0, 'new_value': 34526.0}, {'field': 'total_amount', 'old_value': 47770.17, 'new_value': 48097.97}, {'field': 'order_count', 'old_value': 285, 'new_value': 289}]
2025-05-01 09:01:05,697 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-05-01 09:01:06,197 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-05-01 09:01:06,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103247.0, 'new_value': 106740.0}, {'field': 'total_amount', 'old_value': 103247.0, 'new_value': 106740.0}, {'field': 'order_count', 'old_value': 7119, 'new_value': 7384}]
2025-05-01 09:01:06,197 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-05-01 09:01:06,697 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-05-01 09:01:06,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132797.6, 'new_value': 137342.51}, {'field': 'total_amount', 'old_value': 132797.6, 'new_value': 137342.51}, {'field': 'order_count', 'old_value': 9447, 'new_value': 9758}]
2025-05-01 09:01:06,697 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-05-01 09:01:07,134 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-05-01 09:01:07,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 705465.03, 'new_value': 717166.71}, {'field': 'total_amount', 'old_value': 705465.03, 'new_value': 717166.71}, {'field': 'order_count', 'old_value': 3891, 'new_value': 3993}]
2025-05-01 09:01:07,134 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-05-01 09:01:07,619 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-05-01 09:01:07,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376739.0, 'new_value': 389745.0}, {'field': 'total_amount', 'old_value': 376739.0, 'new_value': 389745.0}, {'field': 'order_count', 'old_value': 8405, 'new_value': 8727}]
2025-05-01 09:01:07,619 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-05-01 09:01:08,087 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-05-01 09:01:08,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 319969.93, 'new_value': 331018.94}, {'field': 'total_amount', 'old_value': 319969.93, 'new_value': 331018.94}, {'field': 'order_count', 'old_value': 928, 'new_value': 961}]
2025-05-01 09:01:08,087 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-05-01 09:01:08,572 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-05-01 09:01:08,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28921.7, 'new_value': 30136.0}, {'field': 'offline_amount', 'old_value': 80131.1, 'new_value': 82875.0}, {'field': 'total_amount', 'old_value': 109052.8, 'new_value': 113011.0}, {'field': 'order_count', 'old_value': 4577, 'new_value': 4733}]
2025-05-01 09:01:08,572 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-05-01 09:01:09,009 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-05-01 09:01:09,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11547.4, 'new_value': 12478.4}, {'field': 'offline_amount', 'old_value': 35480.83, 'new_value': 35948.83}, {'field': 'total_amount', 'old_value': 47028.23, 'new_value': 48427.23}, {'field': 'order_count', 'old_value': 480, 'new_value': 503}]
2025-05-01 09:01:09,009 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-05-01 09:01:09,462 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-05-01 09:01:09,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5061209.7, 'new_value': 5249526.7}, {'field': 'total_amount', 'old_value': 5061209.7, 'new_value': 5249526.7}, {'field': 'order_count', 'old_value': 147835, 'new_value': 148685}]
2025-05-01 09:01:09,462 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-05-01 09:01:09,884 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-05-01 09:01:09,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339948.0, 'new_value': 343442.0}, {'field': 'total_amount', 'old_value': 339948.0, 'new_value': 343442.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 110}]
2025-05-01 09:01:09,884 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-05-01 09:01:10,337 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-05-01 09:01:10,337 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111397.0, 'new_value': 114479.0}, {'field': 'offline_amount', 'old_value': 1313577.0, 'new_value': 1354895.0}, {'field': 'total_amount', 'old_value': 1424974.0, 'new_value': 1469374.0}, {'field': 'order_count', 'old_value': 33308, 'new_value': 34491}]
2025-05-01 09:01:10,337 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-05-01 09:01:10,759 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-05-01 09:01:10,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 373974.0, 'new_value': 388817.0}, {'field': 'total_amount', 'old_value': 385136.0, 'new_value': 399979.0}, {'field': 'order_count', 'old_value': 8650, 'new_value': 8979}]
2025-05-01 09:01:10,759 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-05-01 09:01:11,275 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-05-01 09:01:11,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147297.5, 'new_value': 153734.6}, {'field': 'total_amount', 'old_value': 147297.5, 'new_value': 153734.6}, {'field': 'order_count', 'old_value': 3500, 'new_value': 3636}]
2025-05-01 09:01:11,275 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-05-01 09:01:11,759 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-05-01 09:01:11,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52534.0, 'new_value': 57326.0}, {'field': 'total_amount', 'old_value': 52534.0, 'new_value': 57326.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 72}]
2025-05-01 09:01:11,775 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-05-01 09:01:12,197 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-05-01 09:01:12,197 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40596.16, 'new_value': 47157.54}, {'field': 'offline_amount', 'old_value': 359986.38, 'new_value': 369262.24}, {'field': 'total_amount', 'old_value': 400582.54, 'new_value': 416419.78}, {'field': 'order_count', 'old_value': 4653, 'new_value': 4827}]
2025-05-01 09:01:12,197 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-05-01 09:01:12,650 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-05-01 09:01:12,650 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 216293.23, 'new_value': 232049.29}, {'field': 'offline_amount', 'old_value': 180065.07, 'new_value': 188268.63}, {'field': 'total_amount', 'old_value': 396358.3, 'new_value': 420317.92}, {'field': 'order_count', 'old_value': 1329, 'new_value': 1404}]
2025-05-01 09:01:12,650 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-05-01 09:01:13,290 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-05-01 09:01:13,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225351.0, 'new_value': 301481.0}, {'field': 'total_amount', 'old_value': 225351.0, 'new_value': 301481.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 24}]
2025-05-01 09:01:13,306 - INFO - 日期 2025-04 处理完成 - 更新: 106 条，插入: 0 条，错误: 0 条
2025-05-01 09:01:13,306 - INFO - 数据同步完成！更新: 106 条，插入: 0 条，错误: 0 条
2025-05-01 09:01:13,306 - INFO - =================同步完成====================
2025-05-01 12:00:01,896 - INFO - =================使用默认全量同步=============
2025-05-01 12:00:03,021 - INFO - MySQL查询成功，共获取 2641 条记录
2025-05-01 12:00:03,021 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-01 12:00:03,052 - INFO - 开始处理日期: 2025-01
2025-05-01 12:00:03,052 - INFO - Request Parameters - Page 1:
2025-05-01 12:00:03,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:03,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:04,021 - INFO - Response - Page 1:
2025-05-01 12:00:04,224 - INFO - 第 1 页获取到 100 条记录
2025-05-01 12:00:04,224 - INFO - Request Parameters - Page 2:
2025-05-01 12:00:04,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:04,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:04,755 - INFO - Response - Page 2:
2025-05-01 12:00:04,958 - INFO - 第 2 页获取到 100 条记录
2025-05-01 12:00:04,958 - INFO - Request Parameters - Page 3:
2025-05-01 12:00:04,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:04,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:05,661 - INFO - Response - Page 3:
2025-05-01 12:00:05,864 - INFO - 第 3 页获取到 100 条记录
2025-05-01 12:00:05,864 - INFO - Request Parameters - Page 4:
2025-05-01 12:00:05,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:05,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:06,396 - INFO - Response - Page 4:
2025-05-01 12:00:06,599 - INFO - 第 4 页获取到 100 条记录
2025-05-01 12:00:06,599 - INFO - Request Parameters - Page 5:
2025-05-01 12:00:06,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:06,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:07,130 - INFO - Response - Page 5:
2025-05-01 12:00:07,333 - INFO - 第 5 页获取到 100 条记录
2025-05-01 12:00:07,333 - INFO - Request Parameters - Page 6:
2025-05-01 12:00:07,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:07,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:07,864 - INFO - Response - Page 6:
2025-05-01 12:00:08,067 - INFO - 第 6 页获取到 100 条记录
2025-05-01 12:00:08,067 - INFO - Request Parameters - Page 7:
2025-05-01 12:00:08,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:08,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:08,552 - INFO - Response - Page 7:
2025-05-01 12:00:08,755 - INFO - 第 7 页获取到 82 条记录
2025-05-01 12:00:08,755 - INFO - 查询完成，共获取到 682 条记录
2025-05-01 12:00:08,755 - INFO - 获取到 682 条表单数据
2025-05-01 12:00:08,755 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-01 12:00:08,771 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 12:00:08,771 - INFO - 开始处理日期: 2025-02
2025-05-01 12:00:08,771 - INFO - Request Parameters - Page 1:
2025-05-01 12:00:08,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:08,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:09,271 - INFO - Response - Page 1:
2025-05-01 12:00:09,474 - INFO - 第 1 页获取到 100 条记录
2025-05-01 12:00:09,474 - INFO - Request Parameters - Page 2:
2025-05-01 12:00:09,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:09,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:09,989 - INFO - Response - Page 2:
2025-05-01 12:00:10,192 - INFO - 第 2 页获取到 100 条记录
2025-05-01 12:00:10,192 - INFO - Request Parameters - Page 3:
2025-05-01 12:00:10,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:10,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:10,677 - INFO - Response - Page 3:
2025-05-01 12:00:10,880 - INFO - 第 3 页获取到 100 条记录
2025-05-01 12:00:10,880 - INFO - Request Parameters - Page 4:
2025-05-01 12:00:10,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:10,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:11,364 - INFO - Response - Page 4:
2025-05-01 12:00:11,567 - INFO - 第 4 页获取到 100 条记录
2025-05-01 12:00:11,567 - INFO - Request Parameters - Page 5:
2025-05-01 12:00:11,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:11,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:11,989 - INFO - Response - Page 5:
2025-05-01 12:00:12,192 - INFO - 第 5 页获取到 100 条记录
2025-05-01 12:00:12,192 - INFO - Request Parameters - Page 6:
2025-05-01 12:00:12,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:12,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:12,630 - INFO - Response - Page 6:
2025-05-01 12:00:12,833 - INFO - 第 6 页获取到 100 条记录
2025-05-01 12:00:12,833 - INFO - Request Parameters - Page 7:
2025-05-01 12:00:12,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:12,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:13,333 - INFO - Response - Page 7:
2025-05-01 12:00:13,536 - INFO - 第 7 页获取到 70 条记录
2025-05-01 12:00:13,536 - INFO - 查询完成，共获取到 670 条记录
2025-05-01 12:00:13,536 - INFO - 获取到 670 条表单数据
2025-05-01 12:00:13,536 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-01 12:00:13,552 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 12:00:13,552 - INFO - 开始处理日期: 2025-03
2025-05-01 12:00:13,552 - INFO - Request Parameters - Page 1:
2025-05-01 12:00:13,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:13,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:14,146 - INFO - Response - Page 1:
2025-05-01 12:00:14,349 - INFO - 第 1 页获取到 100 条记录
2025-05-01 12:00:14,349 - INFO - Request Parameters - Page 2:
2025-05-01 12:00:14,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:14,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:14,864 - INFO - Response - Page 2:
2025-05-01 12:00:15,067 - INFO - 第 2 页获取到 100 条记录
2025-05-01 12:00:15,067 - INFO - Request Parameters - Page 3:
2025-05-01 12:00:15,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:15,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:15,567 - INFO - Response - Page 3:
2025-05-01 12:00:15,771 - INFO - 第 3 页获取到 100 条记录
2025-05-01 12:00:15,771 - INFO - Request Parameters - Page 4:
2025-05-01 12:00:15,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:15,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:16,302 - INFO - Response - Page 4:
2025-05-01 12:00:16,505 - INFO - 第 4 页获取到 100 条记录
2025-05-01 12:00:16,505 - INFO - Request Parameters - Page 5:
2025-05-01 12:00:16,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:16,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:16,989 - INFO - Response - Page 5:
2025-05-01 12:00:17,192 - INFO - 第 5 页获取到 100 条记录
2025-05-01 12:00:17,192 - INFO - Request Parameters - Page 6:
2025-05-01 12:00:17,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:17,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:17,677 - INFO - Response - Page 6:
2025-05-01 12:00:17,880 - INFO - 第 6 页获取到 100 条记录
2025-05-01 12:00:17,880 - INFO - Request Parameters - Page 7:
2025-05-01 12:00:17,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:17,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:18,271 - INFO - Response - Page 7:
2025-05-01 12:00:18,474 - INFO - 第 7 页获取到 61 条记录
2025-05-01 12:00:18,474 - INFO - 查询完成，共获取到 661 条记录
2025-05-01 12:00:18,474 - INFO - 获取到 661 条表单数据
2025-05-01 12:00:18,474 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-01 12:00:18,489 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 12:00:18,489 - INFO - 开始处理日期: 2025-04
2025-05-01 12:00:18,489 - INFO - Request Parameters - Page 1:
2025-05-01 12:00:18,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:18,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:19,646 - INFO - Response - Page 1:
2025-05-01 12:00:19,849 - INFO - 第 1 页获取到 100 条记录
2025-05-01 12:00:19,849 - INFO - Request Parameters - Page 2:
2025-05-01 12:00:19,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:19,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:20,396 - INFO - Response - Page 2:
2025-05-01 12:00:20,599 - INFO - 第 2 页获取到 100 条记录
2025-05-01 12:00:20,599 - INFO - Request Parameters - Page 3:
2025-05-01 12:00:20,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:20,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:21,099 - INFO - Response - Page 3:
2025-05-01 12:00:21,302 - INFO - 第 3 页获取到 100 条记录
2025-05-01 12:00:21,302 - INFO - Request Parameters - Page 4:
2025-05-01 12:00:21,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:21,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:21,786 - INFO - Response - Page 4:
2025-05-01 12:00:21,989 - INFO - 第 4 页获取到 100 条记录
2025-05-01 12:00:21,989 - INFO - Request Parameters - Page 5:
2025-05-01 12:00:21,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:21,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:22,505 - INFO - Response - Page 5:
2025-05-01 12:00:22,708 - INFO - 第 5 页获取到 100 条记录
2025-05-01 12:00:22,708 - INFO - Request Parameters - Page 6:
2025-05-01 12:00:22,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:22,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:23,208 - INFO - Response - Page 6:
2025-05-01 12:00:23,411 - INFO - 第 6 页获取到 100 条记录
2025-05-01 12:00:23,411 - INFO - Request Parameters - Page 7:
2025-05-01 12:00:23,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:00:23,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:00:23,724 - INFO - Response - Page 7:
2025-05-01 12:00:23,927 - INFO - 第 7 页获取到 27 条记录
2025-05-01 12:00:23,927 - INFO - 查询完成，共获取到 627 条记录
2025-05-01 12:00:23,927 - INFO - 获取到 627 条表单数据
2025-05-01 12:00:23,927 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-01 12:00:23,927 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-05-01 12:00:24,411 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-05-01 12:00:24,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153546.27, 'new_value': 159520.5}, {'field': 'total_amount', 'old_value': 153546.27, 'new_value': 159520.5}, {'field': 'order_count', 'old_value': 803, 'new_value': 831}]
2025-05-01 12:00:24,411 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M53
2025-05-01 12:00:24,880 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M53
2025-05-01 12:00:24,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16417.0, 'new_value': 17253.0}, {'field': 'total_amount', 'old_value': 16417.0, 'new_value': 17253.0}, {'field': 'order_count', 'old_value': 3145, 'new_value': 4131}]
2025-05-01 12:00:24,880 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-05-01 12:00:25,317 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-05-01 12:00:25,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33920.71, 'new_value': 35184.41}, {'field': 'offline_amount', 'old_value': 16836.98, 'new_value': 17570.68}, {'field': 'total_amount', 'old_value': 50757.69, 'new_value': 52755.09}, {'field': 'order_count', 'old_value': 2267, 'new_value': 2362}]
2025-05-01 12:00:25,317 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-05-01 12:00:25,817 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-05-01 12:00:25,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 403636.0, 'new_value': 421505.0}, {'field': 'offline_amount', 'old_value': 692994.0, 'new_value': 713622.0}, {'field': 'total_amount', 'old_value': 1096630.0, 'new_value': 1135127.0}, {'field': 'order_count', 'old_value': 1054, 'new_value': 1087}]
2025-05-01 12:00:25,817 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-05-01 12:00:26,317 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-05-01 12:00:26,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314275.0, 'new_value': 317275.0}, {'field': 'total_amount', 'old_value': 314275.0, 'new_value': 317275.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 108}]
2025-05-01 12:00:26,317 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MH3
2025-05-01 12:00:26,802 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MH3
2025-05-01 12:00:26,802 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76203.1, 'new_value': 90003.1}, {'field': 'total_amount', 'old_value': 76203.1, 'new_value': 90003.1}, {'field': 'order_count', 'old_value': 30, 'new_value': 40}]
2025-05-01 12:00:26,802 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MI3
2025-05-01 12:00:27,255 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MI3
2025-05-01 12:00:27,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247336.0, 'new_value': 262336.0}, {'field': 'total_amount', 'old_value': 247336.0, 'new_value': 262336.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-01 12:00:27,255 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-05-01 12:00:27,724 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-05-01 12:00:27,724 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21962.9, 'new_value': 22923.9}, {'field': 'offline_amount', 'old_value': 31803.35, 'new_value': 32836.35}, {'field': 'total_amount', 'old_value': 53766.25, 'new_value': 55760.25}, {'field': 'order_count', 'old_value': 2584, 'new_value': 2679}]
2025-05-01 12:00:27,724 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-05-01 12:00:28,333 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-05-01 12:00:28,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109192.0, 'new_value': 117471.0}, {'field': 'total_amount', 'old_value': 109192.0, 'new_value': 117471.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 137}]
2025-05-01 12:00:28,333 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-05-01 12:00:28,833 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-05-01 12:00:28,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 584136.0, 'new_value': 590886.0}, {'field': 'total_amount', 'old_value': 670329.0, 'new_value': 677079.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 116}]
2025-05-01 12:00:28,833 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR3
2025-05-01 12:00:29,380 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR3
2025-05-01 12:00:29,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10534.0, 'new_value': 10949.0}, {'field': 'total_amount', 'old_value': 11124.0, 'new_value': 11539.0}, {'field': 'order_count', 'old_value': 217, 'new_value': 228}]
2025-05-01 12:00:29,380 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-05-01 12:00:29,911 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-05-01 12:00:29,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119037.0, 'new_value': 122566.0}, {'field': 'total_amount', 'old_value': 119037.0, 'new_value': 122566.0}, {'field': 'order_count', 'old_value': 6559, 'new_value': 6767}]
2025-05-01 12:00:29,927 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-05-01 12:00:30,349 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-05-01 12:00:30,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69209.72, 'new_value': 71714.6}, {'field': 'offline_amount', 'old_value': 143240.35, 'new_value': 147143.35}, {'field': 'total_amount', 'old_value': 212450.07, 'new_value': 218857.95}, {'field': 'order_count', 'old_value': 2338, 'new_value': 2407}]
2025-05-01 12:00:30,349 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-05-01 12:00:30,802 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-05-01 12:00:30,802 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14634.66, 'new_value': 15229.56}, {'field': 'offline_amount', 'old_value': 54946.31, 'new_value': 56902.31}, {'field': 'total_amount', 'old_value': 69580.97, 'new_value': 72131.87}, {'field': 'order_count', 'old_value': 1334, 'new_value': 1384}]
2025-05-01 12:00:30,802 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX3
2025-05-01 12:00:31,270 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX3
2025-05-01 12:00:31,270 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2480.0, 'new_value': 5680.0}, {'field': 'offline_amount', 'old_value': 42639.8, 'new_value': 50019.8}, {'field': 'total_amount', 'old_value': 45119.8, 'new_value': 55699.8}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-01 12:00:31,270 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-05-01 12:00:31,739 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-05-01 12:00:31,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65085.0, 'new_value': 65555.0}, {'field': 'offline_amount', 'old_value': 122451.85, 'new_value': 125012.85}, {'field': 'total_amount', 'old_value': 187536.85, 'new_value': 190567.85}, {'field': 'order_count', 'old_value': 259, 'new_value': 268}]
2025-05-01 12:00:31,739 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-05-01 12:00:32,224 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-05-01 12:00:32,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83133.0, 'new_value': 91133.0}, {'field': 'total_amount', 'old_value': 149308.0, 'new_value': 157308.0}, {'field': 'order_count', 'old_value': 2157, 'new_value': 2202}]
2025-05-01 12:00:32,224 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M34
2025-05-01 12:00:32,630 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M34
2025-05-01 12:00:32,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23875.82, 'new_value': 29679.82}, {'field': 'total_amount', 'old_value': 23931.62, 'new_value': 29735.62}, {'field': 'order_count', 'old_value': 35, 'new_value': 39}]
2025-05-01 12:00:32,630 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M44
2025-05-01 12:00:33,114 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M44
2025-05-01 12:00:33,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19243.6, 'new_value': 20951.0}, {'field': 'total_amount', 'old_value': 19344.6, 'new_value': 21052.0}, {'field': 'order_count', 'old_value': 1546, 'new_value': 1684}]
2025-05-01 12:00:33,114 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-05-01 12:00:33,552 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-05-01 12:00:33,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73426.0, 'new_value': 76078.0}, {'field': 'total_amount', 'old_value': 73426.0, 'new_value': 76078.0}, {'field': 'order_count', 'old_value': 1347, 'new_value': 1397}]
2025-05-01 12:00:33,552 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M74
2025-05-01 12:00:33,958 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M74
2025-05-01 12:00:33,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63771.06, 'new_value': 66720.81}, {'field': 'total_amount', 'old_value': 63771.06, 'new_value': 66720.81}, {'field': 'order_count', 'old_value': 3659, 'new_value': 3832}]
2025-05-01 12:00:33,958 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M94
2025-05-01 12:00:34,427 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M94
2025-05-01 12:00:34,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37037.39, 'new_value': 39184.03}, {'field': 'offline_amount', 'old_value': 126230.92, 'new_value': 136827.45}, {'field': 'total_amount', 'old_value': 163268.31, 'new_value': 176011.48}, {'field': 'order_count', 'old_value': 2228, 'new_value': 2400}]
2025-05-01 12:00:34,427 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-05-01 12:00:34,895 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-05-01 12:00:34,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91520.0, 'new_value': 91818.0}, {'field': 'total_amount', 'old_value': 91520.0, 'new_value': 91818.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-01 12:00:34,895 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC4
2025-05-01 12:00:35,442 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC4
2025-05-01 12:00:35,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90397.29, 'new_value': 97472.0}, {'field': 'total_amount', 'old_value': 90397.29, 'new_value': 97472.0}, {'field': 'order_count', 'old_value': 4717, 'new_value': 5086}]
2025-05-01 12:00:35,442 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-05-01 12:00:35,942 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-05-01 12:00:35,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166188.0, 'new_value': 171487.0}, {'field': 'offline_amount', 'old_value': 58279.67, 'new_value': 60960.57}, {'field': 'total_amount', 'old_value': 224467.67, 'new_value': 232447.57}, {'field': 'order_count', 'old_value': 1516, 'new_value': 1565}]
2025-05-01 12:00:35,942 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-05-01 12:00:36,411 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-05-01 12:00:36,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7130.7, 'new_value': 7298.7}, {'field': 'offline_amount', 'old_value': 48594.1, 'new_value': 49701.1}, {'field': 'total_amount', 'old_value': 55724.8, 'new_value': 56999.8}, {'field': 'order_count', 'old_value': 568, 'new_value': 583}]
2025-05-01 12:00:36,411 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-05-01 12:00:36,833 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-05-01 12:00:36,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42173.82, 'new_value': 43842.62}, {'field': 'total_amount', 'old_value': 42173.82, 'new_value': 43842.62}, {'field': 'order_count', 'old_value': 1543, 'new_value': 1605}]
2025-05-01 12:00:36,833 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-05-01 12:00:37,333 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-05-01 12:00:37,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13297.54, 'new_value': 13792.5}, {'field': 'offline_amount', 'old_value': 202343.59, 'new_value': 208594.57}, {'field': 'total_amount', 'old_value': 215641.13, 'new_value': 222387.07}, {'field': 'order_count', 'old_value': 2319, 'new_value': 2408}]
2025-05-01 12:00:37,333 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-05-01 12:00:37,880 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-05-01 12:00:37,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 844452.1, 'new_value': 865212.0}, {'field': 'total_amount', 'old_value': 844452.1, 'new_value': 865212.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 103}]
2025-05-01 12:00:37,880 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP4
2025-05-01 12:00:38,349 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP4
2025-05-01 12:00:38,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138801.0, 'new_value': 142038.0}, {'field': 'total_amount', 'old_value': 272038.0, 'new_value': 275275.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 76}]
2025-05-01 12:00:38,349 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-05-01 12:00:38,786 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-05-01 12:00:38,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93115.53, 'new_value': 97726.48}, {'field': 'offline_amount', 'old_value': 892544.07, 'new_value': 937975.33}, {'field': 'total_amount', 'old_value': 985659.6, 'new_value': 1035701.81}, {'field': 'order_count', 'old_value': 4310, 'new_value': 4442}]
2025-05-01 12:00:38,802 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-05-01 12:00:39,255 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-05-01 12:00:39,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 197164.1, 'new_value': 201506.1}, {'field': 'offline_amount', 'old_value': 73627.36, 'new_value': 77163.07}, {'field': 'total_amount', 'old_value': 270791.46, 'new_value': 278669.17}, {'field': 'order_count', 'old_value': 1967, 'new_value': 2033}]
2025-05-01 12:00:39,255 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU4
2025-05-01 12:00:39,739 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU4
2025-05-01 12:00:39,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49444.3, 'new_value': 50928.0}, {'field': 'total_amount', 'old_value': 49524.1, 'new_value': 51007.8}, {'field': 'order_count', 'old_value': 300, 'new_value': 308}]
2025-05-01 12:00:39,739 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-05-01 12:00:40,224 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-05-01 12:00:40,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 708686.06, 'new_value': 736361.52}, {'field': 'total_amount', 'old_value': 708686.06, 'new_value': 736361.52}, {'field': 'order_count', 'old_value': 5123, 'new_value': 5360}]
2025-05-01 12:00:40,224 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY4
2025-05-01 12:00:40,817 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY4
2025-05-01 12:00:40,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34997.64, 'new_value': 37563.64}, {'field': 'total_amount', 'old_value': 34997.64, 'new_value': 37563.64}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-05-01 12:00:40,817 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M05
2025-05-01 12:00:41,317 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M05
2025-05-01 12:00:41,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57828.0, 'new_value': 60005.0}, {'field': 'total_amount', 'old_value': 57828.0, 'new_value': 60005.0}, {'field': 'order_count', 'old_value': 915, 'new_value': 963}]
2025-05-01 12:00:41,333 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M25
2025-05-01 12:00:41,958 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M25
2025-05-01 12:00:41,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285927.0, 'new_value': 287204.0}, {'field': 'total_amount', 'old_value': 304778.0, 'new_value': 306055.0}, {'field': 'order_count', 'old_value': 261, 'new_value': 267}]
2025-05-01 12:00:41,958 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M35
2025-05-01 12:00:42,427 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M35
2025-05-01 12:00:42,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104475.0, 'new_value': 112475.0}, {'field': 'total_amount', 'old_value': 104475.0, 'new_value': 112475.0}, {'field': 'order_count', 'old_value': 6088, 'new_value': 6403}]
2025-05-01 12:00:42,427 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M45
2025-05-01 12:00:42,895 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M45
2025-05-01 12:00:42,895 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3897.0, 'new_value': 4313.0}, {'field': 'offline_amount', 'old_value': 49988.0, 'new_value': 52187.0}, {'field': 'total_amount', 'old_value': 53885.0, 'new_value': 56500.0}, {'field': 'order_count', 'old_value': 610, 'new_value': 646}]
2025-05-01 12:00:42,895 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-05-01 12:00:43,333 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-05-01 12:00:43,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64262.27, 'new_value': 66656.27}, {'field': 'offline_amount', 'old_value': 658850.97, 'new_value': 684446.2}, {'field': 'total_amount', 'old_value': 723113.24, 'new_value': 751102.47}, {'field': 'order_count', 'old_value': 2349, 'new_value': 2436}]
2025-05-01 12:00:43,333 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M85
2025-05-01 12:00:43,770 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M85
2025-05-01 12:00:43,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45276.0, 'new_value': 46875.0}, {'field': 'total_amount', 'old_value': 92756.0, 'new_value': 94355.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-01 12:00:43,770 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-05-01 12:00:44,239 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-05-01 12:00:44,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230643.02, 'new_value': 240284.89}, {'field': 'total_amount', 'old_value': 230643.02, 'new_value': 240284.89}, {'field': 'order_count', 'old_value': 1190, 'new_value': 1242}]
2025-05-01 12:00:44,239 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-05-01 12:00:44,724 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-05-01 12:00:44,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157928.89, 'new_value': 166223.3}, {'field': 'total_amount', 'old_value': 157928.89, 'new_value': 166223.3}, {'field': 'order_count', 'old_value': 1830, 'new_value': 1910}]
2025-05-01 12:00:44,724 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-05-01 12:00:45,161 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-05-01 12:00:45,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225471.6, 'new_value': 228632.5}, {'field': 'total_amount', 'old_value': 225471.6, 'new_value': 228632.5}, {'field': 'order_count', 'old_value': 2827, 'new_value': 2913}]
2025-05-01 12:00:45,161 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-05-01 12:00:45,645 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-05-01 12:00:45,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48114.0, 'new_value': 50204.0}, {'field': 'total_amount', 'old_value': 52121.0, 'new_value': 54211.0}, {'field': 'order_count', 'old_value': 247, 'new_value': 259}]
2025-05-01 12:00:45,645 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-05-01 12:00:46,083 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-05-01 12:00:46,083 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76822.23, 'new_value': 80632.88}, {'field': 'offline_amount', 'old_value': 54453.29, 'new_value': 56409.49}, {'field': 'total_amount', 'old_value': 131275.52, 'new_value': 137042.37}, {'field': 'order_count', 'old_value': 4487, 'new_value': 4679}]
2025-05-01 12:00:46,083 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-05-01 12:00:46,505 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-05-01 12:00:46,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155182.78, 'new_value': 161189.08}, {'field': 'total_amount', 'old_value': 155182.78, 'new_value': 161189.08}, {'field': 'order_count', 'old_value': 285, 'new_value': 293}]
2025-05-01 12:00:46,505 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML5
2025-05-01 12:00:46,895 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML5
2025-05-01 12:00:46,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153384.98, 'new_value': 163205.4}, {'field': 'total_amount', 'old_value': 163228.27, 'new_value': 173048.69}, {'field': 'order_count', 'old_value': 748, 'new_value': 803}]
2025-05-01 12:00:46,895 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM5
2025-05-01 12:00:47,411 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM5
2025-05-01 12:00:47,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47080.7, 'new_value': 50967.7}, {'field': 'total_amount', 'old_value': 47080.7, 'new_value': 50967.7}, {'field': 'order_count', 'old_value': 242, 'new_value': 259}]
2025-05-01 12:00:47,411 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-05-01 12:00:47,833 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-05-01 12:00:47,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 241773.0, 'new_value': 248469.0}, {'field': 'total_amount', 'old_value': 254274.0, 'new_value': 260970.0}, {'field': 'order_count', 'old_value': 1357, 'new_value': 1428}]
2025-05-01 12:00:47,833 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MA9
2025-05-01 12:00:48,302 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MA9
2025-05-01 12:00:48,302 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64965.0, 'new_value': 70891.0}, {'field': 'offline_amount', 'old_value': 206017.45, 'new_value': 218090.45}, {'field': 'total_amount', 'old_value': 270982.45, 'new_value': 288981.45}, {'field': 'order_count', 'old_value': 1891, 'new_value': 2028}]
2025-05-01 12:00:48,302 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-05-01 12:00:48,645 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-05-01 12:00:48,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27136.27, 'new_value': 28932.71}, {'field': 'offline_amount', 'old_value': 44925.82, 'new_value': 46307.06}, {'field': 'total_amount', 'old_value': 72062.09, 'new_value': 75239.77}, {'field': 'order_count', 'old_value': 2935, 'new_value': 3081}]
2025-05-01 12:00:48,645 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9ME9
2025-05-01 12:00:49,083 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9ME9
2025-05-01 12:00:49,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56242.0, 'new_value': 56585.0}, {'field': 'total_amount', 'old_value': 56242.0, 'new_value': 56585.0}, {'field': 'order_count', 'old_value': 942, 'new_value': 945}]
2025-05-01 12:00:49,083 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MH9
2025-05-01 12:00:49,552 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MH9
2025-05-01 12:00:49,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27706.96, 'new_value': 29361.56}, {'field': 'total_amount', 'old_value': 27706.96, 'new_value': 29361.56}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-05-01 12:00:49,552 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-05-01 12:00:49,989 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-05-01 12:00:49,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225268.06, 'new_value': 236129.56}, {'field': 'total_amount', 'old_value': 225268.06, 'new_value': 236129.56}, {'field': 'order_count', 'old_value': 347, 'new_value': 366}]
2025-05-01 12:00:49,989 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-05-01 12:00:50,489 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-05-01 12:00:50,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153319.37, 'new_value': 159401.57}, {'field': 'offline_amount', 'old_value': 144636.31, 'new_value': 151582.97}, {'field': 'total_amount', 'old_value': 297955.68, 'new_value': 310984.54}, {'field': 'order_count', 'old_value': 940, 'new_value': 973}]
2025-05-01 12:00:50,489 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-05-01 12:00:50,927 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-05-01 12:00:50,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46719.0, 'new_value': 57306.0}, {'field': 'total_amount', 'old_value': 46719.0, 'new_value': 57306.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 167}]
2025-05-01 12:00:50,927 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MO9
2025-05-01 12:00:51,473 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MO9
2025-05-01 12:00:51,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106156.24, 'new_value': 106948.94}, {'field': 'total_amount', 'old_value': 106156.24, 'new_value': 106948.94}, {'field': 'order_count', 'old_value': 195, 'new_value': 198}]
2025-05-01 12:00:51,473 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-05-01 12:00:51,958 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-05-01 12:00:51,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 423411.22, 'new_value': 435542.02}, {'field': 'offline_amount', 'old_value': 1463.0, 'new_value': 1475.0}, {'field': 'total_amount', 'old_value': 424874.22, 'new_value': 437017.02}, {'field': 'order_count', 'old_value': 5120, 'new_value': 5269}]
2025-05-01 12:00:51,958 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-05-01 12:00:52,411 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-05-01 12:00:52,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25541.59, 'new_value': 25826.59}, {'field': 'total_amount', 'old_value': 25541.59, 'new_value': 25826.59}, {'field': 'order_count', 'old_value': 264, 'new_value': 549}]
2025-05-01 12:00:52,411 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-05-01 12:00:52,848 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-05-01 12:00:52,848 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 183342.3, 'new_value': 190441.6}, {'field': 'offline_amount', 'old_value': 729311.55, 'new_value': 743137.43}, {'field': 'total_amount', 'old_value': 912653.85, 'new_value': 933579.03}, {'field': 'order_count', 'old_value': 2086, 'new_value': 2160}]
2025-05-01 12:00:52,848 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-05-01 12:00:53,270 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-05-01 12:00:53,270 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21270.03, 'new_value': 21578.68}, {'field': 'offline_amount', 'old_value': 396503.1, 'new_value': 410942.9}, {'field': 'total_amount', 'old_value': 417773.13, 'new_value': 432521.58}, {'field': 'order_count', 'old_value': 3000, 'new_value': 3110}]
2025-05-01 12:00:53,270 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-05-01 12:00:53,739 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-05-01 12:00:53,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 152982.52, 'new_value': 157296.05}, {'field': 'offline_amount', 'old_value': 33339.12, 'new_value': 34126.02}, {'field': 'total_amount', 'old_value': 186321.64, 'new_value': 191422.07}, {'field': 'order_count', 'old_value': 762, 'new_value': 781}]
2025-05-01 12:00:53,739 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-05-01 12:00:54,192 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-05-01 12:00:54,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25435.0, 'new_value': 28243.0}, {'field': 'total_amount', 'old_value': 25435.0, 'new_value': 28243.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 78}]
2025-05-01 12:00:54,192 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMA
2025-05-01 12:00:54,723 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMA
2025-05-01 12:00:54,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120402.0, 'new_value': 140292.0}, {'field': 'offline_amount', 'old_value': 62253.0, 'new_value': 63495.0}, {'field': 'total_amount', 'old_value': 182655.0, 'new_value': 203787.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 83}]
2025-05-01 12:00:54,723 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-05-01 12:00:55,177 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-05-01 12:00:55,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272429.72, 'new_value': 283097.04}, {'field': 'total_amount', 'old_value': 272462.72, 'new_value': 283130.04}, {'field': 'order_count', 'old_value': 2067, 'new_value': 2157}]
2025-05-01 12:00:55,177 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-05-01 12:00:55,739 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-05-01 12:00:55,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132159.49, 'new_value': 139616.07}, {'field': 'offline_amount', 'old_value': 295785.13, 'new_value': 302752.15}, {'field': 'total_amount', 'old_value': 427944.62, 'new_value': 442368.22}, {'field': 'order_count', 'old_value': 4003, 'new_value': 4160}]
2025-05-01 12:00:55,739 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9B
2025-05-01 12:00:56,177 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9B
2025-05-01 12:00:56,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95331.0, 'new_value': 101219.0}, {'field': 'total_amount', 'old_value': 95331.0, 'new_value': 101219.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-05-01 12:00:56,177 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-05-01 12:00:56,645 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-05-01 12:00:56,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 506136.1, 'new_value': 524449.1}, {'field': 'offline_amount', 'old_value': 113258.0, 'new_value': 115642.0}, {'field': 'total_amount', 'old_value': 619394.1, 'new_value': 640091.1}, {'field': 'order_count', 'old_value': 816, 'new_value': 845}]
2025-05-01 12:00:56,645 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-05-01 12:00:57,114 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-05-01 12:00:57,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86960.0, 'new_value': 87582.0}, {'field': 'total_amount', 'old_value': 89541.0, 'new_value': 90163.0}, {'field': 'order_count', 'old_value': 364, 'new_value': 368}]
2025-05-01 12:00:57,114 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-05-01 12:00:57,614 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-05-01 12:00:57,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25921.8, 'new_value': 26847.7}, {'field': 'offline_amount', 'old_value': 61208.3, 'new_value': 65717.8}, {'field': 'total_amount', 'old_value': 87130.1, 'new_value': 92565.5}, {'field': 'order_count', 'old_value': 245, 'new_value': 254}]
2025-05-01 12:00:57,614 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJB
2025-05-01 12:00:58,036 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJB
2025-05-01 12:00:58,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54709.0, 'new_value': 57407.0}, {'field': 'total_amount', 'old_value': 54709.0, 'new_value': 57407.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 76}]
2025-05-01 12:00:58,036 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-05-01 12:00:58,583 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-05-01 12:00:58,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40623.8, 'new_value': 42911.9}, {'field': 'total_amount', 'old_value': 41020.9, 'new_value': 43309.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 139}]
2025-05-01 12:00:58,583 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-05-01 12:00:58,989 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-05-01 12:00:58,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140598.28, 'new_value': 143337.16}, {'field': 'total_amount', 'old_value': 140598.28, 'new_value': 143337.16}, {'field': 'order_count', 'old_value': 128, 'new_value': 132}]
2025-05-01 12:00:58,989 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPB
2025-05-01 12:00:59,458 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPB
2025-05-01 12:00:59,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 325657.0, 'new_value': 333657.0}, {'field': 'total_amount', 'old_value': 454277.0, 'new_value': 462277.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 95}]
2025-05-01 12:00:59,458 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRB
2025-05-01 12:00:59,911 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRB
2025-05-01 12:00:59,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317241.0, 'new_value': 321786.0}, {'field': 'total_amount', 'old_value': 546391.0, 'new_value': 550936.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 121}]
2025-05-01 12:00:59,911 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTB
2025-05-01 12:01:00,380 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTB
2025-05-01 12:01:00,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285094.0, 'new_value': 307794.0}, {'field': 'total_amount', 'old_value': 483994.0, 'new_value': 506694.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 110}]
2025-05-01 12:01:00,380 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWB
2025-05-01 12:01:00,848 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWB
2025-05-01 12:01:00,848 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 9, 'new_value': 5369}]
2025-05-01 12:01:00,848 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-05-01 12:01:01,255 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-05-01 12:01:01,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 256810.29, 'new_value': 257725.98}, {'field': 'offline_amount', 'old_value': 77362.69, 'new_value': 91255.94}, {'field': 'total_amount', 'old_value': 334172.98, 'new_value': 348981.92}, {'field': 'order_count', 'old_value': 2343, 'new_value': 2457}]
2025-05-01 12:01:01,255 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU5
2025-05-01 12:01:01,833 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU5
2025-05-01 12:01:01,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74487.0, 'new_value': 80486.0}, {'field': 'total_amount', 'old_value': 74487.0, 'new_value': 80486.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-01 12:01:01,833 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-05-01 12:01:02,286 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-05-01 12:01:02,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126563.4, 'new_value': 130196.3}, {'field': 'total_amount', 'old_value': 130204.7, 'new_value': 133837.6}, {'field': 'order_count', 'old_value': 595, 'new_value': 619}]
2025-05-01 12:01:02,286 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB6
2025-05-01 12:01:02,770 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB6
2025-05-01 12:01:02,770 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7555.0, 'new_value': 7671.0}, {'field': 'total_amount', 'old_value': 7555.0, 'new_value': 7671.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 127}]
2025-05-01 12:01:02,770 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-05-01 12:01:03,208 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-05-01 12:01:03,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 362389.26, 'new_value': 375043.85}, {'field': 'total_amount', 'old_value': 362389.26, 'new_value': 375043.85}, {'field': 'order_count', 'old_value': 9468, 'new_value': 9840}]
2025-05-01 12:01:03,208 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-05-01 12:01:03,739 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-05-01 12:01:03,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167538.41, 'new_value': 174514.7}, {'field': 'total_amount', 'old_value': 167538.41, 'new_value': 174514.7}, {'field': 'order_count', 'old_value': 5930, 'new_value': 6169}]
2025-05-01 12:01:03,739 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-05-01 12:01:04,208 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-05-01 12:01:04,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8898.0, 'new_value': 9253.0}, {'field': 'total_amount', 'old_value': 8899.0, 'new_value': 9254.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 67}]
2025-05-01 12:01:04,208 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-05-01 12:01:04,645 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-05-01 12:01:04,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122952.0, 'new_value': 126898.0}, {'field': 'total_amount', 'old_value': 122952.0, 'new_value': 126898.0}, {'field': 'order_count', 'old_value': 3917, 'new_value': 4045}]
2025-05-01 12:01:04,645 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-05-01 12:01:05,067 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-05-01 12:01:05,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214887.4, 'new_value': 222045.93}, {'field': 'total_amount', 'old_value': 214887.4, 'new_value': 222045.93}, {'field': 'order_count', 'old_value': 696, 'new_value': 719}]
2025-05-01 12:01:05,067 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-05-01 12:01:05,505 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-05-01 12:01:05,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43284.0, 'new_value': 44449.0}, {'field': 'total_amount', 'old_value': 43284.0, 'new_value': 44449.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 144}]
2025-05-01 12:01:05,505 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV7
2025-05-01 12:01:06,036 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV7
2025-05-01 12:01:06,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95167.42, 'new_value': 98525.8}, {'field': 'offline_amount', 'old_value': 315236.93, 'new_value': 326249.0}, {'field': 'total_amount', 'old_value': 410404.35, 'new_value': 424774.8}, {'field': 'order_count', 'old_value': 19653, 'new_value': 20325}]
2025-05-01 12:01:06,036 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-05-01 12:01:06,551 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-05-01 12:01:06,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114917.0, 'new_value': 130991.0}, {'field': 'total_amount', 'old_value': 115664.0, 'new_value': 131738.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 60}]
2025-05-01 12:01:06,551 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M38
2025-05-01 12:01:07,036 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M38
2025-05-01 12:01:07,051 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3748.0, 'new_value': 3803.0}, {'field': 'offline_amount', 'old_value': 53998.1, 'new_value': 58441.1}, {'field': 'total_amount', 'old_value': 57746.1, 'new_value': 62244.1}, {'field': 'order_count', 'old_value': 65, 'new_value': 72}]
2025-05-01 12:01:07,051 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-05-01 12:01:07,489 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-05-01 12:01:07,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 167984.35, 'new_value': 173253.49}, {'field': 'offline_amount', 'old_value': 467693.09, 'new_value': 478079.19}, {'field': 'total_amount', 'old_value': 635677.44, 'new_value': 651332.68}, {'field': 'order_count', 'old_value': 3198, 'new_value': 3244}]
2025-05-01 12:01:07,489 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-05-01 12:01:08,036 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-05-01 12:01:08,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154960.83, 'new_value': 162230.49}, {'field': 'offline_amount', 'old_value': 122374.63, 'new_value': 126903.63}, {'field': 'total_amount', 'old_value': 277335.46, 'new_value': 289134.12}, {'field': 'order_count', 'old_value': 2807, 'new_value': 2915}]
2025-05-01 12:01:08,036 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-05-01 12:01:08,520 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-05-01 12:01:08,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 558277.7, 'new_value': 579145.4}, {'field': 'total_amount', 'old_value': 623876.2, 'new_value': 644743.9}, {'field': 'order_count', 'old_value': 1426, 'new_value': 1446}]
2025-05-01 12:01:08,520 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC8
2025-05-01 12:01:09,020 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC8
2025-05-01 12:01:09,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11577.0, 'new_value': 12491.0}, {'field': 'total_amount', 'old_value': 22108.0, 'new_value': 23022.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 107}]
2025-05-01 12:01:09,020 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-05-01 12:01:09,473 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-05-01 12:01:09,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79976.0, 'new_value': 87154.0}, {'field': 'total_amount', 'old_value': 86395.0, 'new_value': 93573.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 62}]
2025-05-01 12:01:09,473 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-05-01 12:01:10,020 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-05-01 12:01:10,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 645880.38, 'new_value': 668542.19}, {'field': 'total_amount', 'old_value': 645880.38, 'new_value': 668542.19}, {'field': 'order_count', 'old_value': 7033, 'new_value': 7302}]
2025-05-01 12:01:10,020 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-05-01 12:01:10,520 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-05-01 12:01:10,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 727803.67, 'new_value': 765750.97}, {'field': 'total_amount', 'old_value': 728424.39, 'new_value': 766371.69}, {'field': 'order_count', 'old_value': 1755, 'new_value': 1838}]
2025-05-01 12:01:10,520 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-05-01 12:01:10,958 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-05-01 12:01:10,958 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 198, 'new_value': 3726}]
2025-05-01 12:01:10,958 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-05-01 12:01:11,411 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-05-01 12:01:11,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 378337.0, 'new_value': 461913.0}, {'field': 'total_amount', 'old_value': 378337.0, 'new_value': 461913.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 160}]
2025-05-01 12:01:11,411 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-05-01 12:01:11,817 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-05-01 12:01:11,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54700.26, 'new_value': 56587.42}, {'field': 'offline_amount', 'old_value': 462431.82, 'new_value': 479462.24}, {'field': 'total_amount', 'old_value': 517132.08, 'new_value': 536049.66}, {'field': 'order_count', 'old_value': 2333, 'new_value': 2415}]
2025-05-01 12:01:11,817 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVH
2025-05-01 12:01:12,333 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVH
2025-05-01 12:01:12,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77966.84, 'new_value': 86030.84}, {'field': 'total_amount', 'old_value': 77966.84, 'new_value': 86030.84}, {'field': 'order_count', 'old_value': 4286, 'new_value': 4672}]
2025-05-01 12:01:12,333 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-05-01 12:01:12,801 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-05-01 12:01:12,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 9147.0}, {'field': 'total_amount', 'old_value': 269022.56, 'new_value': 278169.56}, {'field': 'order_count', 'old_value': 1493, 'new_value': 1543}]
2025-05-01 12:01:12,801 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-05-01 12:01:13,364 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-05-01 12:01:13,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27026.0, 'new_value': 30414.0}, {'field': 'total_amount', 'old_value': 28229.8, 'new_value': 31617.8}, {'field': 'order_count', 'old_value': 288, 'new_value': 308}]
2025-05-01 12:01:13,364 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6I
2025-05-01 12:01:13,848 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6I
2025-05-01 12:01:13,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110940.0, 'new_value': 144490.0}, {'field': 'total_amount', 'old_value': 141440.0, 'new_value': 174990.0}, {'field': 'order_count', 'old_value': 1431, 'new_value': 1433}]
2025-05-01 12:01:13,848 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-05-01 12:01:14,551 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-05-01 12:01:14,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 895106.53, 'new_value': 922632.28}, {'field': 'total_amount', 'old_value': 895106.53, 'new_value': 922632.28}, {'field': 'order_count', 'old_value': 6215, 'new_value': 6451}]
2025-05-01 12:01:14,551 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-05-01 12:01:15,051 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-05-01 12:01:15,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130478.0, 'new_value': 135482.0}, {'field': 'total_amount', 'old_value': 130478.0, 'new_value': 135482.0}, {'field': 'order_count', 'old_value': 588, 'new_value': 608}]
2025-05-01 12:01:15,051 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-05-01 12:01:15,520 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-05-01 12:01:15,520 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174943.0, 'new_value': 181031.0}, {'field': 'offline_amount', 'old_value': 130390.0, 'new_value': 136078.0}, {'field': 'total_amount', 'old_value': 305333.0, 'new_value': 317109.0}, {'field': 'order_count', 'old_value': 11737, 'new_value': 12260}]
2025-05-01 12:01:15,520 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MII
2025-05-01 12:01:16,036 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MII
2025-05-01 12:01:16,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14314.17, 'new_value': 14724.22}, {'field': 'offline_amount', 'old_value': 78024.36, 'new_value': 83351.19}, {'field': 'total_amount', 'old_value': 92338.53, 'new_value': 98075.41}, {'field': 'order_count', 'old_value': 2364, 'new_value': 2524}]
2025-05-01 12:01:16,036 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-05-01 12:01:16,536 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-05-01 12:01:16,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261658.1, 'new_value': 280717.9}, {'field': 'total_amount', 'old_value': 261658.1, 'new_value': 280717.9}, {'field': 'order_count', 'old_value': 5592, 'new_value': 6019}]
2025-05-01 12:01:16,536 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPI
2025-05-01 12:01:16,989 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPI
2025-05-01 12:01:16,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49300.0, 'new_value': 59200.0}, {'field': 'total_amount', 'old_value': 49300.0, 'new_value': 59200.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-01 12:01:16,989 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUI
2025-05-01 12:01:17,458 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUI
2025-05-01 12:01:17,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101000.0, 'new_value': 136000.0}, {'field': 'total_amount', 'old_value': 101000.0, 'new_value': 136000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-01 12:01:17,458 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-05-01 12:01:17,973 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-05-01 12:01:17,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71285.0, 'new_value': 72080.0}, {'field': 'total_amount', 'old_value': 71295.0, 'new_value': 72090.0}, {'field': 'order_count', 'old_value': 269, 'new_value': 272}]
2025-05-01 12:01:17,973 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-05-01 12:01:18,380 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-05-01 12:01:18,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 175.0, 'new_value': 275.72}, {'field': 'offline_amount', 'old_value': 40230.24, 'new_value': 42285.24}, {'field': 'total_amount', 'old_value': 40405.24, 'new_value': 42560.96}, {'field': 'order_count', 'old_value': 317, 'new_value': 329}]
2025-05-01 12:01:18,380 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1J
2025-05-01 12:01:18,817 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1J
2025-05-01 12:01:18,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1205467.0, 'new_value': 1255349.0}, {'field': 'offline_amount', 'old_value': 572922.0, 'new_value': 592694.0}, {'field': 'total_amount', 'old_value': 1778389.0, 'new_value': 1848043.0}, {'field': 'order_count', 'old_value': 1517, 'new_value': 1553}]
2025-05-01 12:01:18,817 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7J
2025-05-01 12:01:19,301 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7J
2025-05-01 12:01:19,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94604.0, 'new_value': 100138.0}, {'field': 'total_amount', 'old_value': 94604.0, 'new_value': 100138.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-05-01 12:01:19,301 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-05-01 12:01:19,770 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-05-01 12:01:19,770 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5555.0, 'new_value': 6041.0}, {'field': 'offline_amount', 'old_value': 25457.0, 'new_value': 25777.0}, {'field': 'total_amount', 'old_value': 31012.0, 'new_value': 31818.0}, {'field': 'order_count', 'old_value': 157, 'new_value': 162}]
2025-05-01 12:01:19,770 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-05-01 12:01:20,208 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-05-01 12:01:20,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50218.58, 'new_value': 52475.75}, {'field': 'total_amount', 'old_value': 50218.58, 'new_value': 52475.75}, {'field': 'order_count', 'old_value': 778, 'new_value': 802}]
2025-05-01 12:01:20,208 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-05-01 12:01:20,630 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-05-01 12:01:20,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14040000.0, 'new_value': 14240000.0}, {'field': 'total_amount', 'old_value': 14040001.0, 'new_value': 14240001.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 72}]
2025-05-01 12:01:20,630 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-05-01 12:01:21,098 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-05-01 12:01:21,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 232066.47, 'new_value': 242883.13}, {'field': 'offline_amount', 'old_value': 44197.77, 'new_value': 44925.57}, {'field': 'total_amount', 'old_value': 276264.24, 'new_value': 287808.7}, {'field': 'order_count', 'old_value': 13026, 'new_value': 13378}]
2025-05-01 12:01:21,098 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-05-01 12:01:21,583 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-05-01 12:01:21,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248584.38, 'new_value': 250336.38}, {'field': 'total_amount', 'old_value': 248584.38, 'new_value': 250336.38}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-05-01 12:01:21,583 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-05-01 12:01:22,098 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-05-01 12:01:22,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61232.93, 'new_value': 64623.54}, {'field': 'offline_amount', 'old_value': 57999.09, 'new_value': 60140.71}, {'field': 'total_amount', 'old_value': 119232.02, 'new_value': 124764.25}, {'field': 'order_count', 'old_value': 2982, 'new_value': 3099}]
2025-05-01 12:01:22,098 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-05-01 12:01:22,583 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-05-01 12:01:22,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1833667.3, 'new_value': 1909664.61}, {'field': 'offline_amount', 'old_value': 291631.0, 'new_value': 292205.0}, {'field': 'total_amount', 'old_value': 2125298.3, 'new_value': 2201869.61}, {'field': 'order_count', 'old_value': 10212, 'new_value': 10505}]
2025-05-01 12:01:22,598 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-05-01 12:01:23,083 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-05-01 12:01:23,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285686.53, 'new_value': 299630.73}, {'field': 'total_amount', 'old_value': 285686.53, 'new_value': 299630.73}, {'field': 'order_count', 'old_value': 2042, 'new_value': 2125}]
2025-05-01 12:01:23,083 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M3
2025-05-01 12:01:23,536 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M3
2025-05-01 12:01:23,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7299.0, 'new_value': 7404.0}, {'field': 'offline_amount', 'old_value': 3703.0, 'new_value': 3873.0}, {'field': 'total_amount', 'old_value': 11002.0, 'new_value': 11277.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 98}]
2025-05-01 12:01:23,536 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M5
2025-05-01 12:01:24,083 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M5
2025-05-01 12:01:24,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74425.0, 'new_value': 83285.0}, {'field': 'total_amount', 'old_value': 74425.0, 'new_value': 83285.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-01 12:01:24,083 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA
2025-05-01 12:01:24,520 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA
2025-05-01 12:01:24,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86796.0, 'new_value': 95916.0}, {'field': 'total_amount', 'old_value': 86796.0, 'new_value': 95916.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-01 12:01:24,520 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-05-01 12:01:25,067 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-05-01 12:01:25,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94559.0, 'new_value': 108519.0}, {'field': 'total_amount', 'old_value': 114559.0, 'new_value': 128519.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-01 12:01:25,067 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF
2025-05-01 12:01:25,551 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF
2025-05-01 12:01:25,567 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21151.9, 'new_value': 21553.9}, {'field': 'offline_amount', 'old_value': 233886.2, 'new_value': 244645.3}, {'field': 'total_amount', 'old_value': 255038.1, 'new_value': 266199.2}, {'field': 'order_count', 'old_value': 7801, 'new_value': 8103}]
2025-05-01 12:01:25,567 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-05-01 12:01:25,942 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-05-01 12:01:25,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1127869.0, 'new_value': 1160279.0}, {'field': 'total_amount', 'old_value': 1128034.0, 'new_value': 1160444.0}, {'field': 'order_count', 'old_value': 1313, 'new_value': 1357}]
2025-05-01 12:01:25,942 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-05-01 12:01:26,395 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-05-01 12:01:26,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 496934.26, 'new_value': 517318.26}, {'field': 'total_amount', 'old_value': 496934.26, 'new_value': 517318.26}, {'field': 'order_count', 'old_value': 3891, 'new_value': 4016}]
2025-05-01 12:01:26,395 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-05-01 12:01:26,879 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-05-01 12:01:26,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85608.28, 'new_value': 88452.59}, {'field': 'offline_amount', 'old_value': 1026207.32, 'new_value': 1065717.47}, {'field': 'total_amount', 'old_value': 1080786.98, 'new_value': 1123141.44}, {'field': 'order_count', 'old_value': 4467, 'new_value': 4646}]
2025-05-01 12:01:26,879 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-05-01 12:01:27,348 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-05-01 12:01:27,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57305.9, 'new_value': 59377.8}, {'field': 'total_amount', 'old_value': 57305.9, 'new_value': 59377.8}, {'field': 'order_count', 'old_value': 311, 'new_value': 323}]
2025-05-01 12:01:27,348 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-05-01 12:01:27,754 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-05-01 12:01:27,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120676.0, 'new_value': 126269.0}, {'field': 'total_amount', 'old_value': 122568.0, 'new_value': 128161.0}, {'field': 'order_count', 'old_value': 594, 'new_value': 621}]
2025-05-01 12:01:27,754 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX
2025-05-01 12:01:28,176 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX
2025-05-01 12:01:28,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23713.2, 'new_value': 23743.2}, {'field': 'total_amount', 'old_value': 23713.2, 'new_value': 23743.2}, {'field': 'order_count', 'old_value': 144, 'new_value': 145}]
2025-05-01 12:01:28,176 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-05-01 12:01:28,645 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-05-01 12:01:28,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79014.0, 'new_value': 80197.0}, {'field': 'offline_amount', 'old_value': 351417.0, 'new_value': 362658.0}, {'field': 'total_amount', 'old_value': 430431.0, 'new_value': 442855.0}, {'field': 'order_count', 'old_value': 1727, 'new_value': 1782}]
2025-05-01 12:01:28,645 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-05-01 12:01:29,083 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-05-01 12:01:29,083 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93952.65, 'new_value': 101100.04}, {'field': 'offline_amount', 'old_value': 274937.11, 'new_value': 287461.71}, {'field': 'total_amount', 'old_value': 368889.76, 'new_value': 388561.75}, {'field': 'order_count', 'old_value': 2548, 'new_value': 2685}]
2025-05-01 12:01:29,083 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD1
2025-05-01 12:01:29,536 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD1
2025-05-01 12:01:29,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19229.0, 'new_value': 20329.0}, {'field': 'total_amount', 'old_value': 19229.0, 'new_value': 20329.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-01 12:01:29,536 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-05-01 12:01:29,973 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-05-01 12:01:29,973 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3720.0, 'new_value': 3805.0}, {'field': 'offline_amount', 'old_value': 46160.0, 'new_value': 46960.0}, {'field': 'total_amount', 'old_value': 49880.0, 'new_value': 50765.0}, {'field': 'order_count', 'old_value': 688, 'new_value': 700}]
2025-05-01 12:01:29,973 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH1
2025-05-01 12:01:30,426 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH1
2025-05-01 12:01:30,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68058.0, 'new_value': 80093.0}, {'field': 'total_amount', 'old_value': 68058.0, 'new_value': 80093.0}, {'field': 'order_count', 'old_value': 14460, 'new_value': 15637}]
2025-05-01 12:01:30,426 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI1
2025-05-01 12:01:30,895 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI1
2025-05-01 12:01:30,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98848.0, 'new_value': 116901.0}, {'field': 'total_amount', 'old_value': 102092.0, 'new_value': 120145.0}, {'field': 'order_count', 'old_value': 14460, 'new_value': 15637}]
2025-05-01 12:01:30,895 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-05-01 12:01:31,348 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-05-01 12:01:31,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57983.8, 'new_value': 60115.8}, {'field': 'total_amount', 'old_value': 64650.28, 'new_value': 66782.28}, {'field': 'order_count', 'old_value': 116, 'new_value': 121}]
2025-05-01 12:01:31,348 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-05-01 12:01:31,817 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-05-01 12:01:31,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256797.0, 'new_value': 268127.0}, {'field': 'total_amount', 'old_value': 256797.0, 'new_value': 268127.0}, {'field': 'order_count', 'old_value': 27780, 'new_value': 28970}]
2025-05-01 12:01:31,817 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-05-01 12:01:32,286 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-05-01 12:01:32,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 380868.0, 'new_value': 395877.0}, {'field': 'total_amount', 'old_value': 391658.0, 'new_value': 406667.0}, {'field': 'order_count', 'old_value': 306, 'new_value': 313}]
2025-05-01 12:01:32,286 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-05-01 12:01:32,817 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-05-01 12:01:32,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1581451.0, 'new_value': 2463451.0}, {'field': 'total_amount', 'old_value': 6449391.0, 'new_value': 7331391.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-05-01 12:01:32,833 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M32
2025-05-01 12:01:33,348 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M32
2025-05-01 12:01:33,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191068.46, 'new_value': 192642.36}, {'field': 'total_amount', 'old_value': 194266.76, 'new_value': 195840.66}, {'field': 'order_count', 'old_value': 76, 'new_value': 85}]
2025-05-01 12:01:33,348 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-05-01 12:01:33,879 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-05-01 12:01:33,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71579.51, 'new_value': 79264.52}, {'field': 'total_amount', 'old_value': 196863.13, 'new_value': 204548.14}, {'field': 'order_count', 'old_value': 12921, 'new_value': 13396}]
2025-05-01 12:01:33,879 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-05-01 12:01:34,333 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-05-01 12:01:34,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46416.0, 'new_value': 48996.0}, {'field': 'total_amount', 'old_value': 46416.0, 'new_value': 48996.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-01 12:01:34,333 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-05-01 12:01:34,786 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-05-01 12:01:34,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35969.02, 'new_value': 39557.6}, {'field': 'total_amount', 'old_value': 100031.21, 'new_value': 103619.79}, {'field': 'order_count', 'old_value': 6849, 'new_value': 7112}]
2025-05-01 12:01:34,786 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9M92
2025-05-01 12:01:35,239 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9M92
2025-05-01 12:01:35,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62019.0, 'new_value': 65599.0}, {'field': 'total_amount', 'old_value': 62019.0, 'new_value': 65599.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-01 12:01:35,239 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MF2
2025-05-01 12:01:35,708 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MF2
2025-05-01 12:01:35,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120289.0, 'new_value': 135784.0}, {'field': 'total_amount', 'old_value': 120289.0, 'new_value': 135784.0}, {'field': 'order_count', 'old_value': 320, 'new_value': 360}]
2025-05-01 12:01:35,708 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-05-01 12:01:36,192 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-05-01 12:01:36,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234961.0, 'new_value': 242359.0}, {'field': 'total_amount', 'old_value': 234961.0, 'new_value': 242359.0}, {'field': 'order_count', 'old_value': 8311, 'new_value': 8575}]
2025-05-01 12:01:36,192 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-05-01 12:01:36,645 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-05-01 12:01:36,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15977328.0, 'new_value': 16597395.0}, {'field': 'total_amount', 'old_value': 15977328.0, 'new_value': 16597395.0}, {'field': 'order_count', 'old_value': 47387, 'new_value': 49149}]
2025-05-01 12:01:36,645 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-05-01 12:01:37,114 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-05-01 12:01:37,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1230845.94, 'new_value': 1280957.23}, {'field': 'total_amount', 'old_value': 1230845.94, 'new_value': 1280957.23}, {'field': 'order_count', 'old_value': 3766, 'new_value': 3907}]
2025-05-01 12:01:37,114 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-05-01 12:01:37,504 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-05-01 12:01:37,504 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1165655.12, 'new_value': 1206163.12}, {'field': 'total_amount', 'old_value': 1165655.12, 'new_value': 1206163.12}, {'field': 'order_count', 'old_value': 4039, 'new_value': 4188}]
2025-05-01 12:01:37,504 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-05-01 12:01:37,989 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-05-01 12:01:37,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 792020.55, 'new_value': 828257.38}, {'field': 'total_amount', 'old_value': 792020.55, 'new_value': 828257.38}, {'field': 'order_count', 'old_value': 3891, 'new_value': 4076}]
2025-05-01 12:01:37,989 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MQ2
2025-05-01 12:01:38,504 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MQ2
2025-05-01 12:01:38,504 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29985.49, 'new_value': 32121.6}, {'field': 'offline_amount', 'old_value': 29070.77, 'new_value': 31577.21}, {'field': 'total_amount', 'old_value': 59056.26, 'new_value': 63698.81}, {'field': 'order_count', 'old_value': 3248, 'new_value': 3504}]
2025-05-01 12:01:38,504 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-05-01 12:01:38,942 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-05-01 12:01:38,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 427730.0, 'new_value': 448390.0}, {'field': 'total_amount', 'old_value': 427730.0, 'new_value': 448390.0}, {'field': 'order_count', 'old_value': 163, 'new_value': 170}]
2025-05-01 12:01:38,942 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-05-01 12:01:39,395 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-05-01 12:01:39,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 369556.0, 'new_value': 383270.0}, {'field': 'total_amount', 'old_value': 369556.0, 'new_value': 383270.0}, {'field': 'order_count', 'old_value': 7549, 'new_value': 7853}]
2025-05-01 12:01:39,395 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-05-01 12:01:39,833 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-05-01 12:01:39,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234435.07, 'new_value': 243457.72}, {'field': 'total_amount', 'old_value': 234435.07, 'new_value': 243457.72}, {'field': 'order_count', 'old_value': 16654, 'new_value': 17283}]
2025-05-01 12:01:39,833 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M46
2025-05-01 12:01:40,317 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M46
2025-05-01 12:01:40,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13967.48, 'new_value': 14387.75}, {'field': 'offline_amount', 'old_value': 134145.78, 'new_value': 144936.3}, {'field': 'total_amount', 'old_value': 148113.26, 'new_value': 159324.05}, {'field': 'order_count', 'old_value': 2278, 'new_value': 2471}]
2025-05-01 12:01:40,317 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-05-01 12:01:40,817 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-05-01 12:01:40,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26771.38, 'new_value': 27734.88}, {'field': 'offline_amount', 'old_value': 20152.55, 'new_value': 20791.55}, {'field': 'total_amount', 'old_value': 46923.93, 'new_value': 48526.43}, {'field': 'order_count', 'old_value': 2390, 'new_value': 2475}]
2025-05-01 12:01:40,817 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-05-01 12:01:41,333 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-05-01 12:01:41,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 795317.56, 'new_value': 815719.64}, {'field': 'total_amount', 'old_value': 795317.56, 'new_value': 815719.64}, {'field': 'order_count', 'old_value': 6642, 'new_value': 6812}]
2025-05-01 12:01:41,333 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-05-01 12:01:41,739 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-05-01 12:01:41,739 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-01 12:01:41,739 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-05-01 12:01:42,161 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-05-01 12:01:42,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196938.21, 'new_value': 203776.58}, {'field': 'total_amount', 'old_value': 196938.21, 'new_value': 203776.58}, {'field': 'order_count', 'old_value': 21837, 'new_value': 22585}]
2025-05-01 12:01:42,161 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-05-01 12:01:42,692 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-05-01 12:01:42,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1388149.0, 'new_value': 1441623.0}, {'field': 'total_amount', 'old_value': 1388149.0, 'new_value': 1441623.0}, {'field': 'order_count', 'old_value': 5884, 'new_value': 6122}]
2025-05-01 12:01:42,692 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-05-01 12:01:43,176 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-05-01 12:01:43,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52224.0, 'new_value': 53516.0}, {'field': 'total_amount', 'old_value': 52224.0, 'new_value': 53516.0}, {'field': 'order_count', 'old_value': 326, 'new_value': 337}]
2025-05-01 12:01:43,176 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-05-01 12:01:43,645 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-05-01 12:01:43,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271457.0, 'new_value': 277545.0}, {'field': 'total_amount', 'old_value': 271460.0, 'new_value': 277548.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 75}]
2025-05-01 12:01:43,645 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-05-01 12:01:44,129 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-05-01 12:01:44,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 823848.67, 'new_value': 850647.67}, {'field': 'total_amount', 'old_value': 859917.65, 'new_value': 886716.65}, {'field': 'order_count', 'old_value': 2183, 'new_value': 2243}]
2025-05-01 12:01:44,129 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-05-01 12:01:44,676 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-05-01 12:01:44,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 393582.0, 'new_value': 403282.0}, {'field': 'total_amount', 'old_value': 393582.0, 'new_value': 403282.0}, {'field': 'order_count', 'old_value': 721, 'new_value': 744}]
2025-05-01 12:01:44,676 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-05-01 12:01:45,114 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-05-01 12:01:45,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65099.39, 'new_value': 69036.39}, {'field': 'total_amount', 'old_value': 65099.39, 'new_value': 69036.39}, {'field': 'order_count', 'old_value': 1109, 'new_value': 1165}]
2025-05-01 12:01:45,114 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-05-01 12:01:45,614 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-05-01 12:01:45,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19066.08, 'new_value': 19930.08}, {'field': 'offline_amount', 'old_value': 292644.0, 'new_value': 304108.0}, {'field': 'total_amount', 'old_value': 311710.08, 'new_value': 324038.08}, {'field': 'order_count', 'old_value': 1547, 'new_value': 1606}]
2025-05-01 12:01:45,614 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP6
2025-05-01 12:01:46,098 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP6
2025-05-01 12:01:46,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120625.8, 'new_value': 131797.67}, {'field': 'total_amount', 'old_value': 120625.8, 'new_value': 131797.67}, {'field': 'order_count', 'old_value': 578, 'new_value': 643}]
2025-05-01 12:01:46,098 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-05-01 12:01:46,520 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-05-01 12:01:46,520 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104120.18, 'new_value': 108731.78}, {'field': 'offline_amount', 'old_value': 368769.54, 'new_value': 380809.34}, {'field': 'total_amount', 'old_value': 472889.72, 'new_value': 489541.12}, {'field': 'order_count', 'old_value': 3543, 'new_value': 3681}]
2025-05-01 12:01:46,520 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-05-01 12:01:46,957 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-05-01 12:01:46,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 642575.14, 'new_value': 664686.14}, {'field': 'total_amount', 'old_value': 642575.14, 'new_value': 664686.14}, {'field': 'order_count', 'old_value': 5320, 'new_value': 5519}]
2025-05-01 12:01:46,957 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX6
2025-05-01 12:01:47,426 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX6
2025-05-01 12:01:47,426 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30982.9, 'new_value': 29682.9}, {'field': 'offline_amount', 'old_value': 117458.0, 'new_value': 125558.0}, {'field': 'total_amount', 'old_value': 148440.9, 'new_value': 155240.9}, {'field': 'order_count', 'old_value': 2999, 'new_value': 2858}]
2025-05-01 12:01:47,426 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-05-01 12:01:47,879 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-05-01 12:01:47,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 313460.91, 'new_value': 323284.91}, {'field': 'total_amount', 'old_value': 313460.91, 'new_value': 323284.91}, {'field': 'order_count', 'old_value': 6694, 'new_value': 6890}]
2025-05-01 12:01:47,879 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-05-01 12:01:48,364 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-05-01 12:01:48,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241852.32, 'new_value': 248694.29}, {'field': 'total_amount', 'old_value': 241852.32, 'new_value': 248694.29}, {'field': 'order_count', 'old_value': 8961, 'new_value': 9280}]
2025-05-01 12:01:48,364 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M27
2025-05-01 12:01:48,911 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M27
2025-05-01 12:01:48,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180475.0, 'new_value': 222044.0}, {'field': 'total_amount', 'old_value': 279447.0, 'new_value': 321016.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 75}]
2025-05-01 12:01:48,911 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-05-01 12:01:49,489 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-05-01 12:01:49,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80486.62, 'new_value': 82211.62}, {'field': 'total_amount', 'old_value': 80486.62, 'new_value': 82211.62}, {'field': 'order_count', 'old_value': 212, 'new_value': 220}]
2025-05-01 12:01:49,489 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-05-01 12:01:49,989 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-05-01 12:01:49,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3825720.74, 'new_value': 3982348.72}, {'field': 'total_amount', 'old_value': 3825720.74, 'new_value': 3982348.72}, {'field': 'order_count', 'old_value': 6633, 'new_value': 6882}]
2025-05-01 12:01:49,989 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-05-01 12:01:50,629 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-05-01 12:01:50,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24687.0, 'new_value': 25956.0}, {'field': 'total_amount', 'old_value': 24687.0, 'new_value': 25956.0}, {'field': 'order_count', 'old_value': 235, 'new_value': 245}]
2025-05-01 12:01:50,629 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF7
2025-05-01 12:01:51,082 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF7
2025-05-01 12:01:51,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62501.9, 'new_value': 63334.9}, {'field': 'total_amount', 'old_value': 63297.9, 'new_value': 64130.9}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-01 12:01:51,082 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-05-01 12:01:51,489 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-05-01 12:01:51,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91263.0, 'new_value': 93791.0}, {'field': 'offline_amount', 'old_value': 155610.0, 'new_value': 161832.0}, {'field': 'total_amount', 'old_value': 246873.0, 'new_value': 255623.0}, {'field': 'order_count', 'old_value': 5946, 'new_value': 6154}]
2025-05-01 12:01:51,489 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MJ7
2025-05-01 12:01:51,973 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MJ7
2025-05-01 12:01:51,973 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1079.9, 'new_value': 1212.1}, {'field': 'total_amount', 'old_value': 10117.9, 'new_value': 10250.1}, {'field': 'order_count', 'old_value': 1008, 'new_value': 1010}]
2025-05-01 12:01:51,973 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK7
2025-05-01 12:01:52,426 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK7
2025-05-01 12:01:52,426 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9504.1, 'new_value': 9591.0}, {'field': 'total_amount', 'old_value': 74350.1, 'new_value': 74437.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-01 12:01:52,426 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-05-01 12:01:52,817 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-05-01 12:01:52,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82530.0, 'new_value': 92355.0}, {'field': 'total_amount', 'old_value': 82530.0, 'new_value': 92355.0}, {'field': 'order_count', 'old_value': 1492, 'new_value': 1715}]
2025-05-01 12:01:52,817 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV7
2025-05-01 12:01:53,379 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV7
2025-05-01 12:01:53,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186728.0, 'new_value': 189108.0}, {'field': 'total_amount', 'old_value': 186728.0, 'new_value': 189108.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-01 12:01:53,379 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-05-01 12:01:53,786 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-05-01 12:01:53,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17440.7, 'new_value': 18211.7}, {'field': 'total_amount', 'old_value': 17440.7, 'new_value': 18211.7}, {'field': 'order_count', 'old_value': 68, 'new_value': 72}]
2025-05-01 12:01:53,786 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M48
2025-05-01 12:01:54,254 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M48
2025-05-01 12:01:54,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65000.0, 'new_value': 68000.0}, {'field': 'total_amount', 'old_value': 65000.0, 'new_value': 68000.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-01 12:01:54,254 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-05-01 12:01:54,692 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-05-01 12:01:54,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1482000.0, 'new_value': 1532000.0}, {'field': 'total_amount', 'old_value': 1482000.0, 'new_value': 1532000.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-01 12:01:54,692 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-05-01 12:01:55,161 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-05-01 12:01:55,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6527974.0, 'new_value': 6813642.0}, {'field': 'total_amount', 'old_value': 6527974.0, 'new_value': 6813642.0}, {'field': 'order_count', 'old_value': 115621, 'new_value': 120054}]
2025-05-01 12:01:55,161 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-05-01 12:01:55,661 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-05-01 12:01:55,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58292.0, 'new_value': 65298.0}, {'field': 'total_amount', 'old_value': 58292.0, 'new_value': 65298.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 64}]
2025-05-01 12:01:55,661 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-05-01 12:01:56,114 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-05-01 12:01:56,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138405.0, 'new_value': 144147.0}, {'field': 'total_amount', 'old_value': 138405.0, 'new_value': 144147.0}, {'field': 'order_count', 'old_value': 1357, 'new_value': 1417}]
2025-05-01 12:01:56,114 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-05-01 12:01:56,551 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-05-01 12:01:56,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 329175.62, 'new_value': 343209.03}, {'field': 'offline_amount', 'old_value': 277983.89, 'new_value': 286299.77}, {'field': 'total_amount', 'old_value': 607159.51, 'new_value': 629508.8}, {'field': 'order_count', 'old_value': 16850, 'new_value': 17473}]
2025-05-01 12:01:56,551 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-05-01 12:01:57,020 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-05-01 12:01:57,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171435.0, 'new_value': 178288.0}, {'field': 'total_amount', 'old_value': 180842.0, 'new_value': 187695.0}, {'field': 'order_count', 'old_value': 739, 'new_value': 781}]
2025-05-01 12:01:57,020 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-05-01 12:01:57,504 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-05-01 12:01:57,504 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90353.3, 'new_value': 94110.48}, {'field': 'offline_amount', 'old_value': 93129.58, 'new_value': 96663.59}, {'field': 'total_amount', 'old_value': 183482.88, 'new_value': 190774.07}, {'field': 'order_count', 'old_value': 7570, 'new_value': 7881}]
2025-05-01 12:01:57,504 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-05-01 12:01:57,879 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-05-01 12:01:57,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167065.0, 'new_value': 170821.0}, {'field': 'total_amount', 'old_value': 184879.0, 'new_value': 188635.0}, {'field': 'order_count', 'old_value': 803, 'new_value': 768}]
2025-05-01 12:01:57,879 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-05-01 12:01:58,332 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-05-01 12:01:58,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131841.0, 'new_value': 136931.0}, {'field': 'total_amount', 'old_value': 137443.0, 'new_value': 142533.0}, {'field': 'order_count', 'old_value': 476, 'new_value': 490}]
2025-05-01 12:01:58,332 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-05-01 12:01:58,739 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-05-01 12:01:58,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74332.0, 'new_value': 80131.0}, {'field': 'offline_amount', 'old_value': 41895.0, 'new_value': 47846.0}, {'field': 'total_amount', 'old_value': 116227.0, 'new_value': 127977.0}, {'field': 'order_count', 'old_value': 195, 'new_value': 206}]
2025-05-01 12:01:58,739 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M52
2025-05-01 12:01:59,129 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M52
2025-05-01 12:01:59,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8140.6, 'new_value': 8279.5}, {'field': 'offline_amount', 'old_value': 44412.5, 'new_value': 44451.5}, {'field': 'total_amount', 'old_value': 52553.1, 'new_value': 52731.0}, {'field': 'order_count', 'old_value': 499, 'new_value': 502}]
2025-05-01 12:01:59,129 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-05-01 12:01:59,598 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-05-01 12:01:59,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15993280.47, 'new_value': 16544772.9}, {'field': 'total_amount', 'old_value': 15993280.47, 'new_value': 16544772.9}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-01 12:01:59,598 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-05-01 12:02:00,098 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-05-01 12:02:00,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116430.0, 'new_value': 133891.0}, {'field': 'total_amount', 'old_value': 156871.0, 'new_value': 174332.0}, {'field': 'order_count', 'old_value': 1058, 'new_value': 1209}]
2025-05-01 12:02:00,098 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I718UXUFQYW74TY65GTGPXF3UKTQW0AML7
2025-05-01 12:02:00,536 - INFO - 更新表单数据成功: FINST-2FD66I718UXUFQYW74TY65GTGPXF3UKTQW0AML7
2025-05-01 12:02:00,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16200.0, 'new_value': 22445.0}, {'field': 'offline_amount', 'old_value': 10738.0, 'new_value': 12392.0}, {'field': 'total_amount', 'old_value': 26938.0, 'new_value': 34837.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 218}]
2025-05-01 12:02:00,536 - INFO - 日期 2025-04 处理完成 - 更新: 204 条，插入: 0 条，错误: 0 条
2025-05-01 12:02:00,536 - INFO - 开始处理日期: 2025-05
2025-05-01 12:02:00,536 - INFO - Request Parameters - Page 1:
2025-05-01 12:02:00,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:02:00,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:02:00,770 - INFO - Response - Page 1:
2025-05-01 12:02:00,973 - INFO - 查询完成，共获取到 0 条记录
2025-05-01 12:02:00,973 - INFO - 获取到 0 条表单数据
2025-05-01 12:02:00,973 - INFO - 当前日期 2025-05 有 1 条MySQL数据需要处理
2025-05-01 12:02:00,973 - INFO - 开始批量插入 1 条新记录
2025-05-01 12:02:01,114 - INFO - 批量插入响应状态码: 200
2025-05-01 12:02:01,114 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 04:01:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D5A7A0C7-42B0-7E2A-AD2F-076AA33987B8', 'x-acs-trace-id': '36bf204a190870aa5362e2c568fed7b7', 'etag': '6a2O1gPQEL48bcMKJq6IUbQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 12:02:01,114 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC1SAZUPWCXCKCYHBKMKFBZ2I5M8U4AMM6']}
2025-05-01 12:02:01,114 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-01 12:02:01,114 - INFO - 成功插入的数据ID: ['FINST-KLF66WC1SAZUPWCXCKCYHBKMKFBZ2I5M8U4AMM6']
2025-05-01 12:02:04,129 - INFO - 批量插入完成，共 1 条记录
2025-05-01 12:02:04,129 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-01 12:02:04,129 - INFO - 数据同步完成！更新: 204 条，插入: 1 条，错误: 0 条
2025-05-01 12:02:04,129 - INFO - =================同步完成====================
2025-05-01 15:00:01,906 - INFO - =================使用默认全量同步=============
2025-05-01 15:00:03,062 - INFO - MySQL查询成功，共获取 2641 条记录
2025-05-01 15:00:03,062 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-01 15:00:03,093 - INFO - 开始处理日期: 2025-01
2025-05-01 15:00:03,093 - INFO - Request Parameters - Page 1:
2025-05-01 15:00:03,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:03,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:04,078 - INFO - Response - Page 1:
2025-05-01 15:00:04,281 - INFO - 第 1 页获取到 100 条记录
2025-05-01 15:00:04,281 - INFO - Request Parameters - Page 2:
2025-05-01 15:00:04,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:04,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:04,796 - INFO - Response - Page 2:
2025-05-01 15:00:05,000 - INFO - 第 2 页获取到 100 条记录
2025-05-01 15:00:05,000 - INFO - Request Parameters - Page 3:
2025-05-01 15:00:05,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:05,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:05,468 - INFO - Response - Page 3:
2025-05-01 15:00:05,671 - INFO - 第 3 页获取到 100 条记录
2025-05-01 15:00:05,671 - INFO - Request Parameters - Page 4:
2025-05-01 15:00:05,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:05,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:06,109 - INFO - Response - Page 4:
2025-05-01 15:00:06,312 - INFO - 第 4 页获取到 100 条记录
2025-05-01 15:00:06,312 - INFO - Request Parameters - Page 5:
2025-05-01 15:00:06,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:06,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:06,781 - INFO - Response - Page 5:
2025-05-01 15:00:06,984 - INFO - 第 5 页获取到 100 条记录
2025-05-01 15:00:06,984 - INFO - Request Parameters - Page 6:
2025-05-01 15:00:06,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:06,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:07,484 - INFO - Response - Page 6:
2025-05-01 15:00:07,687 - INFO - 第 6 页获取到 100 条记录
2025-05-01 15:00:07,687 - INFO - Request Parameters - Page 7:
2025-05-01 15:00:07,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:07,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:08,109 - INFO - Response - Page 7:
2025-05-01 15:00:08,312 - INFO - 第 7 页获取到 82 条记录
2025-05-01 15:00:08,312 - INFO - 查询完成，共获取到 682 条记录
2025-05-01 15:00:08,312 - INFO - 获取到 682 条表单数据
2025-05-01 15:00:08,312 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-01 15:00:08,328 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 15:00:08,328 - INFO - 开始处理日期: 2025-02
2025-05-01 15:00:08,328 - INFO - Request Parameters - Page 1:
2025-05-01 15:00:08,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:08,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:08,859 - INFO - Response - Page 1:
2025-05-01 15:00:09,062 - INFO - 第 1 页获取到 100 条记录
2025-05-01 15:00:09,062 - INFO - Request Parameters - Page 2:
2025-05-01 15:00:09,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:09,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:09,578 - INFO - Response - Page 2:
2025-05-01 15:00:09,781 - INFO - 第 2 页获取到 100 条记录
2025-05-01 15:00:09,781 - INFO - Request Parameters - Page 3:
2025-05-01 15:00:09,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:09,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:10,312 - INFO - Response - Page 3:
2025-05-01 15:00:10,515 - INFO - 第 3 页获取到 100 条记录
2025-05-01 15:00:10,515 - INFO - Request Parameters - Page 4:
2025-05-01 15:00:10,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:10,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:11,109 - INFO - Response - Page 4:
2025-05-01 15:00:11,312 - INFO - 第 4 页获取到 100 条记录
2025-05-01 15:00:11,312 - INFO - Request Parameters - Page 5:
2025-05-01 15:00:11,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:11,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:11,828 - INFO - Response - Page 5:
2025-05-01 15:00:12,031 - INFO - 第 5 页获取到 100 条记录
2025-05-01 15:00:12,031 - INFO - Request Parameters - Page 6:
2025-05-01 15:00:12,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:12,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:12,484 - INFO - Response - Page 6:
2025-05-01 15:00:12,687 - INFO - 第 6 页获取到 100 条记录
2025-05-01 15:00:12,687 - INFO - Request Parameters - Page 7:
2025-05-01 15:00:12,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:12,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:13,140 - INFO - Response - Page 7:
2025-05-01 15:00:13,343 - INFO - 第 7 页获取到 70 条记录
2025-05-01 15:00:13,343 - INFO - 查询完成，共获取到 670 条记录
2025-05-01 15:00:13,343 - INFO - 获取到 670 条表单数据
2025-05-01 15:00:13,343 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-01 15:00:13,359 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 15:00:13,359 - INFO - 开始处理日期: 2025-03
2025-05-01 15:00:13,359 - INFO - Request Parameters - Page 1:
2025-05-01 15:00:13,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:13,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:13,859 - INFO - Response - Page 1:
2025-05-01 15:00:14,062 - INFO - 第 1 页获取到 100 条记录
2025-05-01 15:00:14,062 - INFO - Request Parameters - Page 2:
2025-05-01 15:00:14,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:14,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:14,593 - INFO - Response - Page 2:
2025-05-01 15:00:14,796 - INFO - 第 2 页获取到 100 条记录
2025-05-01 15:00:14,796 - INFO - Request Parameters - Page 3:
2025-05-01 15:00:14,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:14,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:15,218 - INFO - Response - Page 3:
2025-05-01 15:00:15,421 - INFO - 第 3 页获取到 100 条记录
2025-05-01 15:00:15,421 - INFO - Request Parameters - Page 4:
2025-05-01 15:00:15,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:15,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:15,843 - INFO - Response - Page 4:
2025-05-01 15:00:16,046 - INFO - 第 4 页获取到 100 条记录
2025-05-01 15:00:16,046 - INFO - Request Parameters - Page 5:
2025-05-01 15:00:16,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:16,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:16,500 - INFO - Response - Page 5:
2025-05-01 15:00:16,703 - INFO - 第 5 页获取到 100 条记录
2025-05-01 15:00:16,703 - INFO - Request Parameters - Page 6:
2025-05-01 15:00:16,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:16,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:17,218 - INFO - Response - Page 6:
2025-05-01 15:00:17,421 - INFO - 第 6 页获取到 100 条记录
2025-05-01 15:00:17,421 - INFO - Request Parameters - Page 7:
2025-05-01 15:00:17,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:17,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:17,843 - INFO - Response - Page 7:
2025-05-01 15:00:18,046 - INFO - 第 7 页获取到 61 条记录
2025-05-01 15:00:18,046 - INFO - 查询完成，共获取到 661 条记录
2025-05-01 15:00:18,046 - INFO - 获取到 661 条表单数据
2025-05-01 15:00:18,046 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-01 15:00:18,062 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 15:00:18,062 - INFO - 开始处理日期: 2025-04
2025-05-01 15:00:18,062 - INFO - Request Parameters - Page 1:
2025-05-01 15:00:18,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:18,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:18,562 - INFO - Response - Page 1:
2025-05-01 15:00:18,765 - INFO - 第 1 页获取到 100 条记录
2025-05-01 15:00:18,765 - INFO - Request Parameters - Page 2:
2025-05-01 15:00:18,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:18,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:19,250 - INFO - Response - Page 2:
2025-05-01 15:00:19,453 - INFO - 第 2 页获取到 100 条记录
2025-05-01 15:00:19,453 - INFO - Request Parameters - Page 3:
2025-05-01 15:00:19,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:19,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:19,937 - INFO - Response - Page 3:
2025-05-01 15:00:20,140 - INFO - 第 3 页获取到 100 条记录
2025-05-01 15:00:20,140 - INFO - Request Parameters - Page 4:
2025-05-01 15:00:20,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:20,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:20,687 - INFO - Response - Page 4:
2025-05-01 15:00:20,890 - INFO - 第 4 页获取到 100 条记录
2025-05-01 15:00:20,890 - INFO - Request Parameters - Page 5:
2025-05-01 15:00:20,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:20,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:21,328 - INFO - Response - Page 5:
2025-05-01 15:00:21,531 - INFO - 第 5 页获取到 100 条记录
2025-05-01 15:00:21,531 - INFO - Request Parameters - Page 6:
2025-05-01 15:00:21,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:21,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:22,062 - INFO - Response - Page 6:
2025-05-01 15:00:22,265 - INFO - 第 6 页获取到 100 条记录
2025-05-01 15:00:22,265 - INFO - Request Parameters - Page 7:
2025-05-01 15:00:22,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:22,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:22,671 - INFO - Response - Page 7:
2025-05-01 15:00:22,874 - INFO - 第 7 页获取到 27 条记录
2025-05-01 15:00:22,874 - INFO - 查询完成，共获取到 627 条记录
2025-05-01 15:00:22,874 - INFO - 获取到 627 条表单数据
2025-05-01 15:00:22,874 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-01 15:00:22,874 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-05-01 15:00:23,312 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-05-01 15:00:23,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159264.65, 'new_value': 188469.15}, {'field': 'total_amount', 'old_value': 770768.72, 'new_value': 799973.22}, {'field': 'order_count', 'old_value': 2528, 'new_value': 2622}]
2025-05-01 15:00:23,328 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M36
2025-05-01 15:00:23,781 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M36
2025-05-01 15:00:23,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204079.5, 'new_value': 230085.5}, {'field': 'total_amount', 'old_value': 308257.5, 'new_value': 334263.5}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-05-01 15:00:23,781 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-05-01 15:00:24,234 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-05-01 15:00:24,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161934.9, 'new_value': 162034.9}, {'field': 'total_amount', 'old_value': 161934.9, 'new_value': 162034.9}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-05-01 15:00:24,234 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-05-01 15:00:24,734 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-05-01 15:00:24,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 742317.41, 'new_value': 756290.41}, {'field': 'total_amount', 'old_value': 742317.41, 'new_value': 756290.41}, {'field': 'order_count', 'old_value': 660, 'new_value': 691}]
2025-05-01 15:00:24,734 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M22
2025-05-01 15:00:25,218 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M22
2025-05-01 15:00:25,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 312300.0, 'new_value': 313956.0}, {'field': 'total_amount', 'old_value': 355370.0, 'new_value': 357026.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 270}]
2025-05-01 15:00:25,234 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-05-01 15:00:25,734 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-05-01 15:00:25,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32607.0, 'new_value': 33612.0}, {'field': 'total_amount', 'old_value': 32607.0, 'new_value': 33612.0}, {'field': 'order_count', 'old_value': 3059, 'new_value': 3164}]
2025-05-01 15:00:25,734 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-05-01 15:00:26,187 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-05-01 15:00:26,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382330.0, 'new_value': 397128.0}, {'field': 'total_amount', 'old_value': 382330.0, 'new_value': 397128.0}, {'field': 'order_count', 'old_value': 9502, 'new_value': 9737}]
2025-05-01 15:00:26,187 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-05-01 15:00:26,624 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-05-01 15:00:26,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103253.0, 'new_value': 109841.0}, {'field': 'total_amount', 'old_value': 103253.0, 'new_value': 109841.0}, {'field': 'order_count', 'old_value': 703, 'new_value': 738}]
2025-05-01 15:00:26,624 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-05-01 15:00:27,031 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-05-01 15:00:27,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 446925.32, 'new_value': 535463.32}, {'field': 'total_amount', 'old_value': 446925.32, 'new_value': 535463.32}, {'field': 'order_count', 'old_value': 81, 'new_value': 106}]
2025-05-01 15:00:27,031 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-05-01 15:00:27,499 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-05-01 15:00:27,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33562.68, 'new_value': 34697.68}, {'field': 'total_amount', 'old_value': 33562.68, 'new_value': 34697.68}, {'field': 'order_count', 'old_value': 2925, 'new_value': 3023}]
2025-05-01 15:00:27,499 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-05-01 15:00:27,859 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-05-01 15:00:27,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294547.96, 'new_value': 311591.2}, {'field': 'total_amount', 'old_value': 297571.96, 'new_value': 314615.2}, {'field': 'order_count', 'old_value': 616, 'new_value': 658}]
2025-05-01 15:00:27,859 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-05-01 15:00:28,343 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-05-01 15:00:28,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130542.0, 'new_value': 136467.0}, {'field': 'total_amount', 'old_value': 130542.0, 'new_value': 136467.0}, {'field': 'order_count', 'old_value': 4612, 'new_value': 4770}]
2025-05-01 15:00:28,343 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME8
2025-05-01 15:00:28,843 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME8
2025-05-01 15:00:28,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225295.0, 'new_value': 227095.0}, {'field': 'total_amount', 'old_value': 225295.0, 'new_value': 227095.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-01 15:00:28,843 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-05-01 15:00:29,265 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-05-01 15:00:29,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165237.91, 'new_value': 172342.23}, {'field': 'total_amount', 'old_value': 165237.91, 'new_value': 172342.23}, {'field': 'order_count', 'old_value': 14474, 'new_value': 15040}]
2025-05-01 15:00:29,265 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM8
2025-05-01 15:00:29,749 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM8
2025-05-01 15:00:29,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29610.0, 'new_value': 31310.0}, {'field': 'total_amount', 'old_value': 29610.0, 'new_value': 31310.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-01 15:00:29,749 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR8
2025-05-01 15:00:30,140 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR8
2025-05-01 15:00:30,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9322.21, 'new_value': 9470.21}, {'field': 'total_amount', 'old_value': 10762.21, 'new_value': 10910.21}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-05-01 15:00:30,140 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-05-01 15:00:30,546 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-05-01 15:00:30,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12843.33, 'new_value': 13671.33}, {'field': 'total_amount', 'old_value': 12843.33, 'new_value': 13671.33}, {'field': 'order_count', 'old_value': 347, 'new_value': 368}]
2025-05-01 15:00:30,546 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-05-01 15:00:31,046 - INFO - 更新表单数据成功: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-05-01 15:00:31,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1269700.0, 'new_value': 1388500.0}, {'field': 'total_amount', 'old_value': 1269700.0, 'new_value': 1388500.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-01 15:00:31,046 - INFO - 日期 2025-04 处理完成 - 更新: 18 条，插入: 0 条，错误: 0 条
2025-05-01 15:00:31,046 - INFO - 开始处理日期: 2025-05
2025-05-01 15:00:31,046 - INFO - Request Parameters - Page 1:
2025-05-01 15:00:31,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:00:31,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:00:31,265 - INFO - Response - Page 1:
2025-05-01 15:00:31,468 - INFO - 第 1 页获取到 1 条记录
2025-05-01 15:00:31,468 - INFO - 查询完成，共获取到 1 条记录
2025-05-01 15:00:31,468 - INFO - 获取到 1 条表单数据
2025-05-01 15:00:31,468 - INFO - 当前日期 2025-05 有 1 条MySQL数据需要处理
2025-05-01 15:00:31,468 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 15:00:31,468 - INFO - 数据同步完成！更新: 18 条，插入: 0 条，错误: 0 条
2025-05-01 15:00:31,468 - INFO - =================同步完成====================
2025-05-01 18:00:02,011 - INFO - =================使用默认全量同步=============
2025-05-01 18:00:03,151 - INFO - MySQL查询成功，共获取 2642 条记录
2025-05-01 18:00:03,151 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-01 18:00:03,183 - INFO - 开始处理日期: 2025-01
2025-05-01 18:00:03,183 - INFO - Request Parameters - Page 1:
2025-05-01 18:00:03,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:03,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:04,104 - INFO - Response - Page 1:
2025-05-01 18:00:04,308 - INFO - 第 1 页获取到 100 条记录
2025-05-01 18:00:04,308 - INFO - Request Parameters - Page 2:
2025-05-01 18:00:04,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:04,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:04,808 - INFO - Response - Page 2:
2025-05-01 18:00:05,011 - INFO - 第 2 页获取到 100 条记录
2025-05-01 18:00:05,011 - INFO - Request Parameters - Page 3:
2025-05-01 18:00:05,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:05,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:05,667 - INFO - Response - Page 3:
2025-05-01 18:00:05,886 - INFO - 第 3 页获取到 100 条记录
2025-05-01 18:00:05,886 - INFO - Request Parameters - Page 4:
2025-05-01 18:00:05,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:05,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:06,339 - INFO - Response - Page 4:
2025-05-01 18:00:06,542 - INFO - 第 4 页获取到 100 条记录
2025-05-01 18:00:06,542 - INFO - Request Parameters - Page 5:
2025-05-01 18:00:06,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:06,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:06,995 - INFO - Response - Page 5:
2025-05-01 18:00:07,198 - INFO - 第 5 页获取到 100 条记录
2025-05-01 18:00:07,198 - INFO - Request Parameters - Page 6:
2025-05-01 18:00:07,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:07,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:07,651 - INFO - Response - Page 6:
2025-05-01 18:00:07,854 - INFO - 第 6 页获取到 100 条记录
2025-05-01 18:00:07,854 - INFO - Request Parameters - Page 7:
2025-05-01 18:00:07,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:07,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:08,323 - INFO - Response - Page 7:
2025-05-01 18:00:08,526 - INFO - 第 7 页获取到 82 条记录
2025-05-01 18:00:08,526 - INFO - 查询完成，共获取到 682 条记录
2025-05-01 18:00:08,526 - INFO - 获取到 682 条表单数据
2025-05-01 18:00:08,526 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-01 18:00:08,542 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 18:00:08,542 - INFO - 开始处理日期: 2025-02
2025-05-01 18:00:08,542 - INFO - Request Parameters - Page 1:
2025-05-01 18:00:08,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:08,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:09,026 - INFO - Response - Page 1:
2025-05-01 18:00:09,229 - INFO - 第 1 页获取到 100 条记录
2025-05-01 18:00:09,229 - INFO - Request Parameters - Page 2:
2025-05-01 18:00:09,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:09,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:09,729 - INFO - Response - Page 2:
2025-05-01 18:00:09,933 - INFO - 第 2 页获取到 100 条记录
2025-05-01 18:00:09,933 - INFO - Request Parameters - Page 3:
2025-05-01 18:00:09,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:09,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:10,386 - INFO - Response - Page 3:
2025-05-01 18:00:10,589 - INFO - 第 3 页获取到 100 条记录
2025-05-01 18:00:10,589 - INFO - Request Parameters - Page 4:
2025-05-01 18:00:10,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:10,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:11,073 - INFO - Response - Page 4:
2025-05-01 18:00:11,276 - INFO - 第 4 页获取到 100 条记录
2025-05-01 18:00:11,276 - INFO - Request Parameters - Page 5:
2025-05-01 18:00:11,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:11,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:11,776 - INFO - Response - Page 5:
2025-05-01 18:00:11,979 - INFO - 第 5 页获取到 100 条记录
2025-05-01 18:00:11,979 - INFO - Request Parameters - Page 6:
2025-05-01 18:00:11,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:11,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:12,464 - INFO - Response - Page 6:
2025-05-01 18:00:12,667 - INFO - 第 6 页获取到 100 条记录
2025-05-01 18:00:12,667 - INFO - Request Parameters - Page 7:
2025-05-01 18:00:12,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:12,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:13,104 - INFO - Response - Page 7:
2025-05-01 18:00:13,308 - INFO - 第 7 页获取到 70 条记录
2025-05-01 18:00:13,308 - INFO - 查询完成，共获取到 670 条记录
2025-05-01 18:00:13,308 - INFO - 获取到 670 条表单数据
2025-05-01 18:00:13,308 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-01 18:00:13,323 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 18:00:13,323 - INFO - 开始处理日期: 2025-03
2025-05-01 18:00:13,323 - INFO - Request Parameters - Page 1:
2025-05-01 18:00:13,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:13,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:13,808 - INFO - Response - Page 1:
2025-05-01 18:00:14,011 - INFO - 第 1 页获取到 100 条记录
2025-05-01 18:00:14,011 - INFO - Request Parameters - Page 2:
2025-05-01 18:00:14,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:14,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:14,526 - INFO - Response - Page 2:
2025-05-01 18:00:14,729 - INFO - 第 2 页获取到 100 条记录
2025-05-01 18:00:14,729 - INFO - Request Parameters - Page 3:
2025-05-01 18:00:14,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:14,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:15,198 - INFO - Response - Page 3:
2025-05-01 18:00:15,401 - INFO - 第 3 页获取到 100 条记录
2025-05-01 18:00:15,401 - INFO - Request Parameters - Page 4:
2025-05-01 18:00:15,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:15,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:15,901 - INFO - Response - Page 4:
2025-05-01 18:00:16,104 - INFO - 第 4 页获取到 100 条记录
2025-05-01 18:00:16,104 - INFO - Request Parameters - Page 5:
2025-05-01 18:00:16,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:16,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:16,604 - INFO - Response - Page 5:
2025-05-01 18:00:16,807 - INFO - 第 5 页获取到 100 条记录
2025-05-01 18:00:16,807 - INFO - Request Parameters - Page 6:
2025-05-01 18:00:16,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:16,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:17,307 - INFO - Response - Page 6:
2025-05-01 18:00:17,511 - INFO - 第 6 页获取到 100 条记录
2025-05-01 18:00:17,511 - INFO - Request Parameters - Page 7:
2025-05-01 18:00:17,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:17,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:17,886 - INFO - Response - Page 7:
2025-05-01 18:00:18,089 - INFO - 第 7 页获取到 61 条记录
2025-05-01 18:00:18,089 - INFO - 查询完成，共获取到 661 条记录
2025-05-01 18:00:18,089 - INFO - 获取到 661 条表单数据
2025-05-01 18:00:18,089 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-01 18:00:18,104 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 18:00:18,104 - INFO - 开始处理日期: 2025-04
2025-05-01 18:00:18,104 - INFO - Request Parameters - Page 1:
2025-05-01 18:00:18,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:18,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:18,589 - INFO - Response - Page 1:
2025-05-01 18:00:18,792 - INFO - 第 1 页获取到 100 条记录
2025-05-01 18:00:18,792 - INFO - Request Parameters - Page 2:
2025-05-01 18:00:18,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:18,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:19,604 - INFO - Response - Page 2:
2025-05-01 18:00:19,807 - INFO - 第 2 页获取到 100 条记录
2025-05-01 18:00:19,807 - INFO - Request Parameters - Page 3:
2025-05-01 18:00:19,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:19,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:20,276 - INFO - Response - Page 3:
2025-05-01 18:00:20,479 - INFO - 第 3 页获取到 100 条记录
2025-05-01 18:00:20,479 - INFO - Request Parameters - Page 4:
2025-05-01 18:00:20,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:20,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:20,901 - INFO - Response - Page 4:
2025-05-01 18:00:21,104 - INFO - 第 4 页获取到 100 条记录
2025-05-01 18:00:21,104 - INFO - Request Parameters - Page 5:
2025-05-01 18:00:21,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:21,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:21,589 - INFO - Response - Page 5:
2025-05-01 18:00:21,792 - INFO - 第 5 页获取到 100 条记录
2025-05-01 18:00:21,792 - INFO - Request Parameters - Page 6:
2025-05-01 18:00:21,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:21,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:22,276 - INFO - Response - Page 6:
2025-05-01 18:00:22,479 - INFO - 第 6 页获取到 100 条记录
2025-05-01 18:00:22,479 - INFO - Request Parameters - Page 7:
2025-05-01 18:00:22,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:22,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:22,839 - INFO - Response - Page 7:
2025-05-01 18:00:23,042 - INFO - 第 7 页获取到 27 条记录
2025-05-01 18:00:23,042 - INFO - 查询完成，共获取到 627 条记录
2025-05-01 18:00:23,042 - INFO - 获取到 627 条表单数据
2025-05-01 18:00:23,042 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-01 18:00:23,057 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-05-01 18:00:23,479 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-05-01 18:00:23,479 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162034.9, 'new_value': 168971.9}, {'field': 'total_amount', 'old_value': 162034.9, 'new_value': 168971.9}, {'field': 'order_count', 'old_value': 56, 'new_value': 57}]
2025-05-01 18:00:23,479 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M42
2025-05-01 18:00:23,948 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M42
2025-05-01 18:00:23,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 864974.0, 'new_value': 902560.0}, {'field': 'total_amount', 'old_value': 864974.0, 'new_value': 902560.0}, {'field': 'order_count', 'old_value': 37446, 'new_value': 37482}]
2025-05-01 18:00:23,964 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-05-01 18:00:24,464 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-05-01 18:00:24,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33612.0, 'new_value': 33718.0}, {'field': 'total_amount', 'old_value': 33612.0, 'new_value': 33718.0}, {'field': 'order_count', 'old_value': 3164, 'new_value': 4321}]
2025-05-01 18:00:24,464 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-05-01 18:00:24,901 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-05-01 18:00:24,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 397128.0, 'new_value': 405717.0}, {'field': 'total_amount', 'old_value': 397128.0, 'new_value': 405717.0}, {'field': 'order_count', 'old_value': 9737, 'new_value': 9895}]
2025-05-01 18:00:24,901 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-05-01 18:00:25,323 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-05-01 18:00:25,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109841.0, 'new_value': 113909.0}, {'field': 'total_amount', 'old_value': 109841.0, 'new_value': 113909.0}, {'field': 'order_count', 'old_value': 738, 'new_value': 768}]
2025-05-01 18:00:25,323 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-05-01 18:00:25,745 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-05-01 18:00:25,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92355.0, 'new_value': 93042.0}, {'field': 'total_amount', 'old_value': 92355.0, 'new_value': 93042.0}, {'field': 'order_count', 'old_value': 1715, 'new_value': 1740}]
2025-05-01 18:00:25,761 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-05-01 18:00:26,214 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-05-01 18:00:26,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 764500.0, 'new_value': 778500.0}, {'field': 'total_amount', 'old_value': 764500.0, 'new_value': 778500.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-01 18:00:26,214 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV7
2025-05-01 18:00:26,714 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV7
2025-05-01 18:00:26,714 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189108.0, 'new_value': 191488.0}, {'field': 'total_amount', 'old_value': 189108.0, 'new_value': 191488.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-01 18:00:26,714 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-05-01 18:00:27,182 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-05-01 18:00:27,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18211.7, 'new_value': 19149.7}, {'field': 'total_amount', 'old_value': 18211.7, 'new_value': 19149.7}, {'field': 'order_count', 'old_value': 72, 'new_value': 76}]
2025-05-01 18:00:27,182 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-05-01 18:00:27,636 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-05-01 18:00:27,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34697.68, 'new_value': 35855.68}, {'field': 'total_amount', 'old_value': 34697.68, 'new_value': 35855.68}, {'field': 'order_count', 'old_value': 3023, 'new_value': 3098}]
2025-05-01 18:00:27,636 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-05-01 18:00:28,057 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-05-01 18:00:28,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 311591.2, 'new_value': 317108.7}, {'field': 'total_amount', 'old_value': 314615.2, 'new_value': 320132.7}, {'field': 'order_count', 'old_value': 658, 'new_value': 684}]
2025-05-01 18:00:28,057 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M48
2025-05-01 18:00:28,573 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M48
2025-05-01 18:00:28,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68000.0, 'new_value': 71000.0}, {'field': 'total_amount', 'old_value': 68000.0, 'new_value': 71000.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-01 18:00:28,573 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M68
2025-05-01 18:00:29,057 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M68
2025-05-01 18:00:29,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14778.0, 'new_value': 16578.0}, {'field': 'total_amount', 'old_value': 14778.0, 'new_value': 16578.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-01 18:00:29,057 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-05-01 18:00:29,542 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-05-01 18:00:29,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49543.0, 'new_value': 52260.6}, {'field': 'total_amount', 'old_value': 49543.0, 'new_value': 52260.6}, {'field': 'order_count', 'old_value': 555, 'new_value': 591}]
2025-05-01 18:00:29,542 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-05-01 18:00:29,995 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-05-01 18:00:29,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136467.0, 'new_value': 141246.0}, {'field': 'total_amount', 'old_value': 136467.0, 'new_value': 141246.0}, {'field': 'order_count', 'old_value': 4770, 'new_value': 4922}]
2025-05-01 18:00:29,995 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-05-01 18:00:30,432 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-05-01 18:00:30,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177091.25, 'new_value': 186638.03}, {'field': 'total_amount', 'old_value': 183486.25, 'new_value': 193033.03}, {'field': 'order_count', 'old_value': 1073, 'new_value': 1135}]
2025-05-01 18:00:30,432 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB8
2025-05-01 18:00:30,886 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB8
2025-05-01 18:00:30,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11247.0, 'new_value': 13077.0}, {'field': 'total_amount', 'old_value': 11247.0, 'new_value': 13077.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-01 18:00:30,886 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD8
2025-05-01 18:00:31,261 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD8
2025-05-01 18:00:31,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62656.0, 'new_value': 71867.0}, {'field': 'total_amount', 'old_value': 62657.0, 'new_value': 71868.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-01 18:00:31,261 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-05-01 18:00:31,729 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-05-01 18:00:31,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1532000.0, 'new_value': 1580000.0}, {'field': 'total_amount', 'old_value': 1532000.0, 'new_value': 1580000.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-01 18:00:31,729 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH8
2025-05-01 18:00:32,245 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH8
2025-05-01 18:00:32,245 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34744.8, 'new_value': 37329.8}, {'field': 'total_amount', 'old_value': 68961.4, 'new_value': 71546.4}, {'field': 'order_count', 'old_value': 5449, 'new_value': 5593}]
2025-05-01 18:00:32,245 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-05-01 18:00:32,698 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-05-01 18:00:32,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6813642.0, 'new_value': 7025844.0}, {'field': 'total_amount', 'old_value': 6813642.0, 'new_value': 7025844.0}, {'field': 'order_count', 'old_value': 120054, 'new_value': 123861}]
2025-05-01 18:00:32,698 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-05-01 18:00:33,120 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-05-01 18:00:33,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65298.0, 'new_value': 68137.0}, {'field': 'total_amount', 'old_value': 65298.0, 'new_value': 68137.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 67}]
2025-05-01 18:00:33,120 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-05-01 18:00:33,557 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-05-01 18:00:33,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13671.33, 'new_value': 14342.33}, {'field': 'total_amount', 'old_value': 13671.33, 'new_value': 14342.33}, {'field': 'order_count', 'old_value': 368, 'new_value': 391}]
2025-05-01 18:00:33,557 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-05-01 18:00:34,026 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-05-01 18:00:34,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44934.7, 'new_value': 45911.7}, {'field': 'total_amount', 'old_value': 44934.7, 'new_value': 45911.7}, {'field': 'order_count', 'old_value': 499, 'new_value': 511}]
2025-05-01 18:00:34,026 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-05-01 18:00:34,495 - INFO - 更新表单数据成功: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-05-01 18:00:34,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1388500.0, 'new_value': 1442500.0}, {'field': 'total_amount', 'old_value': 1388500.0, 'new_value': 1442500.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-01 18:00:34,511 - INFO - 日期 2025-04 处理完成 - 更新: 25 条，插入: 0 条，错误: 0 条
2025-05-01 18:00:34,511 - INFO - 开始处理日期: 2025-05
2025-05-01 18:00:34,511 - INFO - Request Parameters - Page 1:
2025-05-01 18:00:34,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:00:34,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:00:34,745 - INFO - Response - Page 1:
2025-05-01 18:00:34,948 - INFO - 第 1 页获取到 1 条记录
2025-05-01 18:00:34,948 - INFO - 查询完成，共获取到 1 条记录
2025-05-01 18:00:34,948 - INFO - 获取到 1 条表单数据
2025-05-01 18:00:34,948 - INFO - 当前日期 2025-05 有 2 条MySQL数据需要处理
2025-05-01 18:00:34,948 - INFO - 开始批量插入 1 条新记录
2025-05-01 18:00:35,120 - INFO - 批量插入响应状态码: 200
2025-05-01 18:00:35,120 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 10:00:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4E4DB9B0-021F-7A38-9FA4-6F33E7F4F7A5', 'x-acs-trace-id': '7745930a8558c6f8bb8b654d69d4bf1d', 'etag': '6YGp7CU8eBERxCGNqx3ghdg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 18:00:35,120 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4']}
2025-05-01 18:00:35,120 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-01 18:00:35,120 - INFO - 成功插入的数据ID: ['FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4']
2025-05-01 18:00:38,135 - INFO - 批量插入完成，共 1 条记录
2025-05-01 18:00:38,135 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-01 18:00:38,135 - INFO - 数据同步完成！更新: 25 条，插入: 1 条，错误: 0 条
2025-05-01 18:00:38,135 - INFO - =================同步完成====================
2025-05-01 21:00:01,942 - INFO - =================使用默认全量同步=============
2025-05-01 21:00:03,099 - INFO - MySQL查询成功，共获取 2645 条记录
2025-05-01 21:00:03,099 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-01 21:00:03,130 - INFO - 开始处理日期: 2025-01
2025-05-01 21:00:03,130 - INFO - Request Parameters - Page 1:
2025-05-01 21:00:03,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:03,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:04,052 - INFO - Response - Page 1:
2025-05-01 21:00:04,255 - INFO - 第 1 页获取到 100 条记录
2025-05-01 21:00:04,255 - INFO - Request Parameters - Page 2:
2025-05-01 21:00:04,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:04,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:04,771 - INFO - Response - Page 2:
2025-05-01 21:00:04,974 - INFO - 第 2 页获取到 100 条记录
2025-05-01 21:00:04,974 - INFO - Request Parameters - Page 3:
2025-05-01 21:00:04,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:04,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:05,536 - INFO - Response - Page 3:
2025-05-01 21:00:05,739 - INFO - 第 3 页获取到 100 条记录
2025-05-01 21:00:05,739 - INFO - Request Parameters - Page 4:
2025-05-01 21:00:05,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:05,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:06,208 - INFO - Response - Page 4:
2025-05-01 21:00:06,411 - INFO - 第 4 页获取到 100 条记录
2025-05-01 21:00:06,411 - INFO - Request Parameters - Page 5:
2025-05-01 21:00:06,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:06,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:06,911 - INFO - Response - Page 5:
2025-05-01 21:00:07,114 - INFO - 第 5 页获取到 100 条记录
2025-05-01 21:00:07,114 - INFO - Request Parameters - Page 6:
2025-05-01 21:00:07,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:07,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:07,583 - INFO - Response - Page 6:
2025-05-01 21:00:07,786 - INFO - 第 6 页获取到 100 条记录
2025-05-01 21:00:07,786 - INFO - Request Parameters - Page 7:
2025-05-01 21:00:07,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:07,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:08,271 - INFO - Response - Page 7:
2025-05-01 21:00:08,474 - INFO - 第 7 页获取到 82 条记录
2025-05-01 21:00:08,474 - INFO - 查询完成，共获取到 682 条记录
2025-05-01 21:00:08,474 - INFO - 获取到 682 条表单数据
2025-05-01 21:00:08,474 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-01 21:00:08,489 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 21:00:08,489 - INFO - 开始处理日期: 2025-02
2025-05-01 21:00:08,489 - INFO - Request Parameters - Page 1:
2025-05-01 21:00:08,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:08,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:09,302 - INFO - Response - Page 1:
2025-05-01 21:00:09,505 - INFO - 第 1 页获取到 100 条记录
2025-05-01 21:00:09,505 - INFO - Request Parameters - Page 2:
2025-05-01 21:00:09,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:09,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:09,958 - INFO - Response - Page 2:
2025-05-01 21:00:10,161 - INFO - 第 2 页获取到 100 条记录
2025-05-01 21:00:10,161 - INFO - Request Parameters - Page 3:
2025-05-01 21:00:10,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:10,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:10,630 - INFO - Response - Page 3:
2025-05-01 21:00:10,833 - INFO - 第 3 页获取到 100 条记录
2025-05-01 21:00:10,833 - INFO - Request Parameters - Page 4:
2025-05-01 21:00:10,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:10,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:11,302 - INFO - Response - Page 4:
2025-05-01 21:00:11,505 - INFO - 第 4 页获取到 100 条记录
2025-05-01 21:00:11,505 - INFO - Request Parameters - Page 5:
2025-05-01 21:00:11,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:11,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:12,005 - INFO - Response - Page 5:
2025-05-01 21:00:12,208 - INFO - 第 5 页获取到 100 条记录
2025-05-01 21:00:12,208 - INFO - Request Parameters - Page 6:
2025-05-01 21:00:12,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:12,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:12,661 - INFO - Response - Page 6:
2025-05-01 21:00:12,864 - INFO - 第 6 页获取到 100 条记录
2025-05-01 21:00:12,864 - INFO - Request Parameters - Page 7:
2025-05-01 21:00:12,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:12,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:13,255 - INFO - Response - Page 7:
2025-05-01 21:00:13,458 - INFO - 第 7 页获取到 70 条记录
2025-05-01 21:00:13,458 - INFO - 查询完成，共获取到 670 条记录
2025-05-01 21:00:13,458 - INFO - 获取到 670 条表单数据
2025-05-01 21:00:13,458 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-01 21:00:13,474 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 21:00:13,474 - INFO - 开始处理日期: 2025-03
2025-05-01 21:00:13,474 - INFO - Request Parameters - Page 1:
2025-05-01 21:00:13,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:13,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:13,989 - INFO - Response - Page 1:
2025-05-01 21:00:14,192 - INFO - 第 1 页获取到 100 条记录
2025-05-01 21:00:14,192 - INFO - Request Parameters - Page 2:
2025-05-01 21:00:14,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:14,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:14,645 - INFO - Response - Page 2:
2025-05-01 21:00:14,849 - INFO - 第 2 页获取到 100 条记录
2025-05-01 21:00:14,849 - INFO - Request Parameters - Page 3:
2025-05-01 21:00:14,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:14,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:15,333 - INFO - Response - Page 3:
2025-05-01 21:00:15,536 - INFO - 第 3 页获取到 100 条记录
2025-05-01 21:00:15,536 - INFO - Request Parameters - Page 4:
2025-05-01 21:00:15,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:15,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:16,052 - INFO - Response - Page 4:
2025-05-01 21:00:16,255 - INFO - 第 4 页获取到 100 条记录
2025-05-01 21:00:16,255 - INFO - Request Parameters - Page 5:
2025-05-01 21:00:16,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:16,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:16,849 - INFO - Response - Page 5:
2025-05-01 21:00:17,052 - INFO - 第 5 页获取到 100 条记录
2025-05-01 21:00:17,052 - INFO - Request Parameters - Page 6:
2025-05-01 21:00:17,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:17,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:17,489 - INFO - Response - Page 6:
2025-05-01 21:00:17,692 - INFO - 第 6 页获取到 100 条记录
2025-05-01 21:00:17,692 - INFO - Request Parameters - Page 7:
2025-05-01 21:00:17,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:17,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:18,224 - INFO - Response - Page 7:
2025-05-01 21:00:18,427 - INFO - 第 7 页获取到 61 条记录
2025-05-01 21:00:18,427 - INFO - 查询完成，共获取到 661 条记录
2025-05-01 21:00:18,427 - INFO - 获取到 661 条表单数据
2025-05-01 21:00:18,427 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-01 21:00:18,442 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 21:00:18,442 - INFO - 开始处理日期: 2025-04
2025-05-01 21:00:18,442 - INFO - Request Parameters - Page 1:
2025-05-01 21:00:18,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:18,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:18,958 - INFO - Response - Page 1:
2025-05-01 21:00:19,161 - INFO - 第 1 页获取到 100 条记录
2025-05-01 21:00:19,161 - INFO - Request Parameters - Page 2:
2025-05-01 21:00:19,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:19,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:19,661 - INFO - Response - Page 2:
2025-05-01 21:00:19,864 - INFO - 第 2 页获取到 100 条记录
2025-05-01 21:00:19,864 - INFO - Request Parameters - Page 3:
2025-05-01 21:00:19,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:19,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:20,349 - INFO - Response - Page 3:
2025-05-01 21:00:20,552 - INFO - 第 3 页获取到 100 条记录
2025-05-01 21:00:20,552 - INFO - Request Parameters - Page 4:
2025-05-01 21:00:20,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:20,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:20,974 - INFO - Response - Page 4:
2025-05-01 21:00:21,177 - INFO - 第 4 页获取到 100 条记录
2025-05-01 21:00:21,177 - INFO - Request Parameters - Page 5:
2025-05-01 21:00:21,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:21,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:21,770 - INFO - Response - Page 5:
2025-05-01 21:00:21,974 - INFO - 第 5 页获取到 100 条记录
2025-05-01 21:00:21,974 - INFO - Request Parameters - Page 6:
2025-05-01 21:00:21,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:21,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:22,427 - INFO - Response - Page 6:
2025-05-01 21:00:22,630 - INFO - 第 6 页获取到 100 条记录
2025-05-01 21:00:22,630 - INFO - Request Parameters - Page 7:
2025-05-01 21:00:22,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:22,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:22,958 - INFO - Response - Page 7:
2025-05-01 21:00:23,161 - INFO - 第 7 页获取到 27 条记录
2025-05-01 21:00:23,161 - INFO - 查询完成，共获取到 627 条记录
2025-05-01 21:00:23,161 - INFO - 获取到 627 条表单数据
2025-05-01 21:00:23,161 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-01 21:00:23,177 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-01 21:00:23,177 - INFO - 开始处理日期: 2025-05
2025-05-01 21:00:23,177 - INFO - Request Parameters - Page 1:
2025-05-01 21:00:23,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:00:23,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:00:23,411 - INFO - Response - Page 1:
2025-05-01 21:00:23,614 - INFO - 第 1 页获取到 2 条记录
2025-05-01 21:00:23,614 - INFO - 查询完成，共获取到 2 条记录
2025-05-01 21:00:23,614 - INFO - 获取到 2 条表单数据
2025-05-01 21:00:23,614 - INFO - 当前日期 2025-05 有 5 条MySQL数据需要处理
2025-05-01 21:00:23,614 - INFO - 开始批量插入 3 条新记录
2025-05-01 21:00:23,770 - INFO - 批量插入响应状态码: 200
2025-05-01 21:00:23,770 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 13:00:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '415BE59E-1C13-7C3E-B514-E5045CCFDFEE', 'x-acs-trace-id': 'f3f54ef53e8df2d84e46d6c120767048', 'etag': '1PoM5UTroL5n3mBQG2mBoWg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 21:00:23,770 - INFO - 批量插入响应体: {'result': ['FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A', 'FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A', 'FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A']}
2025-05-01 21:00:23,770 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-01 21:00:23,770 - INFO - 成功插入的数据ID: ['FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A', 'FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A', 'FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A']
2025-05-01 21:00:26,786 - INFO - 批量插入完成，共 3 条记录
2025-05-01 21:00:26,786 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-01 21:00:26,786 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 0 条
2025-05-01 21:00:26,786 - INFO - =================同步完成====================
