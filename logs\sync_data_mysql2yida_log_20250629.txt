2025-06-29 01:30:33,587 - INFO - 使用默认增量同步（当天更新数据）
2025-06-29 01:30:33,587 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-29 01:30:33,602 - INFO - 查询参数: ('2025-06-29',)
2025-06-29 01:30:33,680 - INFO - MySQL查询成功，增量数据（日期: 2025-06-29），共获取 0 条记录
2025-06-29 01:30:33,680 - ERROR - 未获取到MySQL数据
2025-06-29 01:31:33,696 - INFO - 开始同步昨天与今天的销售数据: 2025-06-28 至 2025-06-29
2025-06-29 01:31:33,696 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-29 01:31:33,696 - INFO - 查询参数: ('2025-06-28', '2025-06-29')
2025-06-29 01:31:33,821 - INFO - MySQL查询成功，时间段: 2025-06-28 至 2025-06-29，共获取 84 条记录
2025-06-29 01:31:33,821 - INFO - 获取到 1 个日期需要处理: ['2025-06-28']
2025-06-29 01:31:33,821 - INFO - 开始处理日期: 2025-06-28
2025-06-29 01:31:33,821 - INFO - Request Parameters - Page 1:
2025-06-29 01:31:33,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 01:31:33,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 01:31:41,961 - ERROR - 处理日期 2025-06-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C08671E5-71AB-7246-9A92-9C3DE16FD112 Response: {'code': 'ServiceUnavailable', 'requestid': 'C08671E5-71AB-7246-9A92-9C3DE16FD112', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C08671E5-71AB-7246-9A92-9C3DE16FD112)
2025-06-29 01:31:41,961 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-29 01:31:41,961 - INFO - 同步完成
2025-06-29 04:30:33,811 - INFO - 使用默认增量同步（当天更新数据）
2025-06-29 04:30:33,811 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-29 04:30:33,811 - INFO - 查询参数: ('2025-06-29',)
2025-06-29 04:30:33,952 - INFO - MySQL查询成功，增量数据（日期: 2025-06-29），共获取 5 条记录
2025-06-29 04:30:33,952 - INFO - 获取到 1 个日期需要处理: ['2025-06-28']
2025-06-29 04:30:33,952 - INFO - 开始处理日期: 2025-06-28
2025-06-29 04:30:33,952 - INFO - Request Parameters - Page 1:
2025-06-29 04:30:33,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 04:30:33,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 04:30:42,092 - ERROR - 处理日期 2025-06-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D83DEE62-B760-7F7E-B4B4-A644F6A72483 Response: {'code': 'ServiceUnavailable', 'requestid': 'D83DEE62-B760-7F7E-B4B4-A644F6A72483', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D83DEE62-B760-7F7E-B4B4-A644F6A72483)
2025-06-29 04:30:42,092 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-29 04:31:42,108 - INFO - 开始同步昨天与今天的销售数据: 2025-06-28 至 2025-06-29
2025-06-29 04:31:42,108 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-29 04:31:42,108 - INFO - 查询参数: ('2025-06-28', '2025-06-29')
2025-06-29 04:31:42,233 - INFO - MySQL查询成功，时间段: 2025-06-28 至 2025-06-29，共获取 108 条记录
2025-06-29 04:31:42,233 - INFO - 获取到 1 个日期需要处理: ['2025-06-28']
2025-06-29 04:31:42,233 - INFO - 开始处理日期: 2025-06-28
2025-06-29 04:31:42,233 - INFO - Request Parameters - Page 1:
2025-06-29 04:31:42,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 04:31:42,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 04:31:48,389 - INFO - Response - Page 1:
2025-06-29 04:31:48,389 - INFO - 第 1 页获取到 50 条记录
2025-06-29 04:31:48,889 - INFO - Request Parameters - Page 2:
2025-06-29 04:31:48,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 04:31:48,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 04:31:49,545 - INFO - Response - Page 2:
2025-06-29 04:31:49,545 - INFO - 第 2 页获取到 27 条记录
2025-06-29 04:31:50,061 - INFO - 查询完成，共获取到 77 条记录
2025-06-29 04:31:50,061 - INFO - 获取到 77 条表单数据
2025-06-29 04:31:50,061 - INFO - 当前日期 2025-06-28 有 104 条MySQL数据需要处理
2025-06-29 04:31:50,061 - INFO - 开始批量插入 27 条新记录
2025-06-29 04:31:50,279 - INFO - 批量插入响应状态码: 200
2025-06-29 04:31:50,279 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 20:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1308', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EA8AE72F-993F-7DEF-BA77-2719A55DBD25', 'x-acs-trace-id': '3150d01d1456614177c393cd4dce18ae', 'etag': '1oAL+/3YzR5iMJfCt5lpcQQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 04:31:50,279 - INFO - 批量插入响应体: {'result': ['FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMFG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMGG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMHG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMIG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMJG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMKG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMLG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMMG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMNG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMOG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMPG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMQG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMRG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMSG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMTG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMUG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMVG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMWG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMXG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMYG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMZG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM0H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM1H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM2H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM3H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM4H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM5H']}
2025-06-29 04:31:50,279 - INFO - 批量插入表单数据成功，批次 1，共 27 条记录
2025-06-29 04:31:50,279 - INFO - 成功插入的数据ID: ['FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMFG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMGG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMHG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMIG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMJG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMKG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMLG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMMG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMNG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMOG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMPG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMQG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMRG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMSG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMTG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMUG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMVG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMWG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMXG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMYG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCMZG', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM0H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM1H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM2H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM3H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM4H', 'FINST-X3E66X811POWJIAA93KFXCDC6XS62FNM5PGCM5H']
2025-06-29 04:31:55,295 - INFO - 批量插入完成，共 27 条记录
2025-06-29 04:31:55,295 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 27 条，错误: 0 条
2025-06-29 04:31:55,295 - INFO - 数据同步完成！更新: 0 条，插入: 27 条，错误: 0 条
2025-06-29 04:31:55,295 - INFO - 同步完成
2025-06-29 07:30:33,894 - INFO - 使用默认增量同步（当天更新数据）
2025-06-29 07:30:33,894 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-29 07:30:33,894 - INFO - 查询参数: ('2025-06-29',)
2025-06-29 07:30:34,035 - INFO - MySQL查询成功，增量数据（日期: 2025-06-29），共获取 5 条记录
2025-06-29 07:30:34,035 - INFO - 获取到 1 个日期需要处理: ['2025-06-28']
2025-06-29 07:30:34,035 - INFO - 开始处理日期: 2025-06-28
2025-06-29 07:30:34,035 - INFO - Request Parameters - Page 1:
2025-06-29 07:30:34,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 07:30:34,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 07:30:42,144 - ERROR - 处理日期 2025-06-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0A4B1FB8-F9D6-70FC-A99E-296D5D83D57A Response: {'code': 'ServiceUnavailable', 'requestid': '0A4B1FB8-F9D6-70FC-A99E-296D5D83D57A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0A4B1FB8-F9D6-70FC-A99E-296D5D83D57A)
2025-06-29 07:30:42,144 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-29 07:31:42,159 - INFO - 开始同步昨天与今天的销售数据: 2025-06-28 至 2025-06-29
2025-06-29 07:31:42,159 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-29 07:31:42,159 - INFO - 查询参数: ('2025-06-28', '2025-06-29')
2025-06-29 07:31:42,284 - INFO - MySQL查询成功，时间段: 2025-06-28 至 2025-06-29，共获取 108 条记录
2025-06-29 07:31:42,284 - INFO - 获取到 1 个日期需要处理: ['2025-06-28']
2025-06-29 07:31:42,300 - INFO - 开始处理日期: 2025-06-28
2025-06-29 07:31:42,300 - INFO - Request Parameters - Page 1:
2025-06-29 07:31:42,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 07:31:42,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 07:31:43,019 - INFO - Response - Page 1:
2025-06-29 07:31:43,019 - INFO - 第 1 页获取到 50 条记录
2025-06-29 07:31:43,534 - INFO - Request Parameters - Page 2:
2025-06-29 07:31:43,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 07:31:43,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 07:31:44,206 - INFO - Response - Page 2:
2025-06-29 07:31:44,206 - INFO - 第 2 页获取到 50 条记录
2025-06-29 07:31:44,706 - INFO - Request Parameters - Page 3:
2025-06-29 07:31:44,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 07:31:44,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 07:31:50,581 - INFO - Response - Page 3:
2025-06-29 07:31:50,581 - INFO - 第 3 页获取到 4 条记录
2025-06-29 07:31:51,097 - INFO - 查询完成，共获取到 104 条记录
2025-06-29 07:31:51,097 - INFO - 获取到 104 条表单数据
2025-06-29 07:31:51,097 - INFO - 当前日期 2025-06-28 有 104 条MySQL数据需要处理
2025-06-29 07:31:51,097 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 07:31:51,097 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 07:31:51,097 - INFO - 同步完成
2025-06-29 10:30:34,072 - INFO - 使用默认增量同步（当天更新数据）
2025-06-29 10:30:34,072 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-29 10:30:34,072 - INFO - 查询参数: ('2025-06-29',)
2025-06-29 10:30:34,197 - INFO - MySQL查询成功，增量数据（日期: 2025-06-29），共获取 110 条记录
2025-06-29 10:30:34,197 - INFO - 获取到 3 个日期需要处理: ['2025-06-27', '2025-06-28', '2025-06-29']
2025-06-29 10:30:34,212 - INFO - 开始处理日期: 2025-06-27
2025-06-29 10:30:34,212 - INFO - Request Parameters - Page 1:
2025-06-29 10:30:34,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:30:34,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:30:40,181 - INFO - Response - Page 1:
2025-06-29 10:30:40,181 - INFO - 第 1 页获取到 50 条记录
2025-06-29 10:30:40,696 - INFO - Request Parameters - Page 2:
2025-06-29 10:30:40,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:30:40,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:30:48,821 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8C28592E-F8E4-7759-9D55-6D85BFF25416 Response: {'code': 'ServiceUnavailable', 'requestid': '8C28592E-F8E4-7759-9D55-6D85BFF25416', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8C28592E-F8E4-7759-9D55-6D85BFF25416)
2025-06-29 10:30:48,821 - INFO - 开始处理日期: 2025-06-28
2025-06-29 10:30:48,821 - INFO - Request Parameters - Page 1:
2025-06-29 10:30:48,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:30:48,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:30:49,493 - INFO - Response - Page 1:
2025-06-29 10:30:49,493 - INFO - 第 1 页获取到 50 条记录
2025-06-29 10:30:49,993 - INFO - Request Parameters - Page 2:
2025-06-29 10:30:49,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:30:49,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:30:50,806 - INFO - Response - Page 2:
2025-06-29 10:30:50,806 - INFO - 第 2 页获取到 50 条记录
2025-06-29 10:30:51,306 - INFO - Request Parameters - Page 3:
2025-06-29 10:30:51,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:30:51,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:30:51,821 - INFO - Response - Page 3:
2025-06-29 10:30:51,821 - INFO - 第 3 页获取到 4 条记录
2025-06-29 10:30:52,321 - INFO - 查询完成，共获取到 104 条记录
2025-06-29 10:30:52,321 - INFO - 获取到 104 条表单数据
2025-06-29 10:30:52,321 - INFO - 当前日期 2025-06-28 有 103 条MySQL数据需要处理
2025-06-29 10:30:52,321 - INFO - 开始批量插入 98 条新记录
2025-06-29 10:30:52,556 - INFO - 批量插入响应状态码: 200
2025-06-29 10:30:52,556 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 02:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-1237-7A6F-87E5-77C1716EF306', 'x-acs-trace-id': 'f906c936f10eac7236ec00a5fd76538f', 'etag': '25atoNfs6xYxjbDa3oSrkWA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 10:30:52,556 - INFO - 批量插入响应体: {'result': ['FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCMXW', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCMYW', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCMZW', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM0X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM1X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM2X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM3X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM4X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM5X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM6X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM7X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM8X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM9X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMAX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMBX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMCX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMDX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMEX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMFX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMGX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMHX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMIX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMJX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMKX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMLX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMMX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMNX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMOX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMPX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMQX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMRX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMSX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMTX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMUX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMVX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMWX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMXX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMYX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMZX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM0Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM1Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM2Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM3Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM4Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM5Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM6Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM7Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM8Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM9Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMAY']}
2025-06-29 10:30:52,556 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-29 10:30:52,556 - INFO - 成功插入的数据ID: ['FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCMXW', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCMYW', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCMZW', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM0X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM1X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM2X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM3X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCM4X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM5X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM6X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM7X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM8X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM9X', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMAX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMBX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMCX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMDX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMEX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMFX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMGX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMHX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMIX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMJX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMKX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMLX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMMX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMNX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMOX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMPX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMQX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMRX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMSX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMTX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMUX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMVX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMWX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMXX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMYX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMZX', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM0Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM1Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM2Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM3Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM4Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM5Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM6Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM7Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM8Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCM9Y', 'FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ20WCZ1HCMAY']
2025-06-29 10:30:57,821 - INFO - 批量插入响应状态码: 200
2025-06-29 10:30:57,837 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 02:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2316', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '54811621-2C94-7991-9F9E-58C9B1053ACB', 'x-acs-trace-id': 'aba0de5eb27f4c51c341f2aaf72fe9fc', 'etag': '2xVntKevhJYtP4tbD1XvSDw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 10:30:57,837 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93CYGZ1HCM7Q', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93CYGZ1HCM8Q', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM9Q', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMAQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMBQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMCQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMDQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMEQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMFQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMGQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMHQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMIQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMJQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMKQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMLQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMMQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMNQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMOQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMPQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMQQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMRQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMSQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMTQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMUQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMVQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMWQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMXQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMYQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMZQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM0R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM1R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM2R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM3R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM4R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM5R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM6R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM7R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM8R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM9R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMAR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMBR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMCR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMDR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMER', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMFR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMGR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMHR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMIR']}
2025-06-29 10:30:57,837 - INFO - 批量插入表单数据成功，批次 2，共 48 条记录
2025-06-29 10:30:57,837 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93CYGZ1HCM7Q', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93CYGZ1HCM8Q', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM9Q', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMAQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMBQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMCQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMDQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMEQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMFQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMGQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMHQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMIQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMJQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMKQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMLQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMMQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMNQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMOQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMPQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMQQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMRQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMSQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMTQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMUQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMVQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMWQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMXQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMYQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMZQ', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM0R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM1R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM2R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM3R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM4R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM5R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM6R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM7R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM8R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCM9R', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMAR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMBR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMCR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMDR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMER', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMFR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMGR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMHR', 'FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMIR']
2025-06-29 10:31:02,853 - INFO - 批量插入完成，共 98 条记录
2025-06-29 10:31:02,853 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 98 条，错误: 0 条
2025-06-29 10:31:02,853 - INFO - 开始处理日期: 2025-06-29
2025-06-29 10:31:02,853 - INFO - Request Parameters - Page 1:
2025-06-29 10:31:02,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:31:02,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:31:03,290 - INFO - Response - Page 1:
2025-06-29 10:31:03,290 - INFO - 查询完成，共获取到 0 条记录
2025-06-29 10:31:03,290 - INFO - 获取到 0 条表单数据
2025-06-29 10:31:03,290 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-06-29 10:31:03,290 - INFO - 开始批量插入 1 条新记录
2025-06-29 10:31:03,462 - INFO - 批量插入响应状态码: 200
2025-06-29 10:31:03,462 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 02:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-7C3E-766A-9FB8-9F9E987D6ED1', 'x-acs-trace-id': '7027923d65f15cbff6019679105af1a2', 'etag': '6Qd2Dpdyfcd026hP3UdtKIg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 10:31:03,462 - INFO - 批量插入响应体: {'result': ['FINST-RI766091U8OWGTMQBXRAV9M2V5ZC2VALZ1HCM6Y']}
2025-06-29 10:31:03,462 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-29 10:31:03,462 - INFO - 成功插入的数据ID: ['FINST-RI766091U8OWGTMQBXRAV9M2V5ZC2VALZ1HCM6Y']
2025-06-29 10:31:08,478 - INFO - 批量插入完成，共 1 条记录
2025-06-29 10:31:08,478 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-29 10:31:08,478 - INFO - 数据同步完成！更新: 0 条，插入: 99 条，错误: 1 条
2025-06-29 10:32:08,493 - INFO - 开始同步昨天与今天的销售数据: 2025-06-28 至 2025-06-29
2025-06-29 10:32:08,493 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-29 10:32:08,493 - INFO - 查询参数: ('2025-06-28', '2025-06-29')
2025-06-29 10:32:08,633 - INFO - MySQL查询成功，时间段: 2025-06-28 至 2025-06-29，共获取 433 条记录
2025-06-29 10:32:08,633 - INFO - 获取到 2 个日期需要处理: ['2025-06-28', '2025-06-29']
2025-06-29 10:32:08,633 - INFO - 开始处理日期: 2025-06-28
2025-06-29 10:32:08,633 - INFO - Request Parameters - Page 1:
2025-06-29 10:32:08,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:32:08,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:32:09,430 - INFO - Response - Page 1:
2025-06-29 10:32:09,430 - INFO - 第 1 页获取到 50 条记录
2025-06-29 10:32:09,946 - INFO - Request Parameters - Page 2:
2025-06-29 10:32:09,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:32:09,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:32:10,665 - INFO - Response - Page 2:
2025-06-29 10:32:10,665 - INFO - 第 2 页获取到 50 条记录
2025-06-29 10:32:11,165 - INFO - Request Parameters - Page 3:
2025-06-29 10:32:11,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:32:11,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:32:11,837 - INFO - Response - Page 3:
2025-06-29 10:32:11,837 - INFO - 第 3 页获取到 50 条记录
2025-06-29 10:32:12,337 - INFO - Request Parameters - Page 4:
2025-06-29 10:32:12,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:32:12,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:32:12,993 - INFO - Response - Page 4:
2025-06-29 10:32:12,993 - INFO - 第 4 页获取到 50 条记录
2025-06-29 10:32:13,508 - INFO - Request Parameters - Page 5:
2025-06-29 10:32:13,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:32:13,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:32:13,946 - INFO - Response - Page 5:
2025-06-29 10:32:13,946 - INFO - 第 5 页获取到 2 条记录
2025-06-29 10:32:14,461 - INFO - 查询完成，共获取到 202 条记录
2025-06-29 10:32:14,461 - INFO - 获取到 202 条表单数据
2025-06-29 10:32:14,461 - INFO - 当前日期 2025-06-28 有 413 条MySQL数据需要处理
2025-06-29 10:32:14,461 - INFO - 开始批量插入 211 条新记录
2025-06-29 10:32:14,696 - INFO - 批量插入响应状态码: 200
2025-06-29 10:32:14,696 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 02:32:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '36C5333C-3D8C-7CA2-9FE1-AAC82B144B9C', 'x-acs-trace-id': 'b7ee7438cc93e10648f3e2e942a9b6f7', 'etag': '2cnPLawiG2JsLQq/9A/cV5A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 10:32:14,696 - INFO - 批量插入响应体: {'result': ['FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM2K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM3K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM4K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM5K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM6K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM7K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM8K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM9K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMAK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMBK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMCK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMDK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMEK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMFK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMGK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMHK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMIK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMJK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMKK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMLK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMMK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMNK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMOK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMPK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMQK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMRK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMSK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMTK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMUK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMVK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMWK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMXK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMYK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMZK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM0L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM1L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM2L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM3L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM4L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM5L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM6L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM7L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM8L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM9L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMAL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMBL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMCL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMDL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMEL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMFL']}
2025-06-29 10:32:14,696 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-29 10:32:14,696 - INFO - 成功插入的数据ID: ['FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM2K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM3K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM4K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM5K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM6K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM7K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM8K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM9K', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMAK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMBK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMCK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMDK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMEK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMFK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMGK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMHK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMIK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMJK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMKK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMLK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMMK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMNK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMOK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMPK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMQK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMRK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMSK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMTK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMUK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMVK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMWK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMXK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMYK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMZK', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM0L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM1L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM2L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM3L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM4L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM5L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM6L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM7L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM8L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCM9L', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMAL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMBL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMCL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMDL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMEL', 'FINST-X2F66HC1Q8OWEB3GCO2X7CRFA5RQ2L9412HCMFL']
2025-06-29 10:32:19,930 - INFO - 批量插入响应状态码: 200
2025-06-29 10:32:19,930 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 02:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '94FA5315-2267-799D-9CB3-20DDA1A2EBF5', 'x-acs-trace-id': 'a70ff1cafd828a23bac54b10739a09b1', 'etag': '2ybzmCgtupiL5H2dVdmJwIQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 10:32:19,930 - INFO - 批量插入响应体: {'result': ['FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCML91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMM91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMN91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMO91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMP91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMQ91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMR91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMS91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMT91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMU91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMV91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMW91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMX91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMY91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMZ91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM0A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM1A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM2A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM3A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM4A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM5A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM6A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM7A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM8A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM9A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMAA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMBA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMCA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMDA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMEA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMFA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMGA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMHA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMIA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMJA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMKA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMLA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMMA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMNA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMOA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMPA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMQA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMRA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMSA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMTA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMUA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMVA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMWA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMXA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMYA1']}
2025-06-29 10:32:19,930 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-29 10:32:19,930 - INFO - 成功插入的数据ID: ['FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCML91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMM91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMN91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMO91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMP91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMQ91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMR91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMS91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMT91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMU91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMV91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMW91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMX91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMY91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMZ91', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM0A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM1A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM2A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM3A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM4A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM5A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM6A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM7A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM8A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCM9A1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMAA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMBA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMCA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMDA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMEA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMFA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMGA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMHA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMIA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMJA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMKA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMLA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMMA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMNA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMOA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMPA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMQA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMRA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMSA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMTA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMUA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMVA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMWA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMXA1', 'FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMYA1']
2025-06-29 10:32:25,196 - INFO - 批量插入响应状态码: 200
2025-06-29 10:32:25,196 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 02:32:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D021C70F-F70D-764B-9461-4B6534B0496F', 'x-acs-trace-id': '34702cbe7249a5b42a70861648801d99', 'etag': '2JpUh1ry9CKuwPYZQfb4L+A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 10:32:25,196 - INFO - 批量插入响应体: {'result': ['FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMGK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMHK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMIK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMJK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMKK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMLK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMMK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMNK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMOK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMPK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMQK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMRK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMSK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMTK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMUK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMVK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMWK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMXK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMYK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMZK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM0L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM1L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM2L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM3L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM4L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM5L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM6L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM7L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM8L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM9L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMAL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMBL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMCL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMDL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMEL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMFL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMGL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMHL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMIL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMJL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMKL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMLL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMML', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMNL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMOL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMPL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMQL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMRL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMSL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMTL']}
2025-06-29 10:32:25,196 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-29 10:32:25,196 - INFO - 成功插入的数据ID: ['FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMGK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMHK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMIK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMJK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMKK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMLK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMMK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMNK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMOK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMPK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMQK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMRK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMSK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMTK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMUK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMVK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMWK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMXK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMYK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMZK', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM0L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM1L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM2L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM3L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM4L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM5L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM6L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM7L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM8L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCM9L', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM21DC12HCMAL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMBL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMCL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMDL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMEL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMFL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMGL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMHL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMIL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMJL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMKL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMLL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMML', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMNL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMOL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMPL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMQL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMRL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMSL', 'FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMTL']
2025-06-29 10:32:30,430 - INFO - 批量插入响应状态码: 200
2025-06-29 10:32:30,430 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 02:32:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '489C1552-4DAA-797E-9418-54F10D1B88C2', 'x-acs-trace-id': '65b0b13dbe75f2641090f4d1fa81ae72', 'etag': '24DoPlpwq33/D3TJgc+qFMw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 10:32:30,430 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMF9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMG9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMH9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMI9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMJ9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMK9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCML9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMM9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMN9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMO9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMP9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMQ9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMR9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMS9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMT9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMU9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMV9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMW9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMX9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMY9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMZ9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM0A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM1A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM2A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM3A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM4A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM5A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM6A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM7A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM8A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM9A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMAA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMBA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMCA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMDA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMEA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMFA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMGA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMHA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMIA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMJA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMKA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMLA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMMA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMNA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMOA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMPA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMQA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMRA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMSA']}
2025-06-29 10:32:30,430 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-29 10:32:30,430 - INFO - 成功插入的数据ID: ['FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMF9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMG9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMH9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMI9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMJ9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMK9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCML9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMM9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMN9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMO9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMP9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMQ9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMR9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMS9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMT9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMU9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMV9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMW9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMX9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMY9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMZ9', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM0A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM1A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM2A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM3A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM4A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM5A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM6A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM7A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM8A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCM9A', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMAA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMBA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMCA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMDA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMEA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMFA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMGA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMHA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMIA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMJA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMKA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMLA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMMA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMNA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMOA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMPA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMQA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMRA', 'FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMSA']
2025-06-29 10:32:35,618 - INFO - 批量插入响应状态码: 200
2025-06-29 10:32:35,618 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 02:32:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '540', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '984E407D-EBE0-746B-B7B5-27E30C1AD83A', 'x-acs-trace-id': '99d1c193df056badd3b5f52fd47e3327', 'etag': '5tOggTsorYQnAw+nGLtJ4nw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 10:32:35,618 - INFO - 批量插入响应体: {'result': ['FINST-OLF66581I7OWYT0BC3GEL5CS53FU2OEK12HCMLG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2OEK12HCMMG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2OEK12HCMNG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2OEK12HCMOG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMPG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMQG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMRG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMSG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMTG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMUG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMVG']}
2025-06-29 10:32:35,618 - INFO - 批量插入表单数据成功，批次 5，共 11 条记录
2025-06-29 10:32:35,618 - INFO - 成功插入的数据ID: ['FINST-OLF66581I7OWYT0BC3GEL5CS53FU2OEK12HCMLG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2OEK12HCMMG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2OEK12HCMNG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2OEK12HCMOG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMPG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMQG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMRG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMSG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMTG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMUG', 'FINST-OLF66581I7OWYT0BC3GEL5CS53FU2PEK12HCMVG']
2025-06-29 10:32:40,633 - INFO - 批量插入完成，共 211 条记录
2025-06-29 10:32:40,633 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 211 条，错误: 0 条
2025-06-29 10:32:40,633 - INFO - 开始处理日期: 2025-06-29
2025-06-29 10:32:40,633 - INFO - Request Parameters - Page 1:
2025-06-29 10:32:40,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 10:32:40,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 10:32:41,055 - INFO - Response - Page 1:
2025-06-29 10:32:41,055 - INFO - 第 1 页获取到 1 条记录
2025-06-29 10:32:41,555 - INFO - 查询完成，共获取到 1 条记录
2025-06-29 10:32:41,555 - INFO - 获取到 1 条表单数据
2025-06-29 10:32:41,555 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-06-29 10:32:41,555 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 10:32:41,555 - INFO - 数据同步完成！更新: 0 条，插入: 211 条，错误: 0 条
2025-06-29 10:32:41,555 - INFO - 同步完成
2025-06-29 13:30:33,624 - INFO - 使用默认增量同步（当天更新数据）
2025-06-29 13:30:33,624 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-29 13:30:33,624 - INFO - 查询参数: ('2025-06-29',)
2025-06-29 13:30:33,764 - INFO - MySQL查询成功，增量数据（日期: 2025-06-29），共获取 142 条记录
2025-06-29 13:30:33,764 - INFO - 获取到 4 个日期需要处理: ['2025-06-16', '2025-06-27', '2025-06-28', '2025-06-29']
2025-06-29 13:30:33,764 - INFO - 开始处理日期: 2025-06-16
2025-06-29 13:30:33,780 - INFO - Request Parameters - Page 1:
2025-06-29 13:30:33,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:33,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:41,874 - ERROR - 处理日期 2025-06-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E6DAE7A7-CE70-77D8-B6B7-57DF91D634E1 Response: {'code': 'ServiceUnavailable', 'requestid': 'E6DAE7A7-CE70-77D8-B6B7-57DF91D634E1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E6DAE7A7-CE70-77D8-B6B7-57DF91D634E1)
2025-06-29 13:30:41,874 - INFO - 开始处理日期: 2025-06-27
2025-06-29 13:30:41,889 - INFO - Request Parameters - Page 1:
2025-06-29 13:30:41,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:41,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:42,686 - INFO - Response - Page 1:
2025-06-29 13:30:42,686 - INFO - 第 1 页获取到 50 条记录
2025-06-29 13:30:43,202 - INFO - Request Parameters - Page 2:
2025-06-29 13:30:43,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:43,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:49,327 - INFO - Response - Page 2:
2025-06-29 13:30:49,327 - INFO - 第 2 页获取到 50 条记录
2025-06-29 13:30:49,827 - INFO - Request Parameters - Page 3:
2025-06-29 13:30:49,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:49,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:50,498 - INFO - Response - Page 3:
2025-06-29 13:30:50,498 - INFO - 第 3 页获取到 50 条记录
2025-06-29 13:30:50,999 - INFO - Request Parameters - Page 4:
2025-06-29 13:30:50,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:50,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:51,670 - INFO - Response - Page 4:
2025-06-29 13:30:51,670 - INFO - 第 4 页获取到 50 条记录
2025-06-29 13:30:52,186 - INFO - Request Parameters - Page 5:
2025-06-29 13:30:52,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:52,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:52,842 - INFO - Response - Page 5:
2025-06-29 13:30:52,842 - INFO - 第 5 页获取到 50 条记录
2025-06-29 13:30:53,358 - INFO - Request Parameters - Page 6:
2025-06-29 13:30:53,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:53,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:53,998 - INFO - Response - Page 6:
2025-06-29 13:30:53,998 - INFO - 第 6 页获取到 50 条记录
2025-06-29 13:30:54,498 - INFO - Request Parameters - Page 7:
2025-06-29 13:30:54,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:54,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:55,123 - INFO - Response - Page 7:
2025-06-29 13:30:55,123 - INFO - 第 7 页获取到 50 条记录
2025-06-29 13:30:55,623 - INFO - Request Parameters - Page 8:
2025-06-29 13:30:55,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:55,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:56,342 - INFO - Response - Page 8:
2025-06-29 13:30:56,342 - INFO - 第 8 页获取到 50 条记录
2025-06-29 13:30:56,858 - INFO - Request Parameters - Page 9:
2025-06-29 13:30:56,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:56,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:57,592 - INFO - Response - Page 9:
2025-06-29 13:30:57,592 - INFO - 第 9 页获取到 50 条记录
2025-06-29 13:30:58,108 - INFO - Request Parameters - Page 10:
2025-06-29 13:30:58,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:30:58,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:30:58,702 - INFO - Response - Page 10:
2025-06-29 13:30:58,702 - INFO - 第 10 页获取到 25 条记录
2025-06-29 13:30:59,202 - INFO - 查询完成，共获取到 475 条记录
2025-06-29 13:30:59,202 - INFO - 获取到 475 条表单数据
2025-06-29 13:30:59,202 - INFO - 当前日期 2025-06-27 有 3 条MySQL数据需要处理
2025-06-29 13:30:59,202 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMO2
2025-06-29 13:30:59,905 - INFO - 更新表单数据成功: FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMO2
2025-06-29 13:30:59,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 475357.0, 'new_value': 47573.0}, {'field': 'total_amount', 'old_value': 475357.0, 'new_value': 47573.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-29 13:30:59,905 - INFO - 开始更新记录 - 表单实例ID: FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMP6
2025-06-29 13:31:00,545 - INFO - 更新表单数据成功: FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMP6
2025-06-29 13:31:00,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15369.0, 'new_value': 20155.0}, {'field': 'total_amount', 'old_value': 15369.0, 'new_value': 20155.0}, {'field': 'order_count', 'old_value': 317, 'new_value': 471}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/b4c38b45b5b24d42bd58026a66fdeefe.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=HcbVhRjKajWu58k4KV4pju3plOc%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/270099c225714935ac997bbf6a7a5a3c.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=y%2FZtAwP4%2FTD%2Fwgc8Tl6YKrS6OgE%3D'}]
2025-06-29 13:31:00,545 - INFO - 开始批量插入 1 条新记录
2025-06-29 13:31:00,702 - INFO - 批量插入响应状态码: 200
2025-06-29 13:31:00,702 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 05:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9BA7BC23-1913-798F-95EC-029E15E062F5', 'x-acs-trace-id': '82f5c3708bdf9ea8fd3dc6b8a48ba2e1', 'etag': '658I97+Sw/o36Mg1at3tgmA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 13:31:00,702 - INFO - 批量插入响应体: {'result': ['FINST-MLF662B15POWVBUJE5BR56N0XF7I36K0F8HCM8L']}
2025-06-29 13:31:00,702 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-29 13:31:00,702 - INFO - 成功插入的数据ID: ['FINST-MLF662B15POWVBUJE5BR56N0XF7I36K0F8HCM8L']
2025-06-29 13:31:05,717 - INFO - 批量插入完成，共 1 条记录
2025-06-29 13:31:05,717 - INFO - 日期 2025-06-27 处理完成 - 更新: 2 条，插入: 1 条，错误: 0 条
2025-06-29 13:31:05,717 - INFO - 开始处理日期: 2025-06-28
2025-06-29 13:31:05,717 - INFO - Request Parameters - Page 1:
2025-06-29 13:31:05,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:05,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:06,405 - INFO - Response - Page 1:
2025-06-29 13:31:06,405 - INFO - 第 1 页获取到 50 条记录
2025-06-29 13:31:06,920 - INFO - Request Parameters - Page 2:
2025-06-29 13:31:06,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:06,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:07,623 - INFO - Response - Page 2:
2025-06-29 13:31:07,623 - INFO - 第 2 页获取到 50 条记录
2025-06-29 13:31:08,123 - INFO - Request Parameters - Page 3:
2025-06-29 13:31:08,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:08,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:08,764 - INFO - Response - Page 3:
2025-06-29 13:31:08,764 - INFO - 第 3 页获取到 50 条记录
2025-06-29 13:31:09,280 - INFO - Request Parameters - Page 4:
2025-06-29 13:31:09,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:09,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:09,951 - INFO - Response - Page 4:
2025-06-29 13:31:09,951 - INFO - 第 4 页获取到 50 条记录
2025-06-29 13:31:10,467 - INFO - Request Parameters - Page 5:
2025-06-29 13:31:10,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:10,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:11,108 - INFO - Response - Page 5:
2025-06-29 13:31:11,108 - INFO - 第 5 页获取到 50 条记录
2025-06-29 13:31:11,608 - INFO - Request Parameters - Page 6:
2025-06-29 13:31:11,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:11,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:12,295 - INFO - Response - Page 6:
2025-06-29 13:31:12,295 - INFO - 第 6 页获取到 50 条记录
2025-06-29 13:31:12,811 - INFO - Request Parameters - Page 7:
2025-06-29 13:31:12,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:12,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:13,436 - INFO - Response - Page 7:
2025-06-29 13:31:13,436 - INFO - 第 7 页获取到 50 条记录
2025-06-29 13:31:13,951 - INFO - Request Parameters - Page 8:
2025-06-29 13:31:13,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:13,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:14,576 - INFO - Response - Page 8:
2025-06-29 13:31:14,576 - INFO - 第 8 页获取到 50 条记录
2025-06-29 13:31:15,092 - INFO - Request Parameters - Page 9:
2025-06-29 13:31:15,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:15,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:15,639 - INFO - Response - Page 9:
2025-06-29 13:31:15,655 - INFO - 第 9 页获取到 13 条记录
2025-06-29 13:31:16,155 - INFO - 查询完成，共获取到 413 条记录
2025-06-29 13:31:16,155 - INFO - 获取到 413 条表单数据
2025-06-29 13:31:16,155 - INFO - 当前日期 2025-06-28 有 133 条MySQL数据需要处理
2025-06-29 13:31:16,155 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMR9
2025-06-29 13:31:16,655 - INFO - 更新表单数据成功: FINST-3PF66271MBOWRRQ669F3D9GRC4703PEG12HCMR9
2025-06-29 13:31:16,655 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-29 13:31:16,655 - INFO - 开始更新记录 - 表单实例ID: FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM35
2025-06-29 13:31:17,233 - INFO - 更新表单数据成功: FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM35
2025-06-29 13:31:17,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25000.0, 'new_value': 0.0}, {'field': 'total_amount', 'old_value': 25000.0, 'new_value': 0.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-29 13:31:17,233 - INFO - 开始更新记录 - 表单实例ID: FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMQL
2025-06-29 13:31:17,686 - INFO - 更新表单数据成功: FINST-F3G66Q61BPOW9GJ3CO1J85H696WM22DC12HCMQL
2025-06-29 13:31:17,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26893.0, 'new_value': 6893.0}, {'field': 'total_amount', 'old_value': 26956.0, 'new_value': 6956.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 2}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-29 13:31:17,686 - INFO - 开始批量插入 27 条新记录
2025-06-29 13:31:17,920 - INFO - 批量插入响应状态码: 200
2025-06-29 13:31:17,920 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 05:31:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1308', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FF68140F-423C-73E3-BC13-675908791F44', 'x-acs-trace-id': '85d1ed4cd7958671a8d4dc28cf60d382', 'etag': '1W33zln1Zb6ucpg9Dl5HzYw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 13:31:17,920 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMFN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMGN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMHN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMIN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMJN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMKN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMLN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMMN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMNN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMON', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMPN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMQN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMRN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMSN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMTN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMUN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMVN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMWN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMXN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMYN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMZN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM0O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM1O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM2O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM3O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM4O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM5O']}
2025-06-29 13:31:17,920 - INFO - 批量插入表单数据成功，批次 1，共 27 条记录
2025-06-29 13:31:17,920 - INFO - 成功插入的数据ID: ['FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMFN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMGN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMHN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMIN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMJN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMKN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMLN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMMN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMNN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMON', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMPN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMQN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMRN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMSN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMTN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMUN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMVN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMWN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMXN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMYN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCMZN', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM0O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM1O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM2O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM3O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM4O', 'FINST-7PF66BA1S6OWA6GF749WN4FFREKM2AUDF8HCM5O']
2025-06-29 13:31:22,936 - INFO - 批量插入完成，共 27 条记录
2025-06-29 13:31:22,936 - INFO - 日期 2025-06-28 处理完成 - 更新: 3 条，插入: 27 条，错误: 0 条
2025-06-29 13:31:22,936 - INFO - 开始处理日期: 2025-06-29
2025-06-29 13:31:22,936 - INFO - Request Parameters - Page 1:
2025-06-29 13:31:22,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:31:22,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:31:23,389 - INFO - Response - Page 1:
2025-06-29 13:31:23,389 - INFO - 第 1 页获取到 1 条记录
2025-06-29 13:31:23,905 - INFO - 查询完成，共获取到 1 条记录
2025-06-29 13:31:23,905 - INFO - 获取到 1 条表单数据
2025-06-29 13:31:23,905 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-06-29 13:31:23,905 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 13:31:23,905 - INFO - 数据同步完成！更新: 5 条，插入: 28 条，错误: 1 条
2025-06-29 13:32:23,920 - INFO - 开始同步昨天与今天的销售数据: 2025-06-28 至 2025-06-29
2025-06-29 13:32:23,920 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-29 13:32:23,920 - INFO - 查询参数: ('2025-06-28', '2025-06-29')
2025-06-29 13:32:24,138 - INFO - MySQL查询成功，时间段: 2025-06-28 至 2025-06-29，共获取 496 条记录
2025-06-29 13:32:24,138 - INFO - 获取到 2 个日期需要处理: ['2025-06-28', '2025-06-29']
2025-06-29 13:32:24,138 - INFO - 开始处理日期: 2025-06-28
2025-06-29 13:32:24,138 - INFO - Request Parameters - Page 1:
2025-06-29 13:32:24,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:24,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:24,888 - INFO - Response - Page 1:
2025-06-29 13:32:24,888 - INFO - 第 1 页获取到 50 条记录
2025-06-29 13:32:25,404 - INFO - Request Parameters - Page 2:
2025-06-29 13:32:25,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:25,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:26,045 - INFO - Response - Page 2:
2025-06-29 13:32:26,045 - INFO - 第 2 页获取到 50 条记录
2025-06-29 13:32:26,545 - INFO - Request Parameters - Page 3:
2025-06-29 13:32:26,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:26,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:27,232 - INFO - Response - Page 3:
2025-06-29 13:32:27,232 - INFO - 第 3 页获取到 50 条记录
2025-06-29 13:32:27,748 - INFO - Request Parameters - Page 4:
2025-06-29 13:32:27,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:27,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:28,388 - INFO - Response - Page 4:
2025-06-29 13:32:28,388 - INFO - 第 4 页获取到 50 条记录
2025-06-29 13:32:28,888 - INFO - Request Parameters - Page 5:
2025-06-29 13:32:28,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:28,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:29,592 - INFO - Response - Page 5:
2025-06-29 13:32:29,592 - INFO - 第 5 页获取到 50 条记录
2025-06-29 13:32:30,092 - INFO - Request Parameters - Page 6:
2025-06-29 13:32:30,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:30,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:30,795 - INFO - Response - Page 6:
2025-06-29 13:32:30,795 - INFO - 第 6 页获取到 50 条记录
2025-06-29 13:32:31,310 - INFO - Request Parameters - Page 7:
2025-06-29 13:32:31,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:31,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:31,998 - INFO - Response - Page 7:
2025-06-29 13:32:31,998 - INFO - 第 7 页获取到 50 条记录
2025-06-29 13:32:32,513 - INFO - Request Parameters - Page 8:
2025-06-29 13:32:32,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:32,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:33,170 - INFO - Response - Page 8:
2025-06-29 13:32:33,170 - INFO - 第 8 页获取到 50 条记录
2025-06-29 13:32:33,685 - INFO - Request Parameters - Page 9:
2025-06-29 13:32:33,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:33,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:34,342 - INFO - Response - Page 9:
2025-06-29 13:32:34,357 - INFO - 第 9 页获取到 40 条记录
2025-06-29 13:32:34,873 - INFO - 查询完成，共获取到 440 条记录
2025-06-29 13:32:34,873 - INFO - 获取到 440 条表单数据
2025-06-29 13:32:34,873 - INFO - 当前日期 2025-06-28 有 476 条MySQL数据需要处理
2025-06-29 13:32:34,888 - INFO - 开始批量插入 36 条新记录
2025-06-29 13:32:35,107 - INFO - 批量插入响应状态码: 200
2025-06-29 13:32:35,107 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 05:32:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1740', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7802D6CC-A683-7AED-914E-C56F7C37CA9E', 'x-acs-trace-id': 'cd69ae8894a34cf1f2504be602c6077c', 'etag': '1eC9SSGcil5tv//7cY6EPgw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 13:32:35,107 - INFO - 批量插入响应体: {'result': ['FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMB8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMC8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMD8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCME8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMF8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMG8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMH8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMI8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMJ8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMK8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCML8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMM8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMN8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMO8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMP8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMQ8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMR8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMS8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMT8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMU8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMV8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMW8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMX8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMY8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMZ8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM09', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM19', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM29', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM39', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM49', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM59', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM69', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM79', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM89', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM99', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMA9']}
2025-06-29 13:32:35,107 - INFO - 批量插入表单数据成功，批次 1，共 36 条记录
2025-06-29 13:32:35,107 - INFO - 成功插入的数据ID: ['FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMB8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMC8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMD8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCME8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMF8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMG8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMH8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMI8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMJ8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMK8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCML8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMM8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMN8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMO8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMP8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMQ8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMR8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMS8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMT8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMU8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMV8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMW8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMX8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMY8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMZ8', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM09', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM19', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM29', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM39', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM49', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM59', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM69', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM79', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM89', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCM99', 'FINST-2PF66TC1Z5OWU1EU8TJQCBN7E9NV2WE1H8HCMA9']
2025-06-29 13:32:40,123 - INFO - 批量插入完成，共 36 条记录
2025-06-29 13:32:40,123 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 36 条，错误: 0 条
2025-06-29 13:32:40,123 - INFO - 开始处理日期: 2025-06-29
2025-06-29 13:32:40,123 - INFO - Request Parameters - Page 1:
2025-06-29 13:32:40,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 13:32:40,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 13:32:40,576 - INFO - Response - Page 1:
2025-06-29 13:32:40,576 - INFO - 第 1 页获取到 1 条记录
2025-06-29 13:32:41,076 - INFO - 查询完成，共获取到 1 条记录
2025-06-29 13:32:41,076 - INFO - 获取到 1 条表单数据
2025-06-29 13:32:41,076 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-06-29 13:32:41,076 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 13:32:41,076 - INFO - 数据同步完成！更新: 0 条，插入: 36 条，错误: 0 条
2025-06-29 13:32:41,076 - INFO - 同步完成
2025-06-29 16:30:33,911 - INFO - 使用默认增量同步（当天更新数据）
2025-06-29 16:30:33,911 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-29 16:30:33,911 - INFO - 查询参数: ('2025-06-29',)
2025-06-29 16:30:34,052 - INFO - MySQL查询成功，增量数据（日期: 2025-06-29），共获取 142 条记录
2025-06-29 16:30:34,052 - INFO - 获取到 4 个日期需要处理: ['2025-06-16', '2025-06-27', '2025-06-28', '2025-06-29']
2025-06-29 16:30:34,052 - INFO - 开始处理日期: 2025-06-16
2025-06-29 16:30:34,052 - INFO - Request Parameters - Page 1:
2025-06-29 16:30:34,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:34,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:42,161 - ERROR - 处理日期 2025-06-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F122EB60-3DF1-794A-842E-013C4B210075 Response: {'code': 'ServiceUnavailable', 'requestid': 'F122EB60-3DF1-794A-842E-013C4B210075', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F122EB60-3DF1-794A-842E-013C4B210075)
2025-06-29 16:30:42,161 - INFO - 开始处理日期: 2025-06-27
2025-06-29 16:30:42,161 - INFO - Request Parameters - Page 1:
2025-06-29 16:30:42,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:42,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:49,630 - INFO - Response - Page 1:
2025-06-29 16:30:49,630 - INFO - 第 1 页获取到 50 条记录
2025-06-29 16:30:50,145 - INFO - Request Parameters - Page 2:
2025-06-29 16:30:50,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:50,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:50,864 - INFO - Response - Page 2:
2025-06-29 16:30:50,864 - INFO - 第 2 页获取到 50 条记录
2025-06-29 16:30:51,364 - INFO - Request Parameters - Page 3:
2025-06-29 16:30:51,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:51,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:52,052 - INFO - Response - Page 3:
2025-06-29 16:30:52,052 - INFO - 第 3 页获取到 50 条记录
2025-06-29 16:30:52,552 - INFO - Request Parameters - Page 4:
2025-06-29 16:30:52,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:52,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:53,192 - INFO - Response - Page 4:
2025-06-29 16:30:53,192 - INFO - 第 4 页获取到 50 条记录
2025-06-29 16:30:53,708 - INFO - Request Parameters - Page 5:
2025-06-29 16:30:53,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:53,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:54,348 - INFO - Response - Page 5:
2025-06-29 16:30:54,348 - INFO - 第 5 页获取到 50 条记录
2025-06-29 16:30:54,864 - INFO - Request Parameters - Page 6:
2025-06-29 16:30:54,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:54,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:55,551 - INFO - Response - Page 6:
2025-06-29 16:30:55,551 - INFO - 第 6 页获取到 50 条记录
2025-06-29 16:30:56,067 - INFO - Request Parameters - Page 7:
2025-06-29 16:30:56,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:56,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:56,770 - INFO - Response - Page 7:
2025-06-29 16:30:56,770 - INFO - 第 7 页获取到 50 条记录
2025-06-29 16:30:57,286 - INFO - Request Parameters - Page 8:
2025-06-29 16:30:57,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:57,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:57,926 - INFO - Response - Page 8:
2025-06-29 16:30:57,926 - INFO - 第 8 页获取到 50 条记录
2025-06-29 16:30:58,426 - INFO - Request Parameters - Page 9:
2025-06-29 16:30:58,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:58,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:30:59,161 - INFO - Response - Page 9:
2025-06-29 16:30:59,161 - INFO - 第 9 页获取到 50 条记录
2025-06-29 16:30:59,661 - INFO - Request Parameters - Page 10:
2025-06-29 16:30:59,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:30:59,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:00,255 - INFO - Response - Page 10:
2025-06-29 16:31:00,255 - INFO - 第 10 页获取到 26 条记录
2025-06-29 16:31:00,770 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 16:31:00,770 - INFO - 获取到 476 条表单数据
2025-06-29 16:31:00,770 - INFO - 当前日期 2025-06-27 有 3 条MySQL数据需要处理
2025-06-29 16:31:00,770 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 16:31:00,770 - INFO - 开始处理日期: 2025-06-28
2025-06-29 16:31:00,770 - INFO - Request Parameters - Page 1:
2025-06-29 16:31:00,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:00,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:01,442 - INFO - Response - Page 1:
2025-06-29 16:31:01,442 - INFO - 第 1 页获取到 50 条记录
2025-06-29 16:31:01,958 - INFO - Request Parameters - Page 2:
2025-06-29 16:31:01,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:01,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:02,598 - INFO - Response - Page 2:
2025-06-29 16:31:02,598 - INFO - 第 2 页获取到 50 条记录
2025-06-29 16:31:03,098 - INFO - Request Parameters - Page 3:
2025-06-29 16:31:03,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:03,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:03,817 - INFO - Response - Page 3:
2025-06-29 16:31:03,817 - INFO - 第 3 页获取到 50 条记录
2025-06-29 16:31:04,333 - INFO - Request Parameters - Page 4:
2025-06-29 16:31:04,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:04,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:05,051 - INFO - Response - Page 4:
2025-06-29 16:31:05,051 - INFO - 第 4 页获取到 50 条记录
2025-06-29 16:31:05,567 - INFO - Request Parameters - Page 5:
2025-06-29 16:31:05,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:05,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:06,286 - INFO - Response - Page 5:
2025-06-29 16:31:06,286 - INFO - 第 5 页获取到 50 条记录
2025-06-29 16:31:06,801 - INFO - Request Parameters - Page 6:
2025-06-29 16:31:06,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:06,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:07,473 - INFO - Response - Page 6:
2025-06-29 16:31:07,473 - INFO - 第 6 页获取到 50 条记录
2025-06-29 16:31:07,989 - INFO - Request Parameters - Page 7:
2025-06-29 16:31:07,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:07,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:08,661 - INFO - Response - Page 7:
2025-06-29 16:31:08,661 - INFO - 第 7 页获取到 50 条记录
2025-06-29 16:31:09,161 - INFO - Request Parameters - Page 8:
2025-06-29 16:31:09,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:09,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:09,786 - INFO - Response - Page 8:
2025-06-29 16:31:09,786 - INFO - 第 8 页获取到 50 条记录
2025-06-29 16:31:10,301 - INFO - Request Parameters - Page 9:
2025-06-29 16:31:10,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:10,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:11,004 - INFO - Response - Page 9:
2025-06-29 16:31:11,004 - INFO - 第 9 页获取到 50 条记录
2025-06-29 16:31:11,520 - INFO - Request Parameters - Page 10:
2025-06-29 16:31:11,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:11,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:12,161 - INFO - Response - Page 10:
2025-06-29 16:31:12,176 - INFO - 第 10 页获取到 26 条记录
2025-06-29 16:31:12,676 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 16:31:12,676 - INFO - 获取到 476 条表单数据
2025-06-29 16:31:12,676 - INFO - 当前日期 2025-06-28 有 133 条MySQL数据需要处理
2025-06-29 16:31:12,676 - INFO - 开始更新记录 - 表单实例ID: FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMSQ
2025-06-29 16:31:13,301 - INFO - 更新表单数据成功: FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMSQ
2025-06-29 16:31:13,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 199.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 199.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/91866270d37c429bac134e722f66eac1.png?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=X0Vwv%2Fd6VO%2Bt11zCncZD79NlFYw%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/02e4951fd1f8462c818d51ed5f28f08b.png?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=sj4iyq10I4IUVgK%2Bo4zoMfXgtuE%3D'}]
2025-06-29 16:31:13,301 - INFO - 日期 2025-06-28 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-29 16:31:13,301 - INFO - 开始处理日期: 2025-06-29
2025-06-29 16:31:13,301 - INFO - Request Parameters - Page 1:
2025-06-29 16:31:13,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:31:13,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:31:13,754 - INFO - Response - Page 1:
2025-06-29 16:31:13,754 - INFO - 第 1 页获取到 1 条记录
2025-06-29 16:31:14,270 - INFO - 查询完成，共获取到 1 条记录
2025-06-29 16:31:14,270 - INFO - 获取到 1 条表单数据
2025-06-29 16:31:14,270 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-06-29 16:31:14,270 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 16:31:14,270 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 1 条
2025-06-29 16:32:14,285 - INFO - 开始同步昨天与今天的销售数据: 2025-06-28 至 2025-06-29
2025-06-29 16:32:14,285 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-29 16:32:14,285 - INFO - 查询参数: ('2025-06-28', '2025-06-29')
2025-06-29 16:32:14,426 - INFO - MySQL查询成功，时间段: 2025-06-28 至 2025-06-29，共获取 496 条记录
2025-06-29 16:32:14,426 - INFO - 获取到 2 个日期需要处理: ['2025-06-28', '2025-06-29']
2025-06-29 16:32:14,442 - INFO - 开始处理日期: 2025-06-28
2025-06-29 16:32:14,442 - INFO - Request Parameters - Page 1:
2025-06-29 16:32:14,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:14,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:15,238 - INFO - Response - Page 1:
2025-06-29 16:32:15,238 - INFO - 第 1 页获取到 50 条记录
2025-06-29 16:32:15,754 - INFO - Request Parameters - Page 2:
2025-06-29 16:32:15,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:15,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:16,442 - INFO - Response - Page 2:
2025-06-29 16:32:16,442 - INFO - 第 2 页获取到 50 条记录
2025-06-29 16:32:16,942 - INFO - Request Parameters - Page 3:
2025-06-29 16:32:16,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:16,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:17,582 - INFO - Response - Page 3:
2025-06-29 16:32:17,582 - INFO - 第 3 页获取到 50 条记录
2025-06-29 16:32:18,098 - INFO - Request Parameters - Page 4:
2025-06-29 16:32:18,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:18,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:18,770 - INFO - Response - Page 4:
2025-06-29 16:32:18,770 - INFO - 第 4 页获取到 50 条记录
2025-06-29 16:32:19,285 - INFO - Request Parameters - Page 5:
2025-06-29 16:32:19,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:19,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:19,942 - INFO - Response - Page 5:
2025-06-29 16:32:19,942 - INFO - 第 5 页获取到 50 条记录
2025-06-29 16:32:20,442 - INFO - Request Parameters - Page 6:
2025-06-29 16:32:20,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:20,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:21,098 - INFO - Response - Page 6:
2025-06-29 16:32:21,098 - INFO - 第 6 页获取到 50 条记录
2025-06-29 16:32:21,598 - INFO - Request Parameters - Page 7:
2025-06-29 16:32:21,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:21,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:22,238 - INFO - Response - Page 7:
2025-06-29 16:32:22,238 - INFO - 第 7 页获取到 50 条记录
2025-06-29 16:32:22,754 - INFO - Request Parameters - Page 8:
2025-06-29 16:32:22,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:22,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:23,410 - INFO - Response - Page 8:
2025-06-29 16:32:23,410 - INFO - 第 8 页获取到 50 条记录
2025-06-29 16:32:23,926 - INFO - Request Parameters - Page 9:
2025-06-29 16:32:23,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:23,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:24,567 - INFO - Response - Page 9:
2025-06-29 16:32:24,567 - INFO - 第 9 页获取到 50 条记录
2025-06-29 16:32:25,067 - INFO - Request Parameters - Page 10:
2025-06-29 16:32:25,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:25,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:25,660 - INFO - Response - Page 10:
2025-06-29 16:32:25,660 - INFO - 第 10 页获取到 26 条记录
2025-06-29 16:32:26,176 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 16:32:26,176 - INFO - 获取到 476 条表单数据
2025-06-29 16:32:26,176 - INFO - 当前日期 2025-06-28 有 476 条MySQL数据需要处理
2025-06-29 16:32:26,191 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 16:32:26,191 - INFO - 开始处理日期: 2025-06-29
2025-06-29 16:32:26,191 - INFO - Request Parameters - Page 1:
2025-06-29 16:32:26,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 16:32:26,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 16:32:26,613 - INFO - Response - Page 1:
2025-06-29 16:32:26,613 - INFO - 第 1 页获取到 1 条记录
2025-06-29 16:32:27,129 - INFO - 查询完成，共获取到 1 条记录
2025-06-29 16:32:27,129 - INFO - 获取到 1 条表单数据
2025-06-29 16:32:27,129 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-06-29 16:32:27,129 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 16:32:27,129 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 16:32:27,129 - INFO - 同步完成
2025-06-29 19:30:34,624 - INFO - 使用默认增量同步（当天更新数据）
2025-06-29 19:30:34,639 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-29 19:30:34,639 - INFO - 查询参数: ('2025-06-29',)
2025-06-29 19:30:34,764 - INFO - MySQL查询成功，增量数据（日期: 2025-06-29），共获取 142 条记录
2025-06-29 19:30:34,764 - INFO - 获取到 4 个日期需要处理: ['2025-06-16', '2025-06-27', '2025-06-28', '2025-06-29']
2025-06-29 19:30:34,780 - INFO - 开始处理日期: 2025-06-16
2025-06-29 19:30:34,780 - INFO - Request Parameters - Page 1:
2025-06-29 19:30:34,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:34,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:42,893 - ERROR - 处理日期 2025-06-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 92DF2B64-764E-7DEE-8DB6-648B1B642221 Response: {'code': 'ServiceUnavailable', 'requestid': '92DF2B64-764E-7DEE-8DB6-648B1B642221', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 92DF2B64-764E-7DEE-8DB6-648B1B642221)
2025-06-29 19:30:42,893 - INFO - 开始处理日期: 2025-06-27
2025-06-29 19:30:42,893 - INFO - Request Parameters - Page 1:
2025-06-29 19:30:42,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:42,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:49,426 - INFO - Response - Page 1:
2025-06-29 19:30:49,426 - INFO - 第 1 页获取到 50 条记录
2025-06-29 19:30:49,927 - INFO - Request Parameters - Page 2:
2025-06-29 19:30:49,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:49,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:50,630 - INFO - Response - Page 2:
2025-06-29 19:30:50,630 - INFO - 第 2 页获取到 50 条记录
2025-06-29 19:30:51,146 - INFO - Request Parameters - Page 3:
2025-06-29 19:30:51,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:51,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:51,787 - INFO - Response - Page 3:
2025-06-29 19:30:51,787 - INFO - 第 3 页获取到 50 条记录
2025-06-29 19:30:52,303 - INFO - Request Parameters - Page 4:
2025-06-29 19:30:52,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:52,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:52,990 - INFO - Response - Page 4:
2025-06-29 19:30:52,990 - INFO - 第 4 页获取到 50 条记录
2025-06-29 19:30:53,506 - INFO - Request Parameters - Page 5:
2025-06-29 19:30:53,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:53,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:54,131 - INFO - Response - Page 5:
2025-06-29 19:30:54,131 - INFO - 第 5 页获取到 50 条记录
2025-06-29 19:30:54,632 - INFO - Request Parameters - Page 6:
2025-06-29 19:30:54,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:54,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:55,257 - INFO - Response - Page 6:
2025-06-29 19:30:55,257 - INFO - 第 6 页获取到 50 条记录
2025-06-29 19:30:55,757 - INFO - Request Parameters - Page 7:
2025-06-29 19:30:55,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:55,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:56,429 - INFO - Response - Page 7:
2025-06-29 19:30:56,429 - INFO - 第 7 页获取到 50 条记录
2025-06-29 19:30:56,945 - INFO - Request Parameters - Page 8:
2025-06-29 19:30:56,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:56,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:57,586 - INFO - Response - Page 8:
2025-06-29 19:30:57,586 - INFO - 第 8 页获取到 50 条记录
2025-06-29 19:30:58,102 - INFO - Request Parameters - Page 9:
2025-06-29 19:30:58,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:58,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:58,790 - INFO - Response - Page 9:
2025-06-29 19:30:58,790 - INFO - 第 9 页获取到 50 条记录
2025-06-29 19:30:59,290 - INFO - Request Parameters - Page 10:
2025-06-29 19:30:59,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:30:59,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:30:59,884 - INFO - Response - Page 10:
2025-06-29 19:30:59,884 - INFO - 第 10 页获取到 26 条记录
2025-06-29 19:31:00,400 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 19:31:00,400 - INFO - 获取到 476 条表单数据
2025-06-29 19:31:00,400 - INFO - 当前日期 2025-06-27 有 3 条MySQL数据需要处理
2025-06-29 19:31:00,400 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 19:31:00,400 - INFO - 开始处理日期: 2025-06-28
2025-06-29 19:31:00,400 - INFO - Request Parameters - Page 1:
2025-06-29 19:31:00,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:00,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:01,087 - INFO - Response - Page 1:
2025-06-29 19:31:01,087 - INFO - 第 1 页获取到 50 条记录
2025-06-29 19:31:01,588 - INFO - Request Parameters - Page 2:
2025-06-29 19:31:01,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:01,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:02,260 - INFO - Response - Page 2:
2025-06-29 19:31:02,260 - INFO - 第 2 页获取到 50 条记录
2025-06-29 19:31:02,760 - INFO - Request Parameters - Page 3:
2025-06-29 19:31:02,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:02,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:03,416 - INFO - Response - Page 3:
2025-06-29 19:31:03,416 - INFO - 第 3 页获取到 50 条记录
2025-06-29 19:31:03,917 - INFO - Request Parameters - Page 4:
2025-06-29 19:31:03,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:03,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:04,636 - INFO - Response - Page 4:
2025-06-29 19:31:04,636 - INFO - 第 4 页获取到 50 条记录
2025-06-29 19:31:05,151 - INFO - Request Parameters - Page 5:
2025-06-29 19:31:05,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:05,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:05,808 - INFO - Response - Page 5:
2025-06-29 19:31:05,808 - INFO - 第 5 页获取到 50 条记录
2025-06-29 19:31:06,324 - INFO - Request Parameters - Page 6:
2025-06-29 19:31:06,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:06,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:07,027 - INFO - Response - Page 6:
2025-06-29 19:31:07,027 - INFO - 第 6 页获取到 50 条记录
2025-06-29 19:31:07,527 - INFO - Request Parameters - Page 7:
2025-06-29 19:31:07,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:07,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:08,246 - INFO - Response - Page 7:
2025-06-29 19:31:08,246 - INFO - 第 7 页获取到 50 条记录
2025-06-29 19:31:08,747 - INFO - Request Parameters - Page 8:
2025-06-29 19:31:08,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:08,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:09,497 - INFO - Response - Page 8:
2025-06-29 19:31:09,497 - INFO - 第 8 页获取到 50 条记录
2025-06-29 19:31:10,013 - INFO - Request Parameters - Page 9:
2025-06-29 19:31:10,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:10,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:10,716 - INFO - Response - Page 9:
2025-06-29 19:31:10,716 - INFO - 第 9 页获取到 50 条记录
2025-06-29 19:31:11,232 - INFO - Request Parameters - Page 10:
2025-06-29 19:31:11,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:11,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:11,826 - INFO - Response - Page 10:
2025-06-29 19:31:11,826 - INFO - 第 10 页获取到 26 条记录
2025-06-29 19:31:12,342 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 19:31:12,342 - INFO - 获取到 476 条表单数据
2025-06-29 19:31:12,342 - INFO - 当前日期 2025-06-28 有 133 条MySQL数据需要处理
2025-06-29 19:31:12,342 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 19:31:12,342 - INFO - 开始处理日期: 2025-06-29
2025-06-29 19:31:12,342 - INFO - Request Parameters - Page 1:
2025-06-29 19:31:12,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:31:12,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:31:12,795 - INFO - Response - Page 1:
2025-06-29 19:31:12,795 - INFO - 第 1 页获取到 1 条记录
2025-06-29 19:31:13,311 - INFO - 查询完成，共获取到 1 条记录
2025-06-29 19:31:13,311 - INFO - 获取到 1 条表单数据
2025-06-29 19:31:13,311 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-06-29 19:31:13,311 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 19:31:13,311 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-29 19:32:13,350 - INFO - 开始同步昨天与今天的销售数据: 2025-06-28 至 2025-06-29
2025-06-29 19:32:13,350 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-29 19:32:13,350 - INFO - 查询参数: ('2025-06-28', '2025-06-29')
2025-06-29 19:32:13,491 - INFO - MySQL查询成功，时间段: 2025-06-28 至 2025-06-29，共获取 496 条记录
2025-06-29 19:32:13,491 - INFO - 获取到 2 个日期需要处理: ['2025-06-28', '2025-06-29']
2025-06-29 19:32:13,491 - INFO - 开始处理日期: 2025-06-28
2025-06-29 19:32:13,491 - INFO - Request Parameters - Page 1:
2025-06-29 19:32:13,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:13,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:14,148 - INFO - Response - Page 1:
2025-06-29 19:32:14,148 - INFO - 第 1 页获取到 50 条记录
2025-06-29 19:32:14,648 - INFO - Request Parameters - Page 2:
2025-06-29 19:32:14,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:14,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:15,320 - INFO - Response - Page 2:
2025-06-29 19:32:15,320 - INFO - 第 2 页获取到 50 条记录
2025-06-29 19:32:15,836 - INFO - Request Parameters - Page 3:
2025-06-29 19:32:15,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:15,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:16,477 - INFO - Response - Page 3:
2025-06-29 19:32:16,477 - INFO - 第 3 页获取到 50 条记录
2025-06-29 19:32:16,993 - INFO - Request Parameters - Page 4:
2025-06-29 19:32:16,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:16,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:17,618 - INFO - Response - Page 4:
2025-06-29 19:32:17,618 - INFO - 第 4 页获取到 50 条记录
2025-06-29 19:32:18,134 - INFO - Request Parameters - Page 5:
2025-06-29 19:32:18,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:18,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:18,774 - INFO - Response - Page 5:
2025-06-29 19:32:18,774 - INFO - 第 5 页获取到 50 条记录
2025-06-29 19:32:19,290 - INFO - Request Parameters - Page 6:
2025-06-29 19:32:19,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:19,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:19,916 - INFO - Response - Page 6:
2025-06-29 19:32:19,916 - INFO - 第 6 页获取到 50 条记录
2025-06-29 19:32:20,431 - INFO - Request Parameters - Page 7:
2025-06-29 19:32:20,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:20,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:21,025 - INFO - Response - Page 7:
2025-06-29 19:32:21,025 - INFO - 第 7 页获取到 50 条记录
2025-06-29 19:32:21,541 - INFO - Request Parameters - Page 8:
2025-06-29 19:32:21,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:21,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:22,198 - INFO - Response - Page 8:
2025-06-29 19:32:22,198 - INFO - 第 8 页获取到 50 条记录
2025-06-29 19:32:22,714 - INFO - Request Parameters - Page 9:
2025-06-29 19:32:22,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:22,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:23,308 - INFO - Response - Page 9:
2025-06-29 19:32:23,308 - INFO - 第 9 页获取到 50 条记录
2025-06-29 19:32:23,808 - INFO - Request Parameters - Page 10:
2025-06-29 19:32:23,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:23,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:24,386 - INFO - Response - Page 10:
2025-06-29 19:32:24,386 - INFO - 第 10 页获取到 26 条记录
2025-06-29 19:32:24,886 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 19:32:24,886 - INFO - 获取到 476 条表单数据
2025-06-29 19:32:24,886 - INFO - 当前日期 2025-06-28 有 476 条MySQL数据需要处理
2025-06-29 19:32:24,902 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 19:32:24,902 - INFO - 开始处理日期: 2025-06-29
2025-06-29 19:32:24,902 - INFO - Request Parameters - Page 1:
2025-06-29 19:32:24,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 19:32:24,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 19:32:25,355 - INFO - Response - Page 1:
2025-06-29 19:32:25,371 - INFO - 第 1 页获取到 1 条记录
2025-06-29 19:32:25,871 - INFO - 查询完成，共获取到 1 条记录
2025-06-29 19:32:25,871 - INFO - 获取到 1 条表单数据
2025-06-29 19:32:25,871 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-06-29 19:32:25,871 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 19:32:25,871 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 19:32:25,871 - INFO - 同步完成
2025-06-29 22:30:33,936 - INFO - 使用默认增量同步（当天更新数据）
2025-06-29 22:30:33,936 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-29 22:30:33,936 - INFO - 查询参数: ('2025-06-29',)
2025-06-29 22:30:34,170 - INFO - MySQL查询成功，增量数据（日期: 2025-06-29），共获取 146 条记录
2025-06-29 22:30:34,170 - INFO - 获取到 4 个日期需要处理: ['2025-06-16', '2025-06-27', '2025-06-28', '2025-06-29']
2025-06-29 22:30:34,170 - INFO - 开始处理日期: 2025-06-16
2025-06-29 22:30:34,170 - INFO - Request Parameters - Page 1:
2025-06-29 22:30:34,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:34,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:42,283 - ERROR - 处理日期 2025-06-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3875049D-A407-78A1-BFC6-039A48C4AEB2 Response: {'code': 'ServiceUnavailable', 'requestid': '3875049D-A407-78A1-BFC6-039A48C4AEB2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3875049D-A407-78A1-BFC6-039A48C4AEB2)
2025-06-29 22:30:42,283 - INFO - 开始处理日期: 2025-06-27
2025-06-29 22:30:42,283 - INFO - Request Parameters - Page 1:
2025-06-29 22:30:42,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:42,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:43,440 - INFO - Response - Page 1:
2025-06-29 22:30:43,440 - INFO - 第 1 页获取到 50 条记录
2025-06-29 22:30:43,956 - INFO - Request Parameters - Page 2:
2025-06-29 22:30:43,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:43,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:44,675 - INFO - Response - Page 2:
2025-06-29 22:30:44,675 - INFO - 第 2 页获取到 50 条记录
2025-06-29 22:30:45,190 - INFO - Request Parameters - Page 3:
2025-06-29 22:30:45,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:45,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:45,894 - INFO - Response - Page 3:
2025-06-29 22:30:45,894 - INFO - 第 3 页获取到 50 条记录
2025-06-29 22:30:46,394 - INFO - Request Parameters - Page 4:
2025-06-29 22:30:46,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:46,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:52,350 - INFO - Response - Page 4:
2025-06-29 22:30:52,350 - INFO - 第 4 页获取到 50 条记录
2025-06-29 22:30:52,850 - INFO - Request Parameters - Page 5:
2025-06-29 22:30:52,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:52,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:53,444 - INFO - Response - Page 5:
2025-06-29 22:30:53,444 - INFO - 第 5 页获取到 50 条记录
2025-06-29 22:30:53,944 - INFO - Request Parameters - Page 6:
2025-06-29 22:30:53,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:53,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:54,600 - INFO - Response - Page 6:
2025-06-29 22:30:54,600 - INFO - 第 6 页获取到 50 条记录
2025-06-29 22:30:55,116 - INFO - Request Parameters - Page 7:
2025-06-29 22:30:55,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:55,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:55,710 - INFO - Response - Page 7:
2025-06-29 22:30:55,710 - INFO - 第 7 页获取到 50 条记录
2025-06-29 22:30:56,226 - INFO - Request Parameters - Page 8:
2025-06-29 22:30:56,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:56,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:56,867 - INFO - Response - Page 8:
2025-06-29 22:30:56,867 - INFO - 第 8 页获取到 50 条记录
2025-06-29 22:30:57,383 - INFO - Request Parameters - Page 9:
2025-06-29 22:30:57,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:57,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:58,039 - INFO - Response - Page 9:
2025-06-29 22:30:58,039 - INFO - 第 9 页获取到 50 条记录
2025-06-29 22:30:58,555 - INFO - Request Parameters - Page 10:
2025-06-29 22:30:58,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:58,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:30:59,133 - INFO - Response - Page 10:
2025-06-29 22:30:59,133 - INFO - 第 10 页获取到 26 条记录
2025-06-29 22:30:59,634 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 22:30:59,634 - INFO - 获取到 476 条表单数据
2025-06-29 22:30:59,634 - INFO - 当前日期 2025-06-27 有 3 条MySQL数据需要处理
2025-06-29 22:30:59,634 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 22:30:59,634 - INFO - 开始处理日期: 2025-06-28
2025-06-29 22:30:59,634 - INFO - Request Parameters - Page 1:
2025-06-29 22:30:59,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:30:59,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:00,243 - INFO - Response - Page 1:
2025-06-29 22:31:00,243 - INFO - 第 1 页获取到 50 条记录
2025-06-29 22:31:00,759 - INFO - Request Parameters - Page 2:
2025-06-29 22:31:00,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:00,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:01,353 - INFO - Response - Page 2:
2025-06-29 22:31:01,353 - INFO - 第 2 页获取到 50 条记录
2025-06-29 22:31:01,853 - INFO - Request Parameters - Page 3:
2025-06-29 22:31:01,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:01,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:02,494 - INFO - Response - Page 3:
2025-06-29 22:31:02,494 - INFO - 第 3 页获取到 50 条记录
2025-06-29 22:31:03,010 - INFO - Request Parameters - Page 4:
2025-06-29 22:31:03,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:03,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:03,651 - INFO - Response - Page 4:
2025-06-29 22:31:03,651 - INFO - 第 4 页获取到 50 条记录
2025-06-29 22:31:04,151 - INFO - Request Parameters - Page 5:
2025-06-29 22:31:04,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:04,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:04,745 - INFO - Response - Page 5:
2025-06-29 22:31:04,745 - INFO - 第 5 页获取到 50 条记录
2025-06-29 22:31:05,245 - INFO - Request Parameters - Page 6:
2025-06-29 22:31:05,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:05,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:05,855 - INFO - Response - Page 6:
2025-06-29 22:31:05,855 - INFO - 第 6 页获取到 50 条记录
2025-06-29 22:31:06,355 - INFO - Request Parameters - Page 7:
2025-06-29 22:31:06,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:06,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:06,965 - INFO - Response - Page 7:
2025-06-29 22:31:06,965 - INFO - 第 7 页获取到 50 条记录
2025-06-29 22:31:07,465 - INFO - Request Parameters - Page 8:
2025-06-29 22:31:07,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:07,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:08,059 - INFO - Response - Page 8:
2025-06-29 22:31:08,059 - INFO - 第 8 页获取到 50 条记录
2025-06-29 22:31:08,559 - INFO - Request Parameters - Page 9:
2025-06-29 22:31:08,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:08,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:09,200 - INFO - Response - Page 9:
2025-06-29 22:31:09,200 - INFO - 第 9 页获取到 50 条记录
2025-06-29 22:31:09,700 - INFO - Request Parameters - Page 10:
2025-06-29 22:31:09,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:09,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:10,310 - INFO - Response - Page 10:
2025-06-29 22:31:10,310 - INFO - 第 10 页获取到 26 条记录
2025-06-29 22:31:10,826 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 22:31:10,826 - INFO - 获取到 476 条表单数据
2025-06-29 22:31:10,826 - INFO - 当前日期 2025-06-28 有 133 条MySQL数据需要处理
2025-06-29 22:31:10,826 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 22:31:10,826 - INFO - 开始处理日期: 2025-06-29
2025-06-29 22:31:10,826 - INFO - Request Parameters - Page 1:
2025-06-29 22:31:10,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:31:10,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:31:11,263 - INFO - Response - Page 1:
2025-06-29 22:31:11,263 - INFO - 第 1 页获取到 1 条记录
2025-06-29 22:31:11,779 - INFO - 查询完成，共获取到 1 条记录
2025-06-29 22:31:11,779 - INFO - 获取到 1 条表单数据
2025-06-29 22:31:11,779 - INFO - 当前日期 2025-06-29 有 5 条MySQL数据需要处理
2025-06-29 22:31:11,779 - INFO - 开始批量插入 4 条新记录
2025-06-29 22:31:11,935 - INFO - 批量插入响应状态码: 200
2025-06-29 22:31:11,935 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 14:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C658C110-3A7A-70EC-8D4A-C902C85C9296', 'x-acs-trace-id': '077a12f4cbb3347dbb4dde9e0de5564b', 'etag': '2PILcH84x5U28duwQzGF/SQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 22:31:11,935 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B1RXPWAA6X9VB0Z3B0RSE83JRLPRHCMQ5', 'FINST-LR5668B1RXPWAA6X9VB0Z3B0RSE83JRLPRHCMR5', 'FINST-LR5668B1RXPWAA6X9VB0Z3B0RSE83JRLPRHCMS5', 'FINST-LR5668B1RXPWAA6X9VB0Z3B0RSE83JRLPRHCMT5']}
2025-06-29 22:31:11,935 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-29 22:31:11,935 - INFO - 成功插入的数据ID: ['FINST-LR5668B1RXPWAA6X9VB0Z3B0RSE83JRLPRHCMQ5', 'FINST-LR5668B1RXPWAA6X9VB0Z3B0RSE83JRLPRHCMR5', 'FINST-LR5668B1RXPWAA6X9VB0Z3B0RSE83JRLPRHCMS5', 'FINST-LR5668B1RXPWAA6X9VB0Z3B0RSE83JRLPRHCMT5']
2025-06-29 22:31:16,953 - INFO - 批量插入完成，共 4 条记录
2025-06-29 22:31:16,953 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-06-29 22:31:16,953 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 1 条
2025-06-29 22:32:16,992 - INFO - 开始同步昨天与今天的销售数据: 2025-06-28 至 2025-06-29
2025-06-29 22:32:16,992 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-29 22:32:16,992 - INFO - 查询参数: ('2025-06-28', '2025-06-29')
2025-06-29 22:32:17,149 - INFO - MySQL查询成功，时间段: 2025-06-28 至 2025-06-29，共获取 501 条记录
2025-06-29 22:32:17,149 - INFO - 获取到 2 个日期需要处理: ['2025-06-28', '2025-06-29']
2025-06-29 22:32:17,149 - INFO - 开始处理日期: 2025-06-28
2025-06-29 22:32:17,149 - INFO - Request Parameters - Page 1:
2025-06-29 22:32:17,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:17,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:17,805 - INFO - Response - Page 1:
2025-06-29 22:32:17,805 - INFO - 第 1 页获取到 50 条记录
2025-06-29 22:32:18,305 - INFO - Request Parameters - Page 2:
2025-06-29 22:32:18,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:18,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:18,993 - INFO - Response - Page 2:
2025-06-29 22:32:18,993 - INFO - 第 2 页获取到 50 条记录
2025-06-29 22:32:19,509 - INFO - Request Parameters - Page 3:
2025-06-29 22:32:19,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:19,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:20,134 - INFO - Response - Page 3:
2025-06-29 22:32:20,134 - INFO - 第 3 页获取到 50 条记录
2025-06-29 22:32:20,635 - INFO - Request Parameters - Page 4:
2025-06-29 22:32:20,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:20,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:21,291 - INFO - Response - Page 4:
2025-06-29 22:32:21,291 - INFO - 第 4 页获取到 50 条记录
2025-06-29 22:32:21,807 - INFO - Request Parameters - Page 5:
2025-06-29 22:32:21,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:21,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:22,448 - INFO - Response - Page 5:
2025-06-29 22:32:22,448 - INFO - 第 5 页获取到 50 条记录
2025-06-29 22:32:22,964 - INFO - Request Parameters - Page 6:
2025-06-29 22:32:22,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:22,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:23,526 - INFO - Response - Page 6:
2025-06-29 22:32:23,526 - INFO - 第 6 页获取到 50 条记录
2025-06-29 22:32:24,042 - INFO - Request Parameters - Page 7:
2025-06-29 22:32:24,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:24,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:24,699 - INFO - Response - Page 7:
2025-06-29 22:32:24,699 - INFO - 第 7 页获取到 50 条记录
2025-06-29 22:32:25,214 - INFO - Request Parameters - Page 8:
2025-06-29 22:32:25,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:25,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:25,824 - INFO - Response - Page 8:
2025-06-29 22:32:25,824 - INFO - 第 8 页获取到 50 条记录
2025-06-29 22:32:26,340 - INFO - Request Parameters - Page 9:
2025-06-29 22:32:26,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:26,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:27,012 - INFO - Response - Page 9:
2025-06-29 22:32:27,012 - INFO - 第 9 页获取到 50 条记录
2025-06-29 22:32:27,528 - INFO - Request Parameters - Page 10:
2025-06-29 22:32:27,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:27,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:28,122 - INFO - Response - Page 10:
2025-06-29 22:32:28,122 - INFO - 第 10 页获取到 26 条记录
2025-06-29 22:32:28,638 - INFO - 查询完成，共获取到 476 条记录
2025-06-29 22:32:28,638 - INFO - 获取到 476 条表单数据
2025-06-29 22:32:28,638 - INFO - 当前日期 2025-06-28 有 477 条MySQL数据需要处理
2025-06-29 22:32:28,653 - INFO - 开始批量插入 1 条新记录
2025-06-29 22:32:28,794 - INFO - 批量插入响应状态码: 200
2025-06-29 22:32:28,794 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 14:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F2581D45-AED2-7BFD-B80D-A099DF145DEF', 'x-acs-trace-id': '85a67a85bae8dc30ee79e1ea0f6de07b', 'etag': '6AMHX+4luOMiIYoxwmT7z1g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 22:32:28,794 - INFO - 批量插入响应体: {'result': ['FINST-8SG66JA1JXPWT6MY8KUTB566RY5R3Y19RRHCMA2']}
2025-06-29 22:32:28,794 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-29 22:32:28,794 - INFO - 成功插入的数据ID: ['FINST-8SG66JA1JXPWT6MY8KUTB566RY5R3Y19RRHCMA2']
2025-06-29 22:32:33,812 - INFO - 批量插入完成，共 1 条记录
2025-06-29 22:32:33,812 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-29 22:32:33,812 - INFO - 开始处理日期: 2025-06-29
2025-06-29 22:32:33,812 - INFO - Request Parameters - Page 1:
2025-06-29 22:32:33,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 22:32:33,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 22:32:34,281 - INFO - Response - Page 1:
2025-06-29 22:32:34,281 - INFO - 第 1 页获取到 5 条记录
2025-06-29 22:32:34,781 - INFO - 查询完成，共获取到 5 条记录
2025-06-29 22:32:34,781 - INFO - 获取到 5 条表单数据
2025-06-29 22:32:34,781 - INFO - 当前日期 2025-06-29 有 5 条MySQL数据需要处理
2025-06-29 22:32:34,781 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 22:32:34,781 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-29 22:32:34,781 - INFO - 同步完成
