2025-05-17 00:00:02,870 - INFO - =================使用默认全量同步=============
2025-05-17 00:00:04,289 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-17 00:00:04,289 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-17 00:00:04,316 - INFO - 开始处理日期: 2025-01
2025-05-17 00:00:04,319 - INFO - Request Parameters - Page 1:
2025-05-17 00:00:04,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:04,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:05,206 - INFO - Response - Page 1:
2025-05-17 00:00:05,407 - INFO - 第 1 页获取到 100 条记录
2025-05-17 00:00:05,407 - INFO - Request Parameters - Page 2:
2025-05-17 00:00:05,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:05,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:06,031 - INFO - Response - Page 2:
2025-05-17 00:00:06,231 - INFO - 第 2 页获取到 100 条记录
2025-05-17 00:00:06,231 - INFO - Request Parameters - Page 3:
2025-05-17 00:00:06,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:06,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:07,198 - INFO - Response - Page 3:
2025-05-17 00:00:07,399 - INFO - 第 3 页获取到 100 条记录
2025-05-17 00:00:07,399 - INFO - Request Parameters - Page 4:
2025-05-17 00:00:07,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:07,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:07,882 - INFO - Response - Page 4:
2025-05-17 00:00:08,083 - INFO - 第 4 页获取到 100 条记录
2025-05-17 00:00:08,083 - INFO - Request Parameters - Page 5:
2025-05-17 00:00:08,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:08,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:08,541 - INFO - Response - Page 5:
2025-05-17 00:00:08,743 - INFO - 第 5 页获取到 100 条记录
2025-05-17 00:00:08,743 - INFO - Request Parameters - Page 6:
2025-05-17 00:00:08,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:08,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:09,304 - INFO - Response - Page 6:
2025-05-17 00:00:09,505 - INFO - 第 6 页获取到 100 条记录
2025-05-17 00:00:09,505 - INFO - Request Parameters - Page 7:
2025-05-17 00:00:09,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:09,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:10,031 - INFO - Response - Page 7:
2025-05-17 00:00:10,231 - INFO - 第 7 页获取到 82 条记录
2025-05-17 00:00:10,231 - INFO - 查询完成，共获取到 682 条记录
2025-05-17 00:00:10,233 - INFO - 获取到 682 条表单数据
2025-05-17 00:00:10,253 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-17 00:00:10,267 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 00:00:10,267 - INFO - 开始处理日期: 2025-02
2025-05-17 00:00:10,267 - INFO - Request Parameters - Page 1:
2025-05-17 00:00:10,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:10,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:10,767 - INFO - Response - Page 1:
2025-05-17 00:00:10,967 - INFO - 第 1 页获取到 100 条记录
2025-05-17 00:00:10,967 - INFO - Request Parameters - Page 2:
2025-05-17 00:00:10,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:10,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:11,458 - INFO - Response - Page 2:
2025-05-17 00:00:11,659 - INFO - 第 2 页获取到 100 条记录
2025-05-17 00:00:11,659 - INFO - Request Parameters - Page 3:
2025-05-17 00:00:11,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:11,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:12,227 - INFO - Response - Page 3:
2025-05-17 00:00:12,429 - INFO - 第 3 页获取到 100 条记录
2025-05-17 00:00:12,429 - INFO - Request Parameters - Page 4:
2025-05-17 00:00:12,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:12,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:12,881 - INFO - Response - Page 4:
2025-05-17 00:00:13,082 - INFO - 第 4 页获取到 100 条记录
2025-05-17 00:00:13,082 - INFO - Request Parameters - Page 5:
2025-05-17 00:00:13,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:13,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:13,545 - INFO - Response - Page 5:
2025-05-17 00:00:13,745 - INFO - 第 5 页获取到 100 条记录
2025-05-17 00:00:13,745 - INFO - Request Parameters - Page 6:
2025-05-17 00:00:13,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:13,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:14,260 - INFO - Response - Page 6:
2025-05-17 00:00:14,461 - INFO - 第 6 页获取到 100 条记录
2025-05-17 00:00:14,461 - INFO - Request Parameters - Page 7:
2025-05-17 00:00:14,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:14,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:14,929 - INFO - Response - Page 7:
2025-05-17 00:00:15,130 - INFO - 第 7 页获取到 70 条记录
2025-05-17 00:00:15,130 - INFO - 查询完成，共获取到 670 条记录
2025-05-17 00:00:15,130 - INFO - 获取到 670 条表单数据
2025-05-17 00:00:15,141 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-17 00:00:15,153 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 00:00:15,153 - INFO - 开始处理日期: 2025-03
2025-05-17 00:00:15,153 - INFO - Request Parameters - Page 1:
2025-05-17 00:00:15,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:15,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:15,683 - INFO - Response - Page 1:
2025-05-17 00:00:15,883 - INFO - 第 1 页获取到 100 条记录
2025-05-17 00:00:15,883 - INFO - Request Parameters - Page 2:
2025-05-17 00:00:15,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:15,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:16,355 - INFO - Response - Page 2:
2025-05-17 00:00:16,555 - INFO - 第 2 页获取到 100 条记录
2025-05-17 00:00:16,555 - INFO - Request Parameters - Page 3:
2025-05-17 00:00:16,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:16,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:17,088 - INFO - Response - Page 3:
2025-05-17 00:00:17,289 - INFO - 第 3 页获取到 100 条记录
2025-05-17 00:00:17,289 - INFO - Request Parameters - Page 4:
2025-05-17 00:00:17,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:17,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:17,814 - INFO - Response - Page 4:
2025-05-17 00:00:18,014 - INFO - 第 4 页获取到 100 条记录
2025-05-17 00:00:18,014 - INFO - Request Parameters - Page 5:
2025-05-17 00:00:18,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:18,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:18,507 - INFO - Response - Page 5:
2025-05-17 00:00:18,708 - INFO - 第 5 页获取到 100 条记录
2025-05-17 00:00:18,708 - INFO - Request Parameters - Page 6:
2025-05-17 00:00:18,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:18,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:19,262 - INFO - Response - Page 6:
2025-05-17 00:00:19,463 - INFO - 第 6 页获取到 100 条记录
2025-05-17 00:00:19,463 - INFO - Request Parameters - Page 7:
2025-05-17 00:00:19,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:19,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:19,934 - INFO - Response - Page 7:
2025-05-17 00:00:20,134 - INFO - 第 7 页获取到 61 条记录
2025-05-17 00:00:20,134 - INFO - 查询完成，共获取到 661 条记录
2025-05-17 00:00:20,134 - INFO - 获取到 661 条表单数据
2025-05-17 00:00:20,148 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-17 00:00:20,160 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 00:00:20,160 - INFO - 开始处理日期: 2025-04
2025-05-17 00:00:20,160 - INFO - Request Parameters - Page 1:
2025-05-17 00:00:20,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:20,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:20,699 - INFO - Response - Page 1:
2025-05-17 00:00:20,899 - INFO - 第 1 页获取到 100 条记录
2025-05-17 00:00:20,899 - INFO - Request Parameters - Page 2:
2025-05-17 00:00:20,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:20,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:21,422 - INFO - Response - Page 2:
2025-05-17 00:00:21,622 - INFO - 第 2 页获取到 100 条记录
2025-05-17 00:00:21,622 - INFO - Request Parameters - Page 3:
2025-05-17 00:00:21,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:21,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:22,139 - INFO - Response - Page 3:
2025-05-17 00:00:22,339 - INFO - 第 3 页获取到 100 条记录
2025-05-17 00:00:22,339 - INFO - Request Parameters - Page 4:
2025-05-17 00:00:22,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:22,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:22,874 - INFO - Response - Page 4:
2025-05-17 00:00:23,074 - INFO - 第 4 页获取到 100 条记录
2025-05-17 00:00:23,074 - INFO - Request Parameters - Page 5:
2025-05-17 00:00:23,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:23,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:23,610 - INFO - Response - Page 5:
2025-05-17 00:00:23,810 - INFO - 第 5 页获取到 100 条记录
2025-05-17 00:00:23,810 - INFO - Request Parameters - Page 6:
2025-05-17 00:00:23,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:23,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:24,268 - INFO - Response - Page 6:
2025-05-17 00:00:24,468 - INFO - 第 6 页获取到 100 条记录
2025-05-17 00:00:24,468 - INFO - Request Parameters - Page 7:
2025-05-17 00:00:24,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:24,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:24,861 - INFO - Response - Page 7:
2025-05-17 00:00:25,061 - INFO - 第 7 页获取到 54 条记录
2025-05-17 00:00:25,061 - INFO - 查询完成，共获取到 654 条记录
2025-05-17 00:00:25,061 - INFO - 获取到 654 条表单数据
2025-05-17 00:00:25,073 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-17 00:00:25,084 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 00:00:25,084 - INFO - 开始处理日期: 2025-05
2025-05-17 00:00:25,084 - INFO - Request Parameters - Page 1:
2025-05-17 00:00:25,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:25,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:25,569 - INFO - Response - Page 1:
2025-05-17 00:00:25,769 - INFO - 第 1 页获取到 100 条记录
2025-05-17 00:00:25,769 - INFO - Request Parameters - Page 2:
2025-05-17 00:00:25,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:25,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:26,298 - INFO - Response - Page 2:
2025-05-17 00:00:26,498 - INFO - 第 2 页获取到 100 条记录
2025-05-17 00:00:26,498 - INFO - Request Parameters - Page 3:
2025-05-17 00:00:26,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:26,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:27,185 - INFO - Response - Page 3:
2025-05-17 00:00:27,385 - INFO - 第 3 页获取到 100 条记录
2025-05-17 00:00:27,385 - INFO - Request Parameters - Page 4:
2025-05-17 00:00:27,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:27,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:27,815 - INFO - Response - Page 4:
2025-05-17 00:00:28,015 - INFO - 第 4 页获取到 100 条记录
2025-05-17 00:00:28,015 - INFO - Request Parameters - Page 5:
2025-05-17 00:00:28,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:28,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:28,520 - INFO - Response - Page 5:
2025-05-17 00:00:28,720 - INFO - 第 5 页获取到 100 条记录
2025-05-17 00:00:28,720 - INFO - Request Parameters - Page 6:
2025-05-17 00:00:28,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:28,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:29,233 - INFO - Response - Page 6:
2025-05-17 00:00:29,434 - INFO - 第 6 页获取到 100 条记录
2025-05-17 00:00:29,434 - INFO - Request Parameters - Page 7:
2025-05-17 00:00:29,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:00:29,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:00:29,750 - INFO - Response - Page 7:
2025-05-17 00:00:29,950 - INFO - 第 7 页获取到 25 条记录
2025-05-17 00:00:29,950 - INFO - 查询完成，共获取到 625 条记录
2025-05-17 00:00:29,950 - INFO - 获取到 625 条表单数据
2025-05-17 00:00:29,962 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-17 00:00:29,963 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-17 00:00:30,421 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-17 00:00:30,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74368.0, 'new_value': 79656.0}, {'field': 'offline_amount', 'old_value': 81145.28, 'new_value': 85339.28}, {'field': 'total_amount', 'old_value': 155513.28, 'new_value': 164995.28}, {'field': 'order_count', 'old_value': 3325, 'new_value': 3535}]
2025-05-17 00:00:30,422 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-17 00:00:30,883 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-17 00:00:30,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29248.0, 'new_value': 29367.7}, {'field': 'total_amount', 'old_value': 33208.0, 'new_value': 33327.7}, {'field': 'order_count', 'old_value': 258, 'new_value': 270}]
2025-05-17 00:00:30,884 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-17 00:00:31,266 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-17 00:00:31,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49664.0, 'new_value': 52151.0}, {'field': 'offline_amount', 'old_value': 64216.0, 'new_value': 67348.0}, {'field': 'total_amount', 'old_value': 113880.0, 'new_value': 119499.0}, {'field': 'order_count', 'old_value': 2726, 'new_value': 2879}]
2025-05-17 00:00:31,266 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-17 00:00:31,706 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-17 00:00:31,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14840.2, 'new_value': 16466.2}, {'field': 'offline_amount', 'old_value': 77119.84, 'new_value': 94926.84}, {'field': 'total_amount', 'old_value': 91960.04, 'new_value': 111393.04}, {'field': 'order_count', 'old_value': 157, 'new_value': 175}]
2025-05-17 00:00:31,707 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-17 00:00:32,157 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-17 00:00:32,158 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28780.58, 'new_value': 31912.36}, {'field': 'offline_amount', 'old_value': 418017.64, 'new_value': 456247.24}, {'field': 'total_amount', 'old_value': 446798.22, 'new_value': 488159.6}, {'field': 'order_count', 'old_value': 1841, 'new_value': 1994}]
2025-05-17 00:00:32,159 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-17 00:00:32,523 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-17 00:00:32,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19352.7, 'new_value': 20315.7}, {'field': 'total_amount', 'old_value': 19352.7, 'new_value': 20315.7}, {'field': 'order_count', 'old_value': 106, 'new_value': 111}]
2025-05-17 00:00:32,523 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-17 00:00:32,977 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-17 00:00:32,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95802.0, 'new_value': 107782.0}, {'field': 'total_amount', 'old_value': 103377.8, 'new_value': 115357.8}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-17 00:00:32,977 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-17 00:00:33,392 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-17 00:00:33,392 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25540.49, 'new_value': 27238.96}, {'field': 'offline_amount', 'old_value': 59046.58, 'new_value': 61238.27}, {'field': 'total_amount', 'old_value': 84587.07, 'new_value': 88477.23}, {'field': 'order_count', 'old_value': 3059, 'new_value': 3222}]
2025-05-17 00:00:33,393 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-17 00:00:33,819 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-17 00:00:33,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60203.27, 'new_value': 63268.43}, {'field': 'total_amount', 'old_value': 60203.27, 'new_value': 63268.43}, {'field': 'order_count', 'old_value': 2318, 'new_value': 2439}]
2025-05-17 00:00:33,820 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-17 00:00:34,268 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-17 00:00:34,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9090.22, 'new_value': 9386.82}, {'field': 'total_amount', 'old_value': 15667.46, 'new_value': 15964.06}, {'field': 'order_count', 'old_value': 65, 'new_value': 67}]
2025-05-17 00:00:34,268 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-17 00:00:34,688 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-17 00:00:34,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36253.0, 'new_value': 40483.0}, {'field': 'total_amount', 'old_value': 36253.0, 'new_value': 40483.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-17 00:00:34,688 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-17 00:00:35,041 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-17 00:00:35,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65409.5, 'new_value': 68861.1}, {'field': 'total_amount', 'old_value': 65409.5, 'new_value': 68861.1}, {'field': 'order_count', 'old_value': 201, 'new_value': 214}]
2025-05-17 00:00:35,042 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-17 00:00:35,425 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-17 00:00:35,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31549.0, 'new_value': 32605.0}, {'field': 'total_amount', 'old_value': 31549.0, 'new_value': 32605.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 85}]
2025-05-17 00:00:35,426 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-17 00:00:35,811 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-17 00:00:35,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23795.0, 'new_value': 23954.0}, {'field': 'total_amount', 'old_value': 23795.0, 'new_value': 23954.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-05-17 00:00:35,811 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-17 00:00:36,192 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-17 00:00:36,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4132.0, 'new_value': 4427.0}, {'field': 'total_amount', 'old_value': 4132.0, 'new_value': 4427.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-17 00:00:36,193 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-17 00:00:36,579 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-17 00:00:36,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1125849.35, 'new_value': 1186209.24}, {'field': 'total_amount', 'old_value': 1125849.35, 'new_value': 1186209.24}, {'field': 'order_count', 'old_value': 8839, 'new_value': 9384}]
2025-05-17 00:00:36,580 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-17 00:00:37,093 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-17 00:00:37,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4636.79, 'new_value': 5274.47}, {'field': 'offline_amount', 'old_value': 8034.24, 'new_value': 8312.34}, {'field': 'total_amount', 'old_value': 12671.03, 'new_value': 13586.81}, {'field': 'order_count', 'old_value': 953, 'new_value': 1021}]
2025-05-17 00:00:37,093 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-17 00:00:37,482 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-17 00:00:37,482 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30216.1, 'new_value': 31294.69}, {'field': 'offline_amount', 'old_value': 249848.56, 'new_value': 260063.11}, {'field': 'total_amount', 'old_value': 280064.66, 'new_value': 291357.8}, {'field': 'order_count', 'old_value': 2361, 'new_value': 2465}]
2025-05-17 00:00:37,483 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-17 00:00:37,907 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-17 00:00:37,908 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55496.77, 'new_value': 58125.03}, {'field': 'offline_amount', 'old_value': 25999.94, 'new_value': 27297.14}, {'field': 'total_amount', 'old_value': 81496.71, 'new_value': 85422.17}, {'field': 'order_count', 'old_value': 5171, 'new_value': 5423}]
2025-05-17 00:00:37,908 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-17 00:00:38,335 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-17 00:00:38,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56205.28, 'new_value': 60960.61}, {'field': 'offline_amount', 'old_value': 156317.16, 'new_value': 163660.19}, {'field': 'total_amount', 'old_value': 212522.44, 'new_value': 224620.8}, {'field': 'order_count', 'old_value': 6904, 'new_value': 7348}]
2025-05-17 00:00:38,335 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-17 00:00:38,758 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-17 00:00:38,758 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 184749.33, 'new_value': 194801.43}, {'field': 'offline_amount', 'old_value': 11153.3, 'new_value': 11731.5}, {'field': 'total_amount', 'old_value': 195902.63, 'new_value': 206532.93}, {'field': 'order_count', 'old_value': 7305, 'new_value': 7706}]
2025-05-17 00:00:38,759 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-17 00:00:39,180 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-17 00:00:39,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72451.42, 'new_value': 78016.46}, {'field': 'offline_amount', 'old_value': 171152.35, 'new_value': 177386.72}, {'field': 'total_amount', 'old_value': 243603.77, 'new_value': 255403.18}, {'field': 'order_count', 'old_value': 3052, 'new_value': 3237}]
2025-05-17 00:00:39,181 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-17 00:00:39,680 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-17 00:00:39,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53801.5, 'new_value': 55601.9}, {'field': 'total_amount', 'old_value': 53801.5, 'new_value': 55601.9}, {'field': 'order_count', 'old_value': 407, 'new_value': 417}]
2025-05-17 00:00:39,681 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-17 00:00:40,105 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-17 00:00:40,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14224.23, 'new_value': 14864.63}, {'field': 'total_amount', 'old_value': 15574.23, 'new_value': 16214.63}, {'field': 'order_count', 'old_value': 293, 'new_value': 306}]
2025-05-17 00:00:40,106 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-17 00:00:40,599 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-17 00:00:40,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20686.0, 'new_value': 22042.0}, {'field': 'total_amount', 'old_value': 20686.0, 'new_value': 22042.0}, {'field': 'order_count', 'old_value': 193, 'new_value': 201}]
2025-05-17 00:00:40,599 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-17 00:00:41,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-17 00:00:41,062 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49841.13, 'new_value': 53449.68}, {'field': 'offline_amount', 'old_value': 80688.98, 'new_value': 83756.08}, {'field': 'total_amount', 'old_value': 130530.11, 'new_value': 137205.76}, {'field': 'order_count', 'old_value': 1285, 'new_value': 1353}]
2025-05-17 00:00:41,063 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-17 00:00:41,527 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-17 00:00:41,527 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6274.6, 'new_value': 6523.6}, {'field': 'total_amount', 'old_value': 7102.6, 'new_value': 7351.6}, {'field': 'order_count', 'old_value': 72, 'new_value': 75}]
2025-05-17 00:00:41,528 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-17 00:00:41,992 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-17 00:00:41,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19863.0, 'new_value': 20387.0}, {'field': 'total_amount', 'old_value': 19863.0, 'new_value': 20387.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 89}]
2025-05-17 00:00:41,992 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-17 00:00:42,427 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-17 00:00:42,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59926.4, 'new_value': 62685.4}, {'field': 'offline_amount', 'old_value': 89550.1, 'new_value': 91833.7}, {'field': 'total_amount', 'old_value': 149476.5, 'new_value': 154519.1}, {'field': 'order_count', 'old_value': 3010, 'new_value': 3111}]
2025-05-17 00:00:42,427 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-17 00:00:42,918 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-17 00:00:42,918 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 296566.56, 'new_value': 311724.79}, {'field': 'total_amount', 'old_value': 296566.56, 'new_value': 311724.79}, {'field': 'order_count', 'old_value': 4019, 'new_value': 4252}]
2025-05-17 00:00:42,918 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-17 00:00:43,367 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-17 00:00:43,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55683.62, 'new_value': 58245.02}, {'field': 'total_amount', 'old_value': 55683.62, 'new_value': 58245.02}, {'field': 'order_count', 'old_value': 1682, 'new_value': 1768}]
2025-05-17 00:00:43,367 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-17 00:00:43,778 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-17 00:00:43,778 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5788.46, 'new_value': 6222.9}, {'field': 'offline_amount', 'old_value': 18832.88, 'new_value': 19946.18}, {'field': 'total_amount', 'old_value': 24621.34, 'new_value': 26169.08}, {'field': 'order_count', 'old_value': 869, 'new_value': 917}]
2025-05-17 00:00:43,778 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-17 00:00:44,184 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-17 00:00:44,184 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91791.54, 'new_value': 95615.11}, {'field': 'total_amount', 'old_value': 91791.54, 'new_value': 95615.11}, {'field': 'order_count', 'old_value': 2355, 'new_value': 2462}]
2025-05-17 00:00:44,185 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-17 00:00:44,576 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-17 00:00:44,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20719.09, 'new_value': 21909.35}, {'field': 'offline_amount', 'old_value': 206717.47, 'new_value': 215858.59}, {'field': 'total_amount', 'old_value': 227436.56, 'new_value': 237767.94}, {'field': 'order_count', 'old_value': 5273, 'new_value': 5489}]
2025-05-17 00:00:44,576 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-17 00:00:44,975 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-17 00:00:44,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289122.9, 'new_value': 299554.7}, {'field': 'total_amount', 'old_value': 289122.9, 'new_value': 299554.7}, {'field': 'order_count', 'old_value': 1427, 'new_value': 1488}]
2025-05-17 00:00:44,976 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-17 00:00:45,432 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-17 00:00:45,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128907.0, 'new_value': 132364.0}, {'field': 'total_amount', 'old_value': 128907.0, 'new_value': 132364.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 151}]
2025-05-17 00:00:45,432 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-17 00:00:45,937 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-17 00:00:45,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 458749.59, 'new_value': 475901.84}, {'field': 'total_amount', 'old_value': 458749.59, 'new_value': 475901.84}, {'field': 'order_count', 'old_value': 8991, 'new_value': 9337}]
2025-05-17 00:00:45,938 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-17 00:00:46,341 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-17 00:00:46,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173449.33, 'new_value': 184881.79}, {'field': 'total_amount', 'old_value': 173449.33, 'new_value': 184881.79}, {'field': 'order_count', 'old_value': 7264, 'new_value': 7724}]
2025-05-17 00:00:46,342 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-17 00:00:46,738 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-17 00:00:46,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141042.0, 'new_value': 143972.0}, {'field': 'total_amount', 'old_value': 141042.0, 'new_value': 143972.0}, {'field': 'order_count', 'old_value': 425, 'new_value': 434}]
2025-05-17 00:00:46,738 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-17 00:00:47,148 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-17 00:00:47,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 439102.5, 'new_value': 465865.89}, {'field': 'total_amount', 'old_value': 439102.5, 'new_value': 465865.89}, {'field': 'order_count', 'old_value': 2964, 'new_value': 3168}]
2025-05-17 00:00:47,148 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-17 00:00:47,566 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-17 00:00:47,567 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39230.55, 'new_value': 41729.8}, {'field': 'offline_amount', 'old_value': 57577.74, 'new_value': 60119.73}, {'field': 'total_amount', 'old_value': 96808.29, 'new_value': 101849.53}, {'field': 'order_count', 'old_value': 4444, 'new_value': 4690}]
2025-05-17 00:00:47,567 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-17 00:00:48,002 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-17 00:00:48,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220760.28, 'new_value': 232797.28}, {'field': 'total_amount', 'old_value': 242923.4, 'new_value': 254960.4}, {'field': 'order_count', 'old_value': 10172, 'new_value': 10716}]
2025-05-17 00:00:48,002 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-17 00:00:48,511 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-17 00:00:48,511 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17604.89, 'new_value': 18786.45}, {'field': 'offline_amount', 'old_value': 148710.14, 'new_value': 154840.34}, {'field': 'total_amount', 'old_value': 166315.03, 'new_value': 173626.79}, {'field': 'order_count', 'old_value': 5159, 'new_value': 5398}]
2025-05-17 00:00:48,512 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-17 00:00:48,882 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-17 00:00:48,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20882.2, 'new_value': 22935.7}, {'field': 'total_amount', 'old_value': 20882.2, 'new_value': 22935.7}, {'field': 'order_count', 'old_value': 120, 'new_value': 130}]
2025-05-17 00:00:48,883 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-17 00:00:49,336 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-17 00:00:49,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20960.66, 'new_value': 21960.66}, {'field': 'offline_amount', 'old_value': 31095.62, 'new_value': 33033.54}, {'field': 'total_amount', 'old_value': 52056.28, 'new_value': 54994.2}, {'field': 'order_count', 'old_value': 2378, 'new_value': 2536}]
2025-05-17 00:00:49,337 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-17 00:00:49,845 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-17 00:00:49,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25943.28, 'new_value': 27919.79}, {'field': 'offline_amount', 'old_value': 17014.77, 'new_value': 17708.7}, {'field': 'total_amount', 'old_value': 42958.05, 'new_value': 45628.49}, {'field': 'order_count', 'old_value': 1859, 'new_value': 1973}]
2025-05-17 00:00:49,848 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-17 00:00:50,287 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-17 00:00:50,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223832.0, 'new_value': 224290.0}, {'field': 'total_amount', 'old_value': 228188.0, 'new_value': 228646.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-05-17 00:00:50,288 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-17 00:00:50,684 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-17 00:00:50,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22073.49, 'new_value': 23902.83}, {'field': 'total_amount', 'old_value': 22073.49, 'new_value': 23902.83}, {'field': 'order_count', 'old_value': 898, 'new_value': 1020}]
2025-05-17 00:00:50,684 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-17 00:00:51,158 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-17 00:00:51,158 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2376.62, 'new_value': 2591.12}, {'field': 'offline_amount', 'old_value': 17723.15, 'new_value': 21428.06}, {'field': 'total_amount', 'old_value': 20099.77, 'new_value': 24019.18}, {'field': 'order_count', 'old_value': 809, 'new_value': 947}]
2025-05-17 00:00:51,158 - INFO - 日期 2025-05 处理完成 - 更新: 49 条，插入: 0 条，错误: 0 条
2025-05-17 00:00:51,158 - INFO - 数据同步完成！更新: 49 条，插入: 0 条，错误: 0 条
2025-05-17 00:00:51,160 - INFO - =================同步完成====================
2025-05-17 03:00:02,469 - INFO - =================使用默认全量同步=============
2025-05-17 03:00:03,848 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-17 03:00:03,848 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-17 03:00:03,875 - INFO - 开始处理日期: 2025-01
2025-05-17 03:00:03,877 - INFO - Request Parameters - Page 1:
2025-05-17 03:00:03,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:03,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:05,236 - INFO - Response - Page 1:
2025-05-17 03:00:05,436 - INFO - 第 1 页获取到 100 条记录
2025-05-17 03:00:05,436 - INFO - Request Parameters - Page 2:
2025-05-17 03:00:05,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:05,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:05,932 - INFO - Response - Page 2:
2025-05-17 03:00:06,133 - INFO - 第 2 页获取到 100 条记录
2025-05-17 03:00:06,133 - INFO - Request Parameters - Page 3:
2025-05-17 03:00:06,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:06,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:06,725 - INFO - Response - Page 3:
2025-05-17 03:00:06,925 - INFO - 第 3 页获取到 100 条记录
2025-05-17 03:00:06,925 - INFO - Request Parameters - Page 4:
2025-05-17 03:00:06,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:06,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:07,483 - INFO - Response - Page 4:
2025-05-17 03:00:07,683 - INFO - 第 4 页获取到 100 条记录
2025-05-17 03:00:07,683 - INFO - Request Parameters - Page 5:
2025-05-17 03:00:07,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:07,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:08,159 - INFO - Response - Page 5:
2025-05-17 03:00:08,359 - INFO - 第 5 页获取到 100 条记录
2025-05-17 03:00:08,359 - INFO - Request Parameters - Page 6:
2025-05-17 03:00:08,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:08,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:08,855 - INFO - Response - Page 6:
2025-05-17 03:00:09,056 - INFO - 第 6 页获取到 100 条记录
2025-05-17 03:00:09,056 - INFO - Request Parameters - Page 7:
2025-05-17 03:00:09,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:09,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:09,574 - INFO - Response - Page 7:
2025-05-17 03:00:09,775 - INFO - 第 7 页获取到 82 条记录
2025-05-17 03:00:09,775 - INFO - 查询完成，共获取到 682 条记录
2025-05-17 03:00:09,775 - INFO - 获取到 682 条表单数据
2025-05-17 03:00:09,786 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-17 03:00:09,798 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 03:00:09,798 - INFO - 开始处理日期: 2025-02
2025-05-17 03:00:09,798 - INFO - Request Parameters - Page 1:
2025-05-17 03:00:09,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:09,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:10,269 - INFO - Response - Page 1:
2025-05-17 03:00:10,469 - INFO - 第 1 页获取到 100 条记录
2025-05-17 03:00:10,469 - INFO - Request Parameters - Page 2:
2025-05-17 03:00:10,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:10,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:10,982 - INFO - Response - Page 2:
2025-05-17 03:00:11,182 - INFO - 第 2 页获取到 100 条记录
2025-05-17 03:00:11,182 - INFO - Request Parameters - Page 3:
2025-05-17 03:00:11,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:11,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:11,659 - INFO - Response - Page 3:
2025-05-17 03:00:11,860 - INFO - 第 3 页获取到 100 条记录
2025-05-17 03:00:11,860 - INFO - Request Parameters - Page 4:
2025-05-17 03:00:11,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:11,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:12,304 - INFO - Response - Page 4:
2025-05-17 03:00:12,505 - INFO - 第 4 页获取到 100 条记录
2025-05-17 03:00:12,505 - INFO - Request Parameters - Page 5:
2025-05-17 03:00:12,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:12,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:12,999 - INFO - Response - Page 5:
2025-05-17 03:00:13,200 - INFO - 第 5 页获取到 100 条记录
2025-05-17 03:00:13,200 - INFO - Request Parameters - Page 6:
2025-05-17 03:00:13,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:13,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:13,711 - INFO - Response - Page 6:
2025-05-17 03:00:13,911 - INFO - 第 6 页获取到 100 条记录
2025-05-17 03:00:13,911 - INFO - Request Parameters - Page 7:
2025-05-17 03:00:13,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:13,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:14,344 - INFO - Response - Page 7:
2025-05-17 03:00:14,544 - INFO - 第 7 页获取到 70 条记录
2025-05-17 03:00:14,544 - INFO - 查询完成，共获取到 670 条记录
2025-05-17 03:00:14,544 - INFO - 获取到 670 条表单数据
2025-05-17 03:00:14,556 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-17 03:00:14,567 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 03:00:14,567 - INFO - 开始处理日期: 2025-03
2025-05-17 03:00:14,567 - INFO - Request Parameters - Page 1:
2025-05-17 03:00:14,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:14,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:15,054 - INFO - Response - Page 1:
2025-05-17 03:00:15,254 - INFO - 第 1 页获取到 100 条记录
2025-05-17 03:00:15,254 - INFO - Request Parameters - Page 2:
2025-05-17 03:00:15,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:15,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:15,842 - INFO - Response - Page 2:
2025-05-17 03:00:16,042 - INFO - 第 2 页获取到 100 条记录
2025-05-17 03:00:16,042 - INFO - Request Parameters - Page 3:
2025-05-17 03:00:16,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:16,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:16,647 - INFO - Response - Page 3:
2025-05-17 03:00:16,848 - INFO - 第 3 页获取到 100 条记录
2025-05-17 03:00:16,848 - INFO - Request Parameters - Page 4:
2025-05-17 03:00:16,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:16,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:17,345 - INFO - Response - Page 4:
2025-05-17 03:00:17,545 - INFO - 第 4 页获取到 100 条记录
2025-05-17 03:00:17,545 - INFO - Request Parameters - Page 5:
2025-05-17 03:00:17,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:17,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:18,042 - INFO - Response - Page 5:
2025-05-17 03:00:18,243 - INFO - 第 5 页获取到 100 条记录
2025-05-17 03:00:18,243 - INFO - Request Parameters - Page 6:
2025-05-17 03:00:18,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:18,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:18,751 - INFO - Response - Page 6:
2025-05-17 03:00:18,952 - INFO - 第 6 页获取到 100 条记录
2025-05-17 03:00:18,952 - INFO - Request Parameters - Page 7:
2025-05-17 03:00:18,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:18,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:19,355 - INFO - Response - Page 7:
2025-05-17 03:00:19,556 - INFO - 第 7 页获取到 61 条记录
2025-05-17 03:00:19,556 - INFO - 查询完成，共获取到 661 条记录
2025-05-17 03:00:19,556 - INFO - 获取到 661 条表单数据
2025-05-17 03:00:19,569 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-17 03:00:19,582 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 03:00:19,582 - INFO - 开始处理日期: 2025-04
2025-05-17 03:00:19,582 - INFO - Request Parameters - Page 1:
2025-05-17 03:00:19,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:19,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:20,164 - INFO - Response - Page 1:
2025-05-17 03:00:20,364 - INFO - 第 1 页获取到 100 条记录
2025-05-17 03:00:20,364 - INFO - Request Parameters - Page 2:
2025-05-17 03:00:20,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:20,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:20,825 - INFO - Response - Page 2:
2025-05-17 03:00:21,026 - INFO - 第 2 页获取到 100 条记录
2025-05-17 03:00:21,026 - INFO - Request Parameters - Page 3:
2025-05-17 03:00:21,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:21,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:21,525 - INFO - Response - Page 3:
2025-05-17 03:00:21,727 - INFO - 第 3 页获取到 100 条记录
2025-05-17 03:00:21,727 - INFO - Request Parameters - Page 4:
2025-05-17 03:00:21,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:21,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:22,293 - INFO - Response - Page 4:
2025-05-17 03:00:22,494 - INFO - 第 4 页获取到 100 条记录
2025-05-17 03:00:22,494 - INFO - Request Parameters - Page 5:
2025-05-17 03:00:22,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:22,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:23,041 - INFO - Response - Page 5:
2025-05-17 03:00:23,241 - INFO - 第 5 页获取到 100 条记录
2025-05-17 03:00:23,241 - INFO - Request Parameters - Page 6:
2025-05-17 03:00:23,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:23,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:23,742 - INFO - Response - Page 6:
2025-05-17 03:00:23,943 - INFO - 第 6 页获取到 100 条记录
2025-05-17 03:00:23,943 - INFO - Request Parameters - Page 7:
2025-05-17 03:00:23,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:23,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:24,356 - INFO - Response - Page 7:
2025-05-17 03:00:24,557 - INFO - 第 7 页获取到 54 条记录
2025-05-17 03:00:24,557 - INFO - 查询完成，共获取到 654 条记录
2025-05-17 03:00:24,557 - INFO - 获取到 654 条表单数据
2025-05-17 03:00:24,570 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-17 03:00:24,585 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 03:00:24,586 - INFO - 开始处理日期: 2025-05
2025-05-17 03:00:24,586 - INFO - Request Parameters - Page 1:
2025-05-17 03:00:24,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:24,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:25,124 - INFO - Response - Page 1:
2025-05-17 03:00:25,324 - INFO - 第 1 页获取到 100 条记录
2025-05-17 03:00:25,324 - INFO - Request Parameters - Page 2:
2025-05-17 03:00:25,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:25,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:25,836 - INFO - Response - Page 2:
2025-05-17 03:00:26,036 - INFO - 第 2 页获取到 100 条记录
2025-05-17 03:00:26,036 - INFO - Request Parameters - Page 3:
2025-05-17 03:00:26,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:26,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:26,506 - INFO - Response - Page 3:
2025-05-17 03:00:26,706 - INFO - 第 3 页获取到 100 条记录
2025-05-17 03:00:26,706 - INFO - Request Parameters - Page 4:
2025-05-17 03:00:26,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:26,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:27,216 - INFO - Response - Page 4:
2025-05-17 03:00:27,418 - INFO - 第 4 页获取到 100 条记录
2025-05-17 03:00:27,418 - INFO - Request Parameters - Page 5:
2025-05-17 03:00:27,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:27,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:27,966 - INFO - Response - Page 5:
2025-05-17 03:00:28,166 - INFO - 第 5 页获取到 100 条记录
2025-05-17 03:00:28,166 - INFO - Request Parameters - Page 6:
2025-05-17 03:00:28,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:28,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:28,668 - INFO - Response - Page 6:
2025-05-17 03:00:28,868 - INFO - 第 6 页获取到 100 条记录
2025-05-17 03:00:28,868 - INFO - Request Parameters - Page 7:
2025-05-17 03:00:28,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:00:28,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:00:29,177 - INFO - Response - Page 7:
2025-05-17 03:00:29,377 - INFO - 第 7 页获取到 25 条记录
2025-05-17 03:00:29,377 - INFO - 查询完成，共获取到 625 条记录
2025-05-17 03:00:29,377 - INFO - 获取到 625 条表单数据
2025-05-17 03:00:29,389 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-17 03:00:29,400 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 03:00:29,400 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 03:00:29,402 - INFO - =================同步完成====================
2025-05-17 06:00:02,459 - INFO - =================使用默认全量同步=============
2025-05-17 06:00:03,809 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-17 06:00:03,810 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-17 06:00:03,838 - INFO - 开始处理日期: 2025-01
2025-05-17 06:00:03,840 - INFO - Request Parameters - Page 1:
2025-05-17 06:00:03,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:03,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:05,260 - INFO - Response - Page 1:
2025-05-17 06:00:05,461 - INFO - 第 1 页获取到 100 条记录
2025-05-17 06:00:05,461 - INFO - Request Parameters - Page 2:
2025-05-17 06:00:05,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:05,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:06,076 - INFO - Response - Page 2:
2025-05-17 06:00:06,277 - INFO - 第 2 页获取到 100 条记录
2025-05-17 06:00:06,277 - INFO - Request Parameters - Page 3:
2025-05-17 06:00:06,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:06,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:06,831 - INFO - Response - Page 3:
2025-05-17 06:00:07,032 - INFO - 第 3 页获取到 100 条记录
2025-05-17 06:00:07,032 - INFO - Request Parameters - Page 4:
2025-05-17 06:00:07,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:07,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:07,579 - INFO - Response - Page 4:
2025-05-17 06:00:07,780 - INFO - 第 4 页获取到 100 条记录
2025-05-17 06:00:07,780 - INFO - Request Parameters - Page 5:
2025-05-17 06:00:07,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:07,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:08,349 - INFO - Response - Page 5:
2025-05-17 06:00:08,550 - INFO - 第 5 页获取到 100 条记录
2025-05-17 06:00:08,550 - INFO - Request Parameters - Page 6:
2025-05-17 06:00:08,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:08,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:09,017 - INFO - Response - Page 6:
2025-05-17 06:00:09,218 - INFO - 第 6 页获取到 100 条记录
2025-05-17 06:00:09,218 - INFO - Request Parameters - Page 7:
2025-05-17 06:00:09,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:09,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:09,706 - INFO - Response - Page 7:
2025-05-17 06:00:09,906 - INFO - 第 7 页获取到 82 条记录
2025-05-17 06:00:09,906 - INFO - 查询完成，共获取到 682 条记录
2025-05-17 06:00:09,906 - INFO - 获取到 682 条表单数据
2025-05-17 06:00:09,918 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-17 06:00:09,930 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 06:00:09,930 - INFO - 开始处理日期: 2025-02
2025-05-17 06:00:09,930 - INFO - Request Parameters - Page 1:
2025-05-17 06:00:09,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:09,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:10,582 - INFO - Response - Page 1:
2025-05-17 06:00:10,782 - INFO - 第 1 页获取到 100 条记录
2025-05-17 06:00:10,782 - INFO - Request Parameters - Page 2:
2025-05-17 06:00:10,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:10,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:11,285 - INFO - Response - Page 2:
2025-05-17 06:00:11,485 - INFO - 第 2 页获取到 100 条记录
2025-05-17 06:00:11,485 - INFO - Request Parameters - Page 3:
2025-05-17 06:00:11,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:11,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:12,102 - INFO - Response - Page 3:
2025-05-17 06:00:12,302 - INFO - 第 3 页获取到 100 条记录
2025-05-17 06:00:12,302 - INFO - Request Parameters - Page 4:
2025-05-17 06:00:12,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:12,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:12,776 - INFO - Response - Page 4:
2025-05-17 06:00:12,977 - INFO - 第 4 页获取到 100 条记录
2025-05-17 06:00:12,977 - INFO - Request Parameters - Page 5:
2025-05-17 06:00:12,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:12,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:13,494 - INFO - Response - Page 5:
2025-05-17 06:00:13,694 - INFO - 第 5 页获取到 100 条记录
2025-05-17 06:00:13,694 - INFO - Request Parameters - Page 6:
2025-05-17 06:00:13,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:13,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:14,189 - INFO - Response - Page 6:
2025-05-17 06:00:14,389 - INFO - 第 6 页获取到 100 条记录
2025-05-17 06:00:14,389 - INFO - Request Parameters - Page 7:
2025-05-17 06:00:14,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:14,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:14,978 - INFO - Response - Page 7:
2025-05-17 06:00:15,178 - INFO - 第 7 页获取到 70 条记录
2025-05-17 06:00:15,178 - INFO - 查询完成，共获取到 670 条记录
2025-05-17 06:00:15,178 - INFO - 获取到 670 条表单数据
2025-05-17 06:00:15,190 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-17 06:00:15,201 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 06:00:15,201 - INFO - 开始处理日期: 2025-03
2025-05-17 06:00:15,201 - INFO - Request Parameters - Page 1:
2025-05-17 06:00:15,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:15,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:15,751 - INFO - Response - Page 1:
2025-05-17 06:00:15,953 - INFO - 第 1 页获取到 100 条记录
2025-05-17 06:00:15,953 - INFO - Request Parameters - Page 2:
2025-05-17 06:00:15,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:15,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:16,434 - INFO - Response - Page 2:
2025-05-17 06:00:16,634 - INFO - 第 2 页获取到 100 条记录
2025-05-17 06:00:16,634 - INFO - Request Parameters - Page 3:
2025-05-17 06:00:16,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:16,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:17,169 - INFO - Response - Page 3:
2025-05-17 06:00:17,369 - INFO - 第 3 页获取到 100 条记录
2025-05-17 06:00:17,369 - INFO - Request Parameters - Page 4:
2025-05-17 06:00:17,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:17,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:17,872 - INFO - Response - Page 4:
2025-05-17 06:00:18,073 - INFO - 第 4 页获取到 100 条记录
2025-05-17 06:00:18,073 - INFO - Request Parameters - Page 5:
2025-05-17 06:00:18,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:18,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:18,577 - INFO - Response - Page 5:
2025-05-17 06:00:18,777 - INFO - 第 5 页获取到 100 条记录
2025-05-17 06:00:18,777 - INFO - Request Parameters - Page 6:
2025-05-17 06:00:18,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:18,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:19,324 - INFO - Response - Page 6:
2025-05-17 06:00:19,525 - INFO - 第 6 页获取到 100 条记录
2025-05-17 06:00:19,525 - INFO - Request Parameters - Page 7:
2025-05-17 06:00:19,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:19,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:20,007 - INFO - Response - Page 7:
2025-05-17 06:00:20,208 - INFO - 第 7 页获取到 61 条记录
2025-05-17 06:00:20,208 - INFO - 查询完成，共获取到 661 条记录
2025-05-17 06:00:20,208 - INFO - 获取到 661 条表单数据
2025-05-17 06:00:20,220 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-17 06:00:20,231 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 06:00:20,231 - INFO - 开始处理日期: 2025-04
2025-05-17 06:00:20,231 - INFO - Request Parameters - Page 1:
2025-05-17 06:00:20,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:20,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:20,714 - INFO - Response - Page 1:
2025-05-17 06:00:20,914 - INFO - 第 1 页获取到 100 条记录
2025-05-17 06:00:20,914 - INFO - Request Parameters - Page 2:
2025-05-17 06:00:20,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:20,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:21,382 - INFO - Response - Page 2:
2025-05-17 06:00:21,582 - INFO - 第 2 页获取到 100 条记录
2025-05-17 06:00:21,582 - INFO - Request Parameters - Page 3:
2025-05-17 06:00:21,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:21,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:22,044 - INFO - Response - Page 3:
2025-05-17 06:00:22,244 - INFO - 第 3 页获取到 100 条记录
2025-05-17 06:00:22,244 - INFO - Request Parameters - Page 4:
2025-05-17 06:00:22,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:22,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:22,841 - INFO - Response - Page 4:
2025-05-17 06:00:23,042 - INFO - 第 4 页获取到 100 条记录
2025-05-17 06:00:23,042 - INFO - Request Parameters - Page 5:
2025-05-17 06:00:23,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:23,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:23,523 - INFO - Response - Page 5:
2025-05-17 06:00:23,723 - INFO - 第 5 页获取到 100 条记录
2025-05-17 06:00:23,723 - INFO - Request Parameters - Page 6:
2025-05-17 06:00:23,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:23,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:24,188 - INFO - Response - Page 6:
2025-05-17 06:00:24,389 - INFO - 第 6 页获取到 100 条记录
2025-05-17 06:00:24,389 - INFO - Request Parameters - Page 7:
2025-05-17 06:00:24,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:24,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:24,818 - INFO - Response - Page 7:
2025-05-17 06:00:25,018 - INFO - 第 7 页获取到 54 条记录
2025-05-17 06:00:25,018 - INFO - 查询完成，共获取到 654 条记录
2025-05-17 06:00:25,018 - INFO - 获取到 654 条表单数据
2025-05-17 06:00:25,031 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-17 06:00:25,043 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 06:00:25,043 - INFO - 开始处理日期: 2025-05
2025-05-17 06:00:25,043 - INFO - Request Parameters - Page 1:
2025-05-17 06:00:25,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:25,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:25,512 - INFO - Response - Page 1:
2025-05-17 06:00:25,712 - INFO - 第 1 页获取到 100 条记录
2025-05-17 06:00:25,712 - INFO - Request Parameters - Page 2:
2025-05-17 06:00:25,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:25,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:26,252 - INFO - Response - Page 2:
2025-05-17 06:00:26,452 - INFO - 第 2 页获取到 100 条记录
2025-05-17 06:00:26,452 - INFO - Request Parameters - Page 3:
2025-05-17 06:00:26,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:26,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:26,990 - INFO - Response - Page 3:
2025-05-17 06:00:27,190 - INFO - 第 3 页获取到 100 条记录
2025-05-17 06:00:27,190 - INFO - Request Parameters - Page 4:
2025-05-17 06:00:27,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:27,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:27,727 - INFO - Response - Page 4:
2025-05-17 06:00:27,928 - INFO - 第 4 页获取到 100 条记录
2025-05-17 06:00:27,928 - INFO - Request Parameters - Page 5:
2025-05-17 06:00:27,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:27,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:28,440 - INFO - Response - Page 5:
2025-05-17 06:00:28,641 - INFO - 第 5 页获取到 100 条记录
2025-05-17 06:00:28,641 - INFO - Request Parameters - Page 6:
2025-05-17 06:00:28,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:28,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:29,131 - INFO - Response - Page 6:
2025-05-17 06:00:29,331 - INFO - 第 6 页获取到 100 条记录
2025-05-17 06:00:29,331 - INFO - Request Parameters - Page 7:
2025-05-17 06:00:29,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:00:29,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:00:29,708 - INFO - Response - Page 7:
2025-05-17 06:00:29,909 - INFO - 第 7 页获取到 25 条记录
2025-05-17 06:00:29,909 - INFO - 查询完成，共获取到 625 条记录
2025-05-17 06:00:29,909 - INFO - 获取到 625 条表单数据
2025-05-17 06:00:29,920 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-17 06:00:29,931 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 06:00:29,932 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 06:00:29,933 - INFO - =================同步完成====================
2025-05-17 09:00:01,916 - INFO - =================使用默认全量同步=============
2025-05-17 09:00:03,308 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-17 09:00:03,309 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-17 09:00:03,339 - INFO - 开始处理日期: 2025-01
2025-05-17 09:00:03,343 - INFO - Request Parameters - Page 1:
2025-05-17 09:00:03,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:03,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:04,586 - INFO - Response - Page 1:
2025-05-17 09:00:04,787 - INFO - 第 1 页获取到 100 条记录
2025-05-17 09:00:04,787 - INFO - Request Parameters - Page 2:
2025-05-17 09:00:04,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:04,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:05,286 - INFO - Response - Page 2:
2025-05-17 09:00:05,486 - INFO - 第 2 页获取到 100 条记录
2025-05-17 09:00:05,486 - INFO - Request Parameters - Page 3:
2025-05-17 09:00:05,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:05,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:06,013 - INFO - Response - Page 3:
2025-05-17 09:00:06,214 - INFO - 第 3 页获取到 100 条记录
2025-05-17 09:00:06,214 - INFO - Request Parameters - Page 4:
2025-05-17 09:00:06,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:06,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:06,717 - INFO - Response - Page 4:
2025-05-17 09:00:06,917 - INFO - 第 4 页获取到 100 条记录
2025-05-17 09:00:06,917 - INFO - Request Parameters - Page 5:
2025-05-17 09:00:06,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:06,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:07,373 - INFO - Response - Page 5:
2025-05-17 09:00:07,574 - INFO - 第 5 页获取到 100 条记录
2025-05-17 09:00:07,574 - INFO - Request Parameters - Page 6:
2025-05-17 09:00:07,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:07,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:08,151 - INFO - Response - Page 6:
2025-05-17 09:00:08,351 - INFO - 第 6 页获取到 100 条记录
2025-05-17 09:00:08,351 - INFO - Request Parameters - Page 7:
2025-05-17 09:00:08,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:08,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:08,846 - INFO - Response - Page 7:
2025-05-17 09:00:09,046 - INFO - 第 7 页获取到 82 条记录
2025-05-17 09:00:09,046 - INFO - 查询完成，共获取到 682 条记录
2025-05-17 09:00:09,046 - INFO - 获取到 682 条表单数据
2025-05-17 09:00:09,058 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-17 09:00:09,070 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 09:00:09,070 - INFO - 开始处理日期: 2025-02
2025-05-17 09:00:09,071 - INFO - Request Parameters - Page 1:
2025-05-17 09:00:09,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:09,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:09,572 - INFO - Response - Page 1:
2025-05-17 09:00:09,772 - INFO - 第 1 页获取到 100 条记录
2025-05-17 09:00:09,772 - INFO - Request Parameters - Page 2:
2025-05-17 09:00:09,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:09,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:10,266 - INFO - Response - Page 2:
2025-05-17 09:00:10,466 - INFO - 第 2 页获取到 100 条记录
2025-05-17 09:00:10,466 - INFO - Request Parameters - Page 3:
2025-05-17 09:00:10,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:10,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:10,959 - INFO - Response - Page 3:
2025-05-17 09:00:11,160 - INFO - 第 3 页获取到 100 条记录
2025-05-17 09:00:11,160 - INFO - Request Parameters - Page 4:
2025-05-17 09:00:11,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:11,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:11,673 - INFO - Response - Page 4:
2025-05-17 09:00:11,873 - INFO - 第 4 页获取到 100 条记录
2025-05-17 09:00:11,873 - INFO - Request Parameters - Page 5:
2025-05-17 09:00:11,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:11,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:12,414 - INFO - Response - Page 5:
2025-05-17 09:00:12,615 - INFO - 第 5 页获取到 100 条记录
2025-05-17 09:00:12,615 - INFO - Request Parameters - Page 6:
2025-05-17 09:00:12,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:12,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:13,116 - INFO - Response - Page 6:
2025-05-17 09:00:13,316 - INFO - 第 6 页获取到 100 条记录
2025-05-17 09:00:13,316 - INFO - Request Parameters - Page 7:
2025-05-17 09:00:13,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:13,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:13,751 - INFO - Response - Page 7:
2025-05-17 09:00:13,951 - INFO - 第 7 页获取到 70 条记录
2025-05-17 09:00:13,951 - INFO - 查询完成，共获取到 670 条记录
2025-05-17 09:00:13,952 - INFO - 获取到 670 条表单数据
2025-05-17 09:00:13,965 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-17 09:00:13,977 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 09:00:13,977 - INFO - 开始处理日期: 2025-03
2025-05-17 09:00:13,978 - INFO - Request Parameters - Page 1:
2025-05-17 09:00:13,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:13,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:14,509 - INFO - Response - Page 1:
2025-05-17 09:00:14,709 - INFO - 第 1 页获取到 100 条记录
2025-05-17 09:00:14,709 - INFO - Request Parameters - Page 2:
2025-05-17 09:00:14,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:14,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:15,233 - INFO - Response - Page 2:
2025-05-17 09:00:15,434 - INFO - 第 2 页获取到 100 条记录
2025-05-17 09:00:15,434 - INFO - Request Parameters - Page 3:
2025-05-17 09:00:15,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:15,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:15,966 - INFO - Response - Page 3:
2025-05-17 09:00:16,166 - INFO - 第 3 页获取到 100 条记录
2025-05-17 09:00:16,166 - INFO - Request Parameters - Page 4:
2025-05-17 09:00:16,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:16,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:16,667 - INFO - Response - Page 4:
2025-05-17 09:00:16,867 - INFO - 第 4 页获取到 100 条记录
2025-05-17 09:00:16,867 - INFO - Request Parameters - Page 5:
2025-05-17 09:00:16,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:16,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:17,361 - INFO - Response - Page 5:
2025-05-17 09:00:17,562 - INFO - 第 5 页获取到 100 条记录
2025-05-17 09:00:17,562 - INFO - Request Parameters - Page 6:
2025-05-17 09:00:17,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:17,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:18,008 - INFO - Response - Page 6:
2025-05-17 09:00:18,208 - INFO - 第 6 页获取到 100 条记录
2025-05-17 09:00:18,208 - INFO - Request Parameters - Page 7:
2025-05-17 09:00:18,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:18,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:18,660 - INFO - Response - Page 7:
2025-05-17 09:00:18,861 - INFO - 第 7 页获取到 61 条记录
2025-05-17 09:00:18,861 - INFO - 查询完成，共获取到 661 条记录
2025-05-17 09:00:18,861 - INFO - 获取到 661 条表单数据
2025-05-17 09:00:18,872 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-17 09:00:18,884 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 09:00:18,884 - INFO - 开始处理日期: 2025-04
2025-05-17 09:00:18,884 - INFO - Request Parameters - Page 1:
2025-05-17 09:00:18,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:18,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:19,379 - INFO - Response - Page 1:
2025-05-17 09:00:19,579 - INFO - 第 1 页获取到 100 条记录
2025-05-17 09:00:19,579 - INFO - Request Parameters - Page 2:
2025-05-17 09:00:19,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:19,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:20,131 - INFO - Response - Page 2:
2025-05-17 09:00:20,331 - INFO - 第 2 页获取到 100 条记录
2025-05-17 09:00:20,331 - INFO - Request Parameters - Page 3:
2025-05-17 09:00:20,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:20,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:20,817 - INFO - Response - Page 3:
2025-05-17 09:00:21,017 - INFO - 第 3 页获取到 100 条记录
2025-05-17 09:00:21,017 - INFO - Request Parameters - Page 4:
2025-05-17 09:00:21,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:21,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:21,519 - INFO - Response - Page 4:
2025-05-17 09:00:21,719 - INFO - 第 4 页获取到 100 条记录
2025-05-17 09:00:21,719 - INFO - Request Parameters - Page 5:
2025-05-17 09:00:21,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:21,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:22,225 - INFO - Response - Page 5:
2025-05-17 09:00:22,425 - INFO - 第 5 页获取到 100 条记录
2025-05-17 09:00:22,425 - INFO - Request Parameters - Page 6:
2025-05-17 09:00:22,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:22,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:22,934 - INFO - Response - Page 6:
2025-05-17 09:00:23,135 - INFO - 第 6 页获取到 100 条记录
2025-05-17 09:00:23,135 - INFO - Request Parameters - Page 7:
2025-05-17 09:00:23,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:23,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:23,587 - INFO - Response - Page 7:
2025-05-17 09:00:23,787 - INFO - 第 7 页获取到 54 条记录
2025-05-17 09:00:23,787 - INFO - 查询完成，共获取到 654 条记录
2025-05-17 09:00:23,787 - INFO - 获取到 654 条表单数据
2025-05-17 09:00:23,799 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-17 09:00:23,811 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 09:00:23,811 - INFO - 开始处理日期: 2025-05
2025-05-17 09:00:23,811 - INFO - Request Parameters - Page 1:
2025-05-17 09:00:23,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:23,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:24,307 - INFO - Response - Page 1:
2025-05-17 09:00:24,508 - INFO - 第 1 页获取到 100 条记录
2025-05-17 09:00:24,508 - INFO - Request Parameters - Page 2:
2025-05-17 09:00:24,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:24,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:24,976 - INFO - Response - Page 2:
2025-05-17 09:00:25,176 - INFO - 第 2 页获取到 100 条记录
2025-05-17 09:00:25,176 - INFO - Request Parameters - Page 3:
2025-05-17 09:00:25,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:25,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:25,673 - INFO - Response - Page 3:
2025-05-17 09:00:25,873 - INFO - 第 3 页获取到 100 条记录
2025-05-17 09:00:25,873 - INFO - Request Parameters - Page 4:
2025-05-17 09:00:25,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:25,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:26,310 - INFO - Response - Page 4:
2025-05-17 09:00:26,511 - INFO - 第 4 页获取到 100 条记录
2025-05-17 09:00:26,511 - INFO - Request Parameters - Page 5:
2025-05-17 09:00:26,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:26,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:27,023 - INFO - Response - Page 5:
2025-05-17 09:00:27,223 - INFO - 第 5 页获取到 100 条记录
2025-05-17 09:00:27,223 - INFO - Request Parameters - Page 6:
2025-05-17 09:00:27,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:27,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:27,738 - INFO - Response - Page 6:
2025-05-17 09:00:27,938 - INFO - 第 6 页获取到 100 条记录
2025-05-17 09:00:27,938 - INFO - Request Parameters - Page 7:
2025-05-17 09:00:27,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:00:27,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:00:28,257 - INFO - Response - Page 7:
2025-05-17 09:00:28,457 - INFO - 第 7 页获取到 25 条记录
2025-05-17 09:00:28,457 - INFO - 查询完成，共获取到 625 条记录
2025-05-17 09:00:28,457 - INFO - 获取到 625 条表单数据
2025-05-17 09:00:28,470 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-17 09:00:28,471 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-17 09:00:28,927 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-17 09:00:28,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26150.0, 'new_value': 26950.0}, {'field': 'total_amount', 'old_value': 27585.0, 'new_value': 28385.0}, {'field': 'order_count', 'old_value': 353, 'new_value': 366}]
2025-05-17 09:00:28,928 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-17 09:00:29,376 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-17 09:00:29,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223083.0, 'new_value': 242953.0}, {'field': 'total_amount', 'old_value': 223083.0, 'new_value': 242953.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 177}]
2025-05-17 09:00:29,376 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-17 09:00:29,985 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-17 09:00:29,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187050.0, 'new_value': 205030.0}, {'field': 'total_amount', 'old_value': 187050.0, 'new_value': 205030.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 115}]
2025-05-17 09:00:29,985 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-17 09:00:30,478 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-17 09:00:30,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60151.0, 'new_value': 63747.0}, {'field': 'total_amount', 'old_value': 60151.0, 'new_value': 63747.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 72}]
2025-05-17 09:00:30,478 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-17 09:00:30,929 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-17 09:00:30,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27663.0, 'new_value': 30142.0}, {'field': 'total_amount', 'old_value': 27663.0, 'new_value': 30142.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-17 09:00:30,929 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-17 09:00:31,433 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-17 09:00:31,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2004200.0, 'new_value': 2199100.0}, {'field': 'total_amount', 'old_value': 2004200.0, 'new_value': 2199100.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-17 09:00:31,433 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-17 09:00:31,824 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-17 09:00:31,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109769.0, 'new_value': 114769.0}, {'field': 'total_amount', 'old_value': 109769.0, 'new_value': 114769.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-17 09:00:31,824 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-17 09:00:32,282 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-17 09:00:32,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34863.89, 'new_value': 36917.42}, {'field': 'offline_amount', 'old_value': 69734.0, 'new_value': 73888.45}, {'field': 'total_amount', 'old_value': 104597.89, 'new_value': 110805.87}, {'field': 'order_count', 'old_value': 1267, 'new_value': 1326}]
2025-05-17 09:00:32,282 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-17 09:00:32,669 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-17 09:00:32,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14019.66, 'new_value': 14714.29}, {'field': 'offline_amount', 'old_value': 15954.51, 'new_value': 16765.96}, {'field': 'total_amount', 'old_value': 29974.17, 'new_value': 31480.25}, {'field': 'order_count', 'old_value': 1445, 'new_value': 1527}]
2025-05-17 09:00:32,669 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-17 09:00:33,084 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-17 09:00:33,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35860.0, 'new_value': 41440.0}, {'field': 'total_amount', 'old_value': 35860.0, 'new_value': 41440.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-17 09:00:33,085 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-17 09:00:33,553 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-17 09:00:33,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199117.9, 'new_value': 209135.7}, {'field': 'total_amount', 'old_value': 314137.6, 'new_value': 324155.4}, {'field': 'order_count', 'old_value': 2167, 'new_value': 2255}]
2025-05-17 09:00:33,553 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-17 09:00:34,085 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-17 09:00:34,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62380.8, 'new_value': 66100.2}, {'field': 'total_amount', 'old_value': 62380.8, 'new_value': 66100.2}, {'field': 'order_count', 'old_value': 3388, 'new_value': 3610}]
2025-05-17 09:00:34,086 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-17 09:00:34,649 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-17 09:00:34,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84704.41, 'new_value': 91227.46}, {'field': 'total_amount', 'old_value': 84704.41, 'new_value': 91227.46}, {'field': 'order_count', 'old_value': 955, 'new_value': 1040}]
2025-05-17 09:00:34,649 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-17 09:00:35,081 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-17 09:00:35,081 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11409.0, 'new_value': 13414.0}, {'field': 'total_amount', 'old_value': 15858.0, 'new_value': 17863.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-05-17 09:00:35,081 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-17 09:00:35,541 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-17 09:00:35,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122254.0, 'new_value': 130432.0}, {'field': 'total_amount', 'old_value': 122254.0, 'new_value': 130432.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 237}]
2025-05-17 09:00:35,542 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-17 09:00:36,082 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-17 09:00:36,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44510.0, 'new_value': 48566.0}, {'field': 'total_amount', 'old_value': 44510.0, 'new_value': 48566.0}, {'field': 'order_count', 'old_value': 384, 'new_value': 416}]
2025-05-17 09:00:36,083 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-17 09:00:36,530 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-17 09:00:36,530 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112558.38, 'new_value': 118139.74}, {'field': 'offline_amount', 'old_value': 19347.16, 'new_value': 19827.73}, {'field': 'total_amount', 'old_value': 131905.54, 'new_value': 137967.47}, {'field': 'order_count', 'old_value': 472, 'new_value': 496}]
2025-05-17 09:00:36,530 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-17 09:00:36,952 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-17 09:00:36,952 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112282.0, 'new_value': 116798.0}, {'field': 'offline_amount', 'old_value': 39055.56, 'new_value': 42296.52}, {'field': 'total_amount', 'old_value': 151337.56, 'new_value': 159094.52}, {'field': 'order_count', 'old_value': 918, 'new_value': 972}]
2025-05-17 09:00:36,952 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-17 09:00:37,512 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-17 09:00:37,512 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4556.0, 'new_value': 4714.0}, {'field': 'total_amount', 'old_value': 5674.0, 'new_value': 5832.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 115}]
2025-05-17 09:00:37,512 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-17 09:00:38,013 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-17 09:00:38,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55113.43, 'new_value': 58474.21}, {'field': 'total_amount', 'old_value': 55113.43, 'new_value': 58474.21}, {'field': 'order_count', 'old_value': 1468, 'new_value': 1550}]
2025-05-17 09:00:38,013 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-17 09:00:38,457 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-17 09:00:38,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108142.0, 'new_value': 110714.1}, {'field': 'total_amount', 'old_value': 108142.0, 'new_value': 110714.1}, {'field': 'order_count', 'old_value': 40, 'new_value': 46}]
2025-05-17 09:00:38,458 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-17 09:00:38,861 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-17 09:00:38,861 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90001.0, 'new_value': 98220.0}, {'field': 'offline_amount', 'old_value': 41575.22, 'new_value': 43825.12}, {'field': 'total_amount', 'old_value': 131576.22, 'new_value': 142045.12}, {'field': 'order_count', 'old_value': 935, 'new_value': 1009}]
2025-05-17 09:00:38,861 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-17 09:00:39,280 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-17 09:00:39,280 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6893.12, 'new_value': 7101.49}, {'field': 'offline_amount', 'old_value': 109241.47, 'new_value': 117015.72}, {'field': 'total_amount', 'old_value': 116134.59, 'new_value': 124117.21}, {'field': 'order_count', 'old_value': 1266, 'new_value': 1346}]
2025-05-17 09:00:39,280 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-17 09:00:39,743 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-17 09:00:39,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18084.0, 'new_value': 19383.0}, {'field': 'total_amount', 'old_value': 19284.0, 'new_value': 20583.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 76}]
2025-05-17 09:00:39,744 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-17 09:00:40,176 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-17 09:00:40,176 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5222.38, 'new_value': 5406.06}, {'field': 'offline_amount', 'old_value': 60386.81, 'new_value': 63777.27}, {'field': 'total_amount', 'old_value': 65609.19, 'new_value': 69183.33}, {'field': 'order_count', 'old_value': 1559, 'new_value': 1647}]
2025-05-17 09:00:40,176 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-17 09:00:40,613 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-17 09:00:40,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117164.43, 'new_value': 126654.26}, {'field': 'total_amount', 'old_value': 117164.43, 'new_value': 126654.26}, {'field': 'order_count', 'old_value': 374, 'new_value': 406}]
2025-05-17 09:00:40,613 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-17 09:00:41,048 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-17 09:00:41,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86512.0, 'new_value': 91958.0}, {'field': 'total_amount', 'old_value': 86512.0, 'new_value': 91958.0}, {'field': 'order_count', 'old_value': 2124, 'new_value': 2267}]
2025-05-17 09:00:41,049 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-17 09:00:41,533 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-17 09:00:41,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14585.78, 'new_value': 15124.78}, {'field': 'total_amount', 'old_value': 14585.78, 'new_value': 15124.78}, {'field': 'order_count', 'old_value': 70, 'new_value': 72}]
2025-05-17 09:00:41,533 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-17 09:00:41,952 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-17 09:00:41,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98772.0, 'new_value': 107239.0}, {'field': 'total_amount', 'old_value': 98772.0, 'new_value': 107239.0}, {'field': 'order_count', 'old_value': 3668, 'new_value': 3992}]
2025-05-17 09:00:41,953 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-17 09:00:42,401 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-17 09:00:42,401 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15558.0, 'new_value': 15777.0}, {'field': 'offline_amount', 'old_value': 301552.0, 'new_value': 304293.0}, {'field': 'total_amount', 'old_value': 317110.0, 'new_value': 320070.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-05-17 09:00:42,401 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-17 09:00:42,825 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-17 09:00:42,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81997.41, 'new_value': 87654.48}, {'field': 'total_amount', 'old_value': 81997.41, 'new_value': 87654.48}, {'field': 'order_count', 'old_value': 2983, 'new_value': 3213}]
2025-05-17 09:00:42,826 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-17 09:00:43,368 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-17 09:00:43,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48371.0, 'new_value': 48569.0}, {'field': 'total_amount', 'old_value': 48371.0, 'new_value': 48569.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 86}]
2025-05-17 09:00:43,368 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-17 09:00:43,810 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-17 09:00:43,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261521.3, 'new_value': 303850.3}, {'field': 'total_amount', 'old_value': 306009.13, 'new_value': 348338.13}, {'field': 'order_count', 'old_value': 491, 'new_value': 526}]
2025-05-17 09:00:43,810 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-17 09:00:44,260 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-17 09:00:44,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4118.32, 'new_value': 4308.23}, {'field': 'offline_amount', 'old_value': 118185.9, 'new_value': 128688.55}, {'field': 'total_amount', 'old_value': 122304.22, 'new_value': 132996.78}, {'field': 'order_count', 'old_value': 830, 'new_value': 872}]
2025-05-17 09:00:44,260 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-17 09:00:44,751 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-17 09:00:44,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108880.0, 'new_value': 117093.0}, {'field': 'total_amount', 'old_value': 108880.0, 'new_value': 117093.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-17 09:00:44,751 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-17 09:00:45,226 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-17 09:00:45,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1100971.1, 'new_value': 1154222.9}, {'field': 'total_amount', 'old_value': 1154416.2, 'new_value': 1207668.0}, {'field': 'order_count', 'old_value': 1994, 'new_value': 2092}]
2025-05-17 09:00:45,227 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-17 09:00:45,684 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-17 09:00:45,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31772.0, 'new_value': 34800.0}, {'field': 'total_amount', 'old_value': 60418.0, 'new_value': 63446.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-05-17 09:00:45,684 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-17 09:00:46,139 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-17 09:00:46,139 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13869.0, 'new_value': 13927.0}, {'field': 'total_amount', 'old_value': 13869.0, 'new_value': 13927.0}, {'field': 'order_count', 'old_value': 239, 'new_value': 240}]
2025-05-17 09:00:46,140 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-17 09:00:46,600 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-17 09:00:46,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3976.05, 'new_value': 4180.85}, {'field': 'offline_amount', 'old_value': 9714.52, 'new_value': 10421.52}, {'field': 'total_amount', 'old_value': 13690.57, 'new_value': 14602.37}, {'field': 'order_count', 'old_value': 478, 'new_value': 510}]
2025-05-17 09:00:46,600 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-17 09:00:46,993 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-17 09:00:46,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104036.78, 'new_value': 113334.95}, {'field': 'offline_amount', 'old_value': 85695.58, 'new_value': 92090.36}, {'field': 'total_amount', 'old_value': 189732.36, 'new_value': 205425.31}, {'field': 'order_count', 'old_value': 1648, 'new_value': 1786}]
2025-05-17 09:00:46,993 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-17 09:00:47,448 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-17 09:00:47,448 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 217093.9, 'new_value': 230084.4}, {'field': 'offline_amount', 'old_value': 50634.0, 'new_value': 53004.0}, {'field': 'total_amount', 'old_value': 267727.9, 'new_value': 283088.4}, {'field': 'order_count', 'old_value': 330, 'new_value': 351}]
2025-05-17 09:00:47,448 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-17 09:00:47,913 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-17 09:00:47,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19285.0, 'new_value': 20305.0}, {'field': 'total_amount', 'old_value': 19285.0, 'new_value': 20305.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 57}]
2025-05-17 09:00:47,913 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-17 09:00:48,422 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-17 09:00:48,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52766.0, 'new_value': 54642.0}, {'field': 'total_amount', 'old_value': 52766.0, 'new_value': 54642.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-17 09:00:48,422 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-17 09:00:48,931 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-17 09:00:48,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50251.5, 'new_value': 52148.0}, {'field': 'total_amount', 'old_value': 52889.05, 'new_value': 54785.55}, {'field': 'order_count', 'old_value': 145, 'new_value': 151}]
2025-05-17 09:00:48,931 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-17 09:00:49,357 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-17 09:00:49,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45863.47, 'new_value': 47687.57}, {'field': 'total_amount', 'old_value': 45863.47, 'new_value': 47687.57}, {'field': 'order_count', 'old_value': 1252, 'new_value': 1306}]
2025-05-17 09:00:49,357 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-17 09:00:49,842 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-17 09:00:49,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244842.36, 'new_value': 267022.83}, {'field': 'total_amount', 'old_value': 244842.36, 'new_value': 267022.83}, {'field': 'order_count', 'old_value': 374, 'new_value': 398}]
2025-05-17 09:00:49,843 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-17 09:00:50,258 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-17 09:00:50,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9401.03, 'new_value': 9668.97}, {'field': 'offline_amount', 'old_value': 264843.85, 'new_value': 274217.36}, {'field': 'total_amount', 'old_value': 274244.88, 'new_value': 283886.33}, {'field': 'order_count', 'old_value': 1092, 'new_value': 1143}]
2025-05-17 09:00:50,258 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-17 09:00:50,740 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-17 09:00:50,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51717.0, 'new_value': 59352.0}, {'field': 'offline_amount', 'old_value': 52123.46, 'new_value': 53429.46}, {'field': 'total_amount', 'old_value': 103840.46, 'new_value': 112781.46}, {'field': 'order_count', 'old_value': 123, 'new_value': 135}]
2025-05-17 09:00:50,741 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-17 09:00:51,191 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-17 09:00:51,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6819.0, 'new_value': 7993.0}, {'field': 'total_amount', 'old_value': 6819.0, 'new_value': 7993.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-17 09:00:51,191 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-17 09:00:51,653 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-17 09:00:51,653 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50027.79, 'new_value': 54588.1}, {'field': 'offline_amount', 'old_value': 684173.6, 'new_value': 732880.34}, {'field': 'total_amount', 'old_value': 734201.39, 'new_value': 787468.44}, {'field': 'order_count', 'old_value': 5889, 'new_value': 6309}]
2025-05-17 09:00:51,653 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-17 09:00:52,124 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-17 09:00:52,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53122.18, 'new_value': 57880.52}, {'field': 'total_amount', 'old_value': 53122.18, 'new_value': 57880.52}, {'field': 'order_count', 'old_value': 298, 'new_value': 322}]
2025-05-17 09:00:52,124 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-17 09:00:52,530 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-17 09:00:52,530 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5533.39, 'new_value': 5723.35}, {'field': 'offline_amount', 'old_value': 21905.0, 'new_value': 22481.0}, {'field': 'total_amount', 'old_value': 27438.39, 'new_value': 28204.35}, {'field': 'order_count', 'old_value': 133, 'new_value': 139}]
2025-05-17 09:00:52,530 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-17 09:00:53,016 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-17 09:00:53,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30204.75, 'new_value': 31391.25}, {'field': 'total_amount', 'old_value': 30401.55, 'new_value': 31588.05}, {'field': 'order_count', 'old_value': 259, 'new_value': 269}]
2025-05-17 09:00:53,017 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-17 09:00:53,407 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-17 09:00:53,407 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3249.0, 'new_value': 3410.0}, {'field': 'offline_amount', 'old_value': 14858.5, 'new_value': 15648.9}, {'field': 'total_amount', 'old_value': 18107.5, 'new_value': 19058.9}, {'field': 'order_count', 'old_value': 739, 'new_value': 771}]
2025-05-17 09:00:53,407 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-17 09:00:53,830 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-17 09:00:53,831 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63697.08, 'new_value': 67116.82}, {'field': 'total_amount', 'old_value': 63697.08, 'new_value': 67116.82}, {'field': 'order_count', 'old_value': 206, 'new_value': 216}]
2025-05-17 09:00:53,831 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-17 09:00:54,359 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-17 09:00:54,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18035.4, 'new_value': 18984.6}, {'field': 'offline_amount', 'old_value': 14266.7, 'new_value': 14963.6}, {'field': 'total_amount', 'old_value': 32302.1, 'new_value': 33948.2}, {'field': 'order_count', 'old_value': 171, 'new_value': 179}]
2025-05-17 09:00:54,360 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-17 09:00:54,831 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-17 09:00:54,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48220.0, 'new_value': 52484.0}, {'field': 'total_amount', 'old_value': 48220.0, 'new_value': 52484.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-17 09:00:54,832 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-17 09:00:55,315 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-17 09:00:55,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124725.5, 'new_value': 134655.4}, {'field': 'total_amount', 'old_value': 124725.5, 'new_value': 134655.4}, {'field': 'order_count', 'old_value': 452, 'new_value': 487}]
2025-05-17 09:00:55,316 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-17 09:00:55,695 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-17 09:00:55,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9113.62, 'new_value': 9714.22}, {'field': 'offline_amount', 'old_value': 155657.94, 'new_value': 168993.84}, {'field': 'total_amount', 'old_value': 164771.56, 'new_value': 178708.06}, {'field': 'order_count', 'old_value': 9052, 'new_value': 9794}]
2025-05-17 09:00:55,696 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-17 09:00:56,141 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-17 09:00:56,141 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31065.47, 'new_value': 32376.82}, {'field': 'offline_amount', 'old_value': 19311.0, 'new_value': 20323.0}, {'field': 'total_amount', 'old_value': 50376.47, 'new_value': 52699.82}, {'field': 'order_count', 'old_value': 637, 'new_value': 663}]
2025-05-17 09:00:56,141 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-17 09:00:56,585 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-17 09:00:56,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92817.45, 'new_value': 98005.56}, {'field': 'total_amount', 'old_value': 92817.45, 'new_value': 98005.56}, {'field': 'order_count', 'old_value': 452, 'new_value': 478}]
2025-05-17 09:00:56,585 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-17 09:00:57,044 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-17 09:00:57,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14134.17, 'new_value': 14987.57}, {'field': 'offline_amount', 'old_value': 25982.02, 'new_value': 27418.02}, {'field': 'total_amount', 'old_value': 40116.19, 'new_value': 42405.59}, {'field': 'order_count', 'old_value': 1442, 'new_value': 1523}]
2025-05-17 09:00:57,045 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-17 09:00:57,551 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-17 09:00:57,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40883.0, 'new_value': 44353.0}, {'field': 'total_amount', 'old_value': 43291.0, 'new_value': 46761.0}, {'field': 'order_count', 'old_value': 181, 'new_value': 195}]
2025-05-17 09:00:57,551 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-17 09:00:57,995 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-17 09:00:57,996 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38697.0, 'new_value': 40912.0}, {'field': 'total_amount', 'old_value': 38697.0, 'new_value': 40912.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-17 09:00:57,996 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-17 09:00:58,469 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-17 09:00:58,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14275.7, 'new_value': 15293.7}, {'field': 'offline_amount', 'old_value': 35628.84, 'new_value': 37153.34}, {'field': 'total_amount', 'old_value': 49904.54, 'new_value': 52447.04}, {'field': 'order_count', 'old_value': 571, 'new_value': 602}]
2025-05-17 09:00:58,469 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-17 09:00:58,997 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-17 09:00:58,997 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57369.6, 'new_value': 60572.9}, {'field': 'offline_amount', 'old_value': 77197.37, 'new_value': 83184.75}, {'field': 'total_amount', 'old_value': 134566.97, 'new_value': 143757.65}, {'field': 'order_count', 'old_value': 933, 'new_value': 956}]
2025-05-17 09:00:58,997 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-17 09:00:59,391 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-17 09:00:59,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11783.43, 'new_value': 12724.53}, {'field': 'offline_amount', 'old_value': 24251.62, 'new_value': 25891.68}, {'field': 'total_amount', 'old_value': 36035.05, 'new_value': 38616.21}, {'field': 'order_count', 'old_value': 1927, 'new_value': 2076}]
2025-05-17 09:00:59,391 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-17 09:00:59,812 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-17 09:00:59,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35150.0, 'new_value': 39099.0}, {'field': 'total_amount', 'old_value': 35499.0, 'new_value': 39448.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 67}]
2025-05-17 09:00:59,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-17 09:01:00,286 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-17 09:01:00,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73153.89, 'new_value': 77557.61}, {'field': 'total_amount', 'old_value': 73153.89, 'new_value': 77557.61}, {'field': 'order_count', 'old_value': 2130, 'new_value': 2247}]
2025-05-17 09:01:00,287 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-17 09:01:00,788 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-17 09:01:00,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130164.0, 'new_value': 139956.0}, {'field': 'total_amount', 'old_value': 130164.0, 'new_value': 139956.0}, {'field': 'order_count', 'old_value': 10847, 'new_value': 11663}]
2025-05-17 09:01:00,789 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-17 09:01:01,238 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-17 09:01:01,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5158.0, 'new_value': 8436.0}, {'field': 'total_amount', 'old_value': 5158.0, 'new_value': 8436.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-17 09:01:01,238 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-17 09:01:01,701 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-17 09:01:01,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30410.4, 'new_value': 31577.4}, {'field': 'total_amount', 'old_value': 30410.4, 'new_value': 31577.4}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-17 09:01:01,702 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-17 09:01:02,127 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-17 09:01:02,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27422.8, 'new_value': 29403.5}, {'field': 'total_amount', 'old_value': 27422.8, 'new_value': 29403.5}, {'field': 'order_count', 'old_value': 1212, 'new_value': 1304}]
2025-05-17 09:01:02,128 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-17 09:01:02,502 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-17 09:01:02,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76799.15, 'new_value': 84799.95}, {'field': 'total_amount', 'old_value': 149524.85, 'new_value': 157525.65}, {'field': 'order_count', 'old_value': 3915, 'new_value': 4153}]
2025-05-17 09:01:02,503 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-17 09:01:02,950 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-17 09:01:02,950 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32786.75, 'new_value': 34936.23}, {'field': 'offline_amount', 'old_value': 20276.77, 'new_value': 21934.52}, {'field': 'total_amount', 'old_value': 53063.52, 'new_value': 56870.75}, {'field': 'order_count', 'old_value': 2899, 'new_value': 3100}]
2025-05-17 09:01:02,950 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-17 09:01:03,370 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-17 09:01:03,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10702.18, 'new_value': 11067.18}, {'field': 'total_amount', 'old_value': 10702.18, 'new_value': 11067.18}, {'field': 'order_count', 'old_value': 100, 'new_value': 103}]
2025-05-17 09:01:03,370 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-17 09:01:03,841 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-17 09:01:03,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246562.49, 'new_value': 260873.76}, {'field': 'total_amount', 'old_value': 246562.49, 'new_value': 260873.76}, {'field': 'order_count', 'old_value': 888, 'new_value': 938}]
2025-05-17 09:01:03,841 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-17 09:01:04,335 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-17 09:01:04,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193545.0, 'new_value': 206463.2}, {'field': 'total_amount', 'old_value': 193545.0, 'new_value': 206463.2}, {'field': 'order_count', 'old_value': 4761, 'new_value': 5104}]
2025-05-17 09:01:04,336 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-17 09:01:04,707 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-17 09:01:04,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23691.93, 'new_value': 25047.67}, {'field': 'total_amount', 'old_value': 23691.93, 'new_value': 25047.67}, {'field': 'order_count', 'old_value': 3024, 'new_value': 3204}]
2025-05-17 09:01:04,707 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-17 09:01:05,158 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-17 09:01:05,158 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15471.26, 'new_value': 16650.45}, {'field': 'offline_amount', 'old_value': 19371.2, 'new_value': 20909.77}, {'field': 'total_amount', 'old_value': 34842.46, 'new_value': 37560.22}, {'field': 'order_count', 'old_value': 1539, 'new_value': 1673}]
2025-05-17 09:01:05,158 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-17 09:01:05,597 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-17 09:01:05,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46876.0, 'new_value': 51694.0}, {'field': 'total_amount', 'old_value': 52077.0, 'new_value': 56895.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 163}]
2025-05-17 09:01:05,597 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-17 09:01:06,051 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-17 09:01:06,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200804.6, 'new_value': 207766.6}, {'field': 'total_amount', 'old_value': 200804.6, 'new_value': 207766.6}, {'field': 'order_count', 'old_value': 41, 'new_value': 46}]
2025-05-17 09:01:06,051 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-17 09:01:06,462 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-17 09:01:06,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 284733.96, 'new_value': 298234.88}, {'field': 'total_amount', 'old_value': 284733.96, 'new_value': 298234.88}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1499}]
2025-05-17 09:01:06,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-17 09:01:06,914 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-17 09:01:06,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123987.0, 'new_value': 125016.0}, {'field': 'total_amount', 'old_value': 123987.0, 'new_value': 125016.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 72}]
2025-05-17 09:01:06,915 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-17 09:01:07,341 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-17 09:01:07,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 813226.0, 'new_value': 854412.0}, {'field': 'total_amount', 'old_value': 813226.0, 'new_value': 854412.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 100}]
2025-05-17 09:01:07,341 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-17 09:01:07,855 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-17 09:01:07,855 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32240.94, 'new_value': 32274.44}, {'field': 'offline_amount', 'old_value': 28937.69, 'new_value': 31996.39}, {'field': 'total_amount', 'old_value': 61178.63, 'new_value': 64270.83}, {'field': 'order_count', 'old_value': 202, 'new_value': 217}]
2025-05-17 09:01:07,856 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-17 09:01:08,301 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-17 09:01:08,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202269.52, 'new_value': 211875.32}, {'field': 'offline_amount', 'old_value': 5947.0, 'new_value': 6044.5}, {'field': 'total_amount', 'old_value': 208216.52, 'new_value': 217919.82}, {'field': 'order_count', 'old_value': 1656, 'new_value': 1767}]
2025-05-17 09:01:08,301 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-17 09:01:08,756 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-17 09:01:08,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13466.0, 'new_value': 14119.0}, {'field': 'total_amount', 'old_value': 13466.0, 'new_value': 14119.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 71}]
2025-05-17 09:01:08,756 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-17 09:01:09,262 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-17 09:01:09,262 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11695.3, 'new_value': 12629.5}, {'field': 'total_amount', 'old_value': 11695.3, 'new_value': 12629.5}, {'field': 'order_count', 'old_value': 413, 'new_value': 449}]
2025-05-17 09:01:09,262 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-17 09:01:09,743 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-17 09:01:09,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5263.5, 'new_value': 5346.1}, {'field': 'offline_amount', 'old_value': 22146.3, 'new_value': 23273.8}, {'field': 'total_amount', 'old_value': 27409.8, 'new_value': 28619.9}, {'field': 'order_count', 'old_value': 304, 'new_value': 315}]
2025-05-17 09:01:09,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-17 09:01:10,142 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-17 09:01:10,142 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8370.0, 'new_value': 9245.0}, {'field': 'offline_amount', 'old_value': 14003.0, 'new_value': 14258.0}, {'field': 'total_amount', 'old_value': 22373.0, 'new_value': 23503.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 63}]
2025-05-17 09:01:10,142 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-17 09:01:10,580 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-17 09:01:10,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18805.83, 'new_value': 19478.73}, {'field': 'total_amount', 'old_value': 18805.83, 'new_value': 19478.73}, {'field': 'order_count', 'old_value': 703, 'new_value': 736}]
2025-05-17 09:01:10,581 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-17 09:01:11,004 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-17 09:01:11,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 551776.0, 'new_value': 572017.0}, {'field': 'total_amount', 'old_value': 551776.0, 'new_value': 572017.0}, {'field': 'order_count', 'old_value': 2489, 'new_value': 2587}]
2025-05-17 09:01:11,004 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-17 09:01:11,500 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-17 09:01:11,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12688.0, 'new_value': 13687.0}, {'field': 'total_amount', 'old_value': 12688.0, 'new_value': 13687.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-17 09:01:11,501 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-17 09:01:12,036 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-17 09:01:12,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57142.0, 'new_value': 58512.0}, {'field': 'total_amount', 'old_value': 57142.0, 'new_value': 58512.0}, {'field': 'order_count', 'old_value': 1695, 'new_value': 1734}]
2025-05-17 09:01:12,037 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-17 09:01:12,478 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-17 09:01:12,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56209.61, 'new_value': 60614.62}, {'field': 'offline_amount', 'old_value': 65663.31, 'new_value': 71697.04}, {'field': 'total_amount', 'old_value': 121872.92, 'new_value': 132311.66}, {'field': 'order_count', 'old_value': 4929, 'new_value': 5338}]
2025-05-17 09:01:12,478 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-17 09:01:13,026 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-17 09:01:13,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37056.0, 'new_value': 38181.0}, {'field': 'total_amount', 'old_value': 37056.0, 'new_value': 38181.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 79}]
2025-05-17 09:01:13,027 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-17 09:01:13,477 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-17 09:01:13,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23336.0, 'new_value': 23545.0}, {'field': 'total_amount', 'old_value': 23336.0, 'new_value': 23545.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-05-17 09:01:13,478 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-17 09:01:14,073 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-17 09:01:14,073 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35290.23, 'new_value': 37259.45}, {'field': 'offline_amount', 'old_value': 29863.33, 'new_value': 30962.33}, {'field': 'total_amount', 'old_value': 65153.56, 'new_value': 68221.78}, {'field': 'order_count', 'old_value': 1301, 'new_value': 1367}]
2025-05-17 09:01:14,074 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-17 09:01:14,556 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-17 09:01:14,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117881.3, 'new_value': 124909.2}, {'field': 'total_amount', 'old_value': 117881.3, 'new_value': 124909.2}, {'field': 'order_count', 'old_value': 1509, 'new_value': 1599}]
2025-05-17 09:01:14,556 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-17 09:01:14,969 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-17 09:01:14,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25484.04, 'new_value': 26688.74}, {'field': 'offline_amount', 'old_value': 693864.1, 'new_value': 734481.37}, {'field': 'total_amount', 'old_value': 719348.14, 'new_value': 761170.11}, {'field': 'order_count', 'old_value': 3386, 'new_value': 3623}]
2025-05-17 09:01:14,970 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-17 09:01:15,504 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-17 09:01:15,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317699.0, 'new_value': 340234.0}, {'field': 'total_amount', 'old_value': 317699.0, 'new_value': 340234.0}, {'field': 'order_count', 'old_value': 295, 'new_value': 317}]
2025-05-17 09:01:15,504 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-17 09:01:15,956 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-17 09:01:15,956 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161538.75, 'new_value': 168431.15}, {'field': 'offline_amount', 'old_value': 100704.84, 'new_value': 105254.44}, {'field': 'total_amount', 'old_value': 262243.59, 'new_value': 273685.59}, {'field': 'order_count', 'old_value': 2373, 'new_value': 2503}]
2025-05-17 09:01:15,957 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-17 09:01:16,358 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-17 09:01:16,358 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53918.0, 'new_value': 57659.0}, {'field': 'offline_amount', 'old_value': 650379.0, 'new_value': 693838.0}, {'field': 'total_amount', 'old_value': 704297.0, 'new_value': 751497.0}, {'field': 'order_count', 'old_value': 16834, 'new_value': 18177}]
2025-05-17 09:01:16,358 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-17 09:01:16,801 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-17 09:01:16,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21131.0, 'new_value': 24303.0}, {'field': 'offline_amount', 'old_value': 121604.0, 'new_value': 129210.0}, {'field': 'total_amount', 'old_value': 142735.0, 'new_value': 153513.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 158}]
2025-05-17 09:01:16,802 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-17 09:01:17,242 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-17 09:01:17,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107756.69, 'new_value': 115724.19}, {'field': 'total_amount', 'old_value': 107756.69, 'new_value': 115724.19}, {'field': 'order_count', 'old_value': 5487, 'new_value': 5918}]
2025-05-17 09:01:17,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-17 09:01:17,705 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-17 09:01:17,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99586.5, 'new_value': 104490.4}, {'field': 'total_amount', 'old_value': 99586.5, 'new_value': 104490.4}, {'field': 'order_count', 'old_value': 459, 'new_value': 482}]
2025-05-17 09:01:17,705 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-17 09:01:18,231 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-17 09:01:18,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96048.5, 'new_value': 98108.9}, {'field': 'total_amount', 'old_value': 96048.5, 'new_value': 98108.9}, {'field': 'order_count', 'old_value': 2647, 'new_value': 2702}]
2025-05-17 09:01:18,231 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-17 09:01:18,575 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-17 09:01:18,575 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5199.0, 'new_value': 6506.0}, {'field': 'total_amount', 'old_value': 12886.0, 'new_value': 14193.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 71}]
2025-05-17 09:01:18,576 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-17 09:01:19,035 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-17 09:01:19,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66187.7, 'new_value': 69384.3}, {'field': 'total_amount', 'old_value': 66187.7, 'new_value': 69384.3}, {'field': 'order_count', 'old_value': 316, 'new_value': 333}]
2025-05-17 09:01:19,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-17 09:01:19,456 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-17 09:01:19,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54433.35, 'new_value': 60772.97}, {'field': 'total_amount', 'old_value': 55693.78, 'new_value': 62033.4}, {'field': 'order_count', 'old_value': 258, 'new_value': 286}]
2025-05-17 09:01:19,457 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-17 09:01:19,872 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-17 09:01:19,872 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4449.0, 'new_value': 4539.0}, {'field': 'offline_amount', 'old_value': 2772.0, 'new_value': 2842.0}, {'field': 'total_amount', 'old_value': 7221.0, 'new_value': 7381.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 70}]
2025-05-17 09:01:19,872 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-17 09:01:20,351 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-17 09:01:20,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34357.4, 'new_value': 36273.67}, {'field': 'offline_amount', 'old_value': 30023.93, 'new_value': 32346.57}, {'field': 'total_amount', 'old_value': 64381.33, 'new_value': 68620.24}, {'field': 'order_count', 'old_value': 3345, 'new_value': 3556}]
2025-05-17 09:01:20,351 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-17 09:01:20,814 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-17 09:01:20,814 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61483.02, 'new_value': 66220.95}, {'field': 'offline_amount', 'old_value': 68571.22, 'new_value': 72782.59}, {'field': 'total_amount', 'old_value': 130054.24, 'new_value': 139003.54}, {'field': 'order_count', 'old_value': 3266, 'new_value': 3487}]
2025-05-17 09:01:20,814 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-17 09:01:21,323 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-17 09:01:21,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 599420.0, 'new_value': 648404.0}, {'field': 'total_amount', 'old_value': 599420.0, 'new_value': 648404.0}, {'field': 'order_count', 'old_value': 697, 'new_value': 749}]
2025-05-17 09:01:21,323 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-17 09:01:21,800 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-17 09:01:21,800 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113816.6, 'new_value': 130244.6}, {'field': 'total_amount', 'old_value': 119766.9, 'new_value': 136194.9}, {'field': 'order_count', 'old_value': 236, 'new_value': 262}]
2025-05-17 09:01:21,800 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-17 09:01:22,214 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-17 09:01:22,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25694.95, 'new_value': 28260.15}, {'field': 'offline_amount', 'old_value': 68739.0, 'new_value': 71052.0}, {'field': 'total_amount', 'old_value': 94433.95, 'new_value': 99312.15}, {'field': 'order_count', 'old_value': 1010, 'new_value': 1103}]
2025-05-17 09:01:22,214 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-17 09:01:22,767 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-17 09:01:22,768 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81025.0, 'new_value': 86446.0}, {'field': 'offline_amount', 'old_value': 59396.0, 'new_value': 62198.0}, {'field': 'total_amount', 'old_value': 140421.0, 'new_value': 148644.0}, {'field': 'order_count', 'old_value': 1752, 'new_value': 1870}]
2025-05-17 09:01:22,768 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-17 09:01:23,223 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-17 09:01:23,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5966.2, 'new_value': 6124.0}, {'field': 'offline_amount', 'old_value': 12669.35, 'new_value': 13600.15}, {'field': 'total_amount', 'old_value': 18635.55, 'new_value': 19724.15}, {'field': 'order_count', 'old_value': 194, 'new_value': 206}]
2025-05-17 09:01:23,223 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-17 09:01:23,694 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-17 09:01:23,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6202.5, 'new_value': 6500.42}, {'field': 'offline_amount', 'old_value': 104927.0, 'new_value': 105275.0}, {'field': 'total_amount', 'old_value': 111129.5, 'new_value': 111775.42}, {'field': 'order_count', 'old_value': 46, 'new_value': 50}]
2025-05-17 09:01:23,695 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-17 09:01:24,114 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-17 09:01:24,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17333.52, 'new_value': 20198.42}, {'field': 'total_amount', 'old_value': 40152.12, 'new_value': 43017.02}, {'field': 'order_count', 'old_value': 164, 'new_value': 180}]
2025-05-17 09:01:24,114 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-17 09:01:24,563 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-17 09:01:24,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1286.0, 'new_value': 1344.0}, {'field': 'offline_amount', 'old_value': 36994.0, 'new_value': 38408.0}, {'field': 'total_amount', 'old_value': 38280.0, 'new_value': 39752.0}, {'field': 'order_count', 'old_value': 303, 'new_value': 316}]
2025-05-17 09:01:24,563 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-17 09:01:25,000 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-17 09:01:25,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134758.5, 'new_value': 143706.5}, {'field': 'total_amount', 'old_value': 134758.5, 'new_value': 143706.5}, {'field': 'order_count', 'old_value': 669, 'new_value': 713}]
2025-05-17 09:01:25,001 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-17 09:01:25,435 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-17 09:01:25,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28424.59, 'new_value': 32885.1}, {'field': 'total_amount', 'old_value': 32111.59, 'new_value': 36572.1}, {'field': 'order_count', 'old_value': 307, 'new_value': 325}]
2025-05-17 09:01:25,435 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-17 09:01:25,886 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-17 09:01:25,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102810.74, 'new_value': 114215.05}, {'field': 'total_amount', 'old_value': 102810.74, 'new_value': 114215.05}, {'field': 'order_count', 'old_value': 359, 'new_value': 403}]
2025-05-17 09:01:25,887 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-17 09:01:26,357 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-17 09:01:26,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12857.0, 'new_value': 13156.0}, {'field': 'total_amount', 'old_value': 12857.0, 'new_value': 13156.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-17 09:01:26,357 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-17 09:01:26,806 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-17 09:01:26,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143997.0, 'new_value': 157155.0}, {'field': 'total_amount', 'old_value': 143997.0, 'new_value': 157155.0}, {'field': 'order_count', 'old_value': 299, 'new_value': 329}]
2025-05-17 09:01:26,806 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-17 09:01:27,257 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-17 09:01:27,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76990.7, 'new_value': 91008.7}, {'field': 'total_amount', 'old_value': 197040.48, 'new_value': 211058.48}]
2025-05-17 09:01:27,257 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-17 09:01:27,592 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-17 09:01:27,593 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108660.0, 'new_value': 127581.0}, {'field': 'offline_amount', 'old_value': 89321.0, 'new_value': 101869.0}, {'field': 'total_amount', 'old_value': 197981.0, 'new_value': 229450.0}, {'field': 'order_count', 'old_value': 592, 'new_value': 636}]
2025-05-17 09:01:27,593 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-17 09:01:28,009 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-17 09:01:28,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83043.0, 'new_value': 90048.0}, {'field': 'total_amount', 'old_value': 83043.0, 'new_value': 90048.0}, {'field': 'order_count', 'old_value': 6113, 'new_value': 6670}]
2025-05-17 09:01:28,010 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-17 09:01:28,477 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-17 09:01:28,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25951.0, 'new_value': 29524.0}, {'field': 'total_amount', 'old_value': 25951.0, 'new_value': 29524.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-05-17 09:01:28,477 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-17 09:01:28,993 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-17 09:01:28,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13793.4, 'new_value': 14535.2}, {'field': 'offline_amount', 'old_value': 34730.4, 'new_value': 37531.4}, {'field': 'total_amount', 'old_value': 48523.8, 'new_value': 52066.6}, {'field': 'order_count', 'old_value': 1799, 'new_value': 1942}]
2025-05-17 09:01:28,993 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-17 09:01:29,504 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-17 09:01:29,504 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2383.05, 'new_value': 5359.97}, {'field': 'total_amount', 'old_value': 43451.99, 'new_value': 46428.91}, {'field': 'order_count', 'old_value': 713, 'new_value': 738}]
2025-05-17 09:01:29,504 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-17 09:01:29,978 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-17 09:01:29,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16098.29, 'new_value': 16672.62}, {'field': 'total_amount', 'old_value': 16098.29, 'new_value': 16672.62}, {'field': 'order_count', 'old_value': 67, 'new_value': 70}]
2025-05-17 09:01:29,979 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-17 09:01:30,391 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-17 09:01:30,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192659.0, 'new_value': 207417.0}, {'field': 'total_amount', 'old_value': 192659.0, 'new_value': 207417.0}, {'field': 'order_count', 'old_value': 4180, 'new_value': 4506}]
2025-05-17 09:01:30,392 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-17 09:01:30,851 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-17 09:01:30,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6.4}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6.4}, {'field': 'order_count', 'old_value': 8, 'new_value': 11}]
2025-05-17 09:01:30,851 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-17 09:01:31,369 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-17 09:01:31,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194136.07, 'new_value': 204040.08}, {'field': 'total_amount', 'old_value': 194136.07, 'new_value': 204040.08}, {'field': 'order_count', 'old_value': 538, 'new_value': 568}]
2025-05-17 09:01:31,369 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-17 09:01:31,789 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-17 09:01:31,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88492.0, 'new_value': 97177.0}, {'field': 'total_amount', 'old_value': 88492.0, 'new_value': 97177.0}, {'field': 'order_count', 'old_value': 348, 'new_value': 379}]
2025-05-17 09:01:31,790 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-17 09:01:32,314 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-17 09:01:32,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30352.0, 'new_value': 32863.0}, {'field': 'total_amount', 'old_value': 30352.0, 'new_value': 32863.0}, {'field': 'order_count', 'old_value': 580, 'new_value': 635}]
2025-05-17 09:01:32,314 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-17 09:01:32,773 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-17 09:01:32,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18151.0, 'new_value': 21831.0}, {'field': 'offline_amount', 'old_value': 36371.0, 'new_value': 36414.4}, {'field': 'total_amount', 'old_value': 54522.0, 'new_value': 58245.4}, {'field': 'order_count', 'old_value': 76, 'new_value': 79}]
2025-05-17 09:01:32,774 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXG
2025-05-17 09:01:33,293 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXG
2025-05-17 09:01:33,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 749.0, 'new_value': 1149.0}, {'field': 'total_amount', 'old_value': 749.0, 'new_value': 1149.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-17 09:01:33,293 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-17 09:01:33,719 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-17 09:01:33,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3689.0}, {'field': 'offline_amount', 'old_value': 19522.92, 'new_value': 26602.64}, {'field': 'total_amount', 'old_value': 19522.92, 'new_value': 30291.64}, {'field': 'order_count', 'old_value': 122, 'new_value': 156}]
2025-05-17 09:01:33,719 - INFO - 日期 2025-05 处理完成 - 更新: 142 条，插入: 0 条，错误: 0 条
2025-05-17 09:01:33,719 - INFO - 数据同步完成！更新: 142 条，插入: 0 条，错误: 0 条
2025-05-17 09:01:33,721 - INFO - =================同步完成====================
2025-05-17 12:00:01,899 - INFO - =================使用默认全量同步=============
2025-05-17 12:00:03,259 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-17 12:00:03,260 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-17 12:00:03,286 - INFO - 开始处理日期: 2025-01
2025-05-17 12:00:03,289 - INFO - Request Parameters - Page 1:
2025-05-17 12:00:03,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:03,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:04,409 - INFO - Response - Page 1:
2025-05-17 12:00:04,609 - INFO - 第 1 页获取到 100 条记录
2025-05-17 12:00:04,609 - INFO - Request Parameters - Page 2:
2025-05-17 12:00:04,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:04,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:05,453 - INFO - Response - Page 2:
2025-05-17 12:00:05,654 - INFO - 第 2 页获取到 100 条记录
2025-05-17 12:00:05,654 - INFO - Request Parameters - Page 3:
2025-05-17 12:00:05,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:05,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:06,185 - INFO - Response - Page 3:
2025-05-17 12:00:06,385 - INFO - 第 3 页获取到 100 条记录
2025-05-17 12:00:06,385 - INFO - Request Parameters - Page 4:
2025-05-17 12:00:06,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:06,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:06,904 - INFO - Response - Page 4:
2025-05-17 12:00:07,104 - INFO - 第 4 页获取到 100 条记录
2025-05-17 12:00:07,104 - INFO - Request Parameters - Page 5:
2025-05-17 12:00:07,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:07,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:07,592 - INFO - Response - Page 5:
2025-05-17 12:00:07,792 - INFO - 第 5 页获取到 100 条记录
2025-05-17 12:00:07,792 - INFO - Request Parameters - Page 6:
2025-05-17 12:00:07,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:07,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:08,257 - INFO - Response - Page 6:
2025-05-17 12:00:08,457 - INFO - 第 6 页获取到 100 条记录
2025-05-17 12:00:08,457 - INFO - Request Parameters - Page 7:
2025-05-17 12:00:08,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:08,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:09,003 - INFO - Response - Page 7:
2025-05-17 12:00:09,203 - INFO - 第 7 页获取到 82 条记录
2025-05-17 12:00:09,203 - INFO - 查询完成，共获取到 682 条记录
2025-05-17 12:00:09,203 - INFO - 获取到 682 条表单数据
2025-05-17 12:00:09,217 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-17 12:00:09,228 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 12:00:09,228 - INFO - 开始处理日期: 2025-02
2025-05-17 12:00:09,228 - INFO - Request Parameters - Page 1:
2025-05-17 12:00:09,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:09,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:09,753 - INFO - Response - Page 1:
2025-05-17 12:00:09,954 - INFO - 第 1 页获取到 100 条记录
2025-05-17 12:00:09,954 - INFO - Request Parameters - Page 2:
2025-05-17 12:00:09,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:09,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:10,487 - INFO - Response - Page 2:
2025-05-17 12:00:10,687 - INFO - 第 2 页获取到 100 条记录
2025-05-17 12:00:10,687 - INFO - Request Parameters - Page 3:
2025-05-17 12:00:10,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:10,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:11,217 - INFO - Response - Page 3:
2025-05-17 12:00:11,417 - INFO - 第 3 页获取到 100 条记录
2025-05-17 12:00:11,417 - INFO - Request Parameters - Page 4:
2025-05-17 12:00:11,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:11,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:11,955 - INFO - Response - Page 4:
2025-05-17 12:00:12,155 - INFO - 第 4 页获取到 100 条记录
2025-05-17 12:00:12,155 - INFO - Request Parameters - Page 5:
2025-05-17 12:00:12,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:12,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:12,618 - INFO - Response - Page 5:
2025-05-17 12:00:12,818 - INFO - 第 5 页获取到 100 条记录
2025-05-17 12:00:12,818 - INFO - Request Parameters - Page 6:
2025-05-17 12:00:12,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:12,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:13,280 - INFO - Response - Page 6:
2025-05-17 12:00:13,480 - INFO - 第 6 页获取到 100 条记录
2025-05-17 12:00:13,480 - INFO - Request Parameters - Page 7:
2025-05-17 12:00:13,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:13,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:13,936 - INFO - Response - Page 7:
2025-05-17 12:00:14,136 - INFO - 第 7 页获取到 70 条记录
2025-05-17 12:00:14,136 - INFO - 查询完成，共获取到 670 条记录
2025-05-17 12:00:14,136 - INFO - 获取到 670 条表单数据
2025-05-17 12:00:14,149 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-17 12:00:14,161 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 12:00:14,161 - INFO - 开始处理日期: 2025-03
2025-05-17 12:00:14,161 - INFO - Request Parameters - Page 1:
2025-05-17 12:00:14,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:14,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:14,661 - INFO - Response - Page 1:
2025-05-17 12:00:14,861 - INFO - 第 1 页获取到 100 条记录
2025-05-17 12:00:14,861 - INFO - Request Parameters - Page 2:
2025-05-17 12:00:14,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:14,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:15,317 - INFO - Response - Page 2:
2025-05-17 12:00:15,517 - INFO - 第 2 页获取到 100 条记录
2025-05-17 12:00:15,517 - INFO - Request Parameters - Page 3:
2025-05-17 12:00:15,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:15,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:16,052 - INFO - Response - Page 3:
2025-05-17 12:00:16,254 - INFO - 第 3 页获取到 100 条记录
2025-05-17 12:00:16,254 - INFO - Request Parameters - Page 4:
2025-05-17 12:00:16,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:16,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:16,787 - INFO - Response - Page 4:
2025-05-17 12:00:16,987 - INFO - 第 4 页获取到 100 条记录
2025-05-17 12:00:16,987 - INFO - Request Parameters - Page 5:
2025-05-17 12:00:16,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:16,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:17,423 - INFO - Response - Page 5:
2025-05-17 12:00:17,623 - INFO - 第 5 页获取到 100 条记录
2025-05-17 12:00:17,623 - INFO - Request Parameters - Page 6:
2025-05-17 12:00:17,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:17,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:18,148 - INFO - Response - Page 6:
2025-05-17 12:00:18,348 - INFO - 第 6 页获取到 100 条记录
2025-05-17 12:00:18,348 - INFO - Request Parameters - Page 7:
2025-05-17 12:00:18,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:18,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:18,812 - INFO - Response - Page 7:
2025-05-17 12:00:19,013 - INFO - 第 7 页获取到 61 条记录
2025-05-17 12:00:19,013 - INFO - 查询完成，共获取到 661 条记录
2025-05-17 12:00:19,013 - INFO - 获取到 661 条表单数据
2025-05-17 12:00:19,025 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-17 12:00:19,037 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 12:00:19,037 - INFO - 开始处理日期: 2025-04
2025-05-17 12:00:19,037 - INFO - Request Parameters - Page 1:
2025-05-17 12:00:19,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:19,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:19,599 - INFO - Response - Page 1:
2025-05-17 12:00:19,799 - INFO - 第 1 页获取到 100 条记录
2025-05-17 12:00:19,799 - INFO - Request Parameters - Page 2:
2025-05-17 12:00:19,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:19,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:20,341 - INFO - Response - Page 2:
2025-05-17 12:00:20,542 - INFO - 第 2 页获取到 100 条记录
2025-05-17 12:00:20,542 - INFO - Request Parameters - Page 3:
2025-05-17 12:00:20,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:20,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:21,083 - INFO - Response - Page 3:
2025-05-17 12:00:21,283 - INFO - 第 3 页获取到 100 条记录
2025-05-17 12:00:21,283 - INFO - Request Parameters - Page 4:
2025-05-17 12:00:21,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:21,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:21,808 - INFO - Response - Page 4:
2025-05-17 12:00:22,008 - INFO - 第 4 页获取到 100 条记录
2025-05-17 12:00:22,008 - INFO - Request Parameters - Page 5:
2025-05-17 12:00:22,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:22,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:22,489 - INFO - Response - Page 5:
2025-05-17 12:00:22,689 - INFO - 第 5 页获取到 100 条记录
2025-05-17 12:00:22,689 - INFO - Request Parameters - Page 6:
2025-05-17 12:00:22,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:22,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:23,242 - INFO - Response - Page 6:
2025-05-17 12:00:23,442 - INFO - 第 6 页获取到 100 条记录
2025-05-17 12:00:23,442 - INFO - Request Parameters - Page 7:
2025-05-17 12:00:23,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:23,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:23,856 - INFO - Response - Page 7:
2025-05-17 12:00:24,056 - INFO - 第 7 页获取到 54 条记录
2025-05-17 12:00:24,056 - INFO - 查询完成，共获取到 654 条记录
2025-05-17 12:00:24,056 - INFO - 获取到 654 条表单数据
2025-05-17 12:00:24,068 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-17 12:00:24,080 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 12:00:24,080 - INFO - 开始处理日期: 2025-05
2025-05-17 12:00:24,081 - INFO - Request Parameters - Page 1:
2025-05-17 12:00:24,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:24,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:24,556 - INFO - Response - Page 1:
2025-05-17 12:00:24,756 - INFO - 第 1 页获取到 100 条记录
2025-05-17 12:00:24,756 - INFO - Request Parameters - Page 2:
2025-05-17 12:00:24,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:24,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:25,250 - INFO - Response - Page 2:
2025-05-17 12:00:25,450 - INFO - 第 2 页获取到 100 条记录
2025-05-17 12:00:25,450 - INFO - Request Parameters - Page 3:
2025-05-17 12:00:25,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:25,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:25,928 - INFO - Response - Page 3:
2025-05-17 12:00:26,129 - INFO - 第 3 页获取到 100 条记录
2025-05-17 12:00:26,129 - INFO - Request Parameters - Page 4:
2025-05-17 12:00:26,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:26,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:26,619 - INFO - Response - Page 4:
2025-05-17 12:00:26,819 - INFO - 第 4 页获取到 100 条记录
2025-05-17 12:00:26,819 - INFO - Request Parameters - Page 5:
2025-05-17 12:00:26,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:26,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:27,383 - INFO - Response - Page 5:
2025-05-17 12:00:27,584 - INFO - 第 5 页获取到 100 条记录
2025-05-17 12:00:27,584 - INFO - Request Parameters - Page 6:
2025-05-17 12:00:27,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:27,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:28,151 - INFO - Response - Page 6:
2025-05-17 12:00:28,351 - INFO - 第 6 页获取到 100 条记录
2025-05-17 12:00:28,351 - INFO - Request Parameters - Page 7:
2025-05-17 12:00:28,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:00:28,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:00:28,663 - INFO - Response - Page 7:
2025-05-17 12:00:28,863 - INFO - 第 7 页获取到 25 条记录
2025-05-17 12:00:28,863 - INFO - 查询完成，共获取到 625 条记录
2025-05-17 12:00:28,863 - INFO - 获取到 625 条表单数据
2025-05-17 12:00:28,875 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-17 12:00:28,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-17 12:00:29,293 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-17 12:00:29,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159800.0, 'new_value': 171800.0}, {'field': 'total_amount', 'old_value': 159800.0, 'new_value': 171800.0}]
2025-05-17 12:00:29,294 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-17 12:00:29,752 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-17 12:00:29,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6200000.0, 'new_value': 6400000.0}, {'field': 'total_amount', 'old_value': 6300000.0, 'new_value': 6500000.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-17 12:00:29,752 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-17 12:00:30,251 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-17 12:00:30,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26235.0, 'new_value': 28620.0}, {'field': 'total_amount', 'old_value': 27825.0, 'new_value': 30210.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 114}]
2025-05-17 12:00:30,251 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-17 12:00:30,724 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-17 12:00:30,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 452097.98, 'new_value': 475998.98}, {'field': 'total_amount', 'old_value': 452097.98, 'new_value': 475998.98}, {'field': 'order_count', 'old_value': 1340, 'new_value': 1427}]
2025-05-17 12:00:30,724 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-17 12:00:31,228 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-17 12:00:31,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12081.0, 'new_value': 12347.0}, {'field': 'total_amount', 'old_value': 14978.0, 'new_value': 15244.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-17 12:00:31,228 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-17 12:00:31,706 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-17 12:00:31,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18159.71, 'new_value': 19392.33}, {'field': 'offline_amount', 'old_value': 9251.27, 'new_value': 10196.03}, {'field': 'total_amount', 'old_value': 27410.98, 'new_value': 29588.36}, {'field': 'order_count', 'old_value': 1406, 'new_value': 1507}]
2025-05-17 12:00:31,707 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-17 12:00:32,174 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-17 12:00:32,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37397.78, 'new_value': 41314.78}, {'field': 'total_amount', 'old_value': 37397.78, 'new_value': 41314.78}, {'field': 'order_count', 'old_value': 80, 'new_value': 83}]
2025-05-17 12:00:32,174 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-17 12:00:32,669 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-17 12:00:32,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 271371.0, 'new_value': 285951.0}, {'field': 'offline_amount', 'old_value': 172610.0, 'new_value': 183050.0}, {'field': 'total_amount', 'old_value': 443981.0, 'new_value': 469001.0}, {'field': 'order_count', 'old_value': 484, 'new_value': 517}]
2025-05-17 12:00:32,669 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-17 12:00:33,135 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-17 12:00:33,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28840.0, 'new_value': 30480.0}, {'field': 'total_amount', 'old_value': 32960.0, 'new_value': 34600.0}, {'field': 'order_count', 'old_value': 316, 'new_value': 334}]
2025-05-17 12:00:33,135 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-17 12:00:33,538 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-17 12:00:33,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9475.3, 'new_value': 14241.7}, {'field': 'total_amount', 'old_value': 9825.3, 'new_value': 14591.7}, {'field': 'order_count', 'old_value': 713, 'new_value': 1067}]
2025-05-17 12:00:33,539 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-17 12:00:33,979 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-17 12:00:33,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21805.0, 'new_value': 26505.0}, {'field': 'total_amount', 'old_value': 21805.0, 'new_value': 26505.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-17 12:00:33,979 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-17 12:00:34,443 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-17 12:00:34,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44810.0, 'new_value': 47178.0}, {'field': 'total_amount', 'old_value': 44810.0, 'new_value': 47178.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 82}]
2025-05-17 12:00:34,444 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-17 12:00:35,029 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-17 12:00:35,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250844.48, 'new_value': 269027.48}, {'field': 'total_amount', 'old_value': 250844.48, 'new_value': 269027.48}, {'field': 'order_count', 'old_value': 280, 'new_value': 305}]
2025-05-17 12:00:35,030 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-17 12:00:35,459 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-17 12:00:35,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24987.0, 'new_value': 32305.0}, {'field': 'offline_amount', 'old_value': 91235.0, 'new_value': 118165.0}, {'field': 'total_amount', 'old_value': 116222.0, 'new_value': 150470.0}, {'field': 'order_count', 'old_value': 787, 'new_value': 1008}]
2025-05-17 12:00:35,459 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-17 12:00:35,896 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-17 12:00:35,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60256.7, 'new_value': 60498.7}, {'field': 'total_amount', 'old_value': 65510.6, 'new_value': 65752.6}, {'field': 'order_count', 'old_value': 1328, 'new_value': 1498}]
2025-05-17 12:00:35,897 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-17 12:00:36,393 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-17 12:00:36,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488128.2, 'new_value': 507627.5}, {'field': 'total_amount', 'old_value': 489385.6, 'new_value': 508884.9}, {'field': 'order_count', 'old_value': 54, 'new_value': 56}]
2025-05-17 12:00:36,394 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-17 12:00:36,848 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-17 12:00:36,848 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11483.83, 'new_value': 16083.28}, {'field': 'offline_amount', 'old_value': 48409.22, 'new_value': 67026.14}, {'field': 'total_amount', 'old_value': 59893.05, 'new_value': 83109.42}, {'field': 'order_count', 'old_value': 805, 'new_value': 1147}]
2025-05-17 12:00:36,849 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-17 12:00:37,390 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-17 12:00:37,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127041.72, 'new_value': 130804.62}, {'field': 'total_amount', 'old_value': 127041.72, 'new_value': 130804.62}, {'field': 'order_count', 'old_value': 151, 'new_value': 159}]
2025-05-17 12:00:37,391 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-17 12:00:37,879 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-17 12:00:37,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11775.49, 'new_value': 16615.43}, {'field': 'offline_amount', 'old_value': 10764.95, 'new_value': 15190.48}, {'field': 'total_amount', 'old_value': 22540.44, 'new_value': 31805.91}, {'field': 'order_count', 'old_value': 1251, 'new_value': 1787}]
2025-05-17 12:00:37,879 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-17 12:00:38,338 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-17 12:00:38,338 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53352.0, 'new_value': 57186.0}, {'field': 'total_amount', 'old_value': 99180.0, 'new_value': 103014.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-17 12:00:38,338 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-17 12:00:38,746 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-17 12:00:38,746 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2549.7, 'new_value': 3489.79}, {'field': 'offline_amount', 'old_value': 60088.33, 'new_value': 78102.01}, {'field': 'total_amount', 'old_value': 62638.03, 'new_value': 81591.8}, {'field': 'order_count', 'old_value': 999, 'new_value': 1319}]
2025-05-17 12:00:38,746 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-17 12:00:39,194 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-17 12:00:39,194 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122904.0, 'new_value': 131751.0}, {'field': 'total_amount', 'old_value': 122904.0, 'new_value': 131751.0}, {'field': 'order_count', 'old_value': 594, 'new_value': 654}]
2025-05-17 12:00:39,194 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-17 12:00:39,702 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-17 12:00:39,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36694.24, 'new_value': 50440.04}, {'field': 'total_amount', 'old_value': 36694.24, 'new_value': 50440.04}, {'field': 'order_count', 'old_value': 1867, 'new_value': 2612}]
2025-05-17 12:00:39,702 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-17 12:00:40,154 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-17 12:00:40,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8285.65, 'new_value': 8521.65}, {'field': 'total_amount', 'old_value': 8285.65, 'new_value': 8521.65}, {'field': 'order_count', 'old_value': 138, 'new_value': 141}]
2025-05-17 12:00:40,155 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-17 12:00:40,536 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-17 12:00:40,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50863.85, 'new_value': 55069.22}, {'field': 'offline_amount', 'old_value': 542424.51, 'new_value': 580414.83}, {'field': 'total_amount', 'old_value': 593288.36, 'new_value': 635484.05}, {'field': 'order_count', 'old_value': 1927, 'new_value': 2064}]
2025-05-17 12:00:40,537 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-17 12:00:40,999 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-17 12:00:40,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 430084.98, 'new_value': 454135.69}, {'field': 'total_amount', 'old_value': 430084.98, 'new_value': 454135.69}]
2025-05-17 12:00:40,999 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-17 12:00:41,455 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-17 12:00:41,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55684.0, 'new_value': 58484.0}, {'field': 'total_amount', 'old_value': 55684.0, 'new_value': 58484.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-17 12:00:41,456 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-17 12:00:41,920 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-17 12:00:41,920 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29197.57, 'new_value': 30824.96}, {'field': 'offline_amount', 'old_value': 70703.32, 'new_value': 73510.25}, {'field': 'total_amount', 'old_value': 99900.89, 'new_value': 104335.21}, {'field': 'order_count', 'old_value': 3423, 'new_value': 3606}]
2025-05-17 12:00:41,920 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-17 12:00:42,349 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-17 12:00:42,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223181.0, 'new_value': 224829.0}, {'field': 'total_amount', 'old_value': 223181.0, 'new_value': 224829.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 116}]
2025-05-17 12:00:42,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-17 12:00:42,788 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-17 12:00:42,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103524.39, 'new_value': 112249.5}, {'field': 'total_amount', 'old_value': 103524.39, 'new_value': 112249.5}, {'field': 'order_count', 'old_value': 579, 'new_value': 639}]
2025-05-17 12:00:42,788 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-17 12:00:43,281 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-17 12:00:43,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112071.05, 'new_value': 120467.88}, {'field': 'offline_amount', 'old_value': 291955.81, 'new_value': 302628.26}, {'field': 'total_amount', 'old_value': 404026.86, 'new_value': 423096.14}, {'field': 'order_count', 'old_value': 2795, 'new_value': 2951}]
2025-05-17 12:00:43,281 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-17 12:00:43,734 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-17 12:00:43,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17645.15, 'new_value': 18298.3}, {'field': 'total_amount', 'old_value': 17710.7, 'new_value': 18363.85}, {'field': 'order_count', 'old_value': 175, 'new_value': 176}]
2025-05-17 12:00:43,735 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-17 12:00:44,210 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-17 12:00:44,211 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95092.65, 'new_value': 109095.65}, {'field': 'offline_amount', 'old_value': 59091.0, 'new_value': 66316.0}, {'field': 'total_amount', 'old_value': 154183.65, 'new_value': 175411.65}, {'field': 'order_count', 'old_value': 811, 'new_value': 886}]
2025-05-17 12:00:44,211 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-17 12:00:44,616 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-17 12:00:44,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26249.9, 'new_value': 26734.9}, {'field': 'total_amount', 'old_value': 26249.9, 'new_value': 26734.9}, {'field': 'order_count', 'old_value': 114, 'new_value': 116}]
2025-05-17 12:00:44,616 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-17 12:00:45,028 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-17 12:00:45,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22063.8, 'new_value': 28202.8}, {'field': 'total_amount', 'old_value': 22063.8, 'new_value': 28202.8}, {'field': 'order_count', 'old_value': 94, 'new_value': 122}]
2025-05-17 12:00:45,028 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-17 12:00:45,506 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-17 12:00:45,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83811.0, 'new_value': 86599.0}, {'field': 'total_amount', 'old_value': 83813.0, 'new_value': 86601.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-17 12:00:45,506 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-17 12:00:45,931 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-17 12:00:45,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42751.97, 'new_value': 54962.63}, {'field': 'total_amount', 'old_value': 42755.27, 'new_value': 54965.93}, {'field': 'order_count', 'old_value': 29, 'new_value': 32}]
2025-05-17 12:00:45,931 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-17 12:00:46,357 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-17 12:00:46,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57653.19, 'new_value': 57683.09}, {'field': 'total_amount', 'old_value': 61422.29, 'new_value': 61452.19}, {'field': 'order_count', 'old_value': 317, 'new_value': 323}]
2025-05-17 12:00:46,357 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-17 12:00:46,828 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-17 12:00:46,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112802.0, 'new_value': 122668.0}, {'field': 'total_amount', 'old_value': 142959.0, 'new_value': 152825.0}, {'field': 'order_count', 'old_value': 3050, 'new_value': 3255}]
2025-05-17 12:00:46,828 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-17 12:00:47,343 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-17 12:00:47,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9212.31, 'new_value': 9659.71}, {'field': 'offline_amount', 'old_value': 163874.6, 'new_value': 175585.9}, {'field': 'total_amount', 'old_value': 173086.91, 'new_value': 185245.61}, {'field': 'order_count', 'old_value': 1232, 'new_value': 1313}]
2025-05-17 12:00:47,343 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-17 12:00:47,812 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-17 12:00:47,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5141.0, 'new_value': 5340.0}, {'field': 'total_amount', 'old_value': 68378.64, 'new_value': 68577.64}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-17 12:00:47,812 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-17 12:00:48,231 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-17 12:00:48,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116776.0, 'new_value': 135599.0}, {'field': 'total_amount', 'old_value': 143030.0, 'new_value': 161853.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-05-17 12:00:48,231 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-17 12:00:48,646 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-17 12:00:48,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85274.11, 'new_value': 89291.74}, {'field': 'offline_amount', 'old_value': 78069.45, 'new_value': 80327.45}, {'field': 'total_amount', 'old_value': 163343.56, 'new_value': 169619.19}, {'field': 'order_count', 'old_value': 1628, 'new_value': 1698}]
2025-05-17 12:00:48,646 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-17 12:00:49,097 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-17 12:00:49,097 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 432463.04, 'new_value': 434907.04}, {'field': 'offline_amount', 'old_value': 144193.9, 'new_value': 144283.9}, {'field': 'total_amount', 'old_value': 576656.94, 'new_value': 579190.94}, {'field': 'order_count', 'old_value': 5451, 'new_value': 5482}]
2025-05-17 12:00:49,097 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-17 12:00:49,551 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-17 12:00:49,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25871.6, 'new_value': 26831.6}, {'field': 'offline_amount', 'old_value': 532.0, 'new_value': 535.0}, {'field': 'total_amount', 'old_value': 26403.6, 'new_value': 27366.6}, {'field': 'order_count', 'old_value': 113, 'new_value': 115}]
2025-05-17 12:00:49,552 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-17 12:00:50,004 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-17 12:00:50,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90203.0, 'new_value': 97510.0}, {'field': 'total_amount', 'old_value': 113825.48, 'new_value': 121132.48}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-17 12:00:50,004 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-17 12:00:50,490 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-17 12:00:50,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5244.9, 'new_value': 5502.8}, {'field': 'offline_amount', 'old_value': 30176.0, 'new_value': 32175.0}, {'field': 'total_amount', 'old_value': 35420.9, 'new_value': 37677.8}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-05-17 12:00:50,490 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-17 12:00:50,921 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-17 12:00:50,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76194.3, 'new_value': 80494.27}, {'field': 'offline_amount', 'old_value': 259907.41, 'new_value': 280694.21}, {'field': 'total_amount', 'old_value': 336101.71, 'new_value': 361188.48}, {'field': 'order_count', 'old_value': 2024, 'new_value': 2102}]
2025-05-17 12:00:50,922 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-17 12:00:51,360 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-17 12:00:51,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36478.0, 'new_value': 37533.0}, {'field': 'total_amount', 'old_value': 36826.0, 'new_value': 37881.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-05-17 12:00:51,361 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-17 12:00:51,812 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-17 12:00:51,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 528048.0, 'new_value': 567461.0}, {'field': 'total_amount', 'old_value': 528048.0, 'new_value': 567461.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 99}]
2025-05-17 12:00:51,812 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-17 12:00:52,288 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-17 12:00:52,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100294.1, 'new_value': 104825.5}, {'field': 'total_amount', 'old_value': 100294.1, 'new_value': 104825.5}, {'field': 'order_count', 'old_value': 226, 'new_value': 238}]
2025-05-17 12:00:52,289 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-17 12:00:52,747 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-17 12:00:52,747 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131063.35, 'new_value': 137813.14}, {'field': 'offline_amount', 'old_value': 81007.61, 'new_value': 82642.01}, {'field': 'total_amount', 'old_value': 212070.96, 'new_value': 220455.15}, {'field': 'order_count', 'old_value': 808, 'new_value': 833}]
2025-05-17 12:00:52,747 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-17 12:00:53,162 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-17 12:00:53,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133843.3, 'new_value': 135190.22}, {'field': 'offline_amount', 'old_value': 60862.6, 'new_value': 68670.82}, {'field': 'total_amount', 'old_value': 194705.9, 'new_value': 203861.04}, {'field': 'order_count', 'old_value': 2262, 'new_value': 2356}]
2025-05-17 12:00:53,162 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-17 12:00:53,599 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-17 12:00:53,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12824.61, 'new_value': 13375.35}, {'field': 'offline_amount', 'old_value': 171519.7, 'new_value': 180972.21}, {'field': 'total_amount', 'old_value': 184344.31, 'new_value': 194347.56}, {'field': 'order_count', 'old_value': 873, 'new_value': 922}]
2025-05-17 12:00:53,600 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-17 12:00:54,011 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-17 12:00:54,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28130.0, 'new_value': 28448.0}, {'field': 'total_amount', 'old_value': 28130.0, 'new_value': 28448.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-17 12:00:54,011 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-17 12:00:54,429 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-17 12:00:54,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21094.26, 'new_value': 22522.26}, {'field': 'offline_amount', 'old_value': 347698.08, 'new_value': 364394.45}, {'field': 'total_amount', 'old_value': 368792.34, 'new_value': 386916.71}, {'field': 'order_count', 'old_value': 2071, 'new_value': 2213}]
2025-05-17 12:00:54,429 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-17 12:00:54,906 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-17 12:00:54,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52363.82, 'new_value': 56005.86}, {'field': 'total_amount', 'old_value': 52363.82, 'new_value': 56005.86}, {'field': 'order_count', 'old_value': 50, 'new_value': 54}]
2025-05-17 12:00:54,906 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-17 12:00:55,329 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-17 12:00:55,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29345.0, 'new_value': 30037.0}, {'field': 'total_amount', 'old_value': 29345.0, 'new_value': 30037.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 61}]
2025-05-17 12:00:55,329 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-17 12:00:55,742 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-17 12:00:55,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3878.0, 'new_value': 4268.0}, {'field': 'total_amount', 'old_value': 3878.0, 'new_value': 4268.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-05-17 12:00:55,742 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-17 12:00:56,257 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-17 12:00:56,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99201.17, 'new_value': 106111.1}, {'field': 'offline_amount', 'old_value': 224513.67, 'new_value': 234449.55}, {'field': 'total_amount', 'old_value': 323714.84, 'new_value': 340560.65}, {'field': 'order_count', 'old_value': 8583, 'new_value': 9139}]
2025-05-17 12:00:56,258 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-17 12:00:56,735 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-17 12:00:56,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 758000.0, 'new_value': 818000.0}, {'field': 'total_amount', 'old_value': 758000.0, 'new_value': 818000.0}, {'field': 'order_count', 'old_value': 334, 'new_value': 335}]
2025-05-17 12:00:56,735 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-17 12:00:57,278 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-17 12:00:57,279 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31509.0, 'new_value': 33129.0}, {'field': 'total_amount', 'old_value': 33025.0, 'new_value': 34645.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 139}]
2025-05-17 12:00:57,279 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-17 12:00:57,720 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-17 12:00:57,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203781.0, 'new_value': 212175.0}, {'field': 'total_amount', 'old_value': 203781.0, 'new_value': 212175.0}, {'field': 'order_count', 'old_value': 172, 'new_value': 179}]
2025-05-17 12:00:57,720 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-17 12:00:58,183 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-17 12:00:58,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179146.76, 'new_value': 192243.84}, {'field': 'total_amount', 'old_value': 179146.76, 'new_value': 192243.84}, {'field': 'order_count', 'old_value': 4807, 'new_value': 5155}]
2025-05-17 12:00:58,183 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-17 12:00:58,616 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-17 12:00:58,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200955.6, 'new_value': 210076.3}, {'field': 'total_amount', 'old_value': 200955.6, 'new_value': 210076.3}, {'field': 'order_count', 'old_value': 2223, 'new_value': 2314}]
2025-05-17 12:00:58,617 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-17 12:00:59,067 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-17 12:00:59,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170122.3, 'new_value': 176277.9}, {'field': 'total_amount', 'old_value': 170122.3, 'new_value': 176277.9}, {'field': 'order_count', 'old_value': 282, 'new_value': 298}]
2025-05-17 12:00:59,067 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-17 12:00:59,535 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-17 12:00:59,535 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59717.86, 'new_value': 64584.16}, {'field': 'offline_amount', 'old_value': 325311.84, 'new_value': 341311.84}, {'field': 'total_amount', 'old_value': 385029.7, 'new_value': 405896.0}, {'field': 'order_count', 'old_value': 1676, 'new_value': 1844}]
2025-05-17 12:00:59,535 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-17 12:01:00,003 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-17 12:01:00,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49002.0, 'new_value': 64601.0}, {'field': 'total_amount', 'old_value': 49002.0, 'new_value': 64601.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-05-17 12:01:00,004 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-17 12:01:00,441 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-17 12:01:00,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130433.0, 'new_value': 131418.0}, {'field': 'total_amount', 'old_value': 130433.0, 'new_value': 131418.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 89}]
2025-05-17 12:01:00,441 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-17 12:01:00,957 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-17 12:01:00,957 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67400.19, 'new_value': 70603.19}, {'field': 'total_amount', 'old_value': 73011.71, 'new_value': 76214.71}, {'field': 'order_count', 'old_value': 6454, 'new_value': 6750}]
2025-05-17 12:01:00,957 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-17 12:01:01,444 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-17 12:01:01,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5340.92, 'new_value': 5736.97}, {'field': 'offline_amount', 'old_value': 53192.49, 'new_value': 55781.4}, {'field': 'total_amount', 'old_value': 58533.41, 'new_value': 61518.37}, {'field': 'order_count', 'old_value': 1724, 'new_value': 1805}]
2025-05-17 12:01:01,445 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-17 12:01:01,841 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-17 12:01:01,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21196.0, 'new_value': 21729.9}, {'field': 'total_amount', 'old_value': 22982.9, 'new_value': 23516.8}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-17 12:01:01,841 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-17 12:01:02,268 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-17 12:01:02,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3757767.0, 'new_value': 3991822.0}, {'field': 'total_amount', 'old_value': 3757767.0, 'new_value': 3991822.0}, {'field': 'order_count', 'old_value': 63688, 'new_value': 67819}]
2025-05-17 12:01:02,268 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-17 12:01:02,722 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-17 12:01:02,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51845.0, 'new_value': 53816.0}, {'field': 'total_amount', 'old_value': 51845.0, 'new_value': 53816.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 68}]
2025-05-17 12:01:02,722 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-17 12:01:03,202 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-17 12:01:03,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33810.0, 'new_value': 34459.0}, {'field': 'total_amount', 'old_value': 33810.0, 'new_value': 34459.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 239}]
2025-05-17 12:01:03,202 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-17 12:01:03,615 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-17 12:01:03,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270493.5, 'new_value': 288547.5}, {'field': 'total_amount', 'old_value': 311480.48, 'new_value': 329534.48}, {'field': 'order_count', 'old_value': 2414, 'new_value': 2578}]
2025-05-17 12:01:03,615 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-17 12:01:04,099 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-17 12:01:04,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67954.03, 'new_value': 76179.23}, {'field': 'offline_amount', 'old_value': 171825.59, 'new_value': 180741.79}, {'field': 'total_amount', 'old_value': 239779.62, 'new_value': 256921.02}, {'field': 'order_count', 'old_value': 2867, 'new_value': 3028}]
2025-05-17 12:01:04,099 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-17 12:01:04,590 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-17 12:01:04,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245585.43, 'new_value': 257175.66}, {'field': 'total_amount', 'old_value': 245585.43, 'new_value': 257175.66}, {'field': 'order_count', 'old_value': 1172, 'new_value': 1240}]
2025-05-17 12:01:04,591 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-17 12:01:05,185 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-17 12:01:05,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18710.07, 'new_value': 19895.07}, {'field': 'total_amount', 'old_value': 18710.07, 'new_value': 19895.07}, {'field': 'order_count', 'old_value': 1845, 'new_value': 1954}]
2025-05-17 12:01:05,186 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-17 12:01:05,655 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-17 12:01:05,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71373.68, 'new_value': 74921.98}, {'field': 'total_amount', 'old_value': 71373.68, 'new_value': 74921.98}, {'field': 'order_count', 'old_value': 597, 'new_value': 634}]
2025-05-17 12:01:05,656 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-17 12:01:06,161 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-17 12:01:06,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217751.0, 'new_value': 225751.0}, {'field': 'total_amount', 'old_value': 217751.0, 'new_value': 225751.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-17 12:01:06,161 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-17 12:01:06,646 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-17 12:01:06,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4728900.0, 'new_value': 4830400.0}, {'field': 'total_amount', 'old_value': 4728900.0, 'new_value': 4830400.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-05-17 12:01:06,647 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-17 12:01:07,140 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-17 12:01:07,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36704.0, 'new_value': 37950.0}, {'field': 'total_amount', 'old_value': 43628.0, 'new_value': 44874.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-17 12:01:07,141 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-17 12:01:07,531 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-17 12:01:07,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38613.0, 'new_value': 50235.0}, {'field': 'total_amount', 'old_value': 38613.0, 'new_value': 50235.0}, {'field': 'order_count', 'old_value': 2125, 'new_value': 2858}]
2025-05-17 12:01:07,532 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-17 12:01:08,048 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-17 12:01:08,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28431.4, 'new_value': 29509.3}, {'field': 'total_amount', 'old_value': 28952.6, 'new_value': 30030.5}, {'field': 'order_count', 'old_value': 91, 'new_value': 93}]
2025-05-17 12:01:08,048 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-17 12:01:08,509 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-17 12:01:08,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71245.8, 'new_value': 77845.8}, {'field': 'total_amount', 'old_value': 87815.3, 'new_value': 94415.3}, {'field': 'order_count', 'old_value': 117, 'new_value': 123}]
2025-05-17 12:01:08,510 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-17 12:01:09,012 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-17 12:01:09,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192773.0, 'new_value': 207193.0}, {'field': 'total_amount', 'old_value': 201591.99, 'new_value': 216011.99}, {'field': 'order_count', 'old_value': 35, 'new_value': 40}]
2025-05-17 12:01:09,012 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-17 12:01:09,502 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-17 12:01:09,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158266.0, 'new_value': 164652.0}, {'field': 'total_amount', 'old_value': 158266.0, 'new_value': 164652.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 77}]
2025-05-17 12:01:09,502 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-17 12:01:09,946 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-17 12:01:09,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45893.21, 'new_value': 48426.92}, {'field': 'offline_amount', 'old_value': 156751.36, 'new_value': 163723.45}, {'field': 'total_amount', 'old_value': 202644.57, 'new_value': 212150.37}, {'field': 'order_count', 'old_value': 2716, 'new_value': 2790}]
2025-05-17 12:01:09,946 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-17 12:01:10,424 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-17 12:01:10,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46404.2, 'new_value': 48364.2}, {'field': 'total_amount', 'old_value': 46404.2, 'new_value': 48364.2}, {'field': 'order_count', 'old_value': 126, 'new_value': 130}]
2025-05-17 12:01:10,425 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-17 12:01:10,908 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-17 12:01:10,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20373.0, 'new_value': 21310.0}, {'field': 'total_amount', 'old_value': 20373.0, 'new_value': 21310.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 208}]
2025-05-17 12:01:10,908 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-17 12:01:11,337 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-17 12:01:11,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25403.42, 'new_value': 26163.21}, {'field': 'total_amount', 'old_value': 25403.42, 'new_value': 26163.21}, {'field': 'order_count', 'old_value': 103, 'new_value': 106}]
2025-05-17 12:01:11,337 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-17 12:01:11,793 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-17 12:01:11,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46706.0, 'new_value': 50002.0}, {'field': 'total_amount', 'old_value': 51202.0, 'new_value': 54498.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-05-17 12:01:11,793 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-17 12:01:12,541 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-17 12:01:12,541 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9374.53, 'new_value': 15349.95}, {'field': 'offline_amount', 'old_value': 63375.0, 'new_value': 63415.0}, {'field': 'total_amount', 'old_value': 72749.53, 'new_value': 78764.95}, {'field': 'order_count', 'old_value': 66, 'new_value': 72}]
2025-05-17 12:01:12,541 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-17 12:01:12,981 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-17 12:01:12,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50442.0, 'new_value': 50492.0}, {'field': 'total_amount', 'old_value': 50442.0, 'new_value': 50492.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-17 12:01:12,982 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-17 12:01:13,408 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-17 12:01:13,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53058.89, 'new_value': 66116.89}, {'field': 'total_amount', 'old_value': 53058.89, 'new_value': 66116.89}, {'field': 'order_count', 'old_value': 249, 'new_value': 312}]
2025-05-17 12:01:13,408 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-17 12:01:13,887 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-17 12:01:13,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 481165.7, 'new_value': 494907.7}, {'field': 'total_amount', 'old_value': 481165.7, 'new_value': 494907.7}, {'field': 'order_count', 'old_value': 1252, 'new_value': 1298}]
2025-05-17 12:01:13,887 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-17 12:01:14,370 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-17 12:01:14,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400977.0, 'new_value': 477213.0}, {'field': 'total_amount', 'old_value': 400977.0, 'new_value': 477213.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 64}]
2025-05-17 12:01:14,371 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-17 12:01:14,801 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-17 12:01:14,802 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1015766.93, 'new_value': 1082324.93}, {'field': 'offline_amount', 'old_value': 140363.3, 'new_value': 145157.3}, {'field': 'total_amount', 'old_value': 1156130.23, 'new_value': 1227482.23}, {'field': 'order_count', 'old_value': 4074, 'new_value': 4238}]
2025-05-17 12:01:14,802 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-17 12:01:15,229 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-17 12:01:15,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19085.0, 'new_value': 20474.0}, {'field': 'total_amount', 'old_value': 20461.0, 'new_value': 21850.0}, {'field': 'order_count', 'old_value': 2207, 'new_value': 2336}]
2025-05-17 12:01:15,229 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-17 12:01:15,621 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-17 12:01:15,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17471.0, 'new_value': 18971.0}, {'field': 'total_amount', 'old_value': 17471.0, 'new_value': 18971.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-17 12:01:15,621 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-17 12:01:16,044 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-17 12:01:16,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10137.6, 'new_value': 10728.6}, {'field': 'total_amount', 'old_value': 46976.7, 'new_value': 47567.7}, {'field': 'order_count', 'old_value': 110, 'new_value': 118}]
2025-05-17 12:01:16,045 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-17 12:01:16,448 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-17 12:01:16,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 387381.0, 'new_value': 403322.0}, {'field': 'total_amount', 'old_value': 387821.0, 'new_value': 403762.0}, {'field': 'order_count', 'old_value': 170, 'new_value': 175}]
2025-05-17 12:01:16,449 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-17 12:01:16,939 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-17 12:01:16,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6499.9, 'new_value': 17003.9}, {'field': 'total_amount', 'old_value': 13398.3, 'new_value': 23902.3}, {'field': 'order_count', 'old_value': 61, 'new_value': 65}]
2025-05-17 12:01:16,939 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-17 12:01:17,392 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-17 12:01:17,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123785.0, 'new_value': 128185.0}, {'field': 'total_amount', 'old_value': 123785.0, 'new_value': 128185.0}, {'field': 'order_count', 'old_value': 197, 'new_value': 203}]
2025-05-17 12:01:17,393 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-17 12:01:17,845 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-17 12:01:17,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1.0}, {'field': 'offline_amount', 'old_value': 103010.0, 'new_value': 109270.0}, {'field': 'total_amount', 'old_value': 103010.0, 'new_value': 109271.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-17 12:01:17,846 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-17 12:01:18,307 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-17 12:01:18,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163239.0, 'new_value': 169712.0}, {'field': 'total_amount', 'old_value': 163239.0, 'new_value': 169712.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-05-17 12:01:18,307 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-17 12:01:18,805 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-17 12:01:18,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42300.0, 'new_value': 46800.0}, {'field': 'total_amount', 'old_value': 42300.0, 'new_value': 46800.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-17 12:01:18,805 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-17 12:01:19,378 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-17 12:01:19,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210991.41, 'new_value': 220462.86}, {'field': 'total_amount', 'old_value': 210991.41, 'new_value': 220462.86}, {'field': 'order_count', 'old_value': 1846, 'new_value': 1953}]
2025-05-17 12:01:19,379 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-17 12:01:19,820 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-17 12:01:19,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53497.0, 'new_value': 53671.0}, {'field': 'total_amount', 'old_value': 53497.0, 'new_value': 53671.0}, {'field': 'order_count', 'old_value': 253, 'new_value': 256}]
2025-05-17 12:01:19,821 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-17 12:01:20,302 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-17 12:01:20,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91508.5, 'new_value': 95158.75}, {'field': 'offline_amount', 'old_value': 33208.1, 'new_value': 35296.75}, {'field': 'total_amount', 'old_value': 124716.6, 'new_value': 130455.5}, {'field': 'order_count', 'old_value': 7839, 'new_value': 8141}]
2025-05-17 12:01:20,303 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-17 12:01:20,676 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-17 12:01:20,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7634.6, 'new_value': 9271.6}, {'field': 'total_amount', 'old_value': 8063.6, 'new_value': 9700.6}, {'field': 'order_count', 'old_value': 120, 'new_value': 121}]
2025-05-17 12:01:20,676 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-17 12:01:21,166 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-17 12:01:21,166 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3778.0, 'new_value': 4158.0}, {'field': 'offline_amount', 'old_value': 21034.0, 'new_value': 21553.0}, {'field': 'total_amount', 'old_value': 24812.0, 'new_value': 25711.0}, {'field': 'order_count', 'old_value': 194, 'new_value': 201}]
2025-05-17 12:01:21,166 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-17 12:01:21,628 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-17 12:01:21,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 458946.16, 'new_value': 486206.89}, {'field': 'total_amount', 'old_value': 458946.16, 'new_value': 486206.89}, {'field': 'order_count', 'old_value': 3333, 'new_value': 3555}]
2025-05-17 12:01:21,629 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-17 12:01:22,059 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-17 12:01:22,059 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37846.41, 'new_value': 39932.58}, {'field': 'offline_amount', 'old_value': 277793.72, 'new_value': 290646.94}, {'field': 'total_amount', 'old_value': 315640.13, 'new_value': 330579.52}, {'field': 'order_count', 'old_value': 1415, 'new_value': 1501}]
2025-05-17 12:01:22,060 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-17 12:01:22,509 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-17 12:01:22,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 463004.0, 'new_value': 475244.0}, {'field': 'total_amount', 'old_value': 463004.0, 'new_value': 475244.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 119}]
2025-05-17 12:01:22,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-17 12:01:22,953 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-17 12:01:22,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 984.15, 'new_value': 1080.31}, {'field': 'offline_amount', 'old_value': 16712.75, 'new_value': 17106.31}, {'field': 'total_amount', 'old_value': 17696.9, 'new_value': 18186.62}, {'field': 'order_count', 'old_value': 621, 'new_value': 640}]
2025-05-17 12:01:22,953 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-17 12:01:23,424 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-17 12:01:23,424 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4110.62, 'new_value': 4338.98}, {'field': 'offline_amount', 'old_value': 245115.64, 'new_value': 256776.04}, {'field': 'total_amount', 'old_value': 249226.26, 'new_value': 261115.02}, {'field': 'order_count', 'old_value': 11285, 'new_value': 11849}]
2025-05-17 12:01:23,424 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-17 12:01:23,992 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-17 12:01:23,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52413.0, 'new_value': 54998.0}, {'field': 'total_amount', 'old_value': 52413.0, 'new_value': 54998.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 140}]
2025-05-17 12:01:23,992 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-17 12:01:24,438 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-17 12:01:24,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122275.55, 'new_value': 128466.55}, {'field': 'total_amount', 'old_value': 122275.55, 'new_value': 128466.55}, {'field': 'order_count', 'old_value': 670, 'new_value': 703}]
2025-05-17 12:01:24,438 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-17 12:01:24,843 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-17 12:01:24,844 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7926.0, 'new_value': 8096.7}, {'field': 'total_amount', 'old_value': 18426.0, 'new_value': 18596.7}, {'field': 'order_count', 'old_value': 124, 'new_value': 127}]
2025-05-17 12:01:24,844 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-17 12:01:25,315 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-17 12:01:25,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90547.5, 'new_value': 94447.5}, {'field': 'total_amount', 'old_value': 90547.5, 'new_value': 94447.5}, {'field': 'order_count', 'old_value': 167, 'new_value': 176}]
2025-05-17 12:01:25,315 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-17 12:01:25,783 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-17 12:01:25,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69917.0, 'new_value': 71612.0}, {'field': 'total_amount', 'old_value': 85121.0, 'new_value': 86816.0}, {'field': 'order_count', 'old_value': 1906, 'new_value': 1949}]
2025-05-17 12:01:25,783 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-17 12:01:26,302 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-17 12:01:26,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62862.0, 'new_value': 67552.0}, {'field': 'total_amount', 'old_value': 62862.0, 'new_value': 67552.0}, {'field': 'order_count', 'old_value': 421, 'new_value': 450}]
2025-05-17 12:01:26,303 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-17 12:01:26,798 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-17 12:01:26,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98906.0, 'new_value': 99744.0}, {'field': 'total_amount', 'old_value': 98906.0, 'new_value': 99744.0}, {'field': 'order_count', 'old_value': 3168, 'new_value': 3194}]
2025-05-17 12:01:26,798 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-17 12:01:27,228 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-17 12:01:27,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63069.46, 'new_value': 70532.01}, {'field': 'offline_amount', 'old_value': 246075.6, 'new_value': 251672.85}, {'field': 'total_amount', 'old_value': 309145.06, 'new_value': 322204.86}, {'field': 'order_count', 'old_value': 2362, 'new_value': 2529}]
2025-05-17 12:01:27,228 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-17 12:01:27,680 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-17 12:01:27,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163431.8, 'new_value': 171592.66}, {'field': 'total_amount', 'old_value': 219857.98, 'new_value': 228018.84}, {'field': 'order_count', 'old_value': 388, 'new_value': 400}]
2025-05-17 12:01:27,680 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-17 12:01:28,116 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-17 12:01:28,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145339.17, 'new_value': 154364.71}, {'field': 'total_amount', 'old_value': 164512.6, 'new_value': 173538.14}, {'field': 'order_count', 'old_value': 3443, 'new_value': 3576}]
2025-05-17 12:01:28,117 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-17 12:01:28,592 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-17 12:01:28,592 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68055.0, 'new_value': 70986.0}, {'field': 'offline_amount', 'old_value': 50564.76, 'new_value': 52922.66}, {'field': 'total_amount', 'old_value': 118619.76, 'new_value': 123908.66}, {'field': 'order_count', 'old_value': 782, 'new_value': 818}]
2025-05-17 12:01:28,593 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-17 12:01:29,030 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-17 12:01:29,030 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227394.0, 'new_value': 244077.2}, {'field': 'total_amount', 'old_value': 227394.0, 'new_value': 244077.2}, {'field': 'order_count', 'old_value': 282, 'new_value': 302}]
2025-05-17 12:01:29,030 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-17 12:01:29,516 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-17 12:01:29,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 574226.0, 'new_value': 605174.0}, {'field': 'total_amount', 'old_value': 574226.0, 'new_value': 605174.0}, {'field': 'order_count', 'old_value': 683, 'new_value': 713}]
2025-05-17 12:01:29,517 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-17 12:01:29,922 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-17 12:01:29,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 423980.58, 'new_value': 447328.58}, {'field': 'total_amount', 'old_value': 423980.58, 'new_value': 447328.58}, {'field': 'order_count', 'old_value': 3254, 'new_value': 3470}]
2025-05-17 12:01:29,922 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-17 12:01:30,340 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-17 12:01:30,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16184.0, 'new_value': 17884.0}, {'field': 'total_amount', 'old_value': 16184.0, 'new_value': 17884.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 95}]
2025-05-17 12:01:30,340 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-17 12:01:30,745 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-17 12:01:30,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 399320.0, 'new_value': 426617.0}, {'field': 'total_amount', 'old_value': 399320.0, 'new_value': 426617.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-05-17 12:01:30,745 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-17 12:01:31,191 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-17 12:01:31,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 215185.43, 'new_value': 224058.11}, {'field': 'offline_amount', 'old_value': 772439.29, 'new_value': 818088.59}, {'field': 'total_amount', 'old_value': 987624.72, 'new_value': 1042146.7}, {'field': 'order_count', 'old_value': 4984, 'new_value': 5273}]
2025-05-17 12:01:31,192 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-17 12:01:31,618 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-17 12:01:31,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49363.43, 'new_value': 52077.78}, {'field': 'total_amount', 'old_value': 49363.43, 'new_value': 52077.78}, {'field': 'order_count', 'old_value': 2846, 'new_value': 3003}]
2025-05-17 12:01:31,619 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-17 12:01:32,050 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-17 12:01:32,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5491.0, 'new_value': 5765.0}, {'field': 'total_amount', 'old_value': 5491.0, 'new_value': 5765.0}, {'field': 'order_count', 'old_value': 261, 'new_value': 305}]
2025-05-17 12:01:32,050 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-17 12:01:32,523 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-17 12:01:32,523 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9192.45, 'new_value': 9875.45}, {'field': 'offline_amount', 'old_value': 167099.0, 'new_value': 178466.0}, {'field': 'total_amount', 'old_value': 176291.45, 'new_value': 188341.45}, {'field': 'order_count', 'old_value': 892, 'new_value': 980}]
2025-05-17 12:01:32,523 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-17 12:01:33,015 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-17 12:01:33,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115040.0, 'new_value': 135100.0}, {'field': 'total_amount', 'old_value': 115040.0, 'new_value': 135100.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-05-17 12:01:33,015 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-17 12:01:33,476 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-17 12:01:33,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80788.4, 'new_value': 85741.9}, {'field': 'offline_amount', 'old_value': 67964.6, 'new_value': 72299.4}, {'field': 'total_amount', 'old_value': 148753.0, 'new_value': 158041.3}, {'field': 'order_count', 'old_value': 3502, 'new_value': 3717}]
2025-05-17 12:01:33,476 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-17 12:01:33,888 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-17 12:01:33,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1530000.0, 'new_value': 1580000.0}, {'field': 'total_amount', 'old_value': 1530000.0, 'new_value': 1580000.0}, {'field': 'order_count', 'old_value': 272, 'new_value': 273}]
2025-05-17 12:01:33,888 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-17 12:01:34,351 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-17 12:01:34,352 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-17 12:01:34,352 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-17 12:01:34,853 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-17 12:01:34,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160079.45, 'new_value': 167666.45}, {'field': 'total_amount', 'old_value': 160079.45, 'new_value': 167666.45}, {'field': 'order_count', 'old_value': 983, 'new_value': 1039}]
2025-05-17 12:01:34,854 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-17 12:01:35,361 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-17 12:01:35,361 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9687.0, 'new_value': 10492.0}, {'field': 'offline_amount', 'old_value': 15245.0, 'new_value': 18143.0}, {'field': 'total_amount', 'old_value': 24932.0, 'new_value': 28635.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 41}]
2025-05-17 12:01:35,361 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-17 12:01:35,785 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-17 12:01:35,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52213.83, 'new_value': 70814.83}, {'field': 'total_amount', 'old_value': 52213.83, 'new_value': 70814.83}, {'field': 'order_count', 'old_value': 81, 'new_value': 89}]
2025-05-17 12:01:35,786 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-17 12:01:36,259 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-17 12:01:36,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196704.0, 'new_value': 210655.0}, {'field': 'total_amount', 'old_value': 210479.0, 'new_value': 224430.0}, {'field': 'order_count', 'old_value': 4448, 'new_value': 4653}]
2025-05-17 12:01:36,259 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-17 12:01:36,700 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-17 12:01:36,700 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47226.13, 'new_value': 49603.61}, {'field': 'offline_amount', 'old_value': 123378.72, 'new_value': 129125.18}, {'field': 'total_amount', 'old_value': 170604.85, 'new_value': 178728.79}, {'field': 'order_count', 'old_value': 3016, 'new_value': 3231}]
2025-05-17 12:01:36,700 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-17 12:01:37,219 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-17 12:01:37,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34814.25, 'new_value': 36895.2}, {'field': 'total_amount', 'old_value': 34814.25, 'new_value': 36895.2}, {'field': 'order_count', 'old_value': 130, 'new_value': 139}]
2025-05-17 12:01:37,220 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-17 12:01:37,662 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-17 12:01:37,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 451937.71, 'new_value': 475981.14}, {'field': 'total_amount', 'old_value': 451937.71, 'new_value': 475981.14}, {'field': 'order_count', 'old_value': 5276, 'new_value': 5591}]
2025-05-17 12:01:37,663 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-17 12:01:38,147 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-17 12:01:38,147 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147120.2, 'new_value': 151132.8}, {'field': 'offline_amount', 'old_value': 322977.0, 'new_value': 327809.0}, {'field': 'total_amount', 'old_value': 470097.2, 'new_value': 478941.8}, {'field': 'order_count', 'old_value': 3158, 'new_value': 3253}]
2025-05-17 12:01:38,147 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-17 12:01:38,577 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-17 12:01:38,577 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132036.81, 'new_value': 140685.98}, {'field': 'offline_amount', 'old_value': 477005.43, 'new_value': 499502.98}, {'field': 'total_amount', 'old_value': 609042.24, 'new_value': 640188.96}, {'field': 'order_count', 'old_value': 3462, 'new_value': 3656}]
2025-05-17 12:01:38,577 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-17 12:01:39,046 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-17 12:01:39,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19875.41, 'new_value': 20793.38}, {'field': 'offline_amount', 'old_value': 221158.77, 'new_value': 228273.37}, {'field': 'total_amount', 'old_value': 241034.18, 'new_value': 249066.75}, {'field': 'order_count', 'old_value': 8946, 'new_value': 9011}]
2025-05-17 12:01:39,046 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-17 12:01:39,609 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-17 12:01:39,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125315.0, 'new_value': 129961.0}, {'field': 'total_amount', 'old_value': 125315.0, 'new_value': 129961.0}, {'field': 'order_count', 'old_value': 2046, 'new_value': 2116}]
2025-05-17 12:01:39,609 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-17 12:01:39,998 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-17 12:01:39,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116389.24, 'new_value': 121304.91}, {'field': 'total_amount', 'old_value': 116389.24, 'new_value': 121304.91}, {'field': 'order_count', 'old_value': 4900, 'new_value': 5118}]
2025-05-17 12:01:39,998 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-17 12:01:40,446 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-17 12:01:40,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142076.34, 'new_value': 150049.77}, {'field': 'total_amount', 'old_value': 142076.34, 'new_value': 150049.77}, {'field': 'order_count', 'old_value': 1078, 'new_value': 1146}]
2025-05-17 12:01:40,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-17 12:01:40,882 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-17 12:01:40,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58471.5, 'new_value': 61571.0}, {'field': 'total_amount', 'old_value': 60000.3, 'new_value': 63099.8}, {'field': 'order_count', 'old_value': 360, 'new_value': 376}]
2025-05-17 12:01:40,882 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-17 12:01:41,326 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-17 12:01:41,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24856.6, 'new_value': 29780.6}, {'field': 'total_amount', 'old_value': 100817.51, 'new_value': 105741.51}, {'field': 'order_count', 'old_value': 2895, 'new_value': 3044}]
2025-05-17 12:01:41,327 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-17 12:01:41,729 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-17 12:01:41,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35840.0, 'new_value': 36716.0}, {'field': 'total_amount', 'old_value': 35840.0, 'new_value': 36716.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 80}]
2025-05-17 12:01:41,729 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-17 12:01:42,156 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-17 12:01:42,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118674.3, 'new_value': 126527.3}, {'field': 'offline_amount', 'old_value': 19063.1, 'new_value': 19415.1}, {'field': 'total_amount', 'old_value': 137737.4, 'new_value': 145942.4}, {'field': 'order_count', 'old_value': 6497, 'new_value': 6997}]
2025-05-17 12:01:42,157 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-17 12:01:42,664 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-17 12:01:42,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 450000.0, 'new_value': 455000.0}, {'field': 'total_amount', 'old_value': 450000.0, 'new_value': 455000.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 140}]
2025-05-17 12:01:42,665 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-17 12:01:43,072 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-17 12:01:43,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 430000.0, 'new_value': 435000.0}, {'field': 'total_amount', 'old_value': 430000.0, 'new_value': 435000.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 139}]
2025-05-17 12:01:43,073 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-17 12:01:43,534 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-17 12:01:43,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2648674.0, 'new_value': 2698674.0}, {'field': 'total_amount', 'old_value': 2648674.0, 'new_value': 2698674.0}, {'field': 'order_count', 'old_value': 292, 'new_value': 293}]
2025-05-17 12:01:43,534 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-17 12:01:44,015 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-17 12:01:44,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247791.08, 'new_value': 265403.04}, {'field': 'total_amount', 'old_value': 247791.08, 'new_value': 265403.04}, {'field': 'order_count', 'old_value': 830, 'new_value': 875}]
2025-05-17 12:01:44,015 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-17 12:01:44,430 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-17 12:01:44,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23438.0, 'new_value': 24272.0}, {'field': 'total_amount', 'old_value': 23438.0, 'new_value': 24272.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 37}]
2025-05-17 12:01:44,430 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-17 12:01:44,905 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-17 12:01:44,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196424.0, 'new_value': 209336.0}, {'field': 'total_amount', 'old_value': 196424.0, 'new_value': 209336.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 311}]
2025-05-17 12:01:44,905 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-17 12:01:45,357 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-17 12:01:45,357 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5594.02, 'new_value': 7281.08}, {'field': 'offline_amount', 'old_value': 29999.76, 'new_value': 41597.22}, {'field': 'total_amount', 'old_value': 35593.78, 'new_value': 48878.3}, {'field': 'order_count', 'old_value': 793, 'new_value': 1141}]
2025-05-17 12:01:45,357 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-17 12:01:45,772 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-17 12:01:45,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28465.5, 'new_value': 29102.5}, {'field': 'total_amount', 'old_value': 38220.0, 'new_value': 38857.0}, {'field': 'order_count', 'old_value': 431, 'new_value': 439}]
2025-05-17 12:01:45,773 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-17 12:01:46,192 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-17 12:01:46,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64397.07, 'new_value': 70402.94}, {'field': 'offline_amount', 'old_value': 128829.75, 'new_value': 136204.35}, {'field': 'total_amount', 'old_value': 193226.82, 'new_value': 206607.29}, {'field': 'order_count', 'old_value': 6130, 'new_value': 6562}]
2025-05-17 12:01:46,192 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-17 12:01:46,639 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-17 12:01:46,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61620.0, 'new_value': 66014.0}, {'field': 'total_amount', 'old_value': 61620.0, 'new_value': 66014.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 287}]
2025-05-17 12:01:46,639 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-17 12:01:47,146 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-17 12:01:47,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31579.1, 'new_value': 31581.1}, {'field': 'total_amount', 'old_value': 32007.1, 'new_value': 32009.1}, {'field': 'order_count', 'old_value': 9240, 'new_value': 16269}]
2025-05-17 12:01:47,146 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-17 12:01:47,576 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-17 12:01:47,577 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101071.51, 'new_value': 111268.45}, {'field': 'offline_amount', 'old_value': 31559.08, 'new_value': 32951.09}, {'field': 'total_amount', 'old_value': 132630.59, 'new_value': 144219.54}, {'field': 'order_count', 'old_value': 7437, 'new_value': 8117}]
2025-05-17 12:01:47,577 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-17 12:01:48,112 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-17 12:01:48,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157539.19, 'new_value': 166997.39}, {'field': 'total_amount', 'old_value': 180026.59, 'new_value': 189484.79}, {'field': 'order_count', 'old_value': 995, 'new_value': 1047}]
2025-05-17 12:01:48,112 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-17 12:01:48,610 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-17 12:01:48,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129178.64, 'new_value': 134293.8}, {'field': 'offline_amount', 'old_value': 257946.92, 'new_value': 265946.92}, {'field': 'total_amount', 'old_value': 387125.56, 'new_value': 400240.72}, {'field': 'order_count', 'old_value': 873, 'new_value': 928}]
2025-05-17 12:01:48,611 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-17 12:01:49,092 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-17 12:01:49,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93614.4, 'new_value': 101775.37}, {'field': 'offline_amount', 'old_value': 175150.43, 'new_value': 186091.85}, {'field': 'total_amount', 'old_value': 268764.83, 'new_value': 287867.22}, {'field': 'order_count', 'old_value': 2187, 'new_value': 2367}]
2025-05-17 12:01:49,092 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-17 12:01:49,537 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-17 12:01:49,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1346797.0, 'new_value': 1388971.0}, {'field': 'total_amount', 'old_value': 1346797.0, 'new_value': 1388971.0}, {'field': 'order_count', 'old_value': 5121, 'new_value': 5288}]
2025-05-17 12:01:49,537 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-17 12:01:50,010 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-17 12:01:50,010 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9705.03, 'new_value': 10420.56}, {'field': 'offline_amount', 'old_value': 22648.0, 'new_value': 23407.4}, {'field': 'total_amount', 'old_value': 32353.03, 'new_value': 33827.96}, {'field': 'order_count', 'old_value': 1295, 'new_value': 1357}]
2025-05-17 12:01:50,010 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-17 12:01:50,457 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-17 12:01:50,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215367.84, 'new_value': 221485.92}, {'field': 'total_amount', 'old_value': 215367.84, 'new_value': 221485.92}, {'field': 'order_count', 'old_value': 1129, 'new_value': 1168}]
2025-05-17 12:01:50,458 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-17 12:01:50,941 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-17 12:01:50,941 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30843.56, 'new_value': 34191.07}, {'field': 'offline_amount', 'old_value': 30667.69, 'new_value': 31760.79}, {'field': 'total_amount', 'old_value': 61511.25, 'new_value': 65951.86}, {'field': 'order_count', 'old_value': 4831, 'new_value': 5275}]
2025-05-17 12:01:50,941 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-17 12:01:51,422 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-17 12:01:51,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252529.3, 'new_value': 252569.2}, {'field': 'total_amount', 'old_value': 252529.3, 'new_value': 252569.2}]
2025-05-17 12:01:51,422 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-17 12:01:51,852 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-17 12:01:51,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55862.0, 'new_value': 61236.0}, {'field': 'total_amount', 'old_value': 55862.0, 'new_value': 61236.0}, {'field': 'order_count', 'old_value': 1685, 'new_value': 1686}]
2025-05-17 12:01:51,853 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-17 12:01:52,320 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-17 12:01:52,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56231.56, 'new_value': 60860.56}, {'field': 'total_amount', 'old_value': 56231.56, 'new_value': 60860.56}, {'field': 'order_count', 'old_value': 2679, 'new_value': 3024}]
2025-05-17 12:01:52,320 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-17 12:01:52,789 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-17 12:01:52,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 390640.0, 'new_value': 397777.0}, {'field': 'total_amount', 'old_value': 390640.0, 'new_value': 397777.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 55}]
2025-05-17 12:01:52,789 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-17 12:01:53,240 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-17 12:01:53,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 426297.0, 'new_value': 439198.0}, {'field': 'total_amount', 'old_value': 426297.0, 'new_value': 439198.0}, {'field': 'order_count', 'old_value': 260, 'new_value': 292}]
2025-05-17 12:01:53,240 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-17 12:01:53,709 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-17 12:01:53,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51240.0, 'new_value': 54240.0}, {'field': 'total_amount', 'old_value': 51240.0, 'new_value': 54240.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-17 12:01:53,709 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-17 12:01:54,098 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-17 12:01:54,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 491325.18, 'new_value': 513491.43}, {'field': 'total_amount', 'old_value': 491325.18, 'new_value': 513491.43}, {'field': 'order_count', 'old_value': 3830, 'new_value': 4026}]
2025-05-17 12:01:54,098 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-17 12:01:54,628 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-17 12:01:54,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 387033.0, 'new_value': 398504.0}, {'field': 'total_amount', 'old_value': 387033.0, 'new_value': 398504.0}, {'field': 'order_count', 'old_value': 2305, 'new_value': 2416}]
2025-05-17 12:01:54,628 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-17 12:01:55,071 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-17 12:01:55,072 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2181.0, 'new_value': 2244.0}, {'field': 'offline_amount', 'old_value': 19174.6, 'new_value': 19625.6}, {'field': 'total_amount', 'old_value': 21355.6, 'new_value': 21869.6}, {'field': 'order_count', 'old_value': 787, 'new_value': 809}]
2025-05-17 12:01:55,072 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-17 12:01:55,528 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-17 12:01:55,528 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60429.3, 'new_value': 63274.88}, {'field': 'total_amount', 'old_value': 67658.37, 'new_value': 70503.95}, {'field': 'order_count', 'old_value': 384, 'new_value': 408}]
2025-05-17 12:01:55,529 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-17 12:01:55,953 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-17 12:01:55,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3305.0, 'new_value': 3403.0}, {'field': 'offline_amount', 'old_value': 12998.0, 'new_value': 13683.0}, {'field': 'total_amount', 'old_value': 16303.0, 'new_value': 17086.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 151}]
2025-05-17 12:01:55,953 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-17 12:01:56,408 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-17 12:01:56,408 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43676.0, 'new_value': 45554.0}, {'field': 'offline_amount', 'old_value': 164170.0, 'new_value': 174869.0}, {'field': 'total_amount', 'old_value': 207846.0, 'new_value': 220423.0}, {'field': 'order_count', 'old_value': 908, 'new_value': 958}]
2025-05-17 12:01:56,408 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-17 12:01:56,860 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-17 12:01:56,861 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176104.74, 'new_value': 194324.71}, {'field': 'offline_amount', 'old_value': 134175.34, 'new_value': 141371.59}, {'field': 'total_amount', 'old_value': 310280.08, 'new_value': 335696.3}, {'field': 'order_count', 'old_value': 12314, 'new_value': 13355}]
2025-05-17 12:01:56,861 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-17 12:01:57,317 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-17 12:01:57,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184619.0, 'new_value': 192714.0}, {'field': 'total_amount', 'old_value': 184619.0, 'new_value': 192714.0}, {'field': 'order_count', 'old_value': 213, 'new_value': 228}]
2025-05-17 12:01:57,317 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-17 12:01:57,777 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-17 12:01:57,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221949.1, 'new_value': 234417.3}, {'field': 'total_amount', 'old_value': 221949.1, 'new_value': 234417.3}, {'field': 'order_count', 'old_value': 4790, 'new_value': 5069}]
2025-05-17 12:01:57,778 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-17 12:01:58,196 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-17 12:01:58,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33807.4, 'new_value': 36176.4}, {'field': 'total_amount', 'old_value': 33807.4, 'new_value': 36176.4}, {'field': 'order_count', 'old_value': 173, 'new_value': 186}]
2025-05-17 12:01:58,196 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-17 12:01:58,617 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-17 12:01:58,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 473487.3, 'new_value': 483867.14}, {'field': 'total_amount', 'old_value': 473487.3, 'new_value': 483867.14}, {'field': 'order_count', 'old_value': 2528, 'new_value': 2635}]
2025-05-17 12:01:58,618 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-17 12:01:59,102 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-17 12:01:59,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87492.6, 'new_value': 91843.43}, {'field': 'total_amount', 'old_value': 87492.6, 'new_value': 91843.43}, {'field': 'order_count', 'old_value': 5980, 'new_value': 6283}]
2025-05-17 12:01:59,102 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-17 12:01:59,680 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-17 12:01:59,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 329038.0, 'new_value': 335262.0}, {'field': 'total_amount', 'old_value': 329038.0, 'new_value': 335262.0}, {'field': 'order_count', 'old_value': 7444, 'new_value': 7589}]
2025-05-17 12:01:59,680 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-17 12:02:00,145 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-17 12:02:00,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63666.0, 'new_value': 66729.0}, {'field': 'total_amount', 'old_value': 63666.0, 'new_value': 66729.0}, {'field': 'order_count', 'old_value': 4385, 'new_value': 4573}]
2025-05-17 12:02:00,145 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-17 12:02:00,583 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-17 12:02:00,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26308.0, 'new_value': 36245.0}, {'field': 'total_amount', 'old_value': 26308.0, 'new_value': 36245.0}, {'field': 'order_count', 'old_value': 5397, 'new_value': 7709}]
2025-05-17 12:02:00,584 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-17 12:02:01,112 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-17 12:02:01,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38107.0, 'new_value': 53012.0}, {'field': 'total_amount', 'old_value': 38107.0, 'new_value': 53012.0}, {'field': 'order_count', 'old_value': 5397, 'new_value': 7709}]
2025-05-17 12:02:01,112 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-17 12:02:01,589 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-17 12:02:01,590 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49780.2, 'new_value': 51039.2}, {'field': 'total_amount', 'old_value': 50033.2, 'new_value': 51292.2}, {'field': 'order_count', 'old_value': 768, 'new_value': 783}]
2025-05-17 12:02:01,590 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-17 12:02:02,032 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-17 12:02:02,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22780.0, 'new_value': 33880.0}, {'field': 'total_amount', 'old_value': 22780.0, 'new_value': 33880.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-17 12:02:02,033 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-17 12:02:02,482 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-17 12:02:02,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2966105.12, 'new_value': 3147849.32}, {'field': 'total_amount', 'old_value': 2966105.12, 'new_value': 3147849.32}, {'field': 'order_count', 'old_value': 61726, 'new_value': 65545}]
2025-05-17 12:02:02,482 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-17 12:02:02,857 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-17 12:02:02,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111534.9, 'new_value': 113449.9}, {'field': 'total_amount', 'old_value': 111534.9, 'new_value': 113449.9}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-17 12:02:02,858 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-17 12:02:03,371 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-17 12:02:03,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 355267.47, 'new_value': 370823.47}, {'field': 'total_amount', 'old_value': 355267.47, 'new_value': 370823.47}, {'field': 'order_count', 'old_value': 4444, 'new_value': 4569}]
2025-05-17 12:02:03,371 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-17 12:02:03,847 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-17 12:02:03,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113319.78, 'new_value': 121995.67}, {'field': 'total_amount', 'old_value': 113319.78, 'new_value': 121995.67}, {'field': 'order_count', 'old_value': 2185, 'new_value': 2334}]
2025-05-17 12:02:03,847 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-17 12:02:04,242 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-17 12:02:04,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245205.0, 'new_value': 249675.0}, {'field': 'total_amount', 'old_value': 245205.0, 'new_value': 249675.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 56}]
2025-05-17 12:02:04,243 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-17 12:02:04,709 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-17 12:02:04,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79086.8, 'new_value': 85933.8}, {'field': 'total_amount', 'old_value': 79086.8, 'new_value': 85933.8}, {'field': 'order_count', 'old_value': 2048, 'new_value': 2173}]
2025-05-17 12:02:04,709 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-17 12:02:05,264 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-17 12:02:05,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53908.0, 'new_value': 72909.0}, {'field': 'total_amount', 'old_value': 53908.0, 'new_value': 72909.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 196}]
2025-05-17 12:02:05,264 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-17 12:02:05,759 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-17 12:02:05,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18073.0, 'new_value': 19526.0}, {'field': 'total_amount', 'old_value': 18073.0, 'new_value': 19526.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 67}]
2025-05-17 12:02:05,759 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-17 12:02:06,329 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-17 12:02:06,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57546.63, 'new_value': 61293.41}, {'field': 'offline_amount', 'old_value': 274674.6, 'new_value': 285240.2}, {'field': 'total_amount', 'old_value': 332221.23, 'new_value': 346533.61}, {'field': 'order_count', 'old_value': 2197, 'new_value': 2317}]
2025-05-17 12:02:06,329 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-17 12:02:06,838 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-17 12:02:06,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50518.0, 'new_value': 54168.0}, {'field': 'total_amount', 'old_value': 52368.0, 'new_value': 56018.0}, {'field': 'order_count', 'old_value': 305, 'new_value': 325}]
2025-05-17 12:02:06,839 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-17 12:02:07,287 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-17 12:02:07,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 43.0}, {'field': 'offline_amount', 'old_value': 10627.0, 'new_value': 19141.6}, {'field': 'total_amount', 'old_value': 10627.0, 'new_value': 19184.6}, {'field': 'order_count', 'old_value': 60, 'new_value': 113}]
2025-05-17 12:02:07,288 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-17 12:02:07,909 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-17 12:02:07,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18877.47, 'new_value': 23293.63}, {'field': 'total_amount', 'old_value': 53525.8, 'new_value': 57941.96}, {'field': 'order_count', 'old_value': 3487, 'new_value': 3782}]
2025-05-17 12:02:07,910 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-17 12:02:08,309 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-17 12:02:08,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30408.14, 'new_value': 38284.48}, {'field': 'total_amount', 'old_value': 90190.11, 'new_value': 98066.45}, {'field': 'order_count', 'old_value': 5895, 'new_value': 6414}]
2025-05-17 12:02:08,309 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-17 12:02:08,787 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-17 12:02:08,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 707721.16, 'new_value': 758299.85}, {'field': 'total_amount', 'old_value': 707721.16, 'new_value': 758299.85}, {'field': 'order_count', 'old_value': 2123, 'new_value': 2262}]
2025-05-17 12:02:08,788 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-17 12:02:09,351 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-17 12:02:09,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103547.0, 'new_value': 111238.0}, {'field': 'total_amount', 'old_value': 103547.0, 'new_value': 111238.0}, {'field': 'order_count', 'old_value': 3623, 'new_value': 3911}]
2025-05-17 12:02:09,352 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-17 12:02:09,826 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-17 12:02:09,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 428664.83, 'new_value': 460940.54}, {'field': 'total_amount', 'old_value': 428664.83, 'new_value': 460940.54}, {'field': 'order_count', 'old_value': 2050, 'new_value': 2223}]
2025-05-17 12:02:09,826 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-17 12:02:10,423 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-17 12:02:10,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 484669.76, 'new_value': 507733.96}, {'field': 'total_amount', 'old_value': 484669.76, 'new_value': 507733.96}, {'field': 'order_count', 'old_value': 1551, 'new_value': 1617}]
2025-05-17 12:02:10,423 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-17 12:02:10,863 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-17 12:02:10,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74504.0, 'new_value': 82279.0}, {'field': 'total_amount', 'old_value': 74584.0, 'new_value': 82359.0}, {'field': 'order_count', 'old_value': 7874, 'new_value': 7875}]
2025-05-17 12:02:10,863 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-17 12:02:11,280 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-17 12:02:11,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33109.0, 'new_value': 35013.0}, {'field': 'total_amount', 'old_value': 45251.0, 'new_value': 47155.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 68}]
2025-05-17 12:02:11,281 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-17 12:02:11,701 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-17 12:02:11,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65145.0, 'new_value': 71117.0}, {'field': 'total_amount', 'old_value': 65145.0, 'new_value': 71117.0}, {'field': 'order_count', 'old_value': 357, 'new_value': 358}]
2025-05-17 12:02:11,701 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-17 12:02:12,152 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-17 12:02:12,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110462.0, 'new_value': 122185.0}, {'field': 'total_amount', 'old_value': 110462.0, 'new_value': 122185.0}, {'field': 'order_count', 'old_value': 11725, 'new_value': 12930}]
2025-05-17 12:02:12,153 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-17 12:02:12,566 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-17 12:02:12,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84021.0, 'new_value': 86744.0}, {'field': 'total_amount', 'old_value': 84021.0, 'new_value': 86744.0}, {'field': 'order_count', 'old_value': 715, 'new_value': 757}]
2025-05-17 12:02:12,566 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-17 12:02:13,029 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-17 12:02:13,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32100.46, 'new_value': 33496.06}, {'field': 'total_amount', 'old_value': 32100.46, 'new_value': 33496.06}, {'field': 'order_count', 'old_value': 533, 'new_value': 565}]
2025-05-17 12:02:13,030 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-17 12:02:13,435 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-17 12:02:13,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82619.89, 'new_value': 86128.49}, {'field': 'offline_amount', 'old_value': 419006.13, 'new_value': 450614.33}, {'field': 'total_amount', 'old_value': 501626.02, 'new_value': 536742.82}, {'field': 'order_count', 'old_value': 1131, 'new_value': 1202}]
2025-05-17 12:02:13,435 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-17 12:02:13,871 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-17 12:02:13,872 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50969.4, 'new_value': 54896.8}, {'field': 'offline_amount', 'old_value': 492659.89, 'new_value': 540486.67}, {'field': 'total_amount', 'old_value': 541754.96, 'new_value': 593509.14}, {'field': 'order_count', 'old_value': 2613, 'new_value': 2829}]
2025-05-17 12:02:13,872 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-17 12:02:14,307 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-17 12:02:14,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65599.0, 'new_value': 69716.0}, {'field': 'total_amount', 'old_value': 65599.0, 'new_value': 69716.0}, {'field': 'order_count', 'old_value': 224, 'new_value': 237}]
2025-05-17 12:02:14,307 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-17 12:02:14,776 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-17 12:02:14,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3000.0, 'new_value': 5800.0}, {'field': 'total_amount', 'old_value': 4780.95, 'new_value': 7580.95}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-17 12:02:14,776 - INFO - 日期 2025-05 处理完成 - 更新: 229 条，插入: 0 条，错误: 0 条
2025-05-17 12:02:14,776 - INFO - 数据同步完成！更新: 229 条，插入: 0 条，错误: 0 条
2025-05-17 12:02:14,778 - INFO - =================同步完成====================
2025-05-17 15:00:01,930 - INFO - =================使用默认全量同步=============
2025-05-17 15:00:03,308 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-17 15:00:03,308 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-17 15:00:03,335 - INFO - 开始处理日期: 2025-01
2025-05-17 15:00:03,338 - INFO - Request Parameters - Page 1:
2025-05-17 15:00:03,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:03,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:04,566 - INFO - Response - Page 1:
2025-05-17 15:00:04,766 - INFO - 第 1 页获取到 100 条记录
2025-05-17 15:00:04,766 - INFO - Request Parameters - Page 2:
2025-05-17 15:00:04,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:04,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:05,303 - INFO - Response - Page 2:
2025-05-17 15:00:05,504 - INFO - 第 2 页获取到 100 条记录
2025-05-17 15:00:05,504 - INFO - Request Parameters - Page 3:
2025-05-17 15:00:05,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:05,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:06,050 - INFO - Response - Page 3:
2025-05-17 15:00:06,250 - INFO - 第 3 页获取到 100 条记录
2025-05-17 15:00:06,250 - INFO - Request Parameters - Page 4:
2025-05-17 15:00:06,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:06,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:06,755 - INFO - Response - Page 4:
2025-05-17 15:00:06,956 - INFO - 第 4 页获取到 100 条记录
2025-05-17 15:00:06,956 - INFO - Request Parameters - Page 5:
2025-05-17 15:00:06,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:06,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:07,451 - INFO - Response - Page 5:
2025-05-17 15:00:07,651 - INFO - 第 5 页获取到 100 条记录
2025-05-17 15:00:07,651 - INFO - Request Parameters - Page 6:
2025-05-17 15:00:07,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:07,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:08,165 - INFO - Response - Page 6:
2025-05-17 15:00:08,365 - INFO - 第 6 页获取到 100 条记录
2025-05-17 15:00:08,365 - INFO - Request Parameters - Page 7:
2025-05-17 15:00:08,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:08,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:08,859 - INFO - Response - Page 7:
2025-05-17 15:00:09,059 - INFO - 第 7 页获取到 82 条记录
2025-05-17 15:00:09,059 - INFO - 查询完成，共获取到 682 条记录
2025-05-17 15:00:09,059 - INFO - 获取到 682 条表单数据
2025-05-17 15:00:09,072 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-17 15:00:09,086 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 15:00:09,086 - INFO - 开始处理日期: 2025-02
2025-05-17 15:00:09,086 - INFO - Request Parameters - Page 1:
2025-05-17 15:00:09,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:09,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:09,713 - INFO - Response - Page 1:
2025-05-17 15:00:09,914 - INFO - 第 1 页获取到 100 条记录
2025-05-17 15:00:09,914 - INFO - Request Parameters - Page 2:
2025-05-17 15:00:09,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:09,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:10,486 - INFO - Response - Page 2:
2025-05-17 15:00:10,687 - INFO - 第 2 页获取到 100 条记录
2025-05-17 15:00:10,687 - INFO - Request Parameters - Page 3:
2025-05-17 15:00:10,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:10,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:11,163 - INFO - Response - Page 3:
2025-05-17 15:00:11,363 - INFO - 第 3 页获取到 100 条记录
2025-05-17 15:00:11,363 - INFO - Request Parameters - Page 4:
2025-05-17 15:00:11,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:11,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:11,860 - INFO - Response - Page 4:
2025-05-17 15:00:12,060 - INFO - 第 4 页获取到 100 条记录
2025-05-17 15:00:12,060 - INFO - Request Parameters - Page 5:
2025-05-17 15:00:12,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:12,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:12,671 - INFO - Response - Page 5:
2025-05-17 15:00:12,872 - INFO - 第 5 页获取到 100 条记录
2025-05-17 15:00:12,872 - INFO - Request Parameters - Page 6:
2025-05-17 15:00:12,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:12,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:13,326 - INFO - Response - Page 6:
2025-05-17 15:00:13,526 - INFO - 第 6 页获取到 100 条记录
2025-05-17 15:00:13,526 - INFO - Request Parameters - Page 7:
2025-05-17 15:00:13,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:13,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:13,955 - INFO - Response - Page 7:
2025-05-17 15:00:14,155 - INFO - 第 7 页获取到 70 条记录
2025-05-17 15:00:14,155 - INFO - 查询完成，共获取到 670 条记录
2025-05-17 15:00:14,155 - INFO - 获取到 670 条表单数据
2025-05-17 15:00:14,169 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-17 15:00:14,180 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 15:00:14,180 - INFO - 开始处理日期: 2025-03
2025-05-17 15:00:14,180 - INFO - Request Parameters - Page 1:
2025-05-17 15:00:14,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:14,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:14,794 - INFO - Response - Page 1:
2025-05-17 15:00:14,994 - INFO - 第 1 页获取到 100 条记录
2025-05-17 15:00:14,994 - INFO - Request Parameters - Page 2:
2025-05-17 15:00:14,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:14,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:15,474 - INFO - Response - Page 2:
2025-05-17 15:00:15,674 - INFO - 第 2 页获取到 100 条记录
2025-05-17 15:00:15,674 - INFO - Request Parameters - Page 3:
2025-05-17 15:00:15,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:15,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:16,159 - INFO - Response - Page 3:
2025-05-17 15:00:16,360 - INFO - 第 3 页获取到 100 条记录
2025-05-17 15:00:16,360 - INFO - Request Parameters - Page 4:
2025-05-17 15:00:16,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:16,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:16,941 - INFO - Response - Page 4:
2025-05-17 15:00:17,141 - INFO - 第 4 页获取到 100 条记录
2025-05-17 15:00:17,141 - INFO - Request Parameters - Page 5:
2025-05-17 15:00:17,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:17,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:17,651 - INFO - Response - Page 5:
2025-05-17 15:00:17,851 - INFO - 第 5 页获取到 100 条记录
2025-05-17 15:00:17,851 - INFO - Request Parameters - Page 6:
2025-05-17 15:00:17,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:17,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:18,321 - INFO - Response - Page 6:
2025-05-17 15:00:18,521 - INFO - 第 6 页获取到 100 条记录
2025-05-17 15:00:18,521 - INFO - Request Parameters - Page 7:
2025-05-17 15:00:18,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:18,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:18,961 - INFO - Response - Page 7:
2025-05-17 15:00:19,162 - INFO - 第 7 页获取到 61 条记录
2025-05-17 15:00:19,162 - INFO - 查询完成，共获取到 661 条记录
2025-05-17 15:00:19,162 - INFO - 获取到 661 条表单数据
2025-05-17 15:00:19,177 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-17 15:00:19,192 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 15:00:19,192 - INFO - 开始处理日期: 2025-04
2025-05-17 15:00:19,192 - INFO - Request Parameters - Page 1:
2025-05-17 15:00:19,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:19,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:19,758 - INFO - Response - Page 1:
2025-05-17 15:00:19,959 - INFO - 第 1 页获取到 100 条记录
2025-05-17 15:00:19,959 - INFO - Request Parameters - Page 2:
2025-05-17 15:00:19,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:19,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:20,472 - INFO - Response - Page 2:
2025-05-17 15:00:20,672 - INFO - 第 2 页获取到 100 条记录
2025-05-17 15:00:20,672 - INFO - Request Parameters - Page 3:
2025-05-17 15:00:20,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:20,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:21,143 - INFO - Response - Page 3:
2025-05-17 15:00:21,343 - INFO - 第 3 页获取到 100 条记录
2025-05-17 15:00:21,343 - INFO - Request Parameters - Page 4:
2025-05-17 15:00:21,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:21,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:21,820 - INFO - Response - Page 4:
2025-05-17 15:00:22,020 - INFO - 第 4 页获取到 100 条记录
2025-05-17 15:00:22,020 - INFO - Request Parameters - Page 5:
2025-05-17 15:00:22,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:22,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:22,493 - INFO - Response - Page 5:
2025-05-17 15:00:22,693 - INFO - 第 5 页获取到 100 条记录
2025-05-17 15:00:22,693 - INFO - Request Parameters - Page 6:
2025-05-17 15:00:22,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:22,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:23,242 - INFO - Response - Page 6:
2025-05-17 15:00:23,442 - INFO - 第 6 页获取到 100 条记录
2025-05-17 15:00:23,442 - INFO - Request Parameters - Page 7:
2025-05-17 15:00:23,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:23,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:23,859 - INFO - Response - Page 7:
2025-05-17 15:00:24,059 - INFO - 第 7 页获取到 54 条记录
2025-05-17 15:00:24,059 - INFO - 查询完成，共获取到 654 条记录
2025-05-17 15:00:24,059 - INFO - 获取到 654 条表单数据
2025-05-17 15:00:24,074 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-17 15:00:24,085 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 15:00:24,085 - INFO - 开始处理日期: 2025-05
2025-05-17 15:00:24,086 - INFO - Request Parameters - Page 1:
2025-05-17 15:00:24,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:24,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:24,600 - INFO - Response - Page 1:
2025-05-17 15:00:24,801 - INFO - 第 1 页获取到 100 条记录
2025-05-17 15:00:24,801 - INFO - Request Parameters - Page 2:
2025-05-17 15:00:24,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:24,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:25,274 - INFO - Response - Page 2:
2025-05-17 15:00:25,474 - INFO - 第 2 页获取到 100 条记录
2025-05-17 15:00:25,474 - INFO - Request Parameters - Page 3:
2025-05-17 15:00:25,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:25,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:26,006 - INFO - Response - Page 3:
2025-05-17 15:00:26,207 - INFO - 第 3 页获取到 100 条记录
2025-05-17 15:00:26,207 - INFO - Request Parameters - Page 4:
2025-05-17 15:00:26,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:26,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:26,742 - INFO - Response - Page 4:
2025-05-17 15:00:26,942 - INFO - 第 4 页获取到 100 条记录
2025-05-17 15:00:26,942 - INFO - Request Parameters - Page 5:
2025-05-17 15:00:26,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:26,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:27,399 - INFO - Response - Page 5:
2025-05-17 15:00:27,599 - INFO - 第 5 页获取到 100 条记录
2025-05-17 15:00:27,599 - INFO - Request Parameters - Page 6:
2025-05-17 15:00:27,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:27,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:28,091 - INFO - Response - Page 6:
2025-05-17 15:00:28,292 - INFO - 第 6 页获取到 100 条记录
2025-05-17 15:00:28,292 - INFO - Request Parameters - Page 7:
2025-05-17 15:00:28,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:00:28,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:00:28,677 - INFO - Response - Page 7:
2025-05-17 15:00:28,878 - INFO - 第 7 页获取到 25 条记录
2025-05-17 15:00:28,878 - INFO - 查询完成，共获取到 625 条记录
2025-05-17 15:00:28,878 - INFO - 获取到 625 条表单数据
2025-05-17 15:00:28,890 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-17 15:00:28,892 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-17 15:00:29,457 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-17 15:00:29,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25675.4, 'new_value': 25684.98}, {'field': 'total_amount', 'old_value': 25675.4, 'new_value': 25684.98}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-17 15:00:29,467 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-17 15:00:29,467 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-17 15:00:29,469 - INFO - =================同步完成====================
2025-05-17 18:00:01,989 - INFO - =================使用默认全量同步=============
2025-05-17 18:00:03,344 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-17 18:00:03,345 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-17 18:00:03,373 - INFO - 开始处理日期: 2025-01
2025-05-17 18:00:03,376 - INFO - Request Parameters - Page 1:
2025-05-17 18:00:03,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:03,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:04,702 - INFO - Response - Page 1:
2025-05-17 18:00:04,904 - INFO - 第 1 页获取到 100 条记录
2025-05-17 18:00:04,904 - INFO - Request Parameters - Page 2:
2025-05-17 18:00:04,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:04,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:05,416 - INFO - Response - Page 2:
2025-05-17 18:00:05,616 - INFO - 第 2 页获取到 100 条记录
2025-05-17 18:00:05,616 - INFO - Request Parameters - Page 3:
2025-05-17 18:00:05,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:05,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:06,259 - INFO - Response - Page 3:
2025-05-17 18:00:06,459 - INFO - 第 3 页获取到 100 条记录
2025-05-17 18:00:06,459 - INFO - Request Parameters - Page 4:
2025-05-17 18:00:06,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:06,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:06,962 - INFO - Response - Page 4:
2025-05-17 18:00:07,163 - INFO - 第 4 页获取到 100 条记录
2025-05-17 18:00:07,163 - INFO - Request Parameters - Page 5:
2025-05-17 18:00:07,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:07,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:07,753 - INFO - Response - Page 5:
2025-05-17 18:00:07,954 - INFO - 第 5 页获取到 100 条记录
2025-05-17 18:00:07,954 - INFO - Request Parameters - Page 6:
2025-05-17 18:00:07,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:07,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:08,513 - INFO - Response - Page 6:
2025-05-17 18:00:08,713 - INFO - 第 6 页获取到 100 条记录
2025-05-17 18:00:08,713 - INFO - Request Parameters - Page 7:
2025-05-17 18:00:08,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:08,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:09,212 - INFO - Response - Page 7:
2025-05-17 18:00:09,413 - INFO - 第 7 页获取到 82 条记录
2025-05-17 18:00:09,413 - INFO - 查询完成，共获取到 682 条记录
2025-05-17 18:00:09,413 - INFO - 获取到 682 条表单数据
2025-05-17 18:00:09,426 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-17 18:00:09,439 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 18:00:09,439 - INFO - 开始处理日期: 2025-02
2025-05-17 18:00:09,440 - INFO - Request Parameters - Page 1:
2025-05-17 18:00:09,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:09,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:09,900 - INFO - Response - Page 1:
2025-05-17 18:00:10,100 - INFO - 第 1 页获取到 100 条记录
2025-05-17 18:00:10,100 - INFO - Request Parameters - Page 2:
2025-05-17 18:00:10,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:10,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:10,581 - INFO - Response - Page 2:
2025-05-17 18:00:10,781 - INFO - 第 2 页获取到 100 条记录
2025-05-17 18:00:10,781 - INFO - Request Parameters - Page 3:
2025-05-17 18:00:10,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:10,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:11,277 - INFO - Response - Page 3:
2025-05-17 18:00:11,477 - INFO - 第 3 页获取到 100 条记录
2025-05-17 18:00:11,477 - INFO - Request Parameters - Page 4:
2025-05-17 18:00:11,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:11,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:12,004 - INFO - Response - Page 4:
2025-05-17 18:00:12,205 - INFO - 第 4 页获取到 100 条记录
2025-05-17 18:00:12,205 - INFO - Request Parameters - Page 5:
2025-05-17 18:00:12,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:12,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:12,728 - INFO - Response - Page 5:
2025-05-17 18:00:12,928 - INFO - 第 5 页获取到 100 条记录
2025-05-17 18:00:12,928 - INFO - Request Parameters - Page 6:
2025-05-17 18:00:12,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:12,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:13,419 - INFO - Response - Page 6:
2025-05-17 18:00:13,619 - INFO - 第 6 页获取到 100 条记录
2025-05-17 18:00:13,619 - INFO - Request Parameters - Page 7:
2025-05-17 18:00:13,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:13,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:14,096 - INFO - Response - Page 7:
2025-05-17 18:00:14,296 - INFO - 第 7 页获取到 70 条记录
2025-05-17 18:00:14,296 - INFO - 查询完成，共获取到 670 条记录
2025-05-17 18:00:14,296 - INFO - 获取到 670 条表单数据
2025-05-17 18:00:14,309 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-17 18:00:14,320 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 18:00:14,320 - INFO - 开始处理日期: 2025-03
2025-05-17 18:00:14,321 - INFO - Request Parameters - Page 1:
2025-05-17 18:00:14,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:14,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:14,885 - INFO - Response - Page 1:
2025-05-17 18:00:15,086 - INFO - 第 1 页获取到 100 条记录
2025-05-17 18:00:15,086 - INFO - Request Parameters - Page 2:
2025-05-17 18:00:15,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:15,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:15,650 - INFO - Response - Page 2:
2025-05-17 18:00:15,850 - INFO - 第 2 页获取到 100 条记录
2025-05-17 18:00:15,850 - INFO - Request Parameters - Page 3:
2025-05-17 18:00:15,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:15,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:16,396 - INFO - Response - Page 3:
2025-05-17 18:00:16,596 - INFO - 第 3 页获取到 100 条记录
2025-05-17 18:00:16,596 - INFO - Request Parameters - Page 4:
2025-05-17 18:00:16,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:16,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:17,051 - INFO - Response - Page 4:
2025-05-17 18:00:17,251 - INFO - 第 4 页获取到 100 条记录
2025-05-17 18:00:17,251 - INFO - Request Parameters - Page 5:
2025-05-17 18:00:17,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:17,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:17,739 - INFO - Response - Page 5:
2025-05-17 18:00:17,940 - INFO - 第 5 页获取到 100 条记录
2025-05-17 18:00:17,940 - INFO - Request Parameters - Page 6:
2025-05-17 18:00:17,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:17,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:18,394 - INFO - Response - Page 6:
2025-05-17 18:00:18,594 - INFO - 第 6 页获取到 100 条记录
2025-05-17 18:00:18,594 - INFO - Request Parameters - Page 7:
2025-05-17 18:00:18,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:18,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:19,009 - INFO - Response - Page 7:
2025-05-17 18:00:19,210 - INFO - 第 7 页获取到 61 条记录
2025-05-17 18:00:19,210 - INFO - 查询完成，共获取到 661 条记录
2025-05-17 18:00:19,210 - INFO - 获取到 661 条表单数据
2025-05-17 18:00:19,223 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-17 18:00:19,234 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 18:00:19,234 - INFO - 开始处理日期: 2025-04
2025-05-17 18:00:19,234 - INFO - Request Parameters - Page 1:
2025-05-17 18:00:19,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:19,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:19,742 - INFO - Response - Page 1:
2025-05-17 18:00:19,942 - INFO - 第 1 页获取到 100 条记录
2025-05-17 18:00:19,942 - INFO - Request Parameters - Page 2:
2025-05-17 18:00:19,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:19,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:20,452 - INFO - Response - Page 2:
2025-05-17 18:00:20,652 - INFO - 第 2 页获取到 100 条记录
2025-05-17 18:00:20,652 - INFO - Request Parameters - Page 3:
2025-05-17 18:00:20,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:20,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:21,123 - INFO - Response - Page 3:
2025-05-17 18:00:21,324 - INFO - 第 3 页获取到 100 条记录
2025-05-17 18:00:21,324 - INFO - Request Parameters - Page 4:
2025-05-17 18:00:21,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:21,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:21,805 - INFO - Response - Page 4:
2025-05-17 18:00:22,005 - INFO - 第 4 页获取到 100 条记录
2025-05-17 18:00:22,005 - INFO - Request Parameters - Page 5:
2025-05-17 18:00:22,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:22,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:22,563 - INFO - Response - Page 5:
2025-05-17 18:00:22,764 - INFO - 第 5 页获取到 100 条记录
2025-05-17 18:00:22,764 - INFO - Request Parameters - Page 6:
2025-05-17 18:00:22,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:22,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:23,269 - INFO - Response - Page 6:
2025-05-17 18:00:23,469 - INFO - 第 6 页获取到 100 条记录
2025-05-17 18:00:23,469 - INFO - Request Parameters - Page 7:
2025-05-17 18:00:23,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:23,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:23,895 - INFO - Response - Page 7:
2025-05-17 18:00:24,095 - INFO - 第 7 页获取到 54 条记录
2025-05-17 18:00:24,095 - INFO - 查询完成，共获取到 654 条记录
2025-05-17 18:00:24,095 - INFO - 获取到 654 条表单数据
2025-05-17 18:00:24,108 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-17 18:00:24,120 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 18:00:24,120 - INFO - 开始处理日期: 2025-05
2025-05-17 18:00:24,120 - INFO - Request Parameters - Page 1:
2025-05-17 18:00:24,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:24,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:24,649 - INFO - Response - Page 1:
2025-05-17 18:00:24,850 - INFO - 第 1 页获取到 100 条记录
2025-05-17 18:00:24,850 - INFO - Request Parameters - Page 2:
2025-05-17 18:00:24,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:24,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:25,383 - INFO - Response - Page 2:
2025-05-17 18:00:25,584 - INFO - 第 2 页获取到 100 条记录
2025-05-17 18:00:25,584 - INFO - Request Parameters - Page 3:
2025-05-17 18:00:25,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:25,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:26,119 - INFO - Response - Page 3:
2025-05-17 18:00:26,319 - INFO - 第 3 页获取到 100 条记录
2025-05-17 18:00:26,319 - INFO - Request Parameters - Page 4:
2025-05-17 18:00:26,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:26,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:26,861 - INFO - Response - Page 4:
2025-05-17 18:00:27,062 - INFO - 第 4 页获取到 100 条记录
2025-05-17 18:00:27,062 - INFO - Request Parameters - Page 5:
2025-05-17 18:00:27,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:27,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:27,657 - INFO - Response - Page 5:
2025-05-17 18:00:27,857 - INFO - 第 5 页获取到 100 条记录
2025-05-17 18:00:27,857 - INFO - Request Parameters - Page 6:
2025-05-17 18:00:27,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:27,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:28,348 - INFO - Response - Page 6:
2025-05-17 18:00:28,548 - INFO - 第 6 页获取到 100 条记录
2025-05-17 18:00:28,548 - INFO - Request Parameters - Page 7:
2025-05-17 18:00:28,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:00:28,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:00:28,958 - INFO - Response - Page 7:
2025-05-17 18:00:29,159 - INFO - 第 7 页获取到 25 条记录
2025-05-17 18:00:29,159 - INFO - 查询完成，共获取到 625 条记录
2025-05-17 18:00:29,159 - INFO - 获取到 625 条表单数据
2025-05-17 18:00:29,172 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-17 18:00:29,173 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-17 18:00:29,605 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-17 18:00:29,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33175.26, 'new_value': 37360.26}, {'field': 'total_amount', 'old_value': 93721.73, 'new_value': 97906.73}, {'field': 'order_count', 'old_value': 5358, 'new_value': 5473}]
2025-05-17 18:00:29,606 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-17 18:00:30,035 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-17 18:00:30,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14017.0, 'new_value': 14500.0}, {'field': 'total_amount', 'old_value': 14017.0, 'new_value': 14500.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-17 18:00:30,037 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-17 18:00:30,604 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-17 18:00:30,604 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77759.87, 'new_value': 82046.1}, {'field': 'total_amount', 'old_value': 77759.87, 'new_value': 82046.1}, {'field': 'order_count', 'old_value': 497, 'new_value': 534}]
2025-05-17 18:00:30,605 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-17 18:00:31,076 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-17 18:00:31,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 862564.94, 'new_value': 864564.94}, {'field': 'total_amount', 'old_value': 862564.94, 'new_value': 864564.94}, {'field': 'order_count', 'old_value': 535, 'new_value': 536}]
2025-05-17 18:00:31,077 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-17 18:00:31,589 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-17 18:00:31,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 818000.0, 'new_value': 878000.0}, {'field': 'total_amount', 'old_value': 818000.0, 'new_value': 878000.0}, {'field': 'order_count', 'old_value': 335, 'new_value': 336}]
2025-05-17 18:00:31,589 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-17 18:00:32,085 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-17 18:00:32,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53627.0, 'new_value': 54293.0}, {'field': 'total_amount', 'old_value': 53627.0, 'new_value': 54293.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-17 18:00:32,086 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-17 18:00:32,622 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-17 18:00:32,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29970.0, 'new_value': 31770.0}, {'field': 'total_amount', 'old_value': 29970.0, 'new_value': 31770.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-17 18:00:32,622 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-17 18:00:33,031 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-17 18:00:33,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89017.08, 'new_value': 95235.08}, {'field': 'total_amount', 'old_value': 89017.08, 'new_value': 95235.08}, {'field': 'order_count', 'old_value': 8061, 'new_value': 8579}]
2025-05-17 18:00:33,032 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-17 18:00:33,426 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-17 18:00:33,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79274.3, 'new_value': 81488.2}, {'field': 'total_amount', 'old_value': 79274.3, 'new_value': 81488.2}, {'field': 'order_count', 'old_value': 817, 'new_value': 834}]
2025-05-17 18:00:33,426 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-17 18:00:33,896 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-17 18:00:33,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3991822.0, 'new_value': 4225877.0}, {'field': 'total_amount', 'old_value': 3991822.0, 'new_value': 4225877.0}, {'field': 'order_count', 'old_value': 67819, 'new_value': 71950}]
2025-05-17 18:00:33,896 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-17 18:00:34,328 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-17 18:00:34,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32819.0, 'new_value': 34791.0}, {'field': 'total_amount', 'old_value': 32819.0, 'new_value': 34791.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-17 18:00:34,328 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-17 18:00:34,841 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-17 18:00:34,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53716.74, 'new_value': 57885.74}, {'field': 'total_amount', 'old_value': 53716.74, 'new_value': 57885.74}, {'field': 'order_count', 'old_value': 1698, 'new_value': 1762}]
2025-05-17 18:00:34,842 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-17 18:00:35,252 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-17 18:00:35,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73439.0, 'new_value': 77758.0}, {'field': 'total_amount', 'old_value': 73439.0, 'new_value': 77758.0}, {'field': 'order_count', 'old_value': 2825, 'new_value': 2940}]
2025-05-17 18:00:35,253 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-17 18:00:35,681 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-17 18:00:35,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19895.07, 'new_value': 21080.07}, {'field': 'total_amount', 'old_value': 19895.07, 'new_value': 21080.07}, {'field': 'order_count', 'old_value': 1954, 'new_value': 2063}]
2025-05-17 18:00:35,682 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-17 18:00:36,195 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-17 18:00:36,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36000.0, 'new_value': 40000.0}, {'field': 'total_amount', 'old_value': 36000.0, 'new_value': 40000.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-17 18:00:36,195 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-17 18:00:36,645 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-17 18:00:36,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4830400.0, 'new_value': 4931900.0}, {'field': 'total_amount', 'old_value': 4830400.0, 'new_value': 4931900.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-17 18:00:36,647 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-17 18:00:37,049 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-17 18:00:37,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207193.0, 'new_value': 221613.0}, {'field': 'total_amount', 'old_value': 216011.99, 'new_value': 230431.99}, {'field': 'order_count', 'old_value': 40, 'new_value': 45}]
2025-05-17 18:00:37,050 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-17 18:00:37,532 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-17 18:00:37,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 274295.1, 'new_value': 281818.03}, {'field': 'total_amount', 'old_value': 274295.1, 'new_value': 281818.03}, {'field': 'order_count', 'old_value': 516, 'new_value': 541}]
2025-05-17 18:00:37,533 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-17 18:00:37,933 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-17 18:00:37,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20474.0, 'new_value': 21863.0}, {'field': 'total_amount', 'old_value': 21850.0, 'new_value': 23239.0}, {'field': 'order_count', 'old_value': 2336, 'new_value': 2465}]
2025-05-17 18:00:37,935 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-17 18:00:38,379 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-17 18:00:38,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55364.0, 'new_value': 59244.0}, {'field': 'total_amount', 'old_value': 55364.0, 'new_value': 59244.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-17 18:00:38,380 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-17 18:00:38,812 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-17 18:00:38,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252625.91, 'new_value': 264030.95}, {'field': 'total_amount', 'old_value': 252625.91, 'new_value': 264030.95}, {'field': 'order_count', 'old_value': 1724, 'new_value': 1828}]
2025-05-17 18:00:38,812 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-17 18:00:39,278 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-17 18:00:39,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71612.0, 'new_value': 73307.0}, {'field': 'total_amount', 'old_value': 86816.0, 'new_value': 88511.0}, {'field': 'order_count', 'old_value': 1949, 'new_value': 1992}]
2025-05-17 18:00:39,278 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-17 18:00:39,742 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-17 18:00:39,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67552.0, 'new_value': 72242.0}, {'field': 'total_amount', 'old_value': 67552.0, 'new_value': 72242.0}, {'field': 'order_count', 'old_value': 450, 'new_value': 479}]
2025-05-17 18:00:39,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-17 18:00:40,141 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-17 18:00:40,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17101.0, 'new_value': 17251.0}, {'field': 'total_amount', 'old_value': 17101.0, 'new_value': 17251.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-17 18:00:40,141 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-17 18:00:40,587 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-17 18:00:40,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7117.0, 'new_value': 7416.0}, {'field': 'total_amount', 'old_value': 7117.0, 'new_value': 7416.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-17 18:00:40,587 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-17 18:00:41,029 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-17 18:00:41,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5384.67, 'new_value': 5945.67}, {'field': 'total_amount', 'old_value': 5384.67, 'new_value': 5945.67}, {'field': 'order_count', 'old_value': 169, 'new_value': 186}]
2025-05-17 18:00:41,029 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-17 18:00:41,504 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-17 18:00:41,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52846.0, 'new_value': 60758.0}, {'field': 'total_amount', 'old_value': 52846.0, 'new_value': 60758.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-17 18:00:41,505 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-17 18:00:41,921 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-17 18:00:41,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210655.0, 'new_value': 224606.0}, {'field': 'total_amount', 'old_value': 224430.0, 'new_value': 238381.0}, {'field': 'order_count', 'old_value': 4653, 'new_value': 4858}]
2025-05-17 18:00:41,925 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-17 18:00:42,408 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-17 18:00:42,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101479.17, 'new_value': 109131.17}, {'field': 'total_amount', 'old_value': 101479.17, 'new_value': 109131.17}, {'field': 'order_count', 'old_value': 844, 'new_value': 900}]
2025-05-17 18:00:42,409 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-17 18:00:42,897 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-17 18:00:42,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63088.0, 'new_value': 69486.0}, {'field': 'total_amount', 'old_value': 63089.0, 'new_value': 69487.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-17 18:00:42,897 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-05-17 18:00:43,321 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-05-17 18:00:43,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4300.0, 'new_value': 5420.0}, {'field': 'total_amount', 'old_value': 4300.0, 'new_value': 5420.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-17 18:00:43,321 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-17 18:00:43,712 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-17 18:00:43,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14111.0, 'new_value': 14477.0}, {'field': 'total_amount', 'old_value': 14111.0, 'new_value': 14477.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 85}]
2025-05-17 18:00:43,713 - INFO - 日期 2025-05 处理完成 - 更新: 32 条，插入: 0 条，错误: 0 条
2025-05-17 18:00:43,713 - INFO - 数据同步完成！更新: 32 条，插入: 0 条，错误: 0 条
2025-05-17 18:00:43,715 - INFO - =================同步完成====================
2025-05-17 21:00:02,563 - INFO - =================使用默认全量同步=============
2025-05-17 21:00:03,928 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-17 21:00:03,929 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-17 21:00:03,957 - INFO - 开始处理日期: 2025-01
2025-05-17 21:00:03,959 - INFO - Request Parameters - Page 1:
2025-05-17 21:00:03,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:03,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:04,887 - INFO - Response - Page 1:
2025-05-17 21:00:05,087 - INFO - 第 1 页获取到 100 条记录
2025-05-17 21:00:05,087 - INFO - Request Parameters - Page 2:
2025-05-17 21:00:05,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:05,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:06,087 - INFO - Response - Page 2:
2025-05-17 21:00:06,287 - INFO - 第 2 页获取到 100 条记录
2025-05-17 21:00:06,287 - INFO - Request Parameters - Page 3:
2025-05-17 21:00:06,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:06,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:06,786 - INFO - Response - Page 3:
2025-05-17 21:00:06,986 - INFO - 第 3 页获取到 100 条记录
2025-05-17 21:00:06,986 - INFO - Request Parameters - Page 4:
2025-05-17 21:00:06,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:06,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:07,631 - INFO - Response - Page 4:
2025-05-17 21:00:07,832 - INFO - 第 4 页获取到 100 条记录
2025-05-17 21:00:07,832 - INFO - Request Parameters - Page 5:
2025-05-17 21:00:07,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:07,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:08,575 - INFO - Response - Page 5:
2025-05-17 21:00:08,775 - INFO - 第 5 页获取到 100 条记录
2025-05-17 21:00:08,775 - INFO - Request Parameters - Page 6:
2025-05-17 21:00:08,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:08,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:09,315 - INFO - Response - Page 6:
2025-05-17 21:00:09,515 - INFO - 第 6 页获取到 100 条记录
2025-05-17 21:00:09,515 - INFO - Request Parameters - Page 7:
2025-05-17 21:00:09,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:09,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:09,939 - INFO - Response - Page 7:
2025-05-17 21:00:10,141 - INFO - 第 7 页获取到 82 条记录
2025-05-17 21:00:10,141 - INFO - 查询完成，共获取到 682 条记录
2025-05-17 21:00:10,141 - INFO - 获取到 682 条表单数据
2025-05-17 21:00:10,155 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-17 21:00:10,168 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 21:00:10,169 - INFO - 开始处理日期: 2025-02
2025-05-17 21:00:10,169 - INFO - Request Parameters - Page 1:
2025-05-17 21:00:10,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:10,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:10,680 - INFO - Response - Page 1:
2025-05-17 21:00:10,880 - INFO - 第 1 页获取到 100 条记录
2025-05-17 21:00:10,880 - INFO - Request Parameters - Page 2:
2025-05-17 21:00:10,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:10,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:11,368 - INFO - Response - Page 2:
2025-05-17 21:00:11,569 - INFO - 第 2 页获取到 100 条记录
2025-05-17 21:00:11,569 - INFO - Request Parameters - Page 3:
2025-05-17 21:00:11,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:11,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:12,057 - INFO - Response - Page 3:
2025-05-17 21:00:12,257 - INFO - 第 3 页获取到 100 条记录
2025-05-17 21:00:12,257 - INFO - Request Parameters - Page 4:
2025-05-17 21:00:12,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:12,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:12,746 - INFO - Response - Page 4:
2025-05-17 21:00:12,947 - INFO - 第 4 页获取到 100 条记录
2025-05-17 21:00:12,947 - INFO - Request Parameters - Page 5:
2025-05-17 21:00:12,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:12,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:13,497 - INFO - Response - Page 5:
2025-05-17 21:00:13,697 - INFO - 第 5 页获取到 100 条记录
2025-05-17 21:00:13,697 - INFO - Request Parameters - Page 6:
2025-05-17 21:00:13,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:13,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:14,220 - INFO - Response - Page 6:
2025-05-17 21:00:14,421 - INFO - 第 6 页获取到 100 条记录
2025-05-17 21:00:14,421 - INFO - Request Parameters - Page 7:
2025-05-17 21:00:14,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:14,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:14,870 - INFO - Response - Page 7:
2025-05-17 21:00:15,070 - INFO - 第 7 页获取到 70 条记录
2025-05-17 21:00:15,070 - INFO - 查询完成，共获取到 670 条记录
2025-05-17 21:00:15,070 - INFO - 获取到 670 条表单数据
2025-05-17 21:00:15,083 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-17 21:00:15,095 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 21:00:15,095 - INFO - 开始处理日期: 2025-03
2025-05-17 21:00:15,096 - INFO - Request Parameters - Page 1:
2025-05-17 21:00:15,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:15,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:15,684 - INFO - Response - Page 1:
2025-05-17 21:00:15,884 - INFO - 第 1 页获取到 100 条记录
2025-05-17 21:00:15,884 - INFO - Request Parameters - Page 2:
2025-05-17 21:00:15,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:15,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:16,382 - INFO - Response - Page 2:
2025-05-17 21:00:16,582 - INFO - 第 2 页获取到 100 条记录
2025-05-17 21:00:16,582 - INFO - Request Parameters - Page 3:
2025-05-17 21:00:16,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:16,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:17,156 - INFO - Response - Page 3:
2025-05-17 21:00:17,357 - INFO - 第 3 页获取到 100 条记录
2025-05-17 21:00:17,357 - INFO - Request Parameters - Page 4:
2025-05-17 21:00:17,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:17,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:17,889 - INFO - Response - Page 4:
2025-05-17 21:00:18,089 - INFO - 第 4 页获取到 100 条记录
2025-05-17 21:00:18,089 - INFO - Request Parameters - Page 5:
2025-05-17 21:00:18,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:18,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:18,667 - INFO - Response - Page 5:
2025-05-17 21:00:18,867 - INFO - 第 5 页获取到 100 条记录
2025-05-17 21:00:18,867 - INFO - Request Parameters - Page 6:
2025-05-17 21:00:18,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:18,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:19,409 - INFO - Response - Page 6:
2025-05-17 21:00:19,610 - INFO - 第 6 页获取到 100 条记录
2025-05-17 21:00:19,610 - INFO - Request Parameters - Page 7:
2025-05-17 21:00:19,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:19,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:19,986 - INFO - Response - Page 7:
2025-05-17 21:00:20,186 - INFO - 第 7 页获取到 61 条记录
2025-05-17 21:00:20,186 - INFO - 查询完成，共获取到 661 条记录
2025-05-17 21:00:20,186 - INFO - 获取到 661 条表单数据
2025-05-17 21:00:20,199 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-17 21:00:20,210 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 21:00:20,211 - INFO - 开始处理日期: 2025-04
2025-05-17 21:00:20,211 - INFO - Request Parameters - Page 1:
2025-05-17 21:00:20,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:20,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:20,713 - INFO - Response - Page 1:
2025-05-17 21:00:20,913 - INFO - 第 1 页获取到 100 条记录
2025-05-17 21:00:20,913 - INFO - Request Parameters - Page 2:
2025-05-17 21:00:20,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:20,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:21,382 - INFO - Response - Page 2:
2025-05-17 21:00:21,582 - INFO - 第 2 页获取到 100 条记录
2025-05-17 21:00:21,582 - INFO - Request Parameters - Page 3:
2025-05-17 21:00:21,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:21,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:22,085 - INFO - Response - Page 3:
2025-05-17 21:00:22,286 - INFO - 第 3 页获取到 100 条记录
2025-05-17 21:00:22,286 - INFO - Request Parameters - Page 4:
2025-05-17 21:00:22,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:22,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:22,747 - INFO - Response - Page 4:
2025-05-17 21:00:22,949 - INFO - 第 4 页获取到 100 条记录
2025-05-17 21:00:22,949 - INFO - Request Parameters - Page 5:
2025-05-17 21:00:22,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:22,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:23,485 - INFO - Response - Page 5:
2025-05-17 21:00:23,686 - INFO - 第 5 页获取到 100 条记录
2025-05-17 21:00:23,686 - INFO - Request Parameters - Page 6:
2025-05-17 21:00:23,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:23,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:24,253 - INFO - Response - Page 6:
2025-05-17 21:00:24,453 - INFO - 第 6 页获取到 100 条记录
2025-05-17 21:00:24,453 - INFO - Request Parameters - Page 7:
2025-05-17 21:00:24,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:24,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:24,842 - INFO - Response - Page 7:
2025-05-17 21:00:25,043 - INFO - 第 7 页获取到 54 条记录
2025-05-17 21:00:25,043 - INFO - 查询完成，共获取到 654 条记录
2025-05-17 21:00:25,044 - INFO - 获取到 654 条表单数据
2025-05-17 21:00:25,056 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-17 21:00:25,068 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 21:00:25,068 - INFO - 开始处理日期: 2025-05
2025-05-17 21:00:25,069 - INFO - Request Parameters - Page 1:
2025-05-17 21:00:25,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:25,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:25,867 - INFO - Response - Page 1:
2025-05-17 21:00:26,067 - INFO - 第 1 页获取到 100 条记录
2025-05-17 21:00:26,067 - INFO - Request Parameters - Page 2:
2025-05-17 21:00:26,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:26,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:26,791 - INFO - Response - Page 2:
2025-05-17 21:00:26,991 - INFO - 第 2 页获取到 100 条记录
2025-05-17 21:00:26,991 - INFO - Request Parameters - Page 3:
2025-05-17 21:00:26,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:26,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:27,491 - INFO - Response - Page 3:
2025-05-17 21:00:27,692 - INFO - 第 3 页获取到 100 条记录
2025-05-17 21:00:27,692 - INFO - Request Parameters - Page 4:
2025-05-17 21:00:27,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:27,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:28,213 - INFO - Response - Page 4:
2025-05-17 21:00:28,413 - INFO - 第 4 页获取到 100 条记录
2025-05-17 21:00:28,413 - INFO - Request Parameters - Page 5:
2025-05-17 21:00:28,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:28,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:28,929 - INFO - Response - Page 5:
2025-05-17 21:00:29,129 - INFO - 第 5 页获取到 100 条记录
2025-05-17 21:00:29,129 - INFO - Request Parameters - Page 6:
2025-05-17 21:00:29,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:29,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:29,663 - INFO - Response - Page 6:
2025-05-17 21:00:29,863 - INFO - 第 6 页获取到 100 条记录
2025-05-17 21:00:29,863 - INFO - Request Parameters - Page 7:
2025-05-17 21:00:29,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:00:29,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:00:30,175 - INFO - Response - Page 7:
2025-05-17 21:00:30,376 - INFO - 第 7 页获取到 25 条记录
2025-05-17 21:00:30,376 - INFO - 查询完成，共获取到 625 条记录
2025-05-17 21:00:30,376 - INFO - 获取到 625 条表单数据
2025-05-17 21:00:30,387 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-17 21:00:30,389 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-17 21:00:30,841 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-17 21:00:30,841 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52151.0, 'new_value': 55362.0}, {'field': 'offline_amount', 'old_value': 67348.0, 'new_value': 72335.0}, {'field': 'total_amount', 'old_value': 119499.0, 'new_value': 127697.0}, {'field': 'order_count', 'old_value': 2879, 'new_value': 3051}]
2025-05-17 21:00:30,850 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-17 21:00:31,222 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-17 21:00:31,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 694826.0, 'new_value': 725113.0}, {'field': 'total_amount', 'old_value': 694826.0, 'new_value': 725113.0}, {'field': 'order_count', 'old_value': 2986, 'new_value': 3121}]
2025-05-17 21:00:31,223 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-17 21:00:31,661 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-17 21:00:31,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8979198.0, 'new_value': 9254598.0}, {'field': 'total_amount', 'old_value': 8979198.0, 'new_value': 9254598.0}, {'field': 'order_count', 'old_value': 27025, 'new_value': 27976}]
2025-05-17 21:00:31,661 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-17 21:00:32,147 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-17 21:00:32,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2376797.48, 'new_value': 2495066.65}, {'field': 'total_amount', 'old_value': 2376797.48, 'new_value': 2495066.65}, {'field': 'order_count', 'old_value': 4005, 'new_value': 4230}]
2025-05-17 21:00:32,149 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-17 21:00:32,566 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-17 21:00:32,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107585.11, 'new_value': 116527.04}, {'field': 'total_amount', 'old_value': 107585.11, 'new_value': 116527.04}, {'field': 'order_count', 'old_value': 11290, 'new_value': 12054}]
2025-05-17 21:00:32,566 - INFO - 日期 2025-05 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-05-17 21:00:32,566 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-05-17 21:00:32,568 - INFO - =================同步完成====================
