2025-05-12 00:00:03,751 - INFO - =================使用默认全量同步=============
2025-05-12 00:00:05,102 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-12 00:00:05,103 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-12 00:00:05,130 - INFO - 开始处理日期: 2025-01
2025-05-12 00:00:05,133 - INFO - Request Parameters - Page 1:
2025-05-12 00:00:05,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:05,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:06,134 - INFO - Response - Page 1:
2025-05-12 00:00:06,335 - INFO - 第 1 页获取到 100 条记录
2025-05-12 00:00:06,335 - INFO - Request Parameters - Page 2:
2025-05-12 00:00:06,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:06,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:06,859 - INFO - Response - Page 2:
2025-05-12 00:00:07,059 - INFO - 第 2 页获取到 100 条记录
2025-05-12 00:00:07,059 - INFO - Request Parameters - Page 3:
2025-05-12 00:00:07,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:07,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:07,504 - INFO - Response - Page 3:
2025-05-12 00:00:07,704 - INFO - 第 3 页获取到 100 条记录
2025-05-12 00:00:07,704 - INFO - Request Parameters - Page 4:
2025-05-12 00:00:07,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:07,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:08,246 - INFO - Response - Page 4:
2025-05-12 00:00:08,446 - INFO - 第 4 页获取到 100 条记录
2025-05-12 00:00:08,446 - INFO - Request Parameters - Page 5:
2025-05-12 00:00:08,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:08,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:08,918 - INFO - Response - Page 5:
2025-05-12 00:00:09,118 - INFO - 第 5 页获取到 100 条记录
2025-05-12 00:00:09,118 - INFO - Request Parameters - Page 6:
2025-05-12 00:00:09,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:09,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:09,798 - INFO - Response - Page 6:
2025-05-12 00:00:09,998 - INFO - 第 6 页获取到 100 条记录
2025-05-12 00:00:09,998 - INFO - Request Parameters - Page 7:
2025-05-12 00:00:09,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:09,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:10,480 - INFO - Response - Page 7:
2025-05-12 00:00:10,680 - INFO - 第 7 页获取到 82 条记录
2025-05-12 00:00:10,680 - INFO - 查询完成，共获取到 682 条记录
2025-05-12 00:00:10,680 - INFO - 获取到 682 条表单数据
2025-05-12 00:00:10,694 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-12 00:00:10,708 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 00:00:10,708 - INFO - 开始处理日期: 2025-02
2025-05-12 00:00:10,708 - INFO - Request Parameters - Page 1:
2025-05-12 00:00:10,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:10,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:11,156 - INFO - Response - Page 1:
2025-05-12 00:00:11,357 - INFO - 第 1 页获取到 100 条记录
2025-05-12 00:00:11,357 - INFO - Request Parameters - Page 2:
2025-05-12 00:00:11,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:11,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:11,817 - INFO - Response - Page 2:
2025-05-12 00:00:12,018 - INFO - 第 2 页获取到 100 条记录
2025-05-12 00:00:12,018 - INFO - Request Parameters - Page 3:
2025-05-12 00:00:12,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:12,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:12,447 - INFO - Response - Page 3:
2025-05-12 00:00:12,648 - INFO - 第 3 页获取到 100 条记录
2025-05-12 00:00:12,648 - INFO - Request Parameters - Page 4:
2025-05-12 00:00:12,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:12,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:13,115 - INFO - Response - Page 4:
2025-05-12 00:00:13,316 - INFO - 第 4 页获取到 100 条记录
2025-05-12 00:00:13,316 - INFO - Request Parameters - Page 5:
2025-05-12 00:00:13,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:13,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:13,749 - INFO - Response - Page 5:
2025-05-12 00:00:13,950 - INFO - 第 5 页获取到 100 条记录
2025-05-12 00:00:13,950 - INFO - Request Parameters - Page 6:
2025-05-12 00:00:13,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:13,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:14,444 - INFO - Response - Page 6:
2025-05-12 00:00:14,645 - INFO - 第 6 页获取到 100 条记录
2025-05-12 00:00:14,645 - INFO - Request Parameters - Page 7:
2025-05-12 00:00:14,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:14,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:15,056 - INFO - Response - Page 7:
2025-05-12 00:00:15,256 - INFO - 第 7 页获取到 70 条记录
2025-05-12 00:00:15,256 - INFO - 查询完成，共获取到 670 条记录
2025-05-12 00:00:15,257 - INFO - 获取到 670 条表单数据
2025-05-12 00:00:15,270 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-12 00:00:15,285 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 00:00:15,285 - INFO - 开始处理日期: 2025-03
2025-05-12 00:00:15,285 - INFO - Request Parameters - Page 1:
2025-05-12 00:00:15,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:15,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:15,771 - INFO - Response - Page 1:
2025-05-12 00:00:15,972 - INFO - 第 1 页获取到 100 条记录
2025-05-12 00:00:15,972 - INFO - Request Parameters - Page 2:
2025-05-12 00:00:15,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:15,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:16,614 - INFO - Response - Page 2:
2025-05-12 00:00:16,814 - INFO - 第 2 页获取到 100 条记录
2025-05-12 00:00:16,814 - INFO - Request Parameters - Page 3:
2025-05-12 00:00:16,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:16,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:17,258 - INFO - Response - Page 3:
2025-05-12 00:00:17,458 - INFO - 第 3 页获取到 100 条记录
2025-05-12 00:00:17,458 - INFO - Request Parameters - Page 4:
2025-05-12 00:00:17,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:17,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:17,935 - INFO - Response - Page 4:
2025-05-12 00:00:18,136 - INFO - 第 4 页获取到 100 条记录
2025-05-12 00:00:18,136 - INFO - Request Parameters - Page 5:
2025-05-12 00:00:18,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:18,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:18,618 - INFO - Response - Page 5:
2025-05-12 00:00:18,819 - INFO - 第 5 页获取到 100 条记录
2025-05-12 00:00:18,819 - INFO - Request Parameters - Page 6:
2025-05-12 00:00:18,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:18,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:19,278 - INFO - Response - Page 6:
2025-05-12 00:00:19,479 - INFO - 第 6 页获取到 100 条记录
2025-05-12 00:00:19,479 - INFO - Request Parameters - Page 7:
2025-05-12 00:00:19,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:19,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:19,931 - INFO - Response - Page 7:
2025-05-12 00:00:20,132 - INFO - 第 7 页获取到 61 条记录
2025-05-12 00:00:20,132 - INFO - 查询完成，共获取到 661 条记录
2025-05-12 00:00:20,132 - INFO - 获取到 661 条表单数据
2025-05-12 00:00:20,144 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-12 00:00:20,156 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 00:00:20,157 - INFO - 开始处理日期: 2025-04
2025-05-12 00:00:20,157 - INFO - Request Parameters - Page 1:
2025-05-12 00:00:20,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:20,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:20,875 - INFO - Response - Page 1:
2025-05-12 00:00:21,076 - INFO - 第 1 页获取到 100 条记录
2025-05-12 00:00:21,076 - INFO - Request Parameters - Page 2:
2025-05-12 00:00:21,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:21,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:21,524 - INFO - Response - Page 2:
2025-05-12 00:00:21,725 - INFO - 第 2 页获取到 100 条记录
2025-05-12 00:00:21,725 - INFO - Request Parameters - Page 3:
2025-05-12 00:00:21,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:21,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:22,185 - INFO - Response - Page 3:
2025-05-12 00:00:22,386 - INFO - 第 3 页获取到 100 条记录
2025-05-12 00:00:22,386 - INFO - Request Parameters - Page 4:
2025-05-12 00:00:22,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:22,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:22,843 - INFO - Response - Page 4:
2025-05-12 00:00:23,043 - INFO - 第 4 页获取到 100 条记录
2025-05-12 00:00:23,043 - INFO - Request Parameters - Page 5:
2025-05-12 00:00:23,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:23,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:23,473 - INFO - Response - Page 5:
2025-05-12 00:00:23,673 - INFO - 第 5 页获取到 100 条记录
2025-05-12 00:00:23,673 - INFO - Request Parameters - Page 6:
2025-05-12 00:00:23,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:23,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:24,228 - INFO - Response - Page 6:
2025-05-12 00:00:24,430 - INFO - 第 6 页获取到 100 条记录
2025-05-12 00:00:24,430 - INFO - Request Parameters - Page 7:
2025-05-12 00:00:24,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:24,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:24,848 - INFO - Response - Page 7:
2025-05-12 00:00:25,048 - INFO - 第 7 页获取到 54 条记录
2025-05-12 00:00:25,048 - INFO - 查询完成，共获取到 654 条记录
2025-05-12 00:00:25,049 - INFO - 获取到 654 条表单数据
2025-05-12 00:00:25,067 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-12 00:00:25,079 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 00:00:25,079 - INFO - 开始处理日期: 2025-05
2025-05-12 00:00:25,080 - INFO - Request Parameters - Page 1:
2025-05-12 00:00:25,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:25,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:25,510 - INFO - Response - Page 1:
2025-05-12 00:00:25,710 - INFO - 第 1 页获取到 100 条记录
2025-05-12 00:00:25,710 - INFO - Request Parameters - Page 2:
2025-05-12 00:00:25,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:25,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:26,135 - INFO - Response - Page 2:
2025-05-12 00:00:26,336 - INFO - 第 2 页获取到 100 条记录
2025-05-12 00:00:26,336 - INFO - Request Parameters - Page 3:
2025-05-12 00:00:26,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:26,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:26,817 - INFO - Response - Page 3:
2025-05-12 00:00:27,018 - INFO - 第 3 页获取到 100 条记录
2025-05-12 00:00:27,018 - INFO - Request Parameters - Page 4:
2025-05-12 00:00:27,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:27,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:27,439 - INFO - Response - Page 4:
2025-05-12 00:00:27,640 - INFO - 第 4 页获取到 100 条记录
2025-05-12 00:00:27,640 - INFO - Request Parameters - Page 5:
2025-05-12 00:00:27,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:27,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:28,159 - INFO - Response - Page 5:
2025-05-12 00:00:28,359 - INFO - 第 5 页获取到 100 条记录
2025-05-12 00:00:28,359 - INFO - Request Parameters - Page 6:
2025-05-12 00:00:28,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:28,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:28,967 - INFO - Response - Page 6:
2025-05-12 00:00:29,167 - INFO - 第 6 页获取到 100 条记录
2025-05-12 00:00:29,167 - INFO - Request Parameters - Page 7:
2025-05-12 00:00:29,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 00:00:29,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 00:00:29,487 - INFO - Response - Page 7:
2025-05-12 00:00:29,687 - INFO - 第 7 页获取到 24 条记录
2025-05-12 00:00:29,687 - INFO - 查询完成，共获取到 624 条记录
2025-05-12 00:00:29,687 - INFO - 获取到 624 条表单数据
2025-05-12 00:00:29,699 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-12 00:00:29,700 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-12 00:00:30,183 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-12 00:00:30,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4600000.0, 'new_value': 5000000.0}, {'field': 'total_amount', 'old_value': 4700000.0, 'new_value': 5100000.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-12 00:00:30,184 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-12 00:00:30,576 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-12 00:00:30,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55542.0, 'new_value': 59133.0}, {'field': 'offline_amount', 'old_value': 45234.28, 'new_value': 52322.28}, {'field': 'total_amount', 'old_value': 100776.28, 'new_value': 111455.28}, {'field': 'order_count', 'old_value': 2135, 'new_value': 2341}]
2025-05-12 00:00:30,577 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-12 00:00:31,013 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-12 00:00:31,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21471.1, 'new_value': 25839.1}, {'field': 'total_amount', 'old_value': 25431.1, 'new_value': 29799.1}, {'field': 'order_count', 'old_value': 161, 'new_value': 185}]
2025-05-12 00:00:31,014 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-12 00:00:31,489 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-12 00:00:31,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7140.2, 'new_value': 9283.2}, {'field': 'offline_amount', 'old_value': 42110.66, 'new_value': 52348.66}, {'field': 'total_amount', 'old_value': 49250.86, 'new_value': 61631.86}, {'field': 'order_count', 'old_value': 104, 'new_value': 122}]
2025-05-12 00:00:31,490 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-12 00:00:31,868 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-12 00:00:31,869 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6535.0, 'new_value': 8025.0}, {'field': 'total_amount', 'old_value': 6535.0, 'new_value': 8025.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 21}]
2025-05-12 00:00:31,869 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-12 00:00:32,264 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-12 00:00:32,264 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20140.73, 'new_value': 22009.61}, {'field': 'offline_amount', 'old_value': 269224.42, 'new_value': 310797.42}, {'field': 'total_amount', 'old_value': 289365.15, 'new_value': 332807.03}, {'field': 'order_count', 'old_value': 1202, 'new_value': 1373}]
2025-05-12 00:00:32,265 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-12 00:00:32,720 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-12 00:00:32,721 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3840.32, 'new_value': 4128.32}, {'field': 'offline_amount', 'old_value': 31833.0, 'new_value': 35220.0}, {'field': 'total_amount', 'old_value': 35673.32, 'new_value': 39348.32}, {'field': 'order_count', 'old_value': 423, 'new_value': 428}]
2025-05-12 00:00:32,721 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-12 00:00:33,155 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-12 00:00:33,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21645.34, 'new_value': 23640.71}, {'field': 'offline_amount', 'old_value': 47929.87, 'new_value': 55043.9}, {'field': 'total_amount', 'old_value': 69575.21, 'new_value': 78684.61}, {'field': 'order_count', 'old_value': 2313, 'new_value': 2588}]
2025-05-12 00:00:33,155 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-12 00:00:33,564 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-12 00:00:33,564 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182885.0, 'new_value': 197957.0}, {'field': 'total_amount', 'old_value': 182885.0, 'new_value': 197957.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 89}]
2025-05-12 00:00:33,565 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-12 00:00:34,035 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-12 00:00:34,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14330.7, 'new_value': 16143.7}, {'field': 'total_amount', 'old_value': 14330.7, 'new_value': 16143.7}, {'field': 'order_count', 'old_value': 73, 'new_value': 88}]
2025-05-12 00:00:34,035 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-12 00:00:34,444 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-12 00:00:34,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16497.45, 'new_value': 17407.15}, {'field': 'total_amount', 'old_value': 16563.0, 'new_value': 17472.7}, {'field': 'order_count', 'old_value': 157, 'new_value': 171}]
2025-05-12 00:00:34,445 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-12 00:00:34,877 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-12 00:00:34,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65439.0, 'new_value': 86760.0}, {'field': 'total_amount', 'old_value': 73014.8, 'new_value': 94335.8}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-12 00:00:34,877 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-12 00:00:35,287 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-12 00:00:35,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21741.17, 'new_value': 20593.83}, {'field': 'offline_amount', 'old_value': 37020.23, 'new_value': 49869.71}, {'field': 'total_amount', 'old_value': 58761.4, 'new_value': 70463.54}, {'field': 'order_count', 'old_value': 2128, 'new_value': 2515}]
2025-05-12 00:00:35,288 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-12 00:00:35,643 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-12 00:00:35,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60608.0, 'new_value': 66307.0}, {'field': 'total_amount', 'old_value': 84230.48, 'new_value': 89929.48}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-12 00:00:35,644 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-12 00:00:36,090 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-12 00:00:36,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 348.0}, {'field': 'offline_amount', 'old_value': 30394.0, 'new_value': 33925.0}, {'field': 'total_amount', 'old_value': 30394.0, 'new_value': 34273.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 70}]
2025-05-12 00:00:36,091 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-12 00:00:36,448 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-12 00:00:36,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 393154.0, 'new_value': 432589.0}, {'field': 'total_amount', 'old_value': 393154.0, 'new_value': 432589.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 68}]
2025-05-12 00:00:36,448 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-12 00:00:36,830 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-12 00:00:36,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91988.89, 'new_value': 112899.19}, {'field': 'offline_amount', 'old_value': 64902.61, 'new_value': 70113.23}, {'field': 'total_amount', 'old_value': 156891.5, 'new_value': 183012.42}, {'field': 'order_count', 'old_value': 590, 'new_value': 691}]
2025-05-12 00:00:36,831 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-12 00:00:37,215 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-12 00:00:37,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47319.02, 'new_value': 53869.03}, {'field': 'total_amount', 'old_value': 47319.02, 'new_value': 53869.03}, {'field': 'order_count', 'old_value': 1812, 'new_value': 2060}]
2025-05-12 00:00:37,215 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-12 00:00:37,662 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-12 00:00:37,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9577.08, 'new_value': 11319.86}, {'field': 'offline_amount', 'old_value': 122587.22, 'new_value': 151384.7}, {'field': 'total_amount', 'old_value': 132164.3, 'new_value': 162704.56}, {'field': 'order_count', 'old_value': 627, 'new_value': 759}]
2025-05-12 00:00:37,662 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-12 00:00:38,055 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-12 00:00:38,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14418.14, 'new_value': 16188.14}, {'field': 'offline_amount', 'old_value': 257365.85, 'new_value': 295624.05}, {'field': 'total_amount', 'old_value': 271783.99, 'new_value': 311812.19}, {'field': 'order_count', 'old_value': 1622, 'new_value': 1773}]
2025-05-12 00:00:38,055 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-12 00:00:38,426 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-12 00:00:38,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5583.5, 'new_value': 6540.1}, {'field': 'total_amount', 'old_value': 11725.54, 'new_value': 12682.14}, {'field': 'order_count', 'old_value': 50, 'new_value': 56}]
2025-05-12 00:00:38,427 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-12 00:00:38,884 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-12 00:00:38,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33001.0, 'new_value': 33595.0}, {'field': 'total_amount', 'old_value': 33001.0, 'new_value': 33595.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-12 00:00:38,885 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-12 00:00:39,314 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-12 00:00:39,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51612.6, 'new_value': 57935.2}, {'field': 'total_amount', 'old_value': 51612.6, 'new_value': 57935.2}, {'field': 'order_count', 'old_value': 151, 'new_value': 170}]
2025-05-12 00:00:39,314 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-12 00:00:39,772 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-12 00:00:39,772 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63172.94, 'new_value': 70726.17}, {'field': 'offline_amount', 'old_value': 174250.05, 'new_value': 196864.64}, {'field': 'total_amount', 'old_value': 237422.99, 'new_value': 267590.81}, {'field': 'order_count', 'old_value': 5858, 'new_value': 6594}]
2025-05-12 00:00:39,773 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-12 00:00:40,222 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-12 00:00:40,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24120.0, 'new_value': 28727.0}, {'field': 'total_amount', 'old_value': 24120.0, 'new_value': 28727.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 69}]
2025-05-12 00:00:40,222 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-12 00:00:40,644 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-12 00:00:40,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19107.0, 'new_value': 22409.0}, {'field': 'total_amount', 'old_value': 19107.0, 'new_value': 22409.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 66}]
2025-05-12 00:00:40,645 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-12 00:00:41,022 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-12 00:00:41,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163255.0, 'new_value': 186003.0}, {'field': 'total_amount', 'old_value': 163255.0, 'new_value': 186003.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 153}]
2025-05-12 00:00:41,022 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-12 00:00:41,436 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-12 00:00:41,436 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3082.0, 'new_value': 4033.0}, {'field': 'total_amount', 'old_value': 3082.0, 'new_value': 4033.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 17}]
2025-05-12 00:00:41,436 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-12 00:00:41,783 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-12 00:00:41,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161759.0, 'new_value': 178214.1}, {'field': 'total_amount', 'old_value': 161759.0, 'new_value': 178214.1}, {'field': 'order_count', 'old_value': 1813, 'new_value': 2027}]
2025-05-12 00:00:41,783 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-12 00:00:42,142 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-12 00:00:42,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 800232.49, 'new_value': 909521.63}, {'field': 'total_amount', 'old_value': 800232.49, 'new_value': 909521.63}, {'field': 'order_count', 'old_value': 5997, 'new_value': 6724}]
2025-05-12 00:00:42,143 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-12 00:00:42,533 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-12 00:00:42,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131662.2, 'new_value': 147266.3}, {'field': 'total_amount', 'old_value': 131662.2, 'new_value': 147266.3}, {'field': 'order_count', 'old_value': 220, 'new_value': 236}]
2025-05-12 00:00:42,533 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-12 00:00:42,998 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-12 00:00:42,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249010.87, 'new_value': 291311.84}, {'field': 'total_amount', 'old_value': 284911.59, 'new_value': 327212.56}, {'field': 'order_count', 'old_value': 1103, 'new_value': 1235}]
2025-05-12 00:00:42,998 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-12 00:00:43,479 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-12 00:00:43,479 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7983.0, 'new_value': 19733.0}, {'field': 'total_amount', 'old_value': 7983.0, 'new_value': 19733.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-12 00:00:43,479 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-12 00:00:43,972 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-12 00:00:43,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56181.0, 'new_value': 120701.0}, {'field': 'total_amount', 'old_value': 56181.0, 'new_value': 120701.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 78}]
2025-05-12 00:00:43,972 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-12 00:00:44,351 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-12 00:00:44,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2329.0, 'new_value': 4829.0}, {'field': 'total_amount', 'old_value': 2329.0, 'new_value': 4829.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-12 00:00:44,352 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-12 00:00:44,751 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-12 00:00:44,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53127.85, 'new_value': 59593.17}, {'field': 'total_amount', 'old_value': 56649.37, 'new_value': 63114.69}, {'field': 'order_count', 'old_value': 5011, 'new_value': 5395}]
2025-05-12 00:00:44,751 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-12 00:00:45,196 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-12 00:00:45,197 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3847.25, 'new_value': 4095.35}, {'field': 'offline_amount', 'old_value': 37449.83, 'new_value': 44281.26}, {'field': 'total_amount', 'old_value': 41297.08, 'new_value': 48376.61}, {'field': 'order_count', 'old_value': 1276, 'new_value': 1469}]
2025-05-12 00:00:45,197 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-12 00:00:45,539 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-12 00:00:45,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3118.89, 'new_value': 3422.93}, {'field': 'offline_amount', 'old_value': 6466.84, 'new_value': 7235.94}, {'field': 'total_amount', 'old_value': 9585.73, 'new_value': 10658.87}, {'field': 'order_count', 'old_value': 703, 'new_value': 786}]
2025-05-12 00:00:45,540 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-12 00:00:45,964 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-12 00:00:45,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46009.0, 'new_value': 48329.0}, {'field': 'total_amount', 'old_value': 46009.0, 'new_value': 48329.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 59}]
2025-05-12 00:00:45,965 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-12 00:00:46,318 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-12 00:00:46,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15902.0, 'new_value': 21692.0}, {'field': 'total_amount', 'old_value': 15902.0, 'new_value': 21692.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-12 00:00:46,318 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-12 00:00:46,784 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-12 00:00:46,784 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38798.43, 'new_value': 42567.68}, {'field': 'offline_amount', 'old_value': 21678.3, 'new_value': 23618.26}, {'field': 'total_amount', 'old_value': 60476.73, 'new_value': 66185.94}, {'field': 'order_count', 'old_value': 3933, 'new_value': 4272}]
2025-05-12 00:00:46,785 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-12 00:00:47,167 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-12 00:00:47,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181772.5, 'new_value': 218156.98}, {'field': 'total_amount', 'old_value': 181772.5, 'new_value': 218156.98}, {'field': 'order_count', 'old_value': 835, 'new_value': 999}]
2025-05-12 00:00:47,167 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-12 00:00:47,834 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-12 00:00:47,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50763.7, 'new_value': 59895.7}, {'field': 'total_amount', 'old_value': 50763.7, 'new_value': 59895.7}, {'field': 'order_count', 'old_value': 436, 'new_value': 507}]
2025-05-12 00:00:47,834 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-12 00:00:48,206 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-12 00:00:48,206 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41896.42, 'new_value': 45509.69}, {'field': 'offline_amount', 'old_value': 114376.34, 'new_value': 129641.73}, {'field': 'total_amount', 'old_value': 156272.76, 'new_value': 175151.42}, {'field': 'order_count', 'old_value': 4941, 'new_value': 5476}]
2025-05-12 00:00:48,207 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-12 00:00:48,633 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-12 00:00:48,633 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130425.27, 'new_value': 147295.0}, {'field': 'offline_amount', 'old_value': 8513.35, 'new_value': 9563.35}, {'field': 'total_amount', 'old_value': 138938.62, 'new_value': 156858.35}, {'field': 'order_count', 'old_value': 5150, 'new_value': 5842}]
2025-05-12 00:00:48,633 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-12 00:00:49,021 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-12 00:00:49,021 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15072.5, 'new_value': 15620.5}, {'field': 'offline_amount', 'old_value': 44761.8, 'new_value': 59536.8}, {'field': 'total_amount', 'old_value': 59834.3, 'new_value': 75157.3}, {'field': 'order_count', 'old_value': 91, 'new_value': 105}]
2025-05-12 00:00:49,021 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-12 00:00:49,451 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-12 00:00:49,451 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53199.31, 'new_value': 59647.46}, {'field': 'offline_amount', 'old_value': 127840.65, 'new_value': 147630.41}, {'field': 'total_amount', 'old_value': 181039.96, 'new_value': 207277.87}, {'field': 'order_count', 'old_value': 2077, 'new_value': 2375}]
2025-05-12 00:00:49,451 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-12 00:00:49,835 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-12 00:00:49,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40691.4, 'new_value': 46405.4}, {'field': 'total_amount', 'old_value': 40691.4, 'new_value': 46405.4}, {'field': 'order_count', 'old_value': 309, 'new_value': 358}]
2025-05-12 00:00:49,836 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-12 00:00:50,225 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-12 00:00:50,226 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31270.91, 'new_value': 34492.57}, {'field': 'offline_amount', 'old_value': 114558.6, 'new_value': 136724.31}, {'field': 'total_amount', 'old_value': 145829.51, 'new_value': 171216.88}, {'field': 'order_count', 'old_value': 1921, 'new_value': 2463}]
2025-05-12 00:00:50,226 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-12 00:00:50,593 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-12 00:00:50,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36980.2, 'new_value': 40733.2}, {'field': 'total_amount', 'old_value': 36980.2, 'new_value': 40733.2}, {'field': 'order_count', 'old_value': 100, 'new_value': 108}]
2025-05-12 00:00:50,594 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-12 00:00:50,928 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-12 00:00:50,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10969.56, 'new_value': 11664.41}, {'field': 'total_amount', 'old_value': 11619.56, 'new_value': 12314.41}, {'field': 'order_count', 'old_value': 221, 'new_value': 241}]
2025-05-12 00:00:50,928 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-12 00:00:51,311 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-12 00:00:51,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10846.0, 'new_value': 11085.0}, {'field': 'total_amount', 'old_value': 10846.0, 'new_value': 11085.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-12 00:00:51,312 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-12 00:00:51,766 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-12 00:00:51,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17054.0, 'new_value': 18489.0}, {'field': 'total_amount', 'old_value': 17054.0, 'new_value': 18489.0}, {'field': 'order_count', 'old_value': 156, 'new_value': 168}]
2025-05-12 00:00:51,767 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-12 00:00:52,148 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-12 00:00:52,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392937.6, 'new_value': 417281.6}, {'field': 'total_amount', 'old_value': 392937.6, 'new_value': 417281.6}, {'field': 'order_count', 'old_value': 977, 'new_value': 1045}]
2025-05-12 00:00:52,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-12 00:00:52,587 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-12 00:00:52,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233065.0, 'new_value': 349052.0}, {'field': 'total_amount', 'old_value': 233065.0, 'new_value': 349052.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 49}]
2025-05-12 00:00:52,587 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-12 00:00:52,977 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-12 00:00:52,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34281.07, 'new_value': 40415.89}, {'field': 'offline_amount', 'old_value': 63590.91, 'new_value': 75741.01}, {'field': 'total_amount', 'old_value': 97871.98, 'new_value': 116156.9}, {'field': 'order_count', 'old_value': 960, 'new_value': 1124}]
2025-05-12 00:00:52,977 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-12 00:00:53,351 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-12 00:00:53,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5011.6, 'new_value': 5277.6}, {'field': 'total_amount', 'old_value': 5109.6, 'new_value': 5375.6}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-05-12 00:00:53,351 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-12 00:00:53,692 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-12 00:00:53,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245288.0, 'new_value': 340258.0}, {'field': 'total_amount', 'old_value': 245728.0, 'new_value': 340698.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 149}]
2025-05-12 00:00:53,693 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-12 00:00:54,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-12 00:00:54,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15073.0, 'new_value': 17534.0}, {'field': 'total_amount', 'old_value': 15073.0, 'new_value': 17534.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 75}]
2025-05-12 00:00:54,165 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-12 00:00:54,641 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-12 00:00:54,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47780.9, 'new_value': 55502.8}, {'field': 'offline_amount', 'old_value': 69814.3, 'new_value': 83603.6}, {'field': 'total_amount', 'old_value': 117595.2, 'new_value': 139106.4}, {'field': 'order_count', 'old_value': 2353, 'new_value': 2783}]
2025-05-12 00:00:54,642 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-12 00:00:55,041 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-12 00:00:55,041 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223666.42, 'new_value': 251463.07}, {'field': 'total_amount', 'old_value': 223666.42, 'new_value': 251463.07}, {'field': 'order_count', 'old_value': 2965, 'new_value': 3290}]
2025-05-12 00:00:55,042 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-12 00:00:55,488 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-12 00:00:55,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87924.0, 'new_value': 99278.0}, {'field': 'total_amount', 'old_value': 87924.0, 'new_value': 99278.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 159}]
2025-05-12 00:00:55,488 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-12 00:00:55,852 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-12 00:00:55,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40605.79, 'new_value': 47013.27}, {'field': 'total_amount', 'old_value': 40605.79, 'new_value': 47013.27}, {'field': 'order_count', 'old_value': 1237, 'new_value': 1418}]
2025-05-12 00:00:55,852 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-12 00:00:56,262 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-12 00:00:56,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133203.0, 'new_value': 148852.0}, {'field': 'total_amount', 'old_value': 133203.0, 'new_value': 148852.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-05-12 00:00:56,263 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-12 00:00:56,654 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-12 00:00:56,654 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16974.6, 'new_value': 18275.63}, {'field': 'offline_amount', 'old_value': 162537.47, 'new_value': 182136.57}, {'field': 'total_amount', 'old_value': 179512.07, 'new_value': 200412.2}, {'field': 'order_count', 'old_value': 4133, 'new_value': 4684}]
2025-05-12 00:00:56,654 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-12 00:00:57,064 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-12 00:00:57,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217070.3, 'new_value': 244564.6}, {'field': 'total_amount', 'old_value': 217070.3, 'new_value': 244564.6}, {'field': 'order_count', 'old_value': 954, 'new_value': 1082}]
2025-05-12 00:00:57,064 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-12 00:00:57,485 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-12 00:00:57,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73712.2, 'new_value': 78642.04}, {'field': 'offline_amount', 'old_value': 24901.93, 'new_value': 28485.14}, {'field': 'total_amount', 'old_value': 98614.13, 'new_value': 107127.18}, {'field': 'order_count', 'old_value': 6260, 'new_value': 6749}]
2025-05-12 00:00:57,486 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-12 00:00:57,919 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-12 00:00:57,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102693.0, 'new_value': 109490.0}, {'field': 'total_amount', 'old_value': 102693.0, 'new_value': 109490.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 121}]
2025-05-12 00:00:57,919 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-12 00:00:58,363 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-12 00:00:58,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 360206.94, 'new_value': 407422.75}, {'field': 'total_amount', 'old_value': 360206.94, 'new_value': 407422.75}, {'field': 'order_count', 'old_value': 7272, 'new_value': 8252}]
2025-05-12 00:00:58,363 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-12 00:00:58,735 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-12 00:00:58,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120834.65, 'new_value': 135643.58}, {'field': 'total_amount', 'old_value': 120834.65, 'new_value': 135643.58}, {'field': 'order_count', 'old_value': 5060, 'new_value': 5678}]
2025-05-12 00:00:58,735 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-12 00:00:59,136 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-12 00:00:59,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109627.0, 'new_value': 123714.0}, {'field': 'total_amount', 'old_value': 109627.0, 'new_value': 123714.0}, {'field': 'order_count', 'old_value': 320, 'new_value': 362}]
2025-05-12 00:00:59,136 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-12 00:00:59,579 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-12 00:00:59,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 308498.0, 'new_value': 362276.0}, {'field': 'total_amount', 'old_value': 308498.0, 'new_value': 362276.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 95}]
2025-05-12 00:00:59,579 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-12 00:00:59,967 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-12 00:00:59,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328681.0, 'new_value': 365475.92}, {'field': 'total_amount', 'old_value': 328681.0, 'new_value': 365475.92}, {'field': 'order_count', 'old_value': 2155, 'new_value': 2424}]
2025-05-12 00:00:59,968 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-12 00:01:00,414 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-12 00:01:00,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27628.68, 'new_value': 31439.42}, {'field': 'offline_amount', 'old_value': 41713.13, 'new_value': 47167.27}, {'field': 'total_amount', 'old_value': 69341.81, 'new_value': 78606.69}, {'field': 'order_count', 'old_value': 3208, 'new_value': 3623}]
2025-05-12 00:01:00,415 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-12 00:01:00,845 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-12 00:01:00,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160342.1, 'new_value': 182453.25}, {'field': 'total_amount', 'old_value': 182504.48, 'new_value': 204615.63}, {'field': 'order_count', 'old_value': 7441, 'new_value': 8376}]
2025-05-12 00:01:00,845 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-12 00:01:01,212 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-12 00:01:01,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7136.3, 'new_value': 7477.7}, {'field': 'offline_amount', 'old_value': 3000.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 10136.3, 'new_value': 12477.7}, {'field': 'order_count', 'old_value': 108, 'new_value': 115}]
2025-05-12 00:01:01,212 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-12 00:01:01,609 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-12 00:01:01,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9882.87, 'new_value': 11082.76}, {'field': 'offline_amount', 'old_value': 110909.94, 'new_value': 130327.64}, {'field': 'total_amount', 'old_value': 120792.81, 'new_value': 141410.4}, {'field': 'order_count', 'old_value': 3684, 'new_value': 4309}]
2025-05-12 00:01:01,610 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-12 00:01:02,003 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-12 00:01:02,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76458.7, 'new_value': 78331.6}, {'field': 'total_amount', 'old_value': 76458.7, 'new_value': 78331.6}, {'field': 'order_count', 'old_value': 136, 'new_value': 141}]
2025-05-12 00:01:02,003 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-12 00:01:02,435 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-12 00:01:02,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108461.41, 'new_value': 127186.25}, {'field': 'total_amount', 'old_value': 127634.84, 'new_value': 146359.68}, {'field': 'order_count', 'old_value': 2709, 'new_value': 3100}]
2025-05-12 00:01:02,435 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-12 00:01:02,812 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-12 00:01:02,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48313.0, 'new_value': 59833.0}, {'field': 'offline_amount', 'old_value': 42584.44, 'new_value': 48006.74}, {'field': 'total_amount', 'old_value': 90897.44, 'new_value': 107839.74}, {'field': 'order_count', 'old_value': 595, 'new_value': 698}]
2025-05-12 00:01:02,812 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-12 00:01:03,181 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-12 00:01:03,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187914.3, 'new_value': 201107.4}, {'field': 'total_amount', 'old_value': 187914.3, 'new_value': 201107.4}, {'field': 'order_count', 'old_value': 221, 'new_value': 240}]
2025-05-12 00:01:03,181 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-12 00:01:03,595 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-12 00:01:03,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 426584.0, 'new_value': 487357.0}, {'field': 'total_amount', 'old_value': 426584.0, 'new_value': 487357.0}, {'field': 'order_count', 'old_value': 477, 'new_value': 544}]
2025-05-12 00:01:03,595 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-12 00:01:03,973 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-12 00:01:03,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 308034.0, 'new_value': 339030.0}, {'field': 'total_amount', 'old_value': 308034.0, 'new_value': 339030.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 36}]
2025-05-12 00:01:03,973 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-12 00:01:04,391 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-12 00:01:04,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 182934.91, 'new_value': 196406.12}, {'field': 'offline_amount', 'old_value': 549582.91, 'new_value': 635773.27}, {'field': 'total_amount', 'old_value': 732517.82, 'new_value': 832179.39}, {'field': 'order_count', 'old_value': 3510, 'new_value': 3946}]
2025-05-12 00:01:04,391 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-12 00:01:04,817 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-12 00:01:04,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56056.2, 'new_value': 62408.0}, {'field': 'offline_amount', 'old_value': 45989.3, 'new_value': 53083.9}, {'field': 'total_amount', 'old_value': 102045.5, 'new_value': 115491.9}, {'field': 'order_count', 'old_value': 2436, 'new_value': 2724}]
2025-05-12 00:01:04,817 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-12 00:01:05,246 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-12 00:01:05,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15441.2, 'new_value': 17678.2}, {'field': 'total_amount', 'old_value': 15441.2, 'new_value': 17678.2}, {'field': 'order_count', 'old_value': 86, 'new_value': 101}]
2025-05-12 00:01:05,247 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-12 00:01:05,698 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-12 00:01:05,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122249.6, 'new_value': 142389.13}, {'field': 'total_amount', 'old_value': 122249.6, 'new_value': 142389.13}, {'field': 'order_count', 'old_value': 669, 'new_value': 769}]
2025-05-12 00:01:05,698 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-12 00:01:06,090 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-12 00:01:06,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96541.77, 'new_value': 111835.99}, {'field': 'offline_amount', 'old_value': 341717.48, 'new_value': 397579.26}, {'field': 'total_amount', 'old_value': 438259.25, 'new_value': 509415.25}, {'field': 'order_count', 'old_value': 2436, 'new_value': 2739}]
2025-05-12 00:01:06,091 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-12 00:01:06,475 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-12 00:01:06,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14752.56, 'new_value': 15595.4}, {'field': 'offline_amount', 'old_value': 165686.42, 'new_value': 201012.06}, {'field': 'total_amount', 'old_value': 180438.98, 'new_value': 216607.46}, {'field': 'order_count', 'old_value': 1051, 'new_value': 1217}]
2025-05-12 00:01:06,476 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-12 00:01:06,860 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-12 00:01:06,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17308.0, 'new_value': 19262.0}, {'field': 'total_amount', 'old_value': 17308.0, 'new_value': 19262.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-12 00:01:06,860 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-12 00:01:07,308 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-12 00:01:07,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18273.43, 'new_value': 24113.1}, {'field': 'total_amount', 'old_value': 35340.11, 'new_value': 41179.78}, {'field': 'order_count', 'old_value': 1598, 'new_value': 1812}]
2025-05-12 00:01:07,308 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-12 00:01:07,704 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-12 00:01:07,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137810.0, 'new_value': 155390.0}, {'field': 'total_amount', 'old_value': 137810.0, 'new_value': 155390.0}, {'field': 'order_count', 'old_value': 199, 'new_value': 225}]
2025-05-12 00:01:07,704 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-12 00:01:08,081 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-12 00:01:08,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19097.1, 'new_value': 26446.1}, {'field': 'total_amount', 'old_value': 19525.1, 'new_value': 26874.1}, {'field': 'order_count', 'old_value': 9229, 'new_value': 9231}]
2025-05-12 00:01:08,082 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-12 00:01:08,530 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-12 00:01:08,530 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102581.61, 'new_value': 121142.19}, {'field': 'offline_amount', 'old_value': 213000.0, 'new_value': 226000.0}, {'field': 'total_amount', 'old_value': 315581.61, 'new_value': 347142.19}, {'field': 'order_count', 'old_value': 633, 'new_value': 725}]
2025-05-12 00:01:08,531 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-12 00:01:08,959 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-12 00:01:08,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1043807.0, 'new_value': 1168974.0}, {'field': 'total_amount', 'old_value': 1043807.0, 'new_value': 1168974.0}, {'field': 'order_count', 'old_value': 3933, 'new_value': 4404}]
2025-05-12 00:01:08,959 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-12 00:01:09,380 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-12 00:01:09,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18625.83, 'new_value': 20237.39}, {'field': 'offline_amount', 'old_value': 11689.18, 'new_value': 12898.55}, {'field': 'total_amount', 'old_value': 30315.01, 'new_value': 33135.94}, {'field': 'order_count', 'old_value': 1298, 'new_value': 1429}]
2025-05-12 00:01:09,380 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-12 00:01:09,763 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-12 00:01:09,763 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18629.87, 'new_value': 20984.52}, {'field': 'offline_amount', 'old_value': 24469.11, 'new_value': 27155.76}, {'field': 'total_amount', 'old_value': 43098.98, 'new_value': 48140.28}, {'field': 'order_count', 'old_value': 3235, 'new_value': 3663}]
2025-05-12 00:01:09,763 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-12 00:01:10,296 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-12 00:01:10,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211893.7, 'new_value': 229477.6}, {'field': 'total_amount', 'old_value': 211893.7, 'new_value': 229477.6}]
2025-05-12 00:01:10,297 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-12 00:01:10,763 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-12 00:01:10,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215231.0, 'new_value': 267859.0}, {'field': 'total_amount', 'old_value': 215231.0, 'new_value': 267859.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 37}]
2025-05-12 00:01:10,763 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-12 00:01:11,159 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-12 00:01:11,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 291281.0, 'new_value': 331784.0}, {'field': 'total_amount', 'old_value': 291281.0, 'new_value': 331784.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 200}]
2025-05-12 00:01:11,161 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-12 00:01:11,521 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-12 00:01:11,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175222.0, 'new_value': 181772.0}, {'field': 'total_amount', 'old_value': 176218.0, 'new_value': 182768.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 45}]
2025-05-12 00:01:11,522 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-12 00:01:11,893 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-12 00:01:11,893 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 431.6, 'new_value': 701.56}, {'field': 'offline_amount', 'old_value': 1544.48, 'new_value': 3200.98}, {'field': 'total_amount', 'old_value': 1976.08, 'new_value': 3902.54}, {'field': 'order_count', 'old_value': 90, 'new_value': 154}]
2025-05-12 00:01:11,893 - INFO - 日期 2025-05 处理完成 - 更新: 102 条，插入: 0 条，错误: 0 条
2025-05-12 00:01:11,893 - INFO - 数据同步完成！更新: 102 条，插入: 0 条，错误: 0 条
2025-05-12 00:01:11,894 - INFO - =================同步完成====================
2025-05-12 03:00:03,932 - INFO - =================使用默认全量同步=============
2025-05-12 03:00:05,249 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-12 03:00:05,249 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-12 03:00:05,277 - INFO - 开始处理日期: 2025-01
2025-05-12 03:00:05,280 - INFO - Request Parameters - Page 1:
2025-05-12 03:00:05,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:05,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:06,479 - INFO - Response - Page 1:
2025-05-12 03:00:06,679 - INFO - 第 1 页获取到 100 条记录
2025-05-12 03:00:06,679 - INFO - Request Parameters - Page 2:
2025-05-12 03:00:06,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:06,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:07,186 - INFO - Response - Page 2:
2025-05-12 03:00:07,386 - INFO - 第 2 页获取到 100 条记录
2025-05-12 03:00:07,386 - INFO - Request Parameters - Page 3:
2025-05-12 03:00:07,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:07,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:07,790 - INFO - Response - Page 3:
2025-05-12 03:00:07,990 - INFO - 第 3 页获取到 100 条记录
2025-05-12 03:00:07,990 - INFO - Request Parameters - Page 4:
2025-05-12 03:00:07,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:07,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:08,432 - INFO - Response - Page 4:
2025-05-12 03:00:08,632 - INFO - 第 4 页获取到 100 条记录
2025-05-12 03:00:08,632 - INFO - Request Parameters - Page 5:
2025-05-12 03:00:08,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:08,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:09,200 - INFO - Response - Page 5:
2025-05-12 03:00:09,401 - INFO - 第 5 页获取到 100 条记录
2025-05-12 03:00:09,401 - INFO - Request Parameters - Page 6:
2025-05-12 03:00:09,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:09,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:09,919 - INFO - Response - Page 6:
2025-05-12 03:00:10,120 - INFO - 第 6 页获取到 100 条记录
2025-05-12 03:00:10,120 - INFO - Request Parameters - Page 7:
2025-05-12 03:00:10,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:10,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:10,573 - INFO - Response - Page 7:
2025-05-12 03:00:10,773 - INFO - 第 7 页获取到 82 条记录
2025-05-12 03:00:10,773 - INFO - 查询完成，共获取到 682 条记录
2025-05-12 03:00:10,773 - INFO - 获取到 682 条表单数据
2025-05-12 03:00:10,785 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-12 03:00:10,796 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 03:00:10,796 - INFO - 开始处理日期: 2025-02
2025-05-12 03:00:10,797 - INFO - Request Parameters - Page 1:
2025-05-12 03:00:10,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:10,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:11,285 - INFO - Response - Page 1:
2025-05-12 03:00:11,486 - INFO - 第 1 页获取到 100 条记录
2025-05-12 03:00:11,486 - INFO - Request Parameters - Page 2:
2025-05-12 03:00:11,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:11,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:11,956 - INFO - Response - Page 2:
2025-05-12 03:00:12,156 - INFO - 第 2 页获取到 100 条记录
2025-05-12 03:00:12,156 - INFO - Request Parameters - Page 3:
2025-05-12 03:00:12,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:12,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:12,818 - INFO - Response - Page 3:
2025-05-12 03:00:13,018 - INFO - 第 3 页获取到 100 条记录
2025-05-12 03:00:13,018 - INFO - Request Parameters - Page 4:
2025-05-12 03:00:13,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:13,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:13,597 - INFO - Response - Page 4:
2025-05-12 03:00:13,797 - INFO - 第 4 页获取到 100 条记录
2025-05-12 03:00:13,797 - INFO - Request Parameters - Page 5:
2025-05-12 03:00:13,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:13,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:14,269 - INFO - Response - Page 5:
2025-05-12 03:00:14,469 - INFO - 第 5 页获取到 100 条记录
2025-05-12 03:00:14,469 - INFO - Request Parameters - Page 6:
2025-05-12 03:00:14,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:14,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:14,985 - INFO - Response - Page 6:
2025-05-12 03:00:15,185 - INFO - 第 6 页获取到 100 条记录
2025-05-12 03:00:15,185 - INFO - Request Parameters - Page 7:
2025-05-12 03:00:15,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:15,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:15,664 - INFO - Response - Page 7:
2025-05-12 03:00:15,865 - INFO - 第 7 页获取到 70 条记录
2025-05-12 03:00:15,865 - INFO - 查询完成，共获取到 670 条记录
2025-05-12 03:00:15,865 - INFO - 获取到 670 条表单数据
2025-05-12 03:00:15,878 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-12 03:00:15,890 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 03:00:15,890 - INFO - 开始处理日期: 2025-03
2025-05-12 03:00:15,890 - INFO - Request Parameters - Page 1:
2025-05-12 03:00:15,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:15,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:16,507 - INFO - Response - Page 1:
2025-05-12 03:00:16,708 - INFO - 第 1 页获取到 100 条记录
2025-05-12 03:00:16,708 - INFO - Request Parameters - Page 2:
2025-05-12 03:00:16,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:16,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:17,253 - INFO - Response - Page 2:
2025-05-12 03:00:17,453 - INFO - 第 2 页获取到 100 条记录
2025-05-12 03:00:17,453 - INFO - Request Parameters - Page 3:
2025-05-12 03:00:17,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:17,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:18,102 - INFO - Response - Page 3:
2025-05-12 03:00:18,303 - INFO - 第 3 页获取到 100 条记录
2025-05-12 03:00:18,303 - INFO - Request Parameters - Page 4:
2025-05-12 03:00:18,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:18,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:18,826 - INFO - Response - Page 4:
2025-05-12 03:00:19,027 - INFO - 第 4 页获取到 100 条记录
2025-05-12 03:00:19,027 - INFO - Request Parameters - Page 5:
2025-05-12 03:00:19,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:19,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:19,529 - INFO - Response - Page 5:
2025-05-12 03:00:19,730 - INFO - 第 5 页获取到 100 条记录
2025-05-12 03:00:19,730 - INFO - Request Parameters - Page 6:
2025-05-12 03:00:19,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:19,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:20,216 - INFO - Response - Page 6:
2025-05-12 03:00:20,417 - INFO - 第 6 页获取到 100 条记录
2025-05-12 03:00:20,417 - INFO - Request Parameters - Page 7:
2025-05-12 03:00:20,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:20,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:20,863 - INFO - Response - Page 7:
2025-05-12 03:00:21,063 - INFO - 第 7 页获取到 61 条记录
2025-05-12 03:00:21,063 - INFO - 查询完成，共获取到 661 条记录
2025-05-12 03:00:21,063 - INFO - 获取到 661 条表单数据
2025-05-12 03:00:21,075 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-12 03:00:21,086 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 03:00:21,086 - INFO - 开始处理日期: 2025-04
2025-05-12 03:00:21,086 - INFO - Request Parameters - Page 1:
2025-05-12 03:00:21,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:21,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:21,552 - INFO - Response - Page 1:
2025-05-12 03:00:21,752 - INFO - 第 1 页获取到 100 条记录
2025-05-12 03:00:21,752 - INFO - Request Parameters - Page 2:
2025-05-12 03:00:21,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:21,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:22,219 - INFO - Response - Page 2:
2025-05-12 03:00:22,420 - INFO - 第 2 页获取到 100 条记录
2025-05-12 03:00:22,420 - INFO - Request Parameters - Page 3:
2025-05-12 03:00:22,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:22,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:23,029 - INFO - Response - Page 3:
2025-05-12 03:00:23,229 - INFO - 第 3 页获取到 100 条记录
2025-05-12 03:00:23,229 - INFO - Request Parameters - Page 4:
2025-05-12 03:00:23,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:23,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:23,715 - INFO - Response - Page 4:
2025-05-12 03:00:23,916 - INFO - 第 4 页获取到 100 条记录
2025-05-12 03:00:23,916 - INFO - Request Parameters - Page 5:
2025-05-12 03:00:23,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:23,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:24,610 - INFO - Response - Page 5:
2025-05-12 03:00:24,811 - INFO - 第 5 页获取到 100 条记录
2025-05-12 03:00:24,811 - INFO - Request Parameters - Page 6:
2025-05-12 03:00:24,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:24,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:25,323 - INFO - Response - Page 6:
2025-05-12 03:00:25,523 - INFO - 第 6 页获取到 100 条记录
2025-05-12 03:00:25,523 - INFO - Request Parameters - Page 7:
2025-05-12 03:00:25,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:25,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:25,972 - INFO - Response - Page 7:
2025-05-12 03:00:26,172 - INFO - 第 7 页获取到 54 条记录
2025-05-12 03:00:26,172 - INFO - 查询完成，共获取到 654 条记录
2025-05-12 03:00:26,172 - INFO - 获取到 654 条表单数据
2025-05-12 03:00:26,184 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-12 03:00:26,196 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 03:00:26,196 - INFO - 开始处理日期: 2025-05
2025-05-12 03:00:26,196 - INFO - Request Parameters - Page 1:
2025-05-12 03:00:26,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:26,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:26,856 - INFO - Response - Page 1:
2025-05-12 03:00:27,057 - INFO - 第 1 页获取到 100 条记录
2025-05-12 03:00:27,057 - INFO - Request Parameters - Page 2:
2025-05-12 03:00:27,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:27,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:27,620 - INFO - Response - Page 2:
2025-05-12 03:00:27,820 - INFO - 第 2 页获取到 100 条记录
2025-05-12 03:00:27,820 - INFO - Request Parameters - Page 3:
2025-05-12 03:00:27,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:27,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:28,300 - INFO - Response - Page 3:
2025-05-12 03:00:28,500 - INFO - 第 3 页获取到 100 条记录
2025-05-12 03:00:28,500 - INFO - Request Parameters - Page 4:
2025-05-12 03:00:28,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:28,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:28,947 - INFO - Response - Page 4:
2025-05-12 03:00:29,148 - INFO - 第 4 页获取到 100 条记录
2025-05-12 03:00:29,148 - INFO - Request Parameters - Page 5:
2025-05-12 03:00:29,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:29,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:29,625 - INFO - Response - Page 5:
2025-05-12 03:00:29,826 - INFO - 第 5 页获取到 100 条记录
2025-05-12 03:00:29,826 - INFO - Request Parameters - Page 6:
2025-05-12 03:00:29,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:29,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:30,304 - INFO - Response - Page 6:
2025-05-12 03:00:30,504 - INFO - 第 6 页获取到 100 条记录
2025-05-12 03:00:30,504 - INFO - Request Parameters - Page 7:
2025-05-12 03:00:30,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 03:00:30,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 03:00:30,830 - INFO - Response - Page 7:
2025-05-12 03:00:31,030 - INFO - 第 7 页获取到 24 条记录
2025-05-12 03:00:31,030 - INFO - 查询完成，共获取到 624 条记录
2025-05-12 03:00:31,030 - INFO - 获取到 624 条表单数据
2025-05-12 03:00:31,043 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-12 03:00:31,046 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-12 03:00:31,521 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-12 03:00:31,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79312.4, 'new_value': 87030.28}, {'field': 'offline_amount', 'old_value': 212105.33, 'new_value': 256687.59}, {'field': 'total_amount', 'old_value': 291417.73, 'new_value': 343717.87}, {'field': 'order_count', 'old_value': 2003, 'new_value': 2271}]
2025-05-12 03:00:31,521 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-12 03:00:32,007 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-12 03:00:32,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2458.0, 'new_value': 2557.0}, {'field': 'total_amount', 'old_value': 2458.0, 'new_value': 2557.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-12 03:00:32,008 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-12 03:00:32,447 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-12 03:00:32,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61326.0, 'new_value': 68985.0}, {'field': 'total_amount', 'old_value': 61327.0, 'new_value': 68986.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-12 03:00:32,448 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-12 03:00:32,895 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-12 03:00:32,895 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 258.0, 'new_value': 5141.0}, {'field': 'offline_amount', 'old_value': 47304.0, 'new_value': 47781.0}, {'field': 'total_amount', 'old_value': 47562.0, 'new_value': 52922.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 17}]
2025-05-12 03:00:32,895 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-12 03:00:33,327 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-12 03:00:33,327 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4273.7, 'new_value': 4625.4}, {'field': 'offline_amount', 'old_value': 19860.0, 'new_value': 24679.0}, {'field': 'total_amount', 'old_value': 24133.7, 'new_value': 29304.4}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-05-12 03:00:33,328 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-12 03:00:33,790 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-12 03:00:33,790 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117416.85, 'new_value': 119132.75}, {'field': 'offline_amount', 'old_value': 29041.0, 'new_value': 55644.48}, {'field': 'total_amount', 'old_value': 146457.85, 'new_value': 174777.23}, {'field': 'order_count', 'old_value': 1855, 'new_value': 2018}]
2025-05-12 03:00:33,791 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-12 03:00:34,261 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-12 03:00:34,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16269.2, 'new_value': 18948.2}, {'field': 'total_amount', 'old_value': 16269.2, 'new_value': 18948.2}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-12 03:00:34,262 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-12 03:00:34,740 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-12 03:00:34,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116565.0, 'new_value': 127757.0}, {'field': 'total_amount', 'old_value': 116565.0, 'new_value': 127757.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 58}]
2025-05-12 03:00:34,741 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-12 03:00:35,272 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-12 03:00:35,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15778.02, 'new_value': 18218.2}, {'field': 'total_amount', 'old_value': 15778.02, 'new_value': 18218.2}, {'field': 'order_count', 'old_value': 63, 'new_value': 73}]
2025-05-12 03:00:35,273 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-12 03:00:35,666 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-12 03:00:35,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3964.9, 'new_value': 5618.9}, {'field': 'total_amount', 'old_value': 9873.3, 'new_value': 11527.3}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-05-12 03:00:35,667 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-12 03:00:36,111 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-12 03:00:36,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154230.74, 'new_value': 183603.16}, {'field': 'total_amount', 'old_value': 154230.74, 'new_value': 183603.16}, {'field': 'order_count', 'old_value': 1292, 'new_value': 1481}]
2025-05-12 03:00:36,111 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-12 03:00:36,585 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-12 03:00:36,585 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1719.0, 'new_value': 2149.0}, {'field': 'offline_amount', 'old_value': 12622.0, 'new_value': 19415.0}, {'field': 'total_amount', 'old_value': 14341.0, 'new_value': 21564.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 168}]
2025-05-12 03:00:36,585 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-12 03:00:37,043 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-12 03:00:37,043 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26112.89, 'new_value': 28700.82}, {'field': 'offline_amount', 'old_value': 202989.46, 'new_value': 233627.96}, {'field': 'total_amount', 'old_value': 229102.35, 'new_value': 262328.78}, {'field': 'order_count', 'old_value': 942, 'new_value': 1108}]
2025-05-12 03:00:37,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-12 03:00:37,480 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-12 03:00:37,481 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30236.46, 'new_value': 39712.93}, {'field': 'offline_amount', 'old_value': 199273.6, 'new_value': 233060.3}, {'field': 'total_amount', 'old_value': 229510.06, 'new_value': 272773.23}, {'field': 'order_count', 'old_value': 1448, 'new_value': 1769}]
2025-05-12 03:00:37,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-12 03:00:37,812 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-12 03:00:37,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 302782.58, 'new_value': 357272.58}, {'field': 'total_amount', 'old_value': 302782.58, 'new_value': 357272.58}, {'field': 'order_count', 'old_value': 2272, 'new_value': 2630}]
2025-05-12 03:00:37,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-12 03:00:38,322 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-12 03:00:38,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21759.43, 'new_value': 23518.83}, {'field': 'total_amount', 'old_value': 21759.43, 'new_value': 23518.83}, {'field': 'order_count', 'old_value': 67, 'new_value': 70}]
2025-05-12 03:00:38,323 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-12 03:00:38,712 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-12 03:00:38,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39732.0, 'new_value': 46530.0}, {'field': 'total_amount', 'old_value': 39732.0, 'new_value': 46530.0}, {'field': 'order_count', 'old_value': 171, 'new_value': 196}]
2025-05-12 03:00:38,712 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-12 03:00:39,149 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-12 03:00:39,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107714.32, 'new_value': 138215.32}, {'field': 'total_amount', 'old_value': 130201.72, 'new_value': 160702.72}, {'field': 'order_count', 'old_value': 793, 'new_value': 876}]
2025-05-12 03:00:39,152 - INFO - 日期 2025-05 处理完成 - 更新: 18 条，插入: 0 条，错误: 0 条
2025-05-12 03:00:39,152 - INFO - 数据同步完成！更新: 18 条，插入: 0 条，错误: 0 条
2025-05-12 03:00:39,154 - INFO - =================同步完成====================
2025-05-12 06:00:03,908 - INFO - =================使用默认全量同步=============
2025-05-12 06:00:05,205 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-12 06:00:05,206 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-12 06:00:05,234 - INFO - 开始处理日期: 2025-01
2025-05-12 06:00:05,237 - INFO - Request Parameters - Page 1:
2025-05-12 06:00:05,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:05,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:06,272 - INFO - Response - Page 1:
2025-05-12 06:00:06,473 - INFO - 第 1 页获取到 100 条记录
2025-05-12 06:00:06,473 - INFO - Request Parameters - Page 2:
2025-05-12 06:00:06,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:06,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:07,017 - INFO - Response - Page 2:
2025-05-12 06:00:07,217 - INFO - 第 2 页获取到 100 条记录
2025-05-12 06:00:07,217 - INFO - Request Parameters - Page 3:
2025-05-12 06:00:07,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:07,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:07,802 - INFO - Response - Page 3:
2025-05-12 06:00:08,004 - INFO - 第 3 页获取到 100 条记录
2025-05-12 06:00:08,004 - INFO - Request Parameters - Page 4:
2025-05-12 06:00:08,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:08,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:08,709 - INFO - Response - Page 4:
2025-05-12 06:00:08,910 - INFO - 第 4 页获取到 100 条记录
2025-05-12 06:00:08,910 - INFO - Request Parameters - Page 5:
2025-05-12 06:00:08,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:08,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:09,415 - INFO - Response - Page 5:
2025-05-12 06:00:09,616 - INFO - 第 5 页获取到 100 条记录
2025-05-12 06:00:09,616 - INFO - Request Parameters - Page 6:
2025-05-12 06:00:09,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:09,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:10,152 - INFO - Response - Page 6:
2025-05-12 06:00:10,353 - INFO - 第 6 页获取到 100 条记录
2025-05-12 06:00:10,353 - INFO - Request Parameters - Page 7:
2025-05-12 06:00:10,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:10,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:10,827 - INFO - Response - Page 7:
2025-05-12 06:00:11,028 - INFO - 第 7 页获取到 82 条记录
2025-05-12 06:00:11,028 - INFO - 查询完成，共获取到 682 条记录
2025-05-12 06:00:11,028 - INFO - 获取到 682 条表单数据
2025-05-12 06:00:11,039 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-12 06:00:11,053 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 06:00:11,053 - INFO - 开始处理日期: 2025-02
2025-05-12 06:00:11,054 - INFO - Request Parameters - Page 1:
2025-05-12 06:00:11,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:11,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:11,500 - INFO - Response - Page 1:
2025-05-12 06:00:11,701 - INFO - 第 1 页获取到 100 条记录
2025-05-12 06:00:11,701 - INFO - Request Parameters - Page 2:
2025-05-12 06:00:11,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:11,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:12,212 - INFO - Response - Page 2:
2025-05-12 06:00:12,414 - INFO - 第 2 页获取到 100 条记录
2025-05-12 06:00:12,414 - INFO - Request Parameters - Page 3:
2025-05-12 06:00:12,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:12,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:12,863 - INFO - Response - Page 3:
2025-05-12 06:00:13,065 - INFO - 第 3 页获取到 100 条记录
2025-05-12 06:00:13,065 - INFO - Request Parameters - Page 4:
2025-05-12 06:00:13,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:13,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:13,537 - INFO - Response - Page 4:
2025-05-12 06:00:13,737 - INFO - 第 4 页获取到 100 条记录
2025-05-12 06:00:13,737 - INFO - Request Parameters - Page 5:
2025-05-12 06:00:13,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:13,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:14,187 - INFO - Response - Page 5:
2025-05-12 06:00:14,388 - INFO - 第 5 页获取到 100 条记录
2025-05-12 06:00:14,388 - INFO - Request Parameters - Page 6:
2025-05-12 06:00:14,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:14,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:14,877 - INFO - Response - Page 6:
2025-05-12 06:00:15,077 - INFO - 第 6 页获取到 100 条记录
2025-05-12 06:00:15,077 - INFO - Request Parameters - Page 7:
2025-05-12 06:00:15,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:15,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:15,576 - INFO - Response - Page 7:
2025-05-12 06:00:15,776 - INFO - 第 7 页获取到 70 条记录
2025-05-12 06:00:15,776 - INFO - 查询完成，共获取到 670 条记录
2025-05-12 06:00:15,776 - INFO - 获取到 670 条表单数据
2025-05-12 06:00:15,788 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-12 06:00:15,800 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 06:00:15,801 - INFO - 开始处理日期: 2025-03
2025-05-12 06:00:15,801 - INFO - Request Parameters - Page 1:
2025-05-12 06:00:15,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:15,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:16,292 - INFO - Response - Page 1:
2025-05-12 06:00:16,493 - INFO - 第 1 页获取到 100 条记录
2025-05-12 06:00:16,493 - INFO - Request Parameters - Page 2:
2025-05-12 06:00:16,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:16,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:16,987 - INFO - Response - Page 2:
2025-05-12 06:00:17,188 - INFO - 第 2 页获取到 100 条记录
2025-05-12 06:00:17,188 - INFO - Request Parameters - Page 3:
2025-05-12 06:00:17,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:17,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:17,677 - INFO - Response - Page 3:
2025-05-12 06:00:17,879 - INFO - 第 3 页获取到 100 条记录
2025-05-12 06:00:17,879 - INFO - Request Parameters - Page 4:
2025-05-12 06:00:17,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:17,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:18,387 - INFO - Response - Page 4:
2025-05-12 06:00:18,587 - INFO - 第 4 页获取到 100 条记录
2025-05-12 06:00:18,587 - INFO - Request Parameters - Page 5:
2025-05-12 06:00:18,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:18,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:19,244 - INFO - Response - Page 5:
2025-05-12 06:00:19,445 - INFO - 第 5 页获取到 100 条记录
2025-05-12 06:00:19,445 - INFO - Request Parameters - Page 6:
2025-05-12 06:00:19,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:19,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:19,892 - INFO - Response - Page 6:
2025-05-12 06:00:20,094 - INFO - 第 6 页获取到 100 条记录
2025-05-12 06:00:20,094 - INFO - Request Parameters - Page 7:
2025-05-12 06:00:20,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:20,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:20,546 - INFO - Response - Page 7:
2025-05-12 06:00:20,747 - INFO - 第 7 页获取到 61 条记录
2025-05-12 06:00:20,747 - INFO - 查询完成，共获取到 661 条记录
2025-05-12 06:00:20,747 - INFO - 获取到 661 条表单数据
2025-05-12 06:00:20,758 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-12 06:00:20,770 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 06:00:20,770 - INFO - 开始处理日期: 2025-04
2025-05-12 06:00:20,771 - INFO - Request Parameters - Page 1:
2025-05-12 06:00:20,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:20,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:21,284 - INFO - Response - Page 1:
2025-05-12 06:00:21,485 - INFO - 第 1 页获取到 100 条记录
2025-05-12 06:00:21,485 - INFO - Request Parameters - Page 2:
2025-05-12 06:00:21,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:21,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:22,071 - INFO - Response - Page 2:
2025-05-12 06:00:22,272 - INFO - 第 2 页获取到 100 条记录
2025-05-12 06:00:22,272 - INFO - Request Parameters - Page 3:
2025-05-12 06:00:22,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:22,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:22,706 - INFO - Response - Page 3:
2025-05-12 06:00:22,906 - INFO - 第 3 页获取到 100 条记录
2025-05-12 06:00:22,906 - INFO - Request Parameters - Page 4:
2025-05-12 06:00:22,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:22,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:23,463 - INFO - Response - Page 4:
2025-05-12 06:00:23,664 - INFO - 第 4 页获取到 100 条记录
2025-05-12 06:00:23,664 - INFO - Request Parameters - Page 5:
2025-05-12 06:00:23,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:23,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:24,281 - INFO - Response - Page 5:
2025-05-12 06:00:24,483 - INFO - 第 5 页获取到 100 条记录
2025-05-12 06:00:24,483 - INFO - Request Parameters - Page 6:
2025-05-12 06:00:24,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:24,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:24,957 - INFO - Response - Page 6:
2025-05-12 06:00:25,157 - INFO - 第 6 页获取到 100 条记录
2025-05-12 06:00:25,157 - INFO - Request Parameters - Page 7:
2025-05-12 06:00:25,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:25,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:25,610 - INFO - Response - Page 7:
2025-05-12 06:00:25,810 - INFO - 第 7 页获取到 54 条记录
2025-05-12 06:00:25,810 - INFO - 查询完成，共获取到 654 条记录
2025-05-12 06:00:25,810 - INFO - 获取到 654 条表单数据
2025-05-12 06:00:25,823 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-12 06:00:25,834 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 06:00:25,834 - INFO - 开始处理日期: 2025-05
2025-05-12 06:00:25,835 - INFO - Request Parameters - Page 1:
2025-05-12 06:00:25,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:25,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:26,355 - INFO - Response - Page 1:
2025-05-12 06:00:26,555 - INFO - 第 1 页获取到 100 条记录
2025-05-12 06:00:26,555 - INFO - Request Parameters - Page 2:
2025-05-12 06:00:26,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:26,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:27,044 - INFO - Response - Page 2:
2025-05-12 06:00:27,245 - INFO - 第 2 页获取到 100 条记录
2025-05-12 06:00:27,245 - INFO - Request Parameters - Page 3:
2025-05-12 06:00:27,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:27,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:27,801 - INFO - Response - Page 3:
2025-05-12 06:00:28,002 - INFO - 第 3 页获取到 100 条记录
2025-05-12 06:00:28,002 - INFO - Request Parameters - Page 4:
2025-05-12 06:00:28,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:28,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:28,581 - INFO - Response - Page 4:
2025-05-12 06:00:28,782 - INFO - 第 4 页获取到 100 条记录
2025-05-12 06:00:28,782 - INFO - Request Parameters - Page 5:
2025-05-12 06:00:28,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:28,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:29,490 - INFO - Response - Page 5:
2025-05-12 06:00:29,691 - INFO - 第 5 页获取到 100 条记录
2025-05-12 06:00:29,691 - INFO - Request Parameters - Page 6:
2025-05-12 06:00:29,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:29,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:30,183 - INFO - Response - Page 6:
2025-05-12 06:00:30,385 - INFO - 第 6 页获取到 100 条记录
2025-05-12 06:00:30,385 - INFO - Request Parameters - Page 7:
2025-05-12 06:00:30,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 06:00:30,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 06:00:30,773 - INFO - Response - Page 7:
2025-05-12 06:00:30,973 - INFO - 第 7 页获取到 24 条记录
2025-05-12 06:00:30,973 - INFO - 查询完成，共获取到 624 条记录
2025-05-12 06:00:30,973 - INFO - 获取到 624 条表单数据
2025-05-12 06:00:30,985 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-12 06:00:30,995 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-12 06:00:31,453 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-12 06:00:31,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36930.0, 'new_value': 39513.0}, {'field': 'total_amount', 'old_value': 38780.0, 'new_value': 41363.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 239}]
2025-05-12 06:00:31,455 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-12 06:00:31,455 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-12 06:00:31,456 - INFO - =================同步完成====================
2025-05-12 09:00:02,911 - INFO - =================使用默认全量同步=============
2025-05-12 09:00:04,253 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-12 09:00:04,254 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-12 09:00:04,283 - INFO - 开始处理日期: 2025-01
2025-05-12 09:00:04,286 - INFO - Request Parameters - Page 1:
2025-05-12 09:00:04,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:04,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:05,220 - INFO - Response - Page 1:
2025-05-12 09:00:05,420 - INFO - 第 1 页获取到 100 条记录
2025-05-12 09:00:05,420 - INFO - Request Parameters - Page 2:
2025-05-12 09:00:05,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:05,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:05,944 - INFO - Response - Page 2:
2025-05-12 09:00:06,145 - INFO - 第 2 页获取到 100 条记录
2025-05-12 09:00:06,145 - INFO - Request Parameters - Page 3:
2025-05-12 09:00:06,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:06,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:07,028 - INFO - Response - Page 3:
2025-05-12 09:00:07,229 - INFO - 第 3 页获取到 100 条记录
2025-05-12 09:00:07,229 - INFO - Request Parameters - Page 4:
2025-05-12 09:00:07,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:07,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:07,667 - INFO - Response - Page 4:
2025-05-12 09:00:07,867 - INFO - 第 4 页获取到 100 条记录
2025-05-12 09:00:07,867 - INFO - Request Parameters - Page 5:
2025-05-12 09:00:07,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:07,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:08,408 - INFO - Response - Page 5:
2025-05-12 09:00:08,609 - INFO - 第 5 页获取到 100 条记录
2025-05-12 09:00:08,609 - INFO - Request Parameters - Page 6:
2025-05-12 09:00:08,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:08,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:09,059 - INFO - Response - Page 6:
2025-05-12 09:00:09,259 - INFO - 第 6 页获取到 100 条记录
2025-05-12 09:00:09,259 - INFO - Request Parameters - Page 7:
2025-05-12 09:00:09,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:09,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:09,678 - INFO - Response - Page 7:
2025-05-12 09:00:09,878 - INFO - 第 7 页获取到 82 条记录
2025-05-12 09:00:09,878 - INFO - 查询完成，共获取到 682 条记录
2025-05-12 09:00:09,878 - INFO - 获取到 682 条表单数据
2025-05-12 09:00:09,890 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-12 09:00:09,900 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 09:00:09,900 - INFO - 开始处理日期: 2025-02
2025-05-12 09:00:09,900 - INFO - Request Parameters - Page 1:
2025-05-12 09:00:09,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:09,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:10,359 - INFO - Response - Page 1:
2025-05-12 09:00:10,560 - INFO - 第 1 页获取到 100 条记录
2025-05-12 09:00:10,560 - INFO - Request Parameters - Page 2:
2025-05-12 09:00:10,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:10,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:11,041 - INFO - Response - Page 2:
2025-05-12 09:00:11,241 - INFO - 第 2 页获取到 100 条记录
2025-05-12 09:00:11,241 - INFO - Request Parameters - Page 3:
2025-05-12 09:00:11,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:11,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:11,775 - INFO - Response - Page 3:
2025-05-12 09:00:11,975 - INFO - 第 3 页获取到 100 条记录
2025-05-12 09:00:11,975 - INFO - Request Parameters - Page 4:
2025-05-12 09:00:11,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:11,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:12,473 - INFO - Response - Page 4:
2025-05-12 09:00:12,673 - INFO - 第 4 页获取到 100 条记录
2025-05-12 09:00:12,673 - INFO - Request Parameters - Page 5:
2025-05-12 09:00:12,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:12,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:13,126 - INFO - Response - Page 5:
2025-05-12 09:00:13,326 - INFO - 第 5 页获取到 100 条记录
2025-05-12 09:00:13,326 - INFO - Request Parameters - Page 6:
2025-05-12 09:00:13,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:13,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:13,787 - INFO - Response - Page 6:
2025-05-12 09:00:13,988 - INFO - 第 6 页获取到 100 条记录
2025-05-12 09:00:13,988 - INFO - Request Parameters - Page 7:
2025-05-12 09:00:13,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:13,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:14,513 - INFO - Response - Page 7:
2025-05-12 09:00:14,713 - INFO - 第 7 页获取到 70 条记录
2025-05-12 09:00:14,713 - INFO - 查询完成，共获取到 670 条记录
2025-05-12 09:00:14,713 - INFO - 获取到 670 条表单数据
2025-05-12 09:00:14,727 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-12 09:00:14,737 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 09:00:14,738 - INFO - 开始处理日期: 2025-03
2025-05-12 09:00:14,738 - INFO - Request Parameters - Page 1:
2025-05-12 09:00:14,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:14,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:15,274 - INFO - Response - Page 1:
2025-05-12 09:00:15,475 - INFO - 第 1 页获取到 100 条记录
2025-05-12 09:00:15,475 - INFO - Request Parameters - Page 2:
2025-05-12 09:00:15,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:15,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:15,970 - INFO - Response - Page 2:
2025-05-12 09:00:16,170 - INFO - 第 2 页获取到 100 条记录
2025-05-12 09:00:16,170 - INFO - Request Parameters - Page 3:
2025-05-12 09:00:16,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:16,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:16,698 - INFO - Response - Page 3:
2025-05-12 09:00:16,900 - INFO - 第 3 页获取到 100 条记录
2025-05-12 09:00:16,900 - INFO - Request Parameters - Page 4:
2025-05-12 09:00:16,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:16,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:17,326 - INFO - Response - Page 4:
2025-05-12 09:00:17,526 - INFO - 第 4 页获取到 100 条记录
2025-05-12 09:00:17,526 - INFO - Request Parameters - Page 5:
2025-05-12 09:00:17,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:17,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:18,035 - INFO - Response - Page 5:
2025-05-12 09:00:18,235 - INFO - 第 5 页获取到 100 条记录
2025-05-12 09:00:18,235 - INFO - Request Parameters - Page 6:
2025-05-12 09:00:18,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:18,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:18,661 - INFO - Response - Page 6:
2025-05-12 09:00:18,861 - INFO - 第 6 页获取到 100 条记录
2025-05-12 09:00:18,861 - INFO - Request Parameters - Page 7:
2025-05-12 09:00:18,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:18,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:19,323 - INFO - Response - Page 7:
2025-05-12 09:00:19,523 - INFO - 第 7 页获取到 61 条记录
2025-05-12 09:00:19,523 - INFO - 查询完成，共获取到 661 条记录
2025-05-12 09:00:19,523 - INFO - 获取到 661 条表单数据
2025-05-12 09:00:19,536 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-12 09:00:19,548 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 09:00:19,548 - INFO - 开始处理日期: 2025-04
2025-05-12 09:00:19,548 - INFO - Request Parameters - Page 1:
2025-05-12 09:00:19,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:19,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:20,073 - INFO - Response - Page 1:
2025-05-12 09:00:20,274 - INFO - 第 1 页获取到 100 条记录
2025-05-12 09:00:20,274 - INFO - Request Parameters - Page 2:
2025-05-12 09:00:20,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:20,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:20,822 - INFO - Response - Page 2:
2025-05-12 09:00:21,023 - INFO - 第 2 页获取到 100 条记录
2025-05-12 09:00:21,023 - INFO - Request Parameters - Page 3:
2025-05-12 09:00:21,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:21,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:21,604 - INFO - Response - Page 3:
2025-05-12 09:00:21,804 - INFO - 第 3 页获取到 100 条记录
2025-05-12 09:00:21,804 - INFO - Request Parameters - Page 4:
2025-05-12 09:00:21,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:21,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:22,345 - INFO - Response - Page 4:
2025-05-12 09:00:22,545 - INFO - 第 4 页获取到 100 条记录
2025-05-12 09:00:22,545 - INFO - Request Parameters - Page 5:
2025-05-12 09:00:22,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:22,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:23,006 - INFO - Response - Page 5:
2025-05-12 09:00:23,206 - INFO - 第 5 页获取到 100 条记录
2025-05-12 09:00:23,206 - INFO - Request Parameters - Page 6:
2025-05-12 09:00:23,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:23,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:23,704 - INFO - Response - Page 6:
2025-05-12 09:00:23,904 - INFO - 第 6 页获取到 100 条记录
2025-05-12 09:00:23,904 - INFO - Request Parameters - Page 7:
2025-05-12 09:00:23,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:23,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:24,293 - INFO - Response - Page 7:
2025-05-12 09:00:24,493 - INFO - 第 7 页获取到 54 条记录
2025-05-12 09:00:24,493 - INFO - 查询完成，共获取到 654 条记录
2025-05-12 09:00:24,493 - INFO - 获取到 654 条表单数据
2025-05-12 09:00:24,505 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-12 09:00:24,507 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-05-12 09:00:24,896 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-05-12 09:00:24,896 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43622.63, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 321666.78, 'new_value': 363385.68}, {'field': 'total_amount', 'old_value': 365289.41, 'new_value': 363385.68}]
2025-05-12 09:00:24,897 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-05-12 09:00:25,314 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-05-12 09:00:25,314 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1581.34, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 82325.86, 'new_value': 83848.2}, {'field': 'total_amount', 'old_value': 83907.2, 'new_value': 83848.2}, {'field': 'order_count', 'old_value': 3326, 'new_value': 3396}]
2025-05-12 09:00:25,314 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-05-12 09:00:25,716 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-05-12 09:00:25,717 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11712.5, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 17433.7, 'new_value': 30016.2}, {'field': 'total_amount', 'old_value': 29146.2, 'new_value': 30016.2}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-05-12 09:00:25,718 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-05-12 09:00:26,173 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-05-12 09:00:26,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96826.48, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 187395.51, 'new_value': 284320.47}, {'field': 'total_amount', 'old_value': 284221.99, 'new_value': 284320.47}]
2025-05-12 09:00:26,174 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-05-12 09:00:26,650 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-05-12 09:00:26,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62687.07, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 26179.67, 'new_value': 88909.16}, {'field': 'total_amount', 'old_value': 88866.74, 'new_value': 88909.16}]
2025-05-12 09:00:26,653 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-05-12 09:00:27,076 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-05-12 09:00:27,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135482.0, 'new_value': 135854.0}, {'field': 'total_amount', 'old_value': 135482.0, 'new_value': 135854.0}]
2025-05-12 09:00:27,078 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-05-12 09:00:27,521 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-05-12 09:00:27,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32636.38, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 49335.92, 'new_value': 81468.34}, {'field': 'total_amount', 'old_value': 81972.3, 'new_value': 81468.34}]
2025-05-12 09:00:27,521 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-05-12 09:00:27,999 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-05-12 09:00:27,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 517318.26, 'new_value': 517564.9}, {'field': 'total_amount', 'old_value': 517318.26, 'new_value': 517564.9}]
2025-05-12 09:00:28,002 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-05-12 09:00:28,669 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-05-12 09:00:28,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11162.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 388817.0, 'new_value': 328107.86}, {'field': 'total_amount', 'old_value': 399979.0, 'new_value': 328107.86}]
2025-05-12 09:00:28,669 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-05-12 09:00:29,134 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-05-12 09:00:29,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82211.62, 'new_value': 100311.84}, {'field': 'total_amount', 'old_value': 82211.62, 'new_value': 100311.84}, {'field': 'order_count', 'old_value': 220, 'new_value': 271}]
2025-05-12 09:00:29,136 - INFO - 日期 2025-04 处理完成 - 更新: 10 条，插入: 0 条，错误: 0 条
2025-05-12 09:00:29,137 - INFO - 开始处理日期: 2025-05
2025-05-12 09:00:29,137 - INFO - Request Parameters - Page 1:
2025-05-12 09:00:29,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:29,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:29,590 - INFO - Response - Page 1:
2025-05-12 09:00:29,791 - INFO - 第 1 页获取到 100 条记录
2025-05-12 09:00:29,791 - INFO - Request Parameters - Page 2:
2025-05-12 09:00:29,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:29,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:30,317 - INFO - Response - Page 2:
2025-05-12 09:00:30,518 - INFO - 第 2 页获取到 100 条记录
2025-05-12 09:00:30,518 - INFO - Request Parameters - Page 3:
2025-05-12 09:00:30,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:30,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:31,040 - INFO - Response - Page 3:
2025-05-12 09:00:31,240 - INFO - 第 3 页获取到 100 条记录
2025-05-12 09:00:31,240 - INFO - Request Parameters - Page 4:
2025-05-12 09:00:31,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:31,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:31,675 - INFO - Response - Page 4:
2025-05-12 09:00:31,875 - INFO - 第 4 页获取到 100 条记录
2025-05-12 09:00:31,875 - INFO - Request Parameters - Page 5:
2025-05-12 09:00:31,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:31,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:32,321 - INFO - Response - Page 5:
2025-05-12 09:00:32,521 - INFO - 第 5 页获取到 100 条记录
2025-05-12 09:00:32,521 - INFO - Request Parameters - Page 6:
2025-05-12 09:00:32,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:32,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:33,006 - INFO - Response - Page 6:
2025-05-12 09:00:33,206 - INFO - 第 6 页获取到 100 条记录
2025-05-12 09:00:33,206 - INFO - Request Parameters - Page 7:
2025-05-12 09:00:33,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 09:00:33,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 09:00:33,564 - INFO - Response - Page 7:
2025-05-12 09:00:33,766 - INFO - 第 7 页获取到 24 条记录
2025-05-12 09:00:33,766 - INFO - 查询完成，共获取到 624 条记录
2025-05-12 09:00:33,766 - INFO - 获取到 624 条表单数据
2025-05-12 09:00:33,778 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-12 09:00:33,778 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-12 09:00:34,306 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-12 09:00:34,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123000.0, 'new_value': 155600.0}, {'field': 'total_amount', 'old_value': 123000.0, 'new_value': 155600.0}]
2025-05-12 09:00:34,306 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-12 09:00:34,836 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-12 09:00:34,836 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 795.0, 'new_value': 865.0}, {'field': 'offline_amount', 'old_value': 22105.0, 'new_value': 23375.0}, {'field': 'total_amount', 'old_value': 22900.0, 'new_value': 24240.0}, {'field': 'order_count', 'old_value': 283, 'new_value': 301}]
2025-05-12 09:00:34,836 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-12 09:00:35,299 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-12 09:00:35,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156517.0, 'new_value': 167145.0}, {'field': 'total_amount', 'old_value': 156517.0, 'new_value': 167145.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 119}]
2025-05-12 09:00:35,300 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-12 09:00:35,761 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-12 09:00:35,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 305952.08, 'new_value': 354385.08}, {'field': 'total_amount', 'old_value': 305952.08, 'new_value': 354385.08}, {'field': 'order_count', 'old_value': 922, 'new_value': 1057}]
2025-05-12 09:00:35,762 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-12 09:00:36,351 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-12 09:00:36,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10394.32, 'new_value': 11568.32}, {'field': 'offline_amount', 'old_value': 6197.13, 'new_value': 6971.13}, {'field': 'total_amount', 'old_value': 16591.45, 'new_value': 18539.45}, {'field': 'order_count', 'old_value': 814, 'new_value': 906}]
2025-05-12 09:00:36,351 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-12 09:00:36,861 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-12 09:00:36,862 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114020.0, 'new_value': 133040.0}, {'field': 'total_amount', 'old_value': 114020.0, 'new_value': 133040.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 70}]
2025-05-12 09:00:36,862 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-12 09:00:37,483 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-12 09:00:37,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47653.0, 'new_value': 53972.0}, {'field': 'total_amount', 'old_value': 47653.0, 'new_value': 53972.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 59}]
2025-05-12 09:00:37,484 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-12 09:00:38,017 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-12 09:00:38,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67387.0, 'new_value': 85493.0}, {'field': 'total_amount', 'old_value': 67387.0, 'new_value': 85493.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 30}]
2025-05-12 09:00:38,017 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-12 09:00:38,497 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-12 09:00:38,497 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23025.69, 'new_value': 25621.07}, {'field': 'offline_amount', 'old_value': 45881.8, 'new_value': 50656.15}, {'field': 'total_amount', 'old_value': 68907.49, 'new_value': 76277.22}, {'field': 'order_count', 'old_value': 877, 'new_value': 954}]
2025-05-12 09:00:38,498 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-12 09:00:38,938 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-12 09:00:38,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10401.14, 'new_value': 11063.77}, {'field': 'offline_amount', 'old_value': 11907.36, 'new_value': 13261.73}, {'field': 'total_amount', 'old_value': 22308.5, 'new_value': 24325.5}, {'field': 'order_count', 'old_value': 1056, 'new_value': 1141}]
2025-05-12 09:00:38,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-12 09:00:39,351 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-12 09:00:39,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31880.0, 'new_value': 35360.0}, {'field': 'total_amount', 'old_value': 31880.0, 'new_value': 35360.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-12 09:00:39,351 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-12 09:00:39,787 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-12 09:00:39,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160807.2, 'new_value': 180526.3}, {'field': 'total_amount', 'old_value': 275826.9, 'new_value': 295546.0}, {'field': 'order_count', 'old_value': 1636, 'new_value': 1789}]
2025-05-12 09:00:39,788 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-12 09:00:40,207 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-12 09:00:40,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41617.0, 'new_value': 44545.0}, {'field': 'total_amount', 'old_value': 41617.0, 'new_value': 44545.0}, {'field': 'order_count', 'old_value': 2261, 'new_value': 2422}]
2025-05-12 09:00:40,207 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-12 09:00:40,722 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-12 09:00:40,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30030.0, 'new_value': 35830.0}, {'field': 'total_amount', 'old_value': 30030.0, 'new_value': 35830.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 59}]
2025-05-12 09:00:40,722 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-05-12 09:00:41,180 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-05-12 09:00:41,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30080.0, 'new_value': 33680.0}, {'field': 'total_amount', 'old_value': 30080.0, 'new_value': 33680.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-12 09:00:41,180 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-12 09:00:41,627 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-12 09:00:41,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164276.0, 'new_value': 176772.0}, {'field': 'total_amount', 'old_value': 164276.0, 'new_value': 176772.0}, {'field': 'order_count', 'old_value': 177, 'new_value': 197}]
2025-05-12 09:00:41,628 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-12 09:00:42,028 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-12 09:00:42,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53000.1, 'new_value': 60629.02}, {'field': 'total_amount', 'old_value': 53000.1, 'new_value': 60629.02}, {'field': 'order_count', 'old_value': 596, 'new_value': 669}]
2025-05-12 09:00:42,029 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-12 09:00:42,477 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-12 09:00:42,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 358999.7, 'new_value': 387481.2}, {'field': 'total_amount', 'old_value': 360257.1, 'new_value': 388738.6}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-05-12 09:00:42,477 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-12 09:00:42,939 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-12 09:00:42,939 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2931.0, 'new_value': 3270.0}, {'field': 'offline_amount', 'old_value': 10232.0, 'new_value': 11211.0}, {'field': 'total_amount', 'old_value': 13163.0, 'new_value': 14481.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 36}]
2025-05-12 09:00:42,939 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-12 09:00:43,385 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-12 09:00:43,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79734.0, 'new_value': 90520.0}, {'field': 'total_amount', 'old_value': 79734.0, 'new_value': 90520.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 164}]
2025-05-12 09:00:43,385 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-12 09:00:44,016 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-12 09:00:44,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92225.1, 'new_value': 97871.42}, {'field': 'total_amount', 'old_value': 92225.1, 'new_value': 97871.42}, {'field': 'order_count', 'old_value': 89, 'new_value': 105}]
2025-05-12 09:00:44,017 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-12 09:00:44,446 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-12 09:00:44,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86134.96, 'new_value': 97650.24}, {'field': 'offline_amount', 'old_value': 14886.97, 'new_value': 16871.24}, {'field': 'total_amount', 'old_value': 101021.93, 'new_value': 114521.48}, {'field': 'order_count', 'old_value': 367, 'new_value': 404}]
2025-05-12 09:00:44,446 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-12 09:00:44,923 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-12 09:00:44,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92717.0, 'new_value': 99430.0}, {'field': 'offline_amount', 'old_value': 32769.61, 'new_value': 34787.66}, {'field': 'total_amount', 'old_value': 125486.61, 'new_value': 134217.66}, {'field': 'order_count', 'old_value': 750, 'new_value': 817}]
2025-05-12 09:00:44,923 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-12 09:00:45,397 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-12 09:00:45,397 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1000.0, 'new_value': 1118.0}, {'field': 'offline_amount', 'old_value': 3521.0, 'new_value': 4013.0}, {'field': 'total_amount', 'old_value': 4521.0, 'new_value': 5131.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 96}]
2025-05-12 09:00:45,397 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-12 09:00:45,825 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-12 09:00:45,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36812.33, 'new_value': 39779.04}, {'field': 'total_amount', 'old_value': 36812.33, 'new_value': 39779.04}, {'field': 'order_count', 'old_value': 941, 'new_value': 1016}]
2025-05-12 09:00:45,825 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-12 09:00:46,251 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-12 09:00:46,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73075.0, 'new_value': 88442.0}, {'field': 'total_amount', 'old_value': 73075.0, 'new_value': 88442.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 33}]
2025-05-12 09:00:46,252 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-12 09:00:46,701 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-12 09:00:46,702 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68543.0, 'new_value': 77192.0}, {'field': 'offline_amount', 'old_value': 29325.26, 'new_value': 33394.77}, {'field': 'total_amount', 'old_value': 97868.26, 'new_value': 110586.77}, {'field': 'order_count', 'old_value': 675, 'new_value': 762}]
2025-05-12 09:00:46,702 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-12 09:00:47,184 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-12 09:00:47,184 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5112.44, 'new_value': 5553.78}, {'field': 'offline_amount', 'old_value': 73797.38, 'new_value': 81582.99}, {'field': 'total_amount', 'old_value': 78909.82, 'new_value': 87136.77}, {'field': 'order_count', 'old_value': 875, 'new_value': 960}]
2025-05-12 09:00:47,185 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-12 09:00:47,626 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-12 09:00:47,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102981.0, 'new_value': 116568.0}, {'field': 'total_amount', 'old_value': 102981.0, 'new_value': 116568.0}, {'field': 'order_count', 'old_value': 482, 'new_value': 548}]
2025-05-12 09:00:47,626 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-12 09:00:48,256 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-12 09:00:48,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57993.0, 'new_value': 62714.0}, {'field': 'total_amount', 'old_value': 57993.0, 'new_value': 62714.0}, {'field': 'order_count', 'old_value': 1393, 'new_value': 1513}]
2025-05-12 09:00:48,256 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-12 09:00:48,686 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-12 09:00:48,686 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35078.97, 'new_value': 38857.46}, {'field': 'offline_amount', 'old_value': 354665.61, 'new_value': 426538.01}, {'field': 'total_amount', 'old_value': 389744.58, 'new_value': 465395.47}, {'field': 'order_count', 'old_value': 1278, 'new_value': 1532}]
2025-05-12 09:00:48,686 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-12 09:00:49,159 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-12 09:00:49,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11032.31, 'new_value': 12507.49}, {'field': 'total_amount', 'old_value': 11032.31, 'new_value': 12507.49}, {'field': 'order_count', 'old_value': 47, 'new_value': 52}]
2025-05-12 09:00:49,160 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-12 09:00:49,603 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-12 09:00:49,604 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62000.0, 'new_value': 67950.0}, {'field': 'total_amount', 'old_value': 62000.0, 'new_value': 67950.0}, {'field': 'order_count', 'old_value': 2234, 'new_value': 2442}]
2025-05-12 09:00:49,604 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-12 09:00:50,034 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-12 09:00:50,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 292596.54, 'new_value': 332111.47}, {'field': 'total_amount', 'old_value': 292596.54, 'new_value': 332111.47}, {'field': 'order_count', 'old_value': 2124, 'new_value': 2369}]
2025-05-12 09:00:50,034 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-12 09:00:50,453 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-12 09:00:50,454 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13362.0, 'new_value': 16103.0}, {'field': 'offline_amount', 'old_value': 210422.0, 'new_value': 229047.0}, {'field': 'total_amount', 'old_value': 223784.0, 'new_value': 245150.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 43}]
2025-05-12 09:00:50,454 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-12 09:00:50,939 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-12 09:00:50,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22486.0, 'new_value': 44986.0}, {'field': 'total_amount', 'old_value': 22486.0, 'new_value': 44986.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-12 09:00:50,940 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-12 09:00:51,453 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-12 09:00:51,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50798.08, 'new_value': 56606.4}, {'field': 'total_amount', 'old_value': 50798.08, 'new_value': 56606.4}, {'field': 'order_count', 'old_value': 1907, 'new_value': 2102}]
2025-05-12 09:00:51,454 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-12 09:00:51,993 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-12 09:00:51,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9375.54, 'new_value': 12255.18}, {'field': 'total_amount', 'old_value': 9375.54, 'new_value': 12255.18}, {'field': 'order_count', 'old_value': 15, 'new_value': 19}]
2025-05-12 09:00:51,993 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-12 09:00:52,498 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-12 09:00:52,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45716.0, 'new_value': 45914.0}, {'field': 'total_amount', 'old_value': 45716.0, 'new_value': 45914.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 73}]
2025-05-12 09:00:52,499 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-12 09:00:52,966 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-12 09:00:52,966 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6026.64, 'new_value': 29105.28}, {'field': 'offline_amount', 'old_value': 302367.0, 'new_value': 316383.0}, {'field': 'total_amount', 'old_value': 308393.64, 'new_value': 345488.28}, {'field': 'order_count', 'old_value': 301, 'new_value': 364}]
2025-05-12 09:00:52,966 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-12 09:00:53,440 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-12 09:00:53,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73443.66, 'new_value': 82727.83}, {'field': 'total_amount', 'old_value': 73443.66, 'new_value': 82727.83}, {'field': 'order_count', 'old_value': 378, 'new_value': 420}]
2025-05-12 09:00:53,441 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-12 09:00:53,914 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-12 09:00:53,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58296.0, 'new_value': 68795.0}, {'field': 'total_amount', 'old_value': 58296.0, 'new_value': 68795.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-12 09:00:53,914 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-12 09:00:54,476 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-12 09:00:54,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 837203.3, 'new_value': 976749.1}, {'field': 'total_amount', 'old_value': 837203.3, 'new_value': 976749.1}, {'field': 'order_count', 'old_value': 1426, 'new_value': 1664}]
2025-05-12 09:00:54,476 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-12 09:00:54,931 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-12 09:00:54,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60460.15, 'new_value': 67822.65}, {'field': 'offline_amount', 'old_value': 35721.0, 'new_value': 40687.0}, {'field': 'total_amount', 'old_value': 96181.15, 'new_value': 108509.65}, {'field': 'order_count', 'old_value': 517, 'new_value': 587}]
2025-05-12 09:00:54,932 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-12 09:00:55,400 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-12 09:00:55,400 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18290.0, 'new_value': 23594.0}, {'field': 'total_amount', 'old_value': 46936.0, 'new_value': 52240.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-12 09:00:55,401 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-12 09:00:55,877 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-12 09:00:55,877 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11716.0, 'new_value': 13224.0}, {'field': 'total_amount', 'old_value': 11716.0, 'new_value': 13224.0}, {'field': 'order_count', 'old_value': 202, 'new_value': 228}]
2025-05-12 09:00:55,877 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-12 09:00:56,343 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-12 09:00:56,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2619.46, 'new_value': 2889.86}, {'field': 'offline_amount', 'old_value': 6785.29, 'new_value': 7471.48}, {'field': 'total_amount', 'old_value': 9404.75, 'new_value': 10361.34}, {'field': 'order_count', 'old_value': 323, 'new_value': 357}]
2025-05-12 09:00:56,343 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-12 09:00:56,773 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-12 09:00:56,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69428.82, 'new_value': 76383.05}, {'field': 'offline_amount', 'old_value': 54267.2, 'new_value': 58725.76}, {'field': 'total_amount', 'old_value': 123696.02, 'new_value': 135108.81}, {'field': 'order_count', 'old_value': 1057, 'new_value': 1166}]
2025-05-12 09:00:56,773 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-12 09:00:57,263 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-12 09:00:57,263 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163139.9, 'new_value': 183256.9}, {'field': 'total_amount', 'old_value': 192213.9, 'new_value': 212330.9}, {'field': 'order_count', 'old_value': 239, 'new_value': 263}]
2025-05-12 09:00:57,263 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-12 09:00:57,700 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-12 09:00:57,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16504.0, 'new_value': 17745.0}, {'field': 'total_amount', 'old_value': 16504.0, 'new_value': 17745.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 48}]
2025-05-12 09:00:57,701 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-12 09:00:58,198 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-12 09:00:58,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28349.0, 'new_value': 30051.0}, {'field': 'total_amount', 'old_value': 28349.0, 'new_value': 30051.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-12 09:00:58,198 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-12 09:00:58,706 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-12 09:00:58,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78170.0, 'new_value': 89729.0}, {'field': 'total_amount', 'old_value': 97616.0, 'new_value': 109175.0}, {'field': 'order_count', 'old_value': 1964, 'new_value': 2201}]
2025-05-12 09:00:58,706 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-12 09:00:59,198 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-12 09:00:59,199 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5906.16, 'new_value': 7062.58}, {'field': 'offline_amount', 'old_value': 119409.0, 'new_value': 137861.8}, {'field': 'total_amount', 'old_value': 125315.16, 'new_value': 144924.38}, {'field': 'order_count', 'old_value': 892, 'new_value': 1000}]
2025-05-12 09:00:59,199 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-12 09:00:59,784 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-12 09:00:59,784 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13000.0, 'new_value': 26000.0}, {'field': 'total_amount', 'old_value': 85082.0, 'new_value': 98082.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-05-12 09:00:59,784 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-12 09:01:00,233 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-12 09:01:00,233 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62607.64, 'new_value': 71745.79}, {'field': 'offline_amount', 'old_value': 59625.45, 'new_value': 69238.45}, {'field': 'total_amount', 'old_value': 122233.09, 'new_value': 140984.24}, {'field': 'order_count', 'old_value': 1189, 'new_value': 1359}]
2025-05-12 09:01:00,234 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-12 09:01:00,695 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-12 09:01:00,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30423.8, 'new_value': 32423.8}, {'field': 'offline_amount', 'old_value': 888.35, 'new_value': 1188.35}, {'field': 'total_amount', 'old_value': 31312.15, 'new_value': 33612.15}, {'field': 'order_count', 'old_value': 80, 'new_value': 89}]
2025-05-12 09:01:00,695 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-12 09:01:01,124 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-12 09:01:01,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33351.87, 'new_value': 37795.98}, {'field': 'total_amount', 'old_value': 33351.87, 'new_value': 37795.98}, {'field': 'order_count', 'old_value': 882, 'new_value': 1014}]
2025-05-12 09:01:01,125 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-12 09:01:01,626 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-12 09:01:01,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123942.0, 'new_value': 130742.0}, {'field': 'total_amount', 'old_value': 123942.0, 'new_value': 130742.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-12 09:01:01,627 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-12 09:01:02,189 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-12 09:01:02,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128312.73, 'new_value': 155229.65}, {'field': 'total_amount', 'old_value': 128312.73, 'new_value': 155229.65}, {'field': 'order_count', 'old_value': 263, 'new_value': 286}]
2025-05-12 09:01:02,190 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-12 09:01:02,606 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-12 09:01:02,606 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6813.04, 'new_value': 7299.64}, {'field': 'offline_amount', 'old_value': 192529.55, 'new_value': 223803.99}, {'field': 'total_amount', 'old_value': 199342.59, 'new_value': 231103.63}, {'field': 'order_count', 'old_value': 778, 'new_value': 893}]
2025-05-12 09:01:02,606 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-12 09:01:03,085 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-12 09:01:03,085 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39975.0, 'new_value': 42409.0}, {'field': 'offline_amount', 'old_value': 22983.25, 'new_value': 32946.12}, {'field': 'total_amount', 'old_value': 62958.25, 'new_value': 75355.12}, {'field': 'order_count', 'old_value': 78, 'new_value': 91}]
2025-05-12 09:01:03,086 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-12 09:01:03,481 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-12 09:01:03,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5770.0, 'new_value': 6620.0}, {'field': 'total_amount', 'old_value': 5770.0, 'new_value': 6620.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-12 09:01:03,481 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-12 09:01:03,965 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-12 09:01:03,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26991.0, 'new_value': 27390.0}, {'field': 'total_amount', 'old_value': 26991.0, 'new_value': 27390.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-12 09:01:03,966 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-12 09:01:04,555 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-12 09:01:04,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27777.44, 'new_value': 35866.36}, {'field': 'total_amount', 'old_value': 27777.44, 'new_value': 35866.36}, {'field': 'order_count', 'old_value': 32, 'new_value': 35}]
2025-05-12 09:01:04,556 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-12 09:01:04,984 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-12 09:01:04,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2838.0, 'new_value': 2968.0}, {'field': 'total_amount', 'old_value': 2838.0, 'new_value': 2968.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-12 09:01:04,984 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-12 09:01:05,445 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-12 09:01:05,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3273.02, 'new_value': 3668.7}, {'field': 'offline_amount', 'old_value': 16968.0, 'new_value': 17622.0}, {'field': 'total_amount', 'old_value': 20241.02, 'new_value': 21290.7}, {'field': 'order_count', 'old_value': 86, 'new_value': 98}]
2025-05-12 09:01:05,446 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-12 09:01:06,014 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-12 09:01:06,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22906.2, 'new_value': 24239.02}, {'field': 'total_amount', 'old_value': 23103.0, 'new_value': 24435.82}, {'field': 'order_count', 'old_value': 196, 'new_value': 216}]
2025-05-12 09:01:06,015 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-12 09:01:06,462 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-12 09:01:06,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2271.0, 'new_value': 2421.0}, {'field': 'offline_amount', 'old_value': 11153.4, 'new_value': 11861.6}, {'field': 'total_amount', 'old_value': 13424.4, 'new_value': 14282.6}, {'field': 'order_count', 'old_value': 534, 'new_value': 576}]
2025-05-12 09:01:06,462 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-12 09:01:06,933 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-12 09:01:06,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49349.02, 'new_value': 52400.02}, {'field': 'total_amount', 'old_value': 49349.02, 'new_value': 52400.02}, {'field': 'order_count', 'old_value': 160, 'new_value': 169}]
2025-05-12 09:01:06,934 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-12 09:01:07,427 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-12 09:01:07,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88748.6, 'new_value': 100828.6}, {'field': 'total_amount', 'old_value': 88748.6, 'new_value': 100828.6}, {'field': 'order_count', 'old_value': 321, 'new_value': 357}]
2025-05-12 09:01:07,427 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-12 09:01:07,901 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-12 09:01:07,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114504.6, 'new_value': 127635.69}, {'field': 'total_amount', 'old_value': 114504.6, 'new_value': 127635.69}, {'field': 'order_count', 'old_value': 3042, 'new_value': 3381}]
2025-05-12 09:01:07,901 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-12 09:01:08,372 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-12 09:01:08,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67426.95, 'new_value': 76494.78}, {'field': 'total_amount', 'old_value': 67426.95, 'new_value': 76494.78}, {'field': 'order_count', 'old_value': 318, 'new_value': 358}]
2025-05-12 09:01:08,372 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-12 09:01:08,791 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-12 09:01:08,791 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9473.57, 'new_value': 10432.52}, {'field': 'offline_amount', 'old_value': 17119.46, 'new_value': 18622.46}, {'field': 'total_amount', 'old_value': 26593.03, 'new_value': 29054.98}, {'field': 'order_count', 'old_value': 961, 'new_value': 1048}]
2025-05-12 09:01:08,792 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-12 09:01:09,242 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-12 09:01:09,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26845.0, 'new_value': 31438.0}, {'field': 'total_amount', 'old_value': 29253.0, 'new_value': 33846.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 129}]
2025-05-12 09:01:09,242 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-12 09:01:09,693 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-12 09:01:09,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34309.0, 'new_value': 36485.0}, {'field': 'total_amount', 'old_value': 34309.0, 'new_value': 36485.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-12 09:01:09,693 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-12 09:01:10,242 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-12 09:01:10,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11433.7, 'new_value': 13581.7}, {'field': 'offline_amount', 'old_value': 26345.82, 'new_value': 30490.7}, {'field': 'total_amount', 'old_value': 37779.52, 'new_value': 44072.4}, {'field': 'order_count', 'old_value': 414, 'new_value': 482}]
2025-05-12 09:01:10,242 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-12 09:01:10,664 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-12 09:01:10,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45115.5, 'new_value': 50805.5}, {'field': 'offline_amount', 'old_value': 50922.38, 'new_value': 58530.63}, {'field': 'total_amount', 'old_value': 96037.88, 'new_value': 109336.13}, {'field': 'order_count', 'old_value': 647, 'new_value': 741}]
2025-05-12 09:01:10,665 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-12 09:01:11,102 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-12 09:01:11,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6964.83, 'new_value': 7885.89}, {'field': 'offline_amount', 'old_value': 15570.81, 'new_value': 16567.77}, {'field': 'total_amount', 'old_value': 22535.64, 'new_value': 24453.66}, {'field': 'order_count', 'old_value': 1178, 'new_value': 1290}]
2025-05-12 09:01:11,103 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-12 09:01:11,587 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-12 09:01:11,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 349.0}, {'field': 'offline_amount', 'old_value': 24295.0, 'new_value': 25845.0}, {'field': 'total_amount', 'old_value': 24295.0, 'new_value': 26194.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 45}]
2025-05-12 09:01:11,588 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-12 09:01:12,005 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-12 09:01:12,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26569.0, 'new_value': 28937.0}, {'field': 'total_amount', 'old_value': 26569.0, 'new_value': 28937.0}, {'field': 'order_count', 'old_value': 185, 'new_value': 198}]
2025-05-12 09:01:12,005 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-12 09:01:12,582 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-12 09:01:12,582 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22277.0, 'new_value': 35619.0}, {'field': 'offline_amount', 'old_value': 207888.0, 'new_value': 233332.0}, {'field': 'total_amount', 'old_value': 230165.0, 'new_value': 268951.0}, {'field': 'order_count', 'old_value': 1805, 'new_value': 2071}]
2025-05-12 09:01:12,582 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-12 09:01:13,055 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-12 09:01:13,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55046.14, 'new_value': 61278.33}, {'field': 'total_amount', 'old_value': 55046.14, 'new_value': 61278.33}, {'field': 'order_count', 'old_value': 1575, 'new_value': 1753}]
2025-05-12 09:01:13,055 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-12 09:01:13,521 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-12 09:01:13,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50109.89, 'new_value': 60522.1}, {'field': 'offline_amount', 'old_value': 109462.12, 'new_value': 118559.73}, {'field': 'total_amount', 'old_value': 159572.01, 'new_value': 179081.83}, {'field': 'order_count', 'old_value': 1754, 'new_value': 2032}]
2025-05-12 09:01:13,521 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-12 09:01:13,997 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-12 09:01:13,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18279.98, 'new_value': 19207.58}, {'field': 'total_amount', 'old_value': 18279.98, 'new_value': 19207.58}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-12 09:01:13,998 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-12 09:01:14,424 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-12 09:01:14,424 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85164.0, 'new_value': 92952.0}, {'field': 'total_amount', 'old_value': 85164.0, 'new_value': 92952.0}, {'field': 'order_count', 'old_value': 7097, 'new_value': 7746}]
2025-05-12 09:01:14,424 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-12 09:01:14,863 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-12 09:01:14,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23168.4, 'new_value': 25433.4}, {'field': 'total_amount', 'old_value': 23168.4, 'new_value': 25433.4}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-05-12 09:01:14,864 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-12 09:01:15,325 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-12 09:01:15,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18608.95, 'new_value': 20686.55}, {'field': 'total_amount', 'old_value': 18608.95, 'new_value': 20686.55}, {'field': 'order_count', 'old_value': 817, 'new_value': 915}]
2025-05-12 09:01:15,326 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-12 09:01:15,845 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-12 09:01:15,845 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62818.6, 'new_value': 72725.7}, {'field': 'total_amount', 'old_value': 106856.45, 'new_value': 116763.55}, {'field': 'order_count', 'old_value': 2673, 'new_value': 2943}]
2025-05-12 09:01:15,845 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-12 09:01:16,316 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-12 09:01:16,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11727.0, 'new_value': 13595.0}, {'field': 'total_amount', 'old_value': 11727.0, 'new_value': 13595.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 140}]
2025-05-12 09:01:16,316 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-12 09:01:16,691 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-12 09:01:16,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21593.72, 'new_value': 24318.46}, {'field': 'offline_amount', 'old_value': 14227.66, 'new_value': 15725.15}, {'field': 'total_amount', 'old_value': 35821.38, 'new_value': 40043.61}, {'field': 'order_count', 'old_value': 2000, 'new_value': 2220}]
2025-05-12 09:01:16,692 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-12 09:01:17,153 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-12 09:01:17,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29075.0, 'new_value': 29659.0}, {'field': 'total_amount', 'old_value': 29075.0, 'new_value': 29659.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-12 09:01:17,153 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-12 09:01:17,664 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-12 09:01:17,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9435.48, 'new_value': 9947.38}, {'field': 'total_amount', 'old_value': 9435.48, 'new_value': 9947.38}, {'field': 'order_count', 'old_value': 85, 'new_value': 90}]
2025-05-12 09:01:17,664 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-12 09:01:18,087 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-12 09:01:18,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183809.92, 'new_value': 205822.63}, {'field': 'total_amount', 'old_value': 183809.92, 'new_value': 205822.63}, {'field': 'order_count', 'old_value': 657, 'new_value': 734}]
2025-05-12 09:01:18,087 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-12 09:01:18,473 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-12 09:01:18,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118573.2, 'new_value': 137990.9}, {'field': 'total_amount', 'old_value': 118573.2, 'new_value': 137990.9}, {'field': 'order_count', 'old_value': 3162, 'new_value': 3547}]
2025-05-12 09:01:18,473 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-12 09:01:18,944 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-12 09:01:18,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15757.36, 'new_value': 17365.93}, {'field': 'total_amount', 'old_value': 15757.36, 'new_value': 17365.93}, {'field': 'order_count', 'old_value': 2005, 'new_value': 2207}]
2025-05-12 09:01:18,944 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-12 09:01:19,408 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-12 09:01:19,408 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9971.89, 'new_value': 11156.28}, {'field': 'offline_amount', 'old_value': 14744.4, 'new_value': 15947.6}, {'field': 'total_amount', 'old_value': 24716.29, 'new_value': 27103.88}, {'field': 'order_count', 'old_value': 1076, 'new_value': 1190}]
2025-05-12 09:01:19,408 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-12 09:01:19,878 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-12 09:01:19,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21170.0, 'new_value': 28474.0}, {'field': 'total_amount', 'old_value': 26371.0, 'new_value': 33675.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 109}]
2025-05-12 09:01:19,879 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-12 09:01:20,317 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-12 09:01:20,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 612298.16, 'new_value': 727168.4}, {'field': 'offline_amount', 'old_value': 134066.3, 'new_value': 136815.3}, {'field': 'total_amount', 'old_value': 746364.46, 'new_value': 863983.7}, {'field': 'order_count', 'old_value': 2659, 'new_value': 3009}]
2025-05-12 09:01:20,317 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-12 09:01:20,751 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-12 09:01:20,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89899.0, 'new_value': 149715.0}, {'field': 'total_amount', 'old_value': 89899.0, 'new_value': 149715.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 30}]
2025-05-12 09:01:20,751 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-12 09:01:21,180 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-12 09:01:21,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209524.12, 'new_value': 233373.04}, {'field': 'total_amount', 'old_value': 209524.12, 'new_value': 233373.04}, {'field': 'order_count', 'old_value': 1057, 'new_value': 1174}]
2025-05-12 09:01:21,181 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-12 09:01:21,645 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-12 09:01:21,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117636.0, 'new_value': 118435.0}, {'field': 'total_amount', 'old_value': 117636.0, 'new_value': 118435.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-05-12 09:01:21,646 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-12 09:01:22,086 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-12 09:01:22,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263468.16, 'new_value': 294662.08}, {'field': 'total_amount', 'old_value': 263995.17, 'new_value': 295189.09}, {'field': 'order_count', 'old_value': 598, 'new_value': 673}]
2025-05-12 09:01:22,087 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-12 09:01:22,518 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-12 09:01:22,518 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66510.0, 'new_value': 75370.0}, {'field': 'total_amount', 'old_value': 66510.0, 'new_value': 75370.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-12 09:01:22,518 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-12 09:01:23,004 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-12 09:01:23,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 598262.0, 'new_value': 685942.0}, {'field': 'total_amount', 'old_value': 598262.0, 'new_value': 685942.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 78}]
2025-05-12 09:01:23,004 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-12 09:01:23,542 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-12 09:01:23,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23288.9, 'new_value': 23351.79}, {'field': 'offline_amount', 'old_value': 19191.57, 'new_value': 27439.72}, {'field': 'total_amount', 'old_value': 42480.47, 'new_value': 50791.51}, {'field': 'order_count', 'old_value': 137, 'new_value': 167}]
2025-05-12 09:01:23,542 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-12 09:01:24,042 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-12 09:01:24,042 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 158249.32, 'new_value': 172171.22}, {'field': 'offline_amount', 'old_value': 5154.0, 'new_value': 5370.0}, {'field': 'total_amount', 'old_value': 163403.32, 'new_value': 177541.22}, {'field': 'order_count', 'old_value': 1226, 'new_value': 1344}]
2025-05-12 09:01:24,042 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-12 09:01:24,492 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-12 09:01:24,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7891.0, 'new_value': 9429.0}, {'field': 'total_amount', 'old_value': 7891.0, 'new_value': 9429.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 48}]
2025-05-12 09:01:24,492 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-12 09:01:24,935 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-12 09:01:24,936 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8790.9, 'new_value': 10185.3}, {'field': 'total_amount', 'old_value': 8790.9, 'new_value': 10185.3}, {'field': 'order_count', 'old_value': 303, 'new_value': 335}]
2025-05-12 09:01:24,936 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-12 09:01:25,363 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-12 09:01:25,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4047.5, 'new_value': 4300.5}, {'field': 'offline_amount', 'old_value': 17038.6, 'new_value': 19054.1}, {'field': 'total_amount', 'old_value': 21086.1, 'new_value': 23354.6}, {'field': 'order_count', 'old_value': 224, 'new_value': 254}]
2025-05-12 09:01:25,363 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-12 09:01:25,848 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-12 09:01:25,849 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6585.0, 'new_value': 7135.0}, {'field': 'total_amount', 'old_value': 19732.0, 'new_value': 20282.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 42}]
2025-05-12 09:01:25,849 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-12 09:01:26,349 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-12 09:01:26,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11913.43, 'new_value': 12890.86}, {'field': 'total_amount', 'old_value': 11913.43, 'new_value': 12890.86}, {'field': 'order_count', 'old_value': 433, 'new_value': 472}]
2025-05-12 09:01:26,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-12 09:01:26,884 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-12 09:01:26,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315532.56, 'new_value': 356434.68}, {'field': 'total_amount', 'old_value': 315532.56, 'new_value': 356434.68}, {'field': 'order_count', 'old_value': 2282, 'new_value': 2530}]
2025-05-12 09:01:26,884 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-12 09:01:27,338 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-12 09:01:27,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 431365.0, 'new_value': 473653.0}, {'field': 'total_amount', 'old_value': 431365.0, 'new_value': 473653.0}, {'field': 'order_count', 'old_value': 1956, 'new_value': 2137}]
2025-05-12 09:01:27,338 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-12 09:01:27,775 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-12 09:01:27,775 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3432.0, 'new_value': 3730.0}, {'field': 'total_amount', 'old_value': 3432.0, 'new_value': 3730.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-12 09:01:27,775 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-12 09:01:28,216 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-12 09:01:28,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40419.0, 'new_value': 45649.0}, {'field': 'total_amount', 'old_value': 40419.0, 'new_value': 45649.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 117}]
2025-05-12 09:01:28,217 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-12 09:01:28,606 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-12 09:01:28,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85132.5, 'new_value': 94360.5}, {'field': 'total_amount', 'old_value': 85132.5, 'new_value': 94360.5}, {'field': 'order_count', 'old_value': 463, 'new_value': 514}]
2025-05-12 09:01:28,607 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-12 09:01:29,059 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-12 09:01:29,060 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35665.42, 'new_value': 38636.07}, {'field': 'offline_amount', 'old_value': 45505.53, 'new_value': 50700.52}, {'field': 'total_amount', 'old_value': 81170.95, 'new_value': 89336.59}, {'field': 'order_count', 'old_value': 3314, 'new_value': 3654}]
2025-05-12 09:01:29,060 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-12 09:01:29,618 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-12 09:01:29,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27521.0, 'new_value': 29513.0}, {'field': 'total_amount', 'old_value': 27521.0, 'new_value': 29513.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 61}]
2025-05-12 09:01:29,619 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-12 09:01:30,139 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-12 09:01:30,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19254.0, 'new_value': 20131.0}, {'field': 'total_amount', 'old_value': 19254.0, 'new_value': 20131.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-05-12 09:01:30,139 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-12 09:01:30,781 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-12 09:01:30,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24294.68, 'new_value': 27792.44}, {'field': 'offline_amount', 'old_value': 21820.59, 'new_value': 25171.33}, {'field': 'total_amount', 'old_value': 46115.27, 'new_value': 52963.77}, {'field': 'order_count', 'old_value': 914, 'new_value': 1035}]
2025-05-12 09:01:30,782 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-12 09:01:31,228 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-12 09:01:31,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6000.0, 'new_value': 6749.0}, {'field': 'offline_amount', 'old_value': 113991.0, 'new_value': 135671.0}, {'field': 'total_amount', 'old_value': 119991.0, 'new_value': 142420.0}, {'field': 'order_count', 'old_value': 600, 'new_value': 702}]
2025-05-12 09:01:31,228 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-12 09:01:31,740 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-12 09:01:31,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1230000.0, 'new_value': 1330000.0}, {'field': 'total_amount', 'old_value': 1230000.0, 'new_value': 1330000.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 268}]
2025-05-12 09:01:31,740 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-12 09:01:32,240 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-12 09:01:32,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45600.0, 'new_value': 54400.0}, {'field': 'total_amount', 'old_value': 48600.0, 'new_value': 57400.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-12 09:01:32,241 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-12 09:01:32,739 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-12 09:01:32,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327364.51, 'new_value': 377974.3}, {'field': 'total_amount', 'old_value': 327364.51, 'new_value': 377974.3}, {'field': 'order_count', 'old_value': 3665, 'new_value': 4132}]
2025-05-12 09:01:32,739 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-12 09:01:33,103 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-12 09:01:33,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192714.0, 'new_value': 225426.0}, {'field': 'total_amount', 'old_value': 192714.0, 'new_value': 225426.0}, {'field': 'order_count', 'old_value': 186, 'new_value': 213}]
2025-05-12 09:01:33,103 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-12 09:01:33,608 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-12 09:01:33,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119795.45, 'new_value': 140134.55}, {'field': 'offline_amount', 'old_value': 72800.67, 'new_value': 86475.21}, {'field': 'total_amount', 'old_value': 192596.12, 'new_value': 226609.76}, {'field': 'order_count', 'old_value': 1618, 'new_value': 2004}]
2025-05-12 09:01:33,609 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-12 09:01:34,066 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-12 09:01:34,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85799.94, 'new_value': 95200.94}, {'field': 'offline_amount', 'old_value': 6607.3, 'new_value': 7095.3}, {'field': 'total_amount', 'old_value': 92407.24, 'new_value': 102296.24}, {'field': 'order_count', 'old_value': 3813, 'new_value': 4414}]
2025-05-12 09:01:34,066 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-12 09:01:34,534 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-12 09:01:34,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420000.0, 'new_value': 430000.0}, {'field': 'total_amount', 'old_value': 420000.0, 'new_value': 430000.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 135}]
2025-05-12 09:01:34,535 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-12 09:01:35,093 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-12 09:01:35,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400000.0, 'new_value': 410000.0}, {'field': 'total_amount', 'old_value': 400000.0, 'new_value': 410000.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 134}]
2025-05-12 09:01:35,093 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-12 09:01:35,514 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-12 09:01:35,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2348674.0, 'new_value': 2448674.0}, {'field': 'total_amount', 'old_value': 2348674.0, 'new_value': 2448674.0}, {'field': 'order_count', 'old_value': 287, 'new_value': 288}]
2025-05-12 09:01:35,515 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-12 09:01:36,033 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-12 09:01:36,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34384.0, 'new_value': 38720.0}, {'field': 'offline_amount', 'old_value': 443716.0, 'new_value': 486980.0}, {'field': 'total_amount', 'old_value': 478100.0, 'new_value': 525700.0}, {'field': 'order_count', 'old_value': 10882, 'new_value': 11977}]
2025-05-12 09:01:36,034 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-12 09:01:36,444 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-12 09:01:36,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176683.14, 'new_value': 199376.37}, {'field': 'total_amount', 'old_value': 176683.14, 'new_value': 199376.37}, {'field': 'order_count', 'old_value': 604, 'new_value': 680}]
2025-05-12 09:01:36,444 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-12 09:01:36,836 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-12 09:01:36,836 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14474.0, 'new_value': 17940.0}, {'field': 'offline_amount', 'old_value': 85077.0, 'new_value': 98212.0}, {'field': 'total_amount', 'old_value': 99551.0, 'new_value': 116152.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 114}]
2025-05-12 09:01:36,836 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-12 09:01:37,253 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-12 09:01:37,253 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8771.6, 'new_value': 9734.96}, {'field': 'offline_amount', 'old_value': 7895.5, 'new_value': 8647.5}, {'field': 'total_amount', 'old_value': 16667.1, 'new_value': 18382.46}, {'field': 'order_count', 'old_value': 763, 'new_value': 835}]
2025-05-12 09:01:37,253 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-12 09:01:37,725 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-12 09:01:37,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79178.39, 'new_value': 87740.6}, {'field': 'total_amount', 'old_value': 79178.39, 'new_value': 87740.6}, {'field': 'order_count', 'old_value': 3888, 'new_value': 4400}]
2025-05-12 09:01:37,726 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-12 09:01:38,199 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-12 09:01:38,199 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73587.7, 'new_value': 80183.1}, {'field': 'total_amount', 'old_value': 73587.7, 'new_value': 80183.1}, {'field': 'order_count', 'old_value': 340, 'new_value': 373}]
2025-05-12 09:01:38,199 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-12 09:01:38,706 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-12 09:01:38,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83368.9, 'new_value': 88437.4}, {'field': 'total_amount', 'old_value': 83368.9, 'new_value': 88437.4}, {'field': 'order_count', 'old_value': 2292, 'new_value': 2436}]
2025-05-12 09:01:38,706 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-12 09:01:39,179 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-12 09:01:39,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6472.0, 'new_value': 7687.0}, {'field': 'total_amount', 'old_value': 10220.0, 'new_value': 11435.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 56}]
2025-05-12 09:01:39,180 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-12 09:01:39,636 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-12 09:01:39,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51010.8, 'new_value': 55055.2}, {'field': 'total_amount', 'old_value': 51010.8, 'new_value': 55055.2}, {'field': 'order_count', 'old_value': 234, 'new_value': 256}]
2025-05-12 09:01:39,636 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-12 09:01:40,204 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-12 09:01:40,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152971.75, 'new_value': 205724.87}, {'field': 'total_amount', 'old_value': 152971.75, 'new_value': 205724.87}, {'field': 'order_count', 'old_value': 808, 'new_value': 1066}]
2025-05-12 09:01:40,204 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-12 09:01:40,683 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-12 09:01:40,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36674.0, 'new_value': 41968.0}, {'field': 'total_amount', 'old_value': 36674.0, 'new_value': 41968.0}, {'field': 'order_count', 'old_value': 1684, 'new_value': 1956}]
2025-05-12 09:01:40,684 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-12 09:01:41,095 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-12 09:01:41,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43579.41, 'new_value': 47812.03}, {'field': 'total_amount', 'old_value': 44743.17, 'new_value': 48975.79}, {'field': 'order_count', 'old_value': 205, 'new_value': 230}]
2025-05-12 09:01:41,095 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-12 09:01:41,659 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-12 09:01:41,659 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3949.0, 'new_value': 4449.0}, {'field': 'offline_amount', 'old_value': 2359.0, 'new_value': 2549.0}, {'field': 'total_amount', 'old_value': 6308.0, 'new_value': 6998.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 65}]
2025-05-12 09:01:41,659 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-12 09:01:42,126 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-12 09:01:42,126 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23508.82, 'new_value': 25924.59}, {'field': 'offline_amount', 'old_value': 17636.75, 'new_value': 18842.66}, {'field': 'total_amount', 'old_value': 41145.57, 'new_value': 44767.25}, {'field': 'order_count', 'old_value': 2215, 'new_value': 2408}]
2025-05-12 09:01:42,126 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-12 09:01:42,640 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-12 09:01:42,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38330.71, 'new_value': 42033.87}, {'field': 'offline_amount', 'old_value': 43598.15, 'new_value': 49083.86}, {'field': 'total_amount', 'old_value': 81928.86, 'new_value': 91117.73}, {'field': 'order_count', 'old_value': 2087, 'new_value': 2298}]
2025-05-12 09:01:42,641 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-12 09:01:43,066 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-12 09:01:43,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 391846.0, 'new_value': 445217.0}, {'field': 'total_amount', 'old_value': 391846.0, 'new_value': 445217.0}, {'field': 'order_count', 'old_value': 468, 'new_value': 541}]
2025-05-12 09:01:43,066 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-12 09:01:43,599 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-12 09:01:43,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57014.0, 'new_value': 72390.0}, {'field': 'total_amount', 'old_value': 57014.0, 'new_value': 72390.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-05-12 09:01:43,600 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-12 09:01:44,063 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-12 09:01:44,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18205.9, 'new_value': 20674.1}, {'field': 'offline_amount', 'old_value': 50388.0, 'new_value': 54776.0}, {'field': 'total_amount', 'old_value': 68593.9, 'new_value': 75450.1}, {'field': 'order_count', 'old_value': 714, 'new_value': 792}]
2025-05-12 09:01:44,063 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-12 09:01:44,545 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-12 09:01:44,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56318.0, 'new_value': 65204.0}, {'field': 'offline_amount', 'old_value': 42050.0, 'new_value': 47575.0}, {'field': 'total_amount', 'old_value': 98368.0, 'new_value': 112779.0}, {'field': 'order_count', 'old_value': 1207, 'new_value': 1367}]
2025-05-12 09:01:44,545 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-12 09:01:45,160 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-12 09:01:45,160 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4929.4, 'new_value': 5344.4}, {'field': 'offline_amount', 'old_value': 9434.47, 'new_value': 10886.76}, {'field': 'total_amount', 'old_value': 14363.87, 'new_value': 16231.16}, {'field': 'order_count', 'old_value': 150, 'new_value': 165}]
2025-05-12 09:01:45,161 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-12 09:01:45,638 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-12 09:01:45,638 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4874.68, 'new_value': 5678.48}, {'field': 'offline_amount', 'old_value': 56226.0, 'new_value': 63337.0}, {'field': 'total_amount', 'old_value': 61100.68, 'new_value': 69015.48}, {'field': 'order_count', 'old_value': 25, 'new_value': 33}]
2025-05-12 09:01:45,638 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-12 09:01:46,170 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-12 09:01:46,170 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38516.64, 'new_value': 44326.65}, {'field': 'total_amount', 'old_value': 38516.64, 'new_value': 44326.65}, {'field': 'order_count', 'old_value': 219, 'new_value': 250}]
2025-05-12 09:01:46,170 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-12 09:01:46,629 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-12 09:01:46,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17167.0, 'new_value': 18184.8}, {'field': 'total_amount', 'old_value': 29596.62, 'new_value': 30614.42}, {'field': 'order_count', 'old_value': 112, 'new_value': 121}]
2025-05-12 09:01:46,629 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-12 09:01:47,009 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-12 09:01:47,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94858.5, 'new_value': 108062.5}, {'field': 'total_amount', 'old_value': 94858.5, 'new_value': 108062.5}, {'field': 'order_count', 'old_value': 482, 'new_value': 541}]
2025-05-12 09:01:47,009 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-12 09:01:47,518 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-12 09:01:47,518 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2271.0, 'new_value': 2559.0}, {'field': 'offline_amount', 'old_value': 6808.0, 'new_value': 8088.0}, {'field': 'total_amount', 'old_value': 9079.0, 'new_value': 10647.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 114}]
2025-05-12 09:01:47,519 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-12 09:01:47,943 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-12 09:01:47,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3088.0, 'new_value': 3247.0}, {'field': 'offline_amount', 'old_value': 20781.58, 'new_value': 21827.59}, {'field': 'total_amount', 'old_value': 23869.58, 'new_value': 25074.59}, {'field': 'order_count', 'old_value': 220, 'new_value': 238}]
2025-05-12 09:01:47,943 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-12 09:01:48,395 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-12 09:01:48,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69010.04, 'new_value': 80063.98}, {'field': 'total_amount', 'old_value': 69010.04, 'new_value': 80063.98}]
2025-05-12 09:01:48,395 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-12 09:01:48,880 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-12 09:01:48,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7804.0, 'new_value': 8333.0}, {'field': 'total_amount', 'old_value': 7804.0, 'new_value': 8333.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-12 09:01:48,880 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-12 09:01:49,290 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-12 09:01:49,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 546523.0, 'new_value': 598648.0}, {'field': 'total_amount', 'old_value': 546523.0, 'new_value': 598648.0}, {'field': 'order_count', 'old_value': 2324, 'new_value': 2563}]
2025-05-12 09:01:49,290 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-12 09:01:49,757 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-12 09:01:49,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7024300.0, 'new_value': 7707032.0}, {'field': 'total_amount', 'old_value': 7024300.0, 'new_value': 7707032.0}, {'field': 'order_count', 'old_value': 20454, 'new_value': 22717}]
2025-05-12 09:01:49,758 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-12 09:01:50,239 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-12 09:01:50,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121447.0, 'new_value': 126417.0}, {'field': 'total_amount', 'old_value': 121447.0, 'new_value': 126417.0}, {'field': 'order_count', 'old_value': 241, 'new_value': 254}]
2025-05-12 09:01:50,240 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-12 09:01:50,693 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-12 09:01:50,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122603.79, 'new_value': 132297.5}, {'field': 'offline_amount', 'old_value': 98188.53, 'new_value': 108866.85}, {'field': 'total_amount', 'old_value': 220792.32, 'new_value': 241164.35}, {'field': 'order_count', 'old_value': 8583, 'new_value': 9461}]
2025-05-12 09:01:50,693 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-12 09:01:51,156 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-12 09:01:51,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143408.0, 'new_value': 150997.0}, {'field': 'total_amount', 'old_value': 143408.0, 'new_value': 150997.0}, {'field': 'order_count', 'old_value': 157, 'new_value': 171}]
2025-05-12 09:01:51,157 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-12 09:01:51,653 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-12 09:01:51,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77542.41, 'new_value': 88424.41}, {'field': 'total_amount', 'old_value': 145443.11, 'new_value': 156325.11}, {'field': 'order_count', 'old_value': 138, 'new_value': 158}]
2025-05-12 09:01:51,654 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-12 09:01:52,070 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-12 09:01:52,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192966.3, 'new_value': 206771.6}, {'field': 'total_amount', 'old_value': 192966.3, 'new_value': 206771.6}, {'field': 'order_count', 'old_value': 4168, 'new_value': 4472}]
2025-05-12 09:01:52,071 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-12 09:01:52,620 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-12 09:01:52,620 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77587.0, 'new_value': 82531.0}, {'field': 'offline_amount', 'old_value': 57682.0, 'new_value': 61151.0}, {'field': 'total_amount', 'old_value': 135269.0, 'new_value': 143682.0}, {'field': 'order_count', 'old_value': 432, 'new_value': 475}]
2025-05-12 09:01:52,620 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-12 09:01:53,030 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-12 09:01:53,030 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392804.95, 'new_value': 415242.07}, {'field': 'total_amount', 'old_value': 392804.95, 'new_value': 415242.07}, {'field': 'order_count', 'old_value': 1795, 'new_value': 2151}]
2025-05-12 09:01:53,030 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-12 09:01:53,523 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-12 09:01:53,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64848.53, 'new_value': 73467.93}, {'field': 'total_amount', 'old_value': 64848.53, 'new_value': 73467.93}, {'field': 'order_count', 'old_value': 4379, 'new_value': 4980}]
2025-05-12 09:01:53,523 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-12 09:01:54,004 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-12 09:01:54,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 288772.0, 'new_value': 311967.0}, {'field': 'total_amount', 'old_value': 288772.0, 'new_value': 311967.0}, {'field': 'order_count', 'old_value': 6519, 'new_value': 7059}]
2025-05-12 09:01:54,005 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-12 09:01:54,451 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-12 09:01:54,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48975.0, 'new_value': 54028.0}, {'field': 'total_amount', 'old_value': 48975.0, 'new_value': 54028.0}, {'field': 'order_count', 'old_value': 3292, 'new_value': 3678}]
2025-05-12 09:01:54,451 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-12 09:01:54,857 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-12 09:01:54,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51182.0, 'new_value': 56054.0}, {'field': 'total_amount', 'old_value': 51182.0, 'new_value': 56054.0}, {'field': 'order_count', 'old_value': 3733, 'new_value': 4042}]
2025-05-12 09:01:54,857 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-12 09:01:55,302 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-12 09:01:55,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7251.5, 'new_value': 8560.1}, {'field': 'offline_amount', 'old_value': 24925.1, 'new_value': 27259.4}, {'field': 'total_amount', 'old_value': 32176.6, 'new_value': 35819.5}, {'field': 'order_count', 'old_value': 1212, 'new_value': 1360}]
2025-05-12 09:01:55,303 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-12 09:01:55,783 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-12 09:01:55,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2004924.39, 'new_value': 2321494.39}, {'field': 'total_amount', 'old_value': 2004924.39, 'new_value': 2321494.39}, {'field': 'order_count', 'old_value': 42753, 'new_value': 47398}]
2025-05-12 09:01:55,783 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-12 09:01:56,231 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-12 09:01:56,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13100.29, 'new_value': 13416.29}, {'field': 'total_amount', 'old_value': 13100.29, 'new_value': 13416.29}, {'field': 'order_count', 'old_value': 50, 'new_value': 54}]
2025-05-12 09:01:56,231 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-12 09:01:56,607 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-12 09:01:56,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127444.0, 'new_value': 137813.0}, {'field': 'total_amount', 'old_value': 127444.0, 'new_value': 137813.0}, {'field': 'order_count', 'old_value': 2700, 'new_value': 2936}]
2025-05-12 09:01:56,608 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-12 09:01:57,029 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-12 09:01:57,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11407.0, 'new_value': 19578.0}, {'field': 'total_amount', 'old_value': 11407.0, 'new_value': 19578.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 15}]
2025-05-12 09:01:57,030 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-12 09:01:57,489 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-12 09:01:57,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132011.0, 'new_value': 156047.0}, {'field': 'total_amount', 'old_value': 132011.0, 'new_value': 156047.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 38}]
2025-05-12 09:01:57,489 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-12 09:01:57,990 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-12 09:01:57,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17797.47, 'new_value': 21425.95}, {'field': 'total_amount', 'old_value': 36674.94, 'new_value': 40303.42}, {'field': 'order_count', 'old_value': 2331, 'new_value': 2574}]
2025-05-12 09:01:57,991 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-12 09:01:58,396 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-12 09:01:58,397 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27558.29, 'new_value': 32074.69}, {'field': 'total_amount', 'old_value': 57966.43, 'new_value': 62482.83}, {'field': 'order_count', 'old_value': 3704, 'new_value': 4018}]
2025-05-12 09:01:58,397 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-12 09:01:58,897 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-12 09:01:58,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62713.0, 'new_value': 68167.0}, {'field': 'total_amount', 'old_value': 62713.0, 'new_value': 68167.0}, {'field': 'order_count', 'old_value': 2269, 'new_value': 2438}]
2025-05-12 09:01:58,898 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-12 09:01:59,287 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-12 09:01:59,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142654.77, 'new_value': 165059.74}, {'field': 'total_amount', 'old_value': 142654.77, 'new_value': 165059.74}, {'field': 'order_count', 'old_value': 397, 'new_value': 459}]
2025-05-12 09:01:59,287 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-12 09:01:59,750 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-12 09:01:59,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62346.0, 'new_value': 69096.0}, {'field': 'total_amount', 'old_value': 62346.0, 'new_value': 69096.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 272}]
2025-05-12 09:01:59,750 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-12 09:02:00,166 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-12 09:02:00,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18755.0, 'new_value': 20429.0}, {'field': 'total_amount', 'old_value': 18755.0, 'new_value': 20429.0}, {'field': 'order_count', 'old_value': 357, 'new_value': 389}]
2025-05-12 09:02:00,167 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-12 09:02:00,604 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-12 09:02:00,604 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65520.0, 'new_value': 68351.0}, {'field': 'total_amount', 'old_value': 65520.0, 'new_value': 68351.0}, {'field': 'order_count', 'old_value': 6778, 'new_value': 7048}]
2025-05-12 09:02:00,604 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-12 09:02:01,024 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-12 09:02:01,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10062.0, 'new_value': 10261.0}, {'field': 'offline_amount', 'old_value': 32598.0, 'new_value': 35155.0}, {'field': 'total_amount', 'old_value': 42660.0, 'new_value': 45416.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 64}]
2025-05-12 09:02:01,024 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-12 09:02:01,622 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-12 09:02:01,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23428.61, 'new_value': 25064.61}, {'field': 'total_amount', 'old_value': 23428.61, 'new_value': 25064.61}, {'field': 'order_count', 'old_value': 356, 'new_value': 389}]
2025-05-12 09:02:01,622 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-12 09:02:02,059 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-12 09:02:02,059 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59215.29, 'new_value': 69755.15}, {'field': 'offline_amount', 'old_value': 268408.94, 'new_value': 318078.94}, {'field': 'total_amount', 'old_value': 327624.23, 'new_value': 387834.09}, {'field': 'order_count', 'old_value': 753, 'new_value': 855}]
2025-05-12 09:02:02,060 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-12 09:02:02,695 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-12 09:02:02,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29787.6, 'new_value': 35555.6}, {'field': 'total_amount', 'old_value': 29787.6, 'new_value': 35555.6}, {'field': 'order_count', 'old_value': 19, 'new_value': 23}]
2025-05-12 09:02:02,696 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-12 09:02:03,203 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-12 09:02:03,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32614.16, 'new_value': 36315.91}, {'field': 'offline_amount', 'old_value': 303349.99, 'new_value': 361476.14}, {'field': 'total_amount', 'old_value': 334089.82, 'new_value': 395917.72}, {'field': 'order_count', 'old_value': 1657, 'new_value': 1878}]
2025-05-12 09:02:03,204 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-12 09:02:03,674 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-12 09:02:03,674 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2841.5, 'new_value': 6601.24}, {'field': 'total_amount', 'old_value': 2841.5, 'new_value': 6601.24}, {'field': 'order_count', 'old_value': 18, 'new_value': 36}]
2025-05-12 09:02:03,674 - INFO - 日期 2025-05 处理完成 - 更新: 190 条，插入: 0 条，错误: 0 条
2025-05-12 09:02:03,674 - INFO - 数据同步完成！更新: 200 条，插入: 0 条，错误: 0 条
2025-05-12 09:02:03,677 - INFO - =================同步完成====================
2025-05-12 12:00:01,888 - INFO - =================使用默认全量同步=============
2025-05-12 12:00:03,216 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-12 12:00:03,216 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-12 12:00:03,243 - INFO - 开始处理日期: 2025-01
2025-05-12 12:00:03,246 - INFO - Request Parameters - Page 1:
2025-05-12 12:00:03,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:03,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:04,371 - INFO - Response - Page 1:
2025-05-12 12:00:04,571 - INFO - 第 1 页获取到 100 条记录
2025-05-12 12:00:04,571 - INFO - Request Parameters - Page 2:
2025-05-12 12:00:04,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:04,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:05,111 - INFO - Response - Page 2:
2025-05-12 12:00:05,311 - INFO - 第 2 页获取到 100 条记录
2025-05-12 12:00:05,311 - INFO - Request Parameters - Page 3:
2025-05-12 12:00:05,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:05,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:05,877 - INFO - Response - Page 3:
2025-05-12 12:00:06,078 - INFO - 第 3 页获取到 100 条记录
2025-05-12 12:00:06,078 - INFO - Request Parameters - Page 4:
2025-05-12 12:00:06,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:06,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:06,562 - INFO - Response - Page 4:
2025-05-12 12:00:06,762 - INFO - 第 4 页获取到 100 条记录
2025-05-12 12:00:06,762 - INFO - Request Parameters - Page 5:
2025-05-12 12:00:06,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:06,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:07,307 - INFO - Response - Page 5:
2025-05-12 12:00:07,509 - INFO - 第 5 页获取到 100 条记录
2025-05-12 12:00:07,509 - INFO - Request Parameters - Page 6:
2025-05-12 12:00:07,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:07,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:07,957 - INFO - Response - Page 6:
2025-05-12 12:00:08,158 - INFO - 第 6 页获取到 100 条记录
2025-05-12 12:00:08,158 - INFO - Request Parameters - Page 7:
2025-05-12 12:00:08,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:08,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:08,599 - INFO - Response - Page 7:
2025-05-12 12:00:08,800 - INFO - 第 7 页获取到 82 条记录
2025-05-12 12:00:08,800 - INFO - 查询完成，共获取到 682 条记录
2025-05-12 12:00:08,800 - INFO - 获取到 682 条表单数据
2025-05-12 12:00:08,811 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-12 12:00:08,822 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 12:00:08,822 - INFO - 开始处理日期: 2025-02
2025-05-12 12:00:08,822 - INFO - Request Parameters - Page 1:
2025-05-12 12:00:08,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:08,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:09,325 - INFO - Response - Page 1:
2025-05-12 12:00:09,525 - INFO - 第 1 页获取到 100 条记录
2025-05-12 12:00:09,525 - INFO - Request Parameters - Page 2:
2025-05-12 12:00:09,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:09,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:10,006 - INFO - Response - Page 2:
2025-05-12 12:00:10,206 - INFO - 第 2 页获取到 100 条记录
2025-05-12 12:00:10,206 - INFO - Request Parameters - Page 3:
2025-05-12 12:00:10,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:10,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:10,649 - INFO - Response - Page 3:
2025-05-12 12:00:10,849 - INFO - 第 3 页获取到 100 条记录
2025-05-12 12:00:10,849 - INFO - Request Parameters - Page 4:
2025-05-12 12:00:10,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:10,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:11,312 - INFO - Response - Page 4:
2025-05-12 12:00:11,512 - INFO - 第 4 页获取到 100 条记录
2025-05-12 12:00:11,512 - INFO - Request Parameters - Page 5:
2025-05-12 12:00:11,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:11,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:12,041 - INFO - Response - Page 5:
2025-05-12 12:00:12,241 - INFO - 第 5 页获取到 100 条记录
2025-05-12 12:00:12,241 - INFO - Request Parameters - Page 6:
2025-05-12 12:00:12,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:12,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:12,680 - INFO - Response - Page 6:
2025-05-12 12:00:12,882 - INFO - 第 6 页获取到 100 条记录
2025-05-12 12:00:12,882 - INFO - Request Parameters - Page 7:
2025-05-12 12:00:12,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:12,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:13,348 - INFO - Response - Page 7:
2025-05-12 12:00:13,549 - INFO - 第 7 页获取到 70 条记录
2025-05-12 12:00:13,549 - INFO - 查询完成，共获取到 670 条记录
2025-05-12 12:00:13,549 - INFO - 获取到 670 条表单数据
2025-05-12 12:00:13,561 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-12 12:00:13,576 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 12:00:13,576 - INFO - 开始处理日期: 2025-03
2025-05-12 12:00:13,576 - INFO - Request Parameters - Page 1:
2025-05-12 12:00:13,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:13,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:14,033 - INFO - Response - Page 1:
2025-05-12 12:00:14,233 - INFO - 第 1 页获取到 100 条记录
2025-05-12 12:00:14,233 - INFO - Request Parameters - Page 2:
2025-05-12 12:00:14,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:14,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:14,739 - INFO - Response - Page 2:
2025-05-12 12:00:14,940 - INFO - 第 2 页获取到 100 条记录
2025-05-12 12:00:14,940 - INFO - Request Parameters - Page 3:
2025-05-12 12:00:14,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:14,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:15,449 - INFO - Response - Page 3:
2025-05-12 12:00:15,649 - INFO - 第 3 页获取到 100 条记录
2025-05-12 12:00:15,649 - INFO - Request Parameters - Page 4:
2025-05-12 12:00:15,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:15,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:16,137 - INFO - Response - Page 4:
2025-05-12 12:00:16,338 - INFO - 第 4 页获取到 100 条记录
2025-05-12 12:00:16,338 - INFO - Request Parameters - Page 5:
2025-05-12 12:00:16,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:16,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:16,809 - INFO - Response - Page 5:
2025-05-12 12:00:17,009 - INFO - 第 5 页获取到 100 条记录
2025-05-12 12:00:17,009 - INFO - Request Parameters - Page 6:
2025-05-12 12:00:17,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:17,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:17,695 - INFO - Response - Page 6:
2025-05-12 12:00:17,895 - INFO - 第 6 页获取到 100 条记录
2025-05-12 12:00:17,895 - INFO - Request Parameters - Page 7:
2025-05-12 12:00:17,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:17,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:18,381 - INFO - Response - Page 7:
2025-05-12 12:00:18,581 - INFO - 第 7 页获取到 61 条记录
2025-05-12 12:00:18,581 - INFO - 查询完成，共获取到 661 条记录
2025-05-12 12:00:18,581 - INFO - 获取到 661 条表单数据
2025-05-12 12:00:18,595 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-12 12:00:18,607 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 12:00:18,607 - INFO - 开始处理日期: 2025-04
2025-05-12 12:00:18,607 - INFO - Request Parameters - Page 1:
2025-05-12 12:00:18,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:18,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:19,085 - INFO - Response - Page 1:
2025-05-12 12:00:19,285 - INFO - 第 1 页获取到 100 条记录
2025-05-12 12:00:19,285 - INFO - Request Parameters - Page 2:
2025-05-12 12:00:19,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:19,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:19,737 - INFO - Response - Page 2:
2025-05-12 12:00:19,937 - INFO - 第 2 页获取到 100 条记录
2025-05-12 12:00:19,937 - INFO - Request Parameters - Page 3:
2025-05-12 12:00:19,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:19,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:20,375 - INFO - Response - Page 3:
2025-05-12 12:00:20,575 - INFO - 第 3 页获取到 100 条记录
2025-05-12 12:00:20,575 - INFO - Request Parameters - Page 4:
2025-05-12 12:00:20,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:20,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:21,030 - INFO - Response - Page 4:
2025-05-12 12:00:21,230 - INFO - 第 4 页获取到 100 条记录
2025-05-12 12:00:21,230 - INFO - Request Parameters - Page 5:
2025-05-12 12:00:21,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:21,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:21,805 - INFO - Response - Page 5:
2025-05-12 12:00:22,005 - INFO - 第 5 页获取到 100 条记录
2025-05-12 12:00:22,005 - INFO - Request Parameters - Page 6:
2025-05-12 12:00:22,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:22,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:22,509 - INFO - Response - Page 6:
2025-05-12 12:00:22,710 - INFO - 第 6 页获取到 100 条记录
2025-05-12 12:00:22,710 - INFO - Request Parameters - Page 7:
2025-05-12 12:00:22,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:22,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:23,126 - INFO - Response - Page 7:
2025-05-12 12:00:23,326 - INFO - 第 7 页获取到 54 条记录
2025-05-12 12:00:23,326 - INFO - 查询完成，共获取到 654 条记录
2025-05-12 12:00:23,326 - INFO - 获取到 654 条表单数据
2025-05-12 12:00:23,339 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-12 12:00:23,350 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 12:00:23,350 - INFO - 开始处理日期: 2025-05
2025-05-12 12:00:23,351 - INFO - Request Parameters - Page 1:
2025-05-12 12:00:23,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:23,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:23,821 - INFO - Response - Page 1:
2025-05-12 12:00:24,021 - INFO - 第 1 页获取到 100 条记录
2025-05-12 12:00:24,021 - INFO - Request Parameters - Page 2:
2025-05-12 12:00:24,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:24,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:24,497 - INFO - Response - Page 2:
2025-05-12 12:00:24,697 - INFO - 第 2 页获取到 100 条记录
2025-05-12 12:00:24,697 - INFO - Request Parameters - Page 3:
2025-05-12 12:00:24,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:24,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:25,152 - INFO - Response - Page 3:
2025-05-12 12:00:25,352 - INFO - 第 3 页获取到 100 条记录
2025-05-12 12:00:25,352 - INFO - Request Parameters - Page 4:
2025-05-12 12:00:25,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:25,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:25,802 - INFO - Response - Page 4:
2025-05-12 12:00:26,004 - INFO - 第 4 页获取到 100 条记录
2025-05-12 12:00:26,004 - INFO - Request Parameters - Page 5:
2025-05-12 12:00:26,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:26,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:26,502 - INFO - Response - Page 5:
2025-05-12 12:00:26,702 - INFO - 第 5 页获取到 100 条记录
2025-05-12 12:00:26,702 - INFO - Request Parameters - Page 6:
2025-05-12 12:00:26,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:26,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:27,197 - INFO - Response - Page 6:
2025-05-12 12:00:27,397 - INFO - 第 6 页获取到 100 条记录
2025-05-12 12:00:27,397 - INFO - Request Parameters - Page 7:
2025-05-12 12:00:27,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 12:00:27,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 12:00:27,709 - INFO - Response - Page 7:
2025-05-12 12:00:27,909 - INFO - 第 7 页获取到 24 条记录
2025-05-12 12:00:27,909 - INFO - 查询完成，共获取到 624 条记录
2025-05-12 12:00:27,909 - INFO - 获取到 624 条表单数据
2025-05-12 12:00:27,920 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-12 12:00:27,920 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-12 12:00:28,289 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-12 12:00:28,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1590.0}, {'field': 'total_amount', 'old_value': 12985.0, 'new_value': 14575.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 55}]
2025-05-12 12:00:28,290 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-12 12:00:28,758 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-12 12:00:28,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107441.0, 'new_value': 118441.0}, {'field': 'total_amount', 'old_value': 107441.0, 'new_value': 118441.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-12 12:00:28,759 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-12 12:00:29,250 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-12 12:00:29,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63533.34, 'new_value': 71317.37}, {'field': 'total_amount', 'old_value': 63533.34, 'new_value': 71317.37}, {'field': 'order_count', 'old_value': 2832, 'new_value': 3120}]
2025-05-12 12:00:29,251 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-12 12:00:29,900 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-12 12:00:29,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18500.0, 'new_value': 39670.0}, {'field': 'total_amount', 'old_value': 18500.0, 'new_value': 39670.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-12 12:00:29,900 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-12 12:00:30,415 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-12 12:00:30,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9197.7, 'new_value': 13133.9}, {'field': 'total_amount', 'old_value': 9197.7, 'new_value': 13133.9}, {'field': 'order_count', 'old_value': 52, 'new_value': 81}]
2025-05-12 12:00:30,415 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-12 12:00:30,962 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-12 12:00:30,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10957.28, 'new_value': 15550.33}, {'field': 'total_amount', 'old_value': 10957.28, 'new_value': 15550.33}, {'field': 'order_count', 'old_value': 2026, 'new_value': 2923}]
2025-05-12 12:00:30,962 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-12 12:00:31,431 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-12 12:00:31,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15710.06, 'new_value': 22421.15}, {'field': 'total_amount', 'old_value': 18794.67, 'new_value': 25505.76}, {'field': 'order_count', 'old_value': 652, 'new_value': 886}]
2025-05-12 12:00:31,432 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-12 12:00:31,905 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-12 12:00:31,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21206.36, 'new_value': 24287.88}, {'field': 'total_amount', 'old_value': 21206.36, 'new_value': 24287.88}, {'field': 'order_count', 'old_value': 48, 'new_value': 57}]
2025-05-12 12:00:31,905 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-12 12:00:32,381 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-12 12:00:32,382 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 183172.0, 'new_value': 209649.0}, {'field': 'offline_amount', 'old_value': 103563.0, 'new_value': 120916.0}, {'field': 'total_amount', 'old_value': 286735.0, 'new_value': 330565.0}, {'field': 'order_count', 'old_value': 299, 'new_value': 356}]
2025-05-12 12:00:32,382 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-12 12:00:32,903 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-12 12:00:32,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25021.58, 'new_value': 30401.82}, {'field': 'total_amount', 'old_value': 37206.06, 'new_value': 42586.3}, {'field': 'order_count', 'old_value': 858, 'new_value': 1001}]
2025-05-12 12:00:32,904 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-12 12:00:33,386 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-12 12:00:33,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13960.0, 'new_value': 16560.0}, {'field': 'total_amount', 'old_value': 18080.0, 'new_value': 20680.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 207}]
2025-05-12 12:00:33,387 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-12 12:00:33,978 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-12 12:00:33,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1822.73, 'new_value': 2061.93}, {'field': 'offline_amount', 'old_value': 33181.01, 'new_value': 36641.21}, {'field': 'total_amount', 'old_value': 35003.74, 'new_value': 38703.14}, {'field': 'order_count', 'old_value': 1307, 'new_value': 1456}]
2025-05-12 12:00:33,979 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-12 12:00:34,497 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-12 12:00:34,497 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44828.22, 'new_value': 55527.59}, {'field': 'offline_amount', 'old_value': 4869.35, 'new_value': 7282.95}, {'field': 'total_amount', 'old_value': 49697.57, 'new_value': 62810.54}, {'field': 'order_count', 'old_value': 1671, 'new_value': 2300}]
2025-05-12 12:00:34,498 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-12 12:00:34,946 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-12 12:00:34,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4281.1, 'new_value': 5124.1}, {'field': 'offline_amount', 'old_value': 55376.0, 'new_value': 59753.7}, {'field': 'total_amount', 'old_value': 59657.1, 'new_value': 64877.8}, {'field': 'order_count', 'old_value': 1175, 'new_value': 1305}]
2025-05-12 12:00:34,946 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-12 12:00:35,421 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-12 12:00:35,421 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13931.4, 'new_value': 14331.0}, {'field': 'offline_amount', 'old_value': 27433.6, 'new_value': 30401.6}, {'field': 'total_amount', 'old_value': 41365.0, 'new_value': 44732.6}, {'field': 'order_count', 'old_value': 544, 'new_value': 561}]
2025-05-12 12:00:35,421 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-12 12:00:35,936 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-12 12:00:35,936 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13700.0, 'new_value': 27580.0}, {'field': 'offline_amount', 'old_value': 37269.0, 'new_value': 44795.0}, {'field': 'total_amount', 'old_value': 50969.0, 'new_value': 72375.0}, {'field': 'order_count', 'old_value': 602, 'new_value': 872}]
2025-05-12 12:00:35,937 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-12 12:00:36,413 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-12 12:00:36,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12608.0, 'new_value': 19532.5}, {'field': 'total_amount', 'old_value': 12608.0, 'new_value': 19532.5}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-12 12:00:36,413 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-12 12:00:36,906 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-12 12:00:36,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36930.38, 'new_value': 47423.16}, {'field': 'total_amount', 'old_value': 36930.38, 'new_value': 47423.16}, {'field': 'order_count', 'old_value': 1288, 'new_value': 1704}]
2025-05-12 12:00:36,906 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-12 12:00:37,384 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-12 12:00:37,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37072.29, 'new_value': 49423.59}, {'field': 'offline_amount', 'old_value': 18099.72, 'new_value': 20033.44}, {'field': 'total_amount', 'old_value': 55172.01, 'new_value': 69457.03}, {'field': 'order_count', 'old_value': 3193, 'new_value': 3996}]
2025-05-12 12:00:37,384 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-12 12:00:37,858 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-12 12:00:37,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31113.0, 'new_value': 34444.0}, {'field': 'total_amount', 'old_value': 31113.0, 'new_value': 34444.0}, {'field': 'order_count', 'old_value': 258, 'new_value': 289}]
2025-05-12 12:00:37,859 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-12 12:00:38,276 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-12 12:00:38,277 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17480.09, 'new_value': 19182.51}, {'field': 'offline_amount', 'old_value': 19749.74, 'new_value': 21993.7}, {'field': 'total_amount', 'old_value': 37229.83, 'new_value': 41176.21}, {'field': 'order_count', 'old_value': 1879, 'new_value': 2062}]
2025-05-12 12:00:38,277 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-12 12:00:38,792 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-12 12:00:38,792 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3865.3, 'new_value': 4336.8}, {'field': 'offline_amount', 'old_value': 49017.59, 'new_value': 55102.78}, {'field': 'total_amount', 'old_value': 52882.89, 'new_value': 59439.58}, {'field': 'order_count', 'old_value': 810, 'new_value': 916}]
2025-05-12 12:00:38,792 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-12 12:00:39,309 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-12 12:00:39,309 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 900.0, 'new_value': 1050.0}, {'field': 'offline_amount', 'old_value': 13473.0, 'new_value': 14966.0}, {'field': 'total_amount', 'old_value': 14373.0, 'new_value': 16016.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 59}]
2025-05-12 12:00:39,309 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-12 12:00:39,778 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-12 12:00:39,778 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2956.48, 'new_value': 3145.02}, {'field': 'offline_amount', 'old_value': 46946.28, 'new_value': 55554.3}, {'field': 'total_amount', 'old_value': 49902.76, 'new_value': 58699.32}, {'field': 'order_count', 'old_value': 1170, 'new_value': 1361}]
2025-05-12 12:00:39,778 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-12 12:00:40,196 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-12 12:00:40,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86285.53, 'new_value': 96670.85}, {'field': 'total_amount', 'old_value': 86285.53, 'new_value': 96670.85}, {'field': 'order_count', 'old_value': 265, 'new_value': 299}]
2025-05-12 12:00:40,197 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-12 12:00:40,761 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-12 12:00:40,761 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 8.0}, {'field': 'offline_amount', 'old_value': 35081.48, 'new_value': 47605.57}, {'field': 'total_amount', 'old_value': 35081.48, 'new_value': 47613.57}, {'field': 'order_count', 'old_value': 1977, 'new_value': 2660}]
2025-05-12 12:00:40,761 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-12 12:00:41,200 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-12 12:00:41,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6571.85, 'new_value': 7142.85}, {'field': 'total_amount', 'old_value': 6571.85, 'new_value': 7142.85}, {'field': 'order_count', 'old_value': 102, 'new_value': 114}]
2025-05-12 12:00:41,201 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-12 12:00:41,655 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-12 12:00:41,655 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18773.06, 'new_value': 25417.96}, {'field': 'total_amount', 'old_value': 18773.06, 'new_value': 25417.96}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-05-12 12:00:41,655 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-12 12:00:42,139 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-12 12:00:42,139 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2980.0, 'new_value': 4073.0}, {'field': 'offline_amount', 'old_value': 5520.0, 'new_value': 7350.0}, {'field': 'total_amount', 'old_value': 8500.0, 'new_value': 11423.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 67}]
2025-05-12 12:00:42,139 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-12 12:00:42,601 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-12 12:00:42,601 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27799.71, 'new_value': 40117.91}, {'field': 'offline_amount', 'old_value': 27868.26, 'new_value': 36558.57}, {'field': 'total_amount', 'old_value': 55667.97, 'new_value': 76676.48}, {'field': 'order_count', 'old_value': 1814, 'new_value': 2563}]
2025-05-12 12:00:42,601 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-12 12:00:43,033 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-12 12:00:43,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2916.29, 'new_value': 3240.23}, {'field': 'offline_amount', 'old_value': 75325.86, 'new_value': 82644.69}, {'field': 'total_amount', 'old_value': 78242.15, 'new_value': 85884.92}, {'field': 'order_count', 'old_value': 531, 'new_value': 596}]
2025-05-12 12:00:43,033 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-12 12:00:43,496 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-12 12:00:43,496 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27008.44, 'new_value': 29385.96}, {'field': 'total_amount', 'old_value': 27008.44, 'new_value': 29385.96}, {'field': 'order_count', 'old_value': 1222, 'new_value': 1333}]
2025-05-12 12:00:43,496 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-12 12:00:43,990 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-12 12:00:43,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20408.9, 'new_value': 22947.9}, {'field': 'total_amount', 'old_value': 20408.9, 'new_value': 22947.9}, {'field': 'order_count', 'old_value': 92, 'new_value': 102}]
2025-05-12 12:00:43,991 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-12 12:00:44,440 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-12 12:00:44,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60885.0, 'new_value': 85348.0}, {'field': 'total_amount', 'old_value': 60885.0, 'new_value': 85348.0}, {'field': 'order_count', 'old_value': 8482, 'new_value': 8982}]
2025-05-12 12:00:44,441 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-12 12:00:44,971 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-12 12:00:44,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27610.66, 'new_value': 28213.26}, {'field': 'total_amount', 'old_value': 27613.96, 'new_value': 28216.56}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-12 12:00:44,972 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-12 12:00:45,408 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-12 12:00:45,409 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 6915.66}, {'field': 'offline_amount', 'old_value': 67421.94, 'new_value': 83353.53}, {'field': 'total_amount', 'old_value': 67421.94, 'new_value': 90269.19}, {'field': 'order_count', 'old_value': 1298, 'new_value': 1753}]
2025-05-12 12:00:45,409 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-12 12:00:45,957 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-12 12:00:45,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150504.18, 'new_value': 224072.76}, {'field': 'total_amount', 'old_value': 150504.18, 'new_value': 224072.76}, {'field': 'order_count', 'old_value': 1487, 'new_value': 2217}]
2025-05-12 12:00:45,958 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-12 12:00:46,911 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-12 12:00:46,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51513.99, 'new_value': 56893.39}, {'field': 'total_amount', 'old_value': 55283.09, 'new_value': 60662.49}, {'field': 'order_count', 'old_value': 276, 'new_value': 311}]
2025-05-12 12:00:46,912 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-12 12:00:47,362 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-12 12:00:47,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4947.7, 'new_value': 7431.7}, {'field': 'total_amount', 'old_value': 5489.7, 'new_value': 7973.7}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-05-12 12:00:47,363 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-12 12:00:47,844 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-12 12:00:47,845 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 403847.54, 'new_value': 430295.04}, {'field': 'offline_amount', 'old_value': 118802.9, 'new_value': 139265.9}, {'field': 'total_amount', 'old_value': 522650.44, 'new_value': 569560.94}, {'field': 'order_count', 'old_value': 5054, 'new_value': 5383}]
2025-05-12 12:00:47,845 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-12 12:00:48,385 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-12 12:00:48,385 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22349.6, 'new_value': 25582.6}, {'field': 'offline_amount', 'old_value': 40.0, 'new_value': 49.0}, {'field': 'total_amount', 'old_value': 22389.6, 'new_value': 25631.6}, {'field': 'order_count', 'old_value': 96, 'new_value': 102}]
2025-05-12 12:00:48,386 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-12 12:00:48,877 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-12 12:00:48,877 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46645.93, 'new_value': 51459.87}, {'field': 'offline_amount', 'old_value': 179656.82, 'new_value': 204741.29}, {'field': 'total_amount', 'old_value': 226302.75, 'new_value': 256201.16}, {'field': 'order_count', 'old_value': 1090, 'new_value': 1296}]
2025-05-12 12:00:48,877 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-12 12:00:49,440 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-12 12:00:49,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71219.5, 'new_value': 85366.2}, {'field': 'total_amount', 'old_value': 71219.5, 'new_value': 85366.2}, {'field': 'order_count', 'old_value': 151, 'new_value': 189}]
2025-05-12 12:00:49,441 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-12 12:00:49,873 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-12 12:00:49,873 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30611.56, 'new_value': 34970.25}, {'field': 'offline_amount', 'old_value': 480769.43, 'new_value': 521851.44}, {'field': 'total_amount', 'old_value': 511380.99, 'new_value': 556821.69}, {'field': 'order_count', 'old_value': 3856, 'new_value': 4259}]
2025-05-12 12:00:49,873 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-12 12:00:50,349 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-12 12:00:50,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21257.0, 'new_value': 24544.0}, {'field': 'total_amount', 'old_value': 21257.0, 'new_value': 24544.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 50}]
2025-05-12 12:00:50,349 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-12 12:00:50,801 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-12 12:00:50,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58414.5, 'new_value': 67416.21}, {'field': 'total_amount', 'old_value': 58414.5, 'new_value': 67416.21}, {'field': 'order_count', 'old_value': 364, 'new_value': 442}]
2025-05-12 12:00:50,801 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-12 12:00:51,246 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-12 12:00:51,246 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37217.98, 'new_value': 43404.98}, {'field': 'total_amount', 'old_value': 37217.98, 'new_value': 43404.98}, {'field': 'order_count', 'old_value': 205, 'new_value': 238}]
2025-05-12 12:00:51,246 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-12 12:00:51,661 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-12 12:00:51,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 754.84, 'new_value': 853.9}, {'field': 'offline_amount', 'old_value': 8999.68, 'new_value': 10710.24}, {'field': 'total_amount', 'old_value': 9754.52, 'new_value': 11564.14}, {'field': 'order_count', 'old_value': 426, 'new_value': 512}]
2025-05-12 12:00:51,662 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-12 12:00:52,093 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-12 12:00:52,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3602.85, 'new_value': 5742.95}, {'field': 'offline_amount', 'old_value': 39179.23, 'new_value': 58065.15}, {'field': 'total_amount', 'old_value': 42782.08, 'new_value': 63808.1}, {'field': 'order_count', 'old_value': 1087, 'new_value': 1567}]
2025-05-12 12:00:52,094 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-12 12:00:52,557 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-12 12:00:52,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191.5, 'new_value': 1410.7}, {'field': 'total_amount', 'old_value': 191.5, 'new_value': 1410.7}, {'field': 'order_count', 'old_value': 19, 'new_value': 128}]
2025-05-12 12:00:52,557 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-12 12:00:53,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-12 12:00:53,061 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12974.4, 'new_value': 15062.0}, {'field': 'offline_amount', 'old_value': 10838.7, 'new_value': 12289.7}, {'field': 'total_amount', 'old_value': 23813.1, 'new_value': 27351.7}, {'field': 'order_count', 'old_value': 127, 'new_value': 149}]
2025-05-12 12:00:53,061 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-12 12:00:53,540 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-12 12:00:53,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34895.0, 'new_value': 46893.0}, {'field': 'total_amount', 'old_value': 34895.0, 'new_value': 46893.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-12 12:00:53,540 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-12 12:00:54,054 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-12 12:00:54,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230141.0, 'new_value': 350141.0}, {'field': 'total_amount', 'old_value': 230141.0, 'new_value': 350141.0}, {'field': 'order_count', 'old_value': 1052, 'new_value': 1386}]
2025-05-12 12:00:54,055 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-12 12:00:54,493 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-12 12:00:54,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26164.0, 'new_value': 29796.0}, {'field': 'total_amount', 'old_value': 27058.0, 'new_value': 30690.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 118}]
2025-05-12 12:00:54,493 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-12 12:00:54,925 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-12 12:00:54,925 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14872.0, 'new_value': 17316.5}, {'field': 'offline_amount', 'old_value': 123028.1, 'new_value': 138751.7}, {'field': 'total_amount', 'old_value': 137900.1, 'new_value': 156068.2}, {'field': 'order_count', 'old_value': 1034, 'new_value': 1156}]
2025-05-12 12:00:54,925 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-12 12:00:55,386 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-12 12:00:55,386 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6229.74, 'new_value': 7175.74}, {'field': 'offline_amount', 'old_value': 107292.74, 'new_value': 120120.64}, {'field': 'total_amount', 'old_value': 113522.48, 'new_value': 127296.38}, {'field': 'order_count', 'old_value': 5962, 'new_value': 6684}]
2025-05-12 12:00:55,386 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-12 12:00:55,855 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-12 12:00:55,855 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 11, 'new_value': 10}]
2025-05-12 12:00:55,855 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-12 12:00:56,400 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-12 12:00:56,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21256.88, 'new_value': 24548.61}, {'field': 'offline_amount', 'old_value': 12876.0, 'new_value': 14362.0}, {'field': 'total_amount', 'old_value': 34132.88, 'new_value': 38910.61}, {'field': 'order_count', 'old_value': 436, 'new_value': 496}]
2025-05-12 12:00:56,401 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-12 12:00:56,958 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-12 12:00:56,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47494.22, 'new_value': 55085.22}, {'field': 'total_amount', 'old_value': 47494.22, 'new_value': 55085.22}, {'field': 'order_count', 'old_value': 3665, 'new_value': 4674}]
2025-05-12 12:00:56,959 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-12 12:00:57,502 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-12 12:00:57,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61029.5, 'new_value': 68377.4}, {'field': 'total_amount', 'old_value': 61029.5, 'new_value': 68377.4}, {'field': 'order_count', 'old_value': 574, 'new_value': 694}]
2025-05-12 12:00:57,503 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-12 12:00:57,975 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-12 12:00:57,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17147.33, 'new_value': 21154.5}, {'field': 'offline_amount', 'old_value': 73563.46, 'new_value': 114515.75}, {'field': 'total_amount', 'old_value': 90710.79, 'new_value': 135670.25}, {'field': 'order_count', 'old_value': 2706, 'new_value': 4082}]
2025-05-12 12:00:57,976 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-12 12:00:58,444 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-12 12:00:58,444 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 41872, 'new_value': 42629}]
2025-05-12 12:00:58,445 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-12 12:00:58,899 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-12 12:00:58,899 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23528.83, 'new_value': 26471.73}, {'field': 'offline_amount', 'old_value': 197534.83, 'new_value': 226511.4}, {'field': 'total_amount', 'old_value': 221063.66, 'new_value': 252983.13}, {'field': 'order_count', 'old_value': 1741, 'new_value': 1984}]
2025-05-12 12:00:58,899 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-12 12:00:59,358 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-12 12:00:59,358 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 1305, 'new_value': 1321}]
2025-05-12 12:00:59,359 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-12 12:00:59,823 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-12 12:00:59,824 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 2171, 'new_value': 2173}]
2025-05-12 12:00:59,824 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-12 12:01:00,427 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-12 12:01:00,428 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 1279, 'new_value': 1303}]
2025-05-12 12:01:00,428 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-05-12 12:01:00,900 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-05-12 12:01:00,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13500.0, 'new_value': 36580.0}, {'field': 'total_amount', 'old_value': 13500.0, 'new_value': 36580.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-05-12 12:01:00,900 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-12 12:01:01,343 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-12 12:01:01,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13733.2, 'new_value': 16004.7}, {'field': 'offline_amount', 'old_value': 13063.15, 'new_value': 14384.62}, {'field': 'total_amount', 'old_value': 26796.35, 'new_value': 30389.32}, {'field': 'order_count', 'old_value': 5211, 'new_value': 5443}]
2025-05-12 12:01:01,343 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M
2025-05-12 12:01:01,830 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M
2025-05-12 12:01:01,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1288.0, 'new_value': 14176.0}, {'field': 'total_amount', 'old_value': 1288.0, 'new_value': 14176.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-12 12:01:01,831 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-05-12 12:01:02,329 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-05-12 12:01:02,329 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 5, 'new_value': 4}]
2025-05-12 12:01:02,329 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-12 12:01:02,736 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-12 12:01:02,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47718.77, 'new_value': 50732.76}, {'field': 'offline_amount', 'old_value': 110503.56, 'new_value': 139064.93}, {'field': 'total_amount', 'old_value': 158222.33, 'new_value': 189797.69}, {'field': 'order_count', 'old_value': 1535, 'new_value': 1789}]
2025-05-12 12:01:02,736 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-12 12:01:03,153 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-12 12:01:03,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139334.0, 'new_value': 145634.0}, {'field': 'total_amount', 'old_value': 139334.0, 'new_value': 145634.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-12 12:01:03,154 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-12 12:01:03,614 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-12 12:01:03,614 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 216, 'new_value': 234}]
2025-05-12 12:01:03,614 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-12 12:01:04,072 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-12 12:01:04,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18640.0, 'new_value': 34890.0}, {'field': 'total_amount', 'old_value': 25564.0, 'new_value': 41814.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-05-12 12:01:04,073 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-12 12:01:04,521 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-12 12:01:04,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18163.2, 'new_value': 19556.2}, {'field': 'total_amount', 'old_value': 18163.2, 'new_value': 19556.2}, {'field': 'order_count', 'old_value': 886, 'new_value': 954}]
2025-05-12 12:01:04,522 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-12 12:01:04,995 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-12 12:01:04,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23057.4, 'new_value': 25017.8}, {'field': 'total_amount', 'old_value': 23578.6, 'new_value': 25539.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 83}]
2025-05-12 12:01:04,996 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-12 12:01:05,405 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-12 12:01:05,405 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5839.4, 'new_value': 7675.26}, {'field': 'offline_amount', 'old_value': 90971.65, 'new_value': 126387.48}, {'field': 'total_amount', 'old_value': 96811.05, 'new_value': 134062.74}, {'field': 'order_count', 'old_value': 1280, 'new_value': 1681}]
2025-05-12 12:01:05,405 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-12 12:01:05,858 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-12 12:01:05,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20047.0, 'new_value': 23617.0}, {'field': 'total_amount', 'old_value': 20047.0, 'new_value': 23617.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 114}]
2025-05-12 12:01:05,858 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-12 12:01:06,358 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-12 12:01:06,358 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 25, 'new_value': 24}]
2025-05-12 12:01:06,359 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-12 12:01:06,822 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-12 12:01:06,822 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7235.0, 'new_value': 14054.0}, {'field': 'offline_amount', 'old_value': 65578.0, 'new_value': 97698.0}, {'field': 'total_amount', 'old_value': 72813.0, 'new_value': 111752.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 102}]
2025-05-12 12:01:06,822 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-12 12:01:07,259 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-12 12:01:07,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17536.79, 'new_value': 19777.46}, {'field': 'offline_amount', 'old_value': 101038.78, 'new_value': 114729.05}, {'field': 'total_amount', 'old_value': 118575.57, 'new_value': 134506.51}, {'field': 'order_count', 'old_value': 4506, 'new_value': 5418}]
2025-05-12 12:01:07,260 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-12 12:01:07,717 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-12 12:01:07,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142660.0, 'new_value': 195433.0}, {'field': 'total_amount', 'old_value': 142660.0, 'new_value': 195433.0}, {'field': 'order_count', 'old_value': 3583, 'new_value': 5031}]
2025-05-12 12:01:07,717 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-12 12:01:08,190 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-12 12:01:08,190 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 369, 'new_value': 384}]
2025-05-12 12:01:08,190 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-12 12:01:08,660 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-12 12:01:08,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31710.15, 'new_value': 44012.95}, {'field': 'total_amount', 'old_value': 31710.15, 'new_value': 44012.95}, {'field': 'order_count', 'old_value': 862, 'new_value': 1208}]
2025-05-12 12:01:08,660 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-12 12:01:09,128 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-12 12:01:09,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13595.0, 'new_value': 14990.0}, {'field': 'total_amount', 'old_value': 13595.0, 'new_value': 14990.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 150}]
2025-05-12 12:01:09,128 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-12 12:01:09,588 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-12 12:01:09,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56554.5, 'new_value': 76357.5}, {'field': 'total_amount', 'old_value': 56554.5, 'new_value': 76357.5}, {'field': 'order_count', 'old_value': 2249, 'new_value': 3066}]
2025-05-12 12:01:09,589 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-12 12:01:10,031 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-12 12:01:10,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50463.0, 'new_value': 57256.0}, {'field': 'total_amount', 'old_value': 59387.6, 'new_value': 66180.6}, {'field': 'order_count', 'old_value': 54, 'new_value': 61}]
2025-05-12 12:01:10,031 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-12 12:01:10,505 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-12 12:01:10,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47218.74, 'new_value': 63836.74}, {'field': 'total_amount', 'old_value': 60427.46, 'new_value': 77045.46}, {'field': 'order_count', 'old_value': 3438, 'new_value': 4358}]
2025-05-12 12:01:10,506 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-12 12:01:10,967 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-12 12:01:10,967 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9722.05, 'new_value': 14735.71}, {'field': 'offline_amount', 'old_value': 21930.08, 'new_value': 29123.22}, {'field': 'total_amount', 'old_value': 31652.13, 'new_value': 43858.93}, {'field': 'order_count', 'old_value': 266, 'new_value': 376}]
2025-05-12 12:01:10,968 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-12 12:01:11,474 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-12 12:01:11,474 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 1572, 'new_value': 1589}]
2025-05-12 12:01:11,475 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-12 12:01:11,909 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-12 12:01:11,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8010.5, 'new_value': 8662.4}, {'field': 'offline_amount', 'old_value': 21567.9, 'new_value': 21813.9}, {'field': 'total_amount', 'old_value': 29578.4, 'new_value': 30476.3}, {'field': 'order_count', 'old_value': 77, 'new_value': 86}]
2025-05-12 12:01:11,909 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-12 12:01:12,324 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-12 12:01:12,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31625.0, 'new_value': 36030.0}, {'field': 'total_amount', 'old_value': 31625.0, 'new_value': 36030.0}, {'field': 'order_count', 'old_value': 215, 'new_value': 249}]
2025-05-12 12:01:12,324 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-12 12:01:12,813 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-12 12:01:12,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3908.94, 'new_value': 4397.94}, {'field': 'offline_amount', 'old_value': 13987.0, 'new_value': 15940.99}, {'field': 'total_amount', 'old_value': 17895.94, 'new_value': 20338.93}, {'field': 'order_count', 'old_value': 642, 'new_value': 733}]
2025-05-12 12:01:12,813 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-12 12:01:13,297 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-12 12:01:13,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72124.72, 'new_value': 82627.11}, {'field': 'total_amount', 'old_value': 72124.72, 'new_value': 82627.11}, {'field': 'order_count', 'old_value': 1838, 'new_value': 2059}]
2025-05-12 12:01:13,298 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-12 12:01:13,737 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-12 12:01:13,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12900.0, 'new_value': 25500.0}, {'field': 'total_amount', 'old_value': 12900.0, 'new_value': 25500.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-12 12:01:13,738 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-12 12:01:14,223 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-12 12:01:14,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48644.0, 'new_value': 52377.0}, {'field': 'total_amount', 'old_value': 48644.0, 'new_value': 52377.0}, {'field': 'order_count', 'old_value': 224, 'new_value': 244}]
2025-05-12 12:01:14,224 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-12 12:01:14,756 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-12 12:01:14,756 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2659.5, 'new_value': 3642.32}, {'field': 'offline_amount', 'old_value': 39508.5, 'new_value': 54004.1}, {'field': 'total_amount', 'old_value': 42168.0, 'new_value': 57646.42}, {'field': 'order_count', 'old_value': 3203, 'new_value': 3737}]
2025-05-12 12:01:14,757 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-12 12:01:15,245 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-12 12:01:15,245 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7234.62, 'new_value': 8708.42}, {'field': 'offline_amount', 'old_value': 52420.87, 'new_value': 68761.07}, {'field': 'total_amount', 'old_value': 59655.49, 'new_value': 77469.49}, {'field': 'order_count', 'old_value': 1632, 'new_value': 2260}]
2025-05-12 12:01:15,245 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-12 12:01:15,723 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-12 12:01:15,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6173.2, 'new_value': 7375.9}, {'field': 'total_amount', 'old_value': 6602.2, 'new_value': 7804.9}, {'field': 'order_count', 'old_value': 95, 'new_value': 112}]
2025-05-12 12:01:15,723 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-12 12:01:16,216 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-12 12:01:16,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34654.9, 'new_value': 45018.1}, {'field': 'total_amount', 'old_value': 34654.9, 'new_value': 45018.1}, {'field': 'order_count', 'old_value': 1691, 'new_value': 2199}]
2025-05-12 12:01:16,216 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-12 12:01:16,709 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-12 12:01:16,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 9485.22}, {'field': 'total_amount', 'old_value': 78593.71, 'new_value': 88078.93}, {'field': 'order_count', 'old_value': 3342, 'new_value': 3746}]
2025-05-12 12:01:16,710 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-12 12:01:17,295 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-12 12:01:17,295 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 295128.0, 'new_value': 358541.0}, {'field': 'total_amount', 'old_value': 295128.0, 'new_value': 358541.0}, {'field': 'order_count', 'old_value': 564, 'new_value': 745}]
2025-05-12 12:01:17,295 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA
2025-05-12 12:01:17,738 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA
2025-05-12 12:01:17,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8180.0, 'new_value': 8560.0}, {'field': 'total_amount', 'old_value': 8180.0, 'new_value': 8560.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 49}]
2025-05-12 12:01:17,738 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-12 12:01:18,197 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-12 12:01:18,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6349.0, 'new_value': 7329.0}, {'field': 'total_amount', 'old_value': 6349.0, 'new_value': 7329.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-12 12:01:18,198 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-12 12:01:18,627 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-12 12:01:18,628 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 684.47, 'new_value': 764.84}, {'field': 'offline_amount', 'old_value': 14040.34, 'new_value': 15201.86}, {'field': 'total_amount', 'old_value': 14724.81, 'new_value': 15966.7}, {'field': 'order_count', 'old_value': 516, 'new_value': 563}]
2025-05-12 12:01:18,628 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-12 12:01:19,086 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-12 12:01:19,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3232.39, 'new_value': 3453.4}, {'field': 'offline_amount', 'old_value': 180922.24, 'new_value': 207041.44}, {'field': 'total_amount', 'old_value': 184154.63, 'new_value': 210494.84}, {'field': 'order_count', 'old_value': 8265, 'new_value': 9399}]
2025-05-12 12:01:19,086 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-12 12:01:19,565 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-12 12:01:19,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20010.4, 'new_value': 22417.0}, {'field': 'offline_amount', 'old_value': 138516.81, 'new_value': 165358.32}, {'field': 'total_amount', 'old_value': 158527.21, 'new_value': 187775.32}, {'field': 'order_count', 'old_value': 974, 'new_value': 1144}]
2025-05-12 12:01:19,566 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-12 12:01:19,990 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-12 12:01:19,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48010.0, 'new_value': 52634.0}, {'field': 'total_amount', 'old_value': 48010.0, 'new_value': 52634.0}, {'field': 'order_count', 'old_value': 1432, 'new_value': 1563}]
2025-05-12 12:01:19,990 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-12 12:01:20,437 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-12 12:01:20,437 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-12 12:01:20,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-12 12:01:20,900 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-12 12:01:20,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169082.39, 'new_value': 187873.42}, {'field': 'total_amount', 'old_value': 169082.39, 'new_value': 187873.42}, {'field': 'order_count', 'old_value': 1066, 'new_value': 1202}]
2025-05-12 12:01:20,901 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-12 12:01:21,350 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-12 12:01:21,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59726.0, 'new_value': 64621.0}, {'field': 'total_amount', 'old_value': 74930.0, 'new_value': 79825.0}, {'field': 'order_count', 'old_value': 1525, 'new_value': 1776}]
2025-05-12 12:01:21,351 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-12 12:01:21,737 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-12 12:01:21,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50129.0, 'new_value': 53363.0}, {'field': 'total_amount', 'old_value': 50129.0, 'new_value': 53363.0}, {'field': 'order_count', 'old_value': 282, 'new_value': 317}]
2025-05-12 12:01:21,738 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-12 12:01:22,172 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-12 12:01:22,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70961.66, 'new_value': 84668.66}, {'field': 'total_amount', 'old_value': 70961.66, 'new_value': 84668.66}, {'field': 'order_count', 'old_value': 940, 'new_value': 1071}]
2025-05-12 12:01:22,173 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-12 12:01:22,634 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-12 12:01:22,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12108.0, 'new_value': 13107.0}, {'field': 'total_amount', 'old_value': 12108.0, 'new_value': 13107.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-12 12:01:22,635 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-12 12:01:23,133 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-12 12:01:23,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73753.0, 'new_value': 91786.0}, {'field': 'total_amount', 'old_value': 73753.0, 'new_value': 91786.0}, {'field': 'order_count', 'old_value': 2361, 'new_value': 2947}]
2025-05-12 12:01:23,133 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-12 12:01:23,579 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-12 12:01:23,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39592.0, 'new_value': 59492.34}, {'field': 'total_amount', 'old_value': 39592.0, 'new_value': 59492.34}, {'field': 'order_count', 'old_value': 12, 'new_value': 18}]
2025-05-12 12:01:23,580 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-12 12:01:24,073 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-12 12:01:24,073 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 15, 'new_value': 14}]
2025-05-12 12:01:24,073 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-12 12:01:24,529 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-12 12:01:24,529 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126554.94, 'new_value': 154019.52}, {'field': 'offline_amount', 'old_value': 40868.22, 'new_value': 42436.22}, {'field': 'total_amount', 'old_value': 167423.16, 'new_value': 196455.74}, {'field': 'order_count', 'old_value': 287, 'new_value': 341}]
2025-05-12 12:01:24,529 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-12 12:01:24,989 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-12 12:01:24,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3528.27, 'new_value': 3088.62}, {'field': 'total_amount', 'old_value': 3528.27, 'new_value': 3088.62}, {'field': 'order_count', 'old_value': 116, 'new_value': 101}]
2025-05-12 12:01:24,989 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-12 12:01:25,414 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-12 12:01:25,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34046.0, 'new_value': 37926.0}, {'field': 'total_amount', 'old_value': 34046.0, 'new_value': 37926.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-12 12:01:25,414 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-12 12:01:25,829 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-12 12:01:25,829 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26892.0, 'new_value': 37898.0}, {'field': 'total_amount', 'old_value': 26892.0, 'new_value': 37898.0}, {'field': 'order_count', 'old_value': 564, 'new_value': 856}]
2025-05-12 12:01:25,829 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-12 12:01:26,322 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-12 12:01:26,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11302.0, 'new_value': 12261.0}, {'field': 'total_amount', 'old_value': 11302.0, 'new_value': 12261.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 64}]
2025-05-12 12:01:26,322 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-12 12:01:26,794 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-12 12:01:26,794 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37662.55, 'new_value': 42615.85}, {'field': 'total_amount', 'old_value': 37662.55, 'new_value': 42615.85}, {'field': 'order_count', 'old_value': 2156, 'new_value': 2426}]
2025-05-12 12:01:26,794 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-12 12:01:27,255 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-12 12:01:27,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64600.0, 'new_value': 81840.0}, {'field': 'total_amount', 'old_value': 64600.0, 'new_value': 81840.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 9}]
2025-05-12 12:01:27,255 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-12 12:01:27,715 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-12 12:01:27,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86282.5, 'new_value': 97604.3}, {'field': 'total_amount', 'old_value': 86282.5, 'new_value': 97604.3}, {'field': 'order_count', 'old_value': 1082, 'new_value': 1211}]
2025-05-12 12:01:27,716 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-12 12:01:28,294 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-12 12:01:28,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117206.5, 'new_value': 143849.5}, {'field': 'total_amount', 'old_value': 117206.5, 'new_value': 143849.5}, {'field': 'order_count', 'old_value': 21, 'new_value': 26}]
2025-05-12 12:01:28,295 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-12 12:01:28,779 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-12 12:01:28,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17876.53, 'new_value': 19807.43}, {'field': 'offline_amount', 'old_value': 511619.12, 'new_value': 572019.7}, {'field': 'total_amount', 'old_value': 529495.65, 'new_value': 591827.13}, {'field': 'order_count', 'old_value': 2277, 'new_value': 2566}]
2025-05-12 12:01:28,779 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-12 12:01:29,269 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-12 12:01:29,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3896.0, 'new_value': 2296.0}, {'field': 'offline_amount', 'old_value': 1996.0, 'new_value': 3596.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 26}]
2025-05-12 12:01:29,270 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-12 12:01:30,033 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-12 12:01:30,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132435.0, 'new_value': 145615.0}, {'field': 'total_amount', 'old_value': 146210.0, 'new_value': 159390.0}, {'field': 'order_count', 'old_value': 2951, 'new_value': 3256}]
2025-05-12 12:01:30,034 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-12 12:01:30,436 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-12 12:01:30,436 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37328.87, 'new_value': 38547.21}, {'field': 'offline_amount', 'old_value': 88959.69, 'new_value': 107902.36}, {'field': 'total_amount', 'old_value': 126288.56, 'new_value': 146449.57}, {'field': 'order_count', 'old_value': 2109, 'new_value': 2379}]
2025-05-12 12:01:30,436 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-12 12:01:30,877 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-12 12:01:30,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21457.0, 'new_value': 25421.0}, {'field': 'total_amount', 'old_value': 21457.0, 'new_value': 25421.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 100}]
2025-05-12 12:01:30,877 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-12 12:01:31,404 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-12 12:01:31,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257273.7, 'new_value': 306341.4}, {'field': 'total_amount', 'old_value': 401007.1, 'new_value': 450074.8}, {'field': 'order_count', 'old_value': 2988, 'new_value': 3037}]
2025-05-12 12:01:31,404 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-12 12:01:31,900 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-12 12:01:31,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100693.0, 'new_value': 115606.0}, {'field': 'total_amount', 'old_value': 100693.0, 'new_value': 115606.0}, {'field': 'order_count', 'old_value': 1660, 'new_value': 1908}]
2025-05-12 12:01:31,901 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-12 12:01:32,358 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-12 12:01:32,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87920.97, 'new_value': 101108.96}, {'field': 'total_amount', 'old_value': 87920.97, 'new_value': 101108.96}, {'field': 'order_count', 'old_value': 3659, 'new_value': 4209}]
2025-05-12 12:01:32,359 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-12 12:01:32,852 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-12 12:01:32,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105276.38, 'new_value': 118684.66}, {'field': 'total_amount', 'old_value': 105276.38, 'new_value': 118684.66}, {'field': 'order_count', 'old_value': 763, 'new_value': 862}]
2025-05-12 12:01:32,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-12 12:01:33,314 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-12 12:01:33,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47481.5, 'new_value': 52624.0}, {'field': 'total_amount', 'old_value': 49010.3, 'new_value': 54152.8}, {'field': 'order_count', 'old_value': 292, 'new_value': 328}]
2025-05-12 12:01:33,314 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-12 12:01:33,720 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-12 12:01:33,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56548.41, 'new_value': 69472.67}, {'field': 'total_amount', 'old_value': 75588.01, 'new_value': 88512.27}, {'field': 'order_count', 'old_value': 2195, 'new_value': 2514}]
2025-05-12 12:01:33,721 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-12 12:01:34,250 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-12 12:01:34,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30280.0, 'new_value': 33796.0}, {'field': 'total_amount', 'old_value': 30280.0, 'new_value': 33796.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 72}]
2025-05-12 12:01:34,250 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-12 12:01:34,774 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-12 12:01:34,774 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8672.6, 'new_value': 9615.6}, {'field': 'offline_amount', 'old_value': 24845.1, 'new_value': 28199.8}, {'field': 'total_amount', 'old_value': 33517.7, 'new_value': 37815.4}, {'field': 'order_count', 'old_value': 389, 'new_value': 423}]
2025-05-12 12:01:34,775 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-12 12:01:35,254 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-12 12:01:35,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41021.37, 'new_value': 44795.95}, {'field': 'offline_amount', 'old_value': 90426.75, 'new_value': 103304.9}, {'field': 'total_amount', 'old_value': 131448.12, 'new_value': 148100.85}, {'field': 'order_count', 'old_value': 4035, 'new_value': 4494}]
2025-05-12 12:01:35,254 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-12 12:01:35,709 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-12 12:01:35,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44105.0, 'new_value': 85243.0}, {'field': 'total_amount', 'old_value': 44105.0, 'new_value': 85243.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 12}]
2025-05-12 12:01:35,709 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-12 12:01:36,288 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-12 12:01:36,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42459.21, 'new_value': 57745.84}, {'field': 'total_amount', 'old_value': 42467.21, 'new_value': 57753.84}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-12 12:01:36,288 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-12 12:01:36,767 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-12 12:01:36,767 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66607.67, 'new_value': 73884.74}, {'field': 'offline_amount', 'old_value': 25108.03, 'new_value': 27906.19}, {'field': 'total_amount', 'old_value': 91715.7, 'new_value': 101790.93}, {'field': 'order_count', 'old_value': 5153, 'new_value': 5713}]
2025-05-12 12:01:36,767 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-12 12:01:37,318 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-12 12:01:37,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34645.6, 'new_value': 49615.9}, {'field': 'offline_amount', 'old_value': 15513.8, 'new_value': 18463.8}, {'field': 'total_amount', 'old_value': 50159.4, 'new_value': 68079.7}, {'field': 'order_count', 'old_value': 4216, 'new_value': 5638}]
2025-05-12 12:01:37,319 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-12 12:01:37,766 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-12 12:01:37,766 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67138.84, 'new_value': 75437.54}, {'field': 'offline_amount', 'old_value': 134487.69, 'new_value': 151736.7}, {'field': 'total_amount', 'old_value': 201626.53, 'new_value': 227174.24}, {'field': 'order_count', 'old_value': 1568, 'new_value': 1766}]
2025-05-12 12:01:37,766 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-12 12:01:38,187 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-12 12:01:38,187 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7198.0, 'new_value': 7906.04}, {'field': 'offline_amount', 'old_value': 17686.5, 'new_value': 20367.9}, {'field': 'total_amount', 'old_value': 24884.5, 'new_value': 28273.94}, {'field': 'order_count', 'old_value': 995, 'new_value': 1114}]
2025-05-12 12:01:38,187 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-12 12:01:38,724 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-12 12:01:38,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7450.0, 'new_value': 12200.0}, {'field': 'total_amount', 'old_value': 7450.0, 'new_value': 12200.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-12 12:01:38,725 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-12 12:01:39,174 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-12 12:01:39,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39994.0, 'new_value': 40418.0}, {'field': 'total_amount', 'old_value': 39994.0, 'new_value': 40418.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-12 12:01:39,175 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-12 12:01:39,619 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-12 12:01:39,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20400.0, 'new_value': 47640.0}, {'field': 'total_amount', 'old_value': 20400.0, 'new_value': 47640.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 13}]
2025-05-12 12:01:39,619 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-12 12:01:40,068 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-12 12:01:40,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 365549.85, 'new_value': 424762.15}, {'field': 'total_amount', 'old_value': 365549.85, 'new_value': 424762.15}, {'field': 'order_count', 'old_value': 2719, 'new_value': 3113}]
2025-05-12 12:01:40,068 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-12 12:01:40,490 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-12 12:01:40,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203107.0, 'new_value': 223086.0}, {'field': 'total_amount', 'old_value': 203107.0, 'new_value': 223086.0}, {'field': 'order_count', 'old_value': 1650, 'new_value': 1877}]
2025-05-12 12:01:40,490 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-12 12:01:40,952 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-12 12:01:40,952 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9814.26, 'new_value': 12689.27}, {'field': 'offline_amount', 'old_value': 9986.4, 'new_value': 13890.15}, {'field': 'total_amount', 'old_value': 19800.66, 'new_value': 26579.42}, {'field': 'order_count', 'old_value': 890, 'new_value': 1229}]
2025-05-12 12:01:40,952 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-12 12:01:41,401 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-12 12:01:41,401 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1577.0, 'new_value': 1835.0}, {'field': 'offline_amount', 'old_value': 15396.0, 'new_value': 17390.0}, {'field': 'total_amount', 'old_value': 16973.0, 'new_value': 19225.0}, {'field': 'order_count', 'old_value': 623, 'new_value': 701}]
2025-05-12 12:01:41,401 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-12 12:01:41,876 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-12 12:01:41,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11260.0, 'new_value': 14640.0}, {'field': 'total_amount', 'old_value': 11260.0, 'new_value': 14640.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-12 12:01:41,877 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-12 12:01:42,324 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-12 12:01:42,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 454.0, 'new_value': 512.0}, {'field': 'offline_amount', 'old_value': 28916.0, 'new_value': 30905.0}, {'field': 'total_amount', 'old_value': 29370.0, 'new_value': 31417.0}, {'field': 'order_count', 'old_value': 226, 'new_value': 241}]
2025-05-12 12:01:42,325 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-12 12:01:42,792 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-12 12:01:42,792 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33244.0, 'new_value': 36462.0}, {'field': 'offline_amount', 'old_value': 109176.0, 'new_value': 121673.0}, {'field': 'total_amount', 'old_value': 142420.0, 'new_value': 158135.0}, {'field': 'order_count', 'old_value': 639, 'new_value': 709}]
2025-05-12 12:01:42,792 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-12 12:01:43,343 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-12 12:01:43,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1682001.71, 'new_value': 1938998.44}, {'field': 'total_amount', 'old_value': 1682001.71, 'new_value': 1938998.44}, {'field': 'order_count', 'old_value': 2852, 'new_value': 3228}]
2025-05-12 12:01:43,343 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-12 12:01:43,766 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-12 12:01:43,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67894.42, 'new_value': 74508.81}, {'field': 'total_amount', 'old_value': 67894.42, 'new_value': 74508.81}, {'field': 'order_count', 'old_value': 4630, 'new_value': 5071}]
2025-05-12 12:01:43,766 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-12 12:01:44,202 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-12 12:01:44,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20662.5, 'new_value': 21970.4}, {'field': 'total_amount', 'old_value': 20662.5, 'new_value': 21970.4}, {'field': 'order_count', 'old_value': 115, 'new_value': 125}]
2025-05-12 12:01:44,203 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-12 12:01:44,591 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-12 12:01:44,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20888.0, 'new_value': 20996.0}, {'field': 'total_amount', 'old_value': 20888.0, 'new_value': 20996.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-12 12:01:44,592 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-12 12:01:45,070 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-12 12:01:45,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42261.2, 'new_value': 48239.2}, {'field': 'total_amount', 'old_value': 42374.2, 'new_value': 48352.2}, {'field': 'order_count', 'old_value': 653, 'new_value': 741}]
2025-05-12 12:01:45,070 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-12 12:01:45,518 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-12 12:01:45,519 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1496.0, 'new_value': 2019.0}, {'field': 'offline_amount', 'old_value': 5733.0, 'new_value': 8921.0}, {'field': 'total_amount', 'old_value': 7229.0, 'new_value': 10940.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 266}]
2025-05-12 12:01:45,519 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-12 12:01:46,079 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-12 12:01:46,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57822.86, 'new_value': 86637.9}, {'field': 'total_amount', 'old_value': 57822.86, 'new_value': 86637.9}, {'field': 'order_count', 'old_value': 4263, 'new_value': 5999}]
2025-05-12 12:01:46,079 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-12 12:01:46,566 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-12 12:01:46,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11651.01, 'new_value': 16903.68}, {'field': 'total_amount', 'old_value': 11651.01, 'new_value': 16903.68}, {'field': 'order_count', 'old_value': 552, 'new_value': 793}]
2025-05-12 12:01:46,567 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-12 12:01:47,087 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-12 12:01:47,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30489.38, 'new_value': 32934.66}, {'field': 'total_amount', 'old_value': 30489.38, 'new_value': 32934.66}, {'field': 'order_count', 'old_value': 481, 'new_value': 500}]
2025-05-12 12:01:47,087 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMDQ
2025-05-12 12:01:47,547 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMDQ
2025-05-12 12:01:47,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44137.0, 'new_value': 48136.0}, {'field': 'total_amount', 'old_value': 44137.0, 'new_value': 48136.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-12 12:01:47,547 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-12 12:01:48,044 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-12 12:01:48,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69459.9, 'new_value': 111534.9}, {'field': 'total_amount', 'old_value': 69459.9, 'new_value': 111534.9}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-12 12:01:48,045 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-12 12:01:48,554 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-12 12:01:48,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244285.47, 'new_value': 293157.47}, {'field': 'total_amount', 'old_value': 244285.47, 'new_value': 293157.47}, {'field': 'order_count', 'old_value': 1453, 'new_value': 4105}]
2025-05-12 12:01:48,555 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-12 12:01:49,009 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-12 12:01:49,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68956.21, 'new_value': 75770.31}, {'field': 'total_amount', 'old_value': 68956.21, 'new_value': 75770.31}, {'field': 'order_count', 'old_value': 1423, 'new_value': 1572}]
2025-05-12 12:01:49,009 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-12 12:01:49,417 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-12 12:01:49,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28239.88, 'new_value': 37516.31}, {'field': 'total_amount', 'old_value': 28239.88, 'new_value': 37516.31}, {'field': 'order_count', 'old_value': 2608, 'new_value': 3597}]
2025-05-12 12:01:49,417 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-12 12:01:49,864 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-12 12:01:49,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37410.04, 'new_value': 43112.41}, {'field': 'total_amount', 'old_value': 37410.04, 'new_value': 43112.41}, {'field': 'order_count', 'old_value': 523, 'new_value': 593}]
2025-05-12 12:01:49,864 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-12 12:01:50,270 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-12 12:01:50,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58005.0, 'new_value': 62940.2}, {'field': 'total_amount', 'old_value': 58005.0, 'new_value': 62940.2}, {'field': 'order_count', 'old_value': 1447, 'new_value': 1573}]
2025-05-12 12:01:50,271 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-12 12:01:50,732 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-12 12:01:50,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11816.0, 'new_value': 13122.0}, {'field': 'total_amount', 'old_value': 11816.0, 'new_value': 13122.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-05-12 12:01:50,732 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-12 12:01:51,242 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-12 12:01:51,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39149.35, 'new_value': 42947.14}, {'field': 'offline_amount', 'old_value': 201980.3, 'new_value': 242137.7}, {'field': 'total_amount', 'old_value': 241129.65, 'new_value': 285084.84}, {'field': 'order_count', 'old_value': 1513, 'new_value': 1705}]
2025-05-12 12:01:51,242 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-12 12:01:51,670 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-12 12:01:51,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 489195.27, 'new_value': 565896.74}, {'field': 'total_amount', 'old_value': 489195.27, 'new_value': 565896.74}, {'field': 'order_count', 'old_value': 1476, 'new_value': 1677}]
2025-05-12 12:01:51,670 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-12 12:01:52,122 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-12 12:01:52,122 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 7, 'new_value': 0}]
2025-05-12 12:01:52,123 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-12 12:01:52,555 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-12 12:01:52,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 296763.62, 'new_value': 345816.13}, {'field': 'total_amount', 'old_value': 296763.62, 'new_value': 345816.13}, {'field': 'order_count', 'old_value': 1371, 'new_value': 1570}]
2025-05-12 12:01:52,556 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-12 12:01:53,028 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-12 12:01:53,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 322727.51, 'new_value': 367531.07}, {'field': 'total_amount', 'old_value': 322727.51, 'new_value': 367531.07}, {'field': 'order_count', 'old_value': 1026, 'new_value': 1149}]
2025-05-12 12:01:53,028 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-12 12:01:53,456 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-12 12:01:53,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10988.0, 'new_value': 19711.0}, {'field': 'total_amount', 'old_value': 10988.0, 'new_value': 19711.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 14}]
2025-05-12 12:01:53,456 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-12 12:01:53,993 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-12 12:01:53,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5683.0, 'new_value': 9081.0}, {'field': 'offline_amount', 'old_value': 8083.0, 'new_value': 11481.0}, {'field': 'total_amount', 'old_value': 13766.0, 'new_value': 20562.0}, {'field': 'order_count', 'old_value': 5721, 'new_value': 9119}]
2025-05-12 12:01:53,993 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-12 12:01:54,480 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-12 12:01:54,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31844.0, 'new_value': 43618.0}, {'field': 'total_amount', 'old_value': 31844.0, 'new_value': 43618.0}, {'field': 'order_count', 'old_value': 3204, 'new_value': 4424}]
2025-05-12 12:01:54,480 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-12 12:01:54,937 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-12 12:01:54,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20865.0, 'new_value': 23505.0}, {'field': 'total_amount', 'old_value': 24255.0, 'new_value': 26895.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-12 12:01:54,937 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-12 12:01:55,405 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-12 12:01:55,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47500.0, 'new_value': 50558.0}, {'field': 'total_amount', 'old_value': 47500.0, 'new_value': 50558.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 242}]
2025-05-12 12:01:55,405 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-12 12:01:55,861 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-12 12:01:55,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74185.0, 'new_value': 85985.0}, {'field': 'total_amount', 'old_value': 74185.0, 'new_value': 85985.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 208}]
2025-05-12 12:01:55,861 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-12 12:01:56,345 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-12 12:01:56,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58620.0, 'new_value': 63292.0}, {'field': 'total_amount', 'old_value': 58620.0, 'new_value': 63292.0}, {'field': 'order_count', 'old_value': 493, 'new_value': 543}]
2025-05-12 12:01:56,345 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-12 12:01:56,755 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-12 12:01:56,755 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 575, 'new_value': 626}]
2025-05-12 12:01:56,755 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-12 12:01:57,251 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-12 12:01:57,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22060.0, 'new_value': 26040.0}, {'field': 'total_amount', 'old_value': 22060.0, 'new_value': 26040.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-12 12:01:57,251 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-12 12:01:57,789 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-12 12:01:57,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7441740.16, 'new_value': 8106888.02}, {'field': 'total_amount', 'old_value': 7441740.16, 'new_value': 8106888.02}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-12 12:01:57,790 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-12 12:01:58,397 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-12 12:01:58,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22614.36, 'new_value': 24281.36}, {'field': 'total_amount', 'old_value': 26613.36, 'new_value': 28280.36}, {'field': 'order_count', 'old_value': 1807, 'new_value': 1890}]
2025-05-12 12:01:58,397 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-12 12:01:58,880 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-12 12:01:58,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75702.46, 'new_value': 82335.29}, {'field': 'total_amount', 'old_value': 75702.46, 'new_value': 82335.29}, {'field': 'order_count', 'old_value': 7651, 'new_value': 8383}]
2025-05-12 12:01:58,881 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-12 12:01:59,433 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-12 12:01:59,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10196.0, 'new_value': 11027.0}, {'field': 'total_amount', 'old_value': 10196.0, 'new_value': 11027.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 67}]
2025-05-12 12:01:59,433 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-12 12:01:59,902 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-12 12:01:59,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12595.01, 'new_value': 16727.43}, {'field': 'total_amount', 'old_value': 12595.01, 'new_value': 16727.43}, {'field': 'order_count', 'old_value': 409, 'new_value': 611}]
2025-05-12 12:01:59,902 - INFO - 日期 2025-05 处理完成 - 更新: 192 条，插入: 0 条，错误: 0 条
2025-05-12 12:01:59,902 - INFO - 数据同步完成！更新: 192 条，插入: 0 条，错误: 0 条
2025-05-12 12:01:59,905 - INFO - =================同步完成====================
2025-05-12 15:00:01,890 - INFO - =================使用默认全量同步=============
2025-05-12 15:00:03,236 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-12 15:00:03,237 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-12 15:00:03,263 - INFO - 开始处理日期: 2025-01
2025-05-12 15:00:03,266 - INFO - Request Parameters - Page 1:
2025-05-12 15:00:03,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:03,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:04,374 - INFO - Response - Page 1:
2025-05-12 15:00:04,575 - INFO - 第 1 页获取到 100 条记录
2025-05-12 15:00:04,575 - INFO - Request Parameters - Page 2:
2025-05-12 15:00:04,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:04,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:05,045 - INFO - Response - Page 2:
2025-05-12 15:00:05,245 - INFO - 第 2 页获取到 100 条记录
2025-05-12 15:00:05,245 - INFO - Request Parameters - Page 3:
2025-05-12 15:00:05,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:05,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:05,955 - INFO - Response - Page 3:
2025-05-12 15:00:06,156 - INFO - 第 3 页获取到 100 条记录
2025-05-12 15:00:06,156 - INFO - Request Parameters - Page 4:
2025-05-12 15:00:06,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:06,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:06,706 - INFO - Response - Page 4:
2025-05-12 15:00:06,908 - INFO - 第 4 页获取到 100 条记录
2025-05-12 15:00:06,908 - INFO - Request Parameters - Page 5:
2025-05-12 15:00:06,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:06,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:07,404 - INFO - Response - Page 5:
2025-05-12 15:00:07,604 - INFO - 第 5 页获取到 100 条记录
2025-05-12 15:00:07,604 - INFO - Request Parameters - Page 6:
2025-05-12 15:00:07,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:07,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:08,055 - INFO - Response - Page 6:
2025-05-12 15:00:08,255 - INFO - 第 6 页获取到 100 条记录
2025-05-12 15:00:08,255 - INFO - Request Parameters - Page 7:
2025-05-12 15:00:08,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:08,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:08,778 - INFO - Response - Page 7:
2025-05-12 15:00:08,978 - INFO - 第 7 页获取到 82 条记录
2025-05-12 15:00:08,978 - INFO - 查询完成，共获取到 682 条记录
2025-05-12 15:00:08,978 - INFO - 获取到 682 条表单数据
2025-05-12 15:00:08,990 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-12 15:00:09,001 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 15:00:09,001 - INFO - 开始处理日期: 2025-02
2025-05-12 15:00:09,001 - INFO - Request Parameters - Page 1:
2025-05-12 15:00:09,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:09,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:09,507 - INFO - Response - Page 1:
2025-05-12 15:00:09,707 - INFO - 第 1 页获取到 100 条记录
2025-05-12 15:00:09,707 - INFO - Request Parameters - Page 2:
2025-05-12 15:00:09,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:09,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:10,171 - INFO - Response - Page 2:
2025-05-12 15:00:10,372 - INFO - 第 2 页获取到 100 条记录
2025-05-12 15:00:10,372 - INFO - Request Parameters - Page 3:
2025-05-12 15:00:10,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:10,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:10,926 - INFO - Response - Page 3:
2025-05-12 15:00:11,127 - INFO - 第 3 页获取到 100 条记录
2025-05-12 15:00:11,127 - INFO - Request Parameters - Page 4:
2025-05-12 15:00:11,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:11,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:11,693 - INFO - Response - Page 4:
2025-05-12 15:00:11,893 - INFO - 第 4 页获取到 100 条记录
2025-05-12 15:00:11,893 - INFO - Request Parameters - Page 5:
2025-05-12 15:00:11,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:11,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:12,378 - INFO - Response - Page 5:
2025-05-12 15:00:12,578 - INFO - 第 5 页获取到 100 条记录
2025-05-12 15:00:12,578 - INFO - Request Parameters - Page 6:
2025-05-12 15:00:12,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:12,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:13,089 - INFO - Response - Page 6:
2025-05-12 15:00:13,289 - INFO - 第 6 页获取到 100 条记录
2025-05-12 15:00:13,289 - INFO - Request Parameters - Page 7:
2025-05-12 15:00:13,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:13,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:13,826 - INFO - Response - Page 7:
2025-05-12 15:00:14,027 - INFO - 第 7 页获取到 70 条记录
2025-05-12 15:00:14,027 - INFO - 查询完成，共获取到 670 条记录
2025-05-12 15:00:14,027 - INFO - 获取到 670 条表单数据
2025-05-12 15:00:14,038 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-12 15:00:14,050 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 15:00:14,050 - INFO - 开始处理日期: 2025-03
2025-05-12 15:00:14,050 - INFO - Request Parameters - Page 1:
2025-05-12 15:00:14,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:14,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:14,536 - INFO - Response - Page 1:
2025-05-12 15:00:14,737 - INFO - 第 1 页获取到 100 条记录
2025-05-12 15:00:14,737 - INFO - Request Parameters - Page 2:
2025-05-12 15:00:14,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:14,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:15,219 - INFO - Response - Page 2:
2025-05-12 15:00:15,419 - INFO - 第 2 页获取到 100 条记录
2025-05-12 15:00:15,419 - INFO - Request Parameters - Page 3:
2025-05-12 15:00:15,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:15,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:15,879 - INFO - Response - Page 3:
2025-05-12 15:00:16,079 - INFO - 第 3 页获取到 100 条记录
2025-05-12 15:00:16,079 - INFO - Request Parameters - Page 4:
2025-05-12 15:00:16,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:16,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:16,528 - INFO - Response - Page 4:
2025-05-12 15:00:16,728 - INFO - 第 4 页获取到 100 条记录
2025-05-12 15:00:16,728 - INFO - Request Parameters - Page 5:
2025-05-12 15:00:16,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:16,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:17,240 - INFO - Response - Page 5:
2025-05-12 15:00:17,440 - INFO - 第 5 页获取到 100 条记录
2025-05-12 15:00:17,440 - INFO - Request Parameters - Page 6:
2025-05-12 15:00:17,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:17,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:17,875 - INFO - Response - Page 6:
2025-05-12 15:00:18,075 - INFO - 第 6 页获取到 100 条记录
2025-05-12 15:00:18,075 - INFO - Request Parameters - Page 7:
2025-05-12 15:00:18,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:18,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:18,537 - INFO - Response - Page 7:
2025-05-12 15:00:18,738 - INFO - 第 7 页获取到 61 条记录
2025-05-12 15:00:18,738 - INFO - 查询完成，共获取到 661 条记录
2025-05-12 15:00:18,738 - INFO - 获取到 661 条表单数据
2025-05-12 15:00:18,752 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-12 15:00:18,764 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 15:00:18,764 - INFO - 开始处理日期: 2025-04
2025-05-12 15:00:18,765 - INFO - Request Parameters - Page 1:
2025-05-12 15:00:18,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:18,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:19,251 - INFO - Response - Page 1:
2025-05-12 15:00:19,452 - INFO - 第 1 页获取到 100 条记录
2025-05-12 15:00:19,452 - INFO - Request Parameters - Page 2:
2025-05-12 15:00:19,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:19,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:19,873 - INFO - Response - Page 2:
2025-05-12 15:00:20,074 - INFO - 第 2 页获取到 100 条记录
2025-05-12 15:00:20,074 - INFO - Request Parameters - Page 3:
2025-05-12 15:00:20,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:20,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:20,587 - INFO - Response - Page 3:
2025-05-12 15:00:20,787 - INFO - 第 3 页获取到 100 条记录
2025-05-12 15:00:20,787 - INFO - Request Parameters - Page 4:
2025-05-12 15:00:20,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:20,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:21,237 - INFO - Response - Page 4:
2025-05-12 15:00:21,437 - INFO - 第 4 页获取到 100 条记录
2025-05-12 15:00:21,437 - INFO - Request Parameters - Page 5:
2025-05-12 15:00:21,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:21,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:21,909 - INFO - Response - Page 5:
2025-05-12 15:00:22,110 - INFO - 第 5 页获取到 100 条记录
2025-05-12 15:00:22,110 - INFO - Request Parameters - Page 6:
2025-05-12 15:00:22,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:22,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:22,626 - INFO - Response - Page 6:
2025-05-12 15:00:22,826 - INFO - 第 6 页获取到 100 条记录
2025-05-12 15:00:22,826 - INFO - Request Parameters - Page 7:
2025-05-12 15:00:22,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:22,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:23,229 - INFO - Response - Page 7:
2025-05-12 15:00:23,429 - INFO - 第 7 页获取到 54 条记录
2025-05-12 15:00:23,429 - INFO - 查询完成，共获取到 654 条记录
2025-05-12 15:00:23,429 - INFO - 获取到 654 条表单数据
2025-05-12 15:00:23,441 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-12 15:00:23,442 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-05-12 15:00:23,910 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-05-12 15:00:23,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7154.55, 'new_value': 7988.55}, {'field': 'total_amount', 'old_value': 7154.55, 'new_value': 7988.55}, {'field': 'order_count', 'old_value': 945, 'new_value': 121}]
2025-05-12 15:00:23,911 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-05-12 15:00:24,353 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-05-12 15:00:24,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1083243.0, 'new_value': 1061542.0}, {'field': 'total_amount', 'old_value': 1083243.0, 'new_value': 1061542.0}]
2025-05-12 15:00:24,355 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-05-12 15:00:24,766 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-05-12 15:00:24,766 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36224.1, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 54.0, 'new_value': 36078.0}, {'field': 'total_amount', 'old_value': 36278.1, 'new_value': 36078.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 77}]
2025-05-12 15:00:24,767 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-05-12 15:00:25,287 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-05-12 15:00:25,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1493.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 78472.3, 'new_value': 79695.3}, {'field': 'total_amount', 'old_value': 79965.3, 'new_value': 79695.3}]
2025-05-12 15:00:25,288 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-05-12 15:00:25,732 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-05-12 15:00:25,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34615.2, 'new_value': 35065.0}, {'field': 'total_amount', 'old_value': 34615.2, 'new_value': 35065.0}]
2025-05-12 15:00:25,733 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-05-12 15:00:26,200 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-05-12 15:00:26,200 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3641.3, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 130196.3, 'new_value': 95994.7}, {'field': 'total_amount', 'old_value': 133837.6, 'new_value': 95994.7}]
2025-05-12 15:00:26,203 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-05-12 15:00:26,727 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-05-12 15:00:26,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134637.86, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 133542.95, 'new_value': 274173.89}, {'field': 'total_amount', 'old_value': 268180.81, 'new_value': 274173.89}]
2025-05-12 15:00:26,727 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMI
2025-05-12 15:00:27,156 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMI
2025-05-12 15:00:27,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55140.0, 'new_value': 101292.0}, {'field': 'total_amount', 'old_value': 55140.0, 'new_value': 101292.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-12 15:00:27,157 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-05-12 15:00:27,626 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-05-12 15:00:27,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1979137.0, 'new_value': 1961984.21}, {'field': 'total_amount', 'old_value': 1979137.0, 'new_value': 1961984.21}]
2025-05-12 15:00:27,627 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGJ
2025-05-12 15:00:28,045 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGJ
2025-05-12 15:00:28,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164617.0, 'new_value': 156625.0}, {'field': 'total_amount', 'old_value': 164617.0, 'new_value': 156625.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 32}]
2025-05-12 15:00:28,045 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-05-12 15:00:28,511 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-05-12 15:00:28,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356793.9, 'new_value': 356603.9}, {'field': 'total_amount', 'old_value': 356793.9, 'new_value': 356603.9}]
2025-05-12 15:00:28,512 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA
2025-05-12 15:00:29,012 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA
2025-05-12 15:00:29,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95916.0, 'new_value': 86796.0}, {'field': 'total_amount', 'old_value': 95916.0, 'new_value': 86796.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 11}]
2025-05-12 15:00:29,014 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M32
2025-05-12 15:00:29,436 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M32
2025-05-12 15:00:29,436 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3198.3, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 192642.36, 'new_value': 158660.61}, {'field': 'total_amount', 'old_value': 195840.66, 'new_value': 158660.61}, {'field': 'order_count', 'old_value': 85, 'new_value': 92}]
2025-05-12 15:00:29,437 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR6
2025-05-12 15:00:29,943 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR6
2025-05-12 15:00:29,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4440.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 72160.0, 'new_value': 90040.0}, {'field': 'total_amount', 'old_value': 76600.0, 'new_value': 90040.0}, {'field': 'order_count', 'old_value': 13451, 'new_value': 12}]
2025-05-12 15:00:29,945 - INFO - 日期 2025-04 处理完成 - 更新: 14 条，插入: 0 条，错误: 0 条
2025-05-12 15:00:29,945 - INFO - 开始处理日期: 2025-05
2025-05-12 15:00:29,945 - INFO - Request Parameters - Page 1:
2025-05-12 15:00:29,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:29,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:30,459 - INFO - Response - Page 1:
2025-05-12 15:00:30,660 - INFO - 第 1 页获取到 100 条记录
2025-05-12 15:00:30,660 - INFO - Request Parameters - Page 2:
2025-05-12 15:00:30,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:30,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:31,285 - INFO - Response - Page 2:
2025-05-12 15:00:31,485 - INFO - 第 2 页获取到 100 条记录
2025-05-12 15:00:31,485 - INFO - Request Parameters - Page 3:
2025-05-12 15:00:31,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:31,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:32,000 - INFO - Response - Page 3:
2025-05-12 15:00:32,200 - INFO - 第 3 页获取到 100 条记录
2025-05-12 15:00:32,200 - INFO - Request Parameters - Page 4:
2025-05-12 15:00:32,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:32,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:32,735 - INFO - Response - Page 4:
2025-05-12 15:00:32,935 - INFO - 第 4 页获取到 100 条记录
2025-05-12 15:00:32,935 - INFO - Request Parameters - Page 5:
2025-05-12 15:00:32,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:32,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:33,395 - INFO - Response - Page 5:
2025-05-12 15:00:33,595 - INFO - 第 5 页获取到 100 条记录
2025-05-12 15:00:33,595 - INFO - Request Parameters - Page 6:
2025-05-12 15:00:33,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:33,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:34,064 - INFO - Response - Page 6:
2025-05-12 15:00:34,264 - INFO - 第 6 页获取到 100 条记录
2025-05-12 15:00:34,264 - INFO - Request Parameters - Page 7:
2025-05-12 15:00:34,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 15:00:34,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 15:00:34,594 - INFO - Response - Page 7:
2025-05-12 15:00:34,795 - INFO - 第 7 页获取到 24 条记录
2025-05-12 15:00:34,795 - INFO - 查询完成，共获取到 624 条记录
2025-05-12 15:00:34,795 - INFO - 获取到 624 条表单数据
2025-05-12 15:00:34,807 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-12 15:00:34,811 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-12 15:00:35,315 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-12 15:00:35,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22432.0, 'new_value': 23552.0}, {'field': 'total_amount', 'old_value': 22432.0, 'new_value': 23552.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-12 15:00:35,316 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-12 15:00:35,829 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-12 15:00:35,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51658.36, 'new_value': 77533.96}, {'field': 'total_amount', 'old_value': 51658.36, 'new_value': 77533.96}, {'field': 'order_count', 'old_value': 88, 'new_value': 127}]
2025-05-12 15:00:35,830 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-12 15:00:36,199 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-12 15:00:36,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4161.0, 'new_value': 5158.0}, {'field': 'total_amount', 'old_value': 4161.0, 'new_value': 5158.0}]
2025-05-12 15:00:36,205 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-12 15:00:36,680 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-12 15:00:36,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99769.0, 'new_value': 103737.0}, {'field': 'total_amount', 'old_value': 105719.3, 'new_value': 109687.3}, {'field': 'order_count', 'old_value': 205, 'new_value': 215}]
2025-05-12 15:00:36,684 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-12 15:00:37,127 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-12 15:00:37,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36698.0, 'new_value': 43330.0}, {'field': 'total_amount', 'old_value': 36698.0, 'new_value': 43330.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 142}]
2025-05-12 15:00:37,128 - INFO - 日期 2025-05 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-05-12 15:00:37,128 - INFO - 数据同步完成！更新: 19 条，插入: 0 条，错误: 0 条
2025-05-12 15:00:37,130 - INFO - =================同步完成====================
2025-05-12 18:00:02,013 - INFO - =================使用默认全量同步=============
2025-05-12 18:00:03,356 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-12 18:00:03,357 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-12 18:00:03,385 - INFO - 开始处理日期: 2025-01
2025-05-12 18:00:03,388 - INFO - Request Parameters - Page 1:
2025-05-12 18:00:03,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:03,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:04,618 - INFO - Response - Page 1:
2025-05-12 18:00:04,818 - INFO - 第 1 页获取到 100 条记录
2025-05-12 18:00:04,818 - INFO - Request Parameters - Page 2:
2025-05-12 18:00:04,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:04,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:05,317 - INFO - Response - Page 2:
2025-05-12 18:00:05,518 - INFO - 第 2 页获取到 100 条记录
2025-05-12 18:00:05,518 - INFO - Request Parameters - Page 3:
2025-05-12 18:00:05,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:05,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:06,102 - INFO - Response - Page 3:
2025-05-12 18:00:06,303 - INFO - 第 3 页获取到 100 条记录
2025-05-12 18:00:06,303 - INFO - Request Parameters - Page 4:
2025-05-12 18:00:06,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:06,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:06,759 - INFO - Response - Page 4:
2025-05-12 18:00:06,959 - INFO - 第 4 页获取到 100 条记录
2025-05-12 18:00:06,959 - INFO - Request Parameters - Page 5:
2025-05-12 18:00:06,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:06,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:07,456 - INFO - Response - Page 5:
2025-05-12 18:00:07,657 - INFO - 第 5 页获取到 100 条记录
2025-05-12 18:00:07,657 - INFO - Request Parameters - Page 6:
2025-05-12 18:00:07,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:07,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:08,092 - INFO - Response - Page 6:
2025-05-12 18:00:08,292 - INFO - 第 6 页获取到 100 条记录
2025-05-12 18:00:08,292 - INFO - Request Parameters - Page 7:
2025-05-12 18:00:08,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:08,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:08,798 - INFO - Response - Page 7:
2025-05-12 18:00:08,999 - INFO - 第 7 页获取到 82 条记录
2025-05-12 18:00:08,999 - INFO - 查询完成，共获取到 682 条记录
2025-05-12 18:00:08,999 - INFO - 获取到 682 条表单数据
2025-05-12 18:00:09,011 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-12 18:00:09,022 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 18:00:09,022 - INFO - 开始处理日期: 2025-02
2025-05-12 18:00:09,022 - INFO - Request Parameters - Page 1:
2025-05-12 18:00:09,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:09,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:09,519 - INFO - Response - Page 1:
2025-05-12 18:00:09,720 - INFO - 第 1 页获取到 100 条记录
2025-05-12 18:00:09,720 - INFO - Request Parameters - Page 2:
2025-05-12 18:00:09,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:09,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:10,184 - INFO - Response - Page 2:
2025-05-12 18:00:10,384 - INFO - 第 2 页获取到 100 条记录
2025-05-12 18:00:10,384 - INFO - Request Parameters - Page 3:
2025-05-12 18:00:10,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:10,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:10,890 - INFO - Response - Page 3:
2025-05-12 18:00:11,090 - INFO - 第 3 页获取到 100 条记录
2025-05-12 18:00:11,090 - INFO - Request Parameters - Page 4:
2025-05-12 18:00:11,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:11,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:11,593 - INFO - Response - Page 4:
2025-05-12 18:00:11,793 - INFO - 第 4 页获取到 100 条记录
2025-05-12 18:00:11,793 - INFO - Request Parameters - Page 5:
2025-05-12 18:00:11,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:11,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:12,209 - INFO - Response - Page 5:
2025-05-12 18:00:12,409 - INFO - 第 5 页获取到 100 条记录
2025-05-12 18:00:12,409 - INFO - Request Parameters - Page 6:
2025-05-12 18:00:12,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:12,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:12,991 - INFO - Response - Page 6:
2025-05-12 18:00:13,191 - INFO - 第 6 页获取到 100 条记录
2025-05-12 18:00:13,191 - INFO - Request Parameters - Page 7:
2025-05-12 18:00:13,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:13,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:13,629 - INFO - Response - Page 7:
2025-05-12 18:00:13,830 - INFO - 第 7 页获取到 70 条记录
2025-05-12 18:00:13,830 - INFO - 查询完成，共获取到 670 条记录
2025-05-12 18:00:13,830 - INFO - 获取到 670 条表单数据
2025-05-12 18:00:13,843 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-12 18:00:13,853 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 18:00:13,854 - INFO - 开始处理日期: 2025-03
2025-05-12 18:00:13,854 - INFO - Request Parameters - Page 1:
2025-05-12 18:00:13,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:13,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:14,339 - INFO - Response - Page 1:
2025-05-12 18:00:14,539 - INFO - 第 1 页获取到 100 条记录
2025-05-12 18:00:14,539 - INFO - Request Parameters - Page 2:
2025-05-12 18:00:14,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:14,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:15,010 - INFO - Response - Page 2:
2025-05-12 18:00:15,210 - INFO - 第 2 页获取到 100 条记录
2025-05-12 18:00:15,210 - INFO - Request Parameters - Page 3:
2025-05-12 18:00:15,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:15,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:15,674 - INFO - Response - Page 3:
2025-05-12 18:00:15,875 - INFO - 第 3 页获取到 100 条记录
2025-05-12 18:00:15,875 - INFO - Request Parameters - Page 4:
2025-05-12 18:00:15,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:15,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:16,497 - INFO - Response - Page 4:
2025-05-12 18:00:16,697 - INFO - 第 4 页获取到 100 条记录
2025-05-12 18:00:16,697 - INFO - Request Parameters - Page 5:
2025-05-12 18:00:16,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:16,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:17,178 - INFO - Response - Page 5:
2025-05-12 18:00:17,378 - INFO - 第 5 页获取到 100 条记录
2025-05-12 18:00:17,378 - INFO - Request Parameters - Page 6:
2025-05-12 18:00:17,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:17,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:17,908 - INFO - Response - Page 6:
2025-05-12 18:00:18,108 - INFO - 第 6 页获取到 100 条记录
2025-05-12 18:00:18,108 - INFO - Request Parameters - Page 7:
2025-05-12 18:00:18,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:18,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:18,552 - INFO - Response - Page 7:
2025-05-12 18:00:18,752 - INFO - 第 7 页获取到 61 条记录
2025-05-12 18:00:18,752 - INFO - 查询完成，共获取到 661 条记录
2025-05-12 18:00:18,752 - INFO - 获取到 661 条表单数据
2025-05-12 18:00:18,764 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-12 18:00:18,775 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 18:00:18,775 - INFO - 开始处理日期: 2025-04
2025-05-12 18:00:18,775 - INFO - Request Parameters - Page 1:
2025-05-12 18:00:18,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:18,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:19,275 - INFO - Response - Page 1:
2025-05-12 18:00:19,475 - INFO - 第 1 页获取到 100 条记录
2025-05-12 18:00:19,475 - INFO - Request Parameters - Page 2:
2025-05-12 18:00:19,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:19,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:20,011 - INFO - Response - Page 2:
2025-05-12 18:00:20,211 - INFO - 第 2 页获取到 100 条记录
2025-05-12 18:00:20,211 - INFO - Request Parameters - Page 3:
2025-05-12 18:00:20,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:20,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:20,663 - INFO - Response - Page 3:
2025-05-12 18:00:20,863 - INFO - 第 3 页获取到 100 条记录
2025-05-12 18:00:20,863 - INFO - Request Parameters - Page 4:
2025-05-12 18:00:20,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:20,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:21,362 - INFO - Response - Page 4:
2025-05-12 18:00:21,562 - INFO - 第 4 页获取到 100 条记录
2025-05-12 18:00:21,562 - INFO - Request Parameters - Page 5:
2025-05-12 18:00:21,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:21,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:22,025 - INFO - Response - Page 5:
2025-05-12 18:00:22,225 - INFO - 第 5 页获取到 100 条记录
2025-05-12 18:00:22,225 - INFO - Request Parameters - Page 6:
2025-05-12 18:00:22,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:22,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:22,694 - INFO - Response - Page 6:
2025-05-12 18:00:22,894 - INFO - 第 6 页获取到 100 条记录
2025-05-12 18:00:22,894 - INFO - Request Parameters - Page 7:
2025-05-12 18:00:22,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:22,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:23,303 - INFO - Response - Page 7:
2025-05-12 18:00:23,503 - INFO - 第 7 页获取到 54 条记录
2025-05-12 18:00:23,503 - INFO - 查询完成，共获取到 654 条记录
2025-05-12 18:00:23,503 - INFO - 获取到 654 条表单数据
2025-05-12 18:00:23,514 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-12 18:00:23,516 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-05-12 18:00:23,986 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-05-12 18:00:23,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79618.81, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 116897.59, 'new_value': 200969.95}, {'field': 'total_amount', 'old_value': 196516.4, 'new_value': 200969.95}]
2025-05-12 18:00:23,989 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-05-12 18:00:24,520 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-05-12 18:00:24,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30895.0, 'new_value': 32110.0}, {'field': 'total_amount', 'old_value': 30895.0, 'new_value': 32110.0}]
2025-05-12 18:00:24,522 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-05-12 18:00:24,986 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-05-12 18:00:24,986 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 628439.52, 'new_value': 682126.61}, {'field': 'total_amount', 'old_value': 628439.52, 'new_value': 682126.61}]
2025-05-12 18:00:24,991 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-05-12 18:00:25,475 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-05-12 18:00:25,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5249526.7, 'new_value': 5101291.68}, {'field': 'total_amount', 'old_value': 5249526.7, 'new_value': 5101291.68}, {'field': 'order_count', 'old_value': 148685, 'new_value': 103685}]
2025-05-12 18:00:25,477 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML7
2025-05-12 18:00:25,980 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML7
2025-05-12 18:00:25,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2820.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 181122.0, 'new_value': 173742.0}, {'field': 'total_amount', 'old_value': 183942.0, 'new_value': 173742.0}]
2025-05-12 18:00:25,982 - INFO - 日期 2025-04 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-05-12 18:00:25,982 - INFO - 开始处理日期: 2025-05
2025-05-12 18:00:25,982 - INFO - Request Parameters - Page 1:
2025-05-12 18:00:25,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:25,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:26,442 - INFO - Response - Page 1:
2025-05-12 18:00:26,643 - INFO - 第 1 页获取到 100 条记录
2025-05-12 18:00:26,643 - INFO - Request Parameters - Page 2:
2025-05-12 18:00:26,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:26,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:27,117 - INFO - Response - Page 2:
2025-05-12 18:00:27,317 - INFO - 第 2 页获取到 100 条记录
2025-05-12 18:00:27,317 - INFO - Request Parameters - Page 3:
2025-05-12 18:00:27,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:27,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:27,932 - INFO - Response - Page 3:
2025-05-12 18:00:28,133 - INFO - 第 3 页获取到 100 条记录
2025-05-12 18:00:28,133 - INFO - Request Parameters - Page 4:
2025-05-12 18:00:28,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:28,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:28,630 - INFO - Response - Page 4:
2025-05-12 18:00:28,830 - INFO - 第 4 页获取到 100 条记录
2025-05-12 18:00:28,830 - INFO - Request Parameters - Page 5:
2025-05-12 18:00:28,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:28,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:29,354 - INFO - Response - Page 5:
2025-05-12 18:00:29,555 - INFO - 第 5 页获取到 100 条记录
2025-05-12 18:00:29,555 - INFO - Request Parameters - Page 6:
2025-05-12 18:00:29,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:29,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:30,013 - INFO - Response - Page 6:
2025-05-12 18:00:30,213 - INFO - 第 6 页获取到 100 条记录
2025-05-12 18:00:30,213 - INFO - Request Parameters - Page 7:
2025-05-12 18:00:30,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 18:00:30,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 18:00:30,576 - INFO - Response - Page 7:
2025-05-12 18:00:30,776 - INFO - 第 7 页获取到 24 条记录
2025-05-12 18:00:30,776 - INFO - 查询完成，共获取到 624 条记录
2025-05-12 18:00:30,776 - INFO - 获取到 624 条表单数据
2025-05-12 18:00:30,788 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-12 18:00:30,788 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-12 18:00:31,222 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-12 18:00:31,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13133.9, 'new_value': 14206.7}, {'field': 'total_amount', 'old_value': 13133.9, 'new_value': 14206.7}, {'field': 'order_count', 'old_value': 81, 'new_value': 90}]
2025-05-12 18:00:31,223 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-12 18:00:31,708 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-12 18:00:31,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5165.9, 'new_value': 8362.6}, {'field': 'total_amount', 'old_value': 5515.9, 'new_value': 8712.6}, {'field': 'order_count', 'old_value': 406, 'new_value': 630}]
2025-05-12 18:00:31,709 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-12 18:00:32,202 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-12 18:00:32,202 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16706.0, 'new_value': 24232.0}, {'field': 'offline_amount', 'old_value': 61581.0, 'new_value': 83669.0}, {'field': 'total_amount', 'old_value': 78287.0, 'new_value': 107901.0}, {'field': 'order_count', 'old_value': 537, 'new_value': 736}]
2025-05-12 18:00:32,203 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-12 18:00:32,698 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-12 18:00:32,698 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7408.27, 'new_value': 10084.19}, {'field': 'offline_amount', 'old_value': 29659.21, 'new_value': 42538.51}, {'field': 'total_amount', 'old_value': 37067.48, 'new_value': 52622.7}, {'field': 'order_count', 'old_value': 510, 'new_value': 704}]
2025-05-12 18:00:32,699 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-12 18:00:33,249 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-12 18:00:33,249 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7838.83, 'new_value': 10863.46}, {'field': 'offline_amount', 'old_value': 7038.85, 'new_value': 9473.22}, {'field': 'total_amount', 'old_value': 14877.68, 'new_value': 20336.68}, {'field': 'order_count', 'old_value': 805, 'new_value': 1127}]
2025-05-12 18:00:33,249 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-12 18:00:33,692 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-12 18:00:33,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32310.0, 'new_value': 33390.0}, {'field': 'offline_amount', 'old_value': 27558.0, 'new_value': 30852.0}, {'field': 'total_amount', 'old_value': 59868.0, 'new_value': 64242.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-05-12 18:00:33,693 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-12 18:00:34,162 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-12 18:00:34,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1633.7, 'new_value': 2374.5}, {'field': 'offline_amount', 'old_value': 38458.73, 'new_value': 53082.83}, {'field': 'total_amount', 'old_value': 40092.43, 'new_value': 55457.33}, {'field': 'order_count', 'old_value': 653, 'new_value': 886}]
2025-05-12 18:00:34,162 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-12 18:00:34,678 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-12 18:00:34,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24930.68, 'new_value': 33298.99}, {'field': 'total_amount', 'old_value': 24930.68, 'new_value': 33298.99}, {'field': 'order_count', 'old_value': 1257, 'new_value': 1697}]
2025-05-12 18:00:34,678 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-12 18:00:35,145 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-12 18:00:35,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316383.0, 'new_value': 189687.3}, {'field': 'total_amount', 'old_value': 345488.28, 'new_value': 218792.58}]
2025-05-12 18:00:35,145 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-12 18:00:35,714 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-12 18:00:35,714 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16792.8, 'new_value': 21066.8}, {'field': 'total_amount', 'old_value': 16792.8, 'new_value': 21066.8}, {'field': 'order_count', 'old_value': 65, 'new_value': 88}]
2025-05-12 18:00:35,716 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-12 18:00:36,145 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-12 18:00:36,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55085.22, 'new_value': 66527.99}, {'field': 'total_amount', 'old_value': 55085.22, 'new_value': 66527.99}, {'field': 'order_count', 'old_value': 4674, 'new_value': 6100}]
2025-05-12 18:00:36,146 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-12 18:00:36,643 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-12 18:00:36,643 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25968.0, 'new_value': 35827.0}, {'field': 'total_amount', 'old_value': 25968.0, 'new_value': 35827.0}, {'field': 'order_count', 'old_value': 1487, 'new_value': 1984}]
2025-05-12 18:00:36,644 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-12 18:00:37,085 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-12 18:00:37,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31691.21, 'new_value': 47968.89}, {'field': 'total_amount', 'old_value': 31691.21, 'new_value': 47968.89}, {'field': 'order_count', 'old_value': 154, 'new_value': 228}]
2025-05-12 18:00:37,088 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-12 18:00:37,622 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-12 18:00:37,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5085.0, 'new_value': 5491.0}, {'field': 'total_amount', 'old_value': 5085.0, 'new_value': 5491.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 221}]
2025-05-12 18:00:37,622 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-12 18:00:38,052 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-12 18:00:38,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3131.0, 'new_value': 9589.0}, {'field': 'offline_amount', 'old_value': 9131.0, 'new_value': 15245.0}, {'field': 'total_amount', 'old_value': 12262.0, 'new_value': 24834.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 30}]
2025-05-12 18:00:38,053 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-12 18:00:38,520 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-12 18:00:38,520 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3469.61, 'new_value': 5189.97}, {'field': 'offline_amount', 'old_value': 18459.6, 'new_value': 27711.52}, {'field': 'total_amount', 'old_value': 21929.21, 'new_value': 32901.49}, {'field': 'order_count', 'old_value': 494, 'new_value': 728}]
2025-05-12 18:00:38,522 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-12 18:00:38,943 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-12 18:00:38,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123484.0, 'new_value': 134290.0}, {'field': 'total_amount', 'old_value': 123484.0, 'new_value': 134290.0}, {'field': 'order_count', 'old_value': 2469, 'new_value': 2767}]
2025-05-12 18:00:38,944 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-12 18:00:39,407 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-12 18:00:39,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16911.0, 'new_value': 24070.0}, {'field': 'total_amount', 'old_value': 16911.0, 'new_value': 24070.0}, {'field': 'order_count', 'old_value': 3402, 'new_value': 4862}]
2025-05-12 18:00:39,407 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-12 18:00:39,873 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-12 18:00:39,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24010.0, 'new_value': 34750.0}, {'field': 'total_amount', 'old_value': 24010.0, 'new_value': 34750.0}, {'field': 'order_count', 'old_value': 3402, 'new_value': 4862}]
2025-05-12 18:00:39,874 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-12 18:00:40,323 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-12 18:00:40,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32091.0, 'new_value': 50216.0}, {'field': 'total_amount', 'old_value': 32091.0, 'new_value': 50216.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 136}]
2025-05-12 18:00:40,323 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-12 18:00:40,783 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-12 18:00:40,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1129.0, 'new_value': 9049.0}, {'field': 'total_amount', 'old_value': 1129.0, 'new_value': 9049.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 44}]
2025-05-12 18:00:40,784 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF
2025-05-12 18:00:41,211 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF
2025-05-12 18:00:41,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6205.0, 'new_value': 6990.0}, {'field': 'total_amount', 'old_value': 6205.0, 'new_value': 6990.0}, {'field': 'order_count', 'old_value': 836, 'new_value': 959}]
2025-05-12 18:00:41,212 - INFO - 日期 2025-05 处理完成 - 更新: 22 条，插入: 0 条，错误: 0 条
2025-05-12 18:00:41,212 - INFO - 数据同步完成！更新: 27 条，插入: 0 条，错误: 0 条
2025-05-12 18:00:41,214 - INFO - =================同步完成====================
2025-05-12 21:00:01,957 - INFO - =================使用默认全量同步=============
2025-05-12 21:00:03,285 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-12 21:00:03,285 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-12 21:00:03,312 - INFO - 开始处理日期: 2025-01
2025-05-12 21:00:03,315 - INFO - Request Parameters - Page 1:
2025-05-12 21:00:03,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:03,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:04,167 - INFO - Response - Page 1:
2025-05-12 21:00:04,367 - INFO - 第 1 页获取到 100 条记录
2025-05-12 21:00:04,367 - INFO - Request Parameters - Page 2:
2025-05-12 21:00:04,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:04,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:05,317 - INFO - Response - Page 2:
2025-05-12 21:00:05,517 - INFO - 第 2 页获取到 100 条记录
2025-05-12 21:00:05,517 - INFO - Request Parameters - Page 3:
2025-05-12 21:00:05,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:05,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:06,009 - INFO - Response - Page 3:
2025-05-12 21:00:06,210 - INFO - 第 3 页获取到 100 条记录
2025-05-12 21:00:06,210 - INFO - Request Parameters - Page 4:
2025-05-12 21:00:06,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:06,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:06,811 - INFO - Response - Page 4:
2025-05-12 21:00:07,011 - INFO - 第 4 页获取到 100 条记录
2025-05-12 21:00:07,011 - INFO - Request Parameters - Page 5:
2025-05-12 21:00:07,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:07,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:07,446 - INFO - Response - Page 5:
2025-05-12 21:00:07,647 - INFO - 第 5 页获取到 100 条记录
2025-05-12 21:00:07,647 - INFO - Request Parameters - Page 6:
2025-05-12 21:00:07,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:07,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:08,175 - INFO - Response - Page 6:
2025-05-12 21:00:08,376 - INFO - 第 6 页获取到 100 条记录
2025-05-12 21:00:08,376 - INFO - Request Parameters - Page 7:
2025-05-12 21:00:08,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:08,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:08,931 - INFO - Response - Page 7:
2025-05-12 21:00:09,131 - INFO - 第 7 页获取到 82 条记录
2025-05-12 21:00:09,131 - INFO - 查询完成，共获取到 682 条记录
2025-05-12 21:00:09,131 - INFO - 获取到 682 条表单数据
2025-05-12 21:00:09,143 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-12 21:00:09,155 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 21:00:09,155 - INFO - 开始处理日期: 2025-02
2025-05-12 21:00:09,155 - INFO - Request Parameters - Page 1:
2025-05-12 21:00:09,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:09,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:09,654 - INFO - Response - Page 1:
2025-05-12 21:00:09,855 - INFO - 第 1 页获取到 100 条记录
2025-05-12 21:00:09,855 - INFO - Request Parameters - Page 2:
2025-05-12 21:00:09,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:09,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:10,346 - INFO - Response - Page 2:
2025-05-12 21:00:10,547 - INFO - 第 2 页获取到 100 条记录
2025-05-12 21:00:10,547 - INFO - Request Parameters - Page 3:
2025-05-12 21:00:10,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:10,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:11,018 - INFO - Response - Page 3:
2025-05-12 21:00:11,219 - INFO - 第 3 页获取到 100 条记录
2025-05-12 21:00:11,219 - INFO - Request Parameters - Page 4:
2025-05-12 21:00:11,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:11,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:11,708 - INFO - Response - Page 4:
2025-05-12 21:00:11,908 - INFO - 第 4 页获取到 100 条记录
2025-05-12 21:00:11,908 - INFO - Request Parameters - Page 5:
2025-05-12 21:00:11,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:11,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:12,359 - INFO - Response - Page 5:
2025-05-12 21:00:12,559 - INFO - 第 5 页获取到 100 条记录
2025-05-12 21:00:12,559 - INFO - Request Parameters - Page 6:
2025-05-12 21:00:12,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:12,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:13,060 - INFO - Response - Page 6:
2025-05-12 21:00:13,260 - INFO - 第 6 页获取到 100 条记录
2025-05-12 21:00:13,260 - INFO - Request Parameters - Page 7:
2025-05-12 21:00:13,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:13,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:13,796 - INFO - Response - Page 7:
2025-05-12 21:00:13,997 - INFO - 第 7 页获取到 70 条记录
2025-05-12 21:00:13,997 - INFO - 查询完成，共获取到 670 条记录
2025-05-12 21:00:13,998 - INFO - 获取到 670 条表单数据
2025-05-12 21:00:14,009 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-12 21:00:14,021 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 21:00:14,021 - INFO - 开始处理日期: 2025-03
2025-05-12 21:00:14,021 - INFO - Request Parameters - Page 1:
2025-05-12 21:00:14,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:14,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:14,499 - INFO - Response - Page 1:
2025-05-12 21:00:14,699 - INFO - 第 1 页获取到 100 条记录
2025-05-12 21:00:14,699 - INFO - Request Parameters - Page 2:
2025-05-12 21:00:14,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:14,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:15,261 - INFO - Response - Page 2:
2025-05-12 21:00:15,461 - INFO - 第 2 页获取到 100 条记录
2025-05-12 21:00:15,461 - INFO - Request Parameters - Page 3:
2025-05-12 21:00:15,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:15,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:15,901 - INFO - Response - Page 3:
2025-05-12 21:00:16,101 - INFO - 第 3 页获取到 100 条记录
2025-05-12 21:00:16,101 - INFO - Request Parameters - Page 4:
2025-05-12 21:00:16,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:16,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:16,649 - INFO - Response - Page 4:
2025-05-12 21:00:16,849 - INFO - 第 4 页获取到 100 条记录
2025-05-12 21:00:16,849 - INFO - Request Parameters - Page 5:
2025-05-12 21:00:16,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:16,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:17,319 - INFO - Response - Page 5:
2025-05-12 21:00:17,519 - INFO - 第 5 页获取到 100 条记录
2025-05-12 21:00:17,519 - INFO - Request Parameters - Page 6:
2025-05-12 21:00:17,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:17,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:17,954 - INFO - Response - Page 6:
2025-05-12 21:00:18,154 - INFO - 第 6 页获取到 100 条记录
2025-05-12 21:00:18,154 - INFO - Request Parameters - Page 7:
2025-05-12 21:00:18,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:18,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:18,597 - INFO - Response - Page 7:
2025-05-12 21:00:18,797 - INFO - 第 7 页获取到 61 条记录
2025-05-12 21:00:18,797 - INFO - 查询完成，共获取到 661 条记录
2025-05-12 21:00:18,798 - INFO - 获取到 661 条表单数据
2025-05-12 21:00:18,815 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-12 21:00:18,832 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 21:00:18,832 - INFO - 开始处理日期: 2025-04
2025-05-12 21:00:18,833 - INFO - Request Parameters - Page 1:
2025-05-12 21:00:18,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:18,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:19,414 - INFO - Response - Page 1:
2025-05-12 21:00:19,614 - INFO - 第 1 页获取到 100 条记录
2025-05-12 21:00:19,614 - INFO - Request Parameters - Page 2:
2025-05-12 21:00:19,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:19,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:20,142 - INFO - Response - Page 2:
2025-05-12 21:00:20,343 - INFO - 第 2 页获取到 100 条记录
2025-05-12 21:00:20,343 - INFO - Request Parameters - Page 3:
2025-05-12 21:00:20,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:20,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:20,793 - INFO - Response - Page 3:
2025-05-12 21:00:20,994 - INFO - 第 3 页获取到 100 条记录
2025-05-12 21:00:20,994 - INFO - Request Parameters - Page 4:
2025-05-12 21:00:20,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:20,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:21,469 - INFO - Response - Page 4:
2025-05-12 21:00:21,669 - INFO - 第 4 页获取到 100 条记录
2025-05-12 21:00:21,669 - INFO - Request Parameters - Page 5:
2025-05-12 21:00:21,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:21,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:22,138 - INFO - Response - Page 5:
2025-05-12 21:00:22,339 - INFO - 第 5 页获取到 100 条记录
2025-05-12 21:00:22,339 - INFO - Request Parameters - Page 6:
2025-05-12 21:00:22,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:22,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:22,892 - INFO - Response - Page 6:
2025-05-12 21:00:23,092 - INFO - 第 6 页获取到 100 条记录
2025-05-12 21:00:23,092 - INFO - Request Parameters - Page 7:
2025-05-12 21:00:23,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:23,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:23,508 - INFO - Response - Page 7:
2025-05-12 21:00:23,709 - INFO - 第 7 页获取到 54 条记录
2025-05-12 21:00:23,709 - INFO - 查询完成，共获取到 654 条记录
2025-05-12 21:00:23,709 - INFO - 获取到 654 条表单数据
2025-05-12 21:00:23,720 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-12 21:00:23,732 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 21:00:23,732 - INFO - 开始处理日期: 2025-05
2025-05-12 21:00:23,732 - INFO - Request Parameters - Page 1:
2025-05-12 21:00:23,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:23,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:24,202 - INFO - Response - Page 1:
2025-05-12 21:00:24,402 - INFO - 第 1 页获取到 100 条记录
2025-05-12 21:00:24,402 - INFO - Request Parameters - Page 2:
2025-05-12 21:00:24,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:24,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:24,856 - INFO - Response - Page 2:
2025-05-12 21:00:25,056 - INFO - 第 2 页获取到 100 条记录
2025-05-12 21:00:25,056 - INFO - Request Parameters - Page 3:
2025-05-12 21:00:25,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:25,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:25,649 - INFO - Response - Page 3:
2025-05-12 21:00:25,849 - INFO - 第 3 页获取到 100 条记录
2025-05-12 21:00:25,849 - INFO - Request Parameters - Page 4:
2025-05-12 21:00:25,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:25,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:26,345 - INFO - Response - Page 4:
2025-05-12 21:00:26,545 - INFO - 第 4 页获取到 100 条记录
2025-05-12 21:00:26,545 - INFO - Request Parameters - Page 5:
2025-05-12 21:00:26,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:26,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:27,075 - INFO - Response - Page 5:
2025-05-12 21:00:27,275 - INFO - 第 5 页获取到 100 条记录
2025-05-12 21:00:27,275 - INFO - Request Parameters - Page 6:
2025-05-12 21:00:27,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:27,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:27,749 - INFO - Response - Page 6:
2025-05-12 21:00:27,950 - INFO - 第 6 页获取到 100 条记录
2025-05-12 21:00:27,950 - INFO - Request Parameters - Page 7:
2025-05-12 21:00:27,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-12 21:00:27,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-12 21:00:28,257 - INFO - Response - Page 7:
2025-05-12 21:00:28,458 - INFO - 第 7 页获取到 24 条记录
2025-05-12 21:00:28,458 - INFO - 查询完成，共获取到 624 条记录
2025-05-12 21:00:28,458 - INFO - 获取到 624 条表单数据
2025-05-12 21:00:28,470 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-12 21:00:28,480 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 21:00:28,480 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-12 21:00:28,482 - INFO - =================同步完成====================
