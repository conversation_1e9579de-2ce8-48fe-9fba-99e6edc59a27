2025-05-25 00:30:34,084 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 00:30:34,084 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 00:30:34,084 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 00:30:34,162 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 0 条记录
2025-05-25 00:30:34,162 - ERROR - 未获取到MySQL数据
2025-05-25 00:31:34,177 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 00:31:34,177 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 00:31:34,177 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 00:31:34,240 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 65 条记录
2025-05-25 00:31:34,240 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 00:31:34,240 - INFO - 开始处理日期: 2025-05-24
2025-05-25 00:31:34,240 - INFO - Request Parameters - Page 1:
2025-05-25 00:31:34,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:31:34,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:31:42,365 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CA9BFF8A-5496-727C-AB84-45C288BBBF09 Response: {'code': 'ServiceUnavailable', 'requestid': 'CA9BFF8A-5496-727C-AB84-45C288BBBF09', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CA9BFF8A-5496-727C-AB84-45C288BBBF09)
2025-05-25 00:31:42,365 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 00:31:42,365 - INFO - 同步完成
2025-05-25 01:30:34,045 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 01:30:34,045 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 01:30:34,045 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 01:30:34,107 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 1 条记录
2025-05-25 01:30:34,107 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 01:30:34,107 - INFO - 开始处理日期: 2025-05-24
2025-05-25 01:30:34,107 - INFO - Request Parameters - Page 1:
2025-05-25 01:30:34,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 01:30:34,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 01:30:42,232 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 77799CB8-41AE-7910-B92B-7D3C525B156C Response: {'code': 'ServiceUnavailable', 'requestid': '77799CB8-41AE-7910-B92B-7D3C525B156C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 77799CB8-41AE-7910-B92B-7D3C525B156C)
2025-05-25 01:30:42,232 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 01:31:42,247 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 01:31:42,247 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 01:31:42,247 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 01:31:42,310 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 67 条记录
2025-05-25 01:31:42,310 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 01:31:42,310 - INFO - 开始处理日期: 2025-05-24
2025-05-25 01:31:42,310 - INFO - Request Parameters - Page 1:
2025-05-25 01:31:42,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 01:31:42,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 01:31:43,107 - INFO - Response - Page 1:
2025-05-25 01:31:43,107 - INFO - 第 1 页获取到 57 条记录
2025-05-25 01:31:43,310 - INFO - 查询完成，共获取到 57 条记录
2025-05-25 01:31:43,310 - INFO - 获取到 57 条表单数据
2025-05-25 01:31:43,310 - INFO - 当前日期 2025-05-24 有 67 条MySQL数据需要处理
2025-05-25 01:31:43,310 - INFO - 开始批量插入 10 条新记录
2025-05-25 01:31:43,451 - INFO - 批量插入响应状态码: 200
2025-05-25 01:31:43,451 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 17:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '492', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B3ABAF2C-BE0F-7790-8467-74804EDAAD6E', 'x-acs-trace-id': '90cc07478df8bb1d28210c1cd2d51d6d', 'etag': '4JZ/b+UTxaz+kLKA2C/lLmw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 01:31:43,451 - INFO - 批量插入响应体: {'result': ['FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMR1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMS1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMT1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMU1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMV1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMW1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMX1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMY1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMZ1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BM02']}
2025-05-25 01:31:43,451 - INFO - 批量插入表单数据成功，批次 1，共 10 条记录
2025-05-25 01:31:43,451 - INFO - 成功插入的数据ID: ['FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMR1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMS1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMT1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMU1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMV1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMW1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMX1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMY1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BMZ1', 'FINST-DO566BD1I3PV426UCXZOQBN9N4HX3PY6BI2BM02']
2025-05-25 01:31:48,466 - INFO - 批量插入完成，共 10 条记录
2025-05-25 01:31:48,466 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 10 条，错误: 0 条
2025-05-25 01:31:48,466 - INFO - 数据同步完成！更新: 0 条，插入: 10 条，错误: 0 条
2025-05-25 01:31:48,466 - INFO - 同步完成
2025-05-25 02:30:33,896 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 02:30:33,896 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 02:30:33,896 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 02:30:33,959 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 1 条记录
2025-05-25 02:30:33,959 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 02:30:33,959 - INFO - 开始处理日期: 2025-05-24
2025-05-25 02:30:33,959 - INFO - Request Parameters - Page 1:
2025-05-25 02:30:33,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 02:30:33,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 02:30:42,068 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C16676A2-B16C-7446-BCBF-7E814DC19F06 Response: {'code': 'ServiceUnavailable', 'requestid': 'C16676A2-B16C-7446-BCBF-7E814DC19F06', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C16676A2-B16C-7446-BCBF-7E814DC19F06)
2025-05-25 02:30:42,068 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 02:31:42,084 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 02:31:42,084 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 02:31:42,084 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 02:31:42,146 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 67 条记录
2025-05-25 02:31:42,146 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 02:31:42,146 - INFO - 开始处理日期: 2025-05-24
2025-05-25 02:31:42,146 - INFO - Request Parameters - Page 1:
2025-05-25 02:31:42,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 02:31:42,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 02:31:50,240 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1975ECFF-E2C9-7804-A9D6-BD4B4435BF2B Response: {'code': 'ServiceUnavailable', 'requestid': '1975ECFF-E2C9-7804-A9D6-BD4B4435BF2B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1975ECFF-E2C9-7804-A9D6-BD4B4435BF2B)
2025-05-25 02:31:50,240 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 02:31:50,240 - INFO - 同步完成
2025-05-25 03:30:33,779 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 03:30:33,779 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 03:30:33,779 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 03:30:33,842 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 1 条记录
2025-05-25 03:30:33,842 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 03:30:33,842 - INFO - 开始处理日期: 2025-05-24
2025-05-25 03:30:33,842 - INFO - Request Parameters - Page 1:
2025-05-25 03:30:33,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:30:33,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:30:41,951 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3E98EAEE-EF7F-7123-AC27-4A6A66022DDA Response: {'code': 'ServiceUnavailable', 'requestid': '3E98EAEE-EF7F-7123-AC27-4A6A66022DDA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3E98EAEE-EF7F-7123-AC27-4A6A66022DDA)
2025-05-25 03:30:41,967 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 03:31:41,982 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 03:31:41,982 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 03:31:41,982 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 03:31:42,044 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 67 条记录
2025-05-25 03:31:42,044 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 03:31:42,044 - INFO - 开始处理日期: 2025-05-24
2025-05-25 03:31:42,044 - INFO - Request Parameters - Page 1:
2025-05-25 03:31:42,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:31:42,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:31:42,857 - INFO - Response - Page 1:
2025-05-25 03:31:42,857 - INFO - 第 1 页获取到 67 条记录
2025-05-25 03:31:43,060 - INFO - 查询完成，共获取到 67 条记录
2025-05-25 03:31:43,060 - INFO - 获取到 67 条表单数据
2025-05-25 03:31:43,060 - INFO - 当前日期 2025-05-24 有 67 条MySQL数据需要处理
2025-05-25 03:31:43,060 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 03:31:43,060 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 03:31:43,060 - INFO - 同步完成
2025-05-25 04:30:33,865 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 04:30:33,865 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 04:30:33,865 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 04:30:33,928 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 1 条记录
2025-05-25 04:30:33,928 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 04:30:33,928 - INFO - 开始处理日期: 2025-05-24
2025-05-25 04:30:33,943 - INFO - Request Parameters - Page 1:
2025-05-25 04:30:33,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 04:30:33,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 04:30:42,052 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1F8EDB40-4410-7197-A30A-72637B8B92D5 Response: {'code': 'ServiceUnavailable', 'requestid': '1F8EDB40-4410-7197-A30A-72637B8B92D5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1F8EDB40-4410-7197-A30A-72637B8B92D5)
2025-05-25 04:30:42,052 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 04:31:42,068 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 04:31:42,068 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 04:31:42,068 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 04:31:42,130 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 67 条记录
2025-05-25 04:31:42,130 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 04:31:42,130 - INFO - 开始处理日期: 2025-05-24
2025-05-25 04:31:42,130 - INFO - Request Parameters - Page 1:
2025-05-25 04:31:42,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 04:31:42,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 04:31:50,240 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DD76B9E6-776E-7E74-B0BB-002629793CF5 Response: {'code': 'ServiceUnavailable', 'requestid': 'DD76B9E6-776E-7E74-B0BB-002629793CF5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DD76B9E6-776E-7E74-B0BB-002629793CF5)
2025-05-25 04:31:50,240 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 04:31:50,240 - INFO - 同步完成
2025-05-25 05:30:33,748 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 05:30:33,748 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 05:30:33,748 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 05:30:33,810 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 2 条记录
2025-05-25 05:30:33,810 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 05:30:33,810 - INFO - 开始处理日期: 2025-05-24
2025-05-25 05:30:33,810 - INFO - Request Parameters - Page 1:
2025-05-25 05:30:33,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 05:30:33,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 05:30:41,919 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AE9947C0-8DEE-738B-A288-7AE112CDAF4E Response: {'code': 'ServiceUnavailable', 'requestid': 'AE9947C0-8DEE-738B-A288-7AE112CDAF4E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AE9947C0-8DEE-738B-A288-7AE112CDAF4E)
2025-05-25 05:30:41,919 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 05:31:41,935 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 05:31:41,935 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 05:31:41,935 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 05:31:41,997 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 68 条记录
2025-05-25 05:31:41,997 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 05:31:41,997 - INFO - 开始处理日期: 2025-05-24
2025-05-25 05:31:41,997 - INFO - Request Parameters - Page 1:
2025-05-25 05:31:41,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 05:31:41,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 05:31:50,091 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6AF99E50-4AB9-7E7F-8F15-FA92F4A12FB5 Response: {'code': 'ServiceUnavailable', 'requestid': '6AF99E50-4AB9-7E7F-8F15-FA92F4A12FB5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6AF99E50-4AB9-7E7F-8F15-FA92F4A12FB5)
2025-05-25 05:31:50,091 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 05:31:50,091 - INFO - 同步完成
2025-05-25 06:30:33,911 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 06:30:33,911 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 06:30:33,911 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 06:30:33,974 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 2 条记录
2025-05-25 06:30:33,974 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 06:30:33,974 - INFO - 开始处理日期: 2025-05-24
2025-05-25 06:30:33,974 - INFO - Request Parameters - Page 1:
2025-05-25 06:30:33,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:30:33,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:30:42,115 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D3B50748-2EA6-7B97-B5CD-4C39E90E5221 Response: {'code': 'ServiceUnavailable', 'requestid': 'D3B50748-2EA6-7B97-B5CD-4C39E90E5221', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D3B50748-2EA6-7B97-B5CD-4C39E90E5221)
2025-05-25 06:30:42,115 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 06:31:42,130 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 06:31:42,130 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 06:31:42,130 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 06:31:42,192 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 68 条记录
2025-05-25 06:31:42,192 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 06:31:42,192 - INFO - 开始处理日期: 2025-05-24
2025-05-25 06:31:42,192 - INFO - Request Parameters - Page 1:
2025-05-25 06:31:42,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:31:42,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:31:50,302 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 51C06ED4-3DAD-72B7-9619-5B61963336AE Response: {'code': 'ServiceUnavailable', 'requestid': '51C06ED4-3DAD-72B7-9619-5B61963336AE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 51C06ED4-3DAD-72B7-9619-5B61963336AE)
2025-05-25 06:31:50,302 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 06:31:50,302 - INFO - 同步完成
2025-05-25 07:30:33,778 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 07:30:33,778 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 07:30:33,778 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 07:30:33,841 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 2 条记录
2025-05-25 07:30:33,841 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 07:30:33,841 - INFO - 开始处理日期: 2025-05-24
2025-05-25 07:30:33,841 - INFO - Request Parameters - Page 1:
2025-05-25 07:30:33,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 07:30:33,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 07:30:41,950 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7927532A-4D74-724D-A307-A1E6FAAE27A9 Response: {'code': 'ServiceUnavailable', 'requestid': '7927532A-4D74-724D-A307-A1E6FAAE27A9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7927532A-4D74-724D-A307-A1E6FAAE27A9)
2025-05-25 07:30:41,950 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 07:31:41,965 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 07:31:41,965 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 07:31:41,965 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 07:31:42,028 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 68 条记录
2025-05-25 07:31:42,028 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 07:31:42,028 - INFO - 开始处理日期: 2025-05-24
2025-05-25 07:31:42,028 - INFO - Request Parameters - Page 1:
2025-05-25 07:31:42,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 07:31:42,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 07:31:42,887 - INFO - Response - Page 1:
2025-05-25 07:31:42,887 - INFO - 第 1 页获取到 67 条记录
2025-05-25 07:31:43,090 - INFO - 查询完成，共获取到 67 条记录
2025-05-25 07:31:43,090 - INFO - 获取到 67 条表单数据
2025-05-25 07:31:43,090 - INFO - 当前日期 2025-05-24 有 68 条MySQL数据需要处理
2025-05-25 07:31:43,090 - INFO - 开始批量插入 1 条新记录
2025-05-25 07:31:43,231 - INFO - 批量插入响应状态码: 200
2025-05-25 07:31:43,231 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 23:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-8371-7624-89D4-93351EA4FF3A', 'x-acs-trace-id': '13458b911587673f76c2f72d9dda22e8', 'etag': '6eOSWhGPdIR9iD3RZP1Ud6A0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 07:31:43,231 - INFO - 批量插入响应体: {'result': ['FINST-WS666IB15NPVXZFQ7UTQCA6S2FXK3EK56V2BM34']}
2025-05-25 07:31:43,231 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-25 07:31:43,231 - INFO - 成功插入的数据ID: ['FINST-WS666IB15NPVXZFQ7UTQCA6S2FXK3EK56V2BM34']
2025-05-25 07:31:48,247 - INFO - 批量插入完成，共 1 条记录
2025-05-25 07:31:48,247 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-25 07:31:48,247 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-25 07:31:48,247 - INFO - 同步完成
2025-05-25 08:30:33,739 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 08:30:33,739 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 08:30:33,739 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 08:30:33,802 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 8 条记录
2025-05-25 08:30:33,802 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 08:30:33,802 - INFO - 开始处理日期: 2025-05-24
2025-05-25 08:30:33,817 - INFO - Request Parameters - Page 1:
2025-05-25 08:30:33,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:30:33,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:30:41,927 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F8F8E4F7-71F6-7944-AAC7-1B8D7B8F364B Response: {'code': 'ServiceUnavailable', 'requestid': 'F8F8E4F7-71F6-7944-AAC7-1B8D7B8F364B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F8F8E4F7-71F6-7944-AAC7-1B8D7B8F364B)
2025-05-25 08:30:41,942 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 08:31:41,958 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 08:31:41,958 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 08:31:41,958 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 08:31:42,020 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 123 条记录
2025-05-25 08:31:42,020 - INFO - 获取到 1 个日期需要处理: ['2025-05-24']
2025-05-25 08:31:42,020 - INFO - 开始处理日期: 2025-05-24
2025-05-25 08:31:42,020 - INFO - Request Parameters - Page 1:
2025-05-25 08:31:42,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:31:42,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:31:50,129 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 145628BA-445E-7AD7-9352-28C28C84D19B Response: {'code': 'ServiceUnavailable', 'requestid': '145628BA-445E-7AD7-9352-28C28C84D19B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 145628BA-445E-7AD7-9352-28C28C84D19B)
2025-05-25 08:31:50,129 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 08:31:50,129 - INFO - 同步完成
2025-05-25 09:30:34,200 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 09:30:34,200 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 09:30:34,200 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 09:30:34,263 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 50 条记录
2025-05-25 09:30:34,263 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 09:30:34,263 - INFO - 开始处理日期: 2025-05-24
2025-05-25 09:30:34,263 - INFO - Request Parameters - Page 1:
2025-05-25 09:30:34,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:30:34,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:30:42,388 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 07740372-3DE4-710A-BE17-077C509A2471 Response: {'code': 'ServiceUnavailable', 'requestid': '07740372-3DE4-710A-BE17-077C509A2471', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 07740372-3DE4-710A-BE17-077C509A2471)
2025-05-25 09:30:42,388 - INFO - 开始处理日期: 2025-05-25
2025-05-25 09:30:42,388 - INFO - Request Parameters - Page 1:
2025-05-25 09:30:42,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:30:42,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:30:50,200 - INFO - Response - Page 1:
2025-05-25 09:30:50,200 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 09:30:50,200 - INFO - 获取到 0 条表单数据
2025-05-25 09:30:50,200 - INFO - 当前日期 2025-05-25 有 1 条MySQL数据需要处理
2025-05-25 09:30:50,200 - INFO - 开始批量插入 1 条新记录
2025-05-25 09:30:50,356 - INFO - 批量插入响应状态码: 200
2025-05-25 09:30:50,356 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 01:30:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4A943ECB-B0F0-715A-B665-49D3DF82869F', 'x-acs-trace-id': 'ff703e9ad52f526fd9b3bcf18e7623c8', 'etag': '6KoxPoC5xWyWqdKO0HUKGMQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 09:30:50,356 - INFO - 批量插入响应体: {'result': ['FINST-N79668C1L4NVKFII9TJHD50Z3LAP2BDCFZ2BM63']}
2025-05-25 09:30:50,356 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-25 09:30:50,356 - INFO - 成功插入的数据ID: ['FINST-N79668C1L4NVKFII9TJHD50Z3LAP2BDCFZ2BM63']
2025-05-25 09:30:55,372 - INFO - 批量插入完成，共 1 条记录
2025-05-25 09:30:55,372 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-25 09:30:55,372 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-05-25 09:31:55,387 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 09:31:55,387 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 09:31:55,387 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 09:31:55,450 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 223 条记录
2025-05-25 09:31:55,450 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 09:31:55,450 - INFO - 开始处理日期: 2025-05-24
2025-05-25 09:31:55,450 - INFO - Request Parameters - Page 1:
2025-05-25 09:31:55,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:31:55,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:31:56,325 - INFO - Response - Page 1:
2025-05-25 09:31:56,325 - INFO - 第 1 页获取到 68 条记录
2025-05-25 09:31:56,528 - INFO - 查询完成，共获取到 68 条记录
2025-05-25 09:31:56,528 - INFO - 获取到 68 条表单数据
2025-05-25 09:31:56,528 - INFO - 当前日期 2025-05-24 有 222 条MySQL数据需要处理
2025-05-25 09:31:56,528 - INFO - 开始更新记录 - 表单实例ID: FINST-L5766E71TKOVXXOSDJN8J78V0WNC3YMTGQ1BM73
2025-05-25 09:31:57,075 - INFO - 更新表单数据成功: FINST-L5766E71TKOVXXOSDJN8J78V0WNC3YMTGQ1BM73
2025-05-25 09:31:57,075 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1635.08, 'new_value': 1916.35}, {'field': 'offline_amount', 'old_value': 8233.09, 'new_value': 20893.79}, {'field': 'total_amount', 'old_value': 9868.17, 'new_value': 22810.14}, {'field': 'order_count', 'old_value': 208, 'new_value': 560}]
2025-05-25 09:31:57,075 - INFO - 开始更新记录 - 表单实例ID: FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM2B
2025-05-25 09:31:57,528 - INFO - 更新表单数据成功: FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM2B
2025-05-25 09:31:57,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14907.6, 'new_value': 15697.5}, {'field': 'total_amount', 'old_value': 14907.6, 'new_value': 15697.5}, {'field': 'order_count', 'old_value': 78, 'new_value': 79}]
2025-05-25 09:31:57,528 - INFO - 开始批量插入 154 条新记录
2025-05-25 09:31:57,840 - INFO - 批量插入响应状态码: 200
2025-05-25 09:31:57,840 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 01:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5AB977B2-FC99-78D9-8F16-2868E0C8D0CE', 'x-acs-trace-id': 'c92c880735f6fbe9995df70e67aa3a01', 'etag': '4yp/nd4O3pdX6Ppb73Ee8Cw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 09:31:57,840 - INFO - 批量插入响应体: {'result': ['FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM16', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM26', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM36', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM46', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM56', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM66', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM76', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM86', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM96', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMA6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMB6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMC6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMD6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BME6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMF6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMG6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMH6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMI6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMJ6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMK6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BML6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMM6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMN6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMO6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMP6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMQ6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMR6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMS6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMT6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMU6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMV6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMW6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMX6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMY6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMZ6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM07', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM17', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM27', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM37', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM47', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM57', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM67', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM77', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM87', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM97', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMA7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMB7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMC7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMD7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BME7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMF7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMG7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMH7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMI7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMJ7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMK7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BML7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMM7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMN7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMO7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMP7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMQ7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMR7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMS7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMT7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMU7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMV7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMW7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMX7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMY7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMZ7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM08', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM18', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM28', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM38', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM48', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM58', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM68', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM78', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM88', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM98', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMA8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMB8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMC8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMD8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BME8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMF8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMG8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMH8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMI8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMJ8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMK8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BML8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMM8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMN8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMO8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMP8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMQ8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMR8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMS8']}
2025-05-25 09:31:57,840 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-25 09:31:57,840 - INFO - 成功插入的数据ID: ['FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM16', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM26', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM36', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM46', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM56', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM66', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM76', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32JFSGZ2BM86', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM96', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMA6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMB6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMC6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMD6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BME6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMF6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMG6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMH6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMI6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMJ6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMK6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BML6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMM6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMN6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMO6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMP6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMQ6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMR6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMS6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMT6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMU6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMV6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMW6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMX6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMY6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMZ6', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM07', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM17', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM27', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM37', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM47', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM57', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM67', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM77', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM87', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM97', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMA7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMB7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMC7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMD7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BME7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMF7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMG7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMH7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMI7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMJ7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMK7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BML7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMM7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMN7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMO7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMP7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMQ7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMR7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMS7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMT7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMU7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMV7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMW7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMX7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMY7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMZ7', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM08', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM18', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM28', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM38', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM48', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM58', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM68', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM78', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM88', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BM98', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMA8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMB8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMC8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMD8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BME8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMF8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMG8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMH8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMI8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMJ8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMK8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BML8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMM8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMN8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMO8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMP8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMQ8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMR8', 'FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMS8']
2025-05-25 09:32:03,075 - INFO - 批量插入响应状态码: 200
2025-05-25 09:32:03,075 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 01:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2604', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E6A0C45F-340C-7CDD-A4A6-A404A0F1E82D', 'x-acs-trace-id': '55e24c1353a0f9c43432c0238caa92a4', 'etag': '24MpgS/GaNXyDh9Awfl2Ldw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 09:32:03,075 - INFO - 批量插入响应体: {'result': ['FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMXE', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMYE', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMZE', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM0F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM1F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM2F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM3F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM4F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM5F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM6F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM7F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM8F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM9F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMAF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMBF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMCF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMDF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMEF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMFF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMGF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMHF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMIF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMJF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMKF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMLF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMMF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMNF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMOF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMPF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMQF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMRF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMSF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMTF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMUF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMVF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMWF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMXF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMYF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMZF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM0G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM1G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM2G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM3G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM4G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM5G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM6G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM7G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM8G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM9G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMAG', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMBG', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMCG', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMDG', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMEG']}
2025-05-25 09:32:03,075 - INFO - 批量插入表单数据成功，批次 2，共 54 条记录
2025-05-25 09:32:03,075 - INFO - 成功插入的数据ID: ['FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMXE', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMYE', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMZE', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM0F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM1F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM2F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM3F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM4F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM5F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM6F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM7F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM8F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BM9F', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMAF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMBF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMCF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMDF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMEF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMFF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMGF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMHF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMIF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMJF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMKF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMLF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMMF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMNF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMOF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMPF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMQF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMRF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMSF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMTF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMUF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMVF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMWF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMXF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMYF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMZF', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM0G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM1G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM2G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM3G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM4G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM5G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM6G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM7G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM8G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM9G', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMAG', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMBG', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMCG', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMDG', 'FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMEG']
2025-05-25 09:32:08,090 - INFO - 批量插入完成，共 154 条记录
2025-05-25 09:32:08,090 - INFO - 日期 2025-05-24 处理完成 - 更新: 2 条，插入: 154 条，错误: 0 条
2025-05-25 09:32:08,090 - INFO - 开始处理日期: 2025-05-25
2025-05-25 09:32:08,090 - INFO - Request Parameters - Page 1:
2025-05-25 09:32:08,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:32:08,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:32:08,590 - INFO - Response - Page 1:
2025-05-25 09:32:08,606 - INFO - 第 1 页获取到 1 条记录
2025-05-25 09:32:08,809 - INFO - 查询完成，共获取到 1 条记录
2025-05-25 09:32:08,809 - INFO - 获取到 1 条表单数据
2025-05-25 09:32:08,809 - INFO - 当前日期 2025-05-25 有 1 条MySQL数据需要处理
2025-05-25 09:32:08,809 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 09:32:08,809 - INFO - 数据同步完成！更新: 2 条，插入: 154 条，错误: 0 条
2025-05-25 09:32:08,809 - INFO - 同步完成
2025-05-25 10:30:33,755 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 10:30:33,755 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 10:30:33,755 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 10:30:33,833 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 107 条记录
2025-05-25 10:30:33,833 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 10:30:33,833 - INFO - 开始处理日期: 2025-05-24
2025-05-25 10:30:33,833 - INFO - Request Parameters - Page 1:
2025-05-25 10:30:33,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 10:30:33,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 10:30:41,958 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E268BD1E-D35B-727D-8B6F-53F9E221726A Response: {'code': 'ServiceUnavailable', 'requestid': 'E268BD1E-D35B-727D-8B6F-53F9E221726A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E268BD1E-D35B-727D-8B6F-53F9E221726A)
2025-05-25 10:30:41,958 - INFO - 开始处理日期: 2025-05-25
2025-05-25 10:30:41,958 - INFO - Request Parameters - Page 1:
2025-05-25 10:30:41,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 10:30:41,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 10:30:49,770 - INFO - Response - Page 1:
2025-05-25 10:30:49,770 - INFO - 第 1 页获取到 1 条记录
2025-05-25 10:30:49,974 - INFO - 查询完成，共获取到 1 条记录
2025-05-25 10:30:49,974 - INFO - 获取到 1 条表单数据
2025-05-25 10:30:49,974 - INFO - 当前日期 2025-05-25 有 1 条MySQL数据需要处理
2025-05-25 10:30:49,974 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 10:30:49,974 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 10:31:49,989 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 10:31:49,989 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 10:31:49,989 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 10:31:50,051 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 324 条记录
2025-05-25 10:31:50,051 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 10:31:50,051 - INFO - 开始处理日期: 2025-05-24
2025-05-25 10:31:50,051 - INFO - Request Parameters - Page 1:
2025-05-25 10:31:50,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 10:31:50,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 10:31:50,911 - INFO - Response - Page 1:
2025-05-25 10:31:50,911 - INFO - 第 1 页获取到 100 条记录
2025-05-25 10:31:51,114 - INFO - Request Parameters - Page 2:
2025-05-25 10:31:51,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 10:31:51,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 10:31:51,958 - INFO - Response - Page 2:
2025-05-25 10:31:51,958 - INFO - 第 2 页获取到 100 条记录
2025-05-25 10:31:52,161 - INFO - Request Parameters - Page 3:
2025-05-25 10:31:52,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 10:31:52,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 10:31:52,786 - INFO - Response - Page 3:
2025-05-25 10:31:52,786 - INFO - 第 3 页获取到 22 条记录
2025-05-25 10:31:52,989 - INFO - 查询完成，共获取到 222 条记录
2025-05-25 10:31:52,989 - INFO - 获取到 222 条表单数据
2025-05-25 10:31:52,989 - INFO - 当前日期 2025-05-24 有 323 条MySQL数据需要处理
2025-05-25 10:31:52,989 - INFO - 开始批量插入 101 条新记录
2025-05-25 10:31:53,301 - INFO - 批量插入响应状态码: 200
2025-05-25 10:31:53,301 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 02:31:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E78E7966-2C4D-7B52-B15B-2B2070A9E599', 'x-acs-trace-id': '1ae6b6b48a9a203ba9636a2023e0b669', 'etag': '4zLi0hOlJ73pxtNpSwD7e+A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 10:31:53,301 - INFO - 批量插入响应体: {'result': ['FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMXG', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMYG', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMZG', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM0H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM1H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM2H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM3H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM4H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM5H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM6H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM7H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM8H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM9H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMAH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMBH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMCH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMDH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMEH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMFH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMGH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMHH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMIH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMJH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMKH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMLH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMMH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMNH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMOH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMPH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMQH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMRH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMSH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMTH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMUH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMVH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMWH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMXH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMYH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMZH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM0I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM1I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM2I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM3I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM4I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM5I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM6I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM7I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM8I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM9I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMAI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMBI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMCI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMDI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMEI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMFI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMGI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMHI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMII', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMJI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMKI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMLI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMMI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMNI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMOI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMPI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMQI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMRI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMSI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMTI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMUI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMVI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMWI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMXI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMYI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMZI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM0J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM1J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM2J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM3J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM4J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM5J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM6J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM7J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM8J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM9J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMAJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMBJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMCJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMDJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMEJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMFJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMGJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMHJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMIJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMJJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMKJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMLJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMMJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMNJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMOJ']}
2025-05-25 10:31:53,301 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-25 10:31:53,301 - INFO - 成功插入的数据ID: ['FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMXG', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMYG', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMZG', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM0H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM1H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM2H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM3H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM4H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM5H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM6H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM7H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM8H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM9H', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMAH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMBH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMCH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMDH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMEH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMFH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMGH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMHH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMIH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMJH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMKH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMLH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMMH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMNH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMOH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMPH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMQH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMRH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMSH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMTH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMUH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMVH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMWH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMXH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMYH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMZH', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM0I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM1I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM2I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM3I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM4I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM5I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM6I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM7I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM8I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM9I', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMAI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMBI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMCI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMDI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMEI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMFI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMGI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMHI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMII', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMJI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMKI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMLI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMMI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMNI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMOI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMPI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMQI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMRI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMSI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMTI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMUI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMVI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMWI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMXI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMYI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMZI', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM0J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM1J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM2J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM3J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM4J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM5J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM6J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM7J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM8J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BM9J', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMAJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMBJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMCJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMDJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMEJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMFJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMGJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMHJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMIJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMJJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMKJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMLJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMMJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMNJ', 'FINST-SI766181B5NVTP5FED1TRACMT20Q3QPUL13BMOJ']
2025-05-25 10:31:58,473 - INFO - 批量插入响应状态码: 200
2025-05-25 10:31:58,473 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 02:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '115E77BA-203D-7762-BBE7-0ED4CB6C82F4', 'x-acs-trace-id': '33fcef1672a049058dcf4f37b81d3f1a', 'etag': '6GFFxPFhVtdm4XD1VmFmubQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 10:31:58,473 - INFO - 批量插入响应体: {'result': ['FINST-NWE664C16ALVCXOKF2XX9C73JTYG3SPYL13BMVH']}
2025-05-25 10:31:58,473 - INFO - 批量插入表单数据成功，批次 2，共 1 条记录
2025-05-25 10:31:58,473 - INFO - 成功插入的数据ID: ['FINST-NWE664C16ALVCXOKF2XX9C73JTYG3SPYL13BMVH']
2025-05-25 10:32:03,489 - INFO - 批量插入完成，共 101 条记录
2025-05-25 10:32:03,489 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 101 条，错误: 0 条
2025-05-25 10:32:03,489 - INFO - 开始处理日期: 2025-05-25
2025-05-25 10:32:03,489 - INFO - Request Parameters - Page 1:
2025-05-25 10:32:03,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 10:32:03,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 10:32:03,973 - INFO - Response - Page 1:
2025-05-25 10:32:03,973 - INFO - 第 1 页获取到 1 条记录
2025-05-25 10:32:04,176 - INFO - 查询完成，共获取到 1 条记录
2025-05-25 10:32:04,176 - INFO - 获取到 1 条表单数据
2025-05-25 10:32:04,176 - INFO - 当前日期 2025-05-25 有 1 条MySQL数据需要处理
2025-05-25 10:32:04,176 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 10:32:04,176 - INFO - 数据同步完成！更新: 0 条，插入: 101 条，错误: 0 条
2025-05-25 10:32:04,176 - INFO - 同步完成
2025-05-25 11:30:33,731 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 11:30:33,731 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 11:30:33,731 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 11:30:33,794 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 133 条记录
2025-05-25 11:30:33,794 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 11:30:33,794 - INFO - 开始处理日期: 2025-05-24
2025-05-25 11:30:33,809 - INFO - Request Parameters - Page 1:
2025-05-25 11:30:33,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 11:30:33,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 11:30:41,919 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6217047F-3CA2-7E8F-B274-52BB784D696F Response: {'code': 'ServiceUnavailable', 'requestid': '6217047F-3CA2-7E8F-B274-52BB784D696F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6217047F-3CA2-7E8F-B274-52BB784D696F)
2025-05-25 11:30:41,919 - INFO - 开始处理日期: 2025-05-25
2025-05-25 11:30:41,919 - INFO - Request Parameters - Page 1:
2025-05-25 11:30:41,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 11:30:41,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 11:30:50,028 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 920A1348-1257-7B9F-8E08-9CC6D5F2EE2E Response: {'code': 'ServiceUnavailable', 'requestid': '920A1348-1257-7B9F-8E08-9CC6D5F2EE2E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 920A1348-1257-7B9F-8E08-9CC6D5F2EE2E)
2025-05-25 11:30:50,028 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 11:31:50,043 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 11:31:50,043 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 11:31:50,043 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 11:31:50,121 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 462 条记录
2025-05-25 11:31:50,121 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 11:31:50,121 - INFO - 开始处理日期: 2025-05-24
2025-05-25 11:31:50,121 - INFO - Request Parameters - Page 1:
2025-05-25 11:31:50,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 11:31:50,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 11:31:51,122 - INFO - Response - Page 1:
2025-05-25 11:31:51,122 - INFO - 第 1 页获取到 100 条记录
2025-05-25 11:31:51,325 - INFO - Request Parameters - Page 2:
2025-05-25 11:31:51,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 11:31:51,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 11:31:52,106 - INFO - Response - Page 2:
2025-05-25 11:31:52,106 - INFO - 第 2 页获取到 100 条记录
2025-05-25 11:31:52,309 - INFO - Request Parameters - Page 3:
2025-05-25 11:31:52,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 11:31:52,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 11:31:53,184 - INFO - Response - Page 3:
2025-05-25 11:31:53,184 - INFO - 第 3 页获取到 100 条记录
2025-05-25 11:31:53,387 - INFO - Request Parameters - Page 4:
2025-05-25 11:31:53,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 11:31:53,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 11:31:54,012 - INFO - Response - Page 4:
2025-05-25 11:31:54,012 - INFO - 第 4 页获取到 23 条记录
2025-05-25 11:31:54,215 - INFO - 查询完成，共获取到 323 条记录
2025-05-25 11:31:54,215 - INFO - 获取到 323 条表单数据
2025-05-25 11:31:54,215 - INFO - 当前日期 2025-05-24 有 461 条MySQL数据需要处理
2025-05-25 11:31:54,215 - INFO - 开始批量插入 138 条新记录
2025-05-25 11:31:54,512 - INFO - 批量插入响应状态码: 200
2025-05-25 11:31:54,512 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 03:31:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F4D7F2B1-157B-7C18-A029-86EBBF0DE2C4', 'x-acs-trace-id': '59ef490fc84703a2821012d8b71a1af2', 'etag': '4GiFRonsmbheoLvbiDaacAA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 11:31:54,512 - INFO - 批量插入响应体: {'result': ['FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMZM', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM0N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM1N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM2N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM3N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM4N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM5N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM6N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM7N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM8N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM9N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMAN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMBN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMCN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMDN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMEN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMFN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMGN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMHN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMIN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMJN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMKN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMLN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMMN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMNN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMON', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMPN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMQN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMRN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMSN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMTN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMUN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMVN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMWN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMXN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMYN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMZN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM0O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM1O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM2O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM3O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM4O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM5O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM6O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM7O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM8O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM9O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMAO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMBO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMCO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMDO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMEO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMFO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMGO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMHO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMIO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMJO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMKO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMLO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMMO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMNO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMOO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMPO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMQO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMRO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMSO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMTO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMUO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMVO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMWO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMXO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMYO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMZO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM0P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM1P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM2P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM3P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM4P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM5P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM6P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM7P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM8P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM9P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMAP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMBP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMCP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMDP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMEP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMFP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMGP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMHP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMIP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMJP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMKP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMLP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMMP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMNP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMOP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMPP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMQP']}
2025-05-25 11:31:54,512 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-25 11:31:54,512 - INFO - 成功插入的数据ID: ['FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMZM', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM0N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM1N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM2N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM3N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM4N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM5N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM6N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM7N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM8N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM9N', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMAN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMBN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMCN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMDN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMEN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMFN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMGN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMHN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMIN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMJN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMKN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMLN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMMN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMNN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMON', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMPN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMQN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMRN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMSN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMTN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMUN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMVN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMWN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMXN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMYN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMZN', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM0O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM1O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM2O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM3O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM4O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM5O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM6O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM7O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM8O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM9O', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMAO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMBO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMCO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMDO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMEO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMFO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMGO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMHO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMIO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMJO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMKO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMLO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMMO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMNO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMOO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMPO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMQO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMRO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMSO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMTO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMUO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMVO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMWO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMXO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMYO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMZO', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM0P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM1P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM2P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM3P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM4P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM5P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM6P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM7P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM8P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BM9P', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMAP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMBP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMCP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMDP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMEP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMFP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMGP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMHP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMIP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMJP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMKP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMLP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMMP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMNP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMOP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMPP', 'FINST-MKF66PA17AMVUT7W93DKR6QDYZSQ3BG1R33BMQP']
2025-05-25 11:31:59,715 - INFO - 批量插入响应状态码: 200
2025-05-25 11:31:59,715 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 03:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1836', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '061AF990-6A47-7B9F-9E43-9E73238BDB63', 'x-acs-trace-id': 'e4cdac44330ca9ea5f61b1afad61c77a', 'etag': '1kDaRKxhmBEzhCqdcIpFdpA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 11:31:59,715 - INFO - 批量插入响应体: {'result': ['FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMVA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMWA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMXA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMYA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMZA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM0B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM1B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM2B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM3B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM4B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM5B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM6B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM7B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM8B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM9B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMAB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMBB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMCB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMDB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMEB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMFB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMGB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMHB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMIB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMJB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMKB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMLB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMMB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMNB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMOB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMPB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMQB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMRB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMSB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMTB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMUB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMVB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMWB']}
2025-05-25 11:31:59,715 - INFO - 批量插入表单数据成功，批次 2，共 38 条记录
2025-05-25 11:31:59,715 - INFO - 成功插入的数据ID: ['FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMVA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMWA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMXA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMYA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMZA', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM0B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM1B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM2B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM3B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM4B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM5B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM6B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM7B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM8B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BM9B', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMAB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMBB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMCB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMDB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMEB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMFB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMGB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMHB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMIB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMJB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMKB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMLB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMMB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMNB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMOB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMPB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMQB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMRB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMSB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMTB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMUB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMVB', 'FINST-1PF66VA1J2PVV6XEFUG4V40V9FE03UG5R33BMWB']
2025-05-25 11:32:04,731 - INFO - 批量插入完成，共 138 条记录
2025-05-25 11:32:04,731 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 138 条，错误: 0 条
2025-05-25 11:32:04,731 - INFO - 开始处理日期: 2025-05-25
2025-05-25 11:32:04,731 - INFO - Request Parameters - Page 1:
2025-05-25 11:32:04,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 11:32:04,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 11:32:05,293 - INFO - Response - Page 1:
2025-05-25 11:32:05,293 - INFO - 第 1 页获取到 1 条记录
2025-05-25 11:32:05,496 - INFO - 查询完成，共获取到 1 条记录
2025-05-25 11:32:05,496 - INFO - 获取到 1 条表单数据
2025-05-25 11:32:05,496 - INFO - 当前日期 2025-05-25 有 1 条MySQL数据需要处理
2025-05-25 11:32:05,496 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 11:32:05,496 - INFO - 数据同步完成！更新: 0 条，插入: 138 条，错误: 0 条
2025-05-25 11:32:05,496 - INFO - 同步完成
2025-05-25 12:30:33,739 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 12:30:33,739 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 12:30:33,739 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 12:30:33,817 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 149 条记录
2025-05-25 12:30:33,817 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 12:30:33,817 - INFO - 开始处理日期: 2025-05-24
2025-05-25 12:30:33,817 - INFO - Request Parameters - Page 1:
2025-05-25 12:30:33,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:30:33,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:30:41,927 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 66F7E97A-52D1-7A94-A584-368ACB1D2A9D Response: {'code': 'ServiceUnavailable', 'requestid': '66F7E97A-52D1-7A94-A584-368ACB1D2A9D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 66F7E97A-52D1-7A94-A584-368ACB1D2A9D)
2025-05-25 12:30:41,927 - INFO - 开始处理日期: 2025-05-25
2025-05-25 12:30:41,927 - INFO - Request Parameters - Page 1:
2025-05-25 12:30:41,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:30:41,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:30:50,051 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ADCA15B1-7952-73F0-B001-1683379E79DC Response: {'code': 'ServiceUnavailable', 'requestid': 'ADCA15B1-7952-73F0-B001-1683379E79DC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ADCA15B1-7952-73F0-B001-1683379E79DC)
2025-05-25 12:30:50,051 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 12:31:50,067 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 12:31:50,067 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 12:31:50,067 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 12:31:50,145 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 482 条记录
2025-05-25 12:31:50,145 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 12:31:50,145 - INFO - 开始处理日期: 2025-05-24
2025-05-25 12:31:50,145 - INFO - Request Parameters - Page 1:
2025-05-25 12:31:50,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:31:50,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:31:50,973 - INFO - Response - Page 1:
2025-05-25 12:31:50,973 - INFO - 第 1 页获取到 100 条记录
2025-05-25 12:31:51,176 - INFO - Request Parameters - Page 2:
2025-05-25 12:31:51,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:31:51,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:31:51,910 - INFO - Response - Page 2:
2025-05-25 12:31:51,910 - INFO - 第 2 页获取到 100 条记录
2025-05-25 12:31:52,114 - INFO - Request Parameters - Page 3:
2025-05-25 12:31:52,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:31:52,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:31:52,926 - INFO - Response - Page 3:
2025-05-25 12:31:52,926 - INFO - 第 3 页获取到 100 条记录
2025-05-25 12:31:53,129 - INFO - Request Parameters - Page 4:
2025-05-25 12:31:53,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:31:53,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:31:53,910 - INFO - Response - Page 4:
2025-05-25 12:31:53,910 - INFO - 第 4 页获取到 100 条记录
2025-05-25 12:31:54,114 - INFO - Request Parameters - Page 5:
2025-05-25 12:31:54,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:31:54,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:31:54,879 - INFO - Response - Page 5:
2025-05-25 12:31:54,879 - INFO - 第 5 页获取到 61 条记录
2025-05-25 12:31:55,082 - INFO - 查询完成，共获取到 461 条记录
2025-05-25 12:31:55,082 - INFO - 获取到 461 条表单数据
2025-05-25 12:31:55,082 - INFO - 当前日期 2025-05-24 有 480 条MySQL数据需要处理
2025-05-25 12:31:55,098 - INFO - 开始批量插入 19 条新记录
2025-05-25 12:31:55,254 - INFO - 批量插入响应状态码: 200
2025-05-25 12:31:55,254 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 04:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '905', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BDB697BE-FD8E-7C48-8E88-98C4F373B017', 'x-acs-trace-id': '86b48c6c5355d05161b753d237e546e4', 'etag': '9pcqjHeMbVC++qoCe1Zd51w5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 12:31:55,254 - INFO - 批量插入响应体: {'result': ['FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BM9', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMA', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMB', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMC', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMD', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BME', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMF', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMG', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMH', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMI', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMJ', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMK', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BML', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMM', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMN', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMO', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMP', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMQ', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMR']}
2025-05-25 12:31:55,254 - INFO - 批量插入表单数据成功，批次 1，共 19 条记录
2025-05-25 12:31:55,254 - INFO - 成功插入的数据ID: ['FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BM9', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMA', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMB', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMC', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMD', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BME', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMF', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMG', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMH', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMI', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMJ', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMK', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BML', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMM', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMN', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMO', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMP', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMQ', 'FINST-GX9663D1NBPVTJFQ9959SA77S7KM3NT7W53BMR']
2025-05-25 12:32:00,270 - INFO - 批量插入完成，共 19 条记录
2025-05-25 12:32:00,270 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 19 条，错误: 0 条
2025-05-25 12:32:00,270 - INFO - 开始处理日期: 2025-05-25
2025-05-25 12:32:00,270 - INFO - Request Parameters - Page 1:
2025-05-25 12:32:00,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:32:00,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:32:00,785 - INFO - Response - Page 1:
2025-05-25 12:32:00,785 - INFO - 第 1 页获取到 1 条记录
2025-05-25 12:32:00,988 - INFO - 查询完成，共获取到 1 条记录
2025-05-25 12:32:00,988 - INFO - 获取到 1 条表单数据
2025-05-25 12:32:00,988 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 12:32:00,988 - INFO - 开始批量插入 1 条新记录
2025-05-25 12:32:01,129 - INFO - 批量插入响应状态码: 200
2025-05-25 12:32:01,129 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 04:32:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9EEDC629-0D8C-7756-9375-916C435F6CEF', 'x-acs-trace-id': 'dba32ec03c86f734a448f1f65acb757d', 'etag': '6CT90OSm6UpqU79rQhUMRiw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 12:32:01,129 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1VFOVX2I27SNV27UKWHH43UCCW53BMX5']}
2025-05-25 12:32:01,129 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-25 12:32:01,129 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1VFOVX2I27SNV27UKWHH43UCCW53BMX5']
2025-05-25 12:32:06,145 - INFO - 批量插入完成，共 1 条记录
2025-05-25 12:32:06,145 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-25 12:32:06,145 - INFO - 数据同步完成！更新: 0 条，插入: 20 条，错误: 0 条
2025-05-25 12:32:06,145 - INFO - 同步完成
2025-05-25 13:30:33,747 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 13:30:33,747 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 13:30:33,747 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 13:30:33,825 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 149 条记录
2025-05-25 13:30:33,825 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 13:30:33,825 - INFO - 开始处理日期: 2025-05-24
2025-05-25 13:30:33,825 - INFO - Request Parameters - Page 1:
2025-05-25 13:30:33,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 13:30:33,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 13:30:41,950 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2E6AB03E-3FC4-75EB-BB9A-0409CA28C396 Response: {'code': 'ServiceUnavailable', 'requestid': '2E6AB03E-3FC4-75EB-BB9A-0409CA28C396', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2E6AB03E-3FC4-75EB-BB9A-0409CA28C396)
2025-05-25 13:30:41,950 - INFO - 开始处理日期: 2025-05-25
2025-05-25 13:30:41,950 - INFO - Request Parameters - Page 1:
2025-05-25 13:30:41,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 13:30:41,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 13:30:50,075 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EB960E4A-1401-732E-9BC2-5587C00D4404 Response: {'code': 'ServiceUnavailable', 'requestid': 'EB960E4A-1401-732E-9BC2-5587C00D4404', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EB960E4A-1401-732E-9BC2-5587C00D4404)
2025-05-25 13:30:50,075 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 13:31:50,090 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 13:31:50,090 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 13:31:50,090 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 13:31:50,168 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 482 条记录
2025-05-25 13:31:50,168 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 13:31:50,168 - INFO - 开始处理日期: 2025-05-24
2025-05-25 13:31:50,168 - INFO - Request Parameters - Page 1:
2025-05-25 13:31:50,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 13:31:50,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 13:31:51,074 - INFO - Response - Page 1:
2025-05-25 13:31:51,074 - INFO - 第 1 页获取到 100 条记录
2025-05-25 13:31:51,277 - INFO - Request Parameters - Page 2:
2025-05-25 13:31:51,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 13:31:51,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 13:31:52,105 - INFO - Response - Page 2:
2025-05-25 13:31:52,105 - INFO - 第 2 页获取到 100 条记录
2025-05-25 13:31:52,309 - INFO - Request Parameters - Page 3:
2025-05-25 13:31:52,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 13:31:52,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 13:31:53,137 - INFO - Response - Page 3:
2025-05-25 13:31:53,137 - INFO - 第 3 页获取到 100 条记录
2025-05-25 13:31:53,340 - INFO - Request Parameters - Page 4:
2025-05-25 13:31:53,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 13:31:53,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 13:31:54,402 - INFO - Response - Page 4:
2025-05-25 13:31:54,402 - INFO - 第 4 页获取到 100 条记录
2025-05-25 13:31:54,605 - INFO - Request Parameters - Page 5:
2025-05-25 13:31:54,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 13:31:54,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 13:31:55,387 - INFO - Response - Page 5:
2025-05-25 13:31:55,387 - INFO - 第 5 页获取到 80 条记录
2025-05-25 13:31:55,590 - INFO - 查询完成，共获取到 480 条记录
2025-05-25 13:31:55,590 - INFO - 获取到 480 条表单数据
2025-05-25 13:31:55,590 - INFO - 当前日期 2025-05-24 有 480 条MySQL数据需要处理
2025-05-25 13:31:55,605 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 13:31:55,605 - INFO - 开始处理日期: 2025-05-25
2025-05-25 13:31:55,605 - INFO - Request Parameters - Page 1:
2025-05-25 13:31:55,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 13:31:55,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 13:31:56,137 - INFO - Response - Page 1:
2025-05-25 13:31:56,137 - INFO - 第 1 页获取到 2 条记录
2025-05-25 13:31:56,340 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 13:31:56,340 - INFO - 获取到 2 条表单数据
2025-05-25 13:31:56,340 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 13:31:56,340 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 13:31:56,340 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 13:31:56,340 - INFO - 同步完成
2025-05-25 14:30:33,879 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 14:30:33,879 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 14:30:33,879 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 14:30:33,957 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 160 条记录
2025-05-25 14:30:33,957 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 14:30:33,957 - INFO - 开始处理日期: 2025-05-24
2025-05-25 14:30:33,957 - INFO - Request Parameters - Page 1:
2025-05-25 14:30:33,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 14:30:33,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 14:30:42,082 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F81CF7FF-5967-7989-ACFF-9824B819B23D Response: {'code': 'ServiceUnavailable', 'requestid': 'F81CF7FF-5967-7989-ACFF-9824B819B23D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F81CF7FF-5967-7989-ACFF-9824B819B23D)
2025-05-25 14:30:42,082 - INFO - 开始处理日期: 2025-05-25
2025-05-25 14:30:42,082 - INFO - Request Parameters - Page 1:
2025-05-25 14:30:42,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 14:30:42,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 14:30:44,379 - INFO - Response - Page 1:
2025-05-25 14:30:44,379 - INFO - 第 1 页获取到 2 条记录
2025-05-25 14:30:44,582 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 14:30:44,582 - INFO - 获取到 2 条表单数据
2025-05-25 14:30:44,582 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 14:30:44,582 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 14:30:44,582 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 14:31:44,597 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 14:31:44,597 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 14:31:44,597 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 14:31:44,675 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 502 条记录
2025-05-25 14:31:44,675 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 14:31:44,675 - INFO - 开始处理日期: 2025-05-24
2025-05-25 14:31:44,675 - INFO - Request Parameters - Page 1:
2025-05-25 14:31:44,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 14:31:44,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 14:31:52,785 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 97E610BD-65C3-72F6-B150-0B20DC38EB43 Response: {'code': 'ServiceUnavailable', 'requestid': '97E610BD-65C3-72F6-B150-0B20DC38EB43', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 97E610BD-65C3-72F6-B150-0B20DC38EB43)
2025-05-25 14:31:52,785 - INFO - 开始处理日期: 2025-05-25
2025-05-25 14:31:52,785 - INFO - Request Parameters - Page 1:
2025-05-25 14:31:52,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 14:31:52,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 14:32:00,488 - INFO - Response - Page 1:
2025-05-25 14:32:00,488 - INFO - 第 1 页获取到 2 条记录
2025-05-25 14:32:00,691 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 14:32:00,691 - INFO - 获取到 2 条表单数据
2025-05-25 14:32:00,691 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 14:32:00,691 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 14:32:00,691 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 14:32:00,691 - INFO - 同步完成
2025-05-25 15:30:33,887 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 15:30:33,887 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 15:30:33,887 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 15:30:33,965 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 168 条记录
2025-05-25 15:30:33,965 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 15:30:33,965 - INFO - 开始处理日期: 2025-05-24
2025-05-25 15:30:33,965 - INFO - Request Parameters - Page 1:
2025-05-25 15:30:33,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:30:33,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:30:42,074 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5ECDD231-BA84-72D9-91DC-5D9BD273CF01 Response: {'code': 'ServiceUnavailable', 'requestid': '5ECDD231-BA84-72D9-91DC-5D9BD273CF01', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5ECDD231-BA84-72D9-91DC-5D9BD273CF01)
2025-05-25 15:30:42,074 - INFO - 开始处理日期: 2025-05-25
2025-05-25 15:30:42,074 - INFO - Request Parameters - Page 1:
2025-05-25 15:30:42,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:30:42,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:30:50,183 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D0DF2EA1-B30F-7B69-8F49-C04D6D284C7A Response: {'code': 'ServiceUnavailable', 'requestid': 'D0DF2EA1-B30F-7B69-8F49-C04D6D284C7A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D0DF2EA1-B30F-7B69-8F49-C04D6D284C7A)
2025-05-25 15:30:50,183 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 15:31:50,199 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 15:31:50,199 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 15:31:50,199 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 15:31:50,277 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 510 条记录
2025-05-25 15:31:50,277 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 15:31:50,277 - INFO - 开始处理日期: 2025-05-24
2025-05-25 15:31:50,277 - INFO - Request Parameters - Page 1:
2025-05-25 15:31:50,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:31:50,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:31:51,167 - INFO - Response - Page 1:
2025-05-25 15:31:51,167 - INFO - 第 1 页获取到 100 条记录
2025-05-25 15:31:51,370 - INFO - Request Parameters - Page 2:
2025-05-25 15:31:51,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:31:51,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:31:52,136 - INFO - Response - Page 2:
2025-05-25 15:31:52,136 - INFO - 第 2 页获取到 100 条记录
2025-05-25 15:31:52,339 - INFO - Request Parameters - Page 3:
2025-05-25 15:31:52,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:31:52,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:31:53,120 - INFO - Response - Page 3:
2025-05-25 15:31:53,120 - INFO - 第 3 页获取到 100 条记录
2025-05-25 15:31:53,324 - INFO - Request Parameters - Page 4:
2025-05-25 15:31:53,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:31:53,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:31:54,089 - INFO - Response - Page 4:
2025-05-25 15:31:54,089 - INFO - 第 4 页获取到 100 条记录
2025-05-25 15:31:54,292 - INFO - Request Parameters - Page 5:
2025-05-25 15:31:54,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:31:54,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:31:55,058 - INFO - Response - Page 5:
2025-05-25 15:31:55,058 - INFO - 第 5 页获取到 80 条记录
2025-05-25 15:31:55,261 - INFO - 查询完成，共获取到 480 条记录
2025-05-25 15:31:55,261 - INFO - 获取到 480 条表单数据
2025-05-25 15:31:55,261 - INFO - 当前日期 2025-05-24 有 508 条MySQL数据需要处理
2025-05-25 15:31:55,277 - INFO - 开始批量插入 28 条新记录
2025-05-25 15:31:55,464 - INFO - 批量插入响应状态码: 200
2025-05-25 15:31:55,464 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 07:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1356', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3EC74BAF-D8DD-7EEF-A83C-A9EDA0C8E45C', 'x-acs-trace-id': '50629744d1035f91853eba0c608638f8', 'etag': '19xEqSlie0h0livgmOyAlBA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 15:31:55,464 - INFO - 批量插入响应体: {'result': ['FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM0H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM1H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM2H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM3H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM4H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM5H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM6H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM7H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM8H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM9H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BMAH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BMBH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BMCH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMDH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMEH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMFH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMGH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMHH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMIH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMJH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMKH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMLH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMMH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMNH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMOH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMPH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMQH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMRH']}
2025-05-25 15:31:55,464 - INFO - 批量插入表单数据成功，批次 1，共 28 条记录
2025-05-25 15:31:55,464 - INFO - 成功插入的数据ID: ['FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM0H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM1H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM2H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM3H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM4H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM5H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM6H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM7H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM8H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BM9H', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BMAH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BMBH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2NDPBC3BMCH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMDH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMEH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMFH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMGH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMHH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMIH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMJH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMKH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMLH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMMH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMNH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMOH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMPH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMQH', 'FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMRH']
2025-05-25 15:32:00,480 - INFO - 批量插入完成，共 28 条记录
2025-05-25 15:32:00,480 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 28 条，错误: 0 条
2025-05-25 15:32:00,480 - INFO - 开始处理日期: 2025-05-25
2025-05-25 15:32:00,480 - INFO - Request Parameters - Page 1:
2025-05-25 15:32:00,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:32:00,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:32:00,964 - INFO - Response - Page 1:
2025-05-25 15:32:00,964 - INFO - 第 1 页获取到 2 条记录
2025-05-25 15:32:01,167 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 15:32:01,167 - INFO - 获取到 2 条表单数据
2025-05-25 15:32:01,167 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 15:32:01,167 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 15:32:01,167 - INFO - 数据同步完成！更新: 0 条，插入: 28 条，错误: 0 条
2025-05-25 15:32:01,167 - INFO - 同步完成
2025-05-25 16:30:34,098 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 16:30:34,098 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 16:30:34,098 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 16:30:34,176 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 182 条记录
2025-05-25 16:30:34,176 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 16:30:34,176 - INFO - 开始处理日期: 2025-05-24
2025-05-25 16:30:34,176 - INFO - Request Parameters - Page 1:
2025-05-25 16:30:34,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:30:34,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:30:42,301 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9CE29398-1F3C-763D-81F1-6F24859FF285 Response: {'code': 'ServiceUnavailable', 'requestid': '9CE29398-1F3C-763D-81F1-6F24859FF285', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9CE29398-1F3C-763D-81F1-6F24859FF285)
2025-05-25 16:30:42,301 - INFO - 开始处理日期: 2025-05-25
2025-05-25 16:30:42,301 - INFO - Request Parameters - Page 1:
2025-05-25 16:30:42,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:30:42,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:30:50,410 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A7ABBF3B-49D3-799A-9AFA-446B4656B6A4 Response: {'code': 'ServiceUnavailable', 'requestid': 'A7ABBF3B-49D3-799A-9AFA-446B4656B6A4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A7ABBF3B-49D3-799A-9AFA-446B4656B6A4)
2025-05-25 16:30:50,410 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 16:31:50,425 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 16:31:50,425 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 16:31:50,425 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 16:31:50,503 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 524 条记录
2025-05-25 16:31:50,503 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 16:31:50,503 - INFO - 开始处理日期: 2025-05-24
2025-05-25 16:31:50,503 - INFO - Request Parameters - Page 1:
2025-05-25 16:31:50,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:31:50,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:31:51,566 - INFO - Response - Page 1:
2025-05-25 16:31:51,566 - INFO - 第 1 页获取到 100 条记录
2025-05-25 16:31:51,769 - INFO - Request Parameters - Page 2:
2025-05-25 16:31:51,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:31:51,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:31:52,566 - INFO - Response - Page 2:
2025-05-25 16:31:52,566 - INFO - 第 2 页获取到 100 条记录
2025-05-25 16:31:52,769 - INFO - Request Parameters - Page 3:
2025-05-25 16:31:52,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:31:52,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:31:53,534 - INFO - Response - Page 3:
2025-05-25 16:31:53,534 - INFO - 第 3 页获取到 100 条记录
2025-05-25 16:31:53,738 - INFO - Request Parameters - Page 4:
2025-05-25 16:31:53,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:31:53,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:31:54,597 - INFO - Response - Page 4:
2025-05-25 16:31:54,597 - INFO - 第 4 页获取到 100 条记录
2025-05-25 16:31:54,800 - INFO - Request Parameters - Page 5:
2025-05-25 16:31:54,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:31:54,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:31:55,566 - INFO - Response - Page 5:
2025-05-25 16:31:55,566 - INFO - 第 5 页获取到 100 条记录
2025-05-25 16:31:55,769 - INFO - Request Parameters - Page 6:
2025-05-25 16:31:55,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:31:55,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:31:56,316 - INFO - Response - Page 6:
2025-05-25 16:31:56,316 - INFO - 第 6 页获取到 8 条记录
2025-05-25 16:31:56,519 - INFO - 查询完成，共获取到 508 条记录
2025-05-25 16:31:56,519 - INFO - 获取到 508 条表单数据
2025-05-25 16:31:56,519 - INFO - 当前日期 2025-05-24 有 522 条MySQL数据需要处理
2025-05-25 16:31:56,534 - INFO - 开始批量插入 14 条新记录
2025-05-25 16:31:56,691 - INFO - 批量插入响应状态码: 200
2025-05-25 16:31:56,691 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 08:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C0823A85-334B-7240-B8D2-09EA256C8DAD', 'x-acs-trace-id': '84703b6ce89c7e7b57e90bebf1edaa1d', 'etag': '6TGAtmKFsKL+3dbPnpbgGOA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 16:31:56,691 - INFO - 批量插入响应体: {'result': ['FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BM87', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BM97', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMA7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMB7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMC7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMD7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BME7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMF7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMG7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMH7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMI7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMJ7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMK7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BML7']}
2025-05-25 16:31:56,691 - INFO - 批量插入表单数据成功，批次 1，共 14 条记录
2025-05-25 16:31:56,691 - INFO - 成功插入的数据ID: ['FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BM87', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BM97', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMA7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMB7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMC7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMD7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BME7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMF7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMG7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMH7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMI7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMJ7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BMK7', 'FINST-ZX86657163QV7ZNDEKKO68SGD5943C4WGE3BML7']
2025-05-25 16:32:01,706 - INFO - 批量插入完成，共 14 条记录
2025-05-25 16:32:01,706 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 14 条，错误: 0 条
2025-05-25 16:32:01,706 - INFO - 开始处理日期: 2025-05-25
2025-05-25 16:32:01,706 - INFO - Request Parameters - Page 1:
2025-05-25 16:32:01,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 16:32:01,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 16:32:02,222 - INFO - Response - Page 1:
2025-05-25 16:32:02,222 - INFO - 第 1 页获取到 2 条记录
2025-05-25 16:32:02,425 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 16:32:02,425 - INFO - 获取到 2 条表单数据
2025-05-25 16:32:02,425 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 16:32:02,425 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 16:32:02,425 - INFO - 数据同步完成！更新: 0 条，插入: 14 条，错误: 0 条
2025-05-25 16:32:02,425 - INFO - 同步完成
2025-05-25 17:30:33,887 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 17:30:33,887 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 17:30:33,887 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 17:30:33,965 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 187 条记录
2025-05-25 17:30:33,965 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 17:30:33,965 - INFO - 开始处理日期: 2025-05-24
2025-05-25 17:30:33,965 - INFO - Request Parameters - Page 1:
2025-05-25 17:30:33,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:30:33,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:30:42,074 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 50E555EE-8F47-7D55-878B-8F62C5666C4C Response: {'code': 'ServiceUnavailable', 'requestid': '50E555EE-8F47-7D55-878B-8F62C5666C4C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 50E555EE-8F47-7D55-878B-8F62C5666C4C)
2025-05-25 17:30:42,074 - INFO - 开始处理日期: 2025-05-25
2025-05-25 17:30:42,074 - INFO - Request Parameters - Page 1:
2025-05-25 17:30:42,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:30:42,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:30:50,183 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4DAB5CD8-3B78-79EB-B2E0-B67FDC28137B Response: {'code': 'ServiceUnavailable', 'requestid': '4DAB5CD8-3B78-79EB-B2E0-B67FDC28137B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4DAB5CD8-3B78-79EB-B2E0-B67FDC28137B)
2025-05-25 17:30:50,183 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 17:31:50,199 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 17:31:50,199 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 17:31:50,199 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 17:31:50,277 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 529 条记录
2025-05-25 17:31:50,277 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 17:31:50,277 - INFO - 开始处理日期: 2025-05-24
2025-05-25 17:31:50,277 - INFO - Request Parameters - Page 1:
2025-05-25 17:31:50,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:31:50,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:31:51,199 - INFO - Response - Page 1:
2025-05-25 17:31:51,199 - INFO - 第 1 页获取到 100 条记录
2025-05-25 17:31:51,402 - INFO - Request Parameters - Page 2:
2025-05-25 17:31:51,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:31:51,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:31:52,261 - INFO - Response - Page 2:
2025-05-25 17:31:52,261 - INFO - 第 2 页获取到 100 条记录
2025-05-25 17:31:52,464 - INFO - Request Parameters - Page 3:
2025-05-25 17:31:52,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:31:52,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:31:53,292 - INFO - Response - Page 3:
2025-05-25 17:31:53,292 - INFO - 第 3 页获取到 100 条记录
2025-05-25 17:31:53,496 - INFO - Request Parameters - Page 4:
2025-05-25 17:31:53,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:31:53,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:31:54,261 - INFO - Response - Page 4:
2025-05-25 17:31:54,261 - INFO - 第 4 页获取到 100 条记录
2025-05-25 17:31:54,464 - INFO - Request Parameters - Page 5:
2025-05-25 17:31:54,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:31:54,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:31:55,308 - INFO - Response - Page 5:
2025-05-25 17:31:55,308 - INFO - 第 5 页获取到 100 条记录
2025-05-25 17:31:55,511 - INFO - Request Parameters - Page 6:
2025-05-25 17:31:55,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:31:55,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:31:56,167 - INFO - Response - Page 6:
2025-05-25 17:31:56,167 - INFO - 第 6 页获取到 22 条记录
2025-05-25 17:31:56,370 - INFO - 查询完成，共获取到 522 条记录
2025-05-25 17:31:56,370 - INFO - 获取到 522 条表单数据
2025-05-25 17:31:56,370 - INFO - 当前日期 2025-05-24 有 527 条MySQL数据需要处理
2025-05-25 17:31:56,386 - INFO - 开始批量插入 5 条新记录
2025-05-25 17:31:56,527 - INFO - 批量插入响应状态码: 200
2025-05-25 17:31:56,527 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 09:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F94A2522-F9DD-74DB-AB15-4648169D25D1', 'x-acs-trace-id': 'bce08347f7865ff89933f4d61f8cf3cc', 'etag': '2qtrO/Rzjl7I7QIRumNaVtw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 17:31:56,527 - INFO - 批量插入响应体: {'result': ['FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMII', 'FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMJI', 'FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMKI', 'FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMLI', 'FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMMI']}
2025-05-25 17:31:56,527 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-05-25 17:31:56,527 - INFO - 成功插入的数据ID: ['FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMII', 'FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMJI', 'FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMKI', 'FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMLI', 'FINST-VME66K81U2NVP73V6I6S5BZE9XSQ2GS1MG3BMMI']
2025-05-25 17:32:01,542 - INFO - 批量插入完成，共 5 条记录
2025-05-25 17:32:01,542 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-05-25 17:32:01,542 - INFO - 开始处理日期: 2025-05-25
2025-05-25 17:32:01,542 - INFO - Request Parameters - Page 1:
2025-05-25 17:32:01,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 17:32:01,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 17:32:02,058 - INFO - Response - Page 1:
2025-05-25 17:32:02,058 - INFO - 第 1 页获取到 2 条记录
2025-05-25 17:32:02,261 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 17:32:02,261 - INFO - 获取到 2 条表单数据
2025-05-25 17:32:02,261 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 17:32:02,261 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 17:32:02,261 - INFO - 数据同步完成！更新: 0 条，插入: 5 条，错误: 0 条
2025-05-25 17:32:02,261 - INFO - 同步完成
2025-05-25 18:30:34,316 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 18:30:34,316 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 18:30:34,316 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 18:30:34,395 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 187 条记录
2025-05-25 18:30:34,395 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 18:30:34,395 - INFO - 开始处理日期: 2025-05-24
2025-05-25 18:30:34,395 - INFO - Request Parameters - Page 1:
2025-05-25 18:30:34,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:30:34,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:30:42,519 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0938B6E5-25A4-7035-AC59-DC9BFD6F2E23 Response: {'code': 'ServiceUnavailable', 'requestid': '0938B6E5-25A4-7035-AC59-DC9BFD6F2E23', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0938B6E5-25A4-7035-AC59-DC9BFD6F2E23)
2025-05-25 18:30:42,519 - INFO - 开始处理日期: 2025-05-25
2025-05-25 18:30:42,519 - INFO - Request Parameters - Page 1:
2025-05-25 18:30:42,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:30:42,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:30:50,644 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2D6EDD3C-C14F-7E6E-883C-B7DB8B78243F Response: {'code': 'ServiceUnavailable', 'requestid': '2D6EDD3C-C14F-7E6E-883C-B7DB8B78243F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2D6EDD3C-C14F-7E6E-883C-B7DB8B78243F)
2025-05-25 18:30:50,644 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 18:31:50,660 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 18:31:50,660 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 18:31:50,660 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 18:31:50,738 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 529 条记录
2025-05-25 18:31:50,738 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 18:31:50,738 - INFO - 开始处理日期: 2025-05-24
2025-05-25 18:31:50,738 - INFO - Request Parameters - Page 1:
2025-05-25 18:31:50,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:31:50,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:31:51,628 - INFO - Response - Page 1:
2025-05-25 18:31:51,628 - INFO - 第 1 页获取到 100 条记录
2025-05-25 18:31:51,832 - INFO - Request Parameters - Page 2:
2025-05-25 18:31:51,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:31:51,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:31:52,800 - INFO - Response - Page 2:
2025-05-25 18:31:52,800 - INFO - 第 2 页获取到 100 条记录
2025-05-25 18:31:53,003 - INFO - Request Parameters - Page 3:
2025-05-25 18:31:53,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:31:53,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:31:53,769 - INFO - Response - Page 3:
2025-05-25 18:31:53,769 - INFO - 第 3 页获取到 100 条记录
2025-05-25 18:31:53,972 - INFO - Request Parameters - Page 4:
2025-05-25 18:31:53,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:31:53,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:31:54,753 - INFO - Response - Page 4:
2025-05-25 18:31:54,769 - INFO - 第 4 页获取到 100 条记录
2025-05-25 18:31:54,972 - INFO - Request Parameters - Page 5:
2025-05-25 18:31:54,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:31:54,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:31:55,832 - INFO - Response - Page 5:
2025-05-25 18:31:55,832 - INFO - 第 5 页获取到 100 条记录
2025-05-25 18:31:56,035 - INFO - Request Parameters - Page 6:
2025-05-25 18:31:56,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:31:56,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:31:56,691 - INFO - Response - Page 6:
2025-05-25 18:31:56,691 - INFO - 第 6 页获取到 27 条记录
2025-05-25 18:31:56,894 - INFO - 查询完成，共获取到 527 条记录
2025-05-25 18:31:56,894 - INFO - 获取到 527 条表单数据
2025-05-25 18:31:56,894 - INFO - 当前日期 2025-05-24 有 527 条MySQL数据需要处理
2025-05-25 18:31:56,910 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 18:31:56,910 - INFO - 开始处理日期: 2025-05-25
2025-05-25 18:31:56,910 - INFO - Request Parameters - Page 1:
2025-05-25 18:31:56,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:31:56,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:31:57,457 - INFO - Response - Page 1:
2025-05-25 18:31:57,457 - INFO - 第 1 页获取到 2 条记录
2025-05-25 18:31:57,660 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 18:31:57,660 - INFO - 获取到 2 条表单数据
2025-05-25 18:31:57,660 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 18:31:57,660 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 18:31:57,660 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 18:31:57,660 - INFO - 同步完成
2025-05-25 19:30:34,643 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 19:30:34,643 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 19:30:34,643 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 19:30:34,721 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 187 条记录
2025-05-25 19:30:34,721 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 19:30:34,721 - INFO - 开始处理日期: 2025-05-24
2025-05-25 19:30:34,721 - INFO - Request Parameters - Page 1:
2025-05-25 19:30:34,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:30:34,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:30:42,829 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 85DEE340-482D-7750-8613-76188F180731 Response: {'code': 'ServiceUnavailable', 'requestid': '85DEE340-482D-7750-8613-76188F180731', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 85DEE340-482D-7750-8613-76188F180731)
2025-05-25 19:30:42,829 - INFO - 开始处理日期: 2025-05-25
2025-05-25 19:30:42,829 - INFO - Request Parameters - Page 1:
2025-05-25 19:30:42,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:30:42,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:30:45,157 - INFO - Response - Page 1:
2025-05-25 19:30:45,157 - INFO - 第 1 页获取到 2 条记录
2025-05-25 19:30:45,360 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 19:30:45,360 - INFO - 获取到 2 条表单数据
2025-05-25 19:30:45,360 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 19:30:45,360 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 19:30:45,360 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 19:31:45,367 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 19:31:45,367 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 19:31:45,367 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 19:31:45,445 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 529 条记录
2025-05-25 19:31:45,445 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 19:31:45,445 - INFO - 开始处理日期: 2025-05-24
2025-05-25 19:31:45,445 - INFO - Request Parameters - Page 1:
2025-05-25 19:31:45,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:31:45,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:31:46,383 - INFO - Response - Page 1:
2025-05-25 19:31:46,383 - INFO - 第 1 页获取到 100 条记录
2025-05-25 19:31:46,586 - INFO - Request Parameters - Page 2:
2025-05-25 19:31:46,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:31:46,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:31:47,398 - INFO - Response - Page 2:
2025-05-25 19:31:47,398 - INFO - 第 2 页获取到 100 条记录
2025-05-25 19:31:47,601 - INFO - Request Parameters - Page 3:
2025-05-25 19:31:47,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:31:47,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:31:48,336 - INFO - Response - Page 3:
2025-05-25 19:31:48,336 - INFO - 第 3 页获取到 100 条记录
2025-05-25 19:31:48,539 - INFO - Request Parameters - Page 4:
2025-05-25 19:31:48,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:31:48,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:31:49,382 - INFO - Response - Page 4:
2025-05-25 19:31:49,382 - INFO - 第 4 页获取到 100 条记录
2025-05-25 19:31:49,585 - INFO - Request Parameters - Page 5:
2025-05-25 19:31:49,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:31:49,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:31:50,398 - INFO - Response - Page 5:
2025-05-25 19:31:50,398 - INFO - 第 5 页获取到 100 条记录
2025-05-25 19:31:50,601 - INFO - Request Parameters - Page 6:
2025-05-25 19:31:50,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:31:50,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:31:51,320 - INFO - Response - Page 6:
2025-05-25 19:31:51,320 - INFO - 第 6 页获取到 27 条记录
2025-05-25 19:31:51,523 - INFO - 查询完成，共获取到 527 条记录
2025-05-25 19:31:51,523 - INFO - 获取到 527 条表单数据
2025-05-25 19:31:51,523 - INFO - 当前日期 2025-05-24 有 527 条MySQL数据需要处理
2025-05-25 19:31:51,538 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 19:31:51,538 - INFO - 开始处理日期: 2025-05-25
2025-05-25 19:31:51,538 - INFO - Request Parameters - Page 1:
2025-05-25 19:31:51,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 19:31:51,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 19:31:52,007 - INFO - Response - Page 1:
2025-05-25 19:31:52,007 - INFO - 第 1 页获取到 2 条记录
2025-05-25 19:31:52,210 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 19:31:52,210 - INFO - 获取到 2 条表单数据
2025-05-25 19:31:52,210 - INFO - 当前日期 2025-05-25 有 2 条MySQL数据需要处理
2025-05-25 19:31:52,210 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 19:31:52,210 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 19:31:52,210 - INFO - 同步完成
2025-05-25 20:30:33,182 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 20:30:33,182 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 20:30:33,182 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 20:30:33,244 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 188 条记录
2025-05-25 20:30:33,244 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 20:30:33,260 - INFO - 开始处理日期: 2025-05-24
2025-05-25 20:30:33,260 - INFO - Request Parameters - Page 1:
2025-05-25 20:30:33,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:30:33,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:30:41,368 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5EEE95F5-EFA2-756F-BC6D-EC9CF6C1D9B5 Response: {'code': 'ServiceUnavailable', 'requestid': '5EEE95F5-EFA2-756F-BC6D-EC9CF6C1D9B5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5EEE95F5-EFA2-756F-BC6D-EC9CF6C1D9B5)
2025-05-25 20:30:41,368 - INFO - 开始处理日期: 2025-05-25
2025-05-25 20:30:41,384 - INFO - Request Parameters - Page 1:
2025-05-25 20:30:41,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:30:41,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:30:49,476 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4ED002A0-9D22-747B-B223-069086431804 Response: {'code': 'ServiceUnavailable', 'requestid': '4ED002A0-9D22-747B-B223-069086431804', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4ED002A0-9D22-747B-B223-069086431804)
2025-05-25 20:30:49,476 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 20:31:49,483 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 20:31:49,483 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 20:31:49,483 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 20:31:49,562 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 530 条记录
2025-05-25 20:31:49,562 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 20:31:49,562 - INFO - 开始处理日期: 2025-05-24
2025-05-25 20:31:49,562 - INFO - Request Parameters - Page 1:
2025-05-25 20:31:49,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:31:49,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:31:50,374 - INFO - Response - Page 1:
2025-05-25 20:31:50,374 - INFO - 第 1 页获取到 100 条记录
2025-05-25 20:31:50,577 - INFO - Request Parameters - Page 2:
2025-05-25 20:31:50,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:31:50,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:31:51,389 - INFO - Response - Page 2:
2025-05-25 20:31:51,389 - INFO - 第 2 页获取到 100 条记录
2025-05-25 20:31:51,592 - INFO - Request Parameters - Page 3:
2025-05-25 20:31:51,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:31:51,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:31:52,499 - INFO - Response - Page 3:
2025-05-25 20:31:52,499 - INFO - 第 3 页获取到 100 条记录
2025-05-25 20:31:52,702 - INFO - Request Parameters - Page 4:
2025-05-25 20:31:52,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:31:52,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:31:53,577 - INFO - Response - Page 4:
2025-05-25 20:31:53,577 - INFO - 第 4 页获取到 100 条记录
2025-05-25 20:31:53,780 - INFO - Request Parameters - Page 5:
2025-05-25 20:31:53,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:31:53,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:31:54,608 - INFO - Response - Page 5:
2025-05-25 20:31:54,608 - INFO - 第 5 页获取到 100 条记录
2025-05-25 20:31:54,811 - INFO - Request Parameters - Page 6:
2025-05-25 20:31:54,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:31:54,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:31:55,483 - INFO - Response - Page 6:
2025-05-25 20:31:55,483 - INFO - 第 6 页获取到 27 条记录
2025-05-25 20:31:55,686 - INFO - 查询完成，共获取到 527 条记录
2025-05-25 20:31:55,686 - INFO - 获取到 527 条表单数据
2025-05-25 20:31:55,686 - INFO - 当前日期 2025-05-24 有 527 条MySQL数据需要处理
2025-05-25 20:31:55,701 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 20:31:55,701 - INFO - 开始处理日期: 2025-05-25
2025-05-25 20:31:55,701 - INFO - Request Parameters - Page 1:
2025-05-25 20:31:55,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 20:31:55,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 20:31:56,170 - INFO - Response - Page 1:
2025-05-25 20:31:56,170 - INFO - 第 1 页获取到 2 条记录
2025-05-25 20:31:56,373 - INFO - 查询完成，共获取到 2 条记录
2025-05-25 20:31:56,373 - INFO - 获取到 2 条表单数据
2025-05-25 20:31:56,373 - INFO - 当前日期 2025-05-25 有 3 条MySQL数据需要处理
2025-05-25 20:31:56,373 - INFO - 开始批量插入 1 条新记录
2025-05-25 20:31:56,514 - INFO - 批量插入响应状态码: 200
2025-05-25 20:31:56,514 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 12:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3A0C41BA-A620-7D69-A7D3-7C8993783486', 'x-acs-trace-id': '93639ca3fbdefd7a81bf89bcaeeb849d', 'etag': '5sAcYd+SFOLpQc2AwvruhEA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 20:31:56,514 - INFO - 批量插入响应体: {'result': ['FINST-8SG66JA11HQVU1DYBJ2RSD9SJNS0298J1N3BM7']}
2025-05-25 20:31:56,514 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-25 20:31:56,514 - INFO - 成功插入的数据ID: ['FINST-8SG66JA11HQVU1DYBJ2RSD9SJNS0298J1N3BM7']
2025-05-25 20:32:01,529 - INFO - 批量插入完成，共 1 条记录
2025-05-25 20:32:01,529 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-25 20:32:01,529 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-25 20:32:01,529 - INFO - 同步完成
2025-05-25 21:30:33,346 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 21:30:33,346 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 21:30:33,346 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 21:30:33,409 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 193 条记录
2025-05-25 21:30:33,409 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 21:30:33,409 - INFO - 开始处理日期: 2025-05-24
2025-05-25 21:30:33,425 - INFO - Request Parameters - Page 1:
2025-05-25 21:30:33,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:30:33,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:30:41,517 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EBBEFF24-16C6-7AC4-9437-114FC0CBF885 Response: {'code': 'ServiceUnavailable', 'requestid': 'EBBEFF24-16C6-7AC4-9437-114FC0CBF885', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EBBEFF24-16C6-7AC4-9437-114FC0CBF885)
2025-05-25 21:30:41,517 - INFO - 开始处理日期: 2025-05-25
2025-05-25 21:30:41,517 - INFO - Request Parameters - Page 1:
2025-05-25 21:30:41,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:30:41,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:30:49,641 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 13D15A79-A9B9-7B53-892A-248683C31046 Response: {'code': 'ServiceUnavailable', 'requestid': '13D15A79-A9B9-7B53-892A-248683C31046', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 13D15A79-A9B9-7B53-892A-248683C31046)
2025-05-25 21:30:49,641 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 21:31:49,648 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 21:31:49,648 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 21:31:49,648 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 21:31:49,726 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 538 条记录
2025-05-25 21:31:49,726 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 21:31:49,726 - INFO - 开始处理日期: 2025-05-24
2025-05-25 21:31:49,726 - INFO - Request Parameters - Page 1:
2025-05-25 21:31:49,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:31:49,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:31:50,820 - INFO - Response - Page 1:
2025-05-25 21:31:50,820 - INFO - 第 1 页获取到 100 条记录
2025-05-25 21:31:51,023 - INFO - Request Parameters - Page 2:
2025-05-25 21:31:51,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:31:51,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:31:51,851 - INFO - Response - Page 2:
2025-05-25 21:31:51,851 - INFO - 第 2 页获取到 100 条记录
2025-05-25 21:31:52,054 - INFO - Request Parameters - Page 3:
2025-05-25 21:31:52,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:31:52,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:31:52,913 - INFO - Response - Page 3:
2025-05-25 21:31:52,913 - INFO - 第 3 页获取到 100 条记录
2025-05-25 21:31:53,116 - INFO - Request Parameters - Page 4:
2025-05-25 21:31:53,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:31:53,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:31:53,898 - INFO - Response - Page 4:
2025-05-25 21:31:53,898 - INFO - 第 4 页获取到 100 条记录
2025-05-25 21:31:54,101 - INFO - Request Parameters - Page 5:
2025-05-25 21:31:54,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:31:54,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:31:54,866 - INFO - Response - Page 5:
2025-05-25 21:31:54,866 - INFO - 第 5 页获取到 100 条记录
2025-05-25 21:31:55,069 - INFO - Request Parameters - Page 6:
2025-05-25 21:31:55,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:31:55,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:31:55,741 - INFO - Response - Page 6:
2025-05-25 21:31:55,741 - INFO - 第 6 页获取到 27 条记录
2025-05-25 21:31:55,944 - INFO - 查询完成，共获取到 527 条记录
2025-05-25 21:31:55,944 - INFO - 获取到 527 条表单数据
2025-05-25 21:31:55,944 - INFO - 当前日期 2025-05-24 有 530 条MySQL数据需要处理
2025-05-25 21:31:55,960 - INFO - 开始批量插入 3 条新记录
2025-05-25 21:31:56,147 - INFO - 批量插入响应状态码: 200
2025-05-25 21:31:56,147 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 13:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A6BEA369-7A93-75DC-A8B9-616DFB765CAE', 'x-acs-trace-id': 'e4ceb824fec2da7c1e84bd792e44867b', 'etag': '1X+up7zEfXuSJA4/UBE2Y1g6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 21:31:56,147 - INFO - 批量插入响应体: {'result': ['FINST-MLF669B10JPV337X8GE63DFYX9JD293P6P3BMW9', 'FINST-MLF669B10JPV337X8GE63DFYX9JD293P6P3BMX9', 'FINST-MLF669B10JPV337X8GE63DFYX9JD293P6P3BMY9']}
2025-05-25 21:31:56,147 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-25 21:31:56,147 - INFO - 成功插入的数据ID: ['FINST-MLF669B10JPV337X8GE63DFYX9JD293P6P3BMW9', 'FINST-MLF669B10JPV337X8GE63DFYX9JD293P6P3BMX9', 'FINST-MLF669B10JPV337X8GE63DFYX9JD293P6P3BMY9']
2025-05-25 21:32:01,162 - INFO - 批量插入完成，共 3 条记录
2025-05-25 21:32:01,162 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-25 21:32:01,162 - INFO - 开始处理日期: 2025-05-25
2025-05-25 21:32:01,162 - INFO - Request Parameters - Page 1:
2025-05-25 21:32:01,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:32:01,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:32:01,646 - INFO - Response - Page 1:
2025-05-25 21:32:01,646 - INFO - 第 1 页获取到 3 条记录
2025-05-25 21:32:01,850 - INFO - 查询完成，共获取到 3 条记录
2025-05-25 21:32:01,850 - INFO - 获取到 3 条表单数据
2025-05-25 21:32:01,850 - INFO - 当前日期 2025-05-25 有 8 条MySQL数据需要处理
2025-05-25 21:32:01,850 - INFO - 开始批量插入 5 条新记录
2025-05-25 21:32:01,990 - INFO - 批量插入响应状态码: 200
2025-05-25 21:32:01,990 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 13:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '257', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'ECBDBFD3-91E9-7C16-A6B6-17CFF68EF8D6', 'x-acs-trace-id': '7f9eb8113988829e9322078b1a895d9c', 'etag': '2ZRm5N+Z04kCU+5Gi2H8fBA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 21:32:01,990 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BMD11', 'FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BME11', 'FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BMF11', 'FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BMG11', 'FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BMH11']}
2025-05-25 21:32:01,990 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-05-25 21:32:01,990 - INFO - 成功插入的数据ID: ['FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BMD11', 'FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BME11', 'FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BMF11', 'FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BMG11', 'FINST-FQD66YB1IBLVL2ZU68FUJDQTAFXK3JMT6P3BMH11']
2025-05-25 21:32:07,005 - INFO - 批量插入完成，共 5 条记录
2025-05-25 21:32:07,005 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-05-25 21:32:07,005 - INFO - 数据同步完成！更新: 0 条，插入: 8 条，错误: 0 条
2025-05-25 21:32:07,005 - INFO - 同步完成
2025-05-25 22:30:33,152 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 22:30:33,152 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 22:30:33,152 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 22:30:33,230 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 199 条记录
2025-05-25 22:30:33,230 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 22:30:33,230 - INFO - 开始处理日期: 2025-05-24
2025-05-25 22:30:33,230 - INFO - Request Parameters - Page 1:
2025-05-25 22:30:33,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:30:33,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:30:41,354 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AAD6D380-D42B-725B-A140-166D459FC6CC Response: {'code': 'ServiceUnavailable', 'requestid': 'AAD6D380-D42B-725B-A140-166D459FC6CC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AAD6D380-D42B-725B-A140-166D459FC6CC)
2025-05-25 22:30:41,354 - INFO - 开始处理日期: 2025-05-25
2025-05-25 22:30:41,354 - INFO - Request Parameters - Page 1:
2025-05-25 22:30:41,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:30:41,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:30:49,478 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 34477B82-7A02-7D95-AE21-399AE1B0ECCA Response: {'code': 'ServiceUnavailable', 'requestid': '34477B82-7A02-7D95-AE21-399AE1B0ECCA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 34477B82-7A02-7D95-AE21-399AE1B0ECCA)
2025-05-25 22:30:49,478 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-25 22:31:49,485 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 22:31:49,485 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 22:31:49,485 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 22:31:49,563 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 544 条记录
2025-05-25 22:31:49,563 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 22:31:49,563 - INFO - 开始处理日期: 2025-05-24
2025-05-25 22:31:49,563 - INFO - Request Parameters - Page 1:
2025-05-25 22:31:49,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:31:49,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:31:50,500 - INFO - Response - Page 1:
2025-05-25 22:31:50,500 - INFO - 第 1 页获取到 100 条记录
2025-05-25 22:31:50,703 - INFO - Request Parameters - Page 2:
2025-05-25 22:31:50,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:31:50,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:31:51,609 - INFO - Response - Page 2:
2025-05-25 22:31:51,609 - INFO - 第 2 页获取到 100 条记录
2025-05-25 22:31:51,813 - INFO - Request Parameters - Page 3:
2025-05-25 22:31:51,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:31:51,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:31:52,609 - INFO - Response - Page 3:
2025-05-25 22:31:52,609 - INFO - 第 3 页获取到 100 条记录
2025-05-25 22:31:52,812 - INFO - Request Parameters - Page 4:
2025-05-25 22:31:52,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:31:52,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:31:53,609 - INFO - Response - Page 4:
2025-05-25 22:31:53,609 - INFO - 第 4 页获取到 100 条记录
2025-05-25 22:31:53,812 - INFO - Request Parameters - Page 5:
2025-05-25 22:31:53,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:31:53,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:31:54,578 - INFO - Response - Page 5:
2025-05-25 22:31:54,578 - INFO - 第 5 页获取到 100 条记录
2025-05-25 22:31:54,781 - INFO - Request Parameters - Page 6:
2025-05-25 22:31:54,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:31:54,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:31:55,406 - INFO - Response - Page 6:
2025-05-25 22:31:55,406 - INFO - 第 6 页获取到 30 条记录
2025-05-25 22:31:55,609 - INFO - 查询完成，共获取到 530 条记录
2025-05-25 22:31:55,609 - INFO - 获取到 530 条表单数据
2025-05-25 22:31:55,609 - INFO - 当前日期 2025-05-24 有 530 条MySQL数据需要处理
2025-05-25 22:31:55,625 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 22:31:55,625 - INFO - 开始处理日期: 2025-05-25
2025-05-25 22:31:55,625 - INFO - Request Parameters - Page 1:
2025-05-25 22:31:55,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 22:31:55,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 22:31:56,203 - INFO - Response - Page 1:
2025-05-25 22:31:56,203 - INFO - 第 1 页获取到 8 条记录
2025-05-25 22:31:56,406 - INFO - 查询完成，共获取到 8 条记录
2025-05-25 22:31:56,406 - INFO - 获取到 8 条表单数据
2025-05-25 22:31:56,406 - INFO - 当前日期 2025-05-25 有 14 条MySQL数据需要处理
2025-05-25 22:31:56,406 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1L4NVKFII9TJHD50Z3LAP2BDCFZ2BM63
2025-05-25 22:31:56,765 - INFO - 更新表单数据成功: FINST-N79668C1L4NVKFII9TJHD50Z3LAP2BDCFZ2BM63
2025-05-25 22:31:56,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4000.0, 'new_value': 7720.0}, {'field': 'total_amount', 'old_value': 4000.0, 'new_value': 7720.0}]
2025-05-25 22:31:56,765 - INFO - 开始批量插入 6 条新记录
2025-05-25 22:31:56,921 - INFO - 批量插入响应状态码: 200
2025-05-25 22:31:56,921 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 14:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7BD24998-F820-778D-B1FD-4FA3AAEE2E76', 'x-acs-trace-id': '897a7d72b737695c7ee08d6e1c06bfc1', 'etag': '3VHShC+czOvn3giqy5Z7puw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 22:31:56,921 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BMW6', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BMX6', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BMY6', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BMZ6', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BM07', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BM17']}
2025-05-25 22:31:56,921 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-05-25 22:31:56,921 - INFO - 成功插入的数据ID: ['FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BMW6', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BMX6', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BMY6', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BMZ6', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BM07', 'FINST-FQD66YB1X9MVC7547XMIZ7F454ZB2SVVBR3BM17']
2025-05-25 22:32:01,936 - INFO - 批量插入完成，共 6 条记录
2025-05-25 22:32:01,936 - INFO - 日期 2025-05-25 处理完成 - 更新: 1 条，插入: 6 条，错误: 0 条
2025-05-25 22:32:01,936 - INFO - 数据同步完成！更新: 1 条，插入: 6 条，错误: 0 条
2025-05-25 22:32:01,936 - INFO - 同步完成
2025-05-25 23:30:35,297 - INFO - 使用默认增量同步（当天更新数据）
2025-05-25 23:30:35,297 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 23:30:35,297 - INFO - 查询参数: ('2025-05-25',)
2025-05-25 23:30:35,375 - INFO - MySQL查询成功，增量数据（日期: 2025-05-25），共获取 281 条记录
2025-05-25 23:30:35,375 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 23:30:35,375 - INFO - 开始处理日期: 2025-05-24
2025-05-25 23:30:35,391 - INFO - Request Parameters - Page 1:
2025-05-25 23:30:35,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 23:30:35,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 23:30:43,515 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 85E666EB-A7A4-755E-A7A9-B93565B42F32 Response: {'code': 'ServiceUnavailable', 'requestid': '85E666EB-A7A4-755E-A7A9-B93565B42F32', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 85E666EB-A7A4-755E-A7A9-B93565B42F32)
2025-05-25 23:30:43,515 - INFO - 开始处理日期: 2025-05-25
2025-05-25 23:30:43,515 - INFO - Request Parameters - Page 1:
2025-05-25 23:30:43,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 23:30:43,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 23:30:51,046 - INFO - Response - Page 1:
2025-05-25 23:30:51,046 - INFO - 第 1 页获取到 14 条记录
2025-05-25 23:30:51,249 - INFO - 查询完成，共获取到 14 条记录
2025-05-25 23:30:51,249 - INFO - 获取到 14 条表单数据
2025-05-25 23:30:51,249 - INFO - 当前日期 2025-05-25 有 96 条MySQL数据需要处理
2025-05-25 23:30:51,249 - INFO - 开始批量插入 82 条新记录
2025-05-25 23:30:51,483 - INFO - 批量插入响应状态码: 200
2025-05-25 23:30:51,483 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 15:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3948', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9551DA67-9F50-771D-8D59-E3B446684B41', 'x-acs-trace-id': 'ff07843a5fd13d7aece6556235b44b15', 'etag': '3udROI8sk6OsvqwpiMjpThQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 23:30:51,483 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMP5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMQ5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMR5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMS5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMT5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMU5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMV5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMW5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMX5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMY5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMZ5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM06', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM16', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM26', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM36', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM46', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM56', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM66', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM76', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM86', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM96', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMA6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMB6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMC6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMD6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BME6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMF6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMG6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMH6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMI6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMJ6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMK6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BML6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMM6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMN6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMO6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMP6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMQ6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMR6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMS6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMT6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMU6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMV6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMW6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMX6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMY6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMZ6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM07', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM17', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM27', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM37', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM47', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM57', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM67', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM77', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM87', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM97', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMA7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMB7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMC7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMD7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BME7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMF7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMG7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMH7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMI7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMJ7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMK7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BML7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMM7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMN7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMO7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMP7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMQ7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMR7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMS7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMT7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMU7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMV7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMW7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMX7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMY7']}
2025-05-25 23:30:51,483 - INFO - 批量插入表单数据成功，批次 1，共 82 条记录
2025-05-25 23:30:51,483 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMP5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMQ5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMR5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMS5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMT5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMU5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMV5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMW5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMX5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMY5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMZ5', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM06', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM16', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM26', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM36', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM46', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM56', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM66', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM76', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM86', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BM96', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMA6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMB6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMC6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMD6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BME6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMF6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMG6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMH6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMI6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMJ6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMK6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BML6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMM6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMN6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMO6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMP6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMQ6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMR6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMS6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMT6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMU6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMV6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMW6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82D1MFT3BMX6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMY6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMZ6', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM07', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM17', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM27', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM37', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM47', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM57', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM67', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM77', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM87', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BM97', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMA7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMB7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMC7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMD7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BME7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMF7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMG7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMH7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMI7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMJ7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMK7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BML7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMM7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMN7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMO7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMP7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMQ7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMR7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMS7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMT7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMU7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMV7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMW7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMX7', 'FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMY7']
2025-05-25 23:30:56,499 - INFO - 批量插入完成，共 82 条记录
2025-05-25 23:30:56,499 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 82 条，错误: 0 条
2025-05-25 23:30:56,499 - INFO - 数据同步完成！更新: 0 条，插入: 82 条，错误: 1 条
2025-05-25 23:31:56,510 - INFO - 开始同步昨天与今天的销售数据: 2025-05-24 至 2025-05-25
2025-05-25 23:31:56,510 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-25 23:31:56,510 - INFO - 查询参数: ('2025-05-24', '2025-05-25')
2025-05-25 23:31:56,588 - INFO - MySQL查询成功，时间段: 2025-05-24 至 2025-05-25，共获取 626 条记录
2025-05-25 23:31:56,588 - INFO - 获取到 2 个日期需要处理: ['2025-05-24', '2025-05-25']
2025-05-25 23:31:56,588 - INFO - 开始处理日期: 2025-05-24
2025-05-25 23:31:56,588 - INFO - Request Parameters - Page 1:
2025-05-25 23:31:56,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 23:31:56,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 23:32:04,712 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-0A68-7CB9-A2E3-00BC2F3796B8 Response: {'code': 'ServiceUnavailable', 'requestid': '********-0A68-7CB9-A2E3-00BC2F3796B8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-0A68-7CB9-A2E3-00BC2F3796B8)
2025-05-25 23:32:04,712 - INFO - 开始处理日期: 2025-05-25
2025-05-25 23:32:04,712 - INFO - Request Parameters - Page 1:
2025-05-25 23:32:04,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 23:32:04,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 23:32:05,493 - INFO - Response - Page 1:
2025-05-25 23:32:05,493 - INFO - 第 1 页获取到 96 条记录
2025-05-25 23:32:05,696 - INFO - 查询完成，共获取到 96 条记录
2025-05-25 23:32:05,696 - INFO - 获取到 96 条表单数据
2025-05-25 23:32:05,696 - INFO - 当前日期 2025-05-25 有 96 条MySQL数据需要处理
2025-05-25 23:32:05,696 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 23:32:05,696 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-25 23:32:05,696 - INFO - 同步完成
