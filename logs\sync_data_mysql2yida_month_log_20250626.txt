2025-06-26 00:00:03,251 - INFO - =================使用默认全量同步=============
2025-06-26 00:00:05,080 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-26 00:00:05,081 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-26 00:00:05,115 - INFO - 开始处理日期: 2025-01
2025-06-26 00:00:05,119 - INFO - Request Parameters - Page 1:
2025-06-26 00:00:05,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:05,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:06,375 - INFO - Response - Page 1:
2025-06-26 00:00:06,579 - INFO - 第 1 页获取到 100 条记录
2025-06-26 00:00:06,579 - INFO - Request Parameters - Page 2:
2025-06-26 00:00:06,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:06,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:07,766 - INFO - Response - Page 2:
2025-06-26 00:00:07,969 - INFO - 第 2 页获取到 100 条记录
2025-06-26 00:00:07,969 - INFO - Request Parameters - Page 3:
2025-06-26 00:00:07,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:07,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:08,485 - INFO - Response - Page 3:
2025-06-26 00:00:08,688 - INFO - 第 3 页获取到 100 条记录
2025-06-26 00:00:08,688 - INFO - Request Parameters - Page 4:
2025-06-26 00:00:08,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:08,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:09,235 - INFO - Response - Page 4:
2025-06-26 00:00:09,438 - INFO - 第 4 页获取到 100 条记录
2025-06-26 00:00:09,438 - INFO - Request Parameters - Page 5:
2025-06-26 00:00:09,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:09,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:09,969 - INFO - Response - Page 5:
2025-06-26 00:00:10,172 - INFO - 第 5 页获取到 100 条记录
2025-06-26 00:00:10,172 - INFO - Request Parameters - Page 6:
2025-06-26 00:00:10,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:10,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:10,672 - INFO - Response - Page 6:
2025-06-26 00:00:10,875 - INFO - 第 6 页获取到 100 条记录
2025-06-26 00:00:10,875 - INFO - Request Parameters - Page 7:
2025-06-26 00:00:10,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:10,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:11,375 - INFO - Response - Page 7:
2025-06-26 00:00:11,578 - INFO - 第 7 页获取到 82 条记录
2025-06-26 00:00:11,578 - INFO - 查询完成，共获取到 682 条记录
2025-06-26 00:00:11,578 - INFO - 获取到 682 条表单数据
2025-06-26 00:00:11,578 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-26 00:00:11,594 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 00:00:11,594 - INFO - 开始处理日期: 2025-02
2025-06-26 00:00:11,594 - INFO - Request Parameters - Page 1:
2025-06-26 00:00:11,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:11,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:12,594 - INFO - Response - Page 1:
2025-06-26 00:00:12,797 - INFO - 第 1 页获取到 100 条记录
2025-06-26 00:00:12,797 - INFO - Request Parameters - Page 2:
2025-06-26 00:00:12,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:12,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:13,797 - INFO - Response - Page 2:
2025-06-26 00:00:14,000 - INFO - 第 2 页获取到 100 条记录
2025-06-26 00:00:14,000 - INFO - Request Parameters - Page 3:
2025-06-26 00:00:14,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:14,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:14,500 - INFO - Response - Page 3:
2025-06-26 00:00:14,703 - INFO - 第 3 页获取到 100 条记录
2025-06-26 00:00:14,703 - INFO - Request Parameters - Page 4:
2025-06-26 00:00:14,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:14,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:15,188 - INFO - Response - Page 4:
2025-06-26 00:00:15,391 - INFO - 第 4 页获取到 100 条记录
2025-06-26 00:00:15,391 - INFO - Request Parameters - Page 5:
2025-06-26 00:00:15,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:15,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:15,938 - INFO - Response - Page 5:
2025-06-26 00:00:16,141 - INFO - 第 5 页获取到 100 条记录
2025-06-26 00:00:16,141 - INFO - Request Parameters - Page 6:
2025-06-26 00:00:16,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:16,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:16,641 - INFO - Response - Page 6:
2025-06-26 00:00:16,844 - INFO - 第 6 页获取到 100 条记录
2025-06-26 00:00:16,844 - INFO - Request Parameters - Page 7:
2025-06-26 00:00:16,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:16,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:17,344 - INFO - Response - Page 7:
2025-06-26 00:00:17,547 - INFO - 第 7 页获取到 70 条记录
2025-06-26 00:00:17,547 - INFO - 查询完成，共获取到 670 条记录
2025-06-26 00:00:17,547 - INFO - 获取到 670 条表单数据
2025-06-26 00:00:17,547 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-26 00:00:17,563 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 00:00:17,563 - INFO - 开始处理日期: 2025-03
2025-06-26 00:00:17,563 - INFO - Request Parameters - Page 1:
2025-06-26 00:00:17,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:17,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:18,047 - INFO - Response - Page 1:
2025-06-26 00:00:18,250 - INFO - 第 1 页获取到 100 条记录
2025-06-26 00:00:18,250 - INFO - Request Parameters - Page 2:
2025-06-26 00:00:18,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:18,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:18,782 - INFO - Response - Page 2:
2025-06-26 00:00:18,985 - INFO - 第 2 页获取到 100 条记录
2025-06-26 00:00:18,985 - INFO - Request Parameters - Page 3:
2025-06-26 00:00:18,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:18,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:19,438 - INFO - Response - Page 3:
2025-06-26 00:00:19,641 - INFO - 第 3 页获取到 100 条记录
2025-06-26 00:00:19,641 - INFO - Request Parameters - Page 4:
2025-06-26 00:00:19,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:19,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:20,219 - INFO - Response - Page 4:
2025-06-26 00:00:20,422 - INFO - 第 4 页获取到 100 条记录
2025-06-26 00:00:20,422 - INFO - Request Parameters - Page 5:
2025-06-26 00:00:20,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:20,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:20,938 - INFO - Response - Page 5:
2025-06-26 00:00:21,141 - INFO - 第 5 页获取到 100 条记录
2025-06-26 00:00:21,141 - INFO - Request Parameters - Page 6:
2025-06-26 00:00:21,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:21,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:21,625 - INFO - Response - Page 6:
2025-06-26 00:00:21,828 - INFO - 第 6 页获取到 100 条记录
2025-06-26 00:00:21,828 - INFO - Request Parameters - Page 7:
2025-06-26 00:00:21,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:21,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:22,328 - INFO - Response - Page 7:
2025-06-26 00:00:22,532 - INFO - 第 7 页获取到 61 条记录
2025-06-26 00:00:22,532 - INFO - 查询完成，共获取到 661 条记录
2025-06-26 00:00:22,532 - INFO - 获取到 661 条表单数据
2025-06-26 00:00:22,532 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-26 00:00:22,547 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 00:00:22,547 - INFO - 开始处理日期: 2025-04
2025-06-26 00:00:22,547 - INFO - Request Parameters - Page 1:
2025-06-26 00:00:22,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:22,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:23,078 - INFO - Response - Page 1:
2025-06-26 00:00:23,282 - INFO - 第 1 页获取到 100 条记录
2025-06-26 00:00:23,282 - INFO - Request Parameters - Page 2:
2025-06-26 00:00:23,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:23,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:23,828 - INFO - Response - Page 2:
2025-06-26 00:00:24,032 - INFO - 第 2 页获取到 100 条记录
2025-06-26 00:00:24,032 - INFO - Request Parameters - Page 3:
2025-06-26 00:00:24,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:24,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:24,500 - INFO - Response - Page 3:
2025-06-26 00:00:24,703 - INFO - 第 3 页获取到 100 条记录
2025-06-26 00:00:24,703 - INFO - Request Parameters - Page 4:
2025-06-26 00:00:24,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:24,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:25,188 - INFO - Response - Page 4:
2025-06-26 00:00:25,391 - INFO - 第 4 页获取到 100 条记录
2025-06-26 00:00:25,391 - INFO - Request Parameters - Page 5:
2025-06-26 00:00:25,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:25,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:25,844 - INFO - Response - Page 5:
2025-06-26 00:00:26,047 - INFO - 第 5 页获取到 100 条记录
2025-06-26 00:00:26,047 - INFO - Request Parameters - Page 6:
2025-06-26 00:00:26,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:26,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:26,625 - INFO - Response - Page 6:
2025-06-26 00:00:26,828 - INFO - 第 6 页获取到 100 条记录
2025-06-26 00:00:26,828 - INFO - Request Parameters - Page 7:
2025-06-26 00:00:26,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:26,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:27,282 - INFO - Response - Page 7:
2025-06-26 00:00:27,485 - INFO - 第 7 页获取到 56 条记录
2025-06-26 00:00:27,485 - INFO - 查询完成，共获取到 656 条记录
2025-06-26 00:00:27,485 - INFO - 获取到 656 条表单数据
2025-06-26 00:00:27,485 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-26 00:00:27,500 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 00:00:27,500 - INFO - 开始处理日期: 2025-05
2025-06-26 00:00:27,500 - INFO - Request Parameters - Page 1:
2025-06-26 00:00:27,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:27,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:28,485 - INFO - Response - Page 1:
2025-06-26 00:00:28,688 - INFO - 第 1 页获取到 100 条记录
2025-06-26 00:00:28,688 - INFO - Request Parameters - Page 2:
2025-06-26 00:00:28,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:28,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:29,172 - INFO - Response - Page 2:
2025-06-26 00:00:29,375 - INFO - 第 2 页获取到 100 条记录
2025-06-26 00:00:29,375 - INFO - Request Parameters - Page 3:
2025-06-26 00:00:29,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:29,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:29,891 - INFO - Response - Page 3:
2025-06-26 00:00:30,094 - INFO - 第 3 页获取到 100 条记录
2025-06-26 00:00:30,094 - INFO - Request Parameters - Page 4:
2025-06-26 00:00:30,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:30,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:30,672 - INFO - Response - Page 4:
2025-06-26 00:00:30,875 - INFO - 第 4 页获取到 100 条记录
2025-06-26 00:00:30,875 - INFO - Request Parameters - Page 5:
2025-06-26 00:00:30,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:30,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:31,344 - INFO - Response - Page 5:
2025-06-26 00:00:31,547 - INFO - 第 5 页获取到 100 条记录
2025-06-26 00:00:31,547 - INFO - Request Parameters - Page 6:
2025-06-26 00:00:31,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:31,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:32,016 - INFO - Response - Page 6:
2025-06-26 00:00:32,219 - INFO - 第 6 页获取到 100 条记录
2025-06-26 00:00:32,219 - INFO - Request Parameters - Page 7:
2025-06-26 00:00:32,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:32,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:32,938 - INFO - Response - Page 7:
2025-06-26 00:00:33,141 - INFO - 第 7 页获取到 65 条记录
2025-06-26 00:00:33,141 - INFO - 查询完成，共获取到 665 条记录
2025-06-26 00:00:33,141 - INFO - 获取到 665 条表单数据
2025-06-26 00:00:33,141 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-26 00:00:33,156 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 00:00:33,156 - INFO - 开始处理日期: 2025-06
2025-06-26 00:00:33,156 - INFO - Request Parameters - Page 1:
2025-06-26 00:00:33,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:33,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:33,656 - INFO - Response - Page 1:
2025-06-26 00:00:33,860 - INFO - 第 1 页获取到 100 条记录
2025-06-26 00:00:33,860 - INFO - Request Parameters - Page 2:
2025-06-26 00:00:33,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:33,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:34,453 - INFO - Response - Page 2:
2025-06-26 00:00:34,656 - INFO - 第 2 页获取到 100 条记录
2025-06-26 00:00:34,656 - INFO - Request Parameters - Page 3:
2025-06-26 00:00:34,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:34,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:35,235 - INFO - Response - Page 3:
2025-06-26 00:00:35,438 - INFO - 第 3 页获取到 100 条记录
2025-06-26 00:00:35,438 - INFO - Request Parameters - Page 4:
2025-06-26 00:00:35,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:35,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:35,953 - INFO - Response - Page 4:
2025-06-26 00:00:36,156 - INFO - 第 4 页获取到 100 条记录
2025-06-26 00:00:36,156 - INFO - Request Parameters - Page 5:
2025-06-26 00:00:36,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:36,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:36,610 - INFO - Response - Page 5:
2025-06-26 00:00:36,813 - INFO - 第 5 页获取到 100 条记录
2025-06-26 00:00:36,813 - INFO - Request Parameters - Page 6:
2025-06-26 00:00:36,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:36,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:37,266 - INFO - Response - Page 6:
2025-06-26 00:00:37,469 - INFO - 第 6 页获取到 100 条记录
2025-06-26 00:00:37,469 - INFO - Request Parameters - Page 7:
2025-06-26 00:00:37,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 00:00:37,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 00:00:37,828 - INFO - Response - Page 7:
2025-06-26 00:00:38,047 - INFO - 第 7 页获取到 26 条记录
2025-06-26 00:00:38,047 - INFO - 查询完成，共获取到 626 条记录
2025-06-26 00:00:38,047 - INFO - 获取到 626 条表单数据
2025-06-26 00:00:38,047 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-26 00:00:38,047 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-26 00:00:38,500 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-26 00:00:38,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79127.0, 'new_value': 82237.0}, {'field': 'offline_amount', 'old_value': 95112.0, 'new_value': 100021.0}, {'field': 'total_amount', 'old_value': 174239.0, 'new_value': 182258.0}, {'field': 'order_count', 'old_value': 3708, 'new_value': 3852}]
2025-06-26 00:00:38,516 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-26 00:00:38,922 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-26 00:00:38,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 810946.0, 'new_value': 837439.0}, {'field': 'total_amount', 'old_value': 810946.0, 'new_value': 837439.0}, {'field': 'order_count', 'old_value': 166, 'new_value': 172}]
2025-06-26 00:00:38,922 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-26 00:00:39,391 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-26 00:00:39,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1375327.11, 'new_value': 1435867.0}, {'field': 'total_amount', 'old_value': 1375327.11, 'new_value': 1435867.0}, {'field': 'order_count', 'old_value': 15190, 'new_value': 15955}]
2025-06-26 00:00:39,391 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-26 00:00:39,875 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-26 00:00:39,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58638.58, 'new_value': 64249.91}, {'field': 'offline_amount', 'old_value': 89196.56, 'new_value': 89264.56}, {'field': 'total_amount', 'old_value': 147835.14, 'new_value': 153514.47}, {'field': 'order_count', 'old_value': 4991, 'new_value': 5183}]
2025-06-26 00:00:39,875 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-26 00:00:40,266 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-26 00:00:40,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 577962.0, 'new_value': 597791.0}, {'field': 'total_amount', 'old_value': 602969.0, 'new_value': 622798.0}, {'field': 'order_count', 'old_value': 185, 'new_value': 192}]
2025-06-26 00:00:40,266 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-26 00:00:40,703 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-26 00:00:40,703 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42261.72, 'new_value': 44833.91}, {'field': 'offline_amount', 'old_value': 648753.32, 'new_value': 674847.22}, {'field': 'total_amount', 'old_value': 691015.04, 'new_value': 719681.13}, {'field': 'order_count', 'old_value': 2792, 'new_value': 2907}]
2025-06-26 00:00:40,703 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-26 00:00:41,125 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-26 00:00:41,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 459629.0, 'new_value': 470627.0}, {'field': 'total_amount', 'old_value': 463545.0, 'new_value': 474543.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 74}]
2025-06-26 00:00:41,125 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-26 00:00:41,594 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-26 00:00:41,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192110.5, 'new_value': 192987.5}, {'field': 'total_amount', 'old_value': 192110.5, 'new_value': 192987.5}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-06-26 00:00:41,594 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-26 00:00:42,078 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-26 00:00:42,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31370.0, 'new_value': 31817.0}, {'field': 'total_amount', 'old_value': 31847.0, 'new_value': 32294.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 73}]
2025-06-26 00:00:42,078 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-26 00:00:42,516 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-26 00:00:42,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339381.33, 'new_value': 347380.49}, {'field': 'total_amount', 'old_value': 339381.33, 'new_value': 347380.49}, {'field': 'order_count', 'old_value': 1707, 'new_value': 1757}]
2025-06-26 00:00:42,516 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-26 00:00:42,969 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-26 00:00:42,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80811.0, 'new_value': 102265.0}, {'field': 'total_amount', 'old_value': 85309.05, 'new_value': 106763.05}, {'field': 'order_count', 'old_value': 5607, 'new_value': 5612}]
2025-06-26 00:00:42,969 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-26 00:00:43,438 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-26 00:00:43,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145361.0, 'new_value': 153561.0}, {'field': 'total_amount', 'old_value': 206052.55, 'new_value': 214252.55}, {'field': 'order_count', 'old_value': 90, 'new_value': 92}]
2025-06-26 00:00:43,438 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-26 00:00:43,860 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-26 00:00:43,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57575.0, 'new_value': 62405.0}, {'field': 'total_amount', 'old_value': 57575.0, 'new_value': 62405.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 164}]
2025-06-26 00:00:43,860 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-26 00:00:44,313 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-26 00:00:44,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53730.7, 'new_value': 57007.36}, {'field': 'offline_amount', 'old_value': 436422.29, 'new_value': 448670.87}, {'field': 'total_amount', 'old_value': 490152.99, 'new_value': 505678.23}, {'field': 'order_count', 'old_value': 4536, 'new_value': 4803}]
2025-06-26 00:00:44,313 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-26 00:00:44,703 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-26 00:00:44,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240549.53, 'new_value': 247147.53}, {'field': 'total_amount', 'old_value': 240549.53, 'new_value': 247147.53}, {'field': 'order_count', 'old_value': 145, 'new_value': 147}]
2025-06-26 00:00:44,703 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-26 00:00:45,203 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-26 00:00:45,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1466.37, 'new_value': 2066.37}, {'field': 'offline_amount', 'old_value': 17527.12, 'new_value': 18179.3}, {'field': 'total_amount', 'old_value': 18993.49, 'new_value': 20245.67}, {'field': 'order_count', 'old_value': 377, 'new_value': 396}]
2025-06-26 00:00:45,203 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-26 00:00:45,625 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-26 00:00:45,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54589.0, 'new_value': 56468.0}, {'field': 'total_amount', 'old_value': 54589.0, 'new_value': 56468.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 90}]
2025-06-26 00:00:45,625 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-26 00:00:46,094 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-26 00:00:46,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49812.26, 'new_value': 52674.25}, {'field': 'offline_amount', 'old_value': 303695.9, 'new_value': 311941.6}, {'field': 'total_amount', 'old_value': 353508.16, 'new_value': 364615.85}, {'field': 'order_count', 'old_value': 2724, 'new_value': 2854}]
2025-06-26 00:00:46,094 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-26 00:00:46,485 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-26 00:00:46,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 275108.5, 'new_value': 280778.9}, {'field': 'total_amount', 'old_value': 275108.5, 'new_value': 280778.9}, {'field': 'order_count', 'old_value': 2795, 'new_value': 2851}]
2025-06-26 00:00:46,485 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-26 00:00:46,953 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-26 00:00:46,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60105.5, 'new_value': 60164.5}, {'field': 'total_amount', 'old_value': 66730.9, 'new_value': 66789.9}, {'field': 'order_count', 'old_value': 162, 'new_value': 163}]
2025-06-26 00:00:46,953 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-26 00:00:47,453 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-26 00:00:47,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4346100.0, 'new_value': 4746000.0}, {'field': 'total_amount', 'old_value': 4346100.0, 'new_value': 4746000.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-26 00:00:47,453 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-26 00:00:47,891 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-26 00:00:47,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 790115.0, 'new_value': 799332.0}, {'field': 'total_amount', 'old_value': 862707.0, 'new_value': 871924.0}, {'field': 'order_count', 'old_value': 919, 'new_value': 934}]
2025-06-26 00:00:47,891 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-26 00:00:48,391 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-26 00:00:48,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60811.0, 'new_value': 73472.0}, {'field': 'total_amount', 'old_value': 60811.0, 'new_value': 73472.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 26}]
2025-06-26 00:00:48,391 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-26 00:00:48,797 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-26 00:00:48,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98103.11, 'new_value': 101628.9}, {'field': 'total_amount', 'old_value': 98103.11, 'new_value': 101628.9}, {'field': 'order_count', 'old_value': 8508, 'new_value': 8862}]
2025-06-26 00:00:48,797 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-26 00:00:49,328 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-26 00:00:49,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368697.44, 'new_value': 385387.46}, {'field': 'total_amount', 'old_value': 447906.53, 'new_value': 464596.55}, {'field': 'order_count', 'old_value': 1440, 'new_value': 1488}]
2025-06-26 00:00:49,328 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRR
2025-06-26 00:00:49,828 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRR
2025-06-26 00:00:49,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120707.0, 'new_value': 137586.0}, {'field': 'total_amount', 'old_value': 120708.0, 'new_value': 137587.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-06-26 00:00:49,828 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-26 00:00:50,203 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-26 00:00:50,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347976.0, 'new_value': 376278.0}, {'field': 'total_amount', 'old_value': 357776.0, 'new_value': 386078.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 93}]
2025-06-26 00:00:50,203 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-26 00:00:50,656 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-26 00:00:50,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24671.34, 'new_value': 25359.64}, {'field': 'offline_amount', 'old_value': 239184.04, 'new_value': 244311.19}, {'field': 'total_amount', 'old_value': 263855.38, 'new_value': 269670.83}, {'field': 'order_count', 'old_value': 1270, 'new_value': 1304}]
2025-06-26 00:00:50,672 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-26 00:00:51,125 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-26 00:00:51,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142806.81, 'new_value': 144111.81}, {'field': 'total_amount', 'old_value': 142806.81, 'new_value': 144111.81}, {'field': 'order_count', 'old_value': 317, 'new_value': 323}]
2025-06-26 00:00:51,125 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-26 00:00:51,563 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-26 00:00:51,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 260320.0, 'new_value': 268248.33}, {'field': 'offline_amount', 'old_value': 71921.22, 'new_value': 74055.51}, {'field': 'total_amount', 'old_value': 332241.22, 'new_value': 342303.84}, {'field': 'order_count', 'old_value': 2095, 'new_value': 2149}]
2025-06-26 00:00:51,563 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-26 00:00:51,984 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-26 00:00:51,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120949.0, 'new_value': 122890.0}, {'field': 'total_amount', 'old_value': 125516.0, 'new_value': 127457.0}, {'field': 'order_count', 'old_value': 4342, 'new_value': 4347}]
2025-06-26 00:00:51,984 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-26 00:00:52,422 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-26 00:00:52,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40659.0, 'new_value': 42491.0}, {'field': 'offline_amount', 'old_value': 129233.07, 'new_value': 134031.07}, {'field': 'total_amount', 'old_value': 169892.07, 'new_value': 176522.07}, {'field': 'order_count', 'old_value': 248, 'new_value': 258}]
2025-06-26 00:00:52,422 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-26 00:00:52,828 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-26 00:00:52,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600035.0, 'new_value': 621730.0}, {'field': 'total_amount', 'old_value': 600035.0, 'new_value': 621730.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 61}]
2025-06-26 00:00:52,828 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-26 00:00:53,281 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-26 00:00:53,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107846.02, 'new_value': 112214.82}, {'field': 'offline_amount', 'old_value': 215531.37, 'new_value': 220535.36}, {'field': 'total_amount', 'old_value': 323377.39, 'new_value': 332750.18}, {'field': 'order_count', 'old_value': 4571, 'new_value': 4664}]
2025-06-26 00:00:53,281 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-26 00:00:53,750 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-26 00:00:53,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 478513.0, 'new_value': 498306.0}, {'field': 'total_amount', 'old_value': 478513.0, 'new_value': 498306.0}, {'field': 'order_count', 'old_value': 112, 'new_value': 114}]
2025-06-26 00:00:53,750 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-26 00:00:54,281 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-26 00:00:54,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96315.7, 'new_value': 99195.7}, {'field': 'offline_amount', 'old_value': 55971.6, 'new_value': 56802.9}, {'field': 'total_amount', 'old_value': 152287.3, 'new_value': 155998.6}, {'field': 'order_count', 'old_value': 1001, 'new_value': 1029}]
2025-06-26 00:00:54,281 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-26 00:00:54,750 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-26 00:00:54,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23229.63, 'new_value': 23427.63}, {'field': 'total_amount', 'old_value': 23294.23, 'new_value': 23492.23}, {'field': 'order_count', 'old_value': 136, 'new_value': 145}]
2025-06-26 00:00:54,750 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-26 00:00:55,172 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-26 00:00:55,172 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 247003.24, 'new_value': 254195.39}, {'field': 'offline_amount', 'old_value': 652938.21, 'new_value': 676150.82}, {'field': 'total_amount', 'old_value': 899941.45, 'new_value': 930346.21}, {'field': 'order_count', 'old_value': 5662, 'new_value': 5859}]
2025-06-26 00:00:55,172 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-26 00:00:55,594 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-26 00:00:55,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 725898.34, 'new_value': 756707.46}, {'field': 'total_amount', 'old_value': 725898.34, 'new_value': 756707.46}, {'field': 'order_count', 'old_value': 4876, 'new_value': 5087}]
2025-06-26 00:00:55,594 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-26 00:00:55,969 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-26 00:00:55,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36801.51, 'new_value': 38402.67}, {'field': 'offline_amount', 'old_value': 40431.45, 'new_value': 41557.53}, {'field': 'total_amount', 'old_value': 77232.96, 'new_value': 79960.2}, {'field': 'order_count', 'old_value': 6621, 'new_value': 6859}]
2025-06-26 00:00:55,969 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-26 00:00:56,438 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-26 00:00:56,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99064.0, 'new_value': 100814.3}, {'field': 'total_amount', 'old_value': 99064.0, 'new_value': 100814.3}, {'field': 'order_count', 'old_value': 208, 'new_value': 213}]
2025-06-26 00:00:56,438 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-26 00:00:56,813 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-26 00:00:56,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 269142.02, 'new_value': 277386.39}, {'field': 'offline_amount', 'old_value': 1314601.64, 'new_value': 1359475.08}, {'field': 'total_amount', 'old_value': 1583743.66, 'new_value': 1636861.47}, {'field': 'order_count', 'old_value': 7584, 'new_value': 7852}]
2025-06-26 00:00:56,813 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-26 00:00:57,250 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-26 00:00:57,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 631088.0, 'new_value': 639945.0}, {'field': 'total_amount', 'old_value': 631088.0, 'new_value': 639945.0}, {'field': 'order_count', 'old_value': 428, 'new_value': 434}]
2025-06-26 00:00:57,250 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-26 00:00:57,703 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-26 00:00:57,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28046.0, 'new_value': 29790.0}, {'field': 'total_amount', 'old_value': 28046.0, 'new_value': 29790.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-06-26 00:00:57,703 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-26 00:00:58,125 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-26 00:00:58,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1437322.0, 'new_value': 1476074.0}, {'field': 'total_amount', 'old_value': 1437322.0, 'new_value': 1476074.0}, {'field': 'order_count', 'old_value': 6672, 'new_value': 6852}]
2025-06-26 00:00:58,125 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-26 00:00:58,563 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-26 00:00:58,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260868.17, 'new_value': 270263.17}, {'field': 'total_amount', 'old_value': 274863.17, 'new_value': 284258.17}, {'field': 'order_count', 'old_value': 66, 'new_value': 70}]
2025-06-26 00:00:58,563 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-26 00:00:59,016 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-26 00:00:59,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10400000.0, 'new_value': 10460000.0}, {'field': 'total_amount', 'old_value': 10400000.0, 'new_value': 10460000.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-06-26 00:00:59,016 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-26 00:00:59,531 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-26 00:00:59,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217122.0, 'new_value': 227919.0}, {'field': 'total_amount', 'old_value': 229709.0, 'new_value': 240506.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 106}]
2025-06-26 00:00:59,531 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-26 00:01:00,016 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-26 00:01:00,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79433.0, 'new_value': 82438.0}, {'field': 'offline_amount', 'old_value': 157673.0, 'new_value': 165409.0}, {'field': 'total_amount', 'old_value': 237106.0, 'new_value': 247847.0}, {'field': 'order_count', 'old_value': 4714, 'new_value': 4940}]
2025-06-26 00:01:00,016 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-26 00:01:00,547 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-26 00:01:00,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53576.4, 'new_value': 54779.6}, {'field': 'total_amount', 'old_value': 53576.4, 'new_value': 54779.6}, {'field': 'order_count', 'old_value': 623, 'new_value': 651}]
2025-06-26 00:01:00,547 - INFO - 日期 2025-06 处理完成 - 更新: 50 条，插入: 0 条，错误: 0 条
2025-06-26 00:01:00,547 - INFO - 数据同步完成！更新: 50 条，插入: 0 条，错误: 0 条
2025-06-26 00:01:00,547 - INFO - =================同步完成====================
2025-06-26 03:00:03,090 - INFO - =================使用默认全量同步=============
2025-06-26 03:00:04,824 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-26 03:00:04,824 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-26 03:00:04,856 - INFO - 开始处理日期: 2025-01
2025-06-26 03:00:04,856 - INFO - Request Parameters - Page 1:
2025-06-26 03:00:04,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:04,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:05,965 - INFO - Response - Page 1:
2025-06-26 03:00:06,168 - INFO - 第 1 页获取到 100 条记录
2025-06-26 03:00:06,168 - INFO - Request Parameters - Page 2:
2025-06-26 03:00:06,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:06,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:06,731 - INFO - Response - Page 2:
2025-06-26 03:00:06,934 - INFO - 第 2 页获取到 100 条记录
2025-06-26 03:00:06,934 - INFO - Request Parameters - Page 3:
2025-06-26 03:00:06,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:06,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:07,871 - INFO - Response - Page 3:
2025-06-26 03:00:08,074 - INFO - 第 3 页获取到 100 条记录
2025-06-26 03:00:08,074 - INFO - Request Parameters - Page 4:
2025-06-26 03:00:08,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:08,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:08,606 - INFO - Response - Page 4:
2025-06-26 03:00:08,809 - INFO - 第 4 页获取到 100 条记录
2025-06-26 03:00:08,809 - INFO - Request Parameters - Page 5:
2025-06-26 03:00:08,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:08,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:09,403 - INFO - Response - Page 5:
2025-06-26 03:00:09,606 - INFO - 第 5 页获取到 100 条记录
2025-06-26 03:00:09,606 - INFO - Request Parameters - Page 6:
2025-06-26 03:00:09,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:09,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:10,043 - INFO - Response - Page 6:
2025-06-26 03:00:10,246 - INFO - 第 6 页获取到 100 条记录
2025-06-26 03:00:10,246 - INFO - Request Parameters - Page 7:
2025-06-26 03:00:10,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:10,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:10,762 - INFO - Response - Page 7:
2025-06-26 03:00:10,965 - INFO - 第 7 页获取到 82 条记录
2025-06-26 03:00:10,965 - INFO - 查询完成，共获取到 682 条记录
2025-06-26 03:00:10,965 - INFO - 获取到 682 条表单数据
2025-06-26 03:00:10,965 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-26 03:00:10,981 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 03:00:10,981 - INFO - 开始处理日期: 2025-02
2025-06-26 03:00:10,981 - INFO - Request Parameters - Page 1:
2025-06-26 03:00:10,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:10,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:11,465 - INFO - Response - Page 1:
2025-06-26 03:00:11,668 - INFO - 第 1 页获取到 100 条记录
2025-06-26 03:00:11,668 - INFO - Request Parameters - Page 2:
2025-06-26 03:00:11,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:11,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:12,199 - INFO - Response - Page 2:
2025-06-26 03:00:12,403 - INFO - 第 2 页获取到 100 条记录
2025-06-26 03:00:12,403 - INFO - Request Parameters - Page 3:
2025-06-26 03:00:12,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:12,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:12,856 - INFO - Response - Page 3:
2025-06-26 03:00:13,059 - INFO - 第 3 页获取到 100 条记录
2025-06-26 03:00:13,059 - INFO - Request Parameters - Page 4:
2025-06-26 03:00:13,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:13,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:13,543 - INFO - Response - Page 4:
2025-06-26 03:00:13,746 - INFO - 第 4 页获取到 100 条记录
2025-06-26 03:00:13,746 - INFO - Request Parameters - Page 5:
2025-06-26 03:00:13,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:13,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:14,246 - INFO - Response - Page 5:
2025-06-26 03:00:14,449 - INFO - 第 5 页获取到 100 条记录
2025-06-26 03:00:14,449 - INFO - Request Parameters - Page 6:
2025-06-26 03:00:14,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:14,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:14,981 - INFO - Response - Page 6:
2025-06-26 03:00:15,184 - INFO - 第 6 页获取到 100 条记录
2025-06-26 03:00:15,184 - INFO - Request Parameters - Page 7:
2025-06-26 03:00:15,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:15,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:15,668 - INFO - Response - Page 7:
2025-06-26 03:00:15,871 - INFO - 第 7 页获取到 70 条记录
2025-06-26 03:00:15,871 - INFO - 查询完成，共获取到 670 条记录
2025-06-26 03:00:15,871 - INFO - 获取到 670 条表单数据
2025-06-26 03:00:15,871 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-26 03:00:15,887 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 03:00:15,887 - INFO - 开始处理日期: 2025-03
2025-06-26 03:00:15,887 - INFO - Request Parameters - Page 1:
2025-06-26 03:00:15,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:15,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:16,371 - INFO - Response - Page 1:
2025-06-26 03:00:16,574 - INFO - 第 1 页获取到 100 条记录
2025-06-26 03:00:16,574 - INFO - Request Parameters - Page 2:
2025-06-26 03:00:16,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:16,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:17,121 - INFO - Response - Page 2:
2025-06-26 03:00:17,324 - INFO - 第 2 页获取到 100 条记录
2025-06-26 03:00:17,324 - INFO - Request Parameters - Page 3:
2025-06-26 03:00:17,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:17,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:17,887 - INFO - Response - Page 3:
2025-06-26 03:00:18,090 - INFO - 第 3 页获取到 100 条记录
2025-06-26 03:00:18,090 - INFO - Request Parameters - Page 4:
2025-06-26 03:00:18,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:18,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:18,559 - INFO - Response - Page 4:
2025-06-26 03:00:18,762 - INFO - 第 4 页获取到 100 条记录
2025-06-26 03:00:18,762 - INFO - Request Parameters - Page 5:
2025-06-26 03:00:18,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:18,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:19,231 - INFO - Response - Page 5:
2025-06-26 03:00:19,434 - INFO - 第 5 页获取到 100 条记录
2025-06-26 03:00:19,434 - INFO - Request Parameters - Page 6:
2025-06-26 03:00:19,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:19,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:19,934 - INFO - Response - Page 6:
2025-06-26 03:00:20,137 - INFO - 第 6 页获取到 100 条记录
2025-06-26 03:00:20,137 - INFO - Request Parameters - Page 7:
2025-06-26 03:00:20,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:20,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:20,543 - INFO - Response - Page 7:
2025-06-26 03:00:20,746 - INFO - 第 7 页获取到 61 条记录
2025-06-26 03:00:20,746 - INFO - 查询完成，共获取到 661 条记录
2025-06-26 03:00:20,746 - INFO - 获取到 661 条表单数据
2025-06-26 03:00:20,746 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-26 03:00:20,762 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 03:00:20,762 - INFO - 开始处理日期: 2025-04
2025-06-26 03:00:20,762 - INFO - Request Parameters - Page 1:
2025-06-26 03:00:20,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:20,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:21,293 - INFO - Response - Page 1:
2025-06-26 03:00:21,496 - INFO - 第 1 页获取到 100 条记录
2025-06-26 03:00:21,496 - INFO - Request Parameters - Page 2:
2025-06-26 03:00:21,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:21,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:21,981 - INFO - Response - Page 2:
2025-06-26 03:00:22,184 - INFO - 第 2 页获取到 100 条记录
2025-06-26 03:00:22,184 - INFO - Request Parameters - Page 3:
2025-06-26 03:00:22,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:22,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:22,621 - INFO - Response - Page 3:
2025-06-26 03:00:22,824 - INFO - 第 3 页获取到 100 条记录
2025-06-26 03:00:22,824 - INFO - Request Parameters - Page 4:
2025-06-26 03:00:22,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:22,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:23,293 - INFO - Response - Page 4:
2025-06-26 03:00:23,496 - INFO - 第 4 页获取到 100 条记录
2025-06-26 03:00:23,496 - INFO - Request Parameters - Page 5:
2025-06-26 03:00:23,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:23,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:24,027 - INFO - Response - Page 5:
2025-06-26 03:00:24,231 - INFO - 第 5 页获取到 100 条记录
2025-06-26 03:00:24,231 - INFO - Request Parameters - Page 6:
2025-06-26 03:00:24,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:24,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:24,762 - INFO - Response - Page 6:
2025-06-26 03:00:24,965 - INFO - 第 6 页获取到 100 条记录
2025-06-26 03:00:24,965 - INFO - Request Parameters - Page 7:
2025-06-26 03:00:24,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:24,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:25,449 - INFO - Response - Page 7:
2025-06-26 03:00:25,652 - INFO - 第 7 页获取到 56 条记录
2025-06-26 03:00:25,652 - INFO - 查询完成，共获取到 656 条记录
2025-06-26 03:00:25,652 - INFO - 获取到 656 条表单数据
2025-06-26 03:00:25,652 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-26 03:00:25,668 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 03:00:25,668 - INFO - 开始处理日期: 2025-05
2025-06-26 03:00:25,668 - INFO - Request Parameters - Page 1:
2025-06-26 03:00:25,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:25,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:26,199 - INFO - Response - Page 1:
2025-06-26 03:00:26,402 - INFO - 第 1 页获取到 100 条记录
2025-06-26 03:00:26,402 - INFO - Request Parameters - Page 2:
2025-06-26 03:00:26,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:26,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:26,949 - INFO - Response - Page 2:
2025-06-26 03:00:27,152 - INFO - 第 2 页获取到 100 条记录
2025-06-26 03:00:27,152 - INFO - Request Parameters - Page 3:
2025-06-26 03:00:27,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:27,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:27,652 - INFO - Response - Page 3:
2025-06-26 03:00:27,856 - INFO - 第 3 页获取到 100 条记录
2025-06-26 03:00:27,856 - INFO - Request Parameters - Page 4:
2025-06-26 03:00:27,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:27,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:28,324 - INFO - Response - Page 4:
2025-06-26 03:00:28,527 - INFO - 第 4 页获取到 100 条记录
2025-06-26 03:00:28,527 - INFO - Request Parameters - Page 5:
2025-06-26 03:00:28,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:28,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:29,043 - INFO - Response - Page 5:
2025-06-26 03:00:29,246 - INFO - 第 5 页获取到 100 条记录
2025-06-26 03:00:29,246 - INFO - Request Parameters - Page 6:
2025-06-26 03:00:29,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:29,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:29,731 - INFO - Response - Page 6:
2025-06-26 03:00:29,934 - INFO - 第 6 页获取到 100 条记录
2025-06-26 03:00:29,934 - INFO - Request Parameters - Page 7:
2025-06-26 03:00:29,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:29,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:30,387 - INFO - Response - Page 7:
2025-06-26 03:00:30,590 - INFO - 第 7 页获取到 65 条记录
2025-06-26 03:00:30,590 - INFO - 查询完成，共获取到 665 条记录
2025-06-26 03:00:30,590 - INFO - 获取到 665 条表单数据
2025-06-26 03:00:30,590 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-26 03:00:30,606 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 03:00:30,606 - INFO - 开始处理日期: 2025-06
2025-06-26 03:00:30,606 - INFO - Request Parameters - Page 1:
2025-06-26 03:00:30,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:30,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:31,106 - INFO - Response - Page 1:
2025-06-26 03:00:31,309 - INFO - 第 1 页获取到 100 条记录
2025-06-26 03:00:31,309 - INFO - Request Parameters - Page 2:
2025-06-26 03:00:31,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:31,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:31,856 - INFO - Response - Page 2:
2025-06-26 03:00:32,059 - INFO - 第 2 页获取到 100 条记录
2025-06-26 03:00:32,059 - INFO - Request Parameters - Page 3:
2025-06-26 03:00:32,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:32,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:32,543 - INFO - Response - Page 3:
2025-06-26 03:00:32,746 - INFO - 第 3 页获取到 100 条记录
2025-06-26 03:00:32,746 - INFO - Request Parameters - Page 4:
2025-06-26 03:00:32,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:32,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:33,215 - INFO - Response - Page 4:
2025-06-26 03:00:33,418 - INFO - 第 4 页获取到 100 条记录
2025-06-26 03:00:33,418 - INFO - Request Parameters - Page 5:
2025-06-26 03:00:33,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:33,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:33,840 - INFO - Response - Page 5:
2025-06-26 03:00:34,043 - INFO - 第 5 页获取到 100 条记录
2025-06-26 03:00:34,043 - INFO - Request Parameters - Page 6:
2025-06-26 03:00:34,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:34,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:34,699 - INFO - Response - Page 6:
2025-06-26 03:00:34,902 - INFO - 第 6 页获取到 100 条记录
2025-06-26 03:00:34,902 - INFO - Request Parameters - Page 7:
2025-06-26 03:00:34,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 03:00:34,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 03:00:35,324 - INFO - Response - Page 7:
2025-06-26 03:00:35,527 - INFO - 第 7 页获取到 26 条记录
2025-06-26 03:00:35,527 - INFO - 查询完成，共获取到 626 条记录
2025-06-26 03:00:35,527 - INFO - 获取到 626 条表单数据
2025-06-26 03:00:35,527 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-26 03:00:35,543 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-26 03:00:35,965 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-26 03:00:35,965 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124269.0, 'new_value': 128838.1}, {'field': 'offline_amount', 'old_value': 94332.0, 'new_value': 96952.8}, {'field': 'total_amount', 'old_value': 218601.0, 'new_value': 225790.9}, {'field': 'order_count', 'old_value': 5293, 'new_value': 5467}]
2025-06-26 03:00:35,981 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-26 03:00:35,981 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-26 03:00:35,981 - INFO - =================同步完成====================
2025-06-26 06:00:03,231 - INFO - =================使用默认全量同步=============
2025-06-26 06:00:04,997 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-26 06:00:04,997 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-26 06:00:05,028 - INFO - 开始处理日期: 2025-01
2025-06-26 06:00:05,028 - INFO - Request Parameters - Page 1:
2025-06-26 06:00:05,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:05,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:06,669 - INFO - Response - Page 1:
2025-06-26 06:00:06,872 - INFO - 第 1 页获取到 100 条记录
2025-06-26 06:00:06,872 - INFO - Request Parameters - Page 2:
2025-06-26 06:00:06,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:06,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:07,388 - INFO - Response - Page 2:
2025-06-26 06:00:07,591 - INFO - 第 2 页获取到 100 条记录
2025-06-26 06:00:07,591 - INFO - Request Parameters - Page 3:
2025-06-26 06:00:07,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:07,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:08,372 - INFO - Response - Page 3:
2025-06-26 06:00:08,575 - INFO - 第 3 页获取到 100 条记录
2025-06-26 06:00:08,575 - INFO - Request Parameters - Page 4:
2025-06-26 06:00:08,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:08,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:09,122 - INFO - Response - Page 4:
2025-06-26 06:00:09,325 - INFO - 第 4 页获取到 100 条记录
2025-06-26 06:00:09,325 - INFO - Request Parameters - Page 5:
2025-06-26 06:00:09,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:09,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:09,841 - INFO - Response - Page 5:
2025-06-26 06:00:10,044 - INFO - 第 5 页获取到 100 条记录
2025-06-26 06:00:10,044 - INFO - Request Parameters - Page 6:
2025-06-26 06:00:10,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:10,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:10,544 - INFO - Response - Page 6:
2025-06-26 06:00:10,747 - INFO - 第 6 页获取到 100 条记录
2025-06-26 06:00:10,747 - INFO - Request Parameters - Page 7:
2025-06-26 06:00:10,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:10,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:11,278 - INFO - Response - Page 7:
2025-06-26 06:00:11,481 - INFO - 第 7 页获取到 82 条记录
2025-06-26 06:00:11,481 - INFO - 查询完成，共获取到 682 条记录
2025-06-26 06:00:11,481 - INFO - 获取到 682 条表单数据
2025-06-26 06:00:11,481 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-26 06:00:11,497 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 06:00:11,497 - INFO - 开始处理日期: 2025-02
2025-06-26 06:00:11,497 - INFO - Request Parameters - Page 1:
2025-06-26 06:00:11,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:11,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:12,028 - INFO - Response - Page 1:
2025-06-26 06:00:12,231 - INFO - 第 1 页获取到 100 条记录
2025-06-26 06:00:12,231 - INFO - Request Parameters - Page 2:
2025-06-26 06:00:12,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:12,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:12,716 - INFO - Response - Page 2:
2025-06-26 06:00:12,919 - INFO - 第 2 页获取到 100 条记录
2025-06-26 06:00:12,919 - INFO - Request Parameters - Page 3:
2025-06-26 06:00:12,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:12,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:13,450 - INFO - Response - Page 3:
2025-06-26 06:00:13,653 - INFO - 第 3 页获取到 100 条记录
2025-06-26 06:00:13,653 - INFO - Request Parameters - Page 4:
2025-06-26 06:00:13,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:13,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:14,169 - INFO - Response - Page 4:
2025-06-26 06:00:14,372 - INFO - 第 4 页获取到 100 条记录
2025-06-26 06:00:14,372 - INFO - Request Parameters - Page 5:
2025-06-26 06:00:14,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:14,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:14,934 - INFO - Response - Page 5:
2025-06-26 06:00:15,138 - INFO - 第 5 页获取到 100 条记录
2025-06-26 06:00:15,138 - INFO - Request Parameters - Page 6:
2025-06-26 06:00:15,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:15,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:15,622 - INFO - Response - Page 6:
2025-06-26 06:00:15,825 - INFO - 第 6 页获取到 100 条记录
2025-06-26 06:00:15,825 - INFO - Request Parameters - Page 7:
2025-06-26 06:00:15,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:15,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:16,294 - INFO - Response - Page 7:
2025-06-26 06:00:16,497 - INFO - 第 7 页获取到 70 条记录
2025-06-26 06:00:16,497 - INFO - 查询完成，共获取到 670 条记录
2025-06-26 06:00:16,497 - INFO - 获取到 670 条表单数据
2025-06-26 06:00:16,497 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-26 06:00:16,513 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 06:00:16,513 - INFO - 开始处理日期: 2025-03
2025-06-26 06:00:16,513 - INFO - Request Parameters - Page 1:
2025-06-26 06:00:16,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:16,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:16,997 - INFO - Response - Page 1:
2025-06-26 06:00:17,200 - INFO - 第 1 页获取到 100 条记录
2025-06-26 06:00:17,200 - INFO - Request Parameters - Page 2:
2025-06-26 06:00:17,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:17,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:17,669 - INFO - Response - Page 2:
2025-06-26 06:00:17,872 - INFO - 第 2 页获取到 100 条记录
2025-06-26 06:00:17,872 - INFO - Request Parameters - Page 3:
2025-06-26 06:00:17,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:17,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:18,403 - INFO - Response - Page 3:
2025-06-26 06:00:18,606 - INFO - 第 3 页获取到 100 条记录
2025-06-26 06:00:18,606 - INFO - Request Parameters - Page 4:
2025-06-26 06:00:18,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:18,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:19,122 - INFO - Response - Page 4:
2025-06-26 06:00:19,325 - INFO - 第 4 页获取到 100 条记录
2025-06-26 06:00:19,325 - INFO - Request Parameters - Page 5:
2025-06-26 06:00:19,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:19,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:19,794 - INFO - Response - Page 5:
2025-06-26 06:00:19,997 - INFO - 第 5 页获取到 100 条记录
2025-06-26 06:00:19,997 - INFO - Request Parameters - Page 6:
2025-06-26 06:00:19,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:19,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:20,466 - INFO - Response - Page 6:
2025-06-26 06:00:20,669 - INFO - 第 6 页获取到 100 条记录
2025-06-26 06:00:20,669 - INFO - Request Parameters - Page 7:
2025-06-26 06:00:20,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:20,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:21,138 - INFO - Response - Page 7:
2025-06-26 06:00:21,341 - INFO - 第 7 页获取到 61 条记录
2025-06-26 06:00:21,341 - INFO - 查询完成，共获取到 661 条记录
2025-06-26 06:00:21,341 - INFO - 获取到 661 条表单数据
2025-06-26 06:00:21,341 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-26 06:00:21,356 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 06:00:21,356 - INFO - 开始处理日期: 2025-04
2025-06-26 06:00:21,356 - INFO - Request Parameters - Page 1:
2025-06-26 06:00:21,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:21,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:21,903 - INFO - Response - Page 1:
2025-06-26 06:00:22,106 - INFO - 第 1 页获取到 100 条记录
2025-06-26 06:00:22,106 - INFO - Request Parameters - Page 2:
2025-06-26 06:00:22,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:22,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:22,684 - INFO - Response - Page 2:
2025-06-26 06:00:22,888 - INFO - 第 2 页获取到 100 条记录
2025-06-26 06:00:22,888 - INFO - Request Parameters - Page 3:
2025-06-26 06:00:22,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:22,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:23,388 - INFO - Response - Page 3:
2025-06-26 06:00:23,591 - INFO - 第 3 页获取到 100 条记录
2025-06-26 06:00:23,591 - INFO - Request Parameters - Page 4:
2025-06-26 06:00:23,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:23,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:24,091 - INFO - Response - Page 4:
2025-06-26 06:00:24,294 - INFO - 第 4 页获取到 100 条记录
2025-06-26 06:00:24,294 - INFO - Request Parameters - Page 5:
2025-06-26 06:00:24,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:24,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:24,794 - INFO - Response - Page 5:
2025-06-26 06:00:24,997 - INFO - 第 5 页获取到 100 条记录
2025-06-26 06:00:24,997 - INFO - Request Parameters - Page 6:
2025-06-26 06:00:24,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:24,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:25,497 - INFO - Response - Page 6:
2025-06-26 06:00:25,700 - INFO - 第 6 页获取到 100 条记录
2025-06-26 06:00:25,700 - INFO - Request Parameters - Page 7:
2025-06-26 06:00:25,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:25,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:26,184 - INFO - Response - Page 7:
2025-06-26 06:00:26,388 - INFO - 第 7 页获取到 56 条记录
2025-06-26 06:00:26,388 - INFO - 查询完成，共获取到 656 条记录
2025-06-26 06:00:26,388 - INFO - 获取到 656 条表单数据
2025-06-26 06:00:26,388 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-26 06:00:26,403 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 06:00:26,403 - INFO - 开始处理日期: 2025-05
2025-06-26 06:00:26,403 - INFO - Request Parameters - Page 1:
2025-06-26 06:00:26,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:26,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:26,919 - INFO - Response - Page 1:
2025-06-26 06:00:27,122 - INFO - 第 1 页获取到 100 条记录
2025-06-26 06:00:27,122 - INFO - Request Parameters - Page 2:
2025-06-26 06:00:27,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:27,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:27,575 - INFO - Response - Page 2:
2025-06-26 06:00:27,778 - INFO - 第 2 页获取到 100 条记录
2025-06-26 06:00:27,778 - INFO - Request Parameters - Page 3:
2025-06-26 06:00:27,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:27,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:28,278 - INFO - Response - Page 3:
2025-06-26 06:00:28,481 - INFO - 第 3 页获取到 100 条记录
2025-06-26 06:00:28,481 - INFO - Request Parameters - Page 4:
2025-06-26 06:00:28,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:28,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:29,012 - INFO - Response - Page 4:
2025-06-26 06:00:29,216 - INFO - 第 4 页获取到 100 条记录
2025-06-26 06:00:29,216 - INFO - Request Parameters - Page 5:
2025-06-26 06:00:29,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:29,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:29,669 - INFO - Response - Page 5:
2025-06-26 06:00:29,872 - INFO - 第 5 页获取到 100 条记录
2025-06-26 06:00:29,872 - INFO - Request Parameters - Page 6:
2025-06-26 06:00:29,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:29,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:30,387 - INFO - Response - Page 6:
2025-06-26 06:00:30,591 - INFO - 第 6 页获取到 100 条记录
2025-06-26 06:00:30,591 - INFO - Request Parameters - Page 7:
2025-06-26 06:00:30,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:30,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:31,091 - INFO - Response - Page 7:
2025-06-26 06:00:31,294 - INFO - 第 7 页获取到 65 条记录
2025-06-26 06:00:31,294 - INFO - 查询完成，共获取到 665 条记录
2025-06-26 06:00:31,294 - INFO - 获取到 665 条表单数据
2025-06-26 06:00:31,294 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-26 06:00:31,309 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 06:00:31,309 - INFO - 开始处理日期: 2025-06
2025-06-26 06:00:31,309 - INFO - Request Parameters - Page 1:
2025-06-26 06:00:31,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:31,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:31,825 - INFO - Response - Page 1:
2025-06-26 06:00:32,028 - INFO - 第 1 页获取到 100 条记录
2025-06-26 06:00:32,028 - INFO - Request Parameters - Page 2:
2025-06-26 06:00:32,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:32,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:32,559 - INFO - Response - Page 2:
2025-06-26 06:00:32,762 - INFO - 第 2 页获取到 100 条记录
2025-06-26 06:00:32,762 - INFO - Request Parameters - Page 3:
2025-06-26 06:00:32,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:32,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:33,341 - INFO - Response - Page 3:
2025-06-26 06:00:33,544 - INFO - 第 3 页获取到 100 条记录
2025-06-26 06:00:33,544 - INFO - Request Parameters - Page 4:
2025-06-26 06:00:33,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:33,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:34,044 - INFO - Response - Page 4:
2025-06-26 06:00:34,247 - INFO - 第 4 页获取到 100 条记录
2025-06-26 06:00:34,247 - INFO - Request Parameters - Page 5:
2025-06-26 06:00:34,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:34,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:34,716 - INFO - Response - Page 5:
2025-06-26 06:00:34,919 - INFO - 第 5 页获取到 100 条记录
2025-06-26 06:00:34,919 - INFO - Request Parameters - Page 6:
2025-06-26 06:00:34,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:34,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:35,434 - INFO - Response - Page 6:
2025-06-26 06:00:35,637 - INFO - 第 6 页获取到 100 条记录
2025-06-26 06:00:35,637 - INFO - Request Parameters - Page 7:
2025-06-26 06:00:35,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 06:00:35,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 06:00:36,106 - INFO - Response - Page 7:
2025-06-26 06:00:36,309 - INFO - 第 7 页获取到 26 条记录
2025-06-26 06:00:36,309 - INFO - 查询完成，共获取到 626 条记录
2025-06-26 06:00:36,309 - INFO - 获取到 626 条表单数据
2025-06-26 06:00:36,309 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-26 06:00:36,325 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-26 06:00:36,825 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-26 06:00:36,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83015.0, 'new_value': 85091.0}, {'field': 'total_amount', 'old_value': 83015.0, 'new_value': 85091.0}, {'field': 'order_count', 'old_value': 423, 'new_value': 437}]
2025-06-26 06:00:36,825 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-26 06:00:36,825 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-26 06:00:36,825 - INFO - =================同步完成====================
2025-06-26 09:00:03,247 - INFO - =================使用默认全量同步=============
2025-06-26 09:00:05,049 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-26 09:00:05,050 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-26 09:00:05,077 - INFO - 开始处理日期: 2025-01
2025-06-26 09:00:05,077 - INFO - Request Parameters - Page 1:
2025-06-26 09:00:05,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:05,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:06,077 - INFO - Response - Page 1:
2025-06-26 09:00:06,280 - INFO - 第 1 页获取到 100 条记录
2025-06-26 09:00:06,280 - INFO - Request Parameters - Page 2:
2025-06-26 09:00:06,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:06,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:07,311 - INFO - Response - Page 2:
2025-06-26 09:00:07,514 - INFO - 第 2 页获取到 100 条记录
2025-06-26 09:00:07,514 - INFO - Request Parameters - Page 3:
2025-06-26 09:00:07,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:07,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:08,108 - INFO - Response - Page 3:
2025-06-26 09:00:08,311 - INFO - 第 3 页获取到 100 条记录
2025-06-26 09:00:08,311 - INFO - Request Parameters - Page 4:
2025-06-26 09:00:08,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:08,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:08,827 - INFO - Response - Page 4:
2025-06-26 09:00:09,030 - INFO - 第 4 页获取到 100 条记录
2025-06-26 09:00:09,030 - INFO - Request Parameters - Page 5:
2025-06-26 09:00:09,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:09,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:09,561 - INFO - Response - Page 5:
2025-06-26 09:00:09,764 - INFO - 第 5 页获取到 100 条记录
2025-06-26 09:00:09,764 - INFO - Request Parameters - Page 6:
2025-06-26 09:00:09,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:09,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:10,342 - INFO - Response - Page 6:
2025-06-26 09:00:10,545 - INFO - 第 6 页获取到 100 条记录
2025-06-26 09:00:10,545 - INFO - Request Parameters - Page 7:
2025-06-26 09:00:10,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:10,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:11,061 - INFO - Response - Page 7:
2025-06-26 09:00:11,264 - INFO - 第 7 页获取到 82 条记录
2025-06-26 09:00:11,264 - INFO - 查询完成，共获取到 682 条记录
2025-06-26 09:00:11,264 - INFO - 获取到 682 条表单数据
2025-06-26 09:00:11,264 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-26 09:00:11,280 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 09:00:11,280 - INFO - 开始处理日期: 2025-02
2025-06-26 09:00:11,280 - INFO - Request Parameters - Page 1:
2025-06-26 09:00:11,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:11,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:11,873 - INFO - Response - Page 1:
2025-06-26 09:00:12,077 - INFO - 第 1 页获取到 100 条记录
2025-06-26 09:00:12,077 - INFO - Request Parameters - Page 2:
2025-06-26 09:00:12,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:12,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:12,592 - INFO - Response - Page 2:
2025-06-26 09:00:12,795 - INFO - 第 2 页获取到 100 条记录
2025-06-26 09:00:12,795 - INFO - Request Parameters - Page 3:
2025-06-26 09:00:12,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:12,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:13,311 - INFO - Response - Page 3:
2025-06-26 09:00:13,530 - INFO - 第 3 页获取到 100 条记录
2025-06-26 09:00:13,530 - INFO - Request Parameters - Page 4:
2025-06-26 09:00:13,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:13,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:14,014 - INFO - Response - Page 4:
2025-06-26 09:00:14,217 - INFO - 第 4 页获取到 100 条记录
2025-06-26 09:00:14,217 - INFO - Request Parameters - Page 5:
2025-06-26 09:00:14,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:14,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:14,733 - INFO - Response - Page 5:
2025-06-26 09:00:14,952 - INFO - 第 5 页获取到 100 条记录
2025-06-26 09:00:14,952 - INFO - Request Parameters - Page 6:
2025-06-26 09:00:14,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:14,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:15,467 - INFO - Response - Page 6:
2025-06-26 09:00:15,670 - INFO - 第 6 页获取到 100 条记录
2025-06-26 09:00:15,670 - INFO - Request Parameters - Page 7:
2025-06-26 09:00:15,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:15,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:16,170 - INFO - Response - Page 7:
2025-06-26 09:00:16,373 - INFO - 第 7 页获取到 70 条记录
2025-06-26 09:00:16,373 - INFO - 查询完成，共获取到 670 条记录
2025-06-26 09:00:16,373 - INFO - 获取到 670 条表单数据
2025-06-26 09:00:16,373 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-26 09:00:16,389 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 09:00:16,389 - INFO - 开始处理日期: 2025-03
2025-06-26 09:00:16,389 - INFO - Request Parameters - Page 1:
2025-06-26 09:00:16,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:16,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:16,952 - INFO - Response - Page 1:
2025-06-26 09:00:17,155 - INFO - 第 1 页获取到 100 条记录
2025-06-26 09:00:17,155 - INFO - Request Parameters - Page 2:
2025-06-26 09:00:17,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:17,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:17,670 - INFO - Response - Page 2:
2025-06-26 09:00:17,873 - INFO - 第 2 页获取到 100 条记录
2025-06-26 09:00:17,873 - INFO - Request Parameters - Page 3:
2025-06-26 09:00:17,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:17,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:18,358 - INFO - Response - Page 3:
2025-06-26 09:00:18,561 - INFO - 第 3 页获取到 100 条记录
2025-06-26 09:00:18,561 - INFO - Request Parameters - Page 4:
2025-06-26 09:00:18,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:18,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:19,108 - INFO - Response - Page 4:
2025-06-26 09:00:19,311 - INFO - 第 4 页获取到 100 条记录
2025-06-26 09:00:19,311 - INFO - Request Parameters - Page 5:
2025-06-26 09:00:19,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:19,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:19,826 - INFO - Response - Page 5:
2025-06-26 09:00:20,030 - INFO - 第 5 页获取到 100 条记录
2025-06-26 09:00:20,030 - INFO - Request Parameters - Page 6:
2025-06-26 09:00:20,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:20,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:20,608 - INFO - Response - Page 6:
2025-06-26 09:00:20,811 - INFO - 第 6 页获取到 100 条记录
2025-06-26 09:00:20,811 - INFO - Request Parameters - Page 7:
2025-06-26 09:00:20,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:20,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:21,264 - INFO - Response - Page 7:
2025-06-26 09:00:21,467 - INFO - 第 7 页获取到 61 条记录
2025-06-26 09:00:21,467 - INFO - 查询完成，共获取到 661 条记录
2025-06-26 09:00:21,467 - INFO - 获取到 661 条表单数据
2025-06-26 09:00:21,467 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-26 09:00:21,483 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 09:00:21,483 - INFO - 开始处理日期: 2025-04
2025-06-26 09:00:21,483 - INFO - Request Parameters - Page 1:
2025-06-26 09:00:21,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:21,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:21,951 - INFO - Response - Page 1:
2025-06-26 09:00:22,155 - INFO - 第 1 页获取到 100 条记录
2025-06-26 09:00:22,155 - INFO - Request Parameters - Page 2:
2025-06-26 09:00:22,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:22,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:22,655 - INFO - Response - Page 2:
2025-06-26 09:00:22,858 - INFO - 第 2 页获取到 100 条记录
2025-06-26 09:00:22,858 - INFO - Request Parameters - Page 3:
2025-06-26 09:00:22,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:22,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:23,389 - INFO - Response - Page 3:
2025-06-26 09:00:23,592 - INFO - 第 3 页获取到 100 条记录
2025-06-26 09:00:23,592 - INFO - Request Parameters - Page 4:
2025-06-26 09:00:23,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:23,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:24,108 - INFO - Response - Page 4:
2025-06-26 09:00:24,312 - INFO - 第 4 页获取到 100 条记录
2025-06-26 09:00:24,312 - INFO - Request Parameters - Page 5:
2025-06-26 09:00:24,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:24,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:24,843 - INFO - Response - Page 5:
2025-06-26 09:00:25,046 - INFO - 第 5 页获取到 100 条记录
2025-06-26 09:00:25,046 - INFO - Request Parameters - Page 6:
2025-06-26 09:00:25,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:25,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:25,562 - INFO - Response - Page 6:
2025-06-26 09:00:25,765 - INFO - 第 6 页获取到 100 条记录
2025-06-26 09:00:25,765 - INFO - Request Parameters - Page 7:
2025-06-26 09:00:25,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:25,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:26,624 - INFO - Response - Page 7:
2025-06-26 09:00:26,827 - INFO - 第 7 页获取到 56 条记录
2025-06-26 09:00:26,827 - INFO - 查询完成，共获取到 656 条记录
2025-06-26 09:00:26,827 - INFO - 获取到 656 条表单数据
2025-06-26 09:00:26,827 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-26 09:00:26,843 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 09:00:26,843 - INFO - 开始处理日期: 2025-05
2025-06-26 09:00:26,843 - INFO - Request Parameters - Page 1:
2025-06-26 09:00:26,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:26,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:27,437 - INFO - Response - Page 1:
2025-06-26 09:00:27,640 - INFO - 第 1 页获取到 100 条记录
2025-06-26 09:00:27,640 - INFO - Request Parameters - Page 2:
2025-06-26 09:00:27,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:27,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:28,265 - INFO - Response - Page 2:
2025-06-26 09:00:28,468 - INFO - 第 2 页获取到 100 条记录
2025-06-26 09:00:28,468 - INFO - Request Parameters - Page 3:
2025-06-26 09:00:28,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:28,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:28,952 - INFO - Response - Page 3:
2025-06-26 09:00:29,156 - INFO - 第 3 页获取到 100 条记录
2025-06-26 09:00:29,156 - INFO - Request Parameters - Page 4:
2025-06-26 09:00:29,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:29,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:30,265 - INFO - Response - Page 4:
2025-06-26 09:00:30,468 - INFO - 第 4 页获取到 100 条记录
2025-06-26 09:00:30,468 - INFO - Request Parameters - Page 5:
2025-06-26 09:00:30,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:30,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:30,952 - INFO - Response - Page 5:
2025-06-26 09:00:31,156 - INFO - 第 5 页获取到 100 条记录
2025-06-26 09:00:31,156 - INFO - Request Parameters - Page 6:
2025-06-26 09:00:31,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:31,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:31,609 - INFO - Response - Page 6:
2025-06-26 09:00:31,812 - INFO - 第 6 页获取到 100 条记录
2025-06-26 09:00:31,812 - INFO - Request Parameters - Page 7:
2025-06-26 09:00:31,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:31,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:32,296 - INFO - Response - Page 7:
2025-06-26 09:00:32,499 - INFO - 第 7 页获取到 65 条记录
2025-06-26 09:00:32,499 - INFO - 查询完成，共获取到 665 条记录
2025-06-26 09:00:32,499 - INFO - 获取到 665 条表单数据
2025-06-26 09:00:32,499 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-26 09:00:32,515 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 09:00:32,515 - INFO - 开始处理日期: 2025-06
2025-06-26 09:00:32,515 - INFO - Request Parameters - Page 1:
2025-06-26 09:00:32,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:32,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:33,046 - INFO - Response - Page 1:
2025-06-26 09:00:33,249 - INFO - 第 1 页获取到 100 条记录
2025-06-26 09:00:33,249 - INFO - Request Parameters - Page 2:
2025-06-26 09:00:33,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:33,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:33,765 - INFO - Response - Page 2:
2025-06-26 09:00:33,968 - INFO - 第 2 页获取到 100 条记录
2025-06-26 09:00:33,968 - INFO - Request Parameters - Page 3:
2025-06-26 09:00:33,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:33,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:34,421 - INFO - Response - Page 3:
2025-06-26 09:00:34,640 - INFO - 第 3 页获取到 100 条记录
2025-06-26 09:00:34,640 - INFO - Request Parameters - Page 4:
2025-06-26 09:00:34,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:34,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:35,218 - INFO - Response - Page 4:
2025-06-26 09:00:35,421 - INFO - 第 4 页获取到 100 条记录
2025-06-26 09:00:35,421 - INFO - Request Parameters - Page 5:
2025-06-26 09:00:35,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:35,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:35,921 - INFO - Response - Page 5:
2025-06-26 09:00:36,124 - INFO - 第 5 页获取到 100 条记录
2025-06-26 09:00:36,124 - INFO - Request Parameters - Page 6:
2025-06-26 09:00:36,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:36,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:36,734 - INFO - Response - Page 6:
2025-06-26 09:00:36,937 - INFO - 第 6 页获取到 100 条记录
2025-06-26 09:00:36,937 - INFO - Request Parameters - Page 7:
2025-06-26 09:00:36,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 09:00:36,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 09:00:37,359 - INFO - Response - Page 7:
2025-06-26 09:00:37,562 - INFO - 第 7 页获取到 26 条记录
2025-06-26 09:00:37,562 - INFO - 查询完成，共获取到 626 条记录
2025-06-26 09:00:37,562 - INFO - 获取到 626 条表单数据
2025-06-26 09:00:37,562 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-26 09:00:37,577 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-26 09:00:38,062 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-26 09:00:38,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1700000.0, 'new_value': 1750000.0}, {'field': 'total_amount', 'old_value': 1800000.0, 'new_value': 1850000.0}, {'field': 'order_count', 'old_value': 376, 'new_value': 377}]
2025-06-26 09:00:38,062 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-26 09:00:38,577 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-26 09:00:38,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 521829.55, 'new_value': 545204.65}, {'field': 'total_amount', 'old_value': 521829.55, 'new_value': 545204.65}, {'field': 'order_count', 'old_value': 2001, 'new_value': 2102}]
2025-06-26 09:00:38,577 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-26 09:00:39,030 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-26 09:00:39,030 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 199610.13, 'new_value': 204609.13}, {'field': 'offline_amount', 'old_value': 16238.3, 'new_value': 16247.3}, {'field': 'total_amount', 'old_value': 215848.43, 'new_value': 220856.43}, {'field': 'order_count', 'old_value': 14834, 'new_value': 15030}]
2025-06-26 09:00:39,030 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-26 09:00:39,609 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-26 09:00:39,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68435.0, 'new_value': 71008.0}, {'field': 'total_amount', 'old_value': 68435.0, 'new_value': 71008.0}, {'field': 'order_count', 'old_value': 3047, 'new_value': 3172}]
2025-06-26 09:00:39,609 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-26 09:00:40,015 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-26 09:00:40,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265592.0, 'new_value': 284073.0}, {'field': 'total_amount', 'old_value': 265592.0, 'new_value': 284073.0}, {'field': 'order_count', 'old_value': 341, 'new_value': 364}]
2025-06-26 09:00:40,015 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-26 09:00:40,624 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-26 09:00:40,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91623.0, 'new_value': 94407.0}, {'field': 'total_amount', 'old_value': 91623.0, 'new_value': 94407.0}, {'field': 'order_count', 'old_value': 8613, 'new_value': 8840}]
2025-06-26 09:00:40,624 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-26 09:00:41,077 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-26 09:00:41,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2029588.0, 'new_value': 2079588.0}, {'field': 'total_amount', 'old_value': 2029588.0, 'new_value': 2079588.0}, {'field': 'order_count', 'old_value': 3085, 'new_value': 3086}]
2025-06-26 09:00:41,077 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-26 09:00:41,530 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-26 09:00:41,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'total_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'order_count', 'old_value': 798, 'new_value': 799}]
2025-06-26 09:00:41,530 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-26 09:00:41,984 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-26 09:00:41,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'total_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'order_count', 'old_value': 927, 'new_value': 928}]
2025-06-26 09:00:41,984 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-26 09:00:42,609 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-26 09:00:42,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 937057.36, 'new_value': 950302.41}, {'field': 'total_amount', 'old_value': 937057.36, 'new_value': 950302.41}, {'field': 'order_count', 'old_value': 4337, 'new_value': 4431}]
2025-06-26 09:00:42,609 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-26 09:00:43,109 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-26 09:00:43,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138572.07, 'new_value': 143480.86}, {'field': 'total_amount', 'old_value': 138572.07, 'new_value': 143480.86}, {'field': 'order_count', 'old_value': 9840, 'new_value': 10172}]
2025-06-26 09:00:43,109 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-26 09:00:43,609 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-26 09:00:43,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'total_amount', 'old_value': 180000.0, 'new_value': 185000.0}, {'field': 'order_count', 'old_value': 593, 'new_value': 594}]
2025-06-26 09:00:43,624 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-26 09:00:44,109 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-26 09:00:44,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 417834.0, 'new_value': 427840.0}, {'field': 'total_amount', 'old_value': 455490.0, 'new_value': 465496.0}, {'field': 'order_count', 'old_value': 9477, 'new_value': 9687}]
2025-06-26 09:00:44,109 - INFO - 日期 2025-06 处理完成 - 更新: 13 条，插入: 0 条，错误: 0 条
2025-06-26 09:00:44,109 - INFO - 数据同步完成！更新: 13 条，插入: 0 条，错误: 0 条
2025-06-26 09:00:44,109 - INFO - =================同步完成====================
2025-06-26 12:00:02,679 - INFO - =================使用默认全量同步=============
2025-06-26 12:00:04,460 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-26 12:00:04,475 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-26 12:00:04,507 - INFO - 开始处理日期: 2025-01
2025-06-26 12:00:04,507 - INFO - Request Parameters - Page 1:
2025-06-26 12:00:04,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:04,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:06,382 - INFO - Response - Page 1:
2025-06-26 12:00:06,585 - INFO - 第 1 页获取到 100 条记录
2025-06-26 12:00:06,585 - INFO - Request Parameters - Page 2:
2025-06-26 12:00:06,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:06,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:07,585 - INFO - Response - Page 2:
2025-06-26 12:00:07,788 - INFO - 第 2 页获取到 100 条记录
2025-06-26 12:00:07,788 - INFO - Request Parameters - Page 3:
2025-06-26 12:00:07,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:07,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:08,350 - INFO - Response - Page 3:
2025-06-26 12:00:08,554 - INFO - 第 3 页获取到 100 条记录
2025-06-26 12:00:08,554 - INFO - Request Parameters - Page 4:
2025-06-26 12:00:08,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:08,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:09,304 - INFO - Response - Page 4:
2025-06-26 12:00:09,507 - INFO - 第 4 页获取到 100 条记录
2025-06-26 12:00:09,507 - INFO - Request Parameters - Page 5:
2025-06-26 12:00:09,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:09,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:10,132 - INFO - Response - Page 5:
2025-06-26 12:00:10,335 - INFO - 第 5 页获取到 100 条记录
2025-06-26 12:00:10,335 - INFO - Request Parameters - Page 6:
2025-06-26 12:00:10,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:10,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:10,913 - INFO - Response - Page 6:
2025-06-26 12:00:11,116 - INFO - 第 6 页获取到 100 条记录
2025-06-26 12:00:11,116 - INFO - Request Parameters - Page 7:
2025-06-26 12:00:11,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:11,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:11,616 - INFO - Response - Page 7:
2025-06-26 12:00:11,819 - INFO - 第 7 页获取到 82 条记录
2025-06-26 12:00:11,819 - INFO - 查询完成，共获取到 682 条记录
2025-06-26 12:00:11,819 - INFO - 获取到 682 条表单数据
2025-06-26 12:00:11,819 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-26 12:00:11,835 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 12:00:11,835 - INFO - 开始处理日期: 2025-02
2025-06-26 12:00:11,835 - INFO - Request Parameters - Page 1:
2025-06-26 12:00:11,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:11,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:12,413 - INFO - Response - Page 1:
2025-06-26 12:00:12,616 - INFO - 第 1 页获取到 100 条记录
2025-06-26 12:00:12,616 - INFO - Request Parameters - Page 2:
2025-06-26 12:00:12,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:12,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:13,444 - INFO - Response - Page 2:
2025-06-26 12:00:13,647 - INFO - 第 2 页获取到 100 条记录
2025-06-26 12:00:13,647 - INFO - Request Parameters - Page 3:
2025-06-26 12:00:13,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:13,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:14,147 - INFO - Response - Page 3:
2025-06-26 12:00:14,350 - INFO - 第 3 页获取到 100 条记录
2025-06-26 12:00:14,350 - INFO - Request Parameters - Page 4:
2025-06-26 12:00:14,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:14,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:14,991 - INFO - Response - Page 4:
2025-06-26 12:00:15,194 - INFO - 第 4 页获取到 100 条记录
2025-06-26 12:00:15,194 - INFO - Request Parameters - Page 5:
2025-06-26 12:00:15,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:15,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:15,803 - INFO - Response - Page 5:
2025-06-26 12:00:16,007 - INFO - 第 5 页获取到 100 条记录
2025-06-26 12:00:16,007 - INFO - Request Parameters - Page 6:
2025-06-26 12:00:16,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:16,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:16,491 - INFO - Response - Page 6:
2025-06-26 12:00:16,694 - INFO - 第 6 页获取到 100 条记录
2025-06-26 12:00:16,694 - INFO - Request Parameters - Page 7:
2025-06-26 12:00:16,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:16,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:17,553 - INFO - Response - Page 7:
2025-06-26 12:00:17,757 - INFO - 第 7 页获取到 70 条记录
2025-06-26 12:00:17,757 - INFO - 查询完成，共获取到 670 条记录
2025-06-26 12:00:17,757 - INFO - 获取到 670 条表单数据
2025-06-26 12:00:17,757 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-26 12:00:17,772 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 12:00:17,772 - INFO - 开始处理日期: 2025-03
2025-06-26 12:00:17,772 - INFO - Request Parameters - Page 1:
2025-06-26 12:00:17,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:17,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:18,663 - INFO - Response - Page 1:
2025-06-26 12:00:18,866 - INFO - 第 1 页获取到 100 条记录
2025-06-26 12:00:18,866 - INFO - Request Parameters - Page 2:
2025-06-26 12:00:18,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:18,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:19,413 - INFO - Response - Page 2:
2025-06-26 12:00:19,616 - INFO - 第 2 页获取到 100 条记录
2025-06-26 12:00:19,616 - INFO - Request Parameters - Page 3:
2025-06-26 12:00:19,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:19,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:20,757 - INFO - Response - Page 3:
2025-06-26 12:00:20,960 - INFO - 第 3 页获取到 100 条记录
2025-06-26 12:00:20,960 - INFO - Request Parameters - Page 4:
2025-06-26 12:00:20,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:20,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:21,428 - INFO - Response - Page 4:
2025-06-26 12:00:21,632 - INFO - 第 4 页获取到 100 条记录
2025-06-26 12:00:21,632 - INFO - Request Parameters - Page 5:
2025-06-26 12:00:21,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:21,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:22,210 - INFO - Response - Page 5:
2025-06-26 12:00:22,413 - INFO - 第 5 页获取到 100 条记录
2025-06-26 12:00:22,413 - INFO - Request Parameters - Page 6:
2025-06-26 12:00:22,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:22,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:23,038 - INFO - Response - Page 6:
2025-06-26 12:00:23,241 - INFO - 第 6 页获取到 100 条记录
2025-06-26 12:00:23,241 - INFO - Request Parameters - Page 7:
2025-06-26 12:00:23,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:23,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:23,632 - INFO - Response - Page 7:
2025-06-26 12:00:23,835 - INFO - 第 7 页获取到 61 条记录
2025-06-26 12:00:23,835 - INFO - 查询完成，共获取到 661 条记录
2025-06-26 12:00:23,835 - INFO - 获取到 661 条表单数据
2025-06-26 12:00:23,835 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-26 12:00:23,850 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 12:00:23,850 - INFO - 开始处理日期: 2025-04
2025-06-26 12:00:23,850 - INFO - Request Parameters - Page 1:
2025-06-26 12:00:23,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:23,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:24,397 - INFO - Response - Page 1:
2025-06-26 12:00:24,600 - INFO - 第 1 页获取到 100 条记录
2025-06-26 12:00:24,600 - INFO - Request Parameters - Page 2:
2025-06-26 12:00:24,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:24,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:25,053 - INFO - Response - Page 2:
2025-06-26 12:00:25,257 - INFO - 第 2 页获取到 100 条记录
2025-06-26 12:00:25,257 - INFO - Request Parameters - Page 3:
2025-06-26 12:00:25,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:25,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:26,100 - INFO - Response - Page 3:
2025-06-26 12:00:26,303 - INFO - 第 3 页获取到 100 条记录
2025-06-26 12:00:26,303 - INFO - Request Parameters - Page 4:
2025-06-26 12:00:26,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:26,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:26,897 - INFO - Response - Page 4:
2025-06-26 12:00:27,100 - INFO - 第 4 页获取到 100 条记录
2025-06-26 12:00:27,100 - INFO - Request Parameters - Page 5:
2025-06-26 12:00:27,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:27,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:27,569 - INFO - Response - Page 5:
2025-06-26 12:00:27,772 - INFO - 第 5 页获取到 100 条记录
2025-06-26 12:00:27,772 - INFO - Request Parameters - Page 6:
2025-06-26 12:00:27,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:27,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:28,241 - INFO - Response - Page 6:
2025-06-26 12:00:28,444 - INFO - 第 6 页获取到 100 条记录
2025-06-26 12:00:28,444 - INFO - Request Parameters - Page 7:
2025-06-26 12:00:28,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:28,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:28,928 - INFO - Response - Page 7:
2025-06-26 12:00:29,131 - INFO - 第 7 页获取到 56 条记录
2025-06-26 12:00:29,131 - INFO - 查询完成，共获取到 656 条记录
2025-06-26 12:00:29,131 - INFO - 获取到 656 条表单数据
2025-06-26 12:00:29,131 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-26 12:00:29,147 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 12:00:29,147 - INFO - 开始处理日期: 2025-05
2025-06-26 12:00:29,147 - INFO - Request Parameters - Page 1:
2025-06-26 12:00:29,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:29,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:29,710 - INFO - Response - Page 1:
2025-06-26 12:00:29,913 - INFO - 第 1 页获取到 100 条记录
2025-06-26 12:00:29,913 - INFO - Request Parameters - Page 2:
2025-06-26 12:00:29,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:29,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:30,413 - INFO - Response - Page 2:
2025-06-26 12:00:30,616 - INFO - 第 2 页获取到 100 条记录
2025-06-26 12:00:30,616 - INFO - Request Parameters - Page 3:
2025-06-26 12:00:30,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:30,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:31,881 - INFO - Response - Page 3:
2025-06-26 12:00:32,085 - INFO - 第 3 页获取到 100 条记录
2025-06-26 12:00:32,085 - INFO - Request Parameters - Page 4:
2025-06-26 12:00:32,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:32,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:32,585 - INFO - Response - Page 4:
2025-06-26 12:00:32,788 - INFO - 第 4 页获取到 100 条记录
2025-06-26 12:00:32,788 - INFO - Request Parameters - Page 5:
2025-06-26 12:00:32,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:32,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:33,272 - INFO - Response - Page 5:
2025-06-26 12:00:33,475 - INFO - 第 5 页获取到 100 条记录
2025-06-26 12:00:33,475 - INFO - Request Parameters - Page 6:
2025-06-26 12:00:33,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:33,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:33,975 - INFO - Response - Page 6:
2025-06-26 12:00:34,178 - INFO - 第 6 页获取到 100 条记录
2025-06-26 12:00:34,178 - INFO - Request Parameters - Page 7:
2025-06-26 12:00:34,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:34,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:34,663 - INFO - Response - Page 7:
2025-06-26 12:00:34,866 - INFO - 第 7 页获取到 65 条记录
2025-06-26 12:00:34,866 - INFO - 查询完成，共获取到 665 条记录
2025-06-26 12:00:34,866 - INFO - 获取到 665 条表单数据
2025-06-26 12:00:34,866 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-26 12:00:34,881 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 12:00:34,881 - INFO - 开始处理日期: 2025-06
2025-06-26 12:00:34,881 - INFO - Request Parameters - Page 1:
2025-06-26 12:00:34,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:34,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:35,553 - INFO - Response - Page 1:
2025-06-26 12:00:35,756 - INFO - 第 1 页获取到 100 条记录
2025-06-26 12:00:35,756 - INFO - Request Parameters - Page 2:
2025-06-26 12:00:35,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:35,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:36,210 - INFO - Response - Page 2:
2025-06-26 12:00:36,413 - INFO - 第 2 页获取到 100 条记录
2025-06-26 12:00:36,413 - INFO - Request Parameters - Page 3:
2025-06-26 12:00:36,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:36,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:36,913 - INFO - Response - Page 3:
2025-06-26 12:00:37,116 - INFO - 第 3 页获取到 100 条记录
2025-06-26 12:00:37,116 - INFO - Request Parameters - Page 4:
2025-06-26 12:00:37,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:37,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:37,678 - INFO - Response - Page 4:
2025-06-26 12:00:37,881 - INFO - 第 4 页获取到 100 条记录
2025-06-26 12:00:37,881 - INFO - Request Parameters - Page 5:
2025-06-26 12:00:37,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:37,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:38,710 - INFO - Response - Page 5:
2025-06-26 12:00:38,913 - INFO - 第 5 页获取到 100 条记录
2025-06-26 12:00:38,913 - INFO - Request Parameters - Page 6:
2025-06-26 12:00:38,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:38,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:39,428 - INFO - Response - Page 6:
2025-06-26 12:00:39,631 - INFO - 第 6 页获取到 100 条记录
2025-06-26 12:00:39,631 - INFO - Request Parameters - Page 7:
2025-06-26 12:00:39,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 12:00:39,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 12:00:40,022 - INFO - Response - Page 7:
2025-06-26 12:00:40,225 - INFO - 第 7 页获取到 26 条记录
2025-06-26 12:00:40,225 - INFO - 查询完成，共获取到 626 条记录
2025-06-26 12:00:40,225 - INFO - 获取到 626 条表单数据
2025-06-26 12:00:40,225 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-26 12:00:40,225 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-26 12:00:40,756 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-26 12:00:40,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160172.0, 'new_value': 161903.0}, {'field': 'total_amount', 'old_value': 160172.0, 'new_value': 161903.0}, {'field': 'order_count', 'old_value': 2645, 'new_value': 2681}]
2025-06-26 12:00:40,756 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-26 12:00:41,288 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-26 12:00:41,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 696231.0, 'new_value': 719007.0}, {'field': 'total_amount', 'old_value': 696231.0, 'new_value': 719007.0}, {'field': 'order_count', 'old_value': 4937, 'new_value': 5093}]
2025-06-26 12:00:41,288 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-26 12:00:41,803 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-26 12:00:41,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111960.0, 'new_value': 131960.0}, {'field': 'total_amount', 'old_value': 111960.0, 'new_value': 131960.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-06-26 12:00:41,803 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-26 12:00:42,319 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-26 12:00:42,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132723.28, 'new_value': 136007.51}, {'field': 'total_amount', 'old_value': 132723.28, 'new_value': 136007.51}, {'field': 'order_count', 'old_value': 3464, 'new_value': 3560}]
2025-06-26 12:00:42,319 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-26 12:00:42,803 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-26 12:00:42,803 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79450.73, 'new_value': 83902.12}, {'field': 'offline_amount', 'old_value': 883842.07, 'new_value': 918727.35}, {'field': 'total_amount', 'old_value': 959300.31, 'new_value': 998636.98}, {'field': 'order_count', 'old_value': 4648, 'new_value': 4859}]
2025-06-26 12:00:42,803 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-26 12:00:43,397 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-26 12:00:43,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310670.0, 'new_value': 355670.0}, {'field': 'total_amount', 'old_value': 332850.0, 'new_value': 377850.0}, {'field': 'order_count', 'old_value': 231, 'new_value': 251}]
2025-06-26 12:00:43,397 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-26 12:00:43,850 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-26 12:00:43,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 704525.0, 'new_value': 754525.0}, {'field': 'total_amount', 'old_value': 704525.0, 'new_value': 754525.0}, {'field': 'order_count', 'old_value': 2514, 'new_value': 2517}]
2025-06-26 12:00:43,850 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-26 12:00:44,350 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-26 12:00:44,350 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 379823.0, 'new_value': 393314.0}, {'field': 'offline_amount', 'old_value': 149809.0, 'new_value': 154324.0}, {'field': 'total_amount', 'old_value': 529632.0, 'new_value': 547638.0}, {'field': 'order_count', 'old_value': 582, 'new_value': 596}]
2025-06-26 12:00:44,350 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-26 12:00:44,835 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-26 12:00:44,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184060.0, 'new_value': 190320.0}, {'field': 'total_amount', 'old_value': 184060.0, 'new_value': 190320.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-26 12:00:44,835 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMXX
2025-06-26 12:00:45,303 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMXX
2025-06-26 12:00:45,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14856.0, 'new_value': 16624.0}, {'field': 'total_amount', 'old_value': 14856.0, 'new_value': 16624.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-26 12:00:45,303 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-26 12:00:45,772 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-26 12:00:45,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25959.82, 'new_value': 26445.64}, {'field': 'total_amount', 'old_value': 25959.82, 'new_value': 26445.64}, {'field': 'order_count', 'old_value': 84, 'new_value': 88}]
2025-06-26 12:00:45,772 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Y
2025-06-26 12:00:46,131 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Y
2025-06-26 12:00:46,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 8320.0}, {'field': 'total_amount', 'old_value': 76025.39, 'new_value': 84345.39}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-06-26 12:00:46,131 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Y
2025-06-26 12:00:46,569 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Y
2025-06-26 12:00:46,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310153.0, 'new_value': 317053.0}, {'field': 'total_amount', 'old_value': 340153.0, 'new_value': 347053.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-06-26 12:00:46,569 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Y
2025-06-26 12:00:47,053 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Y
2025-06-26 12:00:47,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258365.0, 'new_value': 286165.0}, {'field': 'total_amount', 'old_value': 308365.0, 'new_value': 336165.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-06-26 12:00:47,053 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Y
2025-06-26 12:00:47,506 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Y
2025-06-26 12:00:47,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 40000.0}, {'field': 'offline_amount', 'old_value': 289983.0, 'new_value': 294503.0}, {'field': 'total_amount', 'old_value': 289983.0, 'new_value': 334503.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 50}]
2025-06-26 12:00:47,506 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-26 12:00:47,991 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-26 12:00:47,991 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6966.0, 'new_value': 7185.0}, {'field': 'offline_amount', 'old_value': 97028.0, 'new_value': 100028.0}, {'field': 'total_amount', 'old_value': 103994.0, 'new_value': 107213.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 119}]
2025-06-26 12:00:47,991 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Y
2025-06-26 12:00:48,428 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Y
2025-06-26 12:00:48,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44922.8, 'new_value': 49596.62}, {'field': 'total_amount', 'old_value': 87220.1, 'new_value': 91893.92}, {'field': 'order_count', 'old_value': 3098, 'new_value': 3275}]
2025-06-26 12:00:48,428 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Y
2025-06-26 12:00:48,897 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Y
2025-06-26 12:00:48,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7286.0, 'new_value': 7937.0}, {'field': 'offline_amount', 'old_value': 36429.9, 'new_value': 51428.9}, {'field': 'total_amount', 'old_value': 43715.9, 'new_value': 59365.9}, {'field': 'order_count', 'old_value': 66, 'new_value': 74}]
2025-06-26 12:00:48,897 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-26 12:00:49,288 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-26 12:00:49,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48760.0, 'new_value': 51300.0}, {'field': 'total_amount', 'old_value': 57960.0, 'new_value': 60500.0}, {'field': 'order_count', 'old_value': 639, 'new_value': 665}]
2025-06-26 12:00:49,288 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-26 12:00:49,756 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-26 12:00:49,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260952.2, 'new_value': 266265.1}, {'field': 'total_amount', 'old_value': 260952.2, 'new_value': 266265.1}, {'field': 'order_count', 'old_value': 3826, 'new_value': 3933}]
2025-06-26 12:00:49,756 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-26 12:00:50,319 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-26 12:00:50,319 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5437.7, 'new_value': 5898.7}, {'field': 'offline_amount', 'old_value': 50042.77, 'new_value': 51071.87}, {'field': 'total_amount', 'old_value': 55480.47, 'new_value': 56970.57}, {'field': 'order_count', 'old_value': 486, 'new_value': 497}]
2025-06-26 12:00:50,319 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-26 12:00:50,788 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-26 12:00:50,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49833.98, 'new_value': 52410.88}, {'field': 'offline_amount', 'old_value': 112934.02, 'new_value': 117899.77}, {'field': 'total_amount', 'old_value': 162768.0, 'new_value': 170310.65}, {'field': 'order_count', 'old_value': 1827, 'new_value': 1899}]
2025-06-26 12:00:50,788 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-26 12:00:51,241 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-26 12:00:51,241 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46620.8, 'new_value': 46864.7}, {'field': 'total_amount', 'old_value': 46620.8, 'new_value': 46864.7}, {'field': 'order_count', 'old_value': 442, 'new_value': 445}]
2025-06-26 12:00:51,241 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMY
2025-06-26 12:00:51,725 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMY
2025-06-26 12:00:51,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1108791.0, 'new_value': 1147680.0}, {'field': 'offline_amount', 'old_value': 288226.0, 'new_value': 291103.0}, {'field': 'total_amount', 'old_value': 1397017.0, 'new_value': 1438783.0}, {'field': 'order_count', 'old_value': 1519, 'new_value': 1607}]
2025-06-26 12:00:51,725 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-26 12:00:52,256 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-26 12:00:52,256 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34101.0, 'new_value': 34964.0}, {'field': 'offline_amount', 'old_value': 53858.08, 'new_value': 56852.08}, {'field': 'total_amount', 'old_value': 87959.08, 'new_value': 91816.08}, {'field': 'order_count', 'old_value': 118, 'new_value': 122}]
2025-06-26 12:00:52,256 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-26 12:00:52,678 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-26 12:00:52,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59514.0, 'new_value': 62448.0}, {'field': 'total_amount', 'old_value': 59514.0, 'new_value': 62448.0}, {'field': 'order_count', 'old_value': 1123, 'new_value': 1187}]
2025-06-26 12:00:52,678 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-26 12:00:53,147 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-26 12:00:53,147 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141228.0, 'new_value': 145031.0}, {'field': 'offline_amount', 'old_value': 46965.87, 'new_value': 49266.07}, {'field': 'total_amount', 'old_value': 188193.87, 'new_value': 194297.07}, {'field': 'order_count', 'old_value': 1269, 'new_value': 1314}]
2025-06-26 12:00:53,147 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-26 12:00:53,600 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-26 12:00:53,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40001.0, 'new_value': 41300.0}, {'field': 'total_amount', 'old_value': 40702.2, 'new_value': 42001.2}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-26 12:00:53,600 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-26 12:00:54,038 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-26 12:00:54,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5452.0, 'new_value': 5579.0}, {'field': 'total_amount', 'old_value': 9644.0, 'new_value': 9771.0}, {'field': 'order_count', 'old_value': 170, 'new_value': 175}]
2025-06-26 12:00:54,038 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-26 12:00:54,506 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-26 12:00:54,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 221476.0, 'new_value': 228041.0}, {'field': 'total_amount', 'old_value': 221476.0, 'new_value': 228041.0}, {'field': 'order_count', 'old_value': 1148, 'new_value': 1193}]
2025-06-26 12:00:54,506 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXY
2025-06-26 12:00:54,944 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXY
2025-06-26 12:00:54,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43291.96, 'new_value': 44558.46}, {'field': 'total_amount', 'old_value': 43291.96, 'new_value': 44558.46}, {'field': 'order_count', 'old_value': 102, 'new_value': 105}]
2025-06-26 12:00:54,944 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-26 12:00:55,506 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-26 12:00:55,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148886.63, 'new_value': 155513.19}, {'field': 'total_amount', 'old_value': 148886.63, 'new_value': 155513.19}, {'field': 'order_count', 'old_value': 968, 'new_value': 1015}]
2025-06-26 12:00:55,506 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-26 12:00:55,991 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-26 12:00:55,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 440540.6, 'new_value': 441815.6}, {'field': 'total_amount', 'old_value': 496033.6, 'new_value': 497308.6}, {'field': 'order_count', 'old_value': 83, 'new_value': 84}]
2025-06-26 12:00:55,991 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-26 12:00:56,491 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-26 12:00:56,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23354.21, 'new_value': 24444.22}, {'field': 'offline_amount', 'old_value': 27261.92, 'new_value': 28114.11}, {'field': 'total_amount', 'old_value': 50616.13, 'new_value': 52558.33}, {'field': 'order_count', 'old_value': 2535, 'new_value': 2649}]
2025-06-26 12:00:56,491 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-26 12:00:56,928 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-26 12:00:56,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 505153.44, 'new_value': 532480.04}, {'field': 'total_amount', 'old_value': 505153.44, 'new_value': 532480.04}, {'field': 'order_count', 'old_value': 3609, 'new_value': 3847}]
2025-06-26 12:00:56,928 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-26 12:00:57,475 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-26 12:00:57,475 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1350.9, 'new_value': 2349.9}, {'field': 'offline_amount', 'old_value': 10909.5, 'new_value': 11042.5}, {'field': 'total_amount', 'old_value': 12260.4, 'new_value': 13392.4}, {'field': 'order_count', 'old_value': 134, 'new_value': 136}]
2025-06-26 12:00:57,475 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-26 12:00:57,991 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-26 12:00:57,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 877931.6, 'new_value': 881662.2}, {'field': 'total_amount', 'old_value': 962287.9, 'new_value': 966018.5}, {'field': 'order_count', 'old_value': 82, 'new_value': 84}]
2025-06-26 12:00:58,006 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-26 12:00:58,506 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-26 12:00:58,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165604.0, 'new_value': 176202.0}, {'field': 'total_amount', 'old_value': 165604.0, 'new_value': 176202.0}, {'field': 'order_count', 'old_value': 305, 'new_value': 318}]
2025-06-26 12:00:58,506 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-26 12:00:58,928 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-26 12:00:58,928 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9945.2, 'new_value': 10115.93}, {'field': 'offline_amount', 'old_value': 148256.61, 'new_value': 153839.62}, {'field': 'total_amount', 'old_value': 158201.81, 'new_value': 163955.55}, {'field': 'order_count', 'old_value': 1783, 'new_value': 1842}]
2025-06-26 12:00:58,928 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDZ
2025-06-26 12:00:59,428 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDZ
2025-06-26 12:00:59,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42472.49, 'new_value': 46569.45}, {'field': 'total_amount', 'old_value': 42472.49, 'new_value': 46569.45}, {'field': 'order_count', 'old_value': 3047, 'new_value': 3296}]
2025-06-26 12:00:59,428 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-26 12:00:59,881 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-26 12:00:59,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141423.71, 'new_value': 144180.71}, {'field': 'total_amount', 'old_value': 168198.42, 'new_value': 170955.42}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-06-26 12:00:59,881 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMIZ
2025-06-26 12:01:00,381 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMIZ
2025-06-26 12:01:00,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1764.5, 'new_value': 2558.5}, {'field': 'offline_amount', 'old_value': 42411.6, 'new_value': 44037.4}, {'field': 'total_amount', 'old_value': 44176.1, 'new_value': 46595.9}, {'field': 'order_count', 'old_value': 260, 'new_value': 271}]
2025-06-26 12:01:00,381 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-26 12:01:00,756 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-26 12:01:00,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10162.16, 'new_value': 11110.66}, {'field': 'total_amount', 'old_value': 10162.16, 'new_value': 11110.66}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-06-26 12:01:00,756 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-26 12:01:01,288 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-26 12:01:01,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134704.26, 'new_value': 141598.52}, {'field': 'total_amount', 'old_value': 134704.26, 'new_value': 141598.52}, {'field': 'order_count', 'old_value': 683, 'new_value': 717}]
2025-06-26 12:01:01,288 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-26 12:01:01,756 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-26 12:01:01,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27241.0, 'new_value': 28195.0}, {'field': 'total_amount', 'old_value': 27241.0, 'new_value': 28195.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 132}]
2025-06-26 12:01:01,756 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMZ
2025-06-26 12:01:02,178 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMZ
2025-06-26 12:01:02,178 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153237.01, 'new_value': 160789.44}, {'field': 'offline_amount', 'old_value': 17648.95, 'new_value': 18456.95}, {'field': 'total_amount', 'old_value': 170885.96, 'new_value': 179246.39}, {'field': 'order_count', 'old_value': 5086, 'new_value': 5326}]
2025-06-26 12:01:02,178 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-26 12:01:02,631 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-26 12:01:02,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97997.21, 'new_value': 102523.64}, {'field': 'total_amount', 'old_value': 99417.88, 'new_value': 103944.31}, {'field': 'order_count', 'old_value': 2344, 'new_value': 2417}]
2025-06-26 12:01:02,631 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-26 12:01:03,147 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-26 12:01:03,147 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111209.33, 'new_value': 113221.43}, {'field': 'offline_amount', 'old_value': 1089144.08, 'new_value': 1131378.08}, {'field': 'total_amount', 'old_value': 1200353.41, 'new_value': 1244599.51}, {'field': 'order_count', 'old_value': 10104, 'new_value': 10539}]
2025-06-26 12:01:03,147 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-26 12:01:03,678 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-26 12:01:03,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178569.0, 'new_value': 185362.0}, {'field': 'total_amount', 'old_value': 178569.0, 'new_value': 185362.0}, {'field': 'order_count', 'old_value': 6882, 'new_value': 7152}]
2025-06-26 12:01:03,678 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-26 12:01:04,147 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-26 12:01:04,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144820.14, 'new_value': 149929.04}, {'field': 'total_amount', 'old_value': 144820.14, 'new_value': 149929.04}, {'field': 'order_count', 'old_value': 578, 'new_value': 598}]
2025-06-26 12:01:04,147 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-26 12:01:04,662 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-26 12:01:04,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124117.5, 'new_value': 128564.5}, {'field': 'offline_amount', 'old_value': 49359.06, 'new_value': 50922.06}, {'field': 'total_amount', 'old_value': 173476.56, 'new_value': 179486.56}, {'field': 'order_count', 'old_value': 1237, 'new_value': 1290}]
2025-06-26 12:01:04,662 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-26 12:01:05,131 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-26 12:01:05,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241735.0, 'new_value': 254513.0}, {'field': 'total_amount', 'old_value': 250597.0, 'new_value': 263375.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-06-26 12:01:05,131 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-26 12:01:05,647 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-26 12:01:05,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129354.4, 'new_value': 134982.51}, {'field': 'total_amount', 'old_value': 129354.4, 'new_value': 134982.51}, {'field': 'order_count', 'old_value': 4578, 'new_value': 4786}]
2025-06-26 12:01:05,647 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-26 12:01:06,100 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-26 12:01:06,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8064.0, 'new_value': 17332.0}, {'field': 'total_amount', 'old_value': 8558.0, 'new_value': 17826.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 14}]
2025-06-26 12:01:06,100 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-26 12:01:06,553 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-26 12:01:06,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35563.5, 'new_value': 36292.5}, {'field': 'total_amount', 'old_value': 35563.5, 'new_value': 36292.5}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-06-26 12:01:06,553 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-26 12:01:07,069 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-26 12:01:07,069 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58254.0, 'new_value': 59558.0}, {'field': 'offline_amount', 'old_value': 172933.0, 'new_value': 175230.0}, {'field': 'total_amount', 'old_value': 231187.0, 'new_value': 234788.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 182}]
2025-06-26 12:01:07,069 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-26 12:01:07,569 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-26 12:01:07,569 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16879.37, 'new_value': 17975.65}, {'field': 'offline_amount', 'old_value': 367815.19, 'new_value': 387974.66}, {'field': 'total_amount', 'old_value': 384694.56, 'new_value': 405950.31}, {'field': 'order_count', 'old_value': 1864, 'new_value': 1950}]
2025-06-26 12:01:07,569 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM101
2025-06-26 12:01:08,084 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM101
2025-06-26 12:01:08,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112476.83, 'new_value': 115476.83}, {'field': 'total_amount', 'old_value': 112476.83, 'new_value': 115476.83}, {'field': 'order_count', 'old_value': 1565, 'new_value': 1665}]
2025-06-26 12:01:08,084 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-26 12:01:08,553 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-26 12:01:08,553 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6434.0, 'new_value': 6670.0}, {'field': 'offline_amount', 'old_value': 54616.0, 'new_value': 54930.0}, {'field': 'total_amount', 'old_value': 61050.0, 'new_value': 61600.0}, {'field': 'order_count', 'old_value': 503, 'new_value': 510}]
2025-06-26 12:01:08,553 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM501
2025-06-26 12:01:09,053 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM501
2025-06-26 12:01:09,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35942.0, 'new_value': 38804.0}, {'field': 'total_amount', 'old_value': 38195.0, 'new_value': 41057.0}, {'field': 'order_count', 'old_value': 173, 'new_value': 184}]
2025-06-26 12:01:09,053 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-26 12:01:09,537 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-26 12:01:09,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149076.0, 'new_value': 155064.63}, {'field': 'total_amount', 'old_value': 149076.0, 'new_value': 155064.63}, {'field': 'order_count', 'old_value': 3908, 'new_value': 4074}]
2025-06-26 12:01:09,537 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-26 12:01:10,069 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-26 12:01:10,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 522143.01, 'new_value': 560140.01}, {'field': 'total_amount', 'old_value': 615143.01, 'new_value': 653140.01}, {'field': 'order_count', 'old_value': 63, 'new_value': 70}]
2025-06-26 12:01:10,069 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDB
2025-06-26 12:01:10,537 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDB
2025-06-26 12:01:10,537 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28640.0, 'new_value': 28941.0}, {'field': 'offline_amount', 'old_value': 232596.0, 'new_value': 245294.0}, {'field': 'total_amount', 'old_value': 261236.0, 'new_value': 274235.0}, {'field': 'order_count', 'old_value': 216, 'new_value': 232}]
2025-06-26 12:01:10,537 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-26 12:01:11,006 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-26 12:01:11,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121949.54, 'new_value': 127505.21}, {'field': 'total_amount', 'old_value': 121949.54, 'new_value': 127505.21}, {'field': 'order_count', 'old_value': 1548, 'new_value': 1610}]
2025-06-26 12:01:11,006 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-26 12:01:11,428 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-26 12:01:11,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64322.46, 'new_value': 67839.61}, {'field': 'total_amount', 'old_value': 64322.46, 'new_value': 67839.61}, {'field': 'order_count', 'old_value': 274, 'new_value': 291}]
2025-06-26 12:01:11,428 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-26 12:01:11,944 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-26 12:01:11,944 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9256.9, 'new_value': 9355.9}, {'field': 'total_amount', 'old_value': 19156.9, 'new_value': 19255.9}, {'field': 'order_count', 'old_value': 63, 'new_value': 64}]
2025-06-26 12:01:11,944 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-26 12:01:12,491 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-26 12:01:12,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12651.29, 'new_value': 13609.29}, {'field': 'offline_amount', 'old_value': 23728.83, 'new_value': 24476.83}, {'field': 'total_amount', 'old_value': 36380.12, 'new_value': 38086.12}, {'field': 'order_count', 'old_value': 1254, 'new_value': 1294}]
2025-06-26 12:01:12,491 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-26 12:01:12,975 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-26 12:01:12,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9313.7, 'new_value': 10110.83}, {'field': 'offline_amount', 'old_value': 188871.52, 'new_value': 197450.19}, {'field': 'total_amount', 'old_value': 198185.22, 'new_value': 207561.02}, {'field': 'order_count', 'old_value': 1131, 'new_value': 1182}]
2025-06-26 12:01:12,975 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-26 12:01:13,444 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-26 12:01:13,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 187564.88, 'new_value': 194874.3}, {'field': 'offline_amount', 'old_value': 366335.65, 'new_value': 374922.23}, {'field': 'total_amount', 'old_value': 553900.53, 'new_value': 569796.53}, {'field': 'order_count', 'old_value': 4197, 'new_value': 4358}]
2025-06-26 12:01:13,444 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-26 12:01:13,834 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-26 12:01:13,834 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23346.01, 'new_value': 23454.01}, {'field': 'total_amount', 'old_value': 26891.01, 'new_value': 26999.01}, {'field': 'order_count', 'old_value': 110, 'new_value': 113}]
2025-06-26 12:01:13,834 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-26 12:01:14,334 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-26 12:01:14,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40676.0, 'new_value': 40974.0}, {'field': 'total_amount', 'old_value': 40676.0, 'new_value': 40974.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-26 12:01:14,334 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-26 12:01:14,803 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-26 12:01:14,803 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 381506.1, 'new_value': 395917.4}, {'field': 'offline_amount', 'old_value': 80642.5, 'new_value': 84934.2}, {'field': 'total_amount', 'old_value': 462148.6, 'new_value': 480851.6}, {'field': 'order_count', 'old_value': 581, 'new_value': 603}]
2025-06-26 12:01:14,803 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-26 12:01:15,303 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-26 12:01:15,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9457.0, 'new_value': 10475.0}, {'field': 'total_amount', 'old_value': 9457.0, 'new_value': 10475.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-26 12:01:15,303 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-26 12:01:15,756 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-26 12:01:15,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26054.96, 'new_value': 29357.16}, {'field': 'total_amount', 'old_value': 26054.96, 'new_value': 29357.16}, {'field': 'order_count', 'old_value': 1038, 'new_value': 1096}]
2025-06-26 12:01:15,756 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-26 12:01:16,209 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-26 12:01:16,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20921.3, 'new_value': 21746.3}, {'field': 'total_amount', 'old_value': 20921.3, 'new_value': 21746.3}, {'field': 'order_count', 'old_value': 112, 'new_value': 115}]
2025-06-26 12:01:16,209 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-26 12:01:16,694 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-26 12:01:16,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71388.0, 'new_value': 75387.0}, {'field': 'total_amount', 'old_value': 71388.0, 'new_value': 75387.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-26 12:01:16,694 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-26 12:01:17,116 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-26 12:01:17,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89675.0, 'new_value': 89849.0}, {'field': 'total_amount', 'old_value': 89675.0, 'new_value': 89849.0}, {'field': 'order_count', 'old_value': 320, 'new_value': 322}]
2025-06-26 12:01:17,116 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-26 12:01:17,694 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-26 12:01:17,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 473143.98, 'new_value': 495521.88}, {'field': 'total_amount', 'old_value': 473143.98, 'new_value': 495521.88}, {'field': 'order_count', 'old_value': 3602, 'new_value': 3782}]
2025-06-26 12:01:17,694 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3C
2025-06-26 12:01:18,131 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3C
2025-06-26 12:01:18,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3745.01, 'new_value': 3831.66}, {'field': 'offline_amount', 'old_value': 113965.46, 'new_value': 118183.76}, {'field': 'total_amount', 'old_value': 117710.47, 'new_value': 122015.42}, {'field': 'order_count', 'old_value': 571, 'new_value': 591}]
2025-06-26 12:01:18,131 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-26 12:01:18,569 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-26 12:01:18,569 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3800.0, 'new_value': 11800.0}, {'field': 'total_amount', 'old_value': 63400.0, 'new_value': 71400.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-26 12:01:18,569 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-26 12:01:19,022 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-26 12:01:19,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99777.38, 'new_value': 103091.88}, {'field': 'offline_amount', 'old_value': 481718.2, 'new_value': 502212.98}, {'field': 'total_amount', 'old_value': 581495.58, 'new_value': 605304.86}, {'field': 'order_count', 'old_value': 1626, 'new_value': 1692}]
2025-06-26 12:01:19,022 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-26 12:01:19,444 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-26 12:01:19,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43180.0, 'new_value': 43594.0}, {'field': 'total_amount', 'old_value': 43180.0, 'new_value': 43594.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-06-26 12:01:19,444 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-26 12:01:19,959 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-26 12:01:19,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99235.7, 'new_value': 104593.04}, {'field': 'total_amount', 'old_value': 99235.7, 'new_value': 104593.04}, {'field': 'order_count', 'old_value': 2808, 'new_value': 2954}]
2025-06-26 12:01:19,959 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAC
2025-06-26 12:01:20,444 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAC
2025-06-26 12:01:20,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134706.21, 'new_value': 141683.89}, {'field': 'offline_amount', 'old_value': 99903.18, 'new_value': 106004.54}, {'field': 'total_amount', 'old_value': 234609.39, 'new_value': 247688.43}, {'field': 'order_count', 'old_value': 9693, 'new_value': 10208}]
2025-06-26 12:01:20,444 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-26 12:01:20,834 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-26 12:01:20,834 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14739.3, 'new_value': 14789.8}, {'field': 'offline_amount', 'old_value': 35032.8, 'new_value': 35730.8}, {'field': 'total_amount', 'old_value': 49772.1, 'new_value': 50520.6}, {'field': 'order_count', 'old_value': 159, 'new_value': 166}]
2025-06-26 12:01:20,834 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFC
2025-06-26 12:01:21,303 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFC
2025-06-26 12:01:21,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3806.16, 'new_value': 5306.16}, {'field': 'total_amount', 'old_value': 54443.7, 'new_value': 55943.7}, {'field': 'order_count', 'old_value': 3506, 'new_value': 3621}]
2025-06-26 12:01:21,303 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-26 12:01:21,787 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-26 12:01:21,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54388.92, 'new_value': 55872.87}, {'field': 'total_amount', 'old_value': 61170.17, 'new_value': 62654.12}]
2025-06-26 12:01:21,787 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-26 12:01:22,209 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-26 12:01:22,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23548.0, 'new_value': 24262.0}, {'field': 'total_amount', 'old_value': 23548.0, 'new_value': 24262.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 96}]
2025-06-26 12:01:22,209 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIC
2025-06-26 12:01:22,662 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIC
2025-06-26 12:01:22,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148381.0, 'new_value': 148569.0}, {'field': 'total_amount', 'old_value': 241636.0, 'new_value': 241824.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-06-26 12:01:22,662 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-26 12:01:23,115 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-26 12:01:23,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 182570.18, 'new_value': 186549.77}, {'field': 'offline_amount', 'old_value': 81022.15, 'new_value': 83911.95}, {'field': 'total_amount', 'old_value': 263592.33, 'new_value': 270461.72}, {'field': 'order_count', 'old_value': 1129, 'new_value': 1155}]
2025-06-26 12:01:23,115 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-26 12:01:23,584 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-26 12:01:23,584 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104523.0, 'new_value': 117616.0}, {'field': 'offline_amount', 'old_value': 259655.0, 'new_value': 262139.0}, {'field': 'total_amount', 'old_value': 364178.0, 'new_value': 379755.0}, {'field': 'order_count', 'old_value': 2690, 'new_value': 2822}]
2025-06-26 12:01:23,584 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-26 12:01:24,100 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-26 12:01:24,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127296.0, 'new_value': 135281.0}, {'field': 'total_amount', 'old_value': 127296.0, 'new_value': 135281.0}, {'field': 'order_count', 'old_value': 184, 'new_value': 185}]
2025-06-26 12:01:24,100 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-26 12:01:24,522 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-26 12:01:24,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 352683.19, 'new_value': 366914.9}, {'field': 'total_amount', 'old_value': 352683.19, 'new_value': 366914.9}, {'field': 'order_count', 'old_value': 1124, 'new_value': 1170}]
2025-06-26 12:01:24,522 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-26 12:01:24,912 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-26 12:01:24,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42190.0, 'new_value': 43808.0}, {'field': 'total_amount', 'old_value': 42559.0, 'new_value': 44177.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 68}]
2025-06-26 12:01:24,912 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-26 12:01:25,444 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-26 12:01:25,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8909.9, 'new_value': 9431.5}, {'field': 'total_amount', 'old_value': 20747.67, 'new_value': 21269.27}, {'field': 'order_count', 'old_value': 96, 'new_value': 99}]
2025-06-26 12:01:25,444 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTC
2025-06-26 12:01:25,975 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTC
2025-06-26 12:01:25,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 499904.51, 'new_value': 516737.81}, {'field': 'total_amount', 'old_value': 499904.51, 'new_value': 516737.81}, {'field': 'order_count', 'old_value': 5753, 'new_value': 5984}]
2025-06-26 12:01:25,975 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-26 12:01:26,381 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-26 12:01:26,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8138.95, 'new_value': 8205.85}, {'field': 'offline_amount', 'old_value': 59801.0, 'new_value': 59862.0}, {'field': 'total_amount', 'old_value': 67939.95, 'new_value': 68067.85}, {'field': 'order_count', 'old_value': 1565, 'new_value': 1573}]
2025-06-26 12:01:26,381 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-26 12:01:26,803 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-26 12:01:26,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23088.0, 'new_value': 23607.0}, {'field': 'total_amount', 'old_value': 23088.0, 'new_value': 23607.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 118}]
2025-06-26 12:01:26,803 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-26 12:01:27,334 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-26 12:01:27,334 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70401.0, 'new_value': 73887.0}, {'field': 'offline_amount', 'old_value': 22240.8, 'new_value': 23685.0}, {'field': 'total_amount', 'old_value': 92641.8, 'new_value': 97572.0}, {'field': 'order_count', 'old_value': 317, 'new_value': 336}]
2025-06-26 12:01:27,334 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-26 12:01:27,787 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-26 12:01:27,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15716.0, 'new_value': 16117.0}, {'field': 'offline_amount', 'old_value': 66581.15, 'new_value': 68661.25}, {'field': 'total_amount', 'old_value': 82297.15, 'new_value': 84778.25}, {'field': 'order_count', 'old_value': 817, 'new_value': 850}]
2025-06-26 12:01:27,803 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-26 12:01:28,240 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-26 12:01:28,240 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5706.0, 'new_value': 6253.0}, {'field': 'offline_amount', 'old_value': 28279.1, 'new_value': 29858.4}, {'field': 'total_amount', 'old_value': 33985.1, 'new_value': 36111.4}, {'field': 'order_count', 'old_value': 1206, 'new_value': 1261}]
2025-06-26 12:01:28,240 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-26 12:01:28,725 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-26 12:01:28,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23891.55, 'new_value': 25076.77}, {'field': 'offline_amount', 'old_value': 46120.72, 'new_value': 47590.36}, {'field': 'total_amount', 'old_value': 70012.27, 'new_value': 72667.13}, {'field': 'order_count', 'old_value': 2595, 'new_value': 2693}]
2025-06-26 12:01:28,725 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-26 12:01:29,147 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-26 12:01:29,147 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24295.2, 'new_value': 24643.2}, {'field': 'offline_amount', 'old_value': 99176.6, 'new_value': 103057.6}, {'field': 'total_amount', 'old_value': 123471.8, 'new_value': 127700.8}, {'field': 'order_count', 'old_value': 152, 'new_value': 158}]
2025-06-26 12:01:29,147 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-26 12:01:29,678 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-26 12:01:29,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23164.0, 'new_value': 23556.0}, {'field': 'total_amount', 'old_value': 23224.0, 'new_value': 23616.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 101}]
2025-06-26 12:01:29,678 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-26 12:01:30,147 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-26 12:01:30,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18363.0, 'new_value': 18841.0}, {'field': 'total_amount', 'old_value': 18363.0, 'new_value': 18841.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 106}]
2025-06-26 12:01:30,147 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-26 12:01:30,584 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-26 12:01:30,584 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90510.96, 'new_value': 94568.76}, {'field': 'offline_amount', 'old_value': 135539.19, 'new_value': 137294.54}, {'field': 'total_amount', 'old_value': 226050.15, 'new_value': 231863.3}, {'field': 'order_count', 'old_value': 1268, 'new_value': 1284}]
2025-06-26 12:01:30,584 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-26 12:01:31,022 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-26 12:01:31,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72558.72, 'new_value': 74854.38}, {'field': 'total_amount', 'old_value': 72558.72, 'new_value': 74854.38}, {'field': 'order_count', 'old_value': 1958, 'new_value': 2030}]
2025-06-26 12:01:31,022 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-26 12:01:32,709 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-26 12:01:32,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39397.17, 'new_value': 39564.76}, {'field': 'offline_amount', 'old_value': 54532.38, 'new_value': 56343.5}, {'field': 'total_amount', 'old_value': 93929.55, 'new_value': 95908.26}, {'field': 'order_count', 'old_value': 561, 'new_value': 571}]
2025-06-26 12:01:32,709 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-26 12:01:33,147 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-26 12:01:33,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16629.0, 'new_value': 17434.0}, {'field': 'total_amount', 'old_value': 16629.0, 'new_value': 17434.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 51}]
2025-06-26 12:01:33,147 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-26 12:01:33,600 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-26 12:01:33,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13109.83, 'new_value': 13856.2}, {'field': 'offline_amount', 'old_value': 200815.22, 'new_value': 210956.22}, {'field': 'total_amount', 'old_value': 213925.05, 'new_value': 224812.42}, {'field': 'order_count', 'old_value': 1423, 'new_value': 1503}]
2025-06-26 12:01:33,600 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-26 12:01:34,069 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-26 12:01:34,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37832.8, 'new_value': 41129.1}, {'field': 'total_amount', 'old_value': 37832.8, 'new_value': 41129.1}, {'field': 'order_count', 'old_value': 41, 'new_value': 45}]
2025-06-26 12:01:34,069 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-26 12:01:34,600 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-26 12:01:34,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 298028.85, 'new_value': 311440.72}, {'field': 'total_amount', 'old_value': 298028.85, 'new_value': 311440.72}, {'field': 'order_count', 'old_value': 8722, 'new_value': 9158}]
2025-06-26 12:01:34,600 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-26 12:01:35,022 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-26 12:01:35,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84302.5, 'new_value': 86751.6}, {'field': 'total_amount', 'old_value': 84302.5, 'new_value': 86751.6}, {'field': 'order_count', 'old_value': 277, 'new_value': 285}]
2025-06-26 12:01:35,022 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPD
2025-06-26 12:01:35,459 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPD
2025-06-26 12:01:35,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85602.66, 'new_value': 89747.35}, {'field': 'offline_amount', 'old_value': 41655.08, 'new_value': 43150.33}, {'field': 'total_amount', 'old_value': 127257.74, 'new_value': 132897.68}, {'field': 'order_count', 'old_value': 4708, 'new_value': 4913}]
2025-06-26 12:01:35,459 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-26 12:01:35,990 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-26 12:01:35,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166075.47, 'new_value': 174876.25}, {'field': 'offline_amount', 'old_value': 141903.26, 'new_value': 148857.61}, {'field': 'total_amount', 'old_value': 307978.73, 'new_value': 323733.86}, {'field': 'order_count', 'old_value': 2894, 'new_value': 3065}]
2025-06-26 12:01:35,990 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-26 12:01:36,537 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-26 12:01:36,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10055.0, 'new_value': 10555.0}, {'field': 'total_amount', 'old_value': 10055.0, 'new_value': 10555.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 55}]
2025-06-26 12:01:36,537 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-26 12:01:37,053 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-26 12:01:37,053 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40668.0, 'new_value': 42288.0}, {'field': 'total_amount', 'old_value': 75874.0, 'new_value': 77494.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 95}]
2025-06-26 12:01:37,053 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-26 12:01:37,444 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-26 12:01:37,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134061.0, 'new_value': 155535.0}, {'field': 'total_amount', 'old_value': 265395.0, 'new_value': 286869.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 148}]
2025-06-26 12:01:37,444 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-26 12:01:37,912 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-26 12:01:37,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83702.0, 'new_value': 87892.0}, {'field': 'total_amount', 'old_value': 83702.0, 'new_value': 87892.0}, {'field': 'order_count', 'old_value': 882, 'new_value': 883}]
2025-06-26 12:01:37,912 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-26 12:01:38,412 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-26 12:01:38,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73506.15, 'new_value': 75438.26}, {'field': 'total_amount', 'old_value': 73506.15, 'new_value': 75438.26}, {'field': 'order_count', 'old_value': 2370, 'new_value': 2438}]
2025-06-26 12:01:38,412 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-26 12:01:38,850 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-26 12:01:38,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45722.0, 'new_value': 48612.0}, {'field': 'total_amount', 'old_value': 45722.0, 'new_value': 48612.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 33}]
2025-06-26 12:01:38,850 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-26 12:01:39,303 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-26 12:01:39,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26943.99, 'new_value': 27591.39}, {'field': 'offline_amount', 'old_value': 255268.25, 'new_value': 267599.15}, {'field': 'total_amount', 'old_value': 282212.24, 'new_value': 295190.54}, {'field': 'order_count', 'old_value': 15578, 'new_value': 16296}]
2025-06-26 12:01:39,303 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-26 12:01:39,772 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-26 12:01:39,772 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8198.7, 'new_value': 8602.5}, {'field': 'offline_amount', 'old_value': 66372.0, 'new_value': 69252.0}, {'field': 'total_amount', 'old_value': 74570.7, 'new_value': 77854.5}, {'field': 'order_count', 'old_value': 64, 'new_value': 66}]
2025-06-26 12:01:39,772 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXQ
2025-06-26 12:01:40,225 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXQ
2025-06-26 12:01:40,225 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79854.22, 'new_value': 82634.66}, {'field': 'offline_amount', 'old_value': 59510.31, 'new_value': 62315.75}, {'field': 'total_amount', 'old_value': 139364.53, 'new_value': 144950.41}, {'field': 'order_count', 'old_value': 6215, 'new_value': 6464}]
2025-06-26 12:01:40,225 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-26 12:01:40,693 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-26 12:01:40,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45068.0, 'new_value': 45196.0}, {'field': 'total_amount', 'old_value': 45068.0, 'new_value': 45196.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 111}]
2025-06-26 12:01:40,693 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-26 12:01:41,162 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-26 12:01:41,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122546.0, 'new_value': 127094.0}, {'field': 'total_amount', 'old_value': 122546.0, 'new_value': 127094.0}, {'field': 'order_count', 'old_value': 3508, 'new_value': 3639}]
2025-06-26 12:01:41,162 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-26 12:01:41,600 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-26 12:01:41,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31460.69, 'new_value': 32527.69}, {'field': 'total_amount', 'old_value': 31460.69, 'new_value': 32527.69}, {'field': 'order_count', 'old_value': 187, 'new_value': 193}]
2025-06-26 12:01:41,600 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-26 12:01:42,068 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-26 12:01:42,068 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15070.0, 'new_value': 15998.0}, {'field': 'total_amount', 'old_value': 15070.0, 'new_value': 15998.0}, {'field': 'order_count', 'old_value': 257, 'new_value': 273}]
2025-06-26 12:01:42,068 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-26 12:01:42,537 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-26 12:01:42,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146092.0, 'new_value': 151745.0}, {'field': 'total_amount', 'old_value': 146092.0, 'new_value': 151745.0}, {'field': 'order_count', 'old_value': 558, 'new_value': 573}]
2025-06-26 12:01:42,537 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-26 12:01:43,037 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-26 12:01:43,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34803.24, 'new_value': 36312.47}, {'field': 'offline_amount', 'old_value': 26741.0, 'new_value': 28247.0}, {'field': 'total_amount', 'old_value': 61544.24, 'new_value': 64559.47}, {'field': 'order_count', 'old_value': 827, 'new_value': 866}]
2025-06-26 12:01:43,037 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-26 12:01:43,506 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-26 12:01:43,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68862.12, 'new_value': 70327.46}, {'field': 'total_amount', 'old_value': 68862.12, 'new_value': 70327.46}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-06-26 12:01:43,506 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHR
2025-06-26 12:01:43,990 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHR
2025-06-26 12:01:43,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29769.55, 'new_value': 30884.69}, {'field': 'offline_amount', 'old_value': 61625.96, 'new_value': 64702.82}, {'field': 'total_amount', 'old_value': 91395.51, 'new_value': 95587.51}, {'field': 'order_count', 'old_value': 809, 'new_value': 845}]
2025-06-26 12:01:43,990 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-26 12:01:44,459 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-26 12:01:44,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211438.76, 'new_value': 218518.76}, {'field': 'total_amount', 'old_value': 211438.76, 'new_value': 218518.76}, {'field': 'order_count', 'old_value': 1179, 'new_value': 1219}]
2025-06-26 12:01:44,459 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-26 12:01:44,912 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-26 12:01:44,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339584.0, 'new_value': 344930.0}, {'field': 'total_amount', 'old_value': 339584.0, 'new_value': 344930.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 86}]
2025-06-26 12:01:44,912 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-26 12:01:45,428 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-26 12:01:45,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94310.29, 'new_value': 97721.23}, {'field': 'offline_amount', 'old_value': 48663.9, 'new_value': 50127.68}, {'field': 'total_amount', 'old_value': 142974.19, 'new_value': 147848.91}, {'field': 'order_count', 'old_value': 8250, 'new_value': 8545}]
2025-06-26 12:01:45,428 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-26 12:01:45,928 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-26 12:01:45,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103834.7, 'new_value': 105196.6}, {'field': 'total_amount', 'old_value': 103834.7, 'new_value': 105196.6}, {'field': 'order_count', 'old_value': 259, 'new_value': 263}]
2025-06-26 12:01:45,928 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-26 12:01:46,365 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-26 12:01:46,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62954.25, 'new_value': 71929.25}, {'field': 'total_amount', 'old_value': 62954.25, 'new_value': 71929.25}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-26 12:01:46,365 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-26 12:01:46,787 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-26 12:01:46,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25208.7, 'new_value': 25654.7}, {'field': 'total_amount', 'old_value': 25208.7, 'new_value': 25654.7}, {'field': 'order_count', 'old_value': 239, 'new_value': 244}]
2025-06-26 12:01:46,787 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-26 12:01:47,225 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-26 12:01:47,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51921.9, 'new_value': 52511.6}, {'field': 'total_amount', 'old_value': 51921.9, 'new_value': 52511.6}, {'field': 'order_count', 'old_value': 317, 'new_value': 319}]
2025-06-26 12:01:47,225 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-26 12:01:47,756 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-26 12:01:47,756 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55961.31, 'new_value': 58110.69}, {'field': 'offline_amount', 'old_value': 75169.53, 'new_value': 77001.97}, {'field': 'total_amount', 'old_value': 131130.84, 'new_value': 135112.66}, {'field': 'order_count', 'old_value': 5147, 'new_value': 5319}]
2025-06-26 12:01:47,756 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVR
2025-06-26 12:01:48,225 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVR
2025-06-26 12:01:48,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 206662.41, 'new_value': 218804.99}, {'field': 'total_amount', 'old_value': 256450.72, 'new_value': 268593.3}, {'field': 'order_count', 'old_value': 6393, 'new_value': 6700}]
2025-06-26 12:01:48,225 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-26 12:01:48,693 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-26 12:01:48,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85146.91, 'new_value': 87159.09}, {'field': 'total_amount', 'old_value': 85146.91, 'new_value': 87159.09}, {'field': 'order_count', 'old_value': 3171, 'new_value': 3250}]
2025-06-26 12:01:48,693 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-26 12:01:49,115 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-26 12:01:49,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372219.58, 'new_value': 378975.88}, {'field': 'total_amount', 'old_value': 372219.58, 'new_value': 378975.88}, {'field': 'order_count', 'old_value': 1955, 'new_value': 1997}]
2025-06-26 12:01:49,115 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-26 12:01:49,600 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-26 12:01:49,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23656.19, 'new_value': 24704.19}, {'field': 'total_amount', 'old_value': 24156.19, 'new_value': 25204.19}, {'field': 'order_count', 'old_value': 106, 'new_value': 113}]
2025-06-26 12:01:49,600 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-26 12:01:50,068 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-26 12:01:50,068 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39537.65, 'new_value': 41474.85}, {'field': 'offline_amount', 'old_value': 18378.2, 'new_value': 19299.16}, {'field': 'total_amount', 'old_value': 57915.85, 'new_value': 60774.01}, {'field': 'order_count', 'old_value': 2594, 'new_value': 2733}]
2025-06-26 12:01:50,068 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-26 12:01:50,553 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-26 12:01:50,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44510.79, 'new_value': 46534.89}, {'field': 'total_amount', 'old_value': 44519.79, 'new_value': 46543.89}, {'field': 'order_count', 'old_value': 1852, 'new_value': 1918}]
2025-06-26 12:01:50,553 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-26 12:01:51,022 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-26 12:01:51,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16442.71, 'new_value': 17468.08}, {'field': 'offline_amount', 'old_value': 10576.89, 'new_value': 10849.79}, {'field': 'total_amount', 'old_value': 27019.6, 'new_value': 28317.87}, {'field': 'order_count', 'old_value': 2201, 'new_value': 2291}]
2025-06-26 12:01:51,022 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-26 12:01:51,490 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-26 12:01:51,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 336297.92, 'new_value': 346918.92}, {'field': 'total_amount', 'old_value': 372281.92, 'new_value': 382902.92}, {'field': 'order_count', 'old_value': 1946, 'new_value': 2011}]
2025-06-26 12:01:51,490 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-26 12:01:51,943 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-26 12:01:51,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259537.0, 'new_value': 281182.0}, {'field': 'total_amount', 'old_value': 294537.0, 'new_value': 316182.0}, {'field': 'order_count', 'old_value': 6913, 'new_value': 7223}]
2025-06-26 12:01:51,943 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-26 12:01:52,397 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-26 12:01:52,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70363.0, 'new_value': 73754.0}, {'field': 'total_amount', 'old_value': 73178.0, 'new_value': 76569.0}, {'field': 'order_count', 'old_value': 286, 'new_value': 296}]
2025-06-26 12:01:52,397 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-26 12:01:52,881 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-26 12:01:52,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 603123.62, 'new_value': 620872.66}, {'field': 'total_amount', 'old_value': 603123.62, 'new_value': 620872.66}, {'field': 'order_count', 'old_value': 10406, 'new_value': 10664}]
2025-06-26 12:01:52,881 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-26 12:01:53,318 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-26 12:01:53,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37964.1, 'new_value': 40092.25}, {'field': 'offline_amount', 'old_value': 258741.76, 'new_value': 265391.16}, {'field': 'total_amount', 'old_value': 296705.86, 'new_value': 305483.41}, {'field': 'order_count', 'old_value': 35936, 'new_value': 36130}]
2025-06-26 12:01:53,318 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-26 12:01:53,740 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-26 12:01:53,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 179404.97, 'new_value': 187198.55}, {'field': 'offline_amount', 'old_value': 41080.68, 'new_value': 42057.35}, {'field': 'total_amount', 'old_value': 220485.65, 'new_value': 229255.9}, {'field': 'order_count', 'old_value': 888, 'new_value': 920}]
2025-06-26 12:01:53,740 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-26 12:01:54,225 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-26 12:01:54,225 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79276.29, 'new_value': 82287.29}, {'field': 'total_amount', 'old_value': 79276.29, 'new_value': 82287.29}, {'field': 'order_count', 'old_value': 388, 'new_value': 403}]
2025-06-26 12:01:54,225 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-26 12:01:54,740 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-26 12:01:54,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55819.0, 'new_value': 59429.0}, {'field': 'total_amount', 'old_value': 55819.0, 'new_value': 59429.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 42}]
2025-06-26 12:01:54,740 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-26 12:01:55,209 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-26 12:01:55,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 545503.62, 'new_value': 574264.52}, {'field': 'total_amount', 'old_value': 545503.62, 'new_value': 574264.52}, {'field': 'order_count', 'old_value': 1769, 'new_value': 1854}]
2025-06-26 12:01:55,209 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-26 12:01:55,693 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-26 12:01:55,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191272.0, 'new_value': 201272.0}, {'field': 'total_amount', 'old_value': 239256.0, 'new_value': 249256.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-06-26 12:01:55,693 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-26 12:01:56,178 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-26 12:01:56,193 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159687.8, 'new_value': 166327.2}, {'field': 'total_amount', 'old_value': 159776.8, 'new_value': 166416.2}, {'field': 'order_count', 'old_value': 1993, 'new_value': 2098}]
2025-06-26 12:01:56,193 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-26 12:01:56,662 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-26 12:01:56,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41415.1, 'new_value': 43159.1}, {'field': 'total_amount', 'old_value': 41415.1, 'new_value': 43159.1}, {'field': 'order_count', 'old_value': 100, 'new_value': 104}]
2025-06-26 12:01:56,662 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-26 12:01:57,240 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-26 12:01:57,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37345.0, 'new_value': 38053.0}, {'field': 'total_amount', 'old_value': 38073.0, 'new_value': 38781.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 144}]
2025-06-26 12:01:57,240 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOS
2025-06-26 12:01:57,678 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOS
2025-06-26 12:01:57,678 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38732.07, 'new_value': 40120.49}, {'field': 'offline_amount', 'old_value': 303045.6, 'new_value': 314012.55}, {'field': 'total_amount', 'old_value': 341777.67, 'new_value': 354133.04}, {'field': 'order_count', 'old_value': 22678, 'new_value': 23069}]
2025-06-26 12:01:57,678 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-26 12:01:58,131 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-26 12:01:58,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94475.0, 'new_value': 98866.0}, {'field': 'offline_amount', 'old_value': 408003.0, 'new_value': 432636.0}, {'field': 'total_amount', 'old_value': 502478.0, 'new_value': 531502.0}, {'field': 'order_count', 'old_value': 654, 'new_value': 691}]
2025-06-26 12:01:58,131 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-26 12:01:58,631 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-26 12:01:58,631 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9591.12, 'new_value': 9947.82}, {'field': 'offline_amount', 'old_value': 30470.19, 'new_value': 31194.29}, {'field': 'total_amount', 'old_value': 40061.31, 'new_value': 41142.11}, {'field': 'order_count', 'old_value': 1411, 'new_value': 1440}]
2025-06-26 12:01:58,631 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-26 12:01:59,131 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-26 12:01:59,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53414.62, 'new_value': 55925.17}, {'field': 'offline_amount', 'old_value': 330300.11, 'new_value': 342342.51}, {'field': 'total_amount', 'old_value': 383714.73, 'new_value': 398267.68}, {'field': 'order_count', 'old_value': 2385, 'new_value': 2483}]
2025-06-26 12:01:59,131 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-26 12:01:59,600 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-26 12:01:59,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36242.7, 'new_value': 37712.76}, {'field': 'offline_amount', 'old_value': 32295.94, 'new_value': 33295.94}, {'field': 'total_amount', 'old_value': 68538.64, 'new_value': 71008.7}, {'field': 'order_count', 'old_value': 3253, 'new_value': 3387}]
2025-06-26 12:01:59,600 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-26 12:02:00,084 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-26 12:02:00,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230403.6, 'new_value': 230681.6}, {'field': 'total_amount', 'old_value': 230403.6, 'new_value': 230681.6}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-06-26 12:02:00,084 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-26 12:02:00,506 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-26 12:02:00,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75359.18, 'new_value': 78230.35}, {'field': 'offline_amount', 'old_value': 30386.37, 'new_value': 31309.41}, {'field': 'total_amount', 'old_value': 105745.55, 'new_value': 109539.76}, {'field': 'order_count', 'old_value': 6131, 'new_value': 6312}]
2025-06-26 12:02:00,506 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-26 12:02:00,990 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-26 12:02:00,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 451812.72, 'new_value': 463351.27}, {'field': 'total_amount', 'old_value': 451812.72, 'new_value': 463351.27}, {'field': 'order_count', 'old_value': 6547, 'new_value': 6752}]
2025-06-26 12:02:00,990 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-26 12:02:01,459 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-26 12:02:01,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1575525.31, 'new_value': 1626459.41}, {'field': 'total_amount', 'old_value': 1634004.01, 'new_value': 1684938.11}, {'field': 'order_count', 'old_value': 3092, 'new_value': 3215}]
2025-06-26 12:02:01,459 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-26 12:02:01,959 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-26 12:02:01,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29529.73, 'new_value': 30237.73}, {'field': 'total_amount', 'old_value': 29529.73, 'new_value': 30237.73}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-06-26 12:02:01,959 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-26 12:02:02,428 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-26 12:02:02,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28052.0, 'new_value': 29252.0}, {'field': 'total_amount', 'old_value': 28052.0, 'new_value': 29252.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 178}]
2025-06-26 12:02:02,428 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-26 12:02:02,850 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-26 12:02:02,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145408.03, 'new_value': 149627.21}, {'field': 'offline_amount', 'old_value': 100907.0, 'new_value': 102577.0}, {'field': 'total_amount', 'old_value': 246315.03, 'new_value': 252204.21}, {'field': 'order_count', 'old_value': 2505, 'new_value': 2572}]
2025-06-26 12:02:02,850 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-26 12:02:03,412 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-26 12:02:03,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 539087.43, 'new_value': 571455.83}, {'field': 'total_amount', 'old_value': 539402.79, 'new_value': 571771.19}, {'field': 'order_count', 'old_value': 1451, 'new_value': 1529}]
2025-06-26 12:02:03,412 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-26 12:02:03,834 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-26 12:02:03,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186784.92, 'new_value': 195545.44}, {'field': 'total_amount', 'old_value': 246465.83, 'new_value': 255226.35}, {'field': 'order_count', 'old_value': 10965, 'new_value': 11365}]
2025-06-26 12:02:03,834 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-26 12:02:04,350 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-26 12:02:04,350 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75332.7, 'new_value': 78110.02}, {'field': 'offline_amount', 'old_value': 61632.93, 'new_value': 63079.98}, {'field': 'total_amount', 'old_value': 136965.63, 'new_value': 141190.0}, {'field': 'order_count', 'old_value': 6408, 'new_value': 6606}]
2025-06-26 12:02:04,350 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-26 12:02:04,787 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-26 12:02:04,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263755.3, 'new_value': 276340.4}, {'field': 'total_amount', 'old_value': 263755.3, 'new_value': 276340.4}, {'field': 'order_count', 'old_value': 7974, 'new_value': 8360}]
2025-06-26 12:02:04,787 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-26 12:02:05,209 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-26 12:02:05,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44589.4, 'new_value': 45206.4}, {'field': 'total_amount', 'old_value': 44589.4, 'new_value': 45206.4}, {'field': 'order_count', 'old_value': 598, 'new_value': 607}]
2025-06-26 12:02:05,209 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-26 12:02:05,678 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-26 12:02:05,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115821.0, 'new_value': 118709.0}, {'field': 'total_amount', 'old_value': 115821.0, 'new_value': 118709.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-26 12:02:05,678 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-26 12:02:06,131 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-26 12:02:06,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 654455.0, 'new_value': 670366.0}, {'field': 'total_amount', 'old_value': 654455.0, 'new_value': 670366.0}, {'field': 'order_count', 'old_value': 3121, 'new_value': 3261}]
2025-06-26 12:02:06,131 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-26 12:02:06,568 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-26 12:02:06,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 245674.19, 'new_value': 252057.51}, {'field': 'total_amount', 'old_value': 327765.81, 'new_value': 334149.13}, {'field': 'order_count', 'old_value': 652, 'new_value': 666}]
2025-06-26 12:02:06,568 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJT
2025-06-26 12:02:07,021 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJT
2025-06-26 12:02:07,021 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40041.0, 'new_value': 43626.0}, {'field': 'total_amount', 'old_value': 40041.0, 'new_value': 43626.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 77}]
2025-06-26 12:02:07,021 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-26 12:02:07,537 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-26 12:02:07,537 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11559.98, 'new_value': 12025.58}, {'field': 'offline_amount', 'old_value': 28732.84, 'new_value': 29594.22}, {'field': 'total_amount', 'old_value': 40292.82, 'new_value': 41619.8}, {'field': 'order_count', 'old_value': 2027, 'new_value': 2116}]
2025-06-26 12:02:07,537 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-26 12:02:07,990 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-26 12:02:07,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 469061.0, 'new_value': 482653.0}, {'field': 'total_amount', 'old_value': 469061.0, 'new_value': 482653.0}, {'field': 'order_count', 'old_value': 483, 'new_value': 502}]
2025-06-26 12:02:07,990 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-26 12:02:08,459 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-26 12:02:08,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214904.0, 'new_value': 228761.0}, {'field': 'total_amount', 'old_value': 214904.0, 'new_value': 228761.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 233}]
2025-06-26 12:02:08,459 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-26 12:02:08,990 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-26 12:02:08,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34742.87, 'new_value': 34909.97}, {'field': 'total_amount', 'old_value': 36954.11, 'new_value': 37121.21}, {'field': 'order_count', 'old_value': 157, 'new_value': 158}]
2025-06-26 12:02:08,990 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-26 12:02:09,443 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-26 12:02:09,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15239.0, 'new_value': 15397.0}, {'field': 'total_amount', 'old_value': 15239.0, 'new_value': 15397.0}, {'field': 'order_count', 'old_value': 170, 'new_value': 172}]
2025-06-26 12:02:09,443 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-26 12:02:09,881 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-26 12:02:09,881 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64368.0, 'new_value': 68502.0}, {'field': 'offline_amount', 'old_value': 119756.0, 'new_value': 125630.0}, {'field': 'total_amount', 'old_value': 184124.0, 'new_value': 194132.0}, {'field': 'order_count', 'old_value': 4206, 'new_value': 4428}]
2025-06-26 12:02:09,881 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-26 12:02:10,365 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-26 12:02:10,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27742.93, 'new_value': 28796.01}, {'field': 'offline_amount', 'old_value': 25030.57, 'new_value': 26465.37}, {'field': 'total_amount', 'old_value': 52773.5, 'new_value': 55261.38}, {'field': 'order_count', 'old_value': 2460, 'new_value': 2562}]
2025-06-26 12:02:10,365 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-26 12:02:10,990 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-26 12:02:10,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15792.41, 'new_value': 21782.14}, {'field': 'offline_amount', 'old_value': 131744.75, 'new_value': 132651.72}, {'field': 'total_amount', 'old_value': 147537.16, 'new_value': 154433.86}, {'field': 'order_count', 'old_value': 7199, 'new_value': 7560}]
2025-06-26 12:02:10,990 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-26 12:02:11,443 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-26 12:02:11,443 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105772.9, 'new_value': 109891.0}, {'field': 'offline_amount', 'old_value': 129105.8, 'new_value': 130005.3}, {'field': 'total_amount', 'old_value': 234878.7, 'new_value': 239896.3}, {'field': 'order_count', 'old_value': 4250, 'new_value': 4350}]
2025-06-26 12:02:11,443 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-26 12:02:11,943 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-26 12:02:11,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55153.79, 'new_value': 57350.84}, {'field': 'offline_amount', 'old_value': 208881.01, 'new_value': 215185.29}, {'field': 'total_amount', 'old_value': 264034.8, 'new_value': 272536.13}, {'field': 'order_count', 'old_value': 5933, 'new_value': 6162}]
2025-06-26 12:02:11,943 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLK
2025-06-26 12:02:12,396 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLK
2025-06-26 12:02:12,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26288.1, 'new_value': 27111.5}, {'field': 'offline_amount', 'old_value': 187252.0, 'new_value': 194498.3}, {'field': 'total_amount', 'old_value': 213540.1, 'new_value': 221609.8}, {'field': 'order_count', 'old_value': 6482, 'new_value': 6678}]
2025-06-26 12:02:12,396 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-26 12:02:12,850 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-26 12:02:12,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6001.07, 'new_value': 6078.97}, {'field': 'offline_amount', 'old_value': 335540.57, 'new_value': 345618.47}, {'field': 'total_amount', 'old_value': 341541.64, 'new_value': 351697.44}, {'field': 'order_count', 'old_value': 16911, 'new_value': 17507}]
2025-06-26 12:02:12,850 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-26 12:02:13,396 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-26 12:02:13,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55075.98, 'new_value': 57880.55}, {'field': 'offline_amount', 'old_value': 35951.88, 'new_value': 37190.84}, {'field': 'total_amount', 'old_value': 91027.86, 'new_value': 95071.39}, {'field': 'order_count', 'old_value': 5390, 'new_value': 5617}]
2025-06-26 12:02:13,396 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-26 12:02:13,943 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-26 12:02:13,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21930.0, 'new_value': 22754.0}, {'field': 'total_amount', 'old_value': 21930.0, 'new_value': 22754.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 99}]
2025-06-26 12:02:13,943 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUK
2025-06-26 12:02:14,568 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUK
2025-06-26 12:02:14,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95114.82, 'new_value': 99797.06}, {'field': 'total_amount', 'old_value': 95114.82, 'new_value': 99797.06}, {'field': 'order_count', 'old_value': 2865, 'new_value': 3017}]
2025-06-26 12:02:14,568 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-26 12:02:15,068 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-26 12:02:15,068 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85111.01, 'new_value': 88900.74}, {'field': 'offline_amount', 'old_value': 87919.67, 'new_value': 91843.52}, {'field': 'total_amount', 'old_value': 173030.68, 'new_value': 180744.26}, {'field': 'order_count', 'old_value': 7148, 'new_value': 7463}]
2025-06-26 12:02:15,068 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-26 12:02:15,568 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-26 12:02:15,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 486290.25, 'new_value': 501972.03}, {'field': 'total_amount', 'old_value': 486290.25, 'new_value': 501972.03}, {'field': 'order_count', 'old_value': 5303, 'new_value': 5527}]
2025-06-26 12:02:15,568 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-26 12:02:16,006 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-26 12:02:16,006 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28549.46, 'new_value': 29996.58}, {'field': 'offline_amount', 'old_value': 198138.0, 'new_value': 202828.7}, {'field': 'total_amount', 'old_value': 226687.46, 'new_value': 232825.28}, {'field': 'order_count', 'old_value': 7327, 'new_value': 7544}]
2025-06-26 12:02:16,006 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-26 12:02:16,475 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-26 12:02:16,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108007.0, 'new_value': 110579.0}, {'field': 'total_amount', 'old_value': 108007.0, 'new_value': 110579.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 70}]
2025-06-26 12:02:16,490 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-26 12:02:16,975 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-26 12:02:16,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115687.0, 'new_value': 120240.0}, {'field': 'total_amount', 'old_value': 115687.0, 'new_value': 120240.0}, {'field': 'order_count', 'old_value': 426, 'new_value': 441}]
2025-06-26 12:02:16,975 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-26 12:02:17,428 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-26 12:02:17,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 284863.0, 'new_value': 292106.8}, {'field': 'offline_amount', 'old_value': 107203.6, 'new_value': 109371.6}, {'field': 'total_amount', 'old_value': 392066.6, 'new_value': 401478.4}, {'field': 'order_count', 'old_value': 1215, 'new_value': 1246}]
2025-06-26 12:02:17,428 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4L
2025-06-26 12:02:17,943 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4L
2025-06-26 12:02:17,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26014.0, 'new_value': 29109.0}, {'field': 'total_amount', 'old_value': 26014.0, 'new_value': 29109.0}, {'field': 'order_count', 'old_value': 633, 'new_value': 645}]
2025-06-26 12:02:17,943 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-26 12:02:18,349 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-26 12:02:18,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 237948.0, 'new_value': 248220.0}, {'field': 'total_amount', 'old_value': 237948.0, 'new_value': 248220.0}, {'field': 'order_count', 'old_value': 19829, 'new_value': 20685}]
2025-06-26 12:02:18,365 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-26 12:02:18,818 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-26 12:02:18,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177171.0, 'new_value': 188529.0}, {'field': 'total_amount', 'old_value': 177171.0, 'new_value': 188529.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-06-26 12:02:18,818 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-26 12:02:19,256 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-26 12:02:19,256 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108367.15, 'new_value': 112829.6}, {'field': 'offline_amount', 'old_value': 224871.74, 'new_value': 231999.34}, {'field': 'total_amount', 'old_value': 333238.89, 'new_value': 344828.94}, {'field': 'order_count', 'old_value': 11952, 'new_value': 12400}]
2025-06-26 12:02:19,256 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-26 12:02:19,771 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-26 12:02:19,771 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125513.92, 'new_value': 129861.37}, {'field': 'offline_amount', 'old_value': 326988.68, 'new_value': 341348.59}, {'field': 'total_amount', 'old_value': 452502.6, 'new_value': 471209.96}, {'field': 'order_count', 'old_value': 4454, 'new_value': 4638}]
2025-06-26 12:02:19,771 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-26 12:02:20,256 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-26 12:02:20,256 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 170691.28, 'new_value': 176204.14}, {'field': 'offline_amount', 'old_value': 208808.71, 'new_value': 209308.71}, {'field': 'total_amount', 'old_value': 379499.99, 'new_value': 385512.85}, {'field': 'order_count', 'old_value': 1202, 'new_value': 1236}]
2025-06-26 12:02:20,256 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-26 12:02:20,740 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-26 12:02:20,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163728.0, 'new_value': 171820.0}, {'field': 'total_amount', 'old_value': 163728.0, 'new_value': 171820.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 114}]
2025-06-26 12:02:20,740 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-26 12:02:21,209 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-26 12:02:21,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140752.99, 'new_value': 140766.99}, {'field': 'offline_amount', 'old_value': 289876.23, 'new_value': 302348.23}, {'field': 'total_amount', 'old_value': 430629.22, 'new_value': 443115.22}, {'field': 'order_count', 'old_value': 3687, 'new_value': 3730}]
2025-06-26 12:02:21,209 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-26 12:02:21,631 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-26 12:02:21,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207634.69, 'new_value': 215681.02}, {'field': 'total_amount', 'old_value': 207634.69, 'new_value': 215681.02}, {'field': 'order_count', 'old_value': 744, 'new_value': 774}]
2025-06-26 12:02:21,631 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-26 12:02:22,037 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-26 12:02:22,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34291.92, 'new_value': 35220.52}, {'field': 'offline_amount', 'old_value': 1007453.71, 'new_value': 1043952.4}, {'field': 'total_amount', 'old_value': 1041745.63, 'new_value': 1079172.92}, {'field': 'order_count', 'old_value': 4953, 'new_value': 5162}]
2025-06-26 12:02:22,037 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-26 12:02:22,490 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-26 12:02:22,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50300.0, 'new_value': 52848.0}, {'field': 'total_amount', 'old_value': 52912.0, 'new_value': 55460.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 129}]
2025-06-26 12:02:22,490 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-26 12:02:22,974 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-26 12:02:22,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11035.0, 'new_value': 11324.0}, {'field': 'total_amount', 'old_value': 11035.0, 'new_value': 11324.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-26 12:02:22,974 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-26 12:02:23,443 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-26 12:02:23,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145879.71, 'new_value': 150381.97}, {'field': 'total_amount', 'old_value': 145879.71, 'new_value': 150381.97}, {'field': 'order_count', 'old_value': 6500, 'new_value': 6688}]
2025-06-26 12:02:23,459 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-26 12:02:23,849 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-26 12:02:23,849 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83090.0, 'new_value': 85384.0}, {'field': 'total_amount', 'old_value': 83090.0, 'new_value': 85384.0}, {'field': 'order_count', 'old_value': 2488, 'new_value': 2559}]
2025-06-26 12:02:23,849 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-26 12:02:24,303 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-26 12:02:24,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328141.98, 'new_value': 339209.16}, {'field': 'total_amount', 'old_value': 328141.98, 'new_value': 339209.16}, {'field': 'order_count', 'old_value': 1125, 'new_value': 1164}]
2025-06-26 12:02:24,303 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-26 12:02:24,787 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-26 12:02:24,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56092.3, 'new_value': 56795.0}, {'field': 'total_amount', 'old_value': 56829.2, 'new_value': 57531.9}, {'field': 'order_count', 'old_value': 409, 'new_value': 417}]
2025-06-26 12:02:24,787 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-26 12:02:25,162 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-26 12:02:25,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 206333.42, 'new_value': 213792.19}, {'field': 'total_amount', 'old_value': 206333.42, 'new_value': 213792.19}, {'field': 'order_count', 'old_value': 1655, 'new_value': 1725}]
2025-06-26 12:02:25,178 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-26 12:02:25,568 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-26 12:02:25,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33385.1, 'new_value': 38231.1}, {'field': 'total_amount', 'old_value': 33385.1, 'new_value': 38231.1}, {'field': 'order_count', 'old_value': 224, 'new_value': 261}]
2025-06-26 12:02:25,568 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-26 12:02:26,131 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-26 12:02:26,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154197.0, 'new_value': 163622.0}, {'field': 'total_amount', 'old_value': 162816.0, 'new_value': 172241.0}, {'field': 'order_count', 'old_value': 11915, 'new_value': 12611}]
2025-06-26 12:02:26,131 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-26 12:02:26,568 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-26 12:02:26,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 786470.98, 'new_value': 814470.98}, {'field': 'total_amount', 'old_value': 865191.24, 'new_value': 893191.24}, {'field': 'order_count', 'old_value': 3664, 'new_value': 3799}]
2025-06-26 12:02:26,568 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-26 12:02:27,115 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-26 12:02:27,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 240079.52, 'new_value': 241879.52}, {'field': 'offline_amount', 'old_value': 221298.79, 'new_value': 221363.79}, {'field': 'total_amount', 'old_value': 461378.31, 'new_value': 463243.31}, {'field': 'order_count', 'old_value': 3063, 'new_value': 3086}]
2025-06-26 12:02:27,115 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-26 12:02:27,553 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-26 12:02:27,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82340.0, 'new_value': 85159.0}, {'field': 'total_amount', 'old_value': 82340.0, 'new_value': 85159.0}, {'field': 'order_count', 'old_value': 661, 'new_value': 686}]
2025-06-26 12:02:27,553 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-26 12:02:28,037 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-26 12:02:28,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247621.26, 'new_value': 253735.97}, {'field': 'total_amount', 'old_value': 247621.26, 'new_value': 253735.97}, {'field': 'order_count', 'old_value': 5226, 'new_value': 5359}]
2025-06-26 12:02:28,037 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-26 12:02:28,568 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-26 12:02:28,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27197.92, 'new_value': 27695.92}, {'field': 'total_amount', 'old_value': 27755.92, 'new_value': 28253.92}, {'field': 'order_count', 'old_value': 65, 'new_value': 66}]
2025-06-26 12:02:28,568 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7M
2025-06-26 12:02:29,178 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7M
2025-06-26 12:02:29,178 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20985.1, 'new_value': 25525.0}, {'field': 'total_amount', 'old_value': 20985.1, 'new_value': 25525.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-06-26 12:02:29,178 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-26 12:02:29,646 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-26 12:02:29,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8409.0, 'new_value': 9113.0}, {'field': 'total_amount', 'old_value': 10792.0, 'new_value': 11496.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 110}]
2025-06-26 12:02:29,646 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-26 12:02:30,146 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-26 12:02:30,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382941.53, 'new_value': 393569.93}, {'field': 'total_amount', 'old_value': 382941.53, 'new_value': 393569.93}, {'field': 'order_count', 'old_value': 15613, 'new_value': 16058}]
2025-06-26 12:02:30,146 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-26 12:02:30,646 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-26 12:02:30,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139344.9, 'new_value': 140221.9}, {'field': 'total_amount', 'old_value': 139344.9, 'new_value': 140221.9}, {'field': 'order_count', 'old_value': 4432, 'new_value': 4464}]
2025-06-26 12:02:30,646 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-26 12:02:31,131 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-26 12:02:31,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240015.55, 'new_value': 244230.28}, {'field': 'total_amount', 'old_value': 240015.55, 'new_value': 244230.28}, {'field': 'order_count', 'old_value': 1497, 'new_value': 1535}]
2025-06-26 12:02:31,131 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-26 12:02:31,537 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-26 12:02:31,537 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 144070.0, 'new_value': 151670.0}, {'field': 'offline_amount', 'old_value': 99030.0, 'new_value': 102922.0}, {'field': 'total_amount', 'old_value': 243100.0, 'new_value': 254592.0}, {'field': 'order_count', 'old_value': 3481, 'new_value': 3655}]
2025-06-26 12:02:31,537 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-26 12:02:31,990 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-26 12:02:31,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1446697.23, 'new_value': 1518242.3}, {'field': 'offline_amount', 'old_value': 323531.0, 'new_value': 337137.8}, {'field': 'total_amount', 'old_value': 1770228.23, 'new_value': 1855380.1}, {'field': 'order_count', 'old_value': 6599, 'new_value': 6884}]
2025-06-26 12:02:31,990 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-26 12:02:32,490 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-26 12:02:32,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25758.94, 'new_value': 26426.94}, {'field': 'offline_amount', 'old_value': 31083.46, 'new_value': 31531.46}, {'field': 'total_amount', 'old_value': 56842.4, 'new_value': 57958.4}, {'field': 'order_count', 'old_value': 6690, 'new_value': 6698}]
2025-06-26 12:02:32,490 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-26 12:02:32,959 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-26 12:02:32,959 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47835.39, 'new_value': 49435.43}, {'field': 'offline_amount', 'old_value': 441717.91, 'new_value': 457561.34}, {'field': 'total_amount', 'old_value': 489553.3, 'new_value': 506996.77}, {'field': 'order_count', 'old_value': 2171, 'new_value': 2249}]
2025-06-26 12:02:32,959 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-26 12:02:33,474 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-26 12:02:33,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164572.6, 'new_value': 175681.9}, {'field': 'total_amount', 'old_value': 298237.1, 'new_value': 309346.4}, {'field': 'order_count', 'old_value': 8051, 'new_value': 8372}]
2025-06-26 12:02:33,474 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-26 12:02:34,053 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-26 12:02:34,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316857.6, 'new_value': 321871.6}, {'field': 'total_amount', 'old_value': 316857.6, 'new_value': 321871.6}, {'field': 'order_count', 'old_value': 365, 'new_value': 374}]
2025-06-26 12:02:34,053 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-26 12:02:34,521 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-26 12:02:34,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 359814.1, 'new_value': 363088.4}, {'field': 'total_amount', 'old_value': 491431.3, 'new_value': 494705.6}, {'field': 'order_count', 'old_value': 3562, 'new_value': 3565}]
2025-06-26 12:02:34,521 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-26 12:02:35,052 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-26 12:02:35,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30917.65, 'new_value': 32544.65}, {'field': 'offline_amount', 'old_value': 28453.3, 'new_value': 28705.3}, {'field': 'total_amount', 'old_value': 59370.95, 'new_value': 61249.95}, {'field': 'order_count', 'old_value': 322, 'new_value': 331}]
2025-06-26 12:02:35,052 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-26 12:02:35,537 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-26 12:02:35,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310669.46, 'new_value': 318891.06}, {'field': 'total_amount', 'old_value': 310669.46, 'new_value': 318891.06}, {'field': 'order_count', 'old_value': 2933, 'new_value': 3042}]
2025-06-26 12:02:35,537 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-26 12:02:35,959 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-26 12:02:35,959 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51444.92, 'new_value': 53209.49}, {'field': 'offline_amount', 'old_value': 342273.39, 'new_value': 350267.01}, {'field': 'total_amount', 'old_value': 393718.31, 'new_value': 403476.5}, {'field': 'order_count', 'old_value': 3248, 'new_value': 3345}]
2025-06-26 12:02:35,974 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-26 12:02:36,459 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-26 12:02:36,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161666.95, 'new_value': 169540.55}, {'field': 'offline_amount', 'old_value': 244770.7, 'new_value': 253979.64}, {'field': 'total_amount', 'old_value': 406437.65, 'new_value': 423520.19}, {'field': 'order_count', 'old_value': 3661, 'new_value': 3837}]
2025-06-26 12:02:36,459 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-26 12:02:36,912 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-26 12:02:36,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103762.0, 'new_value': 108080.0}, {'field': 'total_amount', 'old_value': 103762.0, 'new_value': 108080.0}, {'field': 'order_count', 'old_value': 261, 'new_value': 273}]
2025-06-26 12:02:36,912 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-26 12:02:37,349 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-26 12:02:37,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 283999.07, 'new_value': 284305.57}, {'field': 'offline_amount', 'old_value': 21111.19, 'new_value': 29793.49}, {'field': 'total_amount', 'old_value': 305110.26, 'new_value': 314099.06}, {'field': 'order_count', 'old_value': 11560, 'new_value': 11914}]
2025-06-26 12:02:37,349 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-26 12:02:37,912 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-26 12:02:37,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101696.0, 'new_value': 113696.0}, {'field': 'total_amount', 'old_value': 220421.0, 'new_value': 232421.0}, {'field': 'order_count', 'old_value': 9138, 'new_value': 9139}]
2025-06-26 12:02:37,912 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS01
2025-06-26 12:02:38,334 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS01
2025-06-26 12:02:38,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196232.33, 'new_value': 205232.33}, {'field': 'total_amount', 'old_value': 206050.6, 'new_value': 215050.6}, {'field': 'order_count', 'old_value': 9347, 'new_value': 9747}]
2025-06-26 12:02:38,334 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-26 12:02:38,912 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-26 12:02:38,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 763109.0, 'new_value': 795695.0}, {'field': 'total_amount', 'old_value': 763109.0, 'new_value': 795695.0}, {'field': 'order_count', 'old_value': 970, 'new_value': 1021}]
2025-06-26 12:02:38,912 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-26 12:02:39,381 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-26 12:02:39,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91217.9, 'new_value': 93772.8}, {'field': 'total_amount', 'old_value': 91217.9, 'new_value': 93772.8}, {'field': 'order_count', 'old_value': 413, 'new_value': 428}]
2025-06-26 12:02:39,381 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV01
2025-06-26 12:02:39,912 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV01
2025-06-26 12:02:39,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201090.71, 'new_value': 210887.71}, {'field': 'total_amount', 'old_value': 201090.71, 'new_value': 210887.71}, {'field': 'order_count', 'old_value': 9036, 'new_value': 9502}]
2025-06-26 12:02:39,912 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-26 12:02:40,381 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-26 12:02:40,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38208.95, 'new_value': 39828.86}, {'field': 'offline_amount', 'old_value': 43845.8, 'new_value': 45509.84}, {'field': 'total_amount', 'old_value': 82054.75, 'new_value': 85338.7}, {'field': 'order_count', 'old_value': 4260, 'new_value': 4435}]
2025-06-26 12:02:40,381 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-26 12:02:40,849 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-26 12:02:40,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122199.0, 'new_value': 125098.0}, {'field': 'total_amount', 'old_value': 122199.0, 'new_value': 125098.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 225}]
2025-06-26 12:02:40,849 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-26 12:02:41,318 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-26 12:02:41,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3748.0, 'new_value': 3834.0}, {'field': 'offline_amount', 'old_value': 24593.4, 'new_value': 24795.4}, {'field': 'total_amount', 'old_value': 28341.4, 'new_value': 28629.4}, {'field': 'order_count', 'old_value': 962, 'new_value': 974}]
2025-06-26 12:02:41,318 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-26 12:02:41,802 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-26 12:02:41,802 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132774.5, 'new_value': 138432.2}, {'field': 'total_amount', 'old_value': 132774.5, 'new_value': 138432.2}, {'field': 'order_count', 'old_value': 640, 'new_value': 660}]
2025-06-26 12:02:41,802 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-26 12:02:42,224 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-26 12:02:42,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223480.0, 'new_value': 228760.0}, {'field': 'total_amount', 'old_value': 223480.0, 'new_value': 228760.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-06-26 12:02:42,224 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-26 12:02:42,724 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-26 12:02:42,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45831.5, 'new_value': 47691.5}, {'field': 'total_amount', 'old_value': 45831.5, 'new_value': 47691.5}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-06-26 12:02:42,724 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-26 12:02:43,146 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-26 12:02:43,146 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15133.48, 'new_value': 16051.66}, {'field': 'offline_amount', 'old_value': 275381.1, 'new_value': 284950.1}, {'field': 'total_amount', 'old_value': 290514.58, 'new_value': 301001.76}, {'field': 'order_count', 'old_value': 1524, 'new_value': 1583}]
2025-06-26 12:02:43,146 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-26 12:02:43,709 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-26 12:02:43,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31566.0, 'new_value': 33122.0}, {'field': 'total_amount', 'old_value': 31566.0, 'new_value': 33122.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 99}]
2025-06-26 12:02:43,709 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM811
2025-06-26 12:02:44,146 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM811
2025-06-26 12:02:44,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304118.21, 'new_value': 320237.11}, {'field': 'total_amount', 'old_value': 304118.21, 'new_value': 320237.11}, {'field': 'order_count', 'old_value': 416, 'new_value': 476}]
2025-06-26 12:02:44,146 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-26 12:02:44,599 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-26 12:02:44,599 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16641.57, 'new_value': 17295.93}, {'field': 'offline_amount', 'old_value': 140247.0, 'new_value': 141527.0}, {'field': 'total_amount', 'old_value': 156888.57, 'new_value': 158822.93}, {'field': 'order_count', 'old_value': 80, 'new_value': 84}]
2025-06-26 12:02:44,599 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-26 12:02:45,162 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-26 12:02:45,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49826.0, 'new_value': 49835.0}, {'field': 'total_amount', 'old_value': 49826.0, 'new_value': 49835.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-06-26 12:02:45,162 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-26 12:02:45,662 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-26 12:02:45,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140719.84, 'new_value': 147672.31}, {'field': 'offline_amount', 'old_value': 197051.44, 'new_value': 203455.19}, {'field': 'total_amount', 'old_value': 337771.28, 'new_value': 351127.5}, {'field': 'order_count', 'old_value': 11344, 'new_value': 11875}]
2025-06-26 12:02:45,662 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-26 12:02:46,115 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-26 12:02:46,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 240117.17, 'new_value': 249256.41}, {'field': 'offline_amount', 'old_value': 298450.85, 'new_value': 308021.39}, {'field': 'total_amount', 'old_value': 538568.02, 'new_value': 557277.8}, {'field': 'order_count', 'old_value': 17142, 'new_value': 17748}]
2025-06-26 12:02:46,115 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-26 12:02:46,506 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-26 12:02:46,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62093.15, 'new_value': 64251.28}, {'field': 'total_amount', 'old_value': 62093.15, 'new_value': 64251.28}, {'field': 'order_count', 'old_value': 993, 'new_value': 1012}]
2025-06-26 12:02:46,506 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-26 12:02:47,068 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-26 12:02:47,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155814.6, 'new_value': 158864.1}, {'field': 'total_amount', 'old_value': 155814.6, 'new_value': 158864.1}, {'field': 'order_count', 'old_value': 5068, 'new_value': 5161}]
2025-06-26 12:02:47,068 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF11
2025-06-26 12:02:47,615 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF11
2025-06-26 12:02:47,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26798.0, 'new_value': 28778.0}, {'field': 'total_amount', 'old_value': 26798.0, 'new_value': 28778.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-26 12:02:47,615 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-26 12:02:48,099 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-26 12:02:48,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212834.07, 'new_value': 224670.97}, {'field': 'total_amount', 'old_value': 223461.07, 'new_value': 235297.97}, {'field': 'order_count', 'old_value': 938, 'new_value': 980}]
2025-06-26 12:02:48,099 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-26 12:02:48,568 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-26 12:02:48,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6314.13, 'new_value': 6628.63}, {'field': 'offline_amount', 'old_value': 16282.12, 'new_value': 17053.03}, {'field': 'total_amount', 'old_value': 22596.25, 'new_value': 23681.66}, {'field': 'order_count', 'old_value': 237, 'new_value': 253}]
2025-06-26 12:02:48,568 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-26 12:02:49,006 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-26 12:02:49,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159455.97, 'new_value': 165197.72}, {'field': 'total_amount', 'old_value': 188650.98, 'new_value': 194392.73}, {'field': 'order_count', 'old_value': 10625, 'new_value': 10931}]
2025-06-26 12:02:49,006 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-26 12:02:49,490 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-26 12:02:49,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33780.32, 'new_value': 35133.62}, {'field': 'total_amount', 'old_value': 33780.32, 'new_value': 35133.62}, {'field': 'order_count', 'old_value': 738, 'new_value': 770}]
2025-06-26 12:02:49,490 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-26 12:02:50,037 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-26 12:02:50,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 283283.35, 'new_value': 297507.7}, {'field': 'total_amount', 'old_value': 347389.28, 'new_value': 361613.63}, {'field': 'order_count', 'old_value': 4857, 'new_value': 4999}]
2025-06-26 12:02:50,037 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-26 12:02:50,490 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-26 12:02:50,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 325878.0, 'new_value': 338536.0}, {'field': 'total_amount', 'old_value': 339436.0, 'new_value': 352094.0}, {'field': 'order_count', 'old_value': 286, 'new_value': 296}]
2025-06-26 12:02:50,490 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-26 12:02:50,896 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-26 12:02:50,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120436.0, 'new_value': 124176.0}, {'field': 'total_amount', 'old_value': 120604.0, 'new_value': 124344.0}, {'field': 'order_count', 'old_value': 351, 'new_value': 364}]
2025-06-26 12:02:50,896 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-26 12:02:51,396 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-26 12:02:51,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4358.5, 'new_value': 4676.5}, {'field': 'offline_amount', 'old_value': 55972.01, 'new_value': 58053.51}, {'field': 'total_amount', 'old_value': 60330.51, 'new_value': 62730.01}, {'field': 'order_count', 'old_value': 379, 'new_value': 384}]
2025-06-26 12:02:51,396 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-26 12:02:51,865 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-26 12:02:51,865 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17030.0, 'new_value': 17123.0}, {'field': 'offline_amount', 'old_value': 25095.0, 'new_value': 26094.0}, {'field': 'total_amount', 'old_value': 42125.0, 'new_value': 43217.0}, {'field': 'order_count', 'old_value': 343, 'new_value': 351}]
2025-06-26 12:02:51,865 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-26 12:02:52,334 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-26 12:02:52,334 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134965.29, 'new_value': 140467.22}, {'field': 'offline_amount', 'old_value': 42789.43, 'new_value': 43794.44}, {'field': 'total_amount', 'old_value': 177754.72, 'new_value': 184261.66}, {'field': 'order_count', 'old_value': 10591, 'new_value': 10983}]
2025-06-26 12:02:52,334 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-26 12:02:52,787 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-26 12:02:52,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67041.65, 'new_value': 68839.2}, {'field': 'offline_amount', 'old_value': 91023.2, 'new_value': 92545.08}, {'field': 'total_amount', 'old_value': 158064.85, 'new_value': 161384.28}, {'field': 'order_count', 'old_value': 1809, 'new_value': 1877}]
2025-06-26 12:02:52,787 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-26 12:02:53,255 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-26 12:02:53,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131797.18, 'new_value': 134974.13}, {'field': 'total_amount', 'old_value': 131797.18, 'new_value': 134974.13}, {'field': 'order_count', 'old_value': 632, 'new_value': 657}]
2025-06-26 12:02:53,255 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-26 12:02:53,755 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-26 12:02:53,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339113.8, 'new_value': 353331.8}, {'field': 'total_amount', 'old_value': 339113.8, 'new_value': 353331.8}, {'field': 'order_count', 'old_value': 399, 'new_value': 414}]
2025-06-26 12:02:53,755 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-26 12:02:54,209 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-26 12:02:54,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64935.0, 'new_value': 66140.0}, {'field': 'offline_amount', 'old_value': 294840.0, 'new_value': 305502.0}, {'field': 'total_amount', 'old_value': 359775.0, 'new_value': 371642.0}, {'field': 'order_count', 'old_value': 1393, 'new_value': 1443}]
2025-06-26 12:02:54,209 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-26 12:02:54,693 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-26 12:02:54,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240616.56, 'new_value': 243252.96}, {'field': 'total_amount', 'old_value': 240616.56, 'new_value': 243252.96}, {'field': 'order_count', 'old_value': 1069, 'new_value': 1086}]
2025-06-26 12:02:54,693 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-26 12:02:55,130 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-26 12:02:55,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 402644.09, 'new_value': 408221.99}, {'field': 'total_amount', 'old_value': 402644.09, 'new_value': 408221.99}, {'field': 'order_count', 'old_value': 9604, 'new_value': 9720}]
2025-06-26 12:02:55,130 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-26 12:02:55,599 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-26 12:02:55,599 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96793.26, 'new_value': 101925.18}, {'field': 'offline_amount', 'old_value': 127103.64, 'new_value': 132264.23}, {'field': 'total_amount', 'old_value': 223896.9, 'new_value': 234189.41}, {'field': 'order_count', 'old_value': 8890, 'new_value': 9182}]
2025-06-26 12:02:55,599 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-26 12:02:56,115 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-26 12:02:56,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176177.83, 'new_value': 184282.39}, {'field': 'total_amount', 'old_value': 176177.83, 'new_value': 184282.39}, {'field': 'order_count', 'old_value': 743, 'new_value': 783}]
2025-06-26 12:02:56,115 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-26 12:02:56,615 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-26 12:02:56,615 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 193876.43, 'new_value': 202326.43}, {'field': 'total_amount', 'old_value': 275591.63, 'new_value': 284041.63}, {'field': 'order_count', 'old_value': 438, 'new_value': 451}]
2025-06-26 12:02:56,615 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-26 12:02:57,115 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-26 12:02:57,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84440.07, 'new_value': 87438.74}, {'field': 'total_amount', 'old_value': 84440.07, 'new_value': 87438.74}, {'field': 'order_count', 'old_value': 5480, 'new_value': 5675}]
2025-06-26 12:02:57,115 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-26 12:02:57,568 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-26 12:02:57,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2675.0, 'new_value': 2875.0}, {'field': 'offline_amount', 'old_value': 29685.0, 'new_value': 30285.0}, {'field': 'total_amount', 'old_value': 32360.0, 'new_value': 33160.0}, {'field': 'order_count', 'old_value': 408, 'new_value': 420}]
2025-06-26 12:02:57,568 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-26 12:02:58,099 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-26 12:02:58,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133181.0, 'new_value': 136585.0}, {'field': 'offline_amount', 'old_value': 161247.0, 'new_value': 170316.0}, {'field': 'total_amount', 'old_value': 294428.0, 'new_value': 306901.0}, {'field': 'order_count', 'old_value': 179512, 'new_value': 179552}]
2025-06-26 12:02:58,099 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-26 12:02:58,568 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-26 12:02:58,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66424.24, 'new_value': 71025.12}, {'field': 'offline_amount', 'old_value': 69149.01, 'new_value': 72255.25}, {'field': 'total_amount', 'old_value': 135573.25, 'new_value': 143280.37}, {'field': 'order_count', 'old_value': 7094, 'new_value': 7524}]
2025-06-26 12:02:58,568 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-26 12:02:58,990 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-26 12:02:58,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8209.8, 'new_value': 10375.39}, {'field': 'offline_amount', 'old_value': 25584.68, 'new_value': 26583.68}, {'field': 'total_amount', 'old_value': 33794.48, 'new_value': 36959.07}, {'field': 'order_count', 'old_value': 107, 'new_value': 112}]
2025-06-26 12:02:58,990 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-26 12:02:59,459 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-26 12:02:59,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18990.41, 'new_value': 19975.55}, {'field': 'offline_amount', 'old_value': 11073.84, 'new_value': 11655.84}, {'field': 'total_amount', 'old_value': 30064.25, 'new_value': 31631.39}, {'field': 'order_count', 'old_value': 1110, 'new_value': 1162}]
2025-06-26 12:02:59,459 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-26 12:02:59,959 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-26 12:02:59,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216888.0, 'new_value': 229294.0}, {'field': 'total_amount', 'old_value': 216888.0, 'new_value': 229294.0}, {'field': 'order_count', 'old_value': 22892, 'new_value': 24237}]
2025-06-26 12:02:59,959 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-26 12:03:00,443 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-26 12:03:00,443 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159921.0, 'new_value': 163181.0}, {'field': 'offline_amount', 'old_value': 1111462.0, 'new_value': 1154002.0}, {'field': 'total_amount', 'old_value': 1271383.0, 'new_value': 1317183.0}, {'field': 'order_count', 'old_value': 32883, 'new_value': 45255}]
2025-06-26 12:03:00,443 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-26 12:03:01,099 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-26 12:03:01,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 290113.0, 'new_value': 304232.0}, {'field': 'total_amount', 'old_value': 290113.0, 'new_value': 304232.0}, {'field': 'order_count', 'old_value': 6693, 'new_value': 7007}]
2025-06-26 12:03:01,099 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-26 12:03:01,615 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-26 12:03:01,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17618.0, 'new_value': 17876.0}, {'field': 'total_amount', 'old_value': 17618.0, 'new_value': 17876.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-06-26 12:03:01,615 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-26 12:03:02,068 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-26 12:03:02,068 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43133.75, 'new_value': 45401.95}, {'field': 'total_amount', 'old_value': 43133.75, 'new_value': 45401.95}, {'field': 'order_count', 'old_value': 1137, 'new_value': 1177}]
2025-06-26 12:03:02,068 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-26 12:03:02,646 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-26 12:03:02,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46863.1, 'new_value': 49628.1}, {'field': 'total_amount', 'old_value': 46863.1, 'new_value': 49628.1}, {'field': 'order_count', 'old_value': 247, 'new_value': 257}]
2025-06-26 12:03:02,646 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-26 12:03:03,084 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-26 12:03:03,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 667546.79, 'new_value': 683403.59}, {'field': 'total_amount', 'old_value': 667546.79, 'new_value': 683403.59}, {'field': 'order_count', 'old_value': 5717, 'new_value': 5884}]
2025-06-26 12:03:03,084 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-26 12:03:03,615 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-26 12:03:03,615 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 237797.9, 'new_value': 252999.9}, {'field': 'offline_amount', 'old_value': 20221.0, 'new_value': 21763.5}, {'field': 'total_amount', 'old_value': 258018.9, 'new_value': 274763.4}, {'field': 'order_count', 'old_value': 3010, 'new_value': 3286}]
2025-06-26 12:03:03,615 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX21
2025-06-26 12:03:04,068 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX21
2025-06-26 12:03:04,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119019.0, 'new_value': 126404.0}, {'field': 'total_amount', 'old_value': 119019.0, 'new_value': 126404.0}, {'field': 'order_count', 'old_value': 3650, 'new_value': 3866}]
2025-06-26 12:03:04,068 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-26 12:03:04,630 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-26 12:03:04,630 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30545.8, 'new_value': 31809.3}, {'field': 'offline_amount', 'old_value': 52945.9, 'new_value': 55760.7}, {'field': 'total_amount', 'old_value': 83491.7, 'new_value': 87570.0}, {'field': 'order_count', 'old_value': 3341, 'new_value': 3451}]
2025-06-26 12:03:04,630 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-26 12:03:05,130 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-26 12:03:05,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 696569.4, 'new_value': 712120.4}, {'field': 'total_amount', 'old_value': 696569.4, 'new_value': 712120.4}, {'field': 'order_count', 'old_value': 4449, 'new_value': 4504}]
2025-06-26 12:03:05,130 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-26 12:03:05,607 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-26 12:03:05,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32113.92, 'new_value': 33675.84}, {'field': 'total_amount', 'old_value': 32879.92, 'new_value': 34441.84}, {'field': 'order_count', 'old_value': 306, 'new_value': 324}]
2025-06-26 12:03:05,607 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-26 12:03:06,054 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-26 12:03:06,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33808.93, 'new_value': 35017.26}, {'field': 'total_amount', 'old_value': 33808.93, 'new_value': 35017.26}, {'field': 'order_count', 'old_value': 104, 'new_value': 108}]
2025-06-26 12:03:06,054 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-26 12:03:06,601 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-26 12:03:06,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376947.71, 'new_value': 383256.71}, {'field': 'total_amount', 'old_value': 409351.64, 'new_value': 415660.64}, {'field': 'order_count', 'old_value': 517, 'new_value': 534}]
2025-06-26 12:03:06,601 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM631
2025-06-26 12:03:07,085 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM631
2025-06-26 12:03:07,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114655.0, 'new_value': 145735.0}, {'field': 'total_amount', 'old_value': 123261.0, 'new_value': 154341.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 21}]
2025-06-26 12:03:07,085 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-26 12:03:07,523 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-26 12:03:07,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5071296.49, 'new_value': 5226426.49}, {'field': 'total_amount', 'old_value': 5071296.49, 'new_value': 5226426.49}, {'field': 'order_count', 'old_value': 102539, 'new_value': 104860}]
2025-06-26 12:03:07,523 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-26 12:03:08,070 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-26 12:03:08,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1024313.4, 'new_value': 1064305.4}, {'field': 'total_amount', 'old_value': 1024313.4, 'new_value': 1064305.4}, {'field': 'order_count', 'old_value': 4135, 'new_value': 4244}]
2025-06-26 12:03:08,070 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-26 12:03:08,539 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-26 12:03:08,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 663621.25, 'new_value': 682637.85}, {'field': 'total_amount', 'old_value': 663621.25, 'new_value': 682637.85}, {'field': 'order_count', 'old_value': 1689, 'new_value': 1757}]
2025-06-26 12:03:08,539 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-26 12:03:09,023 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-26 12:03:09,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68231.24, 'new_value': 71731.11}, {'field': 'total_amount', 'old_value': 96628.36, 'new_value': 100128.23}, {'field': 'order_count', 'old_value': 6374, 'new_value': 6619}]
2025-06-26 12:03:09,023 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-26 12:03:09,445 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-26 12:03:09,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119014.95, 'new_value': 126559.77}, {'field': 'total_amount', 'old_value': 171490.78, 'new_value': 179035.6}, {'field': 'order_count', 'old_value': 11392, 'new_value': 11903}]
2025-06-26 12:03:09,445 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-26 12:03:09,882 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-26 12:03:09,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74468.0, 'new_value': 79748.0}, {'field': 'total_amount', 'old_value': 93308.0, 'new_value': 98588.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-26 12:03:09,882 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM51
2025-06-26 12:03:10,304 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM51
2025-06-26 12:03:10,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33000.0, 'new_value': 63000.0}, {'field': 'total_amount', 'old_value': 39000.0, 'new_value': 69000.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-26 12:03:10,304 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-26 12:03:10,773 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-26 12:03:10,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104360.0, 'new_value': 113314.0}, {'field': 'total_amount', 'old_value': 104360.0, 'new_value': 113314.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-26 12:03:10,773 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-26 12:03:11,242 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-26 12:03:11,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10812.04, 'new_value': 11162.14}, {'field': 'offline_amount', 'old_value': 77428.28, 'new_value': 79776.82}, {'field': 'total_amount', 'old_value': 88240.32, 'new_value': 90938.96}, {'field': 'order_count', 'old_value': 2738, 'new_value': 2821}]
2025-06-26 12:03:11,242 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-26 12:03:11,648 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-26 12:03:11,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71500.0, 'new_value': 73030.0}, {'field': 'total_amount', 'old_value': 71500.0, 'new_value': 73030.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-26 12:03:11,648 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-26 12:03:12,070 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-26 12:03:12,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 446004.86, 'new_value': 461276.86}, {'field': 'total_amount', 'old_value': 446004.86, 'new_value': 461276.86}, {'field': 'order_count', 'old_value': 3051, 'new_value': 3176}]
2025-06-26 12:03:12,070 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-26 12:03:12,554 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-26 12:03:12,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40776.9, 'new_value': 40786.8}, {'field': 'total_amount', 'old_value': 47508.55, 'new_value': 47518.45}, {'field': 'order_count', 'old_value': 361, 'new_value': 362}]
2025-06-26 12:03:12,554 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-26 12:03:13,023 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-26 12:03:13,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31834.2, 'new_value': 34604.2}, {'field': 'total_amount', 'old_value': 32317.2, 'new_value': 35087.2}, {'field': 'order_count', 'old_value': 155, 'new_value': 171}]
2025-06-26 12:03:13,023 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-26 12:03:13,539 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-26 12:03:13,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238007.93, 'new_value': 250662.18}, {'field': 'total_amount', 'old_value': 238007.93, 'new_value': 250662.18}, {'field': 'order_count', 'old_value': 6925, 'new_value': 7209}]
2025-06-26 12:03:13,539 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-26 12:03:14,007 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-26 12:03:14,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257100.0, 'new_value': 270400.0}, {'field': 'total_amount', 'old_value': 257100.0, 'new_value': 270400.0}, {'field': 'order_count', 'old_value': 606, 'new_value': 639}]
2025-06-26 12:03:14,007 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-26 12:03:14,476 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-26 12:03:14,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 152739.09, 'new_value': 160687.0}, {'field': 'offline_amount', 'old_value': 48228.14, 'new_value': 50402.14}, {'field': 'total_amount', 'old_value': 200967.23, 'new_value': 211089.14}, {'field': 'order_count', 'old_value': 864, 'new_value': 909}]
2025-06-26 12:03:14,476 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-26 12:03:14,945 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-26 12:03:14,945 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29058.1, 'new_value': 30421.18}, {'field': 'offline_amount', 'old_value': 114646.0, 'new_value': 119079.0}, {'field': 'total_amount', 'old_value': 143704.1, 'new_value': 149500.18}, {'field': 'order_count', 'old_value': 1763, 'new_value': 1823}]
2025-06-26 12:03:14,945 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-26 12:03:15,351 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-26 12:03:15,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205301.31, 'new_value': 217111.49}, {'field': 'total_amount', 'old_value': 205301.31, 'new_value': 217111.49}, {'field': 'order_count', 'old_value': 3131, 'new_value': 3286}]
2025-06-26 12:03:15,351 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-26 12:03:15,773 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-26 12:03:15,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 536944.95, 'new_value': 564978.95}, {'field': 'total_amount', 'old_value': 536944.95, 'new_value': 564978.95}, {'field': 'order_count', 'old_value': 505, 'new_value': 525}]
2025-06-26 12:03:15,773 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-26 12:03:16,226 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-26 12:03:16,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40445.5, 'new_value': 43614.7}, {'field': 'total_amount', 'old_value': 40445.5, 'new_value': 43614.7}, {'field': 'order_count', 'old_value': 340, 'new_value': 357}]
2025-06-26 12:03:16,226 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-26 12:03:16,804 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-26 12:03:16,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67717.29, 'new_value': 70217.29}, {'field': 'total_amount', 'old_value': 67717.29, 'new_value': 70217.29}, {'field': 'order_count', 'old_value': 2649, 'new_value': 2729}]
2025-06-26 12:03:16,804 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-26 12:03:17,210 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-26 12:03:17,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119554.35, 'new_value': 129052.55}, {'field': 'total_amount', 'old_value': 119554.35, 'new_value': 129052.55}, {'field': 'order_count', 'old_value': 3031, 'new_value': 3309}]
2025-06-26 12:03:17,210 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-26 12:03:17,726 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-26 12:03:17,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100332.79, 'new_value': 101266.19}, {'field': 'offline_amount', 'old_value': 185802.55, 'new_value': 194973.43}, {'field': 'total_amount', 'old_value': 286135.34, 'new_value': 296239.62}, {'field': 'order_count', 'old_value': 2276, 'new_value': 2375}]
2025-06-26 12:03:17,726 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-26 12:03:18,195 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-26 12:03:18,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81413.2, 'new_value': 83153.2}, {'field': 'total_amount', 'old_value': 81413.2, 'new_value': 83153.2}, {'field': 'order_count', 'old_value': 738, 'new_value': 748}]
2025-06-26 12:03:18,195 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-26 12:03:18,757 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-26 12:03:18,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146176.0, 'new_value': 151176.0}, {'field': 'total_amount', 'old_value': 146176.0, 'new_value': 151176.0}, {'field': 'order_count', 'old_value': 13092, 'new_value': 13242}]
2025-06-26 12:03:18,757 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-26 12:03:19,335 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-26 12:03:19,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79409.0, 'new_value': 81834.0}, {'field': 'total_amount', 'old_value': 79409.0, 'new_value': 81834.0}, {'field': 'order_count', 'old_value': 1223, 'new_value': 1288}]
2025-06-26 12:03:19,335 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-26 12:03:19,726 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-26 12:03:19,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53282.0, 'new_value': 61890.0}, {'field': 'total_amount', 'old_value': 53282.0, 'new_value': 61890.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-26 12:03:19,726 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-26 12:03:20,132 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-26 12:03:20,132 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58557.7, 'new_value': 66557.7}, {'field': 'offline_amount', 'old_value': 103174.63, 'new_value': 103957.63}, {'field': 'total_amount', 'old_value': 161732.33, 'new_value': 170515.33}, {'field': 'order_count', 'old_value': 3922, 'new_value': 4093}]
2025-06-26 12:03:20,132 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-26 12:03:20,601 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-26 12:03:20,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123067.0, 'new_value': 126711.0}, {'field': 'total_amount', 'old_value': 123067.0, 'new_value': 126711.0}, {'field': 'order_count', 'old_value': 521, 'new_value': 539}]
2025-06-26 12:03:20,601 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-26 12:03:21,070 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-26 12:03:21,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108902.4, 'new_value': 111901.4}, {'field': 'total_amount', 'old_value': 108902.4, 'new_value': 111901.4}, {'field': 'order_count', 'old_value': 44, 'new_value': 46}]
2025-06-26 12:03:21,070 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-26 12:03:21,539 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-26 12:03:21,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 338890.2, 'new_value': 350890.2}, {'field': 'total_amount', 'old_value': 383684.0, 'new_value': 395684.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 122}]
2025-06-26 12:03:21,539 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-26 12:03:22,023 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-26 12:03:22,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68105.0, 'new_value': 72875.0}, {'field': 'total_amount', 'old_value': 71550.0, 'new_value': 76320.0}, {'field': 'order_count', 'old_value': 270, 'new_value': 288}]
2025-06-26 12:03:22,023 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-26 12:03:22,429 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-26 12:03:22,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45420.07, 'new_value': 47363.05}, {'field': 'total_amount', 'old_value': 45420.07, 'new_value': 47363.05}, {'field': 'order_count', 'old_value': 5752, 'new_value': 5978}]
2025-06-26 12:03:22,429 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-26 12:03:22,898 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-26 12:03:22,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303180.56, 'new_value': 314257.17}, {'field': 'total_amount', 'old_value': 303180.56, 'new_value': 314257.17}, {'field': 'order_count', 'old_value': 844, 'new_value': 869}]
2025-06-26 12:03:22,898 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-26 12:03:23,335 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-26 12:03:23,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306101.0, 'new_value': 321572.0}, {'field': 'total_amount', 'old_value': 306101.0, 'new_value': 321572.0}, {'field': 'order_count', 'old_value': 6887, 'new_value': 7222}]
2025-06-26 12:03:23,335 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-26 12:03:23,773 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-26 12:03:23,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109536.0, 'new_value': 113254.0}, {'field': 'total_amount', 'old_value': 109536.0, 'new_value': 113254.0}, {'field': 'order_count', 'old_value': 6217, 'new_value': 6438}]
2025-06-26 12:03:23,773 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-26 12:03:24,242 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-26 12:03:24,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 283447.0, 'new_value': 311816.0}, {'field': 'total_amount', 'old_value': 283447.0, 'new_value': 311816.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 80}]
2025-06-26 12:03:24,242 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-26 12:03:24,742 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-26 12:03:24,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2821548.31, 'new_value': 2931897.55}, {'field': 'total_amount', 'old_value': 2821548.31, 'new_value': 2931897.55}, {'field': 'order_count', 'old_value': 5326, 'new_value': 5529}]
2025-06-26 12:03:24,742 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-26 12:03:25,179 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-26 12:03:25,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 784277.0, 'new_value': 814941.0}, {'field': 'total_amount', 'old_value': 784277.0, 'new_value': 814941.0}, {'field': 'order_count', 'old_value': 4259, 'new_value': 4423}]
2025-06-26 12:03:25,179 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-26 12:03:25,679 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-26 12:03:25,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9020028.73, 'new_value': 9301763.73}, {'field': 'total_amount', 'old_value': 9020028.73, 'new_value': 9301763.73}, {'field': 'order_count', 'old_value': 33766, 'new_value': 34845}]
2025-06-26 12:03:25,679 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-26 12:03:26,163 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-26 12:03:26,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162450.81, 'new_value': 169550.79}, {'field': 'total_amount', 'old_value': 162450.81, 'new_value': 169550.79}, {'field': 'order_count', 'old_value': 17816, 'new_value': 18656}]
2025-06-26 12:03:26,163 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-26 12:03:26,554 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-26 12:03:26,554 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 175157.43, 'new_value': 183163.14}, {'field': 'offline_amount', 'old_value': 149428.77, 'new_value': 155322.46}, {'field': 'total_amount', 'old_value': 324586.2, 'new_value': 338485.6}, {'field': 'order_count', 'old_value': 14024, 'new_value': 14641}]
2025-06-26 12:03:26,554 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-26 12:03:27,054 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-26 12:03:27,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212769.9, 'new_value': 221919.9}, {'field': 'total_amount', 'old_value': 212769.9, 'new_value': 221919.9}, {'field': 'order_count', 'old_value': 7356, 'new_value': 7633}]
2025-06-26 12:03:27,054 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-26 12:03:27,460 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-26 12:03:27,460 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 222162.85, 'new_value': 240761.85}, {'field': 'total_amount', 'old_value': 256364.95, 'new_value': 274963.95}, {'field': 'order_count', 'old_value': 788, 'new_value': 827}]
2025-06-26 12:03:27,460 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-26 12:03:27,929 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-26 12:03:27,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13835079.5, 'new_value': 15092814.0}, {'field': 'total_amount', 'old_value': 13835079.5, 'new_value': 15092814.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-06-26 12:03:27,929 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-26 12:03:28,367 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-26 12:03:28,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176482.0, 'new_value': 185996.0}, {'field': 'total_amount', 'old_value': 176482.0, 'new_value': 185996.0}, {'field': 'order_count', 'old_value': 758, 'new_value': 796}]
2025-06-26 12:03:28,367 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-26 12:03:28,820 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-26 12:03:28,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44175.0, 'new_value': 47175.0}, {'field': 'total_amount', 'old_value': 48721.0, 'new_value': 51721.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 69}]
2025-06-26 12:03:28,820 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU2
2025-06-26 12:03:29,351 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU2
2025-06-26 12:03:29,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150992.0, 'new_value': 158917.0}, {'field': 'total_amount', 'old_value': 151092.0, 'new_value': 159017.0}, {'field': 'order_count', 'old_value': 15936, 'new_value': 16835}]
2025-06-26 12:03:29,351 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-26 12:03:29,820 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-26 12:03:29,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43172.1, 'new_value': 48172.1}, {'field': 'total_amount', 'old_value': 43172.1, 'new_value': 48172.1}, {'field': 'order_count', 'old_value': 3596, 'new_value': 3666}]
2025-06-26 12:03:29,820 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-26 12:03:30,304 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-26 12:03:30,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1151000.0, 'new_value': 1191000.0}, {'field': 'total_amount', 'old_value': 1151000.0, 'new_value': 1191000.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-06-26 12:03:30,304 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-26 12:03:30,773 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-26 12:03:30,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328200.0, 'new_value': 340200.0}, {'field': 'total_amount', 'old_value': 328200.0, 'new_value': 340200.0}, {'field': 'order_count', 'old_value': 9381, 'new_value': 9761}]
2025-06-26 12:03:30,773 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-26 12:03:31,195 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-26 12:03:31,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126474.0, 'new_value': 128438.0}, {'field': 'total_amount', 'old_value': 126474.0, 'new_value': 128438.0}, {'field': 'order_count', 'old_value': 3495, 'new_value': 3548}]
2025-06-26 12:03:31,195 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-26 12:03:31,742 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-26 12:03:31,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154000.0, 'new_value': 161000.0}, {'field': 'total_amount', 'old_value': 154000.0, 'new_value': 161000.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-06-26 12:03:31,742 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-26 12:03:32,163 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-26 12:03:32,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116476.98, 'new_value': 121476.98}, {'field': 'total_amount', 'old_value': 116476.98, 'new_value': 121476.98}, {'field': 'order_count', 'old_value': 4329, 'new_value': 4409}]
2025-06-26 12:03:32,163 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-26 12:03:32,663 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-26 12:03:32,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95102.0, 'new_value': 98386.0}, {'field': 'total_amount', 'old_value': 95102.0, 'new_value': 98386.0}, {'field': 'order_count', 'old_value': 682, 'new_value': 702}]
2025-06-26 12:03:32,663 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-26 12:03:33,117 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-26 12:03:33,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33069.0, 'new_value': 37069.0}, {'field': 'total_amount', 'old_value': 33069.0, 'new_value': 37069.0}, {'field': 'order_count', 'old_value': 2825, 'new_value': 2905}]
2025-06-26 12:03:33,117 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-26 12:03:33,554 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-26 12:03:33,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11598.0, 'new_value': 12398.0}, {'field': 'total_amount', 'old_value': 11598.0, 'new_value': 12398.0}, {'field': 'order_count', 'old_value': 1091, 'new_value': 1111}]
2025-06-26 12:03:33,554 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-26 12:03:34,007 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-26 12:03:34,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259423.35, 'new_value': 269423.35}, {'field': 'total_amount', 'old_value': 259423.35, 'new_value': 269423.35}, {'field': 'order_count', 'old_value': 1923, 'new_value': 2023}]
2025-06-26 12:03:34,007 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-26 12:03:34,460 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-26 12:03:34,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4922074.0, 'new_value': 5112074.0}, {'field': 'total_amount', 'old_value': 4922074.0, 'new_value': 5112074.0}, {'field': 'order_count', 'old_value': 90354, 'new_value': 93854}]
2025-06-26 12:03:34,460 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-26 12:03:34,898 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-26 12:03:34,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82104.0, 'new_value': 91104.0}, {'field': 'total_amount', 'old_value': 82104.0, 'new_value': 91104.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-06-26 12:03:34,898 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-26 12:03:35,335 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-26 12:03:35,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192378.22, 'new_value': 201378.22}, {'field': 'total_amount', 'old_value': 192378.22, 'new_value': 201378.22}, {'field': 'order_count', 'old_value': 374, 'new_value': 377}]
2025-06-26 12:03:35,335 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-26 12:03:35,851 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-26 12:03:35,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8713.5, 'new_value': 8979.5}, {'field': 'total_amount', 'old_value': 8713.5, 'new_value': 8979.5}, {'field': 'order_count', 'old_value': 61, 'new_value': 64}]
2025-06-26 12:03:35,851 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-26 12:03:36,335 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-26 12:03:36,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5043610.0, 'new_value': 5123610.0}, {'field': 'total_amount', 'old_value': 5043610.0, 'new_value': 5123610.0}, {'field': 'order_count', 'old_value': 283, 'new_value': 284}]
2025-06-26 12:03:36,335 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-26 12:03:36,804 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-26 12:03:36,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 412334.0, 'new_value': 417334.0}, {'field': 'total_amount', 'old_value': 412334.0, 'new_value': 417334.0}, {'field': 'order_count', 'old_value': 481, 'new_value': 486}]
2025-06-26 12:03:36,804 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-26 12:03:37,288 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-26 12:03:37,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37624.07, 'new_value': 38914.08}, {'field': 'total_amount', 'old_value': 37624.07, 'new_value': 38914.08}, {'field': 'order_count', 'old_value': 1493, 'new_value': 1546}]
2025-06-26 12:03:37,288 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-26 12:03:37,788 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-26 12:03:37,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176057.15, 'new_value': 185459.04}, {'field': 'total_amount', 'old_value': 176057.15, 'new_value': 185459.04}, {'field': 'order_count', 'old_value': 13297, 'new_value': 14014}]
2025-06-26 12:03:37,788 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-26 12:03:38,242 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-26 12:03:38,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198085.24, 'new_value': 208567.24}, {'field': 'total_amount', 'old_value': 198085.24, 'new_value': 208567.24}, {'field': 'order_count', 'old_value': 13335, 'new_value': 13545}]
2025-06-26 12:03:38,242 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-26 12:03:38,663 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-26 12:03:38,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 457000.0, 'new_value': 487000.0}, {'field': 'total_amount', 'old_value': 457000.0, 'new_value': 487000.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-26 12:03:38,663 - INFO - 开始更新记录 - 表单实例ID: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-26 12:03:39,085 - INFO - 更新表单数据成功: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-26 12:03:39,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48879.32, 'new_value': 54876.32}, {'field': 'total_amount', 'old_value': 48879.32, 'new_value': 54876.32}, {'field': 'order_count', 'old_value': 291, 'new_value': 324}]
2025-06-26 12:03:39,085 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACME1
2025-06-26 12:03:39,492 - INFO - 更新表单数据成功: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACME1
2025-06-26 12:03:39,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6252.7, 'new_value': 12505.4}, {'field': 'total_amount', 'old_value': 6252.7, 'new_value': 12505.4}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-26 12:03:39,492 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACMF1
2025-06-26 12:03:39,945 - INFO - 更新表单数据成功: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACMF1
2025-06-26 12:03:39,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95703.0, 'new_value': 99864.0}, {'field': 'total_amount', 'old_value': 95703.0, 'new_value': 99864.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-26 12:03:39,945 - INFO - 日期 2025-06 处理完成 - 更新: 376 条，插入: 0 条，错误: 0 条
2025-06-26 12:03:39,945 - INFO - 数据同步完成！更新: 376 条，插入: 0 条，错误: 0 条
2025-06-26 12:03:39,945 - INFO - =================同步完成====================
2025-06-26 15:00:03,241 - INFO - =================使用默认全量同步=============
2025-06-26 15:00:04,991 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-26 15:00:04,991 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-26 15:00:05,037 - INFO - 开始处理日期: 2025-01
2025-06-26 15:00:05,037 - INFO - Request Parameters - Page 1:
2025-06-26 15:00:05,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:05,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:06,475 - INFO - Response - Page 1:
2025-06-26 15:00:06,678 - INFO - 第 1 页获取到 100 条记录
2025-06-26 15:00:06,678 - INFO - Request Parameters - Page 2:
2025-06-26 15:00:06,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:06,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:07,272 - INFO - Response - Page 2:
2025-06-26 15:00:07,475 - INFO - 第 2 页获取到 100 条记录
2025-06-26 15:00:07,475 - INFO - Request Parameters - Page 3:
2025-06-26 15:00:07,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:07,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:08,116 - INFO - Response - Page 3:
2025-06-26 15:00:08,319 - INFO - 第 3 页获取到 100 条记录
2025-06-26 15:00:08,319 - INFO - Request Parameters - Page 4:
2025-06-26 15:00:08,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:08,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:09,225 - INFO - Response - Page 4:
2025-06-26 15:00:09,428 - INFO - 第 4 页获取到 100 条记录
2025-06-26 15:00:09,428 - INFO - Request Parameters - Page 5:
2025-06-26 15:00:09,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:09,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:09,944 - INFO - Response - Page 5:
2025-06-26 15:00:10,147 - INFO - 第 5 页获取到 100 条记录
2025-06-26 15:00:10,147 - INFO - Request Parameters - Page 6:
2025-06-26 15:00:10,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:10,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:10,694 - INFO - Response - Page 6:
2025-06-26 15:00:10,897 - INFO - 第 6 页获取到 100 条记录
2025-06-26 15:00:10,897 - INFO - Request Parameters - Page 7:
2025-06-26 15:00:10,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:10,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:11,662 - INFO - Response - Page 7:
2025-06-26 15:00:11,866 - INFO - 第 7 页获取到 82 条记录
2025-06-26 15:00:11,866 - INFO - 查询完成，共获取到 682 条记录
2025-06-26 15:00:11,866 - INFO - 获取到 682 条表单数据
2025-06-26 15:00:11,866 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-26 15:00:11,881 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 15:00:11,881 - INFO - 开始处理日期: 2025-02
2025-06-26 15:00:11,881 - INFO - Request Parameters - Page 1:
2025-06-26 15:00:11,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:11,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:12,444 - INFO - Response - Page 1:
2025-06-26 15:00:12,647 - INFO - 第 1 页获取到 100 条记录
2025-06-26 15:00:12,647 - INFO - Request Parameters - Page 2:
2025-06-26 15:00:12,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:12,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:13,147 - INFO - Response - Page 2:
2025-06-26 15:00:13,350 - INFO - 第 2 页获取到 100 条记录
2025-06-26 15:00:13,350 - INFO - Request Parameters - Page 3:
2025-06-26 15:00:13,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:13,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:13,866 - INFO - Response - Page 3:
2025-06-26 15:00:14,069 - INFO - 第 3 页获取到 100 条记录
2025-06-26 15:00:14,069 - INFO - Request Parameters - Page 4:
2025-06-26 15:00:14,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:14,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:14,647 - INFO - Response - Page 4:
2025-06-26 15:00:14,850 - INFO - 第 4 页获取到 100 条记录
2025-06-26 15:00:14,850 - INFO - Request Parameters - Page 5:
2025-06-26 15:00:14,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:14,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:15,428 - INFO - Response - Page 5:
2025-06-26 15:00:15,631 - INFO - 第 5 页获取到 100 条记录
2025-06-26 15:00:15,631 - INFO - Request Parameters - Page 6:
2025-06-26 15:00:15,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:15,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:16,334 - INFO - Response - Page 6:
2025-06-26 15:00:16,537 - INFO - 第 6 页获取到 100 条记录
2025-06-26 15:00:16,537 - INFO - Request Parameters - Page 7:
2025-06-26 15:00:16,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:16,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:17,069 - INFO - Response - Page 7:
2025-06-26 15:00:17,287 - INFO - 第 7 页获取到 70 条记录
2025-06-26 15:00:17,287 - INFO - 查询完成，共获取到 670 条记录
2025-06-26 15:00:17,287 - INFO - 获取到 670 条表单数据
2025-06-26 15:00:17,287 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-26 15:00:17,303 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 15:00:17,303 - INFO - 开始处理日期: 2025-03
2025-06-26 15:00:17,303 - INFO - Request Parameters - Page 1:
2025-06-26 15:00:17,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:17,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:17,897 - INFO - Response - Page 1:
2025-06-26 15:00:18,100 - INFO - 第 1 页获取到 100 条记录
2025-06-26 15:00:18,100 - INFO - Request Parameters - Page 2:
2025-06-26 15:00:18,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:18,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:18,615 - INFO - Response - Page 2:
2025-06-26 15:00:18,819 - INFO - 第 2 页获取到 100 条记录
2025-06-26 15:00:18,819 - INFO - Request Parameters - Page 3:
2025-06-26 15:00:18,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:18,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:19,334 - INFO - Response - Page 3:
2025-06-26 15:00:19,537 - INFO - 第 3 页获取到 100 条记录
2025-06-26 15:00:19,537 - INFO - Request Parameters - Page 4:
2025-06-26 15:00:19,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:19,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:20,287 - INFO - Response - Page 4:
2025-06-26 15:00:20,490 - INFO - 第 4 页获取到 100 条记录
2025-06-26 15:00:20,490 - INFO - Request Parameters - Page 5:
2025-06-26 15:00:20,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:20,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:21,022 - INFO - Response - Page 5:
2025-06-26 15:00:21,225 - INFO - 第 5 页获取到 100 条记录
2025-06-26 15:00:21,225 - INFO - Request Parameters - Page 6:
2025-06-26 15:00:21,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:21,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:21,725 - INFO - Response - Page 6:
2025-06-26 15:00:21,928 - INFO - 第 6 页获取到 100 条记录
2025-06-26 15:00:21,928 - INFO - Request Parameters - Page 7:
2025-06-26 15:00:21,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:21,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:22,444 - INFO - Response - Page 7:
2025-06-26 15:00:22,647 - INFO - 第 7 页获取到 61 条记录
2025-06-26 15:00:22,647 - INFO - 查询完成，共获取到 661 条记录
2025-06-26 15:00:22,647 - INFO - 获取到 661 条表单数据
2025-06-26 15:00:22,662 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-26 15:00:22,678 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 15:00:22,678 - INFO - 开始处理日期: 2025-04
2025-06-26 15:00:22,678 - INFO - Request Parameters - Page 1:
2025-06-26 15:00:22,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:22,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:23,225 - INFO - Response - Page 1:
2025-06-26 15:00:23,428 - INFO - 第 1 页获取到 100 条记录
2025-06-26 15:00:23,428 - INFO - Request Parameters - Page 2:
2025-06-26 15:00:23,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:23,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:23,944 - INFO - Response - Page 2:
2025-06-26 15:00:24,147 - INFO - 第 2 页获取到 100 条记录
2025-06-26 15:00:24,147 - INFO - Request Parameters - Page 3:
2025-06-26 15:00:24,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:24,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:24,787 - INFO - Response - Page 3:
2025-06-26 15:00:24,991 - INFO - 第 3 页获取到 100 条记录
2025-06-26 15:00:24,991 - INFO - Request Parameters - Page 4:
2025-06-26 15:00:24,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:24,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:25,537 - INFO - Response - Page 4:
2025-06-26 15:00:25,740 - INFO - 第 4 页获取到 100 条记录
2025-06-26 15:00:25,740 - INFO - Request Parameters - Page 5:
2025-06-26 15:00:25,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:25,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:26,272 - INFO - Response - Page 5:
2025-06-26 15:00:26,475 - INFO - 第 5 页获取到 100 条记录
2025-06-26 15:00:26,475 - INFO - Request Parameters - Page 6:
2025-06-26 15:00:26,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:26,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:26,928 - INFO - Response - Page 6:
2025-06-26 15:00:27,131 - INFO - 第 6 页获取到 100 条记录
2025-06-26 15:00:27,131 - INFO - Request Parameters - Page 7:
2025-06-26 15:00:27,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:27,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:27,631 - INFO - Response - Page 7:
2025-06-26 15:00:27,834 - INFO - 第 7 页获取到 56 条记录
2025-06-26 15:00:27,834 - INFO - 查询完成，共获取到 656 条记录
2025-06-26 15:00:27,834 - INFO - 获取到 656 条表单数据
2025-06-26 15:00:27,834 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-26 15:00:27,850 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 15:00:27,850 - INFO - 开始处理日期: 2025-05
2025-06-26 15:00:27,850 - INFO - Request Parameters - Page 1:
2025-06-26 15:00:27,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:27,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:28,350 - INFO - Response - Page 1:
2025-06-26 15:00:28,553 - INFO - 第 1 页获取到 100 条记录
2025-06-26 15:00:28,553 - INFO - Request Parameters - Page 2:
2025-06-26 15:00:28,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:28,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:29,209 - INFO - Response - Page 2:
2025-06-26 15:00:29,412 - INFO - 第 2 页获取到 100 条记录
2025-06-26 15:00:29,412 - INFO - Request Parameters - Page 3:
2025-06-26 15:00:29,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:29,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:30,037 - INFO - Response - Page 3:
2025-06-26 15:00:30,240 - INFO - 第 3 页获取到 100 条记录
2025-06-26 15:00:30,240 - INFO - Request Parameters - Page 4:
2025-06-26 15:00:30,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:30,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:30,725 - INFO - Response - Page 4:
2025-06-26 15:00:30,928 - INFO - 第 4 页获取到 100 条记录
2025-06-26 15:00:30,928 - INFO - Request Parameters - Page 5:
2025-06-26 15:00:30,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:30,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:31,381 - INFO - Response - Page 5:
2025-06-26 15:00:31,584 - INFO - 第 5 页获取到 100 条记录
2025-06-26 15:00:31,584 - INFO - Request Parameters - Page 6:
2025-06-26 15:00:31,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:31,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:32,147 - INFO - Response - Page 6:
2025-06-26 15:00:32,350 - INFO - 第 6 页获取到 100 条记录
2025-06-26 15:00:32,350 - INFO - Request Parameters - Page 7:
2025-06-26 15:00:32,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:32,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:32,787 - INFO - Response - Page 7:
2025-06-26 15:00:32,990 - INFO - 第 7 页获取到 65 条记录
2025-06-26 15:00:32,990 - INFO - 查询完成，共获取到 665 条记录
2025-06-26 15:00:32,990 - INFO - 获取到 665 条表单数据
2025-06-26 15:00:32,990 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-26 15:00:33,006 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 15:00:33,006 - INFO - 开始处理日期: 2025-06
2025-06-26 15:00:33,006 - INFO - Request Parameters - Page 1:
2025-06-26 15:00:33,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:33,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:33,522 - INFO - Response - Page 1:
2025-06-26 15:00:33,725 - INFO - 第 1 页获取到 100 条记录
2025-06-26 15:00:33,725 - INFO - Request Parameters - Page 2:
2025-06-26 15:00:33,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:33,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:34,256 - INFO - Response - Page 2:
2025-06-26 15:00:34,459 - INFO - 第 2 页获取到 100 条记录
2025-06-26 15:00:34,459 - INFO - Request Parameters - Page 3:
2025-06-26 15:00:34,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:34,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:35,037 - INFO - Response - Page 3:
2025-06-26 15:00:35,240 - INFO - 第 3 页获取到 100 条记录
2025-06-26 15:00:35,240 - INFO - Request Parameters - Page 4:
2025-06-26 15:00:35,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:35,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:35,756 - INFO - Response - Page 4:
2025-06-26 15:00:35,959 - INFO - 第 4 页获取到 100 条记录
2025-06-26 15:00:35,959 - INFO - Request Parameters - Page 5:
2025-06-26 15:00:35,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:35,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:36,506 - INFO - Response - Page 5:
2025-06-26 15:00:36,709 - INFO - 第 5 页获取到 100 条记录
2025-06-26 15:00:36,709 - INFO - Request Parameters - Page 6:
2025-06-26 15:00:36,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:36,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:37,287 - INFO - Response - Page 6:
2025-06-26 15:00:37,490 - INFO - 第 6 页获取到 100 条记录
2025-06-26 15:00:37,490 - INFO - Request Parameters - Page 7:
2025-06-26 15:00:37,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 15:00:37,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 15:00:37,865 - INFO - Response - Page 7:
2025-06-26 15:00:38,068 - INFO - 第 7 页获取到 26 条记录
2025-06-26 15:00:38,068 - INFO - 查询完成，共获取到 626 条记录
2025-06-26 15:00:38,068 - INFO - 获取到 626 条表单数据
2025-06-26 15:00:38,068 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-26 15:00:38,068 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-26 15:00:38,600 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-26 15:00:38,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93265.29, 'new_value': 98439.29}, {'field': 'total_amount', 'old_value': 114066.95, 'new_value': 119240.95}, {'field': 'order_count', 'old_value': 2919, 'new_value': 3037}]
2025-06-26 15:00:38,600 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-26 15:00:39,084 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-26 15:00:39,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 674847.22, 'new_value': 675283.68}, {'field': 'total_amount', 'old_value': 719681.13, 'new_value': 720117.59}]
2025-06-26 15:00:39,084 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFC
2025-06-26 15:00:39,662 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFC
2025-06-26 15:00:39,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5306.16, 'new_value': 6087.16}, {'field': 'total_amount', 'old_value': 55943.7, 'new_value': 56724.7}, {'field': 'order_count', 'old_value': 3621, 'new_value': 3648}]
2025-06-26 15:00:39,662 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-26 15:00:40,084 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-26 15:00:40,084 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 29.9}, {'field': 'offline_amount', 'old_value': 21606.9, 'new_value': 21706.9}, {'field': 'total_amount', 'old_value': 21606.9, 'new_value': 21736.8}, {'field': 'order_count', 'old_value': 50, 'new_value': 52}]
2025-06-26 15:00:40,100 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-26 15:00:40,553 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-26 15:00:40,553 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5599.58, 'new_value': 5925.5}, {'field': 'offline_amount', 'old_value': 117064.14, 'new_value': 122402.54}, {'field': 'total_amount', 'old_value': 122663.72, 'new_value': 128328.04}, {'field': 'order_count', 'old_value': 4909, 'new_value': 5150}]
2025-06-26 15:00:40,553 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY21
2025-06-26 15:00:40,990 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY21
2025-06-26 15:00:40,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 828436.0, 'new_value': 846593.0}, {'field': 'total_amount', 'old_value': 828436.0, 'new_value': 846593.0}, {'field': 'order_count', 'old_value': 3002, 'new_value': 3090}]
2025-06-26 15:00:40,990 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM11
2025-06-26 15:00:41,459 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM11
2025-06-26 15:00:41,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6160.0, 'new_value': 7455.0}, {'field': 'total_amount', 'old_value': 48196.0, 'new_value': 49491.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-26 15:00:41,459 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-26 15:00:41,897 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-26 15:00:41,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87488.07, 'new_value': 95661.07}, {'field': 'total_amount', 'old_value': 223221.37, 'new_value': 231394.37}, {'field': 'order_count', 'old_value': 10961, 'new_value': 11414}]
2025-06-26 15:00:41,897 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-26 15:00:42,350 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-26 15:00:42,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350890.2, 'new_value': 342130.2}, {'field': 'total_amount', 'old_value': 395684.0, 'new_value': 386924.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 125}]
2025-06-26 15:00:42,350 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-26 15:00:42,928 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-26 15:00:42,928 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35643.0, 'new_value': 36711.0}, {'field': 'offline_amount', 'old_value': 339044.0, 'new_value': 354944.0}, {'field': 'total_amount', 'old_value': 374687.0, 'new_value': 391655.0}, {'field': 'order_count', 'old_value': 334, 'new_value': 350}]
2025-06-26 15:00:42,928 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI3
2025-06-26 15:00:43,318 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI3
2025-06-26 15:00:43,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65412.87, 'new_value': 67522.68}, {'field': 'total_amount', 'old_value': 68832.62, 'new_value': 70942.43}, {'field': 'order_count', 'old_value': 523, 'new_value': 543}]
2025-06-26 15:00:43,318 - INFO - 日期 2025-06 处理完成 - 更新: 11 条，插入: 0 条，错误: 0 条
2025-06-26 15:00:43,318 - INFO - 数据同步完成！更新: 11 条，插入: 0 条，错误: 0 条
2025-06-26 15:00:43,318 - INFO - =================同步完成====================
2025-06-26 18:00:02,428 - INFO - =================使用默认全量同步=============
2025-06-26 18:00:04,209 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-26 18:00:04,209 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-26 18:00:04,240 - INFO - 开始处理日期: 2025-01
2025-06-26 18:00:04,240 - INFO - Request Parameters - Page 1:
2025-06-26 18:00:04,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:04,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:05,522 - INFO - Response - Page 1:
2025-06-26 18:00:05,725 - INFO - 第 1 页获取到 100 条记录
2025-06-26 18:00:05,725 - INFO - Request Parameters - Page 2:
2025-06-26 18:00:05,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:05,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:06,631 - INFO - Response - Page 2:
2025-06-26 18:00:06,834 - INFO - 第 2 页获取到 100 条记录
2025-06-26 18:00:06,834 - INFO - Request Parameters - Page 3:
2025-06-26 18:00:06,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:06,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:07,397 - INFO - Response - Page 3:
2025-06-26 18:00:07,600 - INFO - 第 3 页获取到 100 条记录
2025-06-26 18:00:07,600 - INFO - Request Parameters - Page 4:
2025-06-26 18:00:07,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:07,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:08,100 - INFO - Response - Page 4:
2025-06-26 18:00:08,303 - INFO - 第 4 页获取到 100 条记录
2025-06-26 18:00:08,303 - INFO - Request Parameters - Page 5:
2025-06-26 18:00:08,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:08,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:08,881 - INFO - Response - Page 5:
2025-06-26 18:00:09,084 - INFO - 第 5 页获取到 100 条记录
2025-06-26 18:00:09,084 - INFO - Request Parameters - Page 6:
2025-06-26 18:00:09,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:09,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:10,084 - INFO - Response - Page 6:
2025-06-26 18:00:10,287 - INFO - 第 6 页获取到 100 条记录
2025-06-26 18:00:10,287 - INFO - Request Parameters - Page 7:
2025-06-26 18:00:10,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:10,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:10,803 - INFO - Response - Page 7:
2025-06-26 18:00:11,006 - INFO - 第 7 页获取到 82 条记录
2025-06-26 18:00:11,006 - INFO - 查询完成，共获取到 682 条记录
2025-06-26 18:00:11,006 - INFO - 获取到 682 条表单数据
2025-06-26 18:00:11,006 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-26 18:00:11,022 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 18:00:11,022 - INFO - 开始处理日期: 2025-02
2025-06-26 18:00:11,022 - INFO - Request Parameters - Page 1:
2025-06-26 18:00:11,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:11,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:11,475 - INFO - Response - Page 1:
2025-06-26 18:00:11,678 - INFO - 第 1 页获取到 100 条记录
2025-06-26 18:00:11,678 - INFO - Request Parameters - Page 2:
2025-06-26 18:00:11,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:11,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:12,194 - INFO - Response - Page 2:
2025-06-26 18:00:12,397 - INFO - 第 2 页获取到 100 条记录
2025-06-26 18:00:12,397 - INFO - Request Parameters - Page 3:
2025-06-26 18:00:12,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:12,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:12,928 - INFO - Response - Page 3:
2025-06-26 18:00:13,131 - INFO - 第 3 页获取到 100 条记录
2025-06-26 18:00:13,131 - INFO - Request Parameters - Page 4:
2025-06-26 18:00:13,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:13,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:13,662 - INFO - Response - Page 4:
2025-06-26 18:00:13,865 - INFO - 第 4 页获取到 100 条记录
2025-06-26 18:00:13,865 - INFO - Request Parameters - Page 5:
2025-06-26 18:00:13,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:13,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:14,318 - INFO - Response - Page 5:
2025-06-26 18:00:14,522 - INFO - 第 5 页获取到 100 条记录
2025-06-26 18:00:14,522 - INFO - Request Parameters - Page 6:
2025-06-26 18:00:14,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:14,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:14,990 - INFO - Response - Page 6:
2025-06-26 18:00:15,193 - INFO - 第 6 页获取到 100 条记录
2025-06-26 18:00:15,193 - INFO - Request Parameters - Page 7:
2025-06-26 18:00:15,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:15,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:15,725 - INFO - Response - Page 7:
2025-06-26 18:00:15,928 - INFO - 第 7 页获取到 70 条记录
2025-06-26 18:00:15,928 - INFO - 查询完成，共获取到 670 条记录
2025-06-26 18:00:15,928 - INFO - 获取到 670 条表单数据
2025-06-26 18:00:15,928 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-26 18:00:15,944 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 18:00:15,944 - INFO - 开始处理日期: 2025-03
2025-06-26 18:00:15,944 - INFO - Request Parameters - Page 1:
2025-06-26 18:00:15,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:15,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:16,443 - INFO - Response - Page 1:
2025-06-26 18:00:16,647 - INFO - 第 1 页获取到 100 条记录
2025-06-26 18:00:16,647 - INFO - Request Parameters - Page 2:
2025-06-26 18:00:16,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:16,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:17,287 - INFO - Response - Page 2:
2025-06-26 18:00:17,490 - INFO - 第 2 页获取到 100 条记录
2025-06-26 18:00:17,490 - INFO - Request Parameters - Page 3:
2025-06-26 18:00:17,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:17,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:17,959 - INFO - Response - Page 3:
2025-06-26 18:00:18,162 - INFO - 第 3 页获取到 100 条记录
2025-06-26 18:00:18,162 - INFO - Request Parameters - Page 4:
2025-06-26 18:00:18,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:18,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:18,662 - INFO - Response - Page 4:
2025-06-26 18:00:18,865 - INFO - 第 4 页获取到 100 条记录
2025-06-26 18:00:18,865 - INFO - Request Parameters - Page 5:
2025-06-26 18:00:18,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:18,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:19,334 - INFO - Response - Page 5:
2025-06-26 18:00:19,537 - INFO - 第 5 页获取到 100 条记录
2025-06-26 18:00:19,537 - INFO - Request Parameters - Page 6:
2025-06-26 18:00:19,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:19,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:20,006 - INFO - Response - Page 6:
2025-06-26 18:00:20,209 - INFO - 第 6 页获取到 100 条记录
2025-06-26 18:00:20,209 - INFO - Request Parameters - Page 7:
2025-06-26 18:00:20,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:20,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:20,662 - INFO - Response - Page 7:
2025-06-26 18:00:20,865 - INFO - 第 7 页获取到 61 条记录
2025-06-26 18:00:20,865 - INFO - 查询完成，共获取到 661 条记录
2025-06-26 18:00:20,865 - INFO - 获取到 661 条表单数据
2025-06-26 18:00:20,865 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-26 18:00:20,881 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 18:00:20,881 - INFO - 开始处理日期: 2025-04
2025-06-26 18:00:20,881 - INFO - Request Parameters - Page 1:
2025-06-26 18:00:20,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:20,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:21,522 - INFO - Response - Page 1:
2025-06-26 18:00:21,725 - INFO - 第 1 页获取到 100 条记录
2025-06-26 18:00:21,725 - INFO - Request Parameters - Page 2:
2025-06-26 18:00:21,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:21,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:22,256 - INFO - Response - Page 2:
2025-06-26 18:00:22,459 - INFO - 第 2 页获取到 100 条记录
2025-06-26 18:00:22,459 - INFO - Request Parameters - Page 3:
2025-06-26 18:00:22,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:22,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:22,990 - INFO - Response - Page 3:
2025-06-26 18:00:23,193 - INFO - 第 3 页获取到 100 条记录
2025-06-26 18:00:23,193 - INFO - Request Parameters - Page 4:
2025-06-26 18:00:23,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:23,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:23,756 - INFO - Response - Page 4:
2025-06-26 18:00:23,959 - INFO - 第 4 页获取到 100 条记录
2025-06-26 18:00:23,959 - INFO - Request Parameters - Page 5:
2025-06-26 18:00:23,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:23,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:24,522 - INFO - Response - Page 5:
2025-06-26 18:00:24,725 - INFO - 第 5 页获取到 100 条记录
2025-06-26 18:00:24,725 - INFO - Request Parameters - Page 6:
2025-06-26 18:00:24,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:24,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:25,178 - INFO - Response - Page 6:
2025-06-26 18:00:25,381 - INFO - 第 6 页获取到 100 条记录
2025-06-26 18:00:25,381 - INFO - Request Parameters - Page 7:
2025-06-26 18:00:25,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:25,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:25,865 - INFO - Response - Page 7:
2025-06-26 18:00:26,068 - INFO - 第 7 页获取到 56 条记录
2025-06-26 18:00:26,068 - INFO - 查询完成，共获取到 656 条记录
2025-06-26 18:00:26,068 - INFO - 获取到 656 条表单数据
2025-06-26 18:00:26,068 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-26 18:00:26,084 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 18:00:26,084 - INFO - 开始处理日期: 2025-05
2025-06-26 18:00:26,084 - INFO - Request Parameters - Page 1:
2025-06-26 18:00:26,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:26,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:26,553 - INFO - Response - Page 1:
2025-06-26 18:00:26,756 - INFO - 第 1 页获取到 100 条记录
2025-06-26 18:00:26,756 - INFO - Request Parameters - Page 2:
2025-06-26 18:00:26,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:26,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:27,303 - INFO - Response - Page 2:
2025-06-26 18:00:27,506 - INFO - 第 2 页获取到 100 条记录
2025-06-26 18:00:27,506 - INFO - Request Parameters - Page 3:
2025-06-26 18:00:27,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:27,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:27,959 - INFO - Response - Page 3:
2025-06-26 18:00:28,162 - INFO - 第 3 页获取到 100 条记录
2025-06-26 18:00:28,162 - INFO - Request Parameters - Page 4:
2025-06-26 18:00:28,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:28,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:28,850 - INFO - Response - Page 4:
2025-06-26 18:00:29,053 - INFO - 第 4 页获取到 100 条记录
2025-06-26 18:00:29,053 - INFO - Request Parameters - Page 5:
2025-06-26 18:00:29,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:29,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:29,600 - INFO - Response - Page 5:
2025-06-26 18:00:29,803 - INFO - 第 5 页获取到 100 条记录
2025-06-26 18:00:29,803 - INFO - Request Parameters - Page 6:
2025-06-26 18:00:29,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:29,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:30,459 - INFO - Response - Page 6:
2025-06-26 18:00:30,662 - INFO - 第 6 页获取到 100 条记录
2025-06-26 18:00:30,662 - INFO - Request Parameters - Page 7:
2025-06-26 18:00:30,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:30,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:31,131 - INFO - Response - Page 7:
2025-06-26 18:00:31,334 - INFO - 第 7 页获取到 65 条记录
2025-06-26 18:00:31,334 - INFO - 查询完成，共获取到 665 条记录
2025-06-26 18:00:31,334 - INFO - 获取到 665 条表单数据
2025-06-26 18:00:31,334 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-26 18:00:31,350 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 18:00:31,350 - INFO - 开始处理日期: 2025-06
2025-06-26 18:00:31,350 - INFO - Request Parameters - Page 1:
2025-06-26 18:00:31,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:31,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:31,865 - INFO - Response - Page 1:
2025-06-26 18:00:32,068 - INFO - 第 1 页获取到 100 条记录
2025-06-26 18:00:32,068 - INFO - Request Parameters - Page 2:
2025-06-26 18:00:32,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:32,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:32,584 - INFO - Response - Page 2:
2025-06-26 18:00:32,787 - INFO - 第 2 页获取到 100 条记录
2025-06-26 18:00:32,787 - INFO - Request Parameters - Page 3:
2025-06-26 18:00:32,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:32,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:33,287 - INFO - Response - Page 3:
2025-06-26 18:00:33,490 - INFO - 第 3 页获取到 100 条记录
2025-06-26 18:00:33,490 - INFO - Request Parameters - Page 4:
2025-06-26 18:00:33,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:33,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:34,209 - INFO - Response - Page 4:
2025-06-26 18:00:34,412 - INFO - 第 4 页获取到 100 条记录
2025-06-26 18:00:34,412 - INFO - Request Parameters - Page 5:
2025-06-26 18:00:34,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:34,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:35,193 - INFO - Response - Page 5:
2025-06-26 18:00:35,396 - INFO - 第 5 页获取到 100 条记录
2025-06-26 18:00:35,396 - INFO - Request Parameters - Page 6:
2025-06-26 18:00:35,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:35,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:35,928 - INFO - Response - Page 6:
2025-06-26 18:00:36,131 - INFO - 第 6 页获取到 100 条记录
2025-06-26 18:00:36,131 - INFO - Request Parameters - Page 7:
2025-06-26 18:00:36,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 18:00:36,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 18:00:36,584 - INFO - Response - Page 7:
2025-06-26 18:00:36,787 - INFO - 第 7 页获取到 26 条记录
2025-06-26 18:00:36,787 - INFO - 查询完成，共获取到 626 条记录
2025-06-26 18:00:36,787 - INFO - 获取到 626 条表单数据
2025-06-26 18:00:36,787 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-26 18:00:36,803 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-26 18:00:37,209 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-26 18:00:37,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69309.87, 'new_value': 72309.69}, {'field': 'offline_amount', 'old_value': 81975.36, 'new_value': 88669.88}, {'field': 'total_amount', 'old_value': 151285.23, 'new_value': 160979.57}, {'field': 'order_count', 'old_value': 7904, 'new_value': 8377}]
2025-06-26 18:00:37,225 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-26 18:00:37,225 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-26 18:00:37,225 - INFO - =================同步完成====================
2025-06-26 21:00:03,521 - INFO - =================使用默认全量同步=============
2025-06-26 21:00:05,302 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-26 21:00:05,302 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-26 21:00:05,334 - INFO - 开始处理日期: 2025-01
2025-06-26 21:00:05,349 - INFO - Request Parameters - Page 1:
2025-06-26 21:00:05,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:05,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:06,365 - INFO - Response - Page 1:
2025-06-26 21:00:06,568 - INFO - 第 1 页获取到 100 条记录
2025-06-26 21:00:06,568 - INFO - Request Parameters - Page 2:
2025-06-26 21:00:06,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:06,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:07,615 - INFO - Response - Page 2:
2025-06-26 21:00:07,818 - INFO - 第 2 页获取到 100 条记录
2025-06-26 21:00:07,818 - INFO - Request Parameters - Page 3:
2025-06-26 21:00:07,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:07,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:08,334 - INFO - Response - Page 3:
2025-06-26 21:00:08,537 - INFO - 第 3 页获取到 100 条记录
2025-06-26 21:00:08,537 - INFO - Request Parameters - Page 4:
2025-06-26 21:00:08,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:08,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:09,022 - INFO - Response - Page 4:
2025-06-26 21:00:09,225 - INFO - 第 4 页获取到 100 条记录
2025-06-26 21:00:09,225 - INFO - Request Parameters - Page 5:
2025-06-26 21:00:09,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:09,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:09,787 - INFO - Response - Page 5:
2025-06-26 21:00:09,990 - INFO - 第 5 页获取到 100 条记录
2025-06-26 21:00:09,990 - INFO - Request Parameters - Page 6:
2025-06-26 21:00:09,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:09,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:10,553 - INFO - Response - Page 6:
2025-06-26 21:00:10,756 - INFO - 第 6 页获取到 100 条记录
2025-06-26 21:00:10,756 - INFO - Request Parameters - Page 7:
2025-06-26 21:00:10,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:10,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:11,319 - INFO - Response - Page 7:
2025-06-26 21:00:11,522 - INFO - 第 7 页获取到 82 条记录
2025-06-26 21:00:11,522 - INFO - 查询完成，共获取到 682 条记录
2025-06-26 21:00:11,522 - INFO - 获取到 682 条表单数据
2025-06-26 21:00:11,522 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-26 21:00:11,538 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 21:00:11,538 - INFO - 开始处理日期: 2025-02
2025-06-26 21:00:11,538 - INFO - Request Parameters - Page 1:
2025-06-26 21:00:11,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:11,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:12,085 - INFO - Response - Page 1:
2025-06-26 21:00:12,288 - INFO - 第 1 页获取到 100 条记录
2025-06-26 21:00:12,288 - INFO - Request Parameters - Page 2:
2025-06-26 21:00:12,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:12,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:12,850 - INFO - Response - Page 2:
2025-06-26 21:00:13,053 - INFO - 第 2 页获取到 100 条记录
2025-06-26 21:00:13,053 - INFO - Request Parameters - Page 3:
2025-06-26 21:00:13,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:13,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:13,554 - INFO - Response - Page 3:
2025-06-26 21:00:13,757 - INFO - 第 3 页获取到 100 条记录
2025-06-26 21:00:13,757 - INFO - Request Parameters - Page 4:
2025-06-26 21:00:13,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:13,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:14,350 - INFO - Response - Page 4:
2025-06-26 21:00:14,554 - INFO - 第 4 页获取到 100 条记录
2025-06-26 21:00:14,554 - INFO - Request Parameters - Page 5:
2025-06-26 21:00:14,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:14,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:15,054 - INFO - Response - Page 5:
2025-06-26 21:00:15,257 - INFO - 第 5 页获取到 100 条记录
2025-06-26 21:00:15,257 - INFO - Request Parameters - Page 6:
2025-06-26 21:00:15,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:15,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:15,726 - INFO - Response - Page 6:
2025-06-26 21:00:15,929 - INFO - 第 6 页获取到 100 条记录
2025-06-26 21:00:15,929 - INFO - Request Parameters - Page 7:
2025-06-26 21:00:15,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:15,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:16,570 - INFO - Response - Page 7:
2025-06-26 21:00:16,773 - INFO - 第 7 页获取到 70 条记录
2025-06-26 21:00:16,773 - INFO - 查询完成，共获取到 670 条记录
2025-06-26 21:00:16,773 - INFO - 获取到 670 条表单数据
2025-06-26 21:00:16,773 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-26 21:00:16,788 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 21:00:16,788 - INFO - 开始处理日期: 2025-03
2025-06-26 21:00:16,788 - INFO - Request Parameters - Page 1:
2025-06-26 21:00:16,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:16,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:17,273 - INFO - Response - Page 1:
2025-06-26 21:00:17,476 - INFO - 第 1 页获取到 100 条记录
2025-06-26 21:00:17,476 - INFO - Request Parameters - Page 2:
2025-06-26 21:00:17,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:17,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:18,210 - INFO - Response - Page 2:
2025-06-26 21:00:18,414 - INFO - 第 2 页获取到 100 条记录
2025-06-26 21:00:18,414 - INFO - Request Parameters - Page 3:
2025-06-26 21:00:18,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:18,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:18,945 - INFO - Response - Page 3:
2025-06-26 21:00:19,148 - INFO - 第 3 页获取到 100 条记录
2025-06-26 21:00:19,148 - INFO - Request Parameters - Page 4:
2025-06-26 21:00:19,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:19,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:19,664 - INFO - Response - Page 4:
2025-06-26 21:00:19,867 - INFO - 第 4 页获取到 100 条记录
2025-06-26 21:00:19,867 - INFO - Request Parameters - Page 5:
2025-06-26 21:00:19,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:19,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:20,383 - INFO - Response - Page 5:
2025-06-26 21:00:20,586 - INFO - 第 5 页获取到 100 条记录
2025-06-26 21:00:20,586 - INFO - Request Parameters - Page 6:
2025-06-26 21:00:20,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:20,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:21,101 - INFO - Response - Page 6:
2025-06-26 21:00:21,305 - INFO - 第 6 页获取到 100 条记录
2025-06-26 21:00:21,305 - INFO - Request Parameters - Page 7:
2025-06-26 21:00:21,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:21,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:21,898 - INFO - Response - Page 7:
2025-06-26 21:00:22,102 - INFO - 第 7 页获取到 61 条记录
2025-06-26 21:00:22,102 - INFO - 查询完成，共获取到 661 条记录
2025-06-26 21:00:22,102 - INFO - 获取到 661 条表单数据
2025-06-26 21:00:22,102 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-26 21:00:22,117 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 21:00:22,117 - INFO - 开始处理日期: 2025-04
2025-06-26 21:00:22,117 - INFO - Request Parameters - Page 1:
2025-06-26 21:00:22,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:22,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:22,664 - INFO - Response - Page 1:
2025-06-26 21:00:22,867 - INFO - 第 1 页获取到 100 条记录
2025-06-26 21:00:22,867 - INFO - Request Parameters - Page 2:
2025-06-26 21:00:22,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:22,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:23,477 - INFO - Response - Page 2:
2025-06-26 21:00:23,680 - INFO - 第 2 页获取到 100 条记录
2025-06-26 21:00:23,680 - INFO - Request Parameters - Page 3:
2025-06-26 21:00:23,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:23,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:24,133 - INFO - Response - Page 3:
2025-06-26 21:00:24,336 - INFO - 第 3 页获取到 100 条记录
2025-06-26 21:00:24,336 - INFO - Request Parameters - Page 4:
2025-06-26 21:00:24,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:24,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:24,852 - INFO - Response - Page 4:
2025-06-26 21:00:25,071 - INFO - 第 4 页获取到 100 条记录
2025-06-26 21:00:25,071 - INFO - Request Parameters - Page 5:
2025-06-26 21:00:25,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:25,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:25,712 - INFO - Response - Page 5:
2025-06-26 21:00:25,915 - INFO - 第 5 页获取到 100 条记录
2025-06-26 21:00:25,915 - INFO - Request Parameters - Page 6:
2025-06-26 21:00:25,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:25,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:26,524 - INFO - Response - Page 6:
2025-06-26 21:00:26,727 - INFO - 第 6 页获取到 100 条记录
2025-06-26 21:00:26,727 - INFO - Request Parameters - Page 7:
2025-06-26 21:00:26,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:26,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:27,227 - INFO - Response - Page 7:
2025-06-26 21:00:27,431 - INFO - 第 7 页获取到 56 条记录
2025-06-26 21:00:27,431 - INFO - 查询完成，共获取到 656 条记录
2025-06-26 21:00:27,431 - INFO - 获取到 656 条表单数据
2025-06-26 21:00:27,431 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-26 21:00:27,446 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 21:00:27,446 - INFO - 开始处理日期: 2025-05
2025-06-26 21:00:27,446 - INFO - Request Parameters - Page 1:
2025-06-26 21:00:27,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:27,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:28,009 - INFO - Response - Page 1:
2025-06-26 21:00:28,212 - INFO - 第 1 页获取到 100 条记录
2025-06-26 21:00:28,212 - INFO - Request Parameters - Page 2:
2025-06-26 21:00:28,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:28,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:28,728 - INFO - Response - Page 2:
2025-06-26 21:00:28,931 - INFO - 第 2 页获取到 100 条记录
2025-06-26 21:00:28,931 - INFO - Request Parameters - Page 3:
2025-06-26 21:00:28,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:28,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:29,400 - INFO - Response - Page 3:
2025-06-26 21:00:29,603 - INFO - 第 3 页获取到 100 条记录
2025-06-26 21:00:29,603 - INFO - Request Parameters - Page 4:
2025-06-26 21:00:29,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:29,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:30,368 - INFO - Response - Page 4:
2025-06-26 21:00:30,572 - INFO - 第 4 页获取到 100 条记录
2025-06-26 21:00:30,572 - INFO - Request Parameters - Page 5:
2025-06-26 21:00:30,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:30,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:31,119 - INFO - Response - Page 5:
2025-06-26 21:00:31,322 - INFO - 第 5 页获取到 100 条记录
2025-06-26 21:00:31,322 - INFO - Request Parameters - Page 6:
2025-06-26 21:00:31,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:31,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:31,822 - INFO - Response - Page 6:
2025-06-26 21:00:32,025 - INFO - 第 6 页获取到 100 条记录
2025-06-26 21:00:32,025 - INFO - Request Parameters - Page 7:
2025-06-26 21:00:32,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:32,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:32,478 - INFO - Response - Page 7:
2025-06-26 21:00:32,681 - INFO - 第 7 页获取到 65 条记录
2025-06-26 21:00:32,681 - INFO - 查询完成，共获取到 665 条记录
2025-06-26 21:00:32,681 - INFO - 获取到 665 条表单数据
2025-06-26 21:00:32,681 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-26 21:00:32,697 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 21:00:32,697 - INFO - 开始处理日期: 2025-06
2025-06-26 21:00:32,697 - INFO - Request Parameters - Page 1:
2025-06-26 21:00:32,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:32,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:33,244 - INFO - Response - Page 1:
2025-06-26 21:00:33,447 - INFO - 第 1 页获取到 100 条记录
2025-06-26 21:00:33,447 - INFO - Request Parameters - Page 2:
2025-06-26 21:00:33,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:33,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:33,947 - INFO - Response - Page 2:
2025-06-26 21:00:34,150 - INFO - 第 2 页获取到 100 条记录
2025-06-26 21:00:34,150 - INFO - Request Parameters - Page 3:
2025-06-26 21:00:34,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:34,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:34,650 - INFO - Response - Page 3:
2025-06-26 21:00:34,854 - INFO - 第 3 页获取到 100 条记录
2025-06-26 21:00:34,854 - INFO - Request Parameters - Page 4:
2025-06-26 21:00:34,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:34,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:35,400 - INFO - Response - Page 4:
2025-06-26 21:00:35,604 - INFO - 第 4 页获取到 100 条记录
2025-06-26 21:00:35,604 - INFO - Request Parameters - Page 5:
2025-06-26 21:00:35,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:35,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:36,197 - INFO - Response - Page 5:
2025-06-26 21:00:36,401 - INFO - 第 5 页获取到 100 条记录
2025-06-26 21:00:36,401 - INFO - Request Parameters - Page 6:
2025-06-26 21:00:36,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:36,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:36,869 - INFO - Response - Page 6:
2025-06-26 21:00:37,073 - INFO - 第 6 页获取到 100 条记录
2025-06-26 21:00:37,073 - INFO - Request Parameters - Page 7:
2025-06-26 21:00:37,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 21:00:37,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 21:00:37,448 - INFO - Response - Page 7:
2025-06-26 21:00:37,651 - INFO - 第 7 页获取到 26 条记录
2025-06-26 21:00:37,651 - INFO - 查询完成，共获取到 626 条记录
2025-06-26 21:00:37,651 - INFO - 获取到 626 条表单数据
2025-06-26 21:00:37,651 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-26 21:00:37,651 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-26 21:00:38,120 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-26 21:00:38,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 918727.35, 'new_value': 923430.34}, {'field': 'total_amount', 'old_value': 998636.98, 'new_value': 1003339.97}]
2025-06-26 21:00:38,120 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-26 21:00:38,588 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-26 21:00:38,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 754525.0, 'new_value': 739739.0}, {'field': 'total_amount', 'old_value': 754525.0, 'new_value': 739739.0}, {'field': 'order_count', 'old_value': 2517, 'new_value': 2582}]
2025-06-26 21:00:38,588 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-26 21:00:39,057 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-26 21:00:39,057 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82237.0, 'new_value': 85360.0}, {'field': 'offline_amount', 'old_value': 100021.0, 'new_value': 104133.0}, {'field': 'total_amount', 'old_value': 182258.0, 'new_value': 189493.0}, {'field': 'order_count', 'old_value': 3852, 'new_value': 3984}]
2025-06-26 21:00:39,057 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-26 21:00:39,464 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-26 21:00:39,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74773.0, 'new_value': 80773.0}, {'field': 'total_amount', 'old_value': 81233.4, 'new_value': 87233.4}, {'field': 'order_count', 'old_value': 73, 'new_value': 76}]
2025-06-26 21:00:39,464 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-26 21:00:39,886 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-26 21:00:39,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11306.24, 'new_value': 11689.08}, {'field': 'offline_amount', 'old_value': 28115.49, 'new_value': 28935.69}, {'field': 'total_amount', 'old_value': 39421.73, 'new_value': 40624.77}, {'field': 'order_count', 'old_value': 866, 'new_value': 892}]
2025-06-26 21:00:39,886 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM101
2025-06-26 21:00:40,370 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM101
2025-06-26 21:00:40,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115476.83, 'new_value': 115326.83}, {'field': 'total_amount', 'old_value': 115476.83, 'new_value': 115326.83}, {'field': 'order_count', 'old_value': 1665, 'new_value': 1680}]
2025-06-26 21:00:40,370 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-26 21:00:40,870 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-26 21:00:40,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31374.25, 'new_value': 33153.83}, {'field': 'offline_amount', 'old_value': 34211.49, 'new_value': 35434.42}, {'field': 'total_amount', 'old_value': 65585.74, 'new_value': 68588.25}, {'field': 'order_count', 'old_value': 3174, 'new_value': 3369}]
2025-06-26 21:00:40,870 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-26 21:00:41,370 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-26 21:00:41,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 324713.55, 'new_value': 338545.86}, {'field': 'offline_amount', 'old_value': 1159.0, 'new_value': 1327.0}, {'field': 'total_amount', 'old_value': 325872.55, 'new_value': 339872.86}, {'field': 'order_count', 'old_value': 4837, 'new_value': 5013}]
2025-06-26 21:00:41,370 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-26 21:00:41,808 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-26 21:00:41,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56550.57, 'new_value': 59670.48}, {'field': 'offline_amount', 'old_value': 440552.6, 'new_value': 459303.26}, {'field': 'total_amount', 'old_value': 497103.17, 'new_value': 518973.74}, {'field': 'order_count', 'old_value': 1556, 'new_value': 1615}]
2025-06-26 21:00:41,808 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-26 21:00:42,245 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-26 21:00:42,245 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124284.3, 'new_value': 126905.18}, {'field': 'total_amount', 'old_value': 125288.3, 'new_value': 127909.18}, {'field': 'order_count', 'old_value': 206, 'new_value': 208}]
2025-06-26 21:00:42,245 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-26 21:00:42,605 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-26 21:00:42,605 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29.9, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 21706.9, 'new_value': 21736.8}]
2025-06-26 21:00:42,605 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-26 21:00:43,073 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-26 21:00:43,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201272.0, 'new_value': 203272.0}, {'field': 'total_amount', 'old_value': 249256.0, 'new_value': 251256.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-06-26 21:00:43,073 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-26 21:00:43,511 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-26 21:00:43,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37083.5, 'new_value': 38762.5}, {'field': 'total_amount', 'old_value': 38287.02, 'new_value': 39966.02}, {'field': 'order_count', 'old_value': 258, 'new_value': 268}]
2025-06-26 21:00:43,511 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-26 21:00:44,027 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-26 21:00:44,027 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78720.26, 'new_value': 81547.54}, {'field': 'offline_amount', 'old_value': 814470.98, 'new_value': 866396.27}, {'field': 'total_amount', 'old_value': 893191.24, 'new_value': 947943.81}, {'field': 'order_count', 'old_value': 3799, 'new_value': 3960}]
2025-06-26 21:00:44,027 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-26 21:00:44,433 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-26 21:00:44,433 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5925.5, 'new_value': 5599.58}, {'field': 'offline_amount', 'old_value': 122402.54, 'new_value': 122728.46}]
2025-06-26 21:00:44,433 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-26 21:00:44,855 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-26 21:00:44,855 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43852.11, 'new_value': 46008.01}, {'field': 'offline_amount', 'old_value': 54510.39, 'new_value': 56075.09}, {'field': 'total_amount', 'old_value': 98362.5, 'new_value': 102083.1}, {'field': 'order_count', 'old_value': 2474, 'new_value': 2577}]
2025-06-26 21:00:44,855 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS01
2025-06-26 21:00:45,293 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS01
2025-06-26 21:00:45,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205232.33, 'new_value': 206701.53}, {'field': 'total_amount', 'old_value': 215050.6, 'new_value': 216519.8}, {'field': 'order_count', 'old_value': 9747, 'new_value': 9808}]
2025-06-26 21:00:45,308 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-26 21:00:45,730 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-26 21:00:45,730 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 977136.61, 'new_value': 1016449.71}, {'field': 'total_amount', 'old_value': 977136.61, 'new_value': 1016449.71}, {'field': 'order_count', 'old_value': 3349, 'new_value': 3486}]
2025-06-26 21:00:45,730 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-26 21:00:46,105 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-26 21:00:46,105 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95661.07, 'new_value': 87488.07}, {'field': 'offline_amount', 'old_value': 135733.3, 'new_value': 143906.3}]
2025-06-26 21:00:46,105 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-26 21:00:46,558 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-26 21:00:46,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70217.29, 'new_value': 70809.49}, {'field': 'total_amount', 'old_value': 70217.29, 'new_value': 70809.49}, {'field': 'order_count', 'old_value': 2729, 'new_value': 2798}]
2025-06-26 21:00:46,558 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-26 21:00:46,996 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-26 21:00:46,996 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151176.0, 'new_value': 152038.0}, {'field': 'total_amount', 'old_value': 151176.0, 'new_value': 152038.0}, {'field': 'order_count', 'old_value': 13242, 'new_value': 13627}]
2025-06-26 21:00:47,012 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-26 21:00:47,496 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-26 21:00:47,512 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14047.0, 'new_value': 14446.0}, {'field': 'total_amount', 'old_value': 16246.0, 'new_value': 16645.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-06-26 21:00:47,512 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-26 21:00:47,980 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-26 21:00:47,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4546.0, 'new_value': 5627.0}, {'field': 'offline_amount', 'old_value': 47175.0, 'new_value': 50959.0}, {'field': 'total_amount', 'old_value': 51721.0, 'new_value': 56586.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 79}]
2025-06-26 21:00:47,980 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-26 21:00:48,449 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-26 21:00:48,449 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36711.0, 'new_value': 35643.0}, {'field': 'offline_amount', 'old_value': 354944.0, 'new_value': 356012.0}]
2025-06-26 21:00:48,449 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-26 21:00:48,856 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-26 21:00:48,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48172.1, 'new_value': 47987.1}, {'field': 'total_amount', 'old_value': 48172.1, 'new_value': 47987.1}, {'field': 'order_count', 'old_value': 3666, 'new_value': 3982}]
2025-06-26 21:00:48,856 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-26 21:00:49,262 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-26 21:00:49,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1191000.0, 'new_value': 1201000.0}, {'field': 'total_amount', 'old_value': 1191000.0, 'new_value': 1201000.0}]
2025-06-26 21:00:49,262 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-26 21:00:49,731 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-26 21:00:49,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340200.0, 'new_value': 340700.0}, {'field': 'total_amount', 'old_value': 340200.0, 'new_value': 340700.0}, {'field': 'order_count', 'old_value': 9761, 'new_value': 9701}]
2025-06-26 21:00:49,731 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-26 21:00:50,184 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-26 21:00:50,184 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51495.0, 'new_value': 52245.0}, {'field': 'total_amount', 'old_value': 51495.0, 'new_value': 52245.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-06-26 21:00:50,184 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM53
2025-06-26 21:00:50,621 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM53
2025-06-26 21:00:50,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7980.0, 'new_value': 9780.0}, {'field': 'total_amount', 'old_value': 7980.0, 'new_value': 9780.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-26 21:00:50,621 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-26 21:00:51,122 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-26 21:00:51,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261037.04, 'new_value': 264265.04}, {'field': 'total_amount', 'old_value': 261037.04, 'new_value': 264265.04}, {'field': 'order_count', 'old_value': 101, 'new_value': 103}]
2025-06-26 21:00:51,122 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-26 21:00:51,622 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-26 21:00:51,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19615.0, 'new_value': 19983.0}, {'field': 'total_amount', 'old_value': 19615.0, 'new_value': 19983.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-26 21:00:51,622 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-26 21:00:52,075 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-26 21:00:52,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161000.0, 'new_value': 154000.0}, {'field': 'total_amount', 'old_value': 161000.0, 'new_value': 154000.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 22}]
2025-06-26 21:00:52,075 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB3
2025-06-26 21:00:52,544 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB3
2025-06-26 21:00:52,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10350.0, 'new_value': 12150.0}, {'field': 'total_amount', 'old_value': 10350.0, 'new_value': 12150.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-26 21:00:52,544 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-26 21:00:53,028 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-26 21:00:53,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121476.98, 'new_value': 120982.98}, {'field': 'total_amount', 'old_value': 121476.98, 'new_value': 120982.98}, {'field': 'order_count', 'old_value': 4409, 'new_value': 4427}]
2025-06-26 21:00:53,044 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-26 21:00:53,466 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-26 21:00:53,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37069.0, 'new_value': 33920.0}, {'field': 'total_amount', 'old_value': 37069.0, 'new_value': 33920.0}, {'field': 'order_count', 'old_value': 2905, 'new_value': 2902}]
2025-06-26 21:00:53,466 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-26 21:00:53,872 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-26 21:00:53,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12398.0, 'new_value': 12135.0}, {'field': 'total_amount', 'old_value': 12398.0, 'new_value': 12135.0}, {'field': 'order_count', 'old_value': 1111, 'new_value': 1106}]
2025-06-26 21:00:53,872 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-26 21:00:54,325 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-26 21:00:54,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 269423.35, 'new_value': 267410.35}, {'field': 'total_amount', 'old_value': 269423.35, 'new_value': 267410.35}, {'field': 'order_count', 'old_value': 2023, 'new_value': 1964}]
2025-06-26 21:00:54,325 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-26 21:00:54,841 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-26 21:00:54,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5963.0, 'new_value': 6562.0}, {'field': 'total_amount', 'old_value': 5963.0, 'new_value': 6562.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-26 21:00:54,841 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-26 21:00:55,278 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-26 21:00:55,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5112074.0, 'new_value': 5126336.0}, {'field': 'total_amount', 'old_value': 5112074.0, 'new_value': 5126336.0}, {'field': 'order_count', 'old_value': 93854, 'new_value': 94427}]
2025-06-26 21:00:55,278 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-26 21:00:55,747 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-26 21:00:55,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91104.0, 'new_value': 82104.0}, {'field': 'total_amount', 'old_value': 91104.0, 'new_value': 82104.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 33}]
2025-06-26 21:00:55,747 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-26 21:00:56,216 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-26 21:00:56,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201378.22, 'new_value': 207617.22}, {'field': 'total_amount', 'old_value': 201378.22, 'new_value': 207617.22}, {'field': 'order_count', 'old_value': 377, 'new_value': 389}]
2025-06-26 21:00:56,216 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-26 21:00:56,685 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-26 21:00:56,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5123610.0, 'new_value': 5138610.0}, {'field': 'total_amount', 'old_value': 5123610.0, 'new_value': 5138610.0}, {'field': 'order_count', 'old_value': 284, 'new_value': 388}]
2025-06-26 21:00:56,685 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-26 21:00:57,185 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-26 21:00:57,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 417334.0, 'new_value': 412334.0}, {'field': 'total_amount', 'old_value': 417334.0, 'new_value': 412334.0}, {'field': 'order_count', 'old_value': 486, 'new_value': 481}]
2025-06-26 21:00:57,185 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-26 21:00:57,591 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-26 21:00:57,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 487000.0, 'new_value': 547000.0}, {'field': 'total_amount', 'old_value': 487000.0, 'new_value': 547000.0}]
2025-06-26 21:00:57,591 - INFO - 日期 2025-06 处理完成 - 更新: 44 条，插入: 0 条，错误: 0 条
2025-06-26 21:00:57,591 - INFO - 数据同步完成！更新: 44 条，插入: 0 条，错误: 0 条
2025-06-26 21:00:57,591 - INFO - =================同步完成====================
