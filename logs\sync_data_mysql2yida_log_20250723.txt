2025-07-23 01:30:33,890 - INFO - 使用默认增量同步（当天更新数据）
2025-07-23 01:30:33,890 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-23 01:30:33,890 - INFO - 查询参数: ('2025-07-23',)
2025-07-23 01:30:33,999 - INFO - MySQL查询成功，增量数据（日期: 2025-07-23），共获取 0 条记录
2025-07-23 01:30:33,999 - ERROR - 未获取到MySQL数据
2025-07-23 01:31:34,014 - INFO - 开始同步昨天与今天的销售数据: 2025-07-22 至 2025-07-23
2025-07-23 01:31:34,014 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-23 01:31:34,014 - INFO - 查询参数: ('2025-07-22', '2025-07-23')
2025-07-23 01:31:34,171 - INFO - MySQL查询成功，时间段: 2025-07-22 至 2025-07-23，共获取 80 条记录
2025-07-23 01:31:34,171 - INFO - 获取到 1 个日期需要处理: ['2025-07-22']
2025-07-23 01:31:34,171 - INFO - 开始处理日期: 2025-07-22
2025-07-23 01:31:34,171 - INFO - Request Parameters - Page 1:
2025-07-23 01:31:34,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 01:31:34,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 01:31:42,296 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 017A3E27-EE2C-7168-BF0F-7F541BB33697 Response: {'code': 'ServiceUnavailable', 'requestid': '017A3E27-EE2C-7168-BF0F-7F541BB33697', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 017A3E27-EE2C-7168-BF0F-7F541BB33697)
2025-07-23 01:31:42,296 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-23 01:31:42,296 - INFO - 同步完成
2025-07-23 04:30:33,739 - INFO - 使用默认增量同步（当天更新数据）
2025-07-23 04:30:33,739 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-23 04:30:33,739 - INFO - 查询参数: ('2025-07-23',)
2025-07-23 04:30:33,817 - INFO - MySQL查询成功，增量数据（日期: 2025-07-23），共获取 0 条记录
2025-07-23 04:30:33,817 - ERROR - 未获取到MySQL数据
2025-07-23 04:31:33,832 - INFO - 开始同步昨天与今天的销售数据: 2025-07-22 至 2025-07-23
2025-07-23 04:31:33,832 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-23 04:31:33,832 - INFO - 查询参数: ('2025-07-22', '2025-07-23')
2025-07-23 04:31:33,973 - INFO - MySQL查询成功，时间段: 2025-07-22 至 2025-07-23，共获取 80 条记录
2025-07-23 04:31:33,973 - INFO - 获取到 1 个日期需要处理: ['2025-07-22']
2025-07-23 04:31:33,973 - INFO - 开始处理日期: 2025-07-22
2025-07-23 04:31:33,989 - INFO - Request Parameters - Page 1:
2025-07-23 04:31:33,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 04:31:33,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 04:31:42,098 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BC4AEC4E-4A69-78BD-B8BD-644484EA5A0A Response: {'code': 'ServiceUnavailable', 'requestid': 'BC4AEC4E-4A69-78BD-B8BD-644484EA5A0A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BC4AEC4E-4A69-78BD-B8BD-644484EA5A0A)
2025-07-23 04:31:42,098 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-23 04:31:42,098 - INFO - 同步完成
2025-07-23 07:30:33,901 - INFO - 使用默认增量同步（当天更新数据）
2025-07-23 07:30:33,901 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-23 07:30:33,901 - INFO - 查询参数: ('2025-07-23',)
2025-07-23 07:30:34,057 - INFO - MySQL查询成功，增量数据（日期: 2025-07-23），共获取 3 条记录
2025-07-23 07:30:34,057 - INFO - 获取到 1 个日期需要处理: ['2025-07-22']
2025-07-23 07:30:34,057 - INFO - 开始处理日期: 2025-07-22
2025-07-23 07:30:34,057 - INFO - Request Parameters - Page 1:
2025-07-23 07:30:34,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 07:30:34,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 07:30:42,182 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5E5D2B76-F3D0-74F0-A42D-5AB1951789EC Response: {'code': 'ServiceUnavailable', 'requestid': '5E5D2B76-F3D0-74F0-A42D-5AB1951789EC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5E5D2B76-F3D0-74F0-A42D-5AB1951789EC)
2025-07-23 07:30:42,182 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-23 07:31:42,197 - INFO - 开始同步昨天与今天的销售数据: 2025-07-22 至 2025-07-23
2025-07-23 07:31:42,197 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-23 07:31:42,197 - INFO - 查询参数: ('2025-07-22', '2025-07-23')
2025-07-23 07:31:42,353 - INFO - MySQL查询成功，时间段: 2025-07-22 至 2025-07-23，共获取 98 条记录
2025-07-23 07:31:42,353 - INFO - 获取到 1 个日期需要处理: ['2025-07-22']
2025-07-23 07:31:42,353 - INFO - 开始处理日期: 2025-07-22
2025-07-23 07:31:42,353 - INFO - Request Parameters - Page 1:
2025-07-23 07:31:42,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 07:31:42,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 07:31:50,291 - INFO - Response - Page 1:
2025-07-23 07:31:50,291 - INFO - 第 1 页获取到 50 条记录
2025-07-23 07:31:50,806 - INFO - Request Parameters - Page 2:
2025-07-23 07:31:50,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 07:31:50,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 07:31:51,385 - INFO - Response - Page 2:
2025-07-23 07:31:51,385 - INFO - 第 2 页获取到 18 条记录
2025-07-23 07:31:51,900 - INFO - 查询完成，共获取到 68 条记录
2025-07-23 07:31:51,900 - INFO - 获取到 68 条表单数据
2025-07-23 07:31:51,900 - INFO - 当前日期 2025-07-22 有 95 条MySQL数据需要处理
2025-07-23 07:31:51,900 - INFO - 开始批量插入 27 条新记录
2025-07-23 07:31:52,150 - INFO - 批量插入响应状态码: 200
2025-07-23 07:31:52,150 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 23:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1335', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9C86E048-5F04-7761-93AE-D21CC13FD5E1', 'x-acs-trace-id': '28e80c64aaaeb0a9568ab03c859e5fdf', 'etag': '1GGhqChhcd5UItFIhLmafLw5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 07:31:52,150 - INFO - 批量插入响应体: {'result': ['FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDM8A1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDM9A1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMAA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMBA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMCA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMDA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMEA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMFA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMGA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMHA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMIA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMJA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMKA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMLA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMMA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMNA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMOA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMPA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMQA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMRA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMSA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMTA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMUA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMVA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMWA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMXA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMYA1']}
2025-07-23 07:31:52,150 - INFO - 批量插入表单数据成功，批次 1，共 27 条记录
2025-07-23 07:31:52,150 - INFO - 成功插入的数据ID: ['FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDM8A1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDM9A1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMAA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMBA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMCA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMDA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMEA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMFA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMGA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMHA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMIA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMJA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMKA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMLA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMMA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMNA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMOA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMPA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMQA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMRA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMSA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMTA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMUA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMVA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMWA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMXA1', 'FINST-5XA66LC1G7CX90AFC3D4QD20FU8B2CHL56FDMYA1']
2025-07-23 07:31:57,166 - INFO - 批量插入完成，共 27 条记录
2025-07-23 07:31:57,166 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 27 条，错误: 0 条
2025-07-23 07:31:57,166 - INFO - 数据同步完成！更新: 0 条，插入: 27 条，错误: 0 条
2025-07-23 07:31:57,166 - INFO - 同步完成
2025-07-23 10:30:33,659 - INFO - 使用默认增量同步（当天更新数据）
2025-07-23 10:30:33,659 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-23 10:30:33,659 - INFO - 查询参数: ('2025-07-23',)
2025-07-23 10:30:33,831 - INFO - MySQL查询成功，增量数据（日期: 2025-07-23），共获取 149 条记录
2025-07-23 10:30:33,831 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 10:30:33,831 - INFO - 开始处理日期: 2025-07-22
2025-07-23 10:30:33,831 - INFO - Request Parameters - Page 1:
2025-07-23 10:30:33,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 10:30:33,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 10:30:42,159 - INFO - Response - Page 1:
2025-07-23 10:30:42,159 - INFO - 第 1 页获取到 50 条记录
2025-07-23 10:30:42,675 - INFO - Request Parameters - Page 2:
2025-07-23 10:30:42,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 10:30:42,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 10:30:50,784 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 957220CD-2602-7B03-AE52-BCE1825EDCBD Response: {'code': 'ServiceUnavailable', 'requestid': '957220CD-2602-7B03-AE52-BCE1825EDCBD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 957220CD-2602-7B03-AE52-BCE1825EDCBD)
2025-07-23 10:30:50,784 - INFO - 开始处理日期: 2025-07-23
2025-07-23 10:30:50,784 - INFO - Request Parameters - Page 1:
2025-07-23 10:30:50,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 10:30:50,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 10:30:51,316 - INFO - Response - Page 1:
2025-07-23 10:30:51,316 - INFO - 查询完成，共获取到 0 条记录
2025-07-23 10:30:51,316 - INFO - 获取到 0 条表单数据
2025-07-23 10:30:51,316 - INFO - 当前日期 2025-07-23 有 3 条MySQL数据需要处理
2025-07-23 10:30:51,316 - INFO - 开始批量插入 3 条新记录
2025-07-23 10:30:51,472 - INFO - 批量插入响应状态码: 200
2025-07-23 10:30:51,472 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6C3B9F56-8F41-7EC4-8821-9A2B8D2A41AE', 'x-acs-trace-id': '6f529015754aaa67a9814ebc41a2aa4e', 'etag': '1DY+KMKxB9+P1txEW2ogltA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:30:51,472 - INFO - 批量插入响应体: {'result': ['FINST-SL966GD14MDX75OH92V0P501XC603S1SJCFDMO5', 'FINST-SL966GD14MDX75OH92V0P501XC603S1SJCFDMP5', 'FINST-SL966GD14MDX75OH92V0P501XC603S1SJCFDMQ5']}
2025-07-23 10:30:51,472 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-23 10:30:51,472 - INFO - 成功插入的数据ID: ['FINST-SL966GD14MDX75OH92V0P501XC603S1SJCFDMO5', 'FINST-SL966GD14MDX75OH92V0P501XC603S1SJCFDMP5', 'FINST-SL966GD14MDX75OH92V0P501XC603S1SJCFDMQ5']
2025-07-23 10:30:56,487 - INFO - 批量插入完成，共 3 条记录
2025-07-23 10:30:56,487 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-23 10:30:56,487 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 1 条
2025-07-23 10:31:56,503 - INFO - 开始同步昨天与今天的销售数据: 2025-07-22 至 2025-07-23
2025-07-23 10:31:56,503 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-23 10:31:56,503 - INFO - 查询参数: ('2025-07-22', '2025-07-23')
2025-07-23 10:31:56,659 - INFO - MySQL查询成功，时间段: 2025-07-22 至 2025-07-23，共获取 502 条记录
2025-07-23 10:31:56,659 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 10:31:56,674 - INFO - 开始处理日期: 2025-07-22
2025-07-23 10:31:56,674 - INFO - Request Parameters - Page 1:
2025-07-23 10:31:56,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 10:31:56,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 10:31:57,456 - INFO - Response - Page 1:
2025-07-23 10:31:57,456 - INFO - 第 1 页获取到 50 条记录
2025-07-23 10:31:57,956 - INFO - Request Parameters - Page 2:
2025-07-23 10:31:57,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 10:31:57,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 10:31:58,706 - INFO - Response - Page 2:
2025-07-23 10:31:58,706 - INFO - 第 2 页获取到 45 条记录
2025-07-23 10:31:59,221 - INFO - 查询完成，共获取到 95 条记录
2025-07-23 10:31:59,221 - INFO - 获取到 95 条表单数据
2025-07-23 10:31:59,221 - INFO - 当前日期 2025-07-22 有 488 条MySQL数据需要处理
2025-07-23 10:31:59,221 - INFO - 开始批量插入 393 条新记录
2025-07-23 10:31:59,487 - INFO - 批量插入响应状态码: 200
2025-07-23 10:31:59,487 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:31:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3E00AEA4-1450-7EAB-A34B-142861492375', 'x-acs-trace-id': '0dcd8aa3c535dc90d069f2165c10e51e', 'etag': '2Wpou1m+93L2ZSVus3t99ng2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:31:59,487 - INFO - 批量插入响应体: {'result': ['FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMED', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMFD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMGD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMHD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMID', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMJD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMKD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMLD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMMD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMND', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMOD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMPD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMQD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMRD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMSD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMTD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMUD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMVD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMWD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMXD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMYD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMZD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM0E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM1E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM2E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM3E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM4E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM5E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM6E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM7E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM8E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM9E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMAE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMBE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMCE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMDE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMEE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMFE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMGE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMHE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMIE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMJE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMKE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMLE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMME', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMNE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMOE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMPE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMQE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMRE']}
2025-07-23 10:31:59,487 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-23 10:31:59,487 - INFO - 成功插入的数据ID: ['FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMED', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMFD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMGD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMHD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMID', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMJD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMKD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMLD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMMD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMND', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMOD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMPD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMQD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMRD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMSD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMTD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMUD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMVD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMWD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMXD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMYD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMZD', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM0E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM1E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM2E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM3E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM4E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM5E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM6E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM7E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM8E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDM9E', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMAE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMBE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMCE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMDE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMEE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMFE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMGE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMHE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMIE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMJE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMKE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMLE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMME', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMNE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMOE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMPE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMQE', 'FINST-FIG66R8193CXM5YSDWI8FDSA1E5330J8LCFDMRE']
2025-07-23 10:32:04,753 - INFO - 批量插入响应状态码: 200
2025-07-23 10:32:04,753 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2411', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E05DBA12-F2A7-780D-9218-531D1D797FE5', 'x-acs-trace-id': '27ee9c81207d550c861ea8b11c0192bd', 'etag': '2N/uhvl39HB8qbGwBGf4nuA1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:32:04,753 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMZ', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM01', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM11', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM21', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM31', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM41', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM51', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM61', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM71', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM81', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM91', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMA1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMB1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMC1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMD1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDME1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMF1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMG1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMH1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMI1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMJ1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMK1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDML1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMM1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMN1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMO1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMP1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMQ1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMR1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMS1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMT1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMU1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMV1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMW1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMX1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMY1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMZ1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM02', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM12', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM22', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM32', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM42', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM52', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM62', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM72', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM82', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM92', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMA2', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMB2', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMC2']}
2025-07-23 10:32:04,753 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-23 10:32:04,753 - INFO - 成功插入的数据ID: ['FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMZ', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM01', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM11', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM21', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM31', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM41', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM51', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM61', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM71', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM81', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDM91', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMA1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMB1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMC1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V26LCLCFDMD1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDME1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMF1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMG1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMH1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMI1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMJ1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMK1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDML1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMM1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMN1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMO1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMP1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMQ1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMR1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMS1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMT1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMU1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMV1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMW1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMX1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMY1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMZ1', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM02', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM12', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM22', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM32', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM42', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM52', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM62', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM72', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM82', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDM92', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMA2', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMB2', 'FINST-K7666JC1X3EXO5TBCAV3A68HC08V27LCLCFDMC2']
2025-07-23 10:32:09,987 - INFO - 批量插入响应状态码: 200
2025-07-23 10:32:09,987 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:32:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '09784D12-67AF-76EC-8D72-08CBE3AF75A2', 'x-acs-trace-id': '6f426a71f24118a73b07f3f9faacb709', 'etag': '2CtUEdUWM/jJll1b/PsFOjQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:32:09,987 - INFO - 批量插入响应体: {'result': ['FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMAG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMBG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMCG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMDG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMEG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMFG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMGG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMHG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMIG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMJG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMKG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMLG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMMG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMNG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMOG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMPG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMQG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMRG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMSG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMTG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMUG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMVG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMWG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMXG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMYG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMZG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM0H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM1H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM2H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM3H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM4H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM5H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM6H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM7H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM8H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM9H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMAH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMBH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMCH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMDH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMEH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMFH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMGH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMHH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMIH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMJH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMKH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMLH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMMH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMNH']}
2025-07-23 10:32:09,987 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-23 10:32:09,987 - INFO - 成功插入的数据ID: ['FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMAG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMBG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMCG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMDG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMEG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMFG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMGG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMHG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMIG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3QMGLCFDMJG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMKG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMLG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMMG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMNG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMOG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMPG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMQG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMRG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMSG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMTG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMUG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMVG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMWG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMXG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMYG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMZG', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM0H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM1H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM2H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM3H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM4H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM5H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM6H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM7H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM8H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDM9H', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMAH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMBH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMCH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMDH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMEH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMFH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMGH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMHH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMIH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMJH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMKH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMLH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMMH', 'FINST-ING66VA14MDXLQRSCJREO6OG3KWA3RMGLCFDMNH']
2025-07-23 10:32:15,252 - INFO - 批量插入响应状态码: 200
2025-07-23 10:32:15,252 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:32:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2379', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B9753316-9F66-7925-B7E7-326487B2A2A4', 'x-acs-trace-id': 'a6253975b71f2346a58b311a3afa4e82', 'etag': '2y64VG7pcCeyWVJ7Ki73ZPA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:32:15,252 - INFO - 批量插入响应体: {'result': ['FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM3', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM4', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM5', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM6', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM7', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM8', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM9', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMA', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMB', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMC', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMD', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDME', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMF', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMG', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMH', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMI', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMJ', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMK', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDML', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMM', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMN', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMO', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMP', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMQ', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMR', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMS', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMT', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMU', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMV', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMW', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMX', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMY', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMZ', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM01', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM11', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM21', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM31', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM41', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM51', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM61', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM71', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM81', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM91', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMA1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMB1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMC1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMD1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDME1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMF1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMG1']}
2025-07-23 10:32:15,252 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-23 10:32:15,252 - INFO - 成功插入的数据ID: ['FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM3', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM4', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM5', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM6', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM7', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM8', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM9', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMA', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMB', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMC', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMD', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDME', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMF', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMG', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMH', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMI', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMJ', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMK', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDML', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMM', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMN', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMO', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMP', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMQ', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMR', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMS', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMT', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMU', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMV', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMW', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMX', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMY', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMZ', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM01', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM11', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM21', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM31', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM41', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM51', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM61', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM71', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM81', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDM91', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMA1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMB1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMC1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMD1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDME1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMF1', 'FINST-DUF6609126EXXO74FFMET74GBQU72UOKLCFDMG1']
2025-07-23 10:32:20,487 - INFO - 批量插入响应状态码: 200
2025-07-23 10:32:20,487 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2394', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '100154EE-2690-7B68-8F37-A7BAB21B01E2', 'x-acs-trace-id': '44289f17d3e719d8ac6dc735db52b267', 'etag': '2EozVF6tkfBLreV0gvDGk8Q4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:32:20,487 - INFO - 批量插入响应体: {'result': ['FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMI', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMJ', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMK', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDML', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMM', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMN', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMO', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMP', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMQ', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMR', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMS', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMT', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMU', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMV', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMW', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMX', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMY', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMZ', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM01', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM11', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM21', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM31', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM41', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM51', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM61', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM71', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM81', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM91', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMA1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMB1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMC1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMD1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDME1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMF1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMG1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMH1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMI1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMJ1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMK1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDML1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMM1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMN1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMO1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMP1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMQ1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMR1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMS1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMT1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMU1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMV1']}
2025-07-23 10:32:20,487 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-23 10:32:20,487 - INFO - 成功插入的数据ID: ['FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMI', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMJ', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMK', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDML', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMM', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMN', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMO', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMP', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMQ', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMR', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMS', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMT', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMU', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMV', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMW', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMX', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMY', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMZ', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM01', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM11', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM21', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM31', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM41', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM51', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM61', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM71', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM81', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDM91', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMA1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMB1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMC1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMD1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDME1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMF1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMG1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMH1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMI1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMJ1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMK1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDML1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMM1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMN1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMO1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMP1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMQ1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMR1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMS1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMT1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMU1', 'FINST-F7D66UA1Q4EXN6XNFXSHQD4HSLWO3IQOLCFDMV1']
2025-07-23 10:32:25,768 - INFO - 批量插入响应状态码: 200
2025-07-23 10:32:25,768 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:32:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7ADC995F-9F85-772F-92DC-B9FCB7262905', 'x-acs-trace-id': '271d153e770bc443de3c5064aa4a06f9', 'etag': '2xIKCta2faYPX4nnuxJwfeg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:32:25,768 - INFO - 批量插入响应体: {'result': ['FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMU9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMV9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMW9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMX9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMY9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMZ9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM0A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM1A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM2A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM3A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM4A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM5A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM6A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM7A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM8A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM9A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMAA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMBA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMCA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMDA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMEA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMFA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMGA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMHA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMIA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMJA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMKA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMLA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMMA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMNA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMOA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMPA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMQA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMRA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMSA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMTA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMUA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMVA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMWA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMXA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMYA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMZA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM0B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM1B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM2B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM3B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM4B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM5B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM6B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM7B']}
2025-07-23 10:32:25,768 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-07-23 10:32:25,768 - INFO - 成功插入的数据ID: ['FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMU9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMV9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMW9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMX9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMY9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMZ9', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM0A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM1A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM2A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM3A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM4A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM5A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM6A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM7A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM8A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDM9A', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMAA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMBA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMCA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMDA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMEA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMFA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMGA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMHA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMIA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMJA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMKA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMLA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMMA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMNA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMOA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMPA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMQA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMRA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE720TSLCFDMSA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMTA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMUA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMVA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMWA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMXA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMYA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDMZA', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM0B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM1B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM2B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM3B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM4B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM5B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM6B', 'FINST-IOC66G71P5DXGHIM6I2FYBGGYOE721TSLCFDM7B']
2025-07-23 10:32:31,018 - INFO - 批量插入响应状态码: 200
2025-07-23 10:32:31,018 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:32:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2401', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B5B19AB6-F6DB-78AE-A9FB-1F0E2CE2238E', 'x-acs-trace-id': 'ae2575ff5f84cf36f89143434d9ca599', 'etag': '2HSoAOkv0OUjWK/8AkaYEag1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:32:31,018 - INFO - 批量插入响应体: {'result': ['FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMP', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMQ', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMR', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMS', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMT', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMU', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMV', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMW', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMX', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMY', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMZ', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM01', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM11', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM21', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM31', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM41', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM51', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM61', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM71', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM81', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM91', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMA1', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMB1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMC1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMD1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDME1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMF1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMG1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMH1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMI1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMJ1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMK1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDML1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMM1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMN1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMO1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMP1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMQ1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMR1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMS1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMT1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMU1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMV1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMW1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMX1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMY1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMZ1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDM02', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDM12', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDM22']}
2025-07-23 10:32:31,018 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-07-23 10:32:31,018 - INFO - 成功插入的数据ID: ['FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMP', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMQ', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMR', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMS', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMT', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMU', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMV', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMW', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMX', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMY', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMZ', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM01', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM11', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM21', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM31', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM41', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM51', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM61', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM71', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM81', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM91', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMA1', 'FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDMB1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMC1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMD1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDME1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMF1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMG1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMH1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMI1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMJ1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMK1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDML1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMM1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMN1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMO1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMP1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMQ1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMR1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMS1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMT1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMU1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMV1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMW1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMX1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMY1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDMZ1', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDM02', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDM12', 'FINST-07E66I9105EXVQIX66S888CI0HIS33VWLCFDM22']
2025-07-23 10:32:36,284 - INFO - 批量插入响应状态码: 200
2025-07-23 10:32:36,284 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 02:32:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2076', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '351F2583-264E-76FE-AF7B-603C22E549C4', 'x-acs-trace-id': 'bfa3a24d0b8d4a028fe013b51b6582ad', 'etag': '2D+QXEFX4WNpQ49yreukicA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 10:32:36,284 - INFO - 批量插入响应体: {'result': ['FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMET', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMFT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMGT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMHT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMIT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMJT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMKT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMLT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMMT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMNT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMOT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMPT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMQT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMRT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMST', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMTT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMUT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMVT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMWT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMXT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMYT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMZT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM0U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM1U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM2U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM3U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM4U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM5U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM6U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM7U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM8U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM9U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMAU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMBU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMCU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMDU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMEU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMFU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMGU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMHU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMIU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMJU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMKU']}
2025-07-23 10:32:36,284 - INFO - 批量插入表单数据成功，批次 8，共 43 条记录
2025-07-23 10:32:36,299 - INFO - 成功插入的数据ID: ['FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMET', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMFT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMGT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMHT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMIT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMJT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMKT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMLT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMMT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMNT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMOT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMPT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMQT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMRT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMST', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMTT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMUT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMVT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMWT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMXT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMYT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3GX0MCFDMZT', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM0U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM1U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM2U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM3U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM4U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM5U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM6U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM7U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM8U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDM9U', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMAU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMBU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMCU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMDU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMEU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMFU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMGU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMHU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMIU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMJU', 'FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMKU']
2025-07-23 10:32:41,315 - INFO - 批量插入完成，共 393 条记录
2025-07-23 10:32:41,315 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 393 条，错误: 0 条
2025-07-23 10:32:41,315 - INFO - 开始处理日期: 2025-07-23
2025-07-23 10:32:41,315 - INFO - Request Parameters - Page 1:
2025-07-23 10:32:41,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 10:32:41,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 10:32:41,893 - INFO - Response - Page 1:
2025-07-23 10:32:41,893 - INFO - 第 1 页获取到 3 条记录
2025-07-23 10:32:42,409 - INFO - 查询完成，共获取到 3 条记录
2025-07-23 10:32:42,409 - INFO - 获取到 3 条表单数据
2025-07-23 10:32:42,409 - INFO - 当前日期 2025-07-23 有 3 条MySQL数据需要处理
2025-07-23 10:32:42,409 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-23 10:32:42,409 - INFO - 数据同步完成！更新: 0 条，插入: 393 条，错误: 0 条
2025-07-23 10:32:42,409 - INFO - 同步完成
2025-07-23 13:30:33,853 - INFO - 使用默认增量同步（当天更新数据）
2025-07-23 13:30:33,853 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-23 13:30:33,853 - INFO - 查询参数: ('2025-07-23',)
2025-07-23 13:30:34,009 - INFO - MySQL查询成功，增量数据（日期: 2025-07-23），共获取 153 条记录
2025-07-23 13:30:34,009 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 13:30:34,009 - INFO - 开始处理日期: 2025-07-22
2025-07-23 13:30:34,009 - INFO - Request Parameters - Page 1:
2025-07-23 13:30:34,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:30:34,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:30:42,134 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EF0CDF50-0FD6-7E65-B63F-C282D198DE04 Response: {'code': 'ServiceUnavailable', 'requestid': 'EF0CDF50-0FD6-7E65-B63F-C282D198DE04', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EF0CDF50-0FD6-7E65-B63F-C282D198DE04)
2025-07-23 13:30:42,134 - INFO - 开始处理日期: 2025-07-23
2025-07-23 13:30:42,134 - INFO - Request Parameters - Page 1:
2025-07-23 13:30:42,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:30:42,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:30:46,774 - INFO - Response - Page 1:
2025-07-23 13:30:46,774 - INFO - 第 1 页获取到 3 条记录
2025-07-23 13:30:47,290 - INFO - 查询完成，共获取到 3 条记录
2025-07-23 13:30:47,290 - INFO - 获取到 3 条表单数据
2025-07-23 13:30:47,290 - INFO - 当前日期 2025-07-23 有 3 条MySQL数据需要处理
2025-07-23 13:30:47,290 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-23 13:30:47,290 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-23 13:31:47,305 - INFO - 开始同步昨天与今天的销售数据: 2025-07-22 至 2025-07-23
2025-07-23 13:31:47,305 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-23 13:31:47,305 - INFO - 查询参数: ('2025-07-22', '2025-07-23')
2025-07-23 13:31:47,462 - INFO - MySQL查询成功，时间段: 2025-07-22 至 2025-07-23，共获取 522 条记录
2025-07-23 13:31:47,462 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 13:31:47,477 - INFO - 开始处理日期: 2025-07-22
2025-07-23 13:31:47,477 - INFO - Request Parameters - Page 1:
2025-07-23 13:31:47,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:31:47,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:31:55,508 - INFO - Response - Page 1:
2025-07-23 13:31:55,508 - INFO - 第 1 页获取到 50 条记录
2025-07-23 13:31:56,024 - INFO - Request Parameters - Page 2:
2025-07-23 13:31:56,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:31:56,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:31:56,665 - INFO - Response - Page 2:
2025-07-23 13:31:56,665 - INFO - 第 2 页获取到 50 条记录
2025-07-23 13:31:57,180 - INFO - Request Parameters - Page 3:
2025-07-23 13:31:57,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:31:57,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:31:57,821 - INFO - Response - Page 3:
2025-07-23 13:31:57,821 - INFO - 第 3 页获取到 50 条记录
2025-07-23 13:31:58,321 - INFO - Request Parameters - Page 4:
2025-07-23 13:31:58,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:31:58,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:31:59,008 - INFO - Response - Page 4:
2025-07-23 13:31:59,008 - INFO - 第 4 页获取到 50 条记录
2025-07-23 13:31:59,524 - INFO - Request Parameters - Page 5:
2025-07-23 13:31:59,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:31:59,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:32:00,196 - INFO - Response - Page 5:
2025-07-23 13:32:00,196 - INFO - 第 5 页获取到 50 条记录
2025-07-23 13:32:00,711 - INFO - Request Parameters - Page 6:
2025-07-23 13:32:00,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:32:00,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:32:01,446 - INFO - Response - Page 6:
2025-07-23 13:32:01,446 - INFO - 第 6 页获取到 50 条记录
2025-07-23 13:32:01,961 - INFO - Request Parameters - Page 7:
2025-07-23 13:32:01,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:32:01,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:32:02,680 - INFO - Response - Page 7:
2025-07-23 13:32:02,680 - INFO - 第 7 页获取到 50 条记录
2025-07-23 13:32:03,196 - INFO - Request Parameters - Page 8:
2025-07-23 13:32:03,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:32:03,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:32:03,993 - INFO - Response - Page 8:
2025-07-23 13:32:03,993 - INFO - 第 8 页获取到 50 条记录
2025-07-23 13:32:04,493 - INFO - Request Parameters - Page 9:
2025-07-23 13:32:04,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:32:04,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:32:05,165 - INFO - Response - Page 9:
2025-07-23 13:32:05,165 - INFO - 第 9 页获取到 50 条记录
2025-07-23 13:32:05,680 - INFO - Request Parameters - Page 10:
2025-07-23 13:32:05,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:32:05,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:32:06,336 - INFO - Response - Page 10:
2025-07-23 13:32:06,336 - INFO - 第 10 页获取到 38 条记录
2025-07-23 13:32:06,852 - INFO - 查询完成，共获取到 488 条记录
2025-07-23 13:32:06,852 - INFO - 获取到 488 条表单数据
2025-07-23 13:32:06,852 - INFO - 当前日期 2025-07-22 有 505 条MySQL数据需要处理
2025-07-23 13:32:06,868 - INFO - 开始批量插入 17 条新记录
2025-07-23 13:32:07,040 - INFO - 批量插入响应状态码: 200
2025-07-23 13:32:07,040 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 05:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '828', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D3DB62A7-153E-7DF9-9770-9A6492AE5FAB', 'x-acs-trace-id': '37104d3c22596eb9c5973b33f496b091', 'etag': '8M2lEr1lr346L07Kb7djo2Q8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 13:32:07,040 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMH3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMI3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMJ3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMK3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDML3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMM3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMN3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMO3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMP3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMQ3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMR3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMS3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMT3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMU3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMV3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMW3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMX3']}
2025-07-23 13:32:07,040 - INFO - 批量插入表单数据成功，批次 1，共 17 条记录
2025-07-23 13:32:07,040 - INFO - 成功插入的数据ID: ['FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMH3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMI3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMJ3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMK3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDML3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMM3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMN3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMO3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMP3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMQ3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMR3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMS3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMT3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMU3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMV3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMW3', 'FINST-XL866HB1Y5DXW17X51UK1APIPC1C2ZQV0JFDMX3']
2025-07-23 13:32:12,055 - INFO - 批量插入完成，共 17 条记录
2025-07-23 13:32:12,055 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 17 条，错误: 0 条
2025-07-23 13:32:12,055 - INFO - 开始处理日期: 2025-07-23
2025-07-23 13:32:12,055 - INFO - Request Parameters - Page 1:
2025-07-23 13:32:12,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 13:32:12,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 13:32:12,539 - INFO - Response - Page 1:
2025-07-23 13:32:12,539 - INFO - 第 1 页获取到 3 条记录
2025-07-23 13:32:13,055 - INFO - 查询完成，共获取到 3 条记录
2025-07-23 13:32:13,055 - INFO - 获取到 3 条表单数据
2025-07-23 13:32:13,055 - INFO - 当前日期 2025-07-23 有 3 条MySQL数据需要处理
2025-07-23 13:32:13,055 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-23 13:32:13,055 - INFO - 数据同步完成！更新: 0 条，插入: 17 条，错误: 0 条
2025-07-23 13:32:13,055 - INFO - 同步完成
2025-07-23 16:30:33,511 - INFO - 使用默认增量同步（当天更新数据）
2025-07-23 16:30:33,511 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-23 16:30:33,511 - INFO - 查询参数: ('2025-07-23',)
2025-07-23 16:30:33,667 - INFO - MySQL查询成功，增量数据（日期: 2025-07-23），共获取 160 条记录
2025-07-23 16:30:33,667 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 16:30:33,667 - INFO - 开始处理日期: 2025-07-22
2025-07-23 16:30:33,667 - INFO - Request Parameters - Page 1:
2025-07-23 16:30:33,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:30:33,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:30:41,792 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 31011A8C-4AA8-77C2-9B11-B5B638140B0E Response: {'code': 'ServiceUnavailable', 'requestid': '31011A8C-4AA8-77C2-9B11-B5B638140B0E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 31011A8C-4AA8-77C2-9B11-B5B638140B0E)
2025-07-23 16:30:41,807 - INFO - 开始处理日期: 2025-07-23
2025-07-23 16:30:41,807 - INFO - Request Parameters - Page 1:
2025-07-23 16:30:41,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:30:41,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:30:49,917 - ERROR - 处理日期 2025-07-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 791F025B-008E-7849-8F7C-F3C4AC03F948 Response: {'code': 'ServiceUnavailable', 'requestid': '791F025B-008E-7849-8F7C-F3C4AC03F948', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 791F025B-008E-7849-8F7C-F3C4AC03F948)
2025-07-23 16:30:49,917 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-23 16:31:49,932 - INFO - 开始同步昨天与今天的销售数据: 2025-07-22 至 2025-07-23
2025-07-23 16:31:49,932 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-23 16:31:49,932 - INFO - 查询参数: ('2025-07-22', '2025-07-23')
2025-07-23 16:31:50,104 - INFO - MySQL查询成功，时间段: 2025-07-22 至 2025-07-23，共获取 532 条记录
2025-07-23 16:31:50,104 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 16:31:50,104 - INFO - 开始处理日期: 2025-07-22
2025-07-23 16:31:50,104 - INFO - Request Parameters - Page 1:
2025-07-23 16:31:50,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:31:50,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:31:50,838 - INFO - Response - Page 1:
2025-07-23 16:31:50,838 - INFO - 第 1 页获取到 50 条记录
2025-07-23 16:31:51,338 - INFO - Request Parameters - Page 2:
2025-07-23 16:31:51,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:31:51,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:31:58,807 - INFO - Response - Page 2:
2025-07-23 16:31:58,807 - INFO - 第 2 页获取到 50 条记录
2025-07-23 16:31:59,307 - INFO - Request Parameters - Page 3:
2025-07-23 16:31:59,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:31:59,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:00,041 - INFO - Response - Page 3:
2025-07-23 16:32:00,041 - INFO - 第 3 页获取到 50 条记录
2025-07-23 16:32:00,557 - INFO - Request Parameters - Page 4:
2025-07-23 16:32:00,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:00,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:01,276 - INFO - Response - Page 4:
2025-07-23 16:32:01,276 - INFO - 第 4 页获取到 50 条记录
2025-07-23 16:32:01,791 - INFO - Request Parameters - Page 5:
2025-07-23 16:32:01,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:01,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:02,494 - INFO - Response - Page 5:
2025-07-23 16:32:02,494 - INFO - 第 5 页获取到 50 条记录
2025-07-23 16:32:03,010 - INFO - Request Parameters - Page 6:
2025-07-23 16:32:03,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:03,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:03,776 - INFO - Response - Page 6:
2025-07-23 16:32:03,776 - INFO - 第 6 页获取到 50 条记录
2025-07-23 16:32:04,291 - INFO - Request Parameters - Page 7:
2025-07-23 16:32:04,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:04,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:04,963 - INFO - Response - Page 7:
2025-07-23 16:32:04,963 - INFO - 第 7 页获取到 50 条记录
2025-07-23 16:32:05,479 - INFO - Request Parameters - Page 8:
2025-07-23 16:32:05,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:05,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:06,229 - INFO - Response - Page 8:
2025-07-23 16:32:06,229 - INFO - 第 8 页获取到 50 条记录
2025-07-23 16:32:06,744 - INFO - Request Parameters - Page 9:
2025-07-23 16:32:06,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:06,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:07,479 - INFO - Response - Page 9:
2025-07-23 16:32:07,479 - INFO - 第 9 页获取到 50 条记录
2025-07-23 16:32:07,979 - INFO - Request Parameters - Page 10:
2025-07-23 16:32:07,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:07,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:08,713 - INFO - Response - Page 10:
2025-07-23 16:32:08,713 - INFO - 第 10 页获取到 50 条记录
2025-07-23 16:32:09,229 - INFO - Request Parameters - Page 11:
2025-07-23 16:32:09,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:09,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:09,776 - INFO - Response - Page 11:
2025-07-23 16:32:09,776 - INFO - 第 11 页获取到 5 条记录
2025-07-23 16:32:10,291 - INFO - 查询完成，共获取到 505 条记录
2025-07-23 16:32:10,291 - INFO - 获取到 505 条表单数据
2025-07-23 16:32:10,291 - INFO - 当前日期 2025-07-22 有 514 条MySQL数据需要处理
2025-07-23 16:32:10,307 - INFO - 开始批量插入 9 条新记录
2025-07-23 16:32:10,463 - INFO - 批量插入响应状态码: 200
2025-07-23 16:32:10,463 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 08:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A766C697-3C32-751F-A037-F7F98A8B78AB', 'x-acs-trace-id': 'f365e911aa14fe0059af478d261a1ae5', 'etag': '4TWr+mlr1u6GOXbT0LUXjrw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 16:32:10,463 - INFO - 批量插入响应体: {'result': ['FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMCK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMDK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMEK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMFK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMGK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMHK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMIK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMJK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMKK']}
2025-07-23 16:32:10,463 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-07-23 16:32:10,463 - INFO - 成功插入的数据ID: ['FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMCK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMDK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMEK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMFK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMGK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMHK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMIK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMJK', 'FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMKK']
2025-07-23 16:32:15,479 - INFO - 批量插入完成，共 9 条记录
2025-07-23 16:32:15,479 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-07-23 16:32:15,479 - INFO - 开始处理日期: 2025-07-23
2025-07-23 16:32:15,479 - INFO - Request Parameters - Page 1:
2025-07-23 16:32:15,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 16:32:15,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 16:32:15,932 - INFO - Response - Page 1:
2025-07-23 16:32:15,932 - INFO - 第 1 页获取到 3 条记录
2025-07-23 16:32:16,432 - INFO - 查询完成，共获取到 3 条记录
2025-07-23 16:32:16,432 - INFO - 获取到 3 条表单数据
2025-07-23 16:32:16,432 - INFO - 当前日期 2025-07-23 有 4 条MySQL数据需要处理
2025-07-23 16:32:16,432 - INFO - 开始批量插入 1 条新记录
2025-07-23 16:32:16,588 - INFO - 批量插入响应状态码: 200
2025-07-23 16:32:16,588 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 08:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A5BD17DB-D5E9-7A76-9DBB-C0D74433AE96', 'x-acs-trace-id': '02e51d29e8a641b5d260cafe56e50377', 'etag': '5IZdwNbewXVPq0gD7zhma7g9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 16:32:16,588 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81RDEXBGARFKS5LDDBLUA42AIKGPFDMS']}
2025-07-23 16:32:16,588 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-23 16:32:16,588 - INFO - 成功插入的数据ID: ['FINST-WBF66B81RDEXBGARFKS5LDDBLUA42AIKGPFDMS']
2025-07-23 16:32:21,604 - INFO - 批量插入完成，共 1 条记录
2025-07-23 16:32:21,604 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-23 16:32:21,604 - INFO - 数据同步完成！更新: 0 条，插入: 10 条，错误: 0 条
2025-07-23 16:32:21,604 - INFO - 同步完成
2025-07-23 19:30:34,580 - INFO - 使用默认增量同步（当天更新数据）
2025-07-23 19:30:34,580 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-23 19:30:34,580 - INFO - 查询参数: ('2025-07-23',)
2025-07-23 19:30:34,737 - INFO - MySQL查询成功，增量数据（日期: 2025-07-23），共获取 161 条记录
2025-07-23 19:30:34,737 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 19:30:34,737 - INFO - 开始处理日期: 2025-07-22
2025-07-23 19:30:34,737 - INFO - Request Parameters - Page 1:
2025-07-23 19:30:34,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:30:34,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:30:42,880 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 64890D82-A8DB-77C3-92DC-E1D85C5C0527 Response: {'code': 'ServiceUnavailable', 'requestid': '64890D82-A8DB-77C3-92DC-E1D85C5C0527', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 64890D82-A8DB-77C3-92DC-E1D85C5C0527)
2025-07-23 19:30:42,880 - INFO - 开始处理日期: 2025-07-23
2025-07-23 19:30:42,880 - INFO - Request Parameters - Page 1:
2025-07-23 19:30:42,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:30:42,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:30:50,243 - INFO - Response - Page 1:
2025-07-23 19:30:50,243 - INFO - 第 1 页获取到 4 条记录
2025-07-23 19:30:50,743 - INFO - 查询完成，共获取到 4 条记录
2025-07-23 19:30:50,743 - INFO - 获取到 4 条表单数据
2025-07-23 19:30:50,743 - INFO - 当前日期 2025-07-23 有 4 条MySQL数据需要处理
2025-07-23 19:30:50,743 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-23 19:30:50,743 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-23 19:31:50,782 - INFO - 开始同步昨天与今天的销售数据: 2025-07-22 至 2025-07-23
2025-07-23 19:31:50,782 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-23 19:31:50,782 - INFO - 查询参数: ('2025-07-22', '2025-07-23')
2025-07-23 19:31:50,939 - INFO - MySQL查询成功，时间段: 2025-07-22 至 2025-07-23，共获取 533 条记录
2025-07-23 19:31:50,939 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 19:31:50,954 - INFO - 开始处理日期: 2025-07-22
2025-07-23 19:31:50,954 - INFO - Request Parameters - Page 1:
2025-07-23 19:31:50,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:31:50,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:31:51,658 - INFO - Response - Page 1:
2025-07-23 19:31:51,658 - INFO - 第 1 页获取到 50 条记录
2025-07-23 19:31:52,173 - INFO - Request Parameters - Page 2:
2025-07-23 19:31:52,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:31:52,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:31:52,877 - INFO - Response - Page 2:
2025-07-23 19:31:52,877 - INFO - 第 2 页获取到 50 条记录
2025-07-23 19:31:53,393 - INFO - Request Parameters - Page 3:
2025-07-23 19:31:53,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:31:53,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:31:54,096 - INFO - Response - Page 3:
2025-07-23 19:31:54,096 - INFO - 第 3 页获取到 50 条记录
2025-07-23 19:31:54,596 - INFO - Request Parameters - Page 4:
2025-07-23 19:31:54,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:31:54,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:31:55,315 - INFO - Response - Page 4:
2025-07-23 19:31:55,315 - INFO - 第 4 页获取到 50 条记录
2025-07-23 19:31:55,831 - INFO - Request Parameters - Page 5:
2025-07-23 19:31:55,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:31:55,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:31:56,535 - INFO - Response - Page 5:
2025-07-23 19:31:56,535 - INFO - 第 5 页获取到 50 条记录
2025-07-23 19:31:57,035 - INFO - Request Parameters - Page 6:
2025-07-23 19:31:57,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:31:57,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:31:57,738 - INFO - Response - Page 6:
2025-07-23 19:31:57,738 - INFO - 第 6 页获取到 50 条记录
2025-07-23 19:31:58,254 - INFO - Request Parameters - Page 7:
2025-07-23 19:31:58,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:31:58,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:31:58,989 - INFO - Response - Page 7:
2025-07-23 19:31:58,989 - INFO - 第 7 页获取到 50 条记录
2025-07-23 19:31:59,489 - INFO - Request Parameters - Page 8:
2025-07-23 19:31:59,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:31:59,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:32:00,177 - INFO - Response - Page 8:
2025-07-23 19:32:00,177 - INFO - 第 8 页获取到 50 条记录
2025-07-23 19:32:00,692 - INFO - Request Parameters - Page 9:
2025-07-23 19:32:00,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:32:00,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:32:01,458 - INFO - Response - Page 9:
2025-07-23 19:32:01,458 - INFO - 第 9 页获取到 50 条记录
2025-07-23 19:32:01,974 - INFO - Request Parameters - Page 10:
2025-07-23 19:32:01,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:32:01,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:32:02,693 - INFO - Response - Page 10:
2025-07-23 19:32:02,693 - INFO - 第 10 页获取到 50 条记录
2025-07-23 19:32:03,193 - INFO - Request Parameters - Page 11:
2025-07-23 19:32:03,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:32:03,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:32:03,803 - INFO - Response - Page 11:
2025-07-23 19:32:03,803 - INFO - 第 11 页获取到 14 条记录
2025-07-23 19:32:04,319 - INFO - 查询完成，共获取到 514 条记录
2025-07-23 19:32:04,319 - INFO - 获取到 514 条表单数据
2025-07-23 19:32:04,319 - INFO - 当前日期 2025-07-22 有 515 条MySQL数据需要处理
2025-07-23 19:32:04,335 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMAU
2025-07-23 19:32:04,850 - INFO - 更新表单数据成功: FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMAU
2025-07-23 19:32:04,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6097.86}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6097.86}, {'field': 'order_count', 'old_value': 0, 'new_value': 24}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1b48102a3d6547c3893c9d9780e1a5b4.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=cXMc1lU3NTc%2BDL0pZ%2FvxfVG9Vxw%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/d4f775c897704defbc428d4d7e398a55.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=YupA4KbU68yAkuq0V3nrUWbuUFw%3D'}]
2025-07-23 19:32:04,850 - INFO - 开始批量插入 1 条新记录
2025-07-23 19:32:05,022 - INFO - 批量插入响应状态码: 200
2025-07-23 19:32:05,022 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 11:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FDDA8B5F-53F4-7E30-B297-A135A3D37B8B', 'x-acs-trace-id': 'd6c382299b67011212353ddcc37b97d0', 'etag': '6tnyY3IKrRsnyfy55eB/Hqw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 19:32:05,022 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D16IEXGUS96WGHX9RHJRUI2JOSVVFDME1']}
2025-07-23 19:32:05,022 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-23 19:32:05,022 - INFO - 成功插入的数据ID: ['FINST-QZE668D16IEXGUS96WGHX9RHJRUI2JOSVVFDME1']
2025-07-23 19:32:10,040 - INFO - 批量插入完成，共 1 条记录
2025-07-23 19:32:10,040 - INFO - 日期 2025-07-22 处理完成 - 更新: 1 条，插入: 1 条，错误: 0 条
2025-07-23 19:32:10,040 - INFO - 开始处理日期: 2025-07-23
2025-07-23 19:32:10,040 - INFO - Request Parameters - Page 1:
2025-07-23 19:32:10,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 19:32:10,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 19:32:10,556 - INFO - Response - Page 1:
2025-07-23 19:32:10,556 - INFO - 第 1 页获取到 4 条记录
2025-07-23 19:32:11,072 - INFO - 查询完成，共获取到 4 条记录
2025-07-23 19:32:11,072 - INFO - 获取到 4 条表单数据
2025-07-23 19:32:11,072 - INFO - 当前日期 2025-07-23 有 4 条MySQL数据需要处理
2025-07-23 19:32:11,072 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-23 19:32:11,072 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 0 条
2025-07-23 19:32:11,072 - INFO - 同步完成
2025-07-23 22:30:35,597 - INFO - 使用默认增量同步（当天更新数据）
2025-07-23 22:30:35,597 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-23 22:30:35,597 - INFO - 查询参数: ('2025-07-23',)
2025-07-23 22:30:35,768 - INFO - MySQL查询成功，增量数据（日期: 2025-07-23），共获取 227 条记录
2025-07-23 22:30:35,768 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 22:30:35,768 - INFO - 开始处理日期: 2025-07-22
2025-07-23 22:30:35,768 - INFO - Request Parameters - Page 1:
2025-07-23 22:30:35,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:30:35,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:30:43,115 - INFO - Response - Page 1:
2025-07-23 22:30:43,115 - INFO - 第 1 页获取到 50 条记录
2025-07-23 22:30:43,615 - INFO - Request Parameters - Page 2:
2025-07-23 22:30:43,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:30:43,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:30:51,728 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 41A7F9AD-C0B4-7C14-99A9-959A634E5630 Response: {'code': 'ServiceUnavailable', 'requestid': '41A7F9AD-C0B4-7C14-99A9-959A634E5630', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 41A7F9AD-C0B4-7C14-99A9-959A634E5630)
2025-07-23 22:30:51,728 - INFO - 开始处理日期: 2025-07-23
2025-07-23 22:30:51,728 - INFO - Request Parameters - Page 1:
2025-07-23 22:30:51,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:30:51,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:30:54,354 - INFO - Response - Page 1:
2025-07-23 22:30:54,354 - INFO - 第 1 页获取到 4 条记录
2025-07-23 22:30:54,854 - INFO - 查询完成，共获取到 4 条记录
2025-07-23 22:30:54,854 - INFO - 获取到 4 条表单数据
2025-07-23 22:30:54,854 - INFO - 当前日期 2025-07-23 有 68 条MySQL数据需要处理
2025-07-23 22:30:54,854 - INFO - 开始批量插入 64 条新记录
2025-07-23 22:30:55,104 - INFO - 批量插入响应状态码: 200
2025-07-23 22:30:55,104 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 14:30:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '96253219-F274-7908-B391-938CF899BBFB', 'x-acs-trace-id': '39dd1ff6638e6897c1e1b2367cc2a08a', 'etag': '2BjkFHImSa0bY/jtOcrvY7Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 22:30:55,104 - INFO - 批量插入响应体: {'result': ['FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDME1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMF1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMG1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMH1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMI1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMJ1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMK1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDML1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMM1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMN1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMO1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMP1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMQ1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMR1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMS1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMT1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMU1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMV1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMW1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMX1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMY1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMZ1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM02', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM12', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM22', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM32', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM42', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM52', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM62', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM72', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM82', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM92', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMA2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMB2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMC2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMD2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDME2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMF2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMG2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMH2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMI2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMJ2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMK2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDML2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMM2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMN2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMO2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMP2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMQ2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMR2']}
2025-07-23 22:30:55,104 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-23 22:30:55,104 - INFO - 成功插入的数据ID: ['FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDME1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMF1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMG1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMH1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMI1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMJ1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMK1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDML1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMM1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMN1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMO1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMP1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMQ1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMR1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMS1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMT1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMU1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMV1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMW1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMX1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMY1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMZ1', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM02', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM12', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM22', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM32', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM42', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM52', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM62', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM72', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM82', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDM92', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMA2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMB2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMC2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMD2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDME2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMF2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMG2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMH2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMI2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMJ2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMK2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDML2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMM2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMN2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMO2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMP2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMQ2', 'FINST-NU966I813IEX4XHNFMS4B4YNYDIS3FSO92GDMR2']
2025-07-23 22:31:00,294 - INFO - 批量插入响应状态码: 200
2025-07-23 22:31:00,294 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 14:30:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C618C11B-3687-733F-9435-CB9257124227', 'x-acs-trace-id': 'f60658b9726cc413f99c67bcb1c529a3', 'etag': '6eDWsd9PFjqqaxhjbvO6+OA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-23 22:31:00,294 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMT2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMU2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMV2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMW2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMX2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMY2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMZ2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM03', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM13', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM23', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM33', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM43', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM53', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM63']}
2025-07-23 22:31:00,294 - INFO - 批量插入表单数据成功，批次 2，共 14 条记录
2025-07-23 22:31:00,294 - INFO - 成功插入的数据ID: ['FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMT2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMU2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMV2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMW2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMX2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMY2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDMZ2', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM03', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM13', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM23', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM33', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM43', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM53', 'FINST-FQD66YB12IEXBF7ZEEBCDB5YWU0G2OSS92GDM63']
2025-07-23 22:31:05,311 - INFO - 批量插入完成，共 64 条记录
2025-07-23 22:31:05,311 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 64 条，错误: 0 条
2025-07-23 22:31:05,311 - INFO - 数据同步完成！更新: 0 条，插入: 64 条，错误: 1 条
2025-07-23 22:32:05,351 - INFO - 开始同步昨天与今天的销售数据: 2025-07-22 至 2025-07-23
2025-07-23 22:32:05,351 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-23 22:32:05,351 - INFO - 查询参数: ('2025-07-22', '2025-07-23')
2025-07-23 22:32:05,523 - INFO - MySQL查询成功，时间段: 2025-07-22 至 2025-07-23，共获取 599 条记录
2025-07-23 22:32:05,523 - INFO - 获取到 2 个日期需要处理: ['2025-07-22', '2025-07-23']
2025-07-23 22:32:05,523 - INFO - 开始处理日期: 2025-07-22
2025-07-23 22:32:05,523 - INFO - Request Parameters - Page 1:
2025-07-23 22:32:05,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:05,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:06,398 - INFO - Response - Page 1:
2025-07-23 22:32:06,398 - INFO - 第 1 页获取到 50 条记录
2025-07-23 22:32:06,914 - INFO - Request Parameters - Page 2:
2025-07-23 22:32:06,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:06,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:07,664 - INFO - Response - Page 2:
2025-07-23 22:32:07,664 - INFO - 第 2 页获取到 50 条记录
2025-07-23 22:32:08,180 - INFO - Request Parameters - Page 3:
2025-07-23 22:32:08,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:08,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:08,884 - INFO - Response - Page 3:
2025-07-23 22:32:08,884 - INFO - 第 3 页获取到 50 条记录
2025-07-23 22:32:09,399 - INFO - Request Parameters - Page 4:
2025-07-23 22:32:09,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:09,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:10,134 - INFO - Response - Page 4:
2025-07-23 22:32:10,134 - INFO - 第 4 页获取到 50 条记录
2025-07-23 22:32:10,650 - INFO - Request Parameters - Page 5:
2025-07-23 22:32:10,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:10,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:11,463 - INFO - Response - Page 5:
2025-07-23 22:32:11,463 - INFO - 第 5 页获取到 50 条记录
2025-07-23 22:32:11,979 - INFO - Request Parameters - Page 6:
2025-07-23 22:32:11,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:11,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:12,713 - INFO - Response - Page 6:
2025-07-23 22:32:12,713 - INFO - 第 6 页获取到 50 条记录
2025-07-23 22:32:13,229 - INFO - Request Parameters - Page 7:
2025-07-23 22:32:13,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:13,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:13,964 - INFO - Response - Page 7:
2025-07-23 22:32:13,964 - INFO - 第 7 页获取到 50 条记录
2025-07-23 22:32:14,464 - INFO - Request Parameters - Page 8:
2025-07-23 22:32:14,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:14,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:15,152 - INFO - Response - Page 8:
2025-07-23 22:32:15,152 - INFO - 第 8 页获取到 50 条记录
2025-07-23 22:32:15,667 - INFO - Request Parameters - Page 9:
2025-07-23 22:32:15,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:15,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:16,387 - INFO - Response - Page 9:
2025-07-23 22:32:16,387 - INFO - 第 9 页获取到 50 条记录
2025-07-23 22:32:16,887 - INFO - Request Parameters - Page 10:
2025-07-23 22:32:16,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:16,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:17,574 - INFO - Response - Page 10:
2025-07-23 22:32:17,574 - INFO - 第 10 页获取到 50 条记录
2025-07-23 22:32:18,075 - INFO - Request Parameters - Page 11:
2025-07-23 22:32:18,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:18,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:18,637 - INFO - Response - Page 11:
2025-07-23 22:32:18,637 - INFO - 第 11 页获取到 15 条记录
2025-07-23 22:32:19,153 - INFO - 查询完成，共获取到 515 条记录
2025-07-23 22:32:19,153 - INFO - 获取到 515 条表单数据
2025-07-23 22:32:19,153 - INFO - 当前日期 2025-07-22 有 515 条MySQL数据需要处理
2025-07-23 22:32:19,169 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-23 22:32:19,169 - INFO - 开始处理日期: 2025-07-23
2025-07-23 22:32:19,169 - INFO - Request Parameters - Page 1:
2025-07-23 22:32:19,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:19,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:19,935 - INFO - Response - Page 1:
2025-07-23 22:32:19,935 - INFO - 第 1 页获取到 50 条记录
2025-07-23 22:32:20,451 - INFO - Request Parameters - Page 2:
2025-07-23 22:32:20,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-23 22:32:20,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-23 22:32:21,029 - INFO - Response - Page 2:
2025-07-23 22:32:21,029 - INFO - 第 2 页获取到 18 条记录
2025-07-23 22:32:21,545 - INFO - 查询完成，共获取到 68 条记录
2025-07-23 22:32:21,545 - INFO - 获取到 68 条表单数据
2025-07-23 22:32:21,545 - INFO - 当前日期 2025-07-23 有 68 条MySQL数据需要处理
2025-07-23 22:32:21,545 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-23 22:32:21,545 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-23 22:32:21,545 - INFO - 同步完成
