2025-06-02 00:00:02,833 - INFO - =================使用默认全量同步=============
2025-06-02 00:00:04,412 - INFO - MySQL查询成功，共获取 3398 条记录
2025-06-02 00:00:04,412 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-02 00:00:04,443 - INFO - 开始处理日期: 2025-01
2025-06-02 00:00:04,443 - INFO - Request Parameters - Page 1:
2025-06-02 00:00:04,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:04,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:05,443 - INFO - Response - Page 1:
2025-06-02 00:00:05,646 - INFO - 第 1 页获取到 100 条记录
2025-06-02 00:00:05,646 - INFO - Request Parameters - Page 2:
2025-06-02 00:00:05,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:05,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:06,537 - INFO - Response - Page 2:
2025-06-02 00:00:06,740 - INFO - 第 2 页获取到 100 条记录
2025-06-02 00:00:06,740 - INFO - Request Parameters - Page 3:
2025-06-02 00:00:06,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:06,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:07,240 - INFO - Response - Page 3:
2025-06-02 00:00:07,459 - INFO - 第 3 页获取到 100 条记录
2025-06-02 00:00:07,459 - INFO - Request Parameters - Page 4:
2025-06-02 00:00:07,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:07,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:07,990 - INFO - Response - Page 4:
2025-06-02 00:00:08,194 - INFO - 第 4 页获取到 100 条记录
2025-06-02 00:00:08,194 - INFO - Request Parameters - Page 5:
2025-06-02 00:00:08,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:08,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:08,631 - INFO - Response - Page 5:
2025-06-02 00:00:08,834 - INFO - 第 5 页获取到 100 条记录
2025-06-02 00:00:08,834 - INFO - Request Parameters - Page 6:
2025-06-02 00:00:08,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:08,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:09,381 - INFO - Response - Page 6:
2025-06-02 00:00:09,584 - INFO - 第 6 页获取到 100 条记录
2025-06-02 00:00:09,584 - INFO - Request Parameters - Page 7:
2025-06-02 00:00:09,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:09,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:10,053 - INFO - Response - Page 7:
2025-06-02 00:00:10,256 - INFO - 第 7 页获取到 82 条记录
2025-06-02 00:00:10,256 - INFO - 查询完成，共获取到 682 条记录
2025-06-02 00:00:10,256 - INFO - 获取到 682 条表单数据
2025-06-02 00:00:10,256 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-02 00:00:10,272 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 00:00:10,272 - INFO - 开始处理日期: 2025-02
2025-06-02 00:00:10,272 - INFO - Request Parameters - Page 1:
2025-06-02 00:00:10,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:10,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:10,741 - INFO - Response - Page 1:
2025-06-02 00:00:10,944 - INFO - 第 1 页获取到 100 条记录
2025-06-02 00:00:10,944 - INFO - Request Parameters - Page 2:
2025-06-02 00:00:10,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:10,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:11,382 - INFO - Response - Page 2:
2025-06-02 00:00:11,585 - INFO - 第 2 页获取到 100 条记录
2025-06-02 00:00:11,585 - INFO - Request Parameters - Page 3:
2025-06-02 00:00:11,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:11,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:12,038 - INFO - Response - Page 3:
2025-06-02 00:00:12,241 - INFO - 第 3 页获取到 100 条记录
2025-06-02 00:00:12,241 - INFO - Request Parameters - Page 4:
2025-06-02 00:00:12,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:12,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:12,694 - INFO - Response - Page 4:
2025-06-02 00:00:12,897 - INFO - 第 4 页获取到 100 条记录
2025-06-02 00:00:12,897 - INFO - Request Parameters - Page 5:
2025-06-02 00:00:12,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:12,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:13,397 - INFO - Response - Page 5:
2025-06-02 00:00:13,601 - INFO - 第 5 页获取到 100 条记录
2025-06-02 00:00:13,601 - INFO - Request Parameters - Page 6:
2025-06-02 00:00:13,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:13,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:14,085 - INFO - Response - Page 6:
2025-06-02 00:00:14,288 - INFO - 第 6 页获取到 100 条记录
2025-06-02 00:00:14,288 - INFO - Request Parameters - Page 7:
2025-06-02 00:00:14,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:14,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:14,726 - INFO - Response - Page 7:
2025-06-02 00:00:14,929 - INFO - 第 7 页获取到 70 条记录
2025-06-02 00:00:14,929 - INFO - 查询完成，共获取到 670 条记录
2025-06-02 00:00:14,929 - INFO - 获取到 670 条表单数据
2025-06-02 00:00:14,929 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-02 00:00:14,945 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 00:00:14,945 - INFO - 开始处理日期: 2025-03
2025-06-02 00:00:14,945 - INFO - Request Parameters - Page 1:
2025-06-02 00:00:14,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:14,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:15,570 - INFO - Response - Page 1:
2025-06-02 00:00:15,773 - INFO - 第 1 页获取到 100 条记录
2025-06-02 00:00:15,773 - INFO - Request Parameters - Page 2:
2025-06-02 00:00:15,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:15,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:16,273 - INFO - Response - Page 2:
2025-06-02 00:00:16,476 - INFO - 第 2 页获取到 100 条记录
2025-06-02 00:00:16,476 - INFO - Request Parameters - Page 3:
2025-06-02 00:00:16,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:16,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:17,164 - INFO - Response - Page 3:
2025-06-02 00:00:17,367 - INFO - 第 3 页获取到 100 条记录
2025-06-02 00:00:17,367 - INFO - Request Parameters - Page 4:
2025-06-02 00:00:17,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:17,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:17,867 - INFO - Response - Page 4:
2025-06-02 00:00:18,070 - INFO - 第 4 页获取到 100 条记录
2025-06-02 00:00:18,070 - INFO - Request Parameters - Page 5:
2025-06-02 00:00:18,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:18,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:18,476 - INFO - Response - Page 5:
2025-06-02 00:00:18,680 - INFO - 第 5 页获取到 100 条记录
2025-06-02 00:00:18,680 - INFO - Request Parameters - Page 6:
2025-06-02 00:00:18,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:18,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:19,211 - INFO - Response - Page 6:
2025-06-02 00:00:19,414 - INFO - 第 6 页获取到 100 条记录
2025-06-02 00:00:19,414 - INFO - Request Parameters - Page 7:
2025-06-02 00:00:19,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:19,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:20,180 - INFO - Response - Page 7:
2025-06-02 00:00:20,383 - INFO - 第 7 页获取到 61 条记录
2025-06-02 00:00:20,383 - INFO - 查询完成，共获取到 661 条记录
2025-06-02 00:00:20,383 - INFO - 获取到 661 条表单数据
2025-06-02 00:00:20,383 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-02 00:00:20,399 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 00:00:20,399 - INFO - 开始处理日期: 2025-04
2025-06-02 00:00:20,399 - INFO - Request Parameters - Page 1:
2025-06-02 00:00:20,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:20,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:21,367 - INFO - Response - Page 1:
2025-06-02 00:00:21,571 - INFO - 第 1 页获取到 100 条记录
2025-06-02 00:00:21,571 - INFO - Request Parameters - Page 2:
2025-06-02 00:00:21,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:21,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:22,039 - INFO - Response - Page 2:
2025-06-02 00:00:22,243 - INFO - 第 2 页获取到 100 条记录
2025-06-02 00:00:22,243 - INFO - Request Parameters - Page 3:
2025-06-02 00:00:22,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:22,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:22,696 - INFO - Response - Page 3:
2025-06-02 00:00:22,899 - INFO - 第 3 页获取到 100 条记录
2025-06-02 00:00:22,899 - INFO - Request Parameters - Page 4:
2025-06-02 00:00:22,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:22,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:23,368 - INFO - Response - Page 4:
2025-06-02 00:00:23,571 - INFO - 第 4 页获取到 100 条记录
2025-06-02 00:00:23,571 - INFO - Request Parameters - Page 5:
2025-06-02 00:00:23,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:23,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:24,040 - INFO - Response - Page 5:
2025-06-02 00:00:24,243 - INFO - 第 5 页获取到 100 条记录
2025-06-02 00:00:24,243 - INFO - Request Parameters - Page 6:
2025-06-02 00:00:24,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:24,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:24,743 - INFO - Response - Page 6:
2025-06-02 00:00:24,946 - INFO - 第 6 页获取到 100 条记录
2025-06-02 00:00:24,946 - INFO - Request Parameters - Page 7:
2025-06-02 00:00:24,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:24,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:25,368 - INFO - Response - Page 7:
2025-06-02 00:00:25,571 - INFO - 第 7 页获取到 56 条记录
2025-06-02 00:00:25,571 - INFO - 查询完成，共获取到 656 条记录
2025-06-02 00:00:25,571 - INFO - 获取到 656 条表单数据
2025-06-02 00:00:25,571 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-02 00:00:25,587 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 00:00:25,587 - INFO - 开始处理日期: 2025-05
2025-06-02 00:00:25,587 - INFO - Request Parameters - Page 1:
2025-06-02 00:00:25,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:25,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:26,228 - INFO - Response - Page 1:
2025-06-02 00:00:26,431 - INFO - 第 1 页获取到 100 条记录
2025-06-02 00:00:26,431 - INFO - Request Parameters - Page 2:
2025-06-02 00:00:26,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:26,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:26,884 - INFO - Response - Page 2:
2025-06-02 00:00:27,087 - INFO - 第 2 页获取到 100 条记录
2025-06-02 00:00:27,087 - INFO - Request Parameters - Page 3:
2025-06-02 00:00:27,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:27,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:27,618 - INFO - Response - Page 3:
2025-06-02 00:00:27,822 - INFO - 第 3 页获取到 100 条记录
2025-06-02 00:00:27,822 - INFO - Request Parameters - Page 4:
2025-06-02 00:00:27,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:27,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:28,353 - INFO - Response - Page 4:
2025-06-02 00:00:28,556 - INFO - 第 4 页获取到 100 条记录
2025-06-02 00:00:28,556 - INFO - Request Parameters - Page 5:
2025-06-02 00:00:28,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:28,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:29,009 - INFO - Response - Page 5:
2025-06-02 00:00:29,213 - INFO - 第 5 页获取到 100 条记录
2025-06-02 00:00:29,213 - INFO - Request Parameters - Page 6:
2025-06-02 00:00:29,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:29,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:29,650 - INFO - Response - Page 6:
2025-06-02 00:00:29,853 - INFO - 第 6 页获取到 100 条记录
2025-06-02 00:00:29,853 - INFO - Request Parameters - Page 7:
2025-06-02 00:00:29,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:29,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:30,197 - INFO - Response - Page 7:
2025-06-02 00:00:30,400 - INFO - 第 7 页获取到 36 条记录
2025-06-02 00:00:30,400 - INFO - 查询完成，共获取到 636 条记录
2025-06-02 00:00:30,400 - INFO - 获取到 636 条表单数据
2025-06-02 00:00:30,400 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-02 00:00:30,416 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-06-02 00:00:30,807 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-06-02 00:00:30,807 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122167.24, 'new_value': 126784.93}, {'field': 'offline_amount', 'old_value': 457722.9, 'new_value': 496962.1}, {'field': 'total_amount', 'old_value': 579890.14, 'new_value': 623747.03}, {'field': 'order_count', 'old_value': 4321, 'new_value': 4520}]
2025-06-02 00:00:30,807 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-02 00:00:30,807 - INFO - 开始处理日期: 2025-06
2025-06-02 00:00:30,807 - INFO - Request Parameters - Page 1:
2025-06-02 00:00:30,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:00:30,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:00:31,088 - INFO - Response - Page 1:
2025-06-02 00:00:31,291 - INFO - 第 1 页获取到 5 条记录
2025-06-02 00:00:31,291 - INFO - 查询完成，共获取到 5 条记录
2025-06-02 00:00:31,291 - INFO - 获取到 5 条表单数据
2025-06-02 00:00:31,291 - INFO - 当前日期 2025-06 有 93 条MySQL数据需要处理
2025-06-02 00:00:31,291 - INFO - 开始批量插入 88 条新记录
2025-06-02 00:00:31,557 - INFO - 批量插入响应状态码: 200
2025-06-02 00:00:31,557 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 16:00:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4236', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8CB39E5F-23D1-73AF-9FC4-A0A862717E2C', 'x-acs-trace-id': 'b2be4767490cdf629c020d56d61f6f9f', 'etag': '4IVmwfMfVW+DeAQOUNHzDlw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 00:00:31,557 - INFO - 批量插入响应体: {'result': ['FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMX4', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMY4', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMZ4', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM05', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM15', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM25', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM35', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM45', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM55', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM65', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM75', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM85', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM95', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMA5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMB5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMC5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMD5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBME5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMF5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMG5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMH5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMI5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMJ5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMK5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBML5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMM5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMN5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMO5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMP5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMQ5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMR5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMS5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMT5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMU5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM06', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM16', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM26', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM36', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM46', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM56', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM66', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM76', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM86', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM96', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMB6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMD6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBME6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMF6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMG6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMH6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMI6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMJ6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMK6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBML6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMM6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMN6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMO6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMP6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMQ6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMR6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMS6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMT6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMU6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM07', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM17', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM27', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM37', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM47', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM57', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM67', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM77', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM87', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM97', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA7', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMB7', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC7']}
2025-06-02 00:00:31,557 - INFO - 批量插入表单数据成功，批次 1，共 88 条记录
2025-06-02 00:00:31,557 - INFO - 成功插入的数据ID: ['FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMX4', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMY4', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMZ4', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM05', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM15', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM25', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM35', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM45', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM55', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM65', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM75', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM85', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM95', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMA5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMB5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMC5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMD5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBME5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMF5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMG5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMH5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMI5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMJ5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMK5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBML5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMM5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMN5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMO5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMP5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMQ5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMR5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMS5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMT5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMU5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ5', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM06', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM16', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM26', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM36', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM46', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM56', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM66', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM76', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM86', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM96', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMB6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMD6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBME6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMF6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMG6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMH6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMI6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMJ6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMK6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBML6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMM6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMN6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMO6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMP6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMQ6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMR6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMS6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMT6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMU6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ6', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM07', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM17', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM27', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM37', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM47', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM57', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM67', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM77', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM87', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM97', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA7', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMB7', 'FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC7']
2025-06-02 00:00:34,573 - INFO - 批量插入完成，共 88 条记录
2025-06-02 00:00:34,573 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 88 条，错误: 0 条
2025-06-02 00:00:34,573 - INFO - 数据同步完成！更新: 1 条，插入: 88 条，错误: 0 条
2025-06-02 00:00:34,573 - INFO - =================同步完成====================
2025-06-02 03:00:02,793 - INFO - =================使用默认全量同步=============
2025-06-02 03:00:04,356 - INFO - MySQL查询成功，共获取 3399 条记录
2025-06-02 03:00:04,356 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-02 03:00:04,387 - INFO - 开始处理日期: 2025-01
2025-06-02 03:00:04,387 - INFO - Request Parameters - Page 1:
2025-06-02 03:00:04,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:04,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:05,372 - INFO - Response - Page 1:
2025-06-02 03:00:05,575 - INFO - 第 1 页获取到 100 条记录
2025-06-02 03:00:05,575 - INFO - Request Parameters - Page 2:
2025-06-02 03:00:05,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:05,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:06,247 - INFO - Response - Page 2:
2025-06-02 03:00:06,450 - INFO - 第 2 页获取到 100 条记录
2025-06-02 03:00:06,450 - INFO - Request Parameters - Page 3:
2025-06-02 03:00:06,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:06,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:06,935 - INFO - Response - Page 3:
2025-06-02 03:00:07,138 - INFO - 第 3 页获取到 100 条记录
2025-06-02 03:00:07,138 - INFO - Request Parameters - Page 4:
2025-06-02 03:00:07,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:07,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:07,935 - INFO - Response - Page 4:
2025-06-02 03:00:08,138 - INFO - 第 4 页获取到 100 条记录
2025-06-02 03:00:08,138 - INFO - Request Parameters - Page 5:
2025-06-02 03:00:08,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:08,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:08,669 - INFO - Response - Page 5:
2025-06-02 03:00:08,872 - INFO - 第 5 页获取到 100 条记录
2025-06-02 03:00:08,872 - INFO - Request Parameters - Page 6:
2025-06-02 03:00:08,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:08,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:09,482 - INFO - Response - Page 6:
2025-06-02 03:00:09,685 - INFO - 第 6 页获取到 100 条记录
2025-06-02 03:00:09,685 - INFO - Request Parameters - Page 7:
2025-06-02 03:00:09,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:09,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:10,107 - INFO - Response - Page 7:
2025-06-02 03:00:10,310 - INFO - 第 7 页获取到 82 条记录
2025-06-02 03:00:10,310 - INFO - 查询完成，共获取到 682 条记录
2025-06-02 03:00:10,310 - INFO - 获取到 682 条表单数据
2025-06-02 03:00:10,310 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-02 03:00:10,326 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 03:00:10,326 - INFO - 开始处理日期: 2025-02
2025-06-02 03:00:10,326 - INFO - Request Parameters - Page 1:
2025-06-02 03:00:10,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:10,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:10,810 - INFO - Response - Page 1:
2025-06-02 03:00:11,013 - INFO - 第 1 页获取到 100 条记录
2025-06-02 03:00:11,013 - INFO - Request Parameters - Page 2:
2025-06-02 03:00:11,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:11,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:11,451 - INFO - Response - Page 2:
2025-06-02 03:00:11,654 - INFO - 第 2 页获取到 100 条记录
2025-06-02 03:00:11,654 - INFO - Request Parameters - Page 3:
2025-06-02 03:00:11,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:11,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:12,170 - INFO - Response - Page 3:
2025-06-02 03:00:12,373 - INFO - 第 3 页获取到 100 条记录
2025-06-02 03:00:12,373 - INFO - Request Parameters - Page 4:
2025-06-02 03:00:12,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:12,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:12,936 - INFO - Response - Page 4:
2025-06-02 03:00:13,139 - INFO - 第 4 页获取到 100 条记录
2025-06-02 03:00:13,139 - INFO - Request Parameters - Page 5:
2025-06-02 03:00:13,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:13,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:13,654 - INFO - Response - Page 5:
2025-06-02 03:00:13,858 - INFO - 第 5 页获取到 100 条记录
2025-06-02 03:00:13,858 - INFO - Request Parameters - Page 6:
2025-06-02 03:00:13,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:13,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:14,342 - INFO - Response - Page 6:
2025-06-02 03:00:14,545 - INFO - 第 6 页获取到 100 条记录
2025-06-02 03:00:14,545 - INFO - Request Parameters - Page 7:
2025-06-02 03:00:14,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:14,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:14,983 - INFO - Response - Page 7:
2025-06-02 03:00:15,186 - INFO - 第 7 页获取到 70 条记录
2025-06-02 03:00:15,186 - INFO - 查询完成，共获取到 670 条记录
2025-06-02 03:00:15,186 - INFO - 获取到 670 条表单数据
2025-06-02 03:00:15,186 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-02 03:00:15,202 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 03:00:15,202 - INFO - 开始处理日期: 2025-03
2025-06-02 03:00:15,202 - INFO - Request Parameters - Page 1:
2025-06-02 03:00:15,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:15,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:15,764 - INFO - Response - Page 1:
2025-06-02 03:00:15,967 - INFO - 第 1 页获取到 100 条记录
2025-06-02 03:00:15,967 - INFO - Request Parameters - Page 2:
2025-06-02 03:00:15,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:15,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:16,452 - INFO - Response - Page 2:
2025-06-02 03:00:16,655 - INFO - 第 2 页获取到 100 条记录
2025-06-02 03:00:16,655 - INFO - Request Parameters - Page 3:
2025-06-02 03:00:16,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:16,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:17,421 - INFO - Response - Page 3:
2025-06-02 03:00:17,624 - INFO - 第 3 页获取到 100 条记录
2025-06-02 03:00:17,624 - INFO - Request Parameters - Page 4:
2025-06-02 03:00:17,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:17,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:18,140 - INFO - Response - Page 4:
2025-06-02 03:00:18,343 - INFO - 第 4 页获取到 100 条记录
2025-06-02 03:00:18,343 - INFO - Request Parameters - Page 5:
2025-06-02 03:00:18,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:18,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:18,858 - INFO - Response - Page 5:
2025-06-02 03:00:19,062 - INFO - 第 5 页获取到 100 条记录
2025-06-02 03:00:19,062 - INFO - Request Parameters - Page 6:
2025-06-02 03:00:19,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:19,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:19,562 - INFO - Response - Page 6:
2025-06-02 03:00:19,765 - INFO - 第 6 页获取到 100 条记录
2025-06-02 03:00:19,765 - INFO - Request Parameters - Page 7:
2025-06-02 03:00:19,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:19,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:20,234 - INFO - Response - Page 7:
2025-06-02 03:00:20,437 - INFO - 第 7 页获取到 61 条记录
2025-06-02 03:00:20,437 - INFO - 查询完成，共获取到 661 条记录
2025-06-02 03:00:20,437 - INFO - 获取到 661 条表单数据
2025-06-02 03:00:20,437 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-02 03:00:20,452 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 03:00:20,452 - INFO - 开始处理日期: 2025-04
2025-06-02 03:00:20,452 - INFO - Request Parameters - Page 1:
2025-06-02 03:00:20,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:20,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:20,999 - INFO - Response - Page 1:
2025-06-02 03:00:21,203 - INFO - 第 1 页获取到 100 条记录
2025-06-02 03:00:21,203 - INFO - Request Parameters - Page 2:
2025-06-02 03:00:21,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:21,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:21,703 - INFO - Response - Page 2:
2025-06-02 03:00:21,906 - INFO - 第 2 页获取到 100 条记录
2025-06-02 03:00:21,906 - INFO - Request Parameters - Page 3:
2025-06-02 03:00:21,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:21,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:22,375 - INFO - Response - Page 3:
2025-06-02 03:00:22,578 - INFO - 第 3 页获取到 100 条记录
2025-06-02 03:00:22,578 - INFO - Request Parameters - Page 4:
2025-06-02 03:00:22,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:22,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:23,109 - INFO - Response - Page 4:
2025-06-02 03:00:23,312 - INFO - 第 4 页获取到 100 条记录
2025-06-02 03:00:23,312 - INFO - Request Parameters - Page 5:
2025-06-02 03:00:23,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:23,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:23,828 - INFO - Response - Page 5:
2025-06-02 03:00:24,031 - INFO - 第 5 页获取到 100 条记录
2025-06-02 03:00:24,031 - INFO - Request Parameters - Page 6:
2025-06-02 03:00:24,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:24,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:24,500 - INFO - Response - Page 6:
2025-06-02 03:00:24,703 - INFO - 第 6 页获取到 100 条记录
2025-06-02 03:00:24,703 - INFO - Request Parameters - Page 7:
2025-06-02 03:00:24,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:24,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:25,125 - INFO - Response - Page 7:
2025-06-02 03:00:25,328 - INFO - 第 7 页获取到 56 条记录
2025-06-02 03:00:25,328 - INFO - 查询完成，共获取到 656 条记录
2025-06-02 03:00:25,328 - INFO - 获取到 656 条表单数据
2025-06-02 03:00:25,328 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-02 03:00:25,344 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 03:00:25,344 - INFO - 开始处理日期: 2025-05
2025-06-02 03:00:25,344 - INFO - Request Parameters - Page 1:
2025-06-02 03:00:25,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:25,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:25,844 - INFO - Response - Page 1:
2025-06-02 03:00:26,047 - INFO - 第 1 页获取到 100 条记录
2025-06-02 03:00:26,047 - INFO - Request Parameters - Page 2:
2025-06-02 03:00:26,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:26,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:26,532 - INFO - Response - Page 2:
2025-06-02 03:00:26,735 - INFO - 第 2 页获取到 100 条记录
2025-06-02 03:00:26,735 - INFO - Request Parameters - Page 3:
2025-06-02 03:00:26,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:26,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:27,157 - INFO - Response - Page 3:
2025-06-02 03:00:27,360 - INFO - 第 3 页获取到 100 条记录
2025-06-02 03:00:27,360 - INFO - Request Parameters - Page 4:
2025-06-02 03:00:27,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:27,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:27,860 - INFO - Response - Page 4:
2025-06-02 03:00:28,063 - INFO - 第 4 页获取到 100 条记录
2025-06-02 03:00:28,063 - INFO - Request Parameters - Page 5:
2025-06-02 03:00:28,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:28,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:28,532 - INFO - Response - Page 5:
2025-06-02 03:00:28,735 - INFO - 第 5 页获取到 100 条记录
2025-06-02 03:00:28,735 - INFO - Request Parameters - Page 6:
2025-06-02 03:00:28,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:28,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:29,313 - INFO - Response - Page 6:
2025-06-02 03:00:29,516 - INFO - 第 6 页获取到 100 条记录
2025-06-02 03:00:29,516 - INFO - Request Parameters - Page 7:
2025-06-02 03:00:29,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:29,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:29,891 - INFO - Response - Page 7:
2025-06-02 03:00:30,095 - INFO - 第 7 页获取到 36 条记录
2025-06-02 03:00:30,095 - INFO - 查询完成，共获取到 636 条记录
2025-06-02 03:00:30,095 - INFO - 获取到 636 条表单数据
2025-06-02 03:00:30,095 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-02 03:00:30,110 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 03:00:30,110 - INFO - 开始处理日期: 2025-06
2025-06-02 03:00:30,110 - INFO - Request Parameters - Page 1:
2025-06-02 03:00:30,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:00:30,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:00:30,548 - INFO - Response - Page 1:
2025-06-02 03:00:30,751 - INFO - 第 1 页获取到 93 条记录
2025-06-02 03:00:30,751 - INFO - 查询完成，共获取到 93 条记录
2025-06-02 03:00:30,751 - INFO - 获取到 93 条表单数据
2025-06-02 03:00:30,751 - INFO - 当前日期 2025-06 有 94 条MySQL数据需要处理
2025-06-02 03:00:30,751 - INFO - 开始批量插入 1 条新记录
2025-06-02 03:00:30,907 - INFO - 批量插入响应状态码: 200
2025-06-02 03:00:30,907 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 19:00:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C5A32B23-E9ED-7178-98D8-7BCF36BCD484', 'x-acs-trace-id': '7fdad05cb58995d703fefea1e464c0d3', 'etag': '6hGAPcyLzAE08LhWx2dfDUA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 03:00:30,907 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA18MWVGZCQAQ67Q8SH2F482W4501EBM78']}
2025-06-02 03:00:30,907 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-02 03:00:30,907 - INFO - 成功插入的数据ID: ['FINST-7PF66BA18MWVGZCQAQ67Q8SH2F482W4501EBM78']
2025-06-02 03:00:33,923 - INFO - 批量插入完成，共 1 条记录
2025-06-02 03:00:33,923 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-02 03:00:33,923 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-02 03:00:33,923 - INFO - =================同步完成====================
2025-06-02 06:00:02,784 - INFO - =================使用默认全量同步=============
2025-06-02 06:00:04,316 - INFO - MySQL查询成功，共获取 3439 条记录
2025-06-02 06:00:04,316 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-02 06:00:04,347 - INFO - 开始处理日期: 2025-01
2025-06-02 06:00:04,347 - INFO - Request Parameters - Page 1:
2025-06-02 06:00:04,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:04,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:05,753 - INFO - Response - Page 1:
2025-06-02 06:00:05,957 - INFO - 第 1 页获取到 100 条记录
2025-06-02 06:00:05,957 - INFO - Request Parameters - Page 2:
2025-06-02 06:00:05,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:05,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:06,550 - INFO - Response - Page 2:
2025-06-02 06:00:06,754 - INFO - 第 2 页获取到 100 条记录
2025-06-02 06:00:06,754 - INFO - Request Parameters - Page 3:
2025-06-02 06:00:06,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:06,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:07,316 - INFO - Response - Page 3:
2025-06-02 06:00:07,519 - INFO - 第 3 页获取到 100 条记录
2025-06-02 06:00:07,519 - INFO - Request Parameters - Page 4:
2025-06-02 06:00:07,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:07,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:08,301 - INFO - Response - Page 4:
2025-06-02 06:00:08,504 - INFO - 第 4 页获取到 100 条记录
2025-06-02 06:00:08,504 - INFO - Request Parameters - Page 5:
2025-06-02 06:00:08,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:08,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:09,004 - INFO - Response - Page 5:
2025-06-02 06:00:09,207 - INFO - 第 5 页获取到 100 条记录
2025-06-02 06:00:09,207 - INFO - Request Parameters - Page 6:
2025-06-02 06:00:09,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:09,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:09,707 - INFO - Response - Page 6:
2025-06-02 06:00:09,910 - INFO - 第 6 页获取到 100 条记录
2025-06-02 06:00:09,910 - INFO - Request Parameters - Page 7:
2025-06-02 06:00:09,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:09,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:10,379 - INFO - Response - Page 7:
2025-06-02 06:00:10,582 - INFO - 第 7 页获取到 82 条记录
2025-06-02 06:00:10,582 - INFO - 查询完成，共获取到 682 条记录
2025-06-02 06:00:10,582 - INFO - 获取到 682 条表单数据
2025-06-02 06:00:10,582 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-02 06:00:10,598 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 06:00:10,598 - INFO - 开始处理日期: 2025-02
2025-06-02 06:00:10,598 - INFO - Request Parameters - Page 1:
2025-06-02 06:00:10,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:10,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:11,114 - INFO - Response - Page 1:
2025-06-02 06:00:11,317 - INFO - 第 1 页获取到 100 条记录
2025-06-02 06:00:11,317 - INFO - Request Parameters - Page 2:
2025-06-02 06:00:11,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:11,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:11,833 - INFO - Response - Page 2:
2025-06-02 06:00:12,036 - INFO - 第 2 页获取到 100 条记录
2025-06-02 06:00:12,036 - INFO - Request Parameters - Page 3:
2025-06-02 06:00:12,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:12,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:12,551 - INFO - Response - Page 3:
2025-06-02 06:00:12,755 - INFO - 第 3 页获取到 100 条记录
2025-06-02 06:00:12,755 - INFO - Request Parameters - Page 4:
2025-06-02 06:00:12,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:12,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:13,286 - INFO - Response - Page 4:
2025-06-02 06:00:13,489 - INFO - 第 4 页获取到 100 条记录
2025-06-02 06:00:13,489 - INFO - Request Parameters - Page 5:
2025-06-02 06:00:13,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:13,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:14,036 - INFO - Response - Page 5:
2025-06-02 06:00:14,239 - INFO - 第 5 页获取到 100 条记录
2025-06-02 06:00:14,239 - INFO - Request Parameters - Page 6:
2025-06-02 06:00:14,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:14,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:14,739 - INFO - Response - Page 6:
2025-06-02 06:00:14,942 - INFO - 第 6 页获取到 100 条记录
2025-06-02 06:00:14,942 - INFO - Request Parameters - Page 7:
2025-06-02 06:00:14,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:14,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:15,380 - INFO - Response - Page 7:
2025-06-02 06:00:15,583 - INFO - 第 7 页获取到 70 条记录
2025-06-02 06:00:15,583 - INFO - 查询完成，共获取到 670 条记录
2025-06-02 06:00:15,583 - INFO - 获取到 670 条表单数据
2025-06-02 06:00:15,583 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-02 06:00:15,599 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 06:00:15,599 - INFO - 开始处理日期: 2025-03
2025-06-02 06:00:15,599 - INFO - Request Parameters - Page 1:
2025-06-02 06:00:15,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:15,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:16,130 - INFO - Response - Page 1:
2025-06-02 06:00:16,333 - INFO - 第 1 页获取到 100 条记录
2025-06-02 06:00:16,333 - INFO - Request Parameters - Page 2:
2025-06-02 06:00:16,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:16,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:16,849 - INFO - Response - Page 2:
2025-06-02 06:00:17,052 - INFO - 第 2 页获取到 100 条记录
2025-06-02 06:00:17,052 - INFO - Request Parameters - Page 3:
2025-06-02 06:00:17,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:17,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:17,583 - INFO - Response - Page 3:
2025-06-02 06:00:17,787 - INFO - 第 3 页获取到 100 条记录
2025-06-02 06:00:17,787 - INFO - Request Parameters - Page 4:
2025-06-02 06:00:17,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:17,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:18,334 - INFO - Response - Page 4:
2025-06-02 06:00:18,537 - INFO - 第 4 页获取到 100 条记录
2025-06-02 06:00:18,537 - INFO - Request Parameters - Page 5:
2025-06-02 06:00:18,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:18,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:18,990 - INFO - Response - Page 5:
2025-06-02 06:00:19,193 - INFO - 第 5 页获取到 100 条记录
2025-06-02 06:00:19,193 - INFO - Request Parameters - Page 6:
2025-06-02 06:00:19,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:19,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:19,724 - INFO - Response - Page 6:
2025-06-02 06:00:19,928 - INFO - 第 6 页获取到 100 条记录
2025-06-02 06:00:19,928 - INFO - Request Parameters - Page 7:
2025-06-02 06:00:19,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:19,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:20,412 - INFO - Response - Page 7:
2025-06-02 06:00:20,615 - INFO - 第 7 页获取到 61 条记录
2025-06-02 06:00:20,615 - INFO - 查询完成，共获取到 661 条记录
2025-06-02 06:00:20,615 - INFO - 获取到 661 条表单数据
2025-06-02 06:00:20,615 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-02 06:00:20,631 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 06:00:20,631 - INFO - 开始处理日期: 2025-04
2025-06-02 06:00:20,631 - INFO - Request Parameters - Page 1:
2025-06-02 06:00:20,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:20,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:21,147 - INFO - Response - Page 1:
2025-06-02 06:00:21,350 - INFO - 第 1 页获取到 100 条记录
2025-06-02 06:00:21,350 - INFO - Request Parameters - Page 2:
2025-06-02 06:00:21,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:21,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:21,834 - INFO - Response - Page 2:
2025-06-02 06:00:22,037 - INFO - 第 2 页获取到 100 条记录
2025-06-02 06:00:22,037 - INFO - Request Parameters - Page 3:
2025-06-02 06:00:22,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:22,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:22,522 - INFO - Response - Page 3:
2025-06-02 06:00:22,725 - INFO - 第 3 页获取到 100 条记录
2025-06-02 06:00:22,725 - INFO - Request Parameters - Page 4:
2025-06-02 06:00:22,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:22,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:23,303 - INFO - Response - Page 4:
2025-06-02 06:00:23,506 - INFO - 第 4 页获取到 100 条记录
2025-06-02 06:00:23,506 - INFO - Request Parameters - Page 5:
2025-06-02 06:00:23,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:23,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:24,022 - INFO - Response - Page 5:
2025-06-02 06:00:24,225 - INFO - 第 5 页获取到 100 条记录
2025-06-02 06:00:24,225 - INFO - Request Parameters - Page 6:
2025-06-02 06:00:24,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:24,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:24,741 - INFO - Response - Page 6:
2025-06-02 06:00:24,944 - INFO - 第 6 页获取到 100 条记录
2025-06-02 06:00:24,944 - INFO - Request Parameters - Page 7:
2025-06-02 06:00:24,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:24,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:25,350 - INFO - Response - Page 7:
2025-06-02 06:00:25,554 - INFO - 第 7 页获取到 56 条记录
2025-06-02 06:00:25,554 - INFO - 查询完成，共获取到 656 条记录
2025-06-02 06:00:25,554 - INFO - 获取到 656 条表单数据
2025-06-02 06:00:25,554 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-02 06:00:25,569 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 06:00:25,569 - INFO - 开始处理日期: 2025-05
2025-06-02 06:00:25,569 - INFO - Request Parameters - Page 1:
2025-06-02 06:00:25,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:25,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:26,085 - INFO - Response - Page 1:
2025-06-02 06:00:26,288 - INFO - 第 1 页获取到 100 条记录
2025-06-02 06:00:26,288 - INFO - Request Parameters - Page 2:
2025-06-02 06:00:26,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:26,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:26,757 - INFO - Response - Page 2:
2025-06-02 06:00:26,960 - INFO - 第 2 页获取到 100 条记录
2025-06-02 06:00:26,960 - INFO - Request Parameters - Page 3:
2025-06-02 06:00:26,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:26,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:27,444 - INFO - Response - Page 3:
2025-06-02 06:00:27,648 - INFO - 第 3 页获取到 100 条记录
2025-06-02 06:00:27,648 - INFO - Request Parameters - Page 4:
2025-06-02 06:00:27,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:27,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:28,116 - INFO - Response - Page 4:
2025-06-02 06:00:28,320 - INFO - 第 4 页获取到 100 条记录
2025-06-02 06:00:28,320 - INFO - Request Parameters - Page 5:
2025-06-02 06:00:28,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:28,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:28,773 - INFO - Response - Page 5:
2025-06-02 06:00:28,976 - INFO - 第 5 页获取到 100 条记录
2025-06-02 06:00:28,976 - INFO - Request Parameters - Page 6:
2025-06-02 06:00:28,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:28,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:29,445 - INFO - Response - Page 6:
2025-06-02 06:00:29,648 - INFO - 第 6 页获取到 100 条记录
2025-06-02 06:00:29,648 - INFO - Request Parameters - Page 7:
2025-06-02 06:00:29,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:29,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:30,054 - INFO - Response - Page 7:
2025-06-02 06:00:30,257 - INFO - 第 7 页获取到 36 条记录
2025-06-02 06:00:30,257 - INFO - 查询完成，共获取到 636 条记录
2025-06-02 06:00:30,257 - INFO - 获取到 636 条表单数据
2025-06-02 06:00:30,257 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-02 06:00:30,273 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 06:00:30,273 - INFO - 开始处理日期: 2025-06
2025-06-02 06:00:30,273 - INFO - Request Parameters - Page 1:
2025-06-02 06:00:30,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:00:30,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:00:30,789 - INFO - Response - Page 1:
2025-06-02 06:00:30,992 - INFO - 第 1 页获取到 94 条记录
2025-06-02 06:00:30,992 - INFO - 查询完成，共获取到 94 条记录
2025-06-02 06:00:30,992 - INFO - 获取到 94 条表单数据
2025-06-02 06:00:30,992 - INFO - 当前日期 2025-06 有 134 条MySQL数据需要处理
2025-06-02 06:00:30,992 - INFO - 开始批量插入 40 条新记录
2025-06-02 06:00:31,211 - INFO - 批量插入响应状态码: 200
2025-06-02 06:00:31,211 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 22:00:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1932', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C80B6065-4D36-73F3-B68E-D61718EBDE45', 'x-acs-trace-id': '30645d97d132c4bfd82995a02d6d85fa', 'etag': '1jDUaJtFKP34BLwmQpLEDAQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 06:00:31,211 - INFO - 批量插入响应体: {'result': ['FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7B', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8B', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9B', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMAB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMBB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMCB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMDB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMEB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMFB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMGB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMHB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMIB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMJB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMKB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMLB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMMB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMNB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMOB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMPB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMQB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMRB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMSB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMTB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMUB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMVB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMWB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMXB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMYB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMZB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM0C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM1C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM2C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM3C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM4C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM5C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM6C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMAC']}
2025-06-02 06:00:31,211 - INFO - 批量插入表单数据成功，批次 1，共 40 条记录
2025-06-02 06:00:31,211 - INFO - 成功插入的数据ID: ['FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7B', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8B', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9B', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMAB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMBB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMCB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMDB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMEB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMFB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMGB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMHB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMIB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMJB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMKB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMLB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMMB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMNB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMOB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMPB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMQB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMRB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMSB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMTB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMUB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMVB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMWB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMXB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMYB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMZB', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM0C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM1C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM2C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM3C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM4C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM5C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM6C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9C', 'FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMAC']
2025-06-02 06:00:34,227 - INFO - 批量插入完成，共 40 条记录
2025-06-02 06:00:34,227 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 40 条，错误: 0 条
2025-06-02 06:00:34,227 - INFO - 数据同步完成！更新: 0 条，插入: 40 条，错误: 0 条
2025-06-02 06:00:34,227 - INFO - =================同步完成====================
2025-06-02 09:00:02,792 - INFO - =================使用默认全量同步=============
2025-06-02 09:00:04,339 - INFO - MySQL查询成功，共获取 3585 条记录
2025-06-02 09:00:04,339 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-02 09:00:04,370 - INFO - 开始处理日期: 2025-01
2025-06-02 09:00:04,370 - INFO - Request Parameters - Page 1:
2025-06-02 09:00:04,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:04,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:05,276 - INFO - Response - Page 1:
2025-06-02 09:00:05,480 - INFO - 第 1 页获取到 100 条记录
2025-06-02 09:00:05,480 - INFO - Request Parameters - Page 2:
2025-06-02 09:00:05,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:05,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:05,995 - INFO - Response - Page 2:
2025-06-02 09:00:06,198 - INFO - 第 2 页获取到 100 条记录
2025-06-02 09:00:06,198 - INFO - Request Parameters - Page 3:
2025-06-02 09:00:06,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:06,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:06,698 - INFO - Response - Page 3:
2025-06-02 09:00:06,902 - INFO - 第 3 页获取到 100 条记录
2025-06-02 09:00:06,902 - INFO - Request Parameters - Page 4:
2025-06-02 09:00:06,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:06,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:07,824 - INFO - Response - Page 4:
2025-06-02 09:00:08,027 - INFO - 第 4 页获取到 100 条记录
2025-06-02 09:00:08,027 - INFO - Request Parameters - Page 5:
2025-06-02 09:00:08,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:08,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:08,543 - INFO - Response - Page 5:
2025-06-02 09:00:08,746 - INFO - 第 5 页获取到 100 条记录
2025-06-02 09:00:08,746 - INFO - Request Parameters - Page 6:
2025-06-02 09:00:08,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:08,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:09,230 - INFO - Response - Page 6:
2025-06-02 09:00:09,433 - INFO - 第 6 页获取到 100 条记录
2025-06-02 09:00:09,433 - INFO - Request Parameters - Page 7:
2025-06-02 09:00:09,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:09,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:09,980 - INFO - Response - Page 7:
2025-06-02 09:00:10,183 - INFO - 第 7 页获取到 82 条记录
2025-06-02 09:00:10,183 - INFO - 查询完成，共获取到 682 条记录
2025-06-02 09:00:10,183 - INFO - 获取到 682 条表单数据
2025-06-02 09:00:10,183 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-02 09:00:10,199 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 09:00:10,199 - INFO - 开始处理日期: 2025-02
2025-06-02 09:00:10,199 - INFO - Request Parameters - Page 1:
2025-06-02 09:00:10,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:10,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:10,683 - INFO - Response - Page 1:
2025-06-02 09:00:10,887 - INFO - 第 1 页获取到 100 条记录
2025-06-02 09:00:10,887 - INFO - Request Parameters - Page 2:
2025-06-02 09:00:10,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:10,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:11,418 - INFO - Response - Page 2:
2025-06-02 09:00:11,621 - INFO - 第 2 页获取到 100 条记录
2025-06-02 09:00:11,621 - INFO - Request Parameters - Page 3:
2025-06-02 09:00:11,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:11,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:12,168 - INFO - Response - Page 3:
2025-06-02 09:00:12,371 - INFO - 第 3 页获取到 100 条记录
2025-06-02 09:00:12,371 - INFO - Request Parameters - Page 4:
2025-06-02 09:00:12,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:12,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:12,918 - INFO - Response - Page 4:
2025-06-02 09:00:13,121 - INFO - 第 4 页获取到 100 条记录
2025-06-02 09:00:13,121 - INFO - Request Parameters - Page 5:
2025-06-02 09:00:13,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:13,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:13,559 - INFO - Response - Page 5:
2025-06-02 09:00:13,762 - INFO - 第 5 页获取到 100 条记录
2025-06-02 09:00:13,762 - INFO - Request Parameters - Page 6:
2025-06-02 09:00:13,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:13,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:14,215 - INFO - Response - Page 6:
2025-06-02 09:00:14,418 - INFO - 第 6 页获取到 100 条记录
2025-06-02 09:00:14,418 - INFO - Request Parameters - Page 7:
2025-06-02 09:00:14,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:14,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:14,919 - INFO - Response - Page 7:
2025-06-02 09:00:15,122 - INFO - 第 7 页获取到 70 条记录
2025-06-02 09:00:15,122 - INFO - 查询完成，共获取到 670 条记录
2025-06-02 09:00:15,122 - INFO - 获取到 670 条表单数据
2025-06-02 09:00:15,122 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-02 09:00:15,137 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 09:00:15,137 - INFO - 开始处理日期: 2025-03
2025-06-02 09:00:15,137 - INFO - Request Parameters - Page 1:
2025-06-02 09:00:15,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:15,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:15,669 - INFO - Response - Page 1:
2025-06-02 09:00:15,872 - INFO - 第 1 页获取到 100 条记录
2025-06-02 09:00:15,872 - INFO - Request Parameters - Page 2:
2025-06-02 09:00:15,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:15,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:16,403 - INFO - Response - Page 2:
2025-06-02 09:00:16,606 - INFO - 第 2 页获取到 100 条记录
2025-06-02 09:00:16,606 - INFO - Request Parameters - Page 3:
2025-06-02 09:00:16,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:16,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:17,122 - INFO - Response - Page 3:
2025-06-02 09:00:17,325 - INFO - 第 3 页获取到 100 条记录
2025-06-02 09:00:17,325 - INFO - Request Parameters - Page 4:
2025-06-02 09:00:17,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:17,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:17,841 - INFO - Response - Page 4:
2025-06-02 09:00:18,044 - INFO - 第 4 页获取到 100 条记录
2025-06-02 09:00:18,044 - INFO - Request Parameters - Page 5:
2025-06-02 09:00:18,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:18,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:18,528 - INFO - Response - Page 5:
2025-06-02 09:00:18,732 - INFO - 第 5 页获取到 100 条记录
2025-06-02 09:00:18,732 - INFO - Request Parameters - Page 6:
2025-06-02 09:00:18,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:18,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:19,263 - INFO - Response - Page 6:
2025-06-02 09:00:19,466 - INFO - 第 6 页获取到 100 条记录
2025-06-02 09:00:19,466 - INFO - Request Parameters - Page 7:
2025-06-02 09:00:19,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:19,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:19,966 - INFO - Response - Page 7:
2025-06-02 09:00:20,169 - INFO - 第 7 页获取到 61 条记录
2025-06-02 09:00:20,169 - INFO - 查询完成，共获取到 661 条记录
2025-06-02 09:00:20,169 - INFO - 获取到 661 条表单数据
2025-06-02 09:00:20,169 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-02 09:00:20,185 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 09:00:20,185 - INFO - 开始处理日期: 2025-04
2025-06-02 09:00:20,185 - INFO - Request Parameters - Page 1:
2025-06-02 09:00:20,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:20,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:20,763 - INFO - Response - Page 1:
2025-06-02 09:00:20,966 - INFO - 第 1 页获取到 100 条记录
2025-06-02 09:00:20,966 - INFO - Request Parameters - Page 2:
2025-06-02 09:00:20,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:20,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:21,466 - INFO - Response - Page 2:
2025-06-02 09:00:21,670 - INFO - 第 2 页获取到 100 条记录
2025-06-02 09:00:21,670 - INFO - Request Parameters - Page 3:
2025-06-02 09:00:21,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:21,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:22,154 - INFO - Response - Page 3:
2025-06-02 09:00:22,357 - INFO - 第 3 页获取到 100 条记录
2025-06-02 09:00:22,357 - INFO - Request Parameters - Page 4:
2025-06-02 09:00:22,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:22,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:22,889 - INFO - Response - Page 4:
2025-06-02 09:00:23,092 - INFO - 第 4 页获取到 100 条记录
2025-06-02 09:00:23,092 - INFO - Request Parameters - Page 5:
2025-06-02 09:00:23,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:23,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:23,607 - INFO - Response - Page 5:
2025-06-02 09:00:23,811 - INFO - 第 5 页获取到 100 条记录
2025-06-02 09:00:23,811 - INFO - Request Parameters - Page 6:
2025-06-02 09:00:23,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:23,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:24,279 - INFO - Response - Page 6:
2025-06-02 09:00:24,483 - INFO - 第 6 页获取到 100 条记录
2025-06-02 09:00:24,483 - INFO - Request Parameters - Page 7:
2025-06-02 09:00:24,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:24,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:24,936 - INFO - Response - Page 7:
2025-06-02 09:00:25,139 - INFO - 第 7 页获取到 56 条记录
2025-06-02 09:00:25,139 - INFO - 查询完成，共获取到 656 条记录
2025-06-02 09:00:25,139 - INFO - 获取到 656 条表单数据
2025-06-02 09:00:25,139 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-02 09:00:25,155 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 09:00:25,155 - INFO - 开始处理日期: 2025-05
2025-06-02 09:00:25,155 - INFO - Request Parameters - Page 1:
2025-06-02 09:00:25,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:25,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:25,686 - INFO - Response - Page 1:
2025-06-02 09:00:25,889 - INFO - 第 1 页获取到 100 条记录
2025-06-02 09:00:25,889 - INFO - Request Parameters - Page 2:
2025-06-02 09:00:25,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:25,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:26,405 - INFO - Response - Page 2:
2025-06-02 09:00:26,608 - INFO - 第 2 页获取到 100 条记录
2025-06-02 09:00:26,608 - INFO - Request Parameters - Page 3:
2025-06-02 09:00:26,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:26,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:27,124 - INFO - Response - Page 3:
2025-06-02 09:00:27,327 - INFO - 第 3 页获取到 100 条记录
2025-06-02 09:00:27,327 - INFO - Request Parameters - Page 4:
2025-06-02 09:00:27,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:27,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:27,827 - INFO - Response - Page 4:
2025-06-02 09:00:28,030 - INFO - 第 4 页获取到 100 条记录
2025-06-02 09:00:28,030 - INFO - Request Parameters - Page 5:
2025-06-02 09:00:28,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:28,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:28,483 - INFO - Response - Page 5:
2025-06-02 09:00:28,686 - INFO - 第 5 页获取到 100 条记录
2025-06-02 09:00:28,686 - INFO - Request Parameters - Page 6:
2025-06-02 09:00:28,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:28,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:29,280 - INFO - Response - Page 6:
2025-06-02 09:00:29,483 - INFO - 第 6 页获取到 100 条记录
2025-06-02 09:00:29,483 - INFO - Request Parameters - Page 7:
2025-06-02 09:00:29,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:29,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:29,874 - INFO - Response - Page 7:
2025-06-02 09:00:30,077 - INFO - 第 7 页获取到 36 条记录
2025-06-02 09:00:30,077 - INFO - 查询完成，共获取到 636 条记录
2025-06-02 09:00:30,077 - INFO - 获取到 636 条表单数据
2025-06-02 09:00:30,077 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-02 09:00:30,093 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 09:00:30,093 - INFO - 开始处理日期: 2025-06
2025-06-02 09:00:30,093 - INFO - Request Parameters - Page 1:
2025-06-02 09:00:30,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:30,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:30,655 - INFO - Response - Page 1:
2025-06-02 09:00:30,859 - INFO - 第 1 页获取到 100 条记录
2025-06-02 09:00:30,859 - INFO - Request Parameters - Page 2:
2025-06-02 09:00:30,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:00:30,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:00:31,234 - INFO - Response - Page 2:
2025-06-02 09:00:31,437 - INFO - 第 2 页获取到 34 条记录
2025-06-02 09:00:31,437 - INFO - 查询完成，共获取到 134 条记录
2025-06-02 09:00:31,437 - INFO - 获取到 134 条表单数据
2025-06-02 09:00:31,437 - INFO - 当前日期 2025-06 有 280 条MySQL数据需要处理
2025-06-02 09:00:31,437 - INFO - 开始批量插入 146 条新记录
2025-06-02 09:00:31,749 - INFO - 批量插入响应状态码: 200
2025-06-02 09:00:31,749 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 01:00:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0E5C967E-5EE2-7A51-8D48-FC105AABA06A', 'x-acs-trace-id': 'b7421366b7336e5bb151bf4ea710150c', 'etag': '4s4KeBpAaCJFGpdQvoKd3VQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 09:00:31,749 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMJ5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMK5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBML5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMM5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMN5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMO5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMP5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMQ5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMR5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMS5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMT5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM06', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM16', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM26', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM36', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM46', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM56', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM66', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM76', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM86', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM96', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMD6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMP6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM07', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM17', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM27', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM37', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM47', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM57', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM67', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM77', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM87', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM97', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMD7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMP7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM08', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM18', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM28', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM38', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM48', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM58', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM68', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM78', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM88', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM98', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA8']}
2025-06-02 09:00:31,749 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-06-02 09:00:31,749 - INFO - 成功插入的数据ID: ['FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMJ5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMK5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBML5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMM5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMN5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMO5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMP5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMQ5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMR5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMS5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMT5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ5', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM06', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM16', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM26', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM36', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM46', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM56', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM66', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM76', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM86', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM96', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMD6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMP6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ6', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM07', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM17', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM27', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM37', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM47', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM57', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM67', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM77', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM87', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM97', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMD7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMP7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ7', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM08', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM18', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM28', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM38', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM48', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM58', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM68', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM78', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM88', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM98', 'FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA8']
2025-06-02 09:00:35,000 - INFO - 批量插入响应状态码: 200
2025-06-02 09:00:35,000 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 01:00:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2266', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0517084F-5771-7785-8DC6-48CE9D53FD87', 'x-acs-trace-id': '1dd3fb0f824aa5c2a5d189140da04f75', 'etag': '21qX0JcHtXPZ2LAw8E1eCWA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 09:00:35,000 - INFO - 批量插入响应体: {'result': ['FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMCQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMEQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMJQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMKQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMLQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMMQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMNQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMOQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMPQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMQQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMRQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMSQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMTQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMUQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMVQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMWQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMXQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMYQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMZQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM0R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM1R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM2R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM3R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM4R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM5R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM6R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM7R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM8R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM9R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMCR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMER1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMJR1']}
2025-06-02 09:00:35,000 - INFO - 批量插入表单数据成功，批次 2，共 46 条记录
2025-06-02 09:00:35,000 - INFO - 成功插入的数据ID: ['FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMCQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMEQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMJQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMKQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMLQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMMQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMNQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMOQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMPQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMQQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMRQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMSQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMTQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMUQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMVQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMWQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMXQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMYQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMZQ1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM0R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM1R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM2R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM3R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM4R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM5R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM6R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM7R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM8R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM9R1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMCR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMER1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIR1', 'FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMJR1']
2025-06-02 09:00:38,016 - INFO - 批量插入完成，共 146 条记录
2025-06-02 09:00:38,016 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 146 条，错误: 0 条
2025-06-02 09:00:38,016 - INFO - 数据同步完成！更新: 0 条，插入: 146 条，错误: 0 条
2025-06-02 09:00:38,016 - INFO - =================同步完成====================
2025-06-02 12:00:02,789 - INFO - =================使用默认全量同步=============
2025-06-02 12:00:04,351 - INFO - MySQL查询成功，共获取 3767 条记录
2025-06-02 12:00:04,352 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-02 12:00:04,385 - INFO - 开始处理日期: 2025-01
2025-06-02 12:00:04,388 - INFO - Request Parameters - Page 1:
2025-06-02 12:00:04,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:04,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:05,598 - INFO - Response - Page 1:
2025-06-02 12:00:05,799 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:00:05,799 - INFO - Request Parameters - Page 2:
2025-06-02 12:00:05,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:05,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:06,430 - INFO - Response - Page 2:
2025-06-02 12:00:06,634 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:00:06,634 - INFO - Request Parameters - Page 3:
2025-06-02 12:00:06,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:06,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:07,170 - INFO - Response - Page 3:
2025-06-02 12:00:07,371 - INFO - 第 3 页获取到 100 条记录
2025-06-02 12:00:07,371 - INFO - Request Parameters - Page 4:
2025-06-02 12:00:07,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:07,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:07,891 - INFO - Response - Page 4:
2025-06-02 12:00:08,091 - INFO - 第 4 页获取到 100 条记录
2025-06-02 12:00:08,091 - INFO - Request Parameters - Page 5:
2025-06-02 12:00:08,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:08,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:08,609 - INFO - Response - Page 5:
2025-06-02 12:00:08,813 - INFO - 第 5 页获取到 100 条记录
2025-06-02 12:00:08,813 - INFO - Request Parameters - Page 6:
2025-06-02 12:00:08,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:08,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:09,414 - INFO - Response - Page 6:
2025-06-02 12:00:09,614 - INFO - 第 6 页获取到 100 条记录
2025-06-02 12:00:09,614 - INFO - Request Parameters - Page 7:
2025-06-02 12:00:09,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:09,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:10,123 - INFO - Response - Page 7:
2025-06-02 12:00:10,324 - INFO - 第 7 页获取到 82 条记录
2025-06-02 12:00:10,324 - INFO - 查询完成，共获取到 682 条记录
2025-06-02 12:00:10,324 - INFO - 获取到 682 条表单数据
2025-06-02 12:00:10,336 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-02 12:00:10,343 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 12:00:10,343 - INFO - 开始处理日期: 2025-02
2025-06-02 12:00:10,343 - INFO - Request Parameters - Page 1:
2025-06-02 12:00:10,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:10,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:10,886 - INFO - Response - Page 1:
2025-06-02 12:00:11,087 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:00:11,087 - INFO - Request Parameters - Page 2:
2025-06-02 12:00:11,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:11,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:11,581 - INFO - Response - Page 2:
2025-06-02 12:00:11,790 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:00:11,790 - INFO - Request Parameters - Page 3:
2025-06-02 12:00:11,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:11,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:12,307 - INFO - Response - Page 3:
2025-06-02 12:00:12,508 - INFO - 第 3 页获取到 100 条记录
2025-06-02 12:00:12,508 - INFO - Request Parameters - Page 4:
2025-06-02 12:00:12,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:12,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:13,010 - INFO - Response - Page 4:
2025-06-02 12:00:13,214 - INFO - 第 4 页获取到 100 条记录
2025-06-02 12:00:13,214 - INFO - Request Parameters - Page 5:
2025-06-02 12:00:13,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:13,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:13,726 - INFO - Response - Page 5:
2025-06-02 12:00:13,931 - INFO - 第 5 页获取到 100 条记录
2025-06-02 12:00:13,931 - INFO - Request Parameters - Page 6:
2025-06-02 12:00:13,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:13,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:14,569 - INFO - Response - Page 6:
2025-06-02 12:00:14,772 - INFO - 第 6 页获取到 100 条记录
2025-06-02 12:00:14,772 - INFO - Request Parameters - Page 7:
2025-06-02 12:00:14,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:14,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:15,259 - INFO - Response - Page 7:
2025-06-02 12:00:15,474 - INFO - 第 7 页获取到 70 条记录
2025-06-02 12:00:15,474 - INFO - 查询完成，共获取到 670 条记录
2025-06-02 12:00:15,474 - INFO - 获取到 670 条表单数据
2025-06-02 12:00:15,491 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-02 12:00:15,498 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 12:00:15,504 - INFO - 开始处理日期: 2025-03
2025-06-02 12:00:15,504 - INFO - Request Parameters - Page 1:
2025-06-02 12:00:15,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:15,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:16,033 - INFO - Response - Page 1:
2025-06-02 12:00:16,238 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:00:16,238 - INFO - Request Parameters - Page 2:
2025-06-02 12:00:16,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:16,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:16,735 - INFO - Response - Page 2:
2025-06-02 12:00:16,941 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:00:16,941 - INFO - Request Parameters - Page 3:
2025-06-02 12:00:16,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:16,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:17,594 - INFO - Response - Page 3:
2025-06-02 12:00:17,794 - INFO - 第 3 页获取到 100 条记录
2025-06-02 12:00:17,794 - INFO - Request Parameters - Page 4:
2025-06-02 12:00:17,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:17,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:18,360 - INFO - Response - Page 4:
2025-06-02 12:00:18,560 - INFO - 第 4 页获取到 100 条记录
2025-06-02 12:00:18,560 - INFO - Request Parameters - Page 5:
2025-06-02 12:00:18,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:18,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:19,140 - INFO - Response - Page 5:
2025-06-02 12:00:19,340 - INFO - 第 5 页获取到 100 条记录
2025-06-02 12:00:19,340 - INFO - Request Parameters - Page 6:
2025-06-02 12:00:19,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:19,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:19,833 - INFO - Response - Page 6:
2025-06-02 12:00:20,045 - INFO - 第 6 页获取到 100 条记录
2025-06-02 12:00:20,045 - INFO - Request Parameters - Page 7:
2025-06-02 12:00:20,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:20,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:20,460 - INFO - Response - Page 7:
2025-06-02 12:00:20,663 - INFO - 第 7 页获取到 61 条记录
2025-06-02 12:00:20,663 - INFO - 查询完成，共获取到 661 条记录
2025-06-02 12:00:20,663 - INFO - 获取到 661 条表单数据
2025-06-02 12:00:20,676 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-02 12:00:20,689 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 12:00:20,689 - INFO - 开始处理日期: 2025-04
2025-06-02 12:00:20,689 - INFO - Request Parameters - Page 1:
2025-06-02 12:00:20,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:20,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:21,322 - INFO - Response - Page 1:
2025-06-02 12:00:21,522 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:00:21,522 - INFO - Request Parameters - Page 2:
2025-06-02 12:00:21,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:21,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:22,045 - INFO - Response - Page 2:
2025-06-02 12:00:22,248 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:00:22,249 - INFO - Request Parameters - Page 3:
2025-06-02 12:00:22,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:22,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:22,801 - INFO - Response - Page 3:
2025-06-02 12:00:23,013 - INFO - 第 3 页获取到 100 条记录
2025-06-02 12:00:23,013 - INFO - Request Parameters - Page 4:
2025-06-02 12:00:23,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:23,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:23,736 - INFO - Response - Page 4:
2025-06-02 12:00:23,946 - INFO - 第 4 页获取到 100 条记录
2025-06-02 12:00:23,946 - INFO - Request Parameters - Page 5:
2025-06-02 12:00:23,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:23,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:24,491 - INFO - Response - Page 5:
2025-06-02 12:00:24,705 - INFO - 第 5 页获取到 100 条记录
2025-06-02 12:00:24,705 - INFO - Request Parameters - Page 6:
2025-06-02 12:00:24,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:24,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:25,185 - INFO - Response - Page 6:
2025-06-02 12:00:25,389 - INFO - 第 6 页获取到 100 条记录
2025-06-02 12:00:25,389 - INFO - Request Parameters - Page 7:
2025-06-02 12:00:25,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:25,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:25,847 - INFO - Response - Page 7:
2025-06-02 12:00:26,058 - INFO - 第 7 页获取到 56 条记录
2025-06-02 12:00:26,058 - INFO - 查询完成，共获取到 656 条记录
2025-06-02 12:00:26,058 - INFO - 获取到 656 条表单数据
2025-06-02 12:00:26,058 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-02 12:00:26,083 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 12:00:26,083 - INFO - 开始处理日期: 2025-05
2025-06-02 12:00:26,083 - INFO - Request Parameters - Page 1:
2025-06-02 12:00:26,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:26,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:26,588 - INFO - Response - Page 1:
2025-06-02 12:00:26,788 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:00:26,788 - INFO - Request Parameters - Page 2:
2025-06-02 12:00:26,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:26,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:27,253 - INFO - Response - Page 2:
2025-06-02 12:00:27,470 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:00:27,470 - INFO - Request Parameters - Page 3:
2025-06-02 12:00:27,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:27,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:28,019 - INFO - Response - Page 3:
2025-06-02 12:00:28,221 - INFO - 第 3 页获取到 100 条记录
2025-06-02 12:00:28,221 - INFO - Request Parameters - Page 4:
2025-06-02 12:00:28,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:28,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:28,736 - INFO - Response - Page 4:
2025-06-02 12:00:28,940 - INFO - 第 4 页获取到 100 条记录
2025-06-02 12:00:28,940 - INFO - Request Parameters - Page 5:
2025-06-02 12:00:28,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:28,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:29,394 - INFO - Response - Page 5:
2025-06-02 12:00:29,597 - INFO - 第 5 页获取到 100 条记录
2025-06-02 12:00:29,597 - INFO - Request Parameters - Page 6:
2025-06-02 12:00:29,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:29,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:30,186 - INFO - Response - Page 6:
2025-06-02 12:00:30,386 - INFO - 第 6 页获取到 100 条记录
2025-06-02 12:00:30,386 - INFO - Request Parameters - Page 7:
2025-06-02 12:00:30,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:30,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:30,788 - INFO - Response - Page 7:
2025-06-02 12:00:30,989 - INFO - 第 7 页获取到 36 条记录
2025-06-02 12:00:30,989 - INFO - 查询完成，共获取到 636 条记录
2025-06-02 12:00:30,989 - INFO - 获取到 636 条表单数据
2025-06-02 12:00:31,002 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-02 12:00:31,002 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-06-02 12:00:31,490 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-06-02 12:00:31,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233860.74, 'new_value': 244736.02}, {'field': 'total_amount', 'old_value': 233860.74, 'new_value': 244736.02}, {'field': 'order_count', 'old_value': 8567, 'new_value': 8845}]
2025-06-02 12:00:31,490 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-06-02 12:00:32,013 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-06-02 12:00:32,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 135922.0, 'new_value': 139329.0}, {'field': 'offline_amount', 'old_value': 165378.28, 'new_value': 170862.28}, {'field': 'total_amount', 'old_value': 301300.28, 'new_value': 310191.28}, {'field': 'order_count', 'old_value': 6416, 'new_value': 6593}]
2025-06-02 12:00:32,013 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-06-02 12:00:32,438 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-06-02 12:00:32,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29028.4, 'new_value': 29912.4}, {'field': 'total_amount', 'old_value': 30020.4, 'new_value': 30904.4}, {'field': 'order_count', 'old_value': 3084, 'new_value': 3156}]
2025-06-02 12:00:32,439 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-06-02 12:00:33,033 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-06-02 12:00:33,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61168.0, 'new_value': 67061.0}, {'field': 'offline_amount', 'old_value': 204031.98, 'new_value': 223573.98}, {'field': 'total_amount', 'old_value': 265199.98, 'new_value': 290634.98}, {'field': 'order_count', 'old_value': 1829, 'new_value': 1983}]
2025-06-02 12:00:33,033 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-06-02 12:00:33,474 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-06-02 12:00:33,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30319.36, 'new_value': 33289.5}, {'field': 'offline_amount', 'old_value': 125383.18, 'new_value': 132627.38}, {'field': 'total_amount', 'old_value': 155702.54, 'new_value': 165916.88}, {'field': 'order_count', 'old_value': 2140, 'new_value': 2293}]
2025-06-02 12:00:33,475 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-06-02 12:00:33,942 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-06-02 12:00:33,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33238.46, 'new_value': 35629.41}, {'field': 'offline_amount', 'old_value': 24885.61, 'new_value': 26258.52}, {'field': 'total_amount', 'old_value': 58124.07, 'new_value': 61887.93}, {'field': 'order_count', 'old_value': 3352, 'new_value': 3596}]
2025-06-02 12:00:33,942 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-06-02 12:00:34,381 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-06-02 12:00:34,382 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6128.34, 'new_value': 6471.94}, {'field': 'offline_amount', 'old_value': 140934.12, 'new_value': 148142.26}, {'field': 'total_amount', 'old_value': 147062.46, 'new_value': 154614.2}, {'field': 'order_count', 'old_value': 2363, 'new_value': 2503}]
2025-06-02 12:00:34,382 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-06-02 12:00:34,812 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-06-02 12:00:34,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88853.78, 'new_value': 94484.65}, {'field': 'total_amount', 'old_value': 88853.78, 'new_value': 94484.65}, {'field': 'order_count', 'old_value': 4684, 'new_value': 4982}]
2025-06-02 12:00:34,813 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-06-02 12:00:35,246 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-06-02 12:00:35,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61014.6, 'new_value': 64797.9}, {'field': 'total_amount', 'old_value': 61014.6, 'new_value': 64797.9}, {'field': 'order_count', 'old_value': 265, 'new_value': 289}]
2025-06-02 12:00:35,246 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-06-02 12:00:35,668 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-06-02 12:00:35,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87622.0, 'new_value': 96008.0}, {'field': 'total_amount', 'old_value': 87622.0, 'new_value': 96008.0}, {'field': 'order_count', 'old_value': 4958, 'new_value': 5384}]
2025-06-02 12:00:35,668 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-06-02 12:00:36,105 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-06-02 12:00:36,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119921.25, 'new_value': 132499.08}, {'field': 'total_amount', 'old_value': 119921.25, 'new_value': 132499.08}, {'field': 'order_count', 'old_value': 593, 'new_value': 650}]
2025-06-02 12:00:36,105 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-06-02 12:00:36,565 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-06-02 12:00:36,565 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 456, 'new_value': 469}]
2025-06-02 12:00:36,566 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-06-02 12:00:36,995 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-06-02 12:00:36,995 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-06-02 12:00:36,995 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-06-02 12:00:37,417 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-06-02 12:00:37,417 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12698.4, 'new_value': 12791.4}, {'field': 'offline_amount', 'old_value': 42058.0, 'new_value': 42658.5}, {'field': 'total_amount', 'old_value': 54756.4, 'new_value': 55449.9}, {'field': 'order_count', 'old_value': 65, 'new_value': 69}]
2025-06-02 12:00:37,417 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-06-02 12:00:37,886 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-06-02 12:00:37,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108110.73, 'new_value': 120054.15}, {'field': 'total_amount', 'old_value': 179418.61, 'new_value': 191362.03}, {'field': 'order_count', 'old_value': 4963, 'new_value': 5265}]
2025-06-02 12:00:37,886 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-06-02 12:00:38,356 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-06-02 12:00:38,356 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13278.0, 'new_value': 14288.74}, {'field': 'offline_amount', 'old_value': 72725.32, 'new_value': 76801.1}, {'field': 'total_amount', 'old_value': 86003.32, 'new_value': 91089.84}, {'field': 'order_count', 'old_value': 1988, 'new_value': 2096}]
2025-06-02 12:00:38,356 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-06-02 12:00:38,840 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-06-02 12:00:38,840 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75956.0, 'new_value': 80256.0}, {'field': 'total_amount', 'old_value': 75956.0, 'new_value': 80256.0}, {'field': 'order_count', 'old_value': 14943, 'new_value': 15819}]
2025-06-02 12:00:38,840 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-06-02 12:00:39,337 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-06-02 12:00:39,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112581.0, 'new_value': 119031.0}, {'field': 'total_amount', 'old_value': 112581.0, 'new_value': 119031.0}, {'field': 'order_count', 'old_value': 14943, 'new_value': 15819}]
2025-06-02 12:00:39,338 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-06-02 12:00:39,933 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-06-02 12:00:39,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114547.0, 'new_value': 119792.0}, {'field': 'total_amount', 'old_value': 114547.0, 'new_value': 119792.0}, {'field': 'order_count', 'old_value': 280, 'new_value': 296}]
2025-06-02 12:00:39,933 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-06-02 12:00:40,371 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-06-02 12:00:40,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41897.0, 'new_value': 43224.0}, {'field': 'total_amount', 'old_value': 41897.0, 'new_value': 43224.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 139}]
2025-06-02 12:00:40,371 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-06-02 12:00:40,808 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-06-02 12:00:40,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2120.5, 'new_value': 2431.5}, {'field': 'offline_amount', 'old_value': 59511.6, 'new_value': 65014.6}, {'field': 'total_amount', 'old_value': 61632.1, 'new_value': 67446.1}, {'field': 'order_count', 'old_value': 383, 'new_value': 420}]
2025-06-02 12:00:40,809 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA12CKVOE62735DBBS3ITMX3ODLPMVAMO5
2025-06-02 12:00:41,263 - INFO - 更新表单数据成功: FINST-F7D66UA12CKVOE62735DBBS3ITMX3ODLPMVAMO5
2025-06-02 12:00:41,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2892.38, 'new_value': 5196.38}, {'field': 'total_amount', 'old_value': 2892.38, 'new_value': 5196.38}, {'field': 'order_count', 'old_value': 589, 'new_value': 1035}]
2025-06-02 12:00:41,263 - INFO - 日期 2025-05 处理完成 - 更新: 22 条，插入: 0 条，错误: 0 条
2025-06-02 12:00:41,263 - INFO - 开始处理日期: 2025-06
2025-06-02 12:00:41,263 - INFO - Request Parameters - Page 1:
2025-06-02 12:00:41,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:41,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:41,757 - INFO - Response - Page 1:
2025-06-02 12:00:41,957 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:00:41,957 - INFO - Request Parameters - Page 2:
2025-06-02 12:00:41,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:41,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:42,481 - INFO - Response - Page 2:
2025-06-02 12:00:42,684 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:00:42,684 - INFO - Request Parameters - Page 3:
2025-06-02 12:00:42,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:00:42,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:00:43,245 - INFO - Response - Page 3:
2025-06-02 12:00:43,450 - INFO - 第 3 页获取到 80 条记录
2025-06-02 12:00:43,450 - INFO - 查询完成，共获取到 280 条记录
2025-06-02 12:00:43,451 - INFO - 获取到 280 条表单数据
2025-06-02 12:00:43,458 - INFO - 当前日期 2025-06 有 462 条MySQL数据需要处理
2025-06-02 12:00:43,458 - INFO - 开始更新记录 - 表单实例ID: FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMP21
2025-06-02 12:00:44,001 - INFO - 更新表单数据成功: FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMP21
2025-06-02 12:00:44,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46052.0, 'new_value': 54727.0}, {'field': 'total_amount', 'old_value': 46052.0, 'new_value': 54727.0}, {'field': 'order_count', 'old_value': 288, 'new_value': 356}]
2025-06-02 12:00:44,007 - INFO - 开始批量插入 182 条新记录
2025-06-02 12:00:44,277 - INFO - 批量插入响应状态码: 200
2025-06-02 12:00:44,277 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 04:00:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4912', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FACF518F-BD3E-7D45-B638-77E214403CD3', 'x-acs-trace-id': '288fd6a6a1dd0641c5520242abc42d89', 'etag': '4nLfP5VO3jtoZvJE8V/qOyg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 12:00:44,277 - INFO - 批量插入响应体: {'result': ['FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMIC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMJC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMNC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMED1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMID1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMJD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMND1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFE1']}
2025-06-02 12:00:44,277 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-06-02 12:00:44,277 - INFO - 成功插入的数据ID: ['FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZB1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9C1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMIC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMJC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMNC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZC1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9D1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMED1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMID1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMJD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMND1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZD1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9E1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEE1', 'FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFE1']
2025-06-02 12:00:47,497 - INFO - 批量插入响应状态码: 200
2025-06-02 12:00:47,497 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 04:00:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3948', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5971A028-7809-7EAA-9559-13E75C98B269', 'x-acs-trace-id': '31e778acbc31f0a47b1c19bc4f9516d1', 'etag': '3ZzwsSVO7pzkVqo2iwkN0fQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 12:00:47,497 - INFO - 批量插入响应体: {'result': ['FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMBO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMKO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMQO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMRO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMSO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMWO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMXO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMBP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMKP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMQP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMRP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMSP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMWP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMXP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9Q']}
2025-06-02 12:00:47,513 - INFO - 批量插入表单数据成功，批次 2，共 82 条记录
2025-06-02 12:00:47,513 - INFO - 成功插入的数据ID: ['FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9O', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMBO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMKO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMQO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMRO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMSO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMWO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMXO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZO', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9P', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMBP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMKP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMQP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMRP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMSP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMWP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMXP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZP', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8Q', 'FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9Q']
2025-06-02 12:00:50,524 - INFO - 批量插入完成，共 182 条记录
2025-06-02 12:00:50,524 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 182 条，错误: 0 条
2025-06-02 12:00:50,524 - INFO - 数据同步完成！更新: 23 条，插入: 182 条，错误: 0 条
2025-06-02 12:00:50,524 - INFO - =================同步完成====================
2025-06-02 15:00:02,792 - INFO - =================使用默认全量同步=============
2025-06-02 15:00:04,339 - INFO - MySQL查询成功，共获取 3774 条记录
2025-06-02 15:00:04,339 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-02 15:00:04,371 - INFO - 开始处理日期: 2025-01
2025-06-02 15:00:04,371 - INFO - Request Parameters - Page 1:
2025-06-02 15:00:04,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:04,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:05,652 - INFO - Response - Page 1:
2025-06-02 15:00:05,855 - INFO - 第 1 页获取到 100 条记录
2025-06-02 15:00:05,855 - INFO - Request Parameters - Page 2:
2025-06-02 15:00:05,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:05,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:06,480 - INFO - Response - Page 2:
2025-06-02 15:00:06,683 - INFO - 第 2 页获取到 100 条记录
2025-06-02 15:00:06,683 - INFO - Request Parameters - Page 3:
2025-06-02 15:00:06,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:06,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:07,215 - INFO - Response - Page 3:
2025-06-02 15:00:07,418 - INFO - 第 3 页获取到 100 条记录
2025-06-02 15:00:07,418 - INFO - Request Parameters - Page 4:
2025-06-02 15:00:07,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:07,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:07,949 - INFO - Response - Page 4:
2025-06-02 15:00:08,152 - INFO - 第 4 页获取到 100 条记录
2025-06-02 15:00:08,152 - INFO - Request Parameters - Page 5:
2025-06-02 15:00:08,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:08,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:08,621 - INFO - Response - Page 5:
2025-06-02 15:00:08,824 - INFO - 第 5 页获取到 100 条记录
2025-06-02 15:00:08,824 - INFO - Request Parameters - Page 6:
2025-06-02 15:00:08,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:08,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:09,450 - INFO - Response - Page 6:
2025-06-02 15:00:09,653 - INFO - 第 6 页获取到 100 条记录
2025-06-02 15:00:09,653 - INFO - Request Parameters - Page 7:
2025-06-02 15:00:09,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:09,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:10,200 - INFO - Response - Page 7:
2025-06-02 15:00:10,403 - INFO - 第 7 页获取到 82 条记录
2025-06-02 15:00:10,403 - INFO - 查询完成，共获取到 682 条记录
2025-06-02 15:00:10,403 - INFO - 获取到 682 条表单数据
2025-06-02 15:00:10,403 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-02 15:00:10,418 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 15:00:10,418 - INFO - 开始处理日期: 2025-02
2025-06-02 15:00:10,418 - INFO - Request Parameters - Page 1:
2025-06-02 15:00:10,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:10,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:10,965 - INFO - Response - Page 1:
2025-06-02 15:00:11,169 - INFO - 第 1 页获取到 100 条记录
2025-06-02 15:00:11,169 - INFO - Request Parameters - Page 2:
2025-06-02 15:00:11,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:11,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:11,669 - INFO - Response - Page 2:
2025-06-02 15:00:11,872 - INFO - 第 2 页获取到 100 条记录
2025-06-02 15:00:11,872 - INFO - Request Parameters - Page 3:
2025-06-02 15:00:11,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:11,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:12,372 - INFO - Response - Page 3:
2025-06-02 15:00:12,575 - INFO - 第 3 页获取到 100 条记录
2025-06-02 15:00:12,575 - INFO - Request Parameters - Page 4:
2025-06-02 15:00:12,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:12,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:13,075 - INFO - Response - Page 4:
2025-06-02 15:00:13,278 - INFO - 第 4 页获取到 100 条记录
2025-06-02 15:00:13,278 - INFO - Request Parameters - Page 5:
2025-06-02 15:00:13,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:13,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:13,794 - INFO - Response - Page 5:
2025-06-02 15:00:13,997 - INFO - 第 5 页获取到 100 条记录
2025-06-02 15:00:13,997 - INFO - Request Parameters - Page 6:
2025-06-02 15:00:13,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:13,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:14,544 - INFO - Response - Page 6:
2025-06-02 15:00:14,747 - INFO - 第 6 页获取到 100 条记录
2025-06-02 15:00:14,747 - INFO - Request Parameters - Page 7:
2025-06-02 15:00:14,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:14,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:15,185 - INFO - Response - Page 7:
2025-06-02 15:00:15,388 - INFO - 第 7 页获取到 70 条记录
2025-06-02 15:00:15,388 - INFO - 查询完成，共获取到 670 条记录
2025-06-02 15:00:15,388 - INFO - 获取到 670 条表单数据
2025-06-02 15:00:15,388 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-02 15:00:15,404 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 15:00:15,404 - INFO - 开始处理日期: 2025-03
2025-06-02 15:00:15,404 - INFO - Request Parameters - Page 1:
2025-06-02 15:00:15,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:15,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:16,169 - INFO - Response - Page 1:
2025-06-02 15:00:16,373 - INFO - 第 1 页获取到 100 条记录
2025-06-02 15:00:16,373 - INFO - Request Parameters - Page 2:
2025-06-02 15:00:16,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:16,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:16,920 - INFO - Response - Page 2:
2025-06-02 15:00:17,123 - INFO - 第 2 页获取到 100 条记录
2025-06-02 15:00:17,123 - INFO - Request Parameters - Page 3:
2025-06-02 15:00:17,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:17,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:17,638 - INFO - Response - Page 3:
2025-06-02 15:00:17,842 - INFO - 第 3 页获取到 100 条记录
2025-06-02 15:00:17,842 - INFO - Request Parameters - Page 4:
2025-06-02 15:00:17,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:17,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:18,388 - INFO - Response - Page 4:
2025-06-02 15:00:18,592 - INFO - 第 4 页获取到 100 条记录
2025-06-02 15:00:18,592 - INFO - Request Parameters - Page 5:
2025-06-02 15:00:18,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:18,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:19,139 - INFO - Response - Page 5:
2025-06-02 15:00:19,342 - INFO - 第 5 页获取到 100 条记录
2025-06-02 15:00:19,342 - INFO - Request Parameters - Page 6:
2025-06-02 15:00:19,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:19,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:19,951 - INFO - Response - Page 6:
2025-06-02 15:00:20,154 - INFO - 第 6 页获取到 100 条记录
2025-06-02 15:00:20,154 - INFO - Request Parameters - Page 7:
2025-06-02 15:00:20,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:20,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:20,654 - INFO - Response - Page 7:
2025-06-02 15:00:20,858 - INFO - 第 7 页获取到 61 条记录
2025-06-02 15:00:20,858 - INFO - 查询完成，共获取到 661 条记录
2025-06-02 15:00:20,858 - INFO - 获取到 661 条表单数据
2025-06-02 15:00:20,858 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-02 15:00:20,873 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 15:00:20,873 - INFO - 开始处理日期: 2025-04
2025-06-02 15:00:20,873 - INFO - Request Parameters - Page 1:
2025-06-02 15:00:20,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:20,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:21,389 - INFO - Response - Page 1:
2025-06-02 15:00:21,592 - INFO - 第 1 页获取到 100 条记录
2025-06-02 15:00:21,592 - INFO - Request Parameters - Page 2:
2025-06-02 15:00:21,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:21,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:22,092 - INFO - Response - Page 2:
2025-06-02 15:00:22,295 - INFO - 第 2 页获取到 100 条记录
2025-06-02 15:00:22,295 - INFO - Request Parameters - Page 3:
2025-06-02 15:00:22,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:22,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:22,764 - INFO - Response - Page 3:
2025-06-02 15:00:22,967 - INFO - 第 3 页获取到 100 条记录
2025-06-02 15:00:22,967 - INFO - Request Parameters - Page 4:
2025-06-02 15:00:22,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:22,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:23,436 - INFO - Response - Page 4:
2025-06-02 15:00:23,639 - INFO - 第 4 页获取到 100 条记录
2025-06-02 15:00:23,639 - INFO - Request Parameters - Page 5:
2025-06-02 15:00:23,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:23,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:24,171 - INFO - Response - Page 5:
2025-06-02 15:00:24,374 - INFO - 第 5 页获取到 100 条记录
2025-06-02 15:00:24,374 - INFO - Request Parameters - Page 6:
2025-06-02 15:00:24,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:24,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:24,858 - INFO - Response - Page 6:
2025-06-02 15:00:25,061 - INFO - 第 6 页获取到 100 条记录
2025-06-02 15:00:25,061 - INFO - Request Parameters - Page 7:
2025-06-02 15:00:25,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:25,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:25,468 - INFO - Response - Page 7:
2025-06-02 15:00:25,671 - INFO - 第 7 页获取到 56 条记录
2025-06-02 15:00:25,671 - INFO - 查询完成，共获取到 656 条记录
2025-06-02 15:00:25,671 - INFO - 获取到 656 条表单数据
2025-06-02 15:00:25,671 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-02 15:00:25,687 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 15:00:25,687 - INFO - 开始处理日期: 2025-05
2025-06-02 15:00:25,687 - INFO - Request Parameters - Page 1:
2025-06-02 15:00:25,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:25,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:26,280 - INFO - Response - Page 1:
2025-06-02 15:00:26,484 - INFO - 第 1 页获取到 100 条记录
2025-06-02 15:00:26,484 - INFO - Request Parameters - Page 2:
2025-06-02 15:00:26,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:26,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:26,984 - INFO - Response - Page 2:
2025-06-02 15:00:27,187 - INFO - 第 2 页获取到 100 条记录
2025-06-02 15:00:27,187 - INFO - Request Parameters - Page 3:
2025-06-02 15:00:27,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:27,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:27,812 - INFO - Response - Page 3:
2025-06-02 15:00:28,015 - INFO - 第 3 页获取到 100 条记录
2025-06-02 15:00:28,015 - INFO - Request Parameters - Page 4:
2025-06-02 15:00:28,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:28,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:28,515 - INFO - Response - Page 4:
2025-06-02 15:00:28,718 - INFO - 第 4 页获取到 100 条记录
2025-06-02 15:00:28,718 - INFO - Request Parameters - Page 5:
2025-06-02 15:00:28,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:28,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:29,312 - INFO - Response - Page 5:
2025-06-02 15:00:29,515 - INFO - 第 5 页获取到 100 条记录
2025-06-02 15:00:29,515 - INFO - Request Parameters - Page 6:
2025-06-02 15:00:29,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:29,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:29,968 - INFO - Response - Page 6:
2025-06-02 15:00:30,172 - INFO - 第 6 页获取到 100 条记录
2025-06-02 15:00:30,172 - INFO - Request Parameters - Page 7:
2025-06-02 15:00:30,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:30,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:30,594 - INFO - Response - Page 7:
2025-06-02 15:00:30,797 - INFO - 第 7 页获取到 36 条记录
2025-06-02 15:00:30,797 - INFO - 查询完成，共获取到 636 条记录
2025-06-02 15:00:30,797 - INFO - 获取到 636 条表单数据
2025-06-02 15:00:30,797 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-02 15:00:30,812 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-06-02 15:00:31,391 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-06-02 15:00:31,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141046.0, 'new_value': 143634.0}, {'field': 'total_amount', 'old_value': 141126.0, 'new_value': 143714.0}, {'field': 'order_count', 'old_value': 14092, 'new_value': 14093}]
2025-06-02 15:00:31,391 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-06-02 15:00:31,797 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-06-02 15:00:31,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69929.0, 'new_value': 70605.0}, {'field': 'total_amount', 'old_value': 92130.0, 'new_value': 92806.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 160}]
2025-06-02 15:00:31,797 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-06-02 15:00:32,281 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-06-02 15:00:32,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20744697.36, 'new_value': 21409845.22}, {'field': 'total_amount', 'old_value': 20744697.36, 'new_value': 21409845.22}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-02 15:00:32,281 - INFO - 日期 2025-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-02 15:00:32,281 - INFO - 开始处理日期: 2025-06
2025-06-02 15:00:32,281 - INFO - Request Parameters - Page 1:
2025-06-02 15:00:32,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:32,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:32,797 - INFO - Response - Page 1:
2025-06-02 15:00:33,000 - INFO - 第 1 页获取到 100 条记录
2025-06-02 15:00:33,000 - INFO - Request Parameters - Page 2:
2025-06-02 15:00:33,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:33,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:33,547 - INFO - Response - Page 2:
2025-06-02 15:00:33,750 - INFO - 第 2 页获取到 100 条记录
2025-06-02 15:00:33,750 - INFO - Request Parameters - Page 3:
2025-06-02 15:00:33,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:33,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:34,579 - INFO - Response - Page 3:
2025-06-02 15:00:34,782 - INFO - 第 3 页获取到 100 条记录
2025-06-02 15:00:34,782 - INFO - Request Parameters - Page 4:
2025-06-02 15:00:34,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:34,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:35,313 - INFO - Response - Page 4:
2025-06-02 15:00:35,516 - INFO - 第 4 页获取到 100 条记录
2025-06-02 15:00:35,516 - INFO - Request Parameters - Page 5:
2025-06-02 15:00:35,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:00:35,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:00:36,126 - INFO - Response - Page 5:
2025-06-02 15:00:36,329 - INFO - 第 5 页获取到 62 条记录
2025-06-02 15:00:36,329 - INFO - 查询完成，共获取到 462 条记录
2025-06-02 15:00:36,329 - INFO - 获取到 462 条表单数据
2025-06-02 15:00:36,329 - INFO - 当前日期 2025-06 有 469 条MySQL数据需要处理
2025-06-02 15:00:36,345 - INFO - 开始批量插入 7 条新记录
2025-06-02 15:00:36,501 - INFO - 批量插入响应状态码: 200
2025-06-02 15:00:36,516 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 07:00:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7EE2F558-ED4B-70B4-8584-EE62F540DA7B', 'x-acs-trace-id': '0e1b3d1c02f6977be148d2895e6a41bb', 'etag': '3R3oKmbQp5z9x9+D5QaWcjA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 15:00:36,516 - INFO - 批量插入响应体: {'result': ['FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMJK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMKK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMLK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMMK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMNK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMOK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMPK']}
2025-06-02 15:00:36,516 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-06-02 15:00:36,516 - INFO - 成功插入的数据ID: ['FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMJK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMKK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMLK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMMK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMNK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMOK', 'FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMPK']
2025-06-02 15:00:39,533 - INFO - 批量插入完成，共 7 条记录
2025-06-02 15:00:39,533 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-06-02 15:00:39,533 - INFO - 数据同步完成！更新: 3 条，插入: 7 条，错误: 0 条
2025-06-02 15:00:39,533 - INFO - =================同步完成====================
2025-06-02 18:00:02,318 - INFO - =================使用默认全量同步=============
2025-06-02 18:00:03,865 - INFO - MySQL查询成功，共获取 3818 条记录
2025-06-02 18:00:03,865 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-02 18:00:03,897 - INFO - 开始处理日期: 2025-01
2025-06-02 18:00:03,897 - INFO - Request Parameters - Page 1:
2025-06-02 18:00:03,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:03,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:05,256 - INFO - Response - Page 1:
2025-06-02 18:00:05,459 - INFO - 第 1 页获取到 100 条记录
2025-06-02 18:00:05,459 - INFO - Request Parameters - Page 2:
2025-06-02 18:00:05,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:05,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:05,959 - INFO - Response - Page 2:
2025-06-02 18:00:06,162 - INFO - 第 2 页获取到 100 条记录
2025-06-02 18:00:06,162 - INFO - Request Parameters - Page 3:
2025-06-02 18:00:06,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:06,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:06,693 - INFO - Response - Page 3:
2025-06-02 18:00:06,897 - INFO - 第 3 页获取到 100 条记录
2025-06-02 18:00:06,897 - INFO - Request Parameters - Page 4:
2025-06-02 18:00:06,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:06,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:07,428 - INFO - Response - Page 4:
2025-06-02 18:00:07,631 - INFO - 第 4 页获取到 100 条记录
2025-06-02 18:00:07,631 - INFO - Request Parameters - Page 5:
2025-06-02 18:00:07,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:07,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:08,100 - INFO - Response - Page 5:
2025-06-02 18:00:08,303 - INFO - 第 5 页获取到 100 条记录
2025-06-02 18:00:08,303 - INFO - Request Parameters - Page 6:
2025-06-02 18:00:08,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:08,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:08,818 - INFO - Response - Page 6:
2025-06-02 18:00:09,022 - INFO - 第 6 页获取到 100 条记录
2025-06-02 18:00:09,022 - INFO - Request Parameters - Page 7:
2025-06-02 18:00:09,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:09,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:09,631 - INFO - Response - Page 7:
2025-06-02 18:00:09,834 - INFO - 第 7 页获取到 82 条记录
2025-06-02 18:00:09,834 - INFO - 查询完成，共获取到 682 条记录
2025-06-02 18:00:09,834 - INFO - 获取到 682 条表单数据
2025-06-02 18:00:09,834 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-02 18:00:09,850 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 18:00:09,850 - INFO - 开始处理日期: 2025-02
2025-06-02 18:00:09,850 - INFO - Request Parameters - Page 1:
2025-06-02 18:00:09,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:09,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:10,365 - INFO - Response - Page 1:
2025-06-02 18:00:10,568 - INFO - 第 1 页获取到 100 条记录
2025-06-02 18:00:10,568 - INFO - Request Parameters - Page 2:
2025-06-02 18:00:10,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:10,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:11,115 - INFO - Response - Page 2:
2025-06-02 18:00:11,318 - INFO - 第 2 页获取到 100 条记录
2025-06-02 18:00:11,318 - INFO - Request Parameters - Page 3:
2025-06-02 18:00:11,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:11,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:11,803 - INFO - Response - Page 3:
2025-06-02 18:00:12,006 - INFO - 第 3 页获取到 100 条记录
2025-06-02 18:00:12,006 - INFO - Request Parameters - Page 4:
2025-06-02 18:00:12,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:12,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:12,490 - INFO - Response - Page 4:
2025-06-02 18:00:12,693 - INFO - 第 4 页获取到 100 条记录
2025-06-02 18:00:12,693 - INFO - Request Parameters - Page 5:
2025-06-02 18:00:12,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:12,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:13,209 - INFO - Response - Page 5:
2025-06-02 18:00:13,412 - INFO - 第 5 页获取到 100 条记录
2025-06-02 18:00:13,412 - INFO - Request Parameters - Page 6:
2025-06-02 18:00:13,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:13,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:13,897 - INFO - Response - Page 6:
2025-06-02 18:00:14,100 - INFO - 第 6 页获取到 100 条记录
2025-06-02 18:00:14,100 - INFO - Request Parameters - Page 7:
2025-06-02 18:00:14,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:14,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:14,553 - INFO - Response - Page 7:
2025-06-02 18:00:14,756 - INFO - 第 7 页获取到 70 条记录
2025-06-02 18:00:14,756 - INFO - 查询完成，共获取到 670 条记录
2025-06-02 18:00:14,756 - INFO - 获取到 670 条表单数据
2025-06-02 18:00:14,756 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-02 18:00:14,772 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 18:00:14,772 - INFO - 开始处理日期: 2025-03
2025-06-02 18:00:14,772 - INFO - Request Parameters - Page 1:
2025-06-02 18:00:14,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:14,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:15,334 - INFO - Response - Page 1:
2025-06-02 18:00:15,537 - INFO - 第 1 页获取到 100 条记录
2025-06-02 18:00:15,537 - INFO - Request Parameters - Page 2:
2025-06-02 18:00:15,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:15,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:16,084 - INFO - Response - Page 2:
2025-06-02 18:00:16,287 - INFO - 第 2 页获取到 100 条记录
2025-06-02 18:00:16,287 - INFO - Request Parameters - Page 3:
2025-06-02 18:00:16,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:16,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:16,865 - INFO - Response - Page 3:
2025-06-02 18:00:17,068 - INFO - 第 3 页获取到 100 条记录
2025-06-02 18:00:17,068 - INFO - Request Parameters - Page 4:
2025-06-02 18:00:17,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:17,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:17,709 - INFO - Response - Page 4:
2025-06-02 18:00:17,912 - INFO - 第 4 页获取到 100 条记录
2025-06-02 18:00:17,912 - INFO - Request Parameters - Page 5:
2025-06-02 18:00:17,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:17,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:18,443 - INFO - Response - Page 5:
2025-06-02 18:00:18,646 - INFO - 第 5 页获取到 100 条记录
2025-06-02 18:00:18,646 - INFO - Request Parameters - Page 6:
2025-06-02 18:00:18,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:18,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:19,146 - INFO - Response - Page 6:
2025-06-02 18:00:19,350 - INFO - 第 6 页获取到 100 条记录
2025-06-02 18:00:19,350 - INFO - Request Parameters - Page 7:
2025-06-02 18:00:19,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:19,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:19,771 - INFO - Response - Page 7:
2025-06-02 18:00:19,975 - INFO - 第 7 页获取到 61 条记录
2025-06-02 18:00:19,975 - INFO - 查询完成，共获取到 661 条记录
2025-06-02 18:00:19,975 - INFO - 获取到 661 条表单数据
2025-06-02 18:00:19,975 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-02 18:00:19,990 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 18:00:19,990 - INFO - 开始处理日期: 2025-04
2025-06-02 18:00:19,990 - INFO - Request Parameters - Page 1:
2025-06-02 18:00:19,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:19,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:20,475 - INFO - Response - Page 1:
2025-06-02 18:00:20,678 - INFO - 第 1 页获取到 100 条记录
2025-06-02 18:00:20,678 - INFO - Request Parameters - Page 2:
2025-06-02 18:00:20,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:20,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:21,225 - INFO - Response - Page 2:
2025-06-02 18:00:21,443 - INFO - 第 2 页获取到 100 条记录
2025-06-02 18:00:21,443 - INFO - Request Parameters - Page 3:
2025-06-02 18:00:21,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:21,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:21,943 - INFO - Response - Page 3:
2025-06-02 18:00:22,146 - INFO - 第 3 页获取到 100 条记录
2025-06-02 18:00:22,146 - INFO - Request Parameters - Page 4:
2025-06-02 18:00:22,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:22,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:22,662 - INFO - Response - Page 4:
2025-06-02 18:00:22,865 - INFO - 第 4 页获取到 100 条记录
2025-06-02 18:00:22,865 - INFO - Request Parameters - Page 5:
2025-06-02 18:00:22,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:22,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:23,365 - INFO - Response - Page 5:
2025-06-02 18:00:23,568 - INFO - 第 5 页获取到 100 条记录
2025-06-02 18:00:23,568 - INFO - Request Parameters - Page 6:
2025-06-02 18:00:23,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:23,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:24,068 - INFO - Response - Page 6:
2025-06-02 18:00:24,271 - INFO - 第 6 页获取到 100 条记录
2025-06-02 18:00:24,271 - INFO - Request Parameters - Page 7:
2025-06-02 18:00:24,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:24,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:24,662 - INFO - Response - Page 7:
2025-06-02 18:00:24,865 - INFO - 第 7 页获取到 56 条记录
2025-06-02 18:00:24,865 - INFO - 查询完成，共获取到 656 条记录
2025-06-02 18:00:24,865 - INFO - 获取到 656 条表单数据
2025-06-02 18:00:24,865 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-02 18:00:24,881 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 18:00:24,881 - INFO - 开始处理日期: 2025-05
2025-06-02 18:00:24,881 - INFO - Request Parameters - Page 1:
2025-06-02 18:00:24,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:24,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:25,428 - INFO - Response - Page 1:
2025-06-02 18:00:25,631 - INFO - 第 1 页获取到 100 条记录
2025-06-02 18:00:25,631 - INFO - Request Parameters - Page 2:
2025-06-02 18:00:25,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:25,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:26,178 - INFO - Response - Page 2:
2025-06-02 18:00:26,381 - INFO - 第 2 页获取到 100 条记录
2025-06-02 18:00:26,381 - INFO - Request Parameters - Page 3:
2025-06-02 18:00:26,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:26,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:26,865 - INFO - Response - Page 3:
2025-06-02 18:00:27,068 - INFO - 第 3 页获取到 100 条记录
2025-06-02 18:00:27,068 - INFO - Request Parameters - Page 4:
2025-06-02 18:00:27,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:27,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:27,553 - INFO - Response - Page 4:
2025-06-02 18:00:27,756 - INFO - 第 4 页获取到 100 条记录
2025-06-02 18:00:27,756 - INFO - Request Parameters - Page 5:
2025-06-02 18:00:27,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:27,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:28,271 - INFO - Response - Page 5:
2025-06-02 18:00:28,475 - INFO - 第 5 页获取到 100 条记录
2025-06-02 18:00:28,475 - INFO - Request Parameters - Page 6:
2025-06-02 18:00:28,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:28,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:29,100 - INFO - Response - Page 6:
2025-06-02 18:00:29,303 - INFO - 第 6 页获取到 100 条记录
2025-06-02 18:00:29,303 - INFO - Request Parameters - Page 7:
2025-06-02 18:00:29,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:29,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:29,834 - INFO - Response - Page 7:
2025-06-02 18:00:30,037 - INFO - 第 7 页获取到 36 条记录
2025-06-02 18:00:30,037 - INFO - 查询完成，共获取到 636 条记录
2025-06-02 18:00:30,037 - INFO - 获取到 636 条表单数据
2025-06-02 18:00:30,037 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-02 18:00:30,053 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-06-02 18:00:30,646 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-06-02 18:00:30,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108414.13, 'new_value': 108425.15}, {'field': 'total_amount', 'old_value': 108414.13, 'new_value': 108425.15}, {'field': 'order_count', 'old_value': 4213, 'new_value': 4209}]
2025-06-02 18:00:30,646 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-06-02 18:00:31,084 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-06-02 18:00:31,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145483.63, 'new_value': 143453.11}, {'field': 'total_amount', 'old_value': 157998.94, 'new_value': 155968.42}]
2025-06-02 18:00:31,100 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-06-02 18:00:31,506 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-06-02 18:00:31,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372285.46, 'new_value': 355001.46}, {'field': 'total_amount', 'old_value': 373756.46, 'new_value': 356472.46}, {'field': 'order_count', 'old_value': 70, 'new_value': 62}]
2025-06-02 18:00:31,506 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-06-02 18:00:32,006 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-06-02 18:00:32,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98800.0, 'new_value': 105000.0}, {'field': 'total_amount', 'old_value': 98800.0, 'new_value': 105000.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-02 18:00:32,006 - INFO - 日期 2025-05 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-06-02 18:00:32,006 - INFO - 开始处理日期: 2025-06
2025-06-02 18:00:32,006 - INFO - Request Parameters - Page 1:
2025-06-02 18:00:32,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:32,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:32,506 - INFO - Response - Page 1:
2025-06-02 18:00:32,709 - INFO - 第 1 页获取到 100 条记录
2025-06-02 18:00:32,709 - INFO - Request Parameters - Page 2:
2025-06-02 18:00:32,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:32,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:33,412 - INFO - Response - Page 2:
2025-06-02 18:00:33,615 - INFO - 第 2 页获取到 100 条记录
2025-06-02 18:00:33,615 - INFO - Request Parameters - Page 3:
2025-06-02 18:00:33,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:33,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:34,115 - INFO - Response - Page 3:
2025-06-02 18:00:34,318 - INFO - 第 3 页获取到 100 条记录
2025-06-02 18:00:34,318 - INFO - Request Parameters - Page 4:
2025-06-02 18:00:34,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:34,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:34,834 - INFO - Response - Page 4:
2025-06-02 18:00:35,037 - INFO - 第 4 页获取到 100 条记录
2025-06-02 18:00:35,037 - INFO - Request Parameters - Page 5:
2025-06-02 18:00:35,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:00:35,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:00:35,506 - INFO - Response - Page 5:
2025-06-02 18:00:35,709 - INFO - 第 5 页获取到 69 条记录
2025-06-02 18:00:35,709 - INFO - 查询完成，共获取到 469 条记录
2025-06-02 18:00:35,709 - INFO - 获取到 469 条表单数据
2025-06-02 18:00:35,709 - INFO - 当前日期 2025-06 有 513 条MySQL数据需要处理
2025-06-02 18:00:35,725 - INFO - 开始批量插入 44 条新记录
2025-06-02 18:00:35,959 - INFO - 批量插入响应状态码: 200
2025-06-02 18:00:35,959 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 10:00:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2088', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CAE0E72F-E8D9-7A1C-9BAA-2D1F2BD8F65D', 'x-acs-trace-id': '69fcf3a8fd2a6f80adc4877300cd5690', 'etag': '2uhLsYddlyzXCjuBsYt4ZtQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 18:00:35,959 - INFO - 批量插入响应体: {'result': ['FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM0', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM1', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM2', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM3', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM4', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM5', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM6', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM7', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM8', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM9', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMA', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMB', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMC', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMD', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBME', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMF', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMG', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMH', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMI', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMJ', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMK', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBML', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMM', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMN', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMO', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMP', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMQ', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMR', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMS', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMT', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMU', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMV', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMW', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMX', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMY', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMZ', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM01', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM11', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM21', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM31', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM41', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM51', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM61', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM71']}
2025-06-02 18:00:35,959 - INFO - 批量插入表单数据成功，批次 1，共 44 条记录
2025-06-02 18:00:35,959 - INFO - 成功插入的数据ID: ['FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM0', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM1', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM2', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM3', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM4', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM5', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM6', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM7', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM8', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM9', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMA', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMB', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMC', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMD', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBME', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMF', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMG', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMH', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMI', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMJ', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMK', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBML', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMM', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMN', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMO', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMP', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMQ', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMR', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMS', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMT', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMU', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMV', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMW', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMX', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMY', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMZ', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM01', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM11', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM21', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM31', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM41', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM51', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM61', 'FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM71']
2025-06-02 18:00:38,974 - INFO - 批量插入完成，共 44 条记录
2025-06-02 18:00:38,974 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 44 条，错误: 0 条
2025-06-02 18:00:38,974 - INFO - 数据同步完成！更新: 4 条，插入: 44 条，错误: 0 条
2025-06-02 18:00:38,974 - INFO - =================同步完成====================
2025-06-02 21:00:02,763 - INFO - =================使用默认全量同步=============
2025-06-02 21:00:04,310 - INFO - MySQL查询成功，共获取 3818 条记录
2025-06-02 21:00:04,325 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-02 21:00:04,357 - INFO - 开始处理日期: 2025-01
2025-06-02 21:00:04,357 - INFO - Request Parameters - Page 1:
2025-06-02 21:00:04,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:04,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:05,685 - INFO - Response - Page 1:
2025-06-02 21:00:05,888 - INFO - 第 1 页获取到 100 条记录
2025-06-02 21:00:05,888 - INFO - Request Parameters - Page 2:
2025-06-02 21:00:05,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:05,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:06,419 - INFO - Response - Page 2:
2025-06-02 21:00:06,622 - INFO - 第 2 页获取到 100 条记录
2025-06-02 21:00:06,622 - INFO - Request Parameters - Page 3:
2025-06-02 21:00:06,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:06,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:07,138 - INFO - Response - Page 3:
2025-06-02 21:00:07,341 - INFO - 第 3 页获取到 100 条记录
2025-06-02 21:00:07,341 - INFO - Request Parameters - Page 4:
2025-06-02 21:00:07,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:07,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:07,888 - INFO - Response - Page 4:
2025-06-02 21:00:08,091 - INFO - 第 4 页获取到 100 条记录
2025-06-02 21:00:08,091 - INFO - Request Parameters - Page 5:
2025-06-02 21:00:08,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:08,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:08,685 - INFO - Response - Page 5:
2025-06-02 21:00:08,888 - INFO - 第 5 页获取到 100 条记录
2025-06-02 21:00:08,888 - INFO - Request Parameters - Page 6:
2025-06-02 21:00:08,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:08,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:09,357 - INFO - Response - Page 6:
2025-06-02 21:00:09,560 - INFO - 第 6 页获取到 100 条记录
2025-06-02 21:00:09,560 - INFO - Request Parameters - Page 7:
2025-06-02 21:00:09,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:09,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:10,044 - INFO - Response - Page 7:
2025-06-02 21:00:10,247 - INFO - 第 7 页获取到 82 条记录
2025-06-02 21:00:10,247 - INFO - 查询完成，共获取到 682 条记录
2025-06-02 21:00:10,247 - INFO - 获取到 682 条表单数据
2025-06-02 21:00:10,247 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-02 21:00:10,263 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 21:00:10,263 - INFO - 开始处理日期: 2025-02
2025-06-02 21:00:10,263 - INFO - Request Parameters - Page 1:
2025-06-02 21:00:10,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:10,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:10,794 - INFO - Response - Page 1:
2025-06-02 21:00:10,997 - INFO - 第 1 页获取到 100 条记录
2025-06-02 21:00:10,997 - INFO - Request Parameters - Page 2:
2025-06-02 21:00:10,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:10,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:11,544 - INFO - Response - Page 2:
2025-06-02 21:00:11,747 - INFO - 第 2 页获取到 100 条记录
2025-06-02 21:00:11,747 - INFO - Request Parameters - Page 3:
2025-06-02 21:00:11,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:11,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:12,294 - INFO - Response - Page 3:
2025-06-02 21:00:12,497 - INFO - 第 3 页获取到 100 条记录
2025-06-02 21:00:12,497 - INFO - Request Parameters - Page 4:
2025-06-02 21:00:12,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:12,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:12,982 - INFO - Response - Page 4:
2025-06-02 21:00:13,185 - INFO - 第 4 页获取到 100 条记录
2025-06-02 21:00:13,185 - INFO - Request Parameters - Page 5:
2025-06-02 21:00:13,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:13,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:13,638 - INFO - Response - Page 5:
2025-06-02 21:00:13,841 - INFO - 第 5 页获取到 100 条记录
2025-06-02 21:00:13,841 - INFO - Request Parameters - Page 6:
2025-06-02 21:00:13,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:13,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:14,372 - INFO - Response - Page 6:
2025-06-02 21:00:14,575 - INFO - 第 6 页获取到 100 条记录
2025-06-02 21:00:14,575 - INFO - Request Parameters - Page 7:
2025-06-02 21:00:14,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:14,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:15,075 - INFO - Response - Page 7:
2025-06-02 21:00:15,278 - INFO - 第 7 页获取到 70 条记录
2025-06-02 21:00:15,278 - INFO - 查询完成，共获取到 670 条记录
2025-06-02 21:00:15,278 - INFO - 获取到 670 条表单数据
2025-06-02 21:00:15,278 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-02 21:00:15,294 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 21:00:15,294 - INFO - 开始处理日期: 2025-03
2025-06-02 21:00:15,294 - INFO - Request Parameters - Page 1:
2025-06-02 21:00:15,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:15,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:15,841 - INFO - Response - Page 1:
2025-06-02 21:00:16,044 - INFO - 第 1 页获取到 100 条记录
2025-06-02 21:00:16,044 - INFO - Request Parameters - Page 2:
2025-06-02 21:00:16,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:16,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:16,560 - INFO - Response - Page 2:
2025-06-02 21:00:16,763 - INFO - 第 2 页获取到 100 条记录
2025-06-02 21:00:16,763 - INFO - Request Parameters - Page 3:
2025-06-02 21:00:16,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:16,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:17,372 - INFO - Response - Page 3:
2025-06-02 21:00:17,575 - INFO - 第 3 页获取到 100 条记录
2025-06-02 21:00:17,575 - INFO - Request Parameters - Page 4:
2025-06-02 21:00:17,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:17,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:18,044 - INFO - Response - Page 4:
2025-06-02 21:00:18,247 - INFO - 第 4 页获取到 100 条记录
2025-06-02 21:00:18,247 - INFO - Request Parameters - Page 5:
2025-06-02 21:00:18,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:18,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:18,763 - INFO - Response - Page 5:
2025-06-02 21:00:18,966 - INFO - 第 5 页获取到 100 条记录
2025-06-02 21:00:18,966 - INFO - Request Parameters - Page 6:
2025-06-02 21:00:18,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:18,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:19,528 - INFO - Response - Page 6:
2025-06-02 21:00:19,731 - INFO - 第 6 页获取到 100 条记录
2025-06-02 21:00:19,731 - INFO - Request Parameters - Page 7:
2025-06-02 21:00:19,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:19,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:20,169 - INFO - Response - Page 7:
2025-06-02 21:00:20,372 - INFO - 第 7 页获取到 61 条记录
2025-06-02 21:00:20,372 - INFO - 查询完成，共获取到 661 条记录
2025-06-02 21:00:20,372 - INFO - 获取到 661 条表单数据
2025-06-02 21:00:20,372 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-02 21:00:20,388 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 21:00:20,388 - INFO - 开始处理日期: 2025-04
2025-06-02 21:00:20,388 - INFO - Request Parameters - Page 1:
2025-06-02 21:00:20,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:20,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:20,935 - INFO - Response - Page 1:
2025-06-02 21:00:21,138 - INFO - 第 1 页获取到 100 条记录
2025-06-02 21:00:21,138 - INFO - Request Parameters - Page 2:
2025-06-02 21:00:21,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:21,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:21,731 - INFO - Response - Page 2:
2025-06-02 21:00:21,935 - INFO - 第 2 页获取到 100 条记录
2025-06-02 21:00:21,935 - INFO - Request Parameters - Page 3:
2025-06-02 21:00:21,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:21,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:22,466 - INFO - Response - Page 3:
2025-06-02 21:00:22,669 - INFO - 第 3 页获取到 100 条记录
2025-06-02 21:00:22,669 - INFO - Request Parameters - Page 4:
2025-06-02 21:00:22,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:22,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:23,185 - INFO - Response - Page 4:
2025-06-02 21:00:23,388 - INFO - 第 4 页获取到 100 条记录
2025-06-02 21:00:23,388 - INFO - Request Parameters - Page 5:
2025-06-02 21:00:23,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:23,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:23,903 - INFO - Response - Page 5:
2025-06-02 21:00:24,106 - INFO - 第 5 页获取到 100 条记录
2025-06-02 21:00:24,106 - INFO - Request Parameters - Page 6:
2025-06-02 21:00:24,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:24,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:24,575 - INFO - Response - Page 6:
2025-06-02 21:00:24,778 - INFO - 第 6 页获取到 100 条记录
2025-06-02 21:00:24,778 - INFO - Request Parameters - Page 7:
2025-06-02 21:00:24,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:24,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:25,200 - INFO - Response - Page 7:
2025-06-02 21:00:25,403 - INFO - 第 7 页获取到 56 条记录
2025-06-02 21:00:25,403 - INFO - 查询完成，共获取到 656 条记录
2025-06-02 21:00:25,403 - INFO - 获取到 656 条表单数据
2025-06-02 21:00:25,403 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-02 21:00:25,419 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 21:00:25,419 - INFO - 开始处理日期: 2025-05
2025-06-02 21:00:25,419 - INFO - Request Parameters - Page 1:
2025-06-02 21:00:25,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:25,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:25,935 - INFO - Response - Page 1:
2025-06-02 21:00:26,138 - INFO - 第 1 页获取到 100 条记录
2025-06-02 21:00:26,138 - INFO - Request Parameters - Page 2:
2025-06-02 21:00:26,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:26,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:26,638 - INFO - Response - Page 2:
2025-06-02 21:00:26,841 - INFO - 第 2 页获取到 100 条记录
2025-06-02 21:00:26,841 - INFO - Request Parameters - Page 3:
2025-06-02 21:00:26,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:26,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:27,481 - INFO - Response - Page 3:
2025-06-02 21:00:27,685 - INFO - 第 3 页获取到 100 条记录
2025-06-02 21:00:27,685 - INFO - Request Parameters - Page 4:
2025-06-02 21:00:27,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:27,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:28,310 - INFO - Response - Page 4:
2025-06-02 21:00:28,513 - INFO - 第 4 页获取到 100 条记录
2025-06-02 21:00:28,513 - INFO - Request Parameters - Page 5:
2025-06-02 21:00:28,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:28,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:28,981 - INFO - Response - Page 5:
2025-06-02 21:00:29,185 - INFO - 第 5 页获取到 100 条记录
2025-06-02 21:00:29,185 - INFO - Request Parameters - Page 6:
2025-06-02 21:00:29,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:29,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:30,169 - INFO - Response - Page 6:
2025-06-02 21:00:30,372 - INFO - 第 6 页获取到 100 条记录
2025-06-02 21:00:30,372 - INFO - Request Parameters - Page 7:
2025-06-02 21:00:30,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:30,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:30,763 - INFO - Response - Page 7:
2025-06-02 21:00:30,966 - INFO - 第 7 页获取到 36 条记录
2025-06-02 21:00:30,966 - INFO - 查询完成，共获取到 636 条记录
2025-06-02 21:00:30,966 - INFO - 获取到 636 条表单数据
2025-06-02 21:00:30,966 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-02 21:00:30,981 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 21:00:30,981 - INFO - 开始处理日期: 2025-06
2025-06-02 21:00:30,981 - INFO - Request Parameters - Page 1:
2025-06-02 21:00:30,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:30,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:31,450 - INFO - Response - Page 1:
2025-06-02 21:00:31,653 - INFO - 第 1 页获取到 100 条记录
2025-06-02 21:00:31,653 - INFO - Request Parameters - Page 2:
2025-06-02 21:00:31,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:31,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:32,216 - INFO - Response - Page 2:
2025-06-02 21:00:32,419 - INFO - 第 2 页获取到 100 条记录
2025-06-02 21:00:32,419 - INFO - Request Parameters - Page 3:
2025-06-02 21:00:32,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:32,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:32,934 - INFO - Response - Page 3:
2025-06-02 21:00:33,138 - INFO - 第 3 页获取到 100 条记录
2025-06-02 21:00:33,138 - INFO - Request Parameters - Page 4:
2025-06-02 21:00:33,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:33,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:33,622 - INFO - Response - Page 4:
2025-06-02 21:00:33,825 - INFO - 第 4 页获取到 100 条记录
2025-06-02 21:00:33,825 - INFO - Request Parameters - Page 5:
2025-06-02 21:00:33,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:33,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:34,356 - INFO - Response - Page 5:
2025-06-02 21:00:34,559 - INFO - 第 5 页获取到 100 条记录
2025-06-02 21:00:34,559 - INFO - Request Parameters - Page 6:
2025-06-02 21:00:34,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:00:34,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:00:34,888 - INFO - Response - Page 6:
2025-06-02 21:00:35,091 - INFO - 第 6 页获取到 13 条记录
2025-06-02 21:00:35,091 - INFO - 查询完成，共获取到 513 条记录
2025-06-02 21:00:35,091 - INFO - 获取到 513 条表单数据
2025-06-02 21:00:35,091 - INFO - 当前日期 2025-06 有 513 条MySQL数据需要处理
2025-06-02 21:00:35,091 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUC1
2025-06-02 21:00:35,653 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUC1
2025-06-02 21:00:35,653 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1512.0, 'new_value': 2754.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6084.0}, {'field': 'total_amount', 'old_value': 1512.0, 'new_value': 8838.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-06-02 21:00:35,669 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9P
2025-06-02 21:00:36,169 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9P
2025-06-02 21:00:36,169 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8300.0, 'new_value': 17400.0}, {'field': 'total_amount', 'old_value': 8300.0, 'new_value': 17400.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 39}]
2025-06-02 21:00:36,184 - INFO - 日期 2025-06 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-06-02 21:00:36,184 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 0 条
2025-06-02 21:00:36,184 - INFO - =================同步完成====================
