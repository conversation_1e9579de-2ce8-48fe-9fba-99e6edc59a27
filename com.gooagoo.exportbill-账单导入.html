<!DOCTYPE html>
<!-- saved from url=(0014)about:internet -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>开放平台</title>
    <link rel="shortcut icon" href="https://openplus.gooagoo.com/logo.png" type="image/x-icon">
    <link rel="stylesheet" href="./com.gooagoo.exportbill-账单导入_files/fonts.css">
  <link href="./com.gooagoo.exportbill-账单导入_files/index.7d3696df.css" rel="stylesheet"></head>

  <body>
    <div id="root"><div class="screen-xl"><section class="layout ant-layout"><header class="ant-layout-header" style="padding: 0px; height: auto; background: rgb(255, 255, 255); flex-shrink: 0;"><div class="header___1QOYl"><div class="headerTop___3K09B"><a href="https://openplus.gooagoo.com/#/openPlatform/home" style="display: flex; align-items: center;"><img src="data:image/svg+xml;base64,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" alt="logo" style="display: block; width: 22px; height: 22px;"><div class="ant-divider ant-divider-vertical" role="separator" style="margin: 0px 12px; background: rgb(51, 51, 51);"></div><span class="logoName___3L72j">开放平台</span></a><ul class="ant-menu menu___3mE8h ant-menu-light ant-menu-root ant-menu-horizontal" role="menu"><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-submenu ant-menu-submenu-horizontal" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span><i aria-label="图标: user" class="anticon anticon-user"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="user" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M858.5 763.6a374 374 0 0 0-80.6-119.5 375.63 375.63 0 0 0-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 0 0-80.6 119.5A371.7 371.7 0 0 0 136 901.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 0 0 8-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"></path></svg></i></span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="visibility: hidden; position: absolute; display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li></ul></div><ul class="ant-menu menuStyle___2dPft ant-menu-light ant-menu-root ant-menu-horizontal" role="menu"><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item menuItemStyle___101YF" role="menuitem"><a class="menuItemContent___1wSQn " href="https://openplus.gooagoo.com/#/openPlatform/home" target=""><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAfRJREFUOE/F1EFrE0EUB/D/m9kEPQg2xx6LNy/iyQ9Q9FSxhyAixbCb3QwaLLkELAh7UMToRc1lZhPIQaywB4uC0IN+AOnVk3hVhJKIt+yy82SlLdumpoaK7nHemx/73swbwl/+6CjP87wzjuPUmZkB9Iwxn6ftmQoqpS4x810AW8wsieictfZeFEWbv0MPBavVqqxUKqsArgJ4prV+DoCUUjestTcBrI9Go6dxHGcH4QnQ87yKlPIBgAUiuqO13ipuqtfrF6SU95n5U5Zla/1+f1iM7wN93z8rhOgA2M6yrN3r9b4dVprruvOO4zwiotPW2nYURR938/bAIAiuEFEbwNvhcNiJ4ziZ1vxarXaiXC6vEdEiM3eMMRt5PgVBUALQJqJla+3jKIpeHnXyxbjv+ytCiFVmfgWgk4PnATwkorlSqXS52+1+mQVsNpvzaZq+ZuYfeYW/St5dPCa4ZIz5+n/AMAzLeSVhGE4cVKHkP/tDpdR1Zr6dg8z8xBjzotjjmcFGo7HBzJtCCFhrLxpjlo8LvgFgdpBAa73070HXdU85jvOOiPIZ/XDgHvaZeV1KmZd8DYBXjAshFvLejsfjxcFg8H1v9JRSK9baW0TkzHKxd3Lfa63zscW+x6HVap1M03RuFjBJksQYsz3xOMyCTMv9CURyTMQmEBEXAAAAAElFTkSuQmCC"><span>首页</span></a></li><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item menuItemStyle___101YF" role="menuitem"><a class="menuItemContent___1wSQn " href="http://www.ddriven-tech.com/#productions" target="_blank"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAnNJREFUOE+tlDtoFFEUhv9zhjVaScAisdPCBz5RQZCITYJFBKsggoVZM3d2XIiuhZCIOgohIGLMrnH23lnBQlBIJZgqWgSDlYqK4iOF3QbEFDZBWeYesyGBbDIbN+ApZ87/zX9eQ/jPQavxfN/fa61tE5HN1TwiKjPzZBiGH+rpEoHpdHprKpXqB7CbiN5aa79XAcy8RUQOAPgYx/FAqVSaf740VgBd193PzMNE9ArAULFY/FEVeJ6XATAlIlPM7IvIEWvthSiK3tUFKqU2AXgMYMwYc3dpolJqGMAeIvozZ/amiBwG0AngtDHm52JujUPP8/qIaHtLS0s6CAK7vJwgCNaVy+VuAN2O41wUkYyIfNVaD64AKqVSRPTcWnstiqKJ1YallMoS0XFrbZ6Zr4pIuzGmMj+4RaHruruI6GFTU1NboVCollU3crnchtnZ2XFmvhLH8W0RORtF0acaoO/7R62117XW7Y2s5ly/7wGYICKPmW+EYfiyBuh53iEA+dbW1rak/iV9JAgCnp6engTQq7V+XQNUSm0EME5Ertb6fSMuPc/bJyIRgA5jzK8a4MKu5QH81lpfbhB4C8B6rXVv4tr09PTsdBzngYgMGGPG/jHpTiKqDuVcqVT6nAhccHlSRPqYuTAzM/NkdHQ0Xgru6upympubT1X7RkSDWuunq55e9aXruu1E1M/MX+ZO7/yyi7kPYAcRDWitXyyvou7fJpPJnBCRXmvt0FIRM+eIKF8sFp8ltaQu0Pf9Y3EcX0oSOY5zJwzDxGuqC8xms9sqlUpHEjCVSo2PjIx8W6vDg3Ecn6nj8FEYhm/WBGxkD5Ny/gLluv0VfNyZAQAAAABJRU5ErkJggg=="><span>解决方案</span></a></li><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item menuItemStyle___101YF" role="menuitem"><a class="menuItemContent___1wSQn " href="https://openplus.gooagoo.com/#/openPlatform/document-center/PalatDes" target=""><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAqVJREFUOE+tlD1oFEEUx9+bXS9BD8k1mtvTxohFQizFWjCoGDBiUvkRyGZ2WW4tJKXoqZ2FUWMObu6uiNqdlZ9VrMXCYCEIgoUfBMSQkAiG25t53hy3YXPZNRYOLCzz8Xvv/d/MH+E/D0zieZ63v9FojCPiPgDYsk8p9TGXy90tFAoqyogFep6XllI+IaJPAPC+MygiXiCiFQBYsCzrZhS6Cej7/u4gCPqIaJCIxpeXl4/XajXZCXQc5wUACAAYBIAd2Wz2RgjdAHLOhwDgOiJ2tQGLpVJpOE4Sx3HuE1HDNM1rUsorRGRallXQ0BZwdHTUyGQy84yxh0tLS3M9PT1DjDEvCcg570PEewDQGwYkorNCiC8toOd5vVLK54ZhjBSLxa+Tk5Mn/wZsJ5HKZDIHGGMWEd3eBOScZxHxmZTyTKVS+fYvwDCz8OwmoOu6e4joJRENCyEWo8C2HP2IyIhI6Y8xRk3tVnXwWKCONjExcaRarb7V/1Gg1gsAHuluIuJGE4nohxDiVCLQtu2jlUrlTSews8tEhGNjY2xgYIB0V2OBvu93ra+vz6bTaX96evp3NEPXdXPNK3KViFolM8Z02QQAP4UQhcQMo5lEgVNTU7vW1tZGdMlKKV01Y4yhUmpFCFGLBXLOdyLirGEYfrFY/OU4zolmBvlSqXR6O+/I5/NWEARPwyvXEjmcjFybQ4j4GABcIcS7JGj7BpwHABsAjgkhgligBnDOLyPixeZz1HolDSSiOhHdKpfLr/SmFlCbQr1ef62UulQulz+Ep23b3ssYy2ntwjnTNA0iugMAVaXUQnd39+eZmZnVcD1qDg8QUT+jeUTc4jCRN3u4KcfBVCp1LgraAtQeqA0VAPoBwEioUZf/HQDmtBHE7Ul07O26m7T+B19FkCQ8sJejAAAAAElFTkSuQmCC"><span>文档中心</span></a></li><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item menuItemStyle___101YF ant-menu-item-selected" role="menuitem"><a class="menuItemContent___1wSQn menuItemContentActive___1qR4i" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1I1PCC5QS57AOB7UCCVRVITS07001NQV" target=""><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAk9JREFUOE+t1EtIFVEcBvDvm3O0zEgLQoKgRUUFFfQgCBy5GQTqKoMKpMeiRUUPCFqJzRnFTUVQULhxUUFpmzaZEFSXO0YQWVGLWtQuEglKM66Zc84/5sb1EVkjOKsZmP9vvjmH7xBzfHGOPcwa3H5JyiqHUdIb8lsSxjfjF0BvNArUueQ5NZgJZLmlvQqiHoAG+RYirwHsg8jFyJScTQ3WtsnKcWezAEcJ2w7nfRYP+wEcINCdC3RyX7j+m3ASkwElelfWcKg47IfxDUA2R0HJ+tSgH8YNIO7CoTMy6jhAKQ7XtEm9ONuTF1Xeb5hPlRAi9FttI4jbEHREgTpdRGvC+JAAHQsXq8reUxybEcyYH6ti6LWE+gS6OyJePWk3TEUzBhWW7hkhr3KB3jvjLyeYpX4MogqCPAVPypeoxiSBH8Z7CqhDJyhbAC4r1Wrrw2YO/hXc2S5VP2P7HJABCPtBHBVBU5/Rt+quyLyRL2MrSF1MOqgk3pE1899PLce0Xa42cTfJTUq8bVmDYT+0lwuow2EPaBKPa6IWb3V1aBs04nd/YtPWMEnwfch+pbhjuaD0+u+vCgsocBLAMETVRoYv/lXXiYRJpfSIzdOhIRfq+5NDQt/YayCOiGB3n9H3UoHJSzWhfSOQl1GgDxaHMkYqLeMHyQaUeCrzqIUfUoN+GHcVugncpEMXPCwVsBmQsjTYtDX0Q3sekKTg3YBsBLgOQAygRzl1IhvyY5qjbmIN/VYbwrkFxVOjLpBFQxUYf3qGo2mg1F2eDZauy7MUfwG99voV+E5LZQAAAABJRU5ErkJggg=="><span>Open API</span></a></li><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item menuItemStyle___101YF" role="menuitem"><a class="menuItemContent___1wSQn " href="https://openplus.gooagoo.com/#/openPlatform/OnlineDebugging" target=""><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAf9JREFUOE/NlL+LE1EQx2feLgkcV60gJOiB4n9woIiVhXCFWiiLoCcKm3272VTKWapPsBBODjSQ3fcSUQmIP9BGEQQ7m/P8Dyw8ECSNjYIE42ZGX7EH5oLCmsJXDt/5zJsffBFm/HDGPNgCJkkyn+f5CgAcAIBNIlrt9XqbtmAYhueFEKi1vlt8QEq5gIg3arXaslKKivgWMIqiK8y8KIS4R0RHAKCmtT6BiBxF0TUAEFrry0Vis9lcJCLNzAeNMT+mAZ8jYj/LssdBEOxzXfchMx8zxgzKAm8DwBwz3xRCHCeipXq9vqSUGpUCJkmyezwerwHAHgD4IoRQaZq+sa2UAtpEpZQ7GAzWETHJsmzDxlqt1o48z1eZWQDAijHms43/dYZW5Pu+43neW8dxmp1O510QBJ7rug8AwA6dmbkKAGcstBQwDMOziHiqWq2etAVHo9ETZn5qjLn/fwBn3vKflhLH8X5m7lQqlUPtdvv7tsOetpRCNHk2UsrDv5Z0FRHnmfmT4zgX0jT9YPW/mcPklqcBfd+veJ73iplfENEz13UvEREYY5JSwDiOdzLzSyI63e1230spfUQ8p7U+Wgpou5JSPkLEr4j4moiWEXFda319G9CKoyi6w8zKGPOxaHnSvhqNxi4hxEVE3MvMG8Ph8Fa/3/82DfjPfjtzx/4JUYCOJHJ9G70AAAAASUVORK5CYII="><span>在线调试</span></a></li><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item menuItemStyle___101YF" role="menuitem"><a class="menuItemContent___1wSQn " href="https://openplus.gooagoo.com/#/openPlatform/BillingManage/Home" target=""><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAX5JREFUOE+9lL1KA0EQx2fmYoo8gKbxo7JSREhlYyPxGSSgoN7uXVDQzjKllU3Ay+5qpYIP4SNYaKGFVgrprCxscnejG+8kRMGsHA5ssezOb2f2PzMIBRvmPN/3Z0ul0tRf+HEcPxtjHqxvH2hhiHiBiF8PuID50xoW2geEYbiSpumhUqrmAsrvSimvieggiqKr/wP6vr+EiAta62gwaiFEnYgmOp3OmVOEUsoaMx8z85Ex5tI6B0Ewx8wGAE6UUqdOwEysTSISiBj2er0nz/POieixWq3ut1qt1BloqyAIgmUAeGPmbQAYL5fL6+12+3XwG0YSRUq5CwBrmaMVbwwAEruY+U5r7TtFKISwRT5vnYionqbpDABou2fmrjHmxgnYbDYnkyTpAxFxlZmnc+BHL3SVUrdOQCHEDiI2soi8rIvibH+vtd5yAg7V3h4iLiqlNn7qpJFEGVIxAAALlIUAhRAVAKhorV8KAf42ML6lXPj4ymdiYQP2t5Rczt8Blzo5JMC5OnIAAAAASUVORK5CYII="><span>计费管理</span></a></li><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item menuItemStyle___101YF" role="menuitem"><a class="menuItemContent___1wSQn " href="https://openplus.gooagoo.com/#/openPlatform/control-platform" target=""><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAT1JREFUOE/tlCFPA0EQhd9LaAL0kPwAUtAEicLhMEgUai6EBnWigoSQIBB1hJDZnEBhETgcBkvQpcFjuQLJthl6F45AKbR3qWTcZne/N7PvZYkJFyfMQwYMw3AZwC6AmZICrwBOVfU+A4rICclVM3sA0CkCJVkFsAjgVlX38g61rzCvqptFYPnZ/oSXAJ5UNfwKnFPVrZLACwDP/0BM9g3NbAXASxlTAMySvPtmipktAbgpCVwj2Rp0uQZgp1KpFAq29z4N9hmA9idQRA5IbpTsLrtmZlfOucMs2FEUVZMkWTez6V+geeDTAP8okm9BEFw3m83OWL+NiJynFOfc9qgphgLr9fqC975hZlMpgGRqWDpW62Pd7fV6x3EcPw4KDAWKSI3kPoAMOKS6fbEj51x7LOCosf7aH+sNiwi8A4ZxlxVZo7AHAAAAAElFTkSuQmCC"><span>控制台</span></a></li><li class="ant-menu-submenu ant-menu-submenu-horizontal ant-menu-overflowed-submenu" role="menuitem" style="visibility: hidden; position: absolute; display: none;"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true"><span>···</span><i class="ant-menu-submenu-arrow"></i></div></li></ul></div></header><main class="ant-layout-content" style="padding: 0px; min-height: 0px; flex: 1 1 0%; background: rgb(255, 255, 255); overflow: hidden;"><section class="openId___3WUzK ant-layout ant-layout-has-sider"><aside class="ant-layout-sider ant-layout-sider-dark" style="overflow-y: auto; background: rgb(255, 255, 255); flex: 0 0 200px; max-width: 200px; min-width: 200px; width: 200px;"><div class="ant-layout-sider-children"><ul class="ant-menu ant-menu-light ant-menu-root ant-menu-inline" role="menu" style="border-right: 1px solid rgb(238, 238, 238); padding-bottom: 80px;"><li id="1001" class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>店铺</span></span><i class="ant-menu-submenu-arrow"></i></div><div></div></li><li id="1002" class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>机构</span></span><i class="ant-menu-submenu-arrow"></i></div><div></div></li><li id="1003" class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>业态</span></span><i class="ant-menu-submenu-arrow"></i></div><div></div></li><li id="1004" class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>设备</span></span><i class="ant-menu-submenu-arrow"></i></div><div></div></li><li id="1005" class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>解析</span></span><i class="ant-menu-submenu-arrow"></i></div><div></div></li><li id="1006" class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open ant-menu-submenu-selected" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="true" aria-haspopup="true" aria-owns="1006$Menu" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>账单</span></span><i class="ant-menu-submenu-arrow"></i></div><ul id="1006$Menu" class="ant-menu ant-menu-sub ant-menu-inline" role="menu" style=""><li id="1IESG12R62L6FT0MN4QI3D39FB0014SR" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="通联合作方商户交易结果数据实时下发私有云" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1IESG12R62L6FT0MN4QI3D39FB0014SR" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">通联合作方商户交易结果数据实时下发私有云</a></li><li id="1I0B0VL3EK8CFO72UQI41D6FNL001TDC" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="公有云账单实时下发私有云" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1I0B0VL3EK8CFO72UQI41D6FNL001TDC" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">公有云账单实时下发私有云</a></li><li id="1HD3H6OMFDRUF56MJE5LC5GS0D001GUI" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="根据账单文件名密文查询账单信息-鑫耀专用" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1HD3H6OMFDRUF56MJE5LC5GS0D001GUI" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">根据账单文件名密文查询账单信息-鑫耀专用</a></li><li id="1H595Q4U6L8A637Q2OV6FUR3KU001DUS" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="POS账单导入" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1H595Q4U6L8A637Q2OV6FUR3KU001DUS" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">POS账单导入</a></li><li id="1GFS25DDD2I6T97Q2OV1BTNQIQ001V8P" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="账单删除" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1GFS25DDD2I6T97Q2OV1BTNQIQ001V8P" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">账单删除</a></li><li id="1FUB7CUGINLOC47Q2OVA41QO8G001DV0" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="账单导入（适用于o2o数据）" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1FUB7CUGINLOC47Q2OVA41QO8G001DV0" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">账单导入（适用于o2o数据）</a></li><li id="1FAKD4HM8BEOU47Q2OVAE57DBM0012M9" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="日结单查询接口" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1FAKD4HM8BEOU47Q2OVAE57DBM0012M9" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">日结单查询接口</a></li><li id="1EE08EV36JV9317Q2OVA41QO8I00148V" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="华润唯一码账单查询" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1EE08EV36JV9317Q2OVA41QO8I00148V" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">华润唯一码账单查询</a></li><li id="1DQ0P8IG3QHPO10AB2M100J5LT00141V" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="唯一码查询账单信息" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1DQ0P8IG3QHPO10AB2M100J5LT00141V" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">唯一码查询账单信息</a></li><li id="1DATCHM3OQ1OB50AB2M100KJNM001MTQ" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="账单导入(适用没有设备号的情况)" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1DATCHM3OQ1OB50AB2M100KJNM001MTQ" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">账单导入(适用没有设备号的情况)</a></li><li id="1DAPV76NIN327L0AB2M100IB2B001VAQ" class="ant-menu-item ant-menu-item-selected" role="menuitem" style="padding-left: 48px;"><a title="账单导入（默认）" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1DAPV76NIN327L0AB2M100IB2B001VAQ" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">账单导入（默认）</a></li><li id="1D5T5L6908CJFE0AB2M101QJ2C001V6O" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="根据账单文件名密文查询账单信息" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1D5T5L6908CJFE0AB2M101QJ2C001V6O" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">根据账单文件名密文查询账单信息</a></li><li id="1D3NJDE2UDHQJE0AB2M102P2NN001I5R" class="ant-menu-item" role="menuitem" style="padding-left: 48px;"><a title="账单实时推送" href="https://openplus.gooagoo.com/#/openPlatform/open-id/1D3NJDE2UDHQJE0AB2M102P2NN001I5R" style="font-size: 14px; font-weight: 400; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">账单实时推送</a></li></ul></li><li id="1007" class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>发票</span></span><i class="ant-menu-submenu-arrow"></i></div><div></div></li><li id="1008" class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>积分</span></span><i class="ant-menu-submenu-arrow"></i></div><div></div></li><li id="1009" class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem"><div class="ant-menu-submenu-title" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;"><span><i aria-label="图标: book" class="anticon anticon-book"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="book" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0 0 22.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"></path></svg></i><span>数据+</span></span><i class="ant-menu-submenu-arrow"></i></div><div></div></li></ul></div></aside><section class="ant-layout" style="overflow-y: auto; background: rgb(255, 255, 255);"><div style="border-bottom: 1px solid rgb(238, 238, 238);"><div class="ant-breadcrumb" style="margin: 16px 20px; line-height: 24px;"><span><span class="ant-breadcrumb-link">账单</span><span class="ant-breadcrumb-separator">/</span></span><span><span class="ant-breadcrumb-link">账单导入（默认）</span><span class="ant-breadcrumb-separator">/</span></span></div></div><main class="ant-layout-content" style="background: rgb(255, 255, 255); height: auto;"><div class="template"><div><div class="tempHeader module"><div class="tepHeader"><h1 style="font-size: 24px;">com.gooagoo.exportbill (账单导入（默认）)</h1></div></div><div class="module"><div class="version___1-F_y"><span><span>当前版本: 1.0.17</span><span style="margin-left: 30px;">更新日期: 2025-06-27 18:06:59</span></span><span><a href="javascript:;">更新记录</a></span></div></div><div class="module"><div class="application"><h1 class="title" style="font-size: 20px;">应用场景</h1><span style="white-space: normal; word-break: break-all; overflow: auto;">第三方通过该接口将账单数据接入到数衍科技平台，调用此接口前先通过其他接口把数衍设备编号获取到。
请求方式：POST
Header：Content-Type : application/x-www-form-urlencoded
测试环境url：http://api.test.goago.cn/oapi/rest
测试参数：
AppId：d1667ebbaa3e4935a7e09be1a50f0af5
AppKey：2c968875814106ca0181adf9657d0005
SecretKey：D22B58F177D6739D413C5FE24CD32ED0
terminalNumber：6A53BB2D7CDE</span></div></div><div class="location module"><h1 class="title" style="font-size: 20px;">请求参数</h1><div class="requestAddress category"><h3 class="title" style="font-size: 16px;">请求地址</h3><div style="font-size: 14px; line-height: 36px;"><span>接口地址</span><span style="margin-left: 50px;">http://api.gooagoo.com/oapi/rest</span></div><div style="font-size: 14px; line-height: 36px;"><span>请求类型</span><span style="margin-left: 50px;">POST</span></div><div style="font-size: 14px; line-height: 36px;"><span>数据格式</span><span style="margin-left: 50px;">Content-Type = "application/x-www-form-urlencoded"; charset=utf-8</span></div><div style="font-size: 14px; line-height: 36px;"><span>参数类型</span><span style="margin-left: 50px;">form表单</span></div></div><div class="publicRequest category"><div class="PublicRequest"><h3 class="title" style="font-size: 16px;">公共请求参数</h3><div class="ant-table-wrapper"><div class="ant-spin-nested-loading"><div class="ant-spin-container"><div class="ant-table ant-table-middle ant-table-scroll-position-left"><div class="ant-table-content"><div class="ant-table-body"><table class=""><colgroup><col style="width: 20%; min-width: 20%;"><col style="width: 8%; min-width: 8%;"><col style="width: 8%; min-width: 8%;"><col style="width: 32%; min-width: 32%;"><col style="width: 32%; min-width: 32%;"></colgroup><thead class="ant-table-thead"><tr><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">参数</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">类型</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">是否必填</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">描述</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">示例值</span><span class="ant-table-column-sorter"></span></div></span></th></tr></thead><tbody class="ant-table-tbody"><tr class="ant-table-row ant-table-row-level-0" data-row-key="1"><td class="ant-table-row-cell-break-word">method</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">固定值，gogo.open.auto.routing</td><td class="ant-table-row-cell-break-word">gogo.open.auto.routing</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="2"><td class="ant-table-row-cell-break-word">timestamp</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">发送请求的时间，格式 “yyyyMMDDHHmmss”</td><td class="ant-table-row-cell-break-word">**************</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="3"><td class="ant-table-row-cell-break-word">messageFormat</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">接口报文格式，目前仅支持json格式</td><td class="ant-table-row-cell-break-word">json</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="*************"><td class="ant-table-row-cell-break-word">appId</td><td class="ant-table-row-cell-break-word">string</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">数衍平台分发给开发者应用的ID，在开放平台创建应用时自动分配；</td><td class="ant-table-row-cell-break-word">5K8264ILTKCH16CQ2502SI8ZNMTM67VS</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="4"><td class="ant-table-row-cell-break-word">appKey</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">数衍平台分发给开发者应用的appKey，在开放平台创建应用时自动分配</td><td class="ant-table-row-cell-break-word">5K8264ILTKCH16CQ2502SI8ZNMTM67VS</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="5"><td class="ant-table-row-cell-break-word">v</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">接口版本，固定为：1.0</td><td class="ant-table-row-cell-break-word">1.0</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="6"><td class="ant-table-row-cell-break-word">sign</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">签名值，详见签名算法</td><td class="ant-table-row-cell-break-word">5K8264ILTKCH16CQ2502SI8ZNMTM67VS</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="7"><td class="ant-table-row-cell-break-word">signMethod</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">签名算法类型，目前仅支持MD5</td><td class="ant-table-row-cell-break-word">MD5</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="8"><td class="ant-table-row-cell-break-word">lowerMethod</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">固定值：com.gooagoo.exportbill</td><td class="ant-table-row-cell-break-word">com.gooagoo.exportbill</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="9"><td class="ant-table-row-cell-break-word">data</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">业务请求参数的集合，最大长度不限，除公共参数外所有请求参数都必须放在这个参数中传递</td><td class="ant-table-row-cell-break-word">{key:value}</td></tr></tbody></table></div></div></div></div></div></div></div></div><div class="parameter category"><div class="Parameter"><h3 class="title" style="font-size: 16px;">业务请求参数</h3><div class="ant-table-wrapper"><div class="ant-spin-nested-loading"><div class="ant-spin-container"><div class="ant-table ant-table-middle ant-table-scroll-position-left"><div class="ant-table-content"><div class="ant-table-body"><table class=""><colgroup><col style="width: 20%; min-width: 20%;"><col style="width: 8%; min-width: 8%;"><col style="width: 8%; min-width: 8%;"><col style="width: 32%; min-width: 32%;"><col style="width: 32%; min-width: 32%;"></colgroup><thead class="ant-table-thead"><tr><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">参数</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">类型</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">是否必填</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">描述</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">示例值</span><span class="ant-table-column-sorter"></span></div></span></th></tr></thead><tbody class="ant-table-tbody"><tr class="ant-table-row ant-table-row-level-0" data-row-key="1694657029438"><td class="ant-table-row-cell-break-word">exactBillType</td><td class="ant-table-row-cell-break-word">string</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">细分账单类型。10102-美团外卖单；10103-饿了么外卖单；10104-抖音外卖单；10105-京东外卖单；10602-美团外卖退款单；10603-饿了么外卖退款单；10604-抖音外卖退款单；10605-京东外卖退款单。billType还是结账单就传1，退款单就传6</td><td class="ant-table-row-cell-break-word">10102</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1693465524944"><td class="ant-table-row-cell-break-word">salesNum</td><td class="ant-table-row-cell-break-word">number</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">日结单-总销售单数</td><td class="ant-table-row-cell-break-word"></td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1693465370352"><td class="ant-table-row-cell-break-word">refundAmount</td><td class="ant-table-row-cell-break-word">number</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">日结单-退款金额</td><td class="ant-table-row-cell-break-word"></td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1693465352696"><td class="ant-table-row-cell-break-word">refundOrders</td><td class="ant-table-row-cell-break-word">number</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">日结单-退款单数</td><td class="ant-table-row-cell-break-word"></td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1693465335632"><td class="ant-table-row-cell-break-word">takeoutAmount</td><td class="ant-table-row-cell-break-word">number</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">日结单-外单销售金额</td><td class="ant-table-row-cell-break-word"></td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1693465282656"><td class="ant-table-row-cell-break-word">takeoutOrders</td><td class="ant-table-row-cell-break-word">number</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">日结单-外卖销售单数</td><td class="ant-table-row-cell-break-word"></td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1693465197927"><td class="ant-table-row-cell-break-word">inStoreAmount</td><td class="ant-table-row-cell-break-word">number</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">日结单-店内销售金额</td><td class="ant-table-row-cell-break-word"></td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1693465170000"><td class="ant-table-row-cell-break-word">inStoreOrders</td><td class="ant-table-row-cell-break-word">number</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">日结单-店内销售单数</td><td class="ant-table-row-cell-break-word"></td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1"><td class="ant-table-row-cell-break-word">shopIds</td><td class="ant-table-row-cell-break-word">	String[]</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">数衍平台机构编号，数组 （数组内的值可传一个或多个），平台内要求唯一性</td><td class="ant-table-row-cell-break-word">[“1CN8ERKQKBMIAR0AB2M10254H0001INR”]</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1558683660657"><td class="ant-table-row-cell-break-word">billSerialNumber</td><td class="ant-table-row-cell-break-word">string</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">票据上的流水号字段</td><td class="ant-table-row-cell-break-word">"111555554444444"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="2"><td class="ant-table-row-cell-break-word">terminalNumber</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">数衍平台分配给第三方的设备编号，平台内要求唯一性，12位(必须在数衍平台存在,否则导入失败)</td><td class="ant-table-row-cell-break-word">	"F832E486B137"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="3"><td class="ant-table-row-cell-break-word">saleTime	</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">销售时间，一般指打印小票上的销售时间，格式:yyyy-MM-dd HH:mm:ss</td><td class="ant-table-row-cell-break-word">	"2015-10-15 05:25:55"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="4"><td class="ant-table-row-cell-break-word">thirdPartyOrderNo</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">	第三方订单号 ,数衍平台要求唯一性,重复会覆盖</td><td class="ant-table-row-cell-break-word">	"111111111111111"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="5"><td class="ant-table-row-cell-break-word">receivableAmount</td><td class="ant-table-row-cell-break-word">	Double</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">实收金额，保留两位小数在数衍平台也表示最终实际付款金额</td><td class="ant-table-row-cell-break-word">	55.22</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="6"><td class="ant-table-row-cell-break-word">totalNum</td><td class="ant-table-row-cell-break-word">	Double</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">商品数量</td><td class="ant-table-row-cell-break-word">10</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="7"><td class="ant-table-row-cell-break-word">totalFee</td><td class="ant-table-row-cell-break-word">	Double</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">应收金额</td><td class="ant-table-row-cell-break-word">	5000.00</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="14"><td class="ant-table-row-cell-break-word">saler</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">售货员	</td><td class="ant-table-row-cell-break-word">"张三"	</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="15"><td class="ant-table-row-cell-break-word">checkstand</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">收银台	</td><td class="ant-table-row-cell-break-word">"收银台1号"	</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="16"><td class="ant-table-row-cell-break-word">cashier</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">收银员	</td><td class="ant-table-row-cell-break-word">	"李四"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="17"><td class="ant-table-row-cell-break-word">paidAmount</td><td class="ant-table-row-cell-break-word">Double</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">实付金额，保留两位小数</td><td class="ant-table-row-cell-break-word">12.22</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="18"><td class="ant-table-row-cell-break-word">discountAmount</td><td class="ant-table-row-cell-break-word">Double</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">折扣金额，保留两位小数</td><td class="ant-table-row-cell-break-word">	2.22</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="19"><td class="ant-table-row-cell-break-word">couponAmount</td><td class="ant-table-row-cell-break-word">	Double</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">优惠金额，保留两位小数</td><td class="ant-table-row-cell-break-word">	2.22</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="20"><td class="ant-table-row-cell-break-word">changeAmount	</td><td class="ant-table-row-cell-break-word">	Double</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">找零金额，保留两位小数</td><td class="ant-table-row-cell-break-word">1.11</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="21"><td class="ant-table-row-cell-break-word">settlementWay</td><td class="ant-table-row-cell-break-word">json</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">结算方式</td><td class="ant-table-row-cell-break-word">	[ { "p" : "现金", "a" : 4.5 }, { "p" : "优惠券", "a" : 5.5 } ]</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="22"><td class="ant-table-row-cell-break-word">memberCardNumber	</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">会员卡号</td><td class="ant-table-row-cell-break-word">	"1231232432"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="23"><td class="ant-table-row-cell-break-word">totalConsumption</td><td class="ant-table-row-cell-break-word">Double</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">累计消费	</td><td class="ant-table-row-cell-break-word">	5555.22</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="24"><td class="ant-table-row-cell-break-word">website</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">网址</td><td class="ant-table-row-cell-break-word">	"http://www.goago.cn/bill/001"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="25"><td class="ant-table-row-cell-break-word">goodsDetails</td><td class="ant-table-row-cell-break-word">json</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">商品详情</td><td class="ant-table-row-cell-break-word">[ { "name" : "益达口香糖", "itemserial" : "6923450657438", "price" : 0.9, "totalnum" : 5.0, "totalprice" : 4.5 }, { "name" : "可口可乐", "itemserial" : "6922049007593", "price" : 1.1, "totalnum" : 5.0, "totalprice" : 5.5 } ]	</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="26"><td class="ant-table-row-cell-break-word">roomNo	</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">房间号	</td><td class="ant-table-row-cell-break-word">"1111"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="27"><td class="ant-table-row-cell-break-word">checkinName	</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">	入住姓名</td><td class="ant-table-row-cell-break-word">"张三"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="28"><td class="ant-table-row-cell-break-word">deskNo</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">桌号</td><td class="ant-table-row-cell-break-word">	"222"</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="29"><td class="ant-table-row-cell-break-word">consumeNum</td><td class="ant-table-row-cell-break-word">	Double</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">	消费人数</td><td class="ant-table-row-cell-break-word">3</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="1652757518056"><td class="ant-table-row-cell-break-word">billType</td><td class="ant-table-row-cell-break-word">string</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">1-结账单，6-退款单，3-日结单</td><td class="ant-table-row-cell-break-word">1</td></tr></tbody></table></div></div></div></div></div></div></div></div></div><div class="respond module"><h1 style="font-size: 20px;">响应参数</h1><div class="publicResponse category"><div class="PublicResponse category"><h3 class="title" style="font-size: 16px;">公共响应参数</h3><div class="ant-table-wrapper"><div class="ant-spin-nested-loading"><div class="ant-spin-container"><div class="ant-table ant-table-middle ant-table-scroll-position-left"><div class="ant-table-content"><div class="ant-table-body"><table class=""><colgroup><col style="width: 20%; min-width: 20%;"><col style="width: 8%; min-width: 8%;"><col style="width: 8%; min-width: 8%;"><col style="width: 32%; min-width: 32%;"><col style="width: 32%; min-width: 32%;"></colgroup><thead class="ant-table-thead"><tr><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">参数</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">类型</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">是否必填</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">描述</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">示例值</span><span class="ant-table-column-sorter"></span></div></span></th></tr></thead><tbody class="ant-table-tbody"><tr class="ant-table-row ant-table-row-level-0" data-row-key="1"><td class="ant-table-row-cell-break-word">data</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">接口请求返回业务数据 集合</td><td class="ant-table-row-cell-break-word">{key:value}</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="2"><td class="ant-table-row-cell-break-word">rescode</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">接口请求返回状态码（OPEN_SUCCESS-成功，OPEN_FAIL-失败）</td><td class="ant-table-row-cell-break-word">OPEN_SUCCESS</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="3"><td class="ant-table-row-cell-break-word">resmsg</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">接口请求返回签名结果</td><td class="ant-table-row-cell-break-word">成功</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="4"><td class="ant-table-row-cell-break-word">sign</td><td class="ant-table-row-cell-break-word">String</td><td class="ant-table-row-cell-break-word"><div>是</div></td><td class="ant-table-row-cell-break-word">签名结果</td><td class="ant-table-row-cell-break-word">BB30E730D3706514C77BCA2EDC405D08</td></tr></tbody></table></div></div></div></div></div></div></div></div><div class="business category"><div class="respond"><h3 class="title" style="font-size: 16px;">业务响应参数</h3><div class="ant-table-wrapper"><div class="ant-spin-nested-loading"><div class="ant-spin-container"><div class="ant-table ant-table-middle ant-table-scroll-position-left"><div class="ant-table-content"><div class="ant-table-body"><table class=""><colgroup><col style="width: 20%; min-width: 20%;"><col style="width: 8%; min-width: 8%;"><col style="width: 8%; min-width: 8%;"><col style="width: 32%; min-width: 32%;"><col style="width: 32%; min-width: 32%;"></colgroup><thead class="ant-table-thead"><tr><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">参数</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">类型</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">是否必填</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">描述</span><span class="ant-table-column-sorter"></span></div></span></th><th class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">示例值</span><span class="ant-table-column-sorter"></span></div></span></th></tr></thead><tbody class="ant-table-tbody"><tr class="ant-table-row ant-table-row-level-0" data-row-key="1"><td class="ant-table-row-cell-break-word">data</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">业务接口返回数据(成功/失败)</td><td class="ant-table-row-cell-break-word">	成功</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="2"><td class="ant-table-row-cell-break-word">rescode</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">接口请求返回状态码（OPEN_SUCCESS-成功，OPEN_FAIL-失败）</td><td class="ant-table-row-cell-break-word">OPEN_SUCCESS</td></tr><tr class="ant-table-row ant-table-row-level-0" data-row-key="3"><td class="ant-table-row-cell-break-word">resmsg</td><td class="ant-table-row-cell-break-word">	String</td><td class="ant-table-row-cell-break-word"><div>否</div></td><td class="ant-table-row-cell-break-word">接口请求返回提示信息</td><td class="ant-table-row-cell-break-word">成功</td></tr></tbody></table></div></div></div></div></div></div></div></div></div><div class="example module"><div class="example demo"><h1 class="title" style="font-size: 20px;">请求示例</h1><div class="ant-tabs ant-tabs-top ant-tabs-line"><div role="tablist" class="ant-tabs-bar ant-tabs-top-bar" tabindex="0"><div class="ant-tabs-nav-container"><span unselectable="unselectable" class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i aria-label="图标: left" class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="left" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 0 0 0 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"></path></svg></i></span></span><span unselectable="unselectable" class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i aria-label="图标: right" class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="right" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 0 0 302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 0 0 0-50.4z"></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div role="tab" aria-disabled="false" aria-selected="true" class="ant-tabs-tab-active ant-tabs-tab">JSON示例</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style="display: block; transform: translate3d(0px, 0px, 0px); width: 95px;"></div></div></div></div></div></div><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div><div class="ant-tabs-content ant-tabs-content-animated ant-tabs-top-content" style="margin-left: 0%;"><div role="tabpanel" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active"><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div><pre>{
	"terminalNumber": "AAAAAAAAAAAA",
	"saler": "售货员",
	"checkstand": "收银台",
	"cashier": "收银员",
	"receivableAmount": 100,
	"totalNum": 10,
	"billSerialNumber": "小票流水号143246",
	"totalFee": 10,
	"paidAmount": 10,
	"discountAmount": 0,
	"couponAmount": 0,
	"changeAmount": 0,
	"settlementWay": [
		{
			"p": "现金",
			"a": 4.5
		},
		{
			"p": "优惠券",
			"a": 5.5
		}
	],
	"saleTime": "2015-10-15 05:25:55",
	"memberCardNumber": "AABB11001100",
	"totalConsumption": 2,
	"website": "http://www.baidu.com/",
	"goodsDetails": [
		{
			"name": "益达口香糖",
			"itemserial": "6923450657438",
			"price": 0.9,
			"totalnum": 5,
			"totalprice": 4.5
		},
		{
			"name": "可口可乐",
			"itemserial": "6922049007593",
			"price": 1.1,
			"totalnum": 5,
			"totalprice": 5.5
		}
	],
	"roomNo": "房间1115",
	"checkinName": "张三",
	"deskNo": "桌号1115",
	"consumeNum": 3,
	"thirdPartyOrderNo": "1111111111111111"
}</pre><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div></div></div><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div></div></div></div><div class="esponseSample module"><div class="example demo"><h1 class="title" style="font-size: 20px;">响应示例</h1><div class="ant-tabs ant-tabs-top ant-tabs-line"><div role="tablist" class="ant-tabs-bar ant-tabs-top-bar" tabindex="0"><div class="ant-tabs-nav-container"><span unselectable="unselectable" class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i aria-label="图标: left" class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="left" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 0 0 0 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"></path></svg></i></span></span><span unselectable="unselectable" class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i aria-label="图标: right" class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="right" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 0 0 302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 0 0 0-50.4z"></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div role="tab" aria-disabled="false" aria-selected="true" class="ant-tabs-tab-active ant-tabs-tab">JSON示例</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style="display: block; transform: translate3d(0px, 0px, 0px); width: 95px;"></div></div></div></div></div></div><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div><div class="ant-tabs-content ant-tabs-content-animated ant-tabs-top-content" style="margin-left: 0%;"><div role="tabpanel" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active"><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div><pre>{
	"rescode": "OPEN_SUCCESS",
	"resmsg": "账单导入成功",
	"data": "成功",
	"sign": "A1CEA4C453218B8D962196EAD8070F07"
}</pre><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div></div></div><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div></div></div></div><div class="abnormal module"><div class="example demo"><h1 class="title" style="font-size: 20px;">异常示例</h1><div class="ant-tabs ant-tabs-top ant-tabs-line"><div role="tablist" class="ant-tabs-bar ant-tabs-top-bar" tabindex="0"><div class="ant-tabs-nav-container"><span unselectable="unselectable" class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i aria-label="图标: left" class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="left" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 0 0 0 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"></path></svg></i></span></span><span unselectable="unselectable" class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i aria-label="图标: right" class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="right" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 0 0 302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 0 0 0-50.4z"></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div role="tab" aria-disabled="false" aria-selected="true" class="ant-tabs-tab-active ant-tabs-tab">JSON示例</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style="display: block; transform: translate3d(0px, 0px, 0px); width: 95px;"></div></div></div></div></div></div><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div><div class="ant-tabs-content ant-tabs-content-animated ant-tabs-top-content" style="margin-left: 0%;"><div role="tabpanel" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active"><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div><pre>{
	"rescode": "OPEN_FAIL",
	"resmsg": "账单导入失败",
	"data": "失败",
	"sign": "A1CEA4C453218B8D962196EAD8070F07"
}</pre><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div></div></div><div tabindex="0" role="presentation" style="width: 0px; height: 0px; overflow: hidden; position: absolute;"></div></div></div></div><div></div><div class="module" style="margin-bottom: 20px;"><h1 style="font-size: 20px;">签名方法</h1><h3 style="font-size: 16px;">第一步</h3><div>所有发送或者接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到大排序（字典序）,使用URL键值对的格式（即key1=value1&amp;key2=value2…）拼接成字符串stringA。</div><div>特别注意以下重要规则:</div><div style="padding: 10px 0px;"><ul><li>参数名ASCII码从小到大排序（字典序）；</li><li>如果参数的值为空不参与签名；</li><li>参数名区分大小写；</li><li>传送的sign参数不参与签名，将生成的签名与该sign值作校验。</li><li>接口可能增加字段，验证签名时必须支持增加的扩展字段</li></ul></div><h3 style="font-size: 16px;">第二步</h3><div>在stringA最后拼接上key=(API密钥的值)得到stringSignTemp字符串，并对stringSignTemp进行MD5运算，再将得到的字符串所有字符转换为大写，得到sign值signValue。示例，假设传送的参数如下:</div><pre style="border: 1px solid rgb(204, 204, 204); padding: 10px; margin: 10px;">appKey=8641474589158318
timestamp=201904091000371
method=gogo.bill.amount.query
signMethod=MD5
messageFormat=json
v=1.0
qrCode=http://f.test.goago.cn/001/B107GGGGHkiikGkjklstljljsikqhllql</pre><li>对参数按照key=value的格式，并按照参数名ASCII字典序排序如下：</li><pre style="white-space: normal; word-break: break-all; overflow: auto; border: 1px solid rgb(204, 204, 204); padding: 10px; margin: 10px;">stringA=appKey=8641474589158318&amp;messageFormat=json&amp;method=gogo.bill.amount.query&amp;qrCode=http://f.test.goago.cn/001/B107GGGGHkiikGkjklstljljsikqhllql&amp;signMethod=MD5&amp;timestamp=201904091000371&amp;v=1.0</pre><li>拼接API密钥：</li><pre style="border: 1px solid rgb(204, 204, 204); padding: 10px; margin: 10px;">stringSignTemp="stringA&amp;key=1D293J60TR392T0AB2M102HLGM00108N"
sign=MD5(stringSignTemp).toUpperCase()</pre></div><div><div style="text-align: center; height: 80px; line-height: 80px; border-top: 1px solid rgb(238, 238, 238);"><span style="margin-right: 10px; color: rgb(85, 85, 85);">Copyright  <i aria-label="图标: copyright" class="anticon anticon-copyright"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="copyright" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"></path></svg></i> 2025 ddriven-tech.com. All rights reserved.</span></div></div></div></div></main></section></section></main></section></div></div>
  <script type="text/javascript" src="./com.gooagoo.exportbill-账单导入_files/index.a7bb20b8.js.下载"></script>

</body></html>