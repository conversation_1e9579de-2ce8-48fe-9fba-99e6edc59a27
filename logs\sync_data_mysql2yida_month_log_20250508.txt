2025-05-08 00:00:04,091 - INFO - =================使用默认全量同步=============
2025-05-08 00:00:05,337 - INFO - MySQL查询成功，共获取 3264 条记录
2025-05-08 00:00:05,338 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-08 00:00:05,366 - INFO - 开始处理日期: 2025-01
2025-05-08 00:00:05,369 - INFO - Request Parameters - Page 1:
2025-05-08 00:00:05,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:05,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:06,695 - INFO - Response - Page 1:
2025-05-08 00:00:06,895 - INFO - 第 1 页获取到 100 条记录
2025-05-08 00:00:06,895 - INFO - Request Parameters - Page 2:
2025-05-08 00:00:06,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:06,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:07,428 - INFO - Response - Page 2:
2025-05-08 00:00:07,628 - INFO - 第 2 页获取到 100 条记录
2025-05-08 00:00:07,628 - INFO - Request Parameters - Page 3:
2025-05-08 00:00:07,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:07,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:08,072 - INFO - Response - Page 3:
2025-05-08 00:00:08,272 - INFO - 第 3 页获取到 100 条记录
2025-05-08 00:00:08,272 - INFO - Request Parameters - Page 4:
2025-05-08 00:00:08,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:08,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:08,813 - INFO - Response - Page 4:
2025-05-08 00:00:09,013 - INFO - 第 4 页获取到 100 条记录
2025-05-08 00:00:09,013 - INFO - Request Parameters - Page 5:
2025-05-08 00:00:09,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:09,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:09,483 - INFO - Response - Page 5:
2025-05-08 00:00:09,683 - INFO - 第 5 页获取到 100 条记录
2025-05-08 00:00:09,683 - INFO - Request Parameters - Page 6:
2025-05-08 00:00:09,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:09,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:10,159 - INFO - Response - Page 6:
2025-05-08 00:00:10,360 - INFO - 第 6 页获取到 100 条记录
2025-05-08 00:00:10,360 - INFO - Request Parameters - Page 7:
2025-05-08 00:00:10,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:10,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:10,901 - INFO - Response - Page 7:
2025-05-08 00:00:11,102 - INFO - 第 7 页获取到 82 条记录
2025-05-08 00:00:11,102 - INFO - 查询完成，共获取到 682 条记录
2025-05-08 00:00:11,102 - INFO - 获取到 682 条表单数据
2025-05-08 00:00:11,113 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-08 00:00:11,124 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 00:00:11,125 - INFO - 开始处理日期: 2025-02
2025-05-08 00:00:11,125 - INFO - Request Parameters - Page 1:
2025-05-08 00:00:11,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:11,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:11,592 - INFO - Response - Page 1:
2025-05-08 00:00:11,794 - INFO - 第 1 页获取到 100 条记录
2025-05-08 00:00:11,794 - INFO - Request Parameters - Page 2:
2025-05-08 00:00:11,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:11,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:12,286 - INFO - Response - Page 2:
2025-05-08 00:00:12,488 - INFO - 第 2 页获取到 100 条记录
2025-05-08 00:00:12,488 - INFO - Request Parameters - Page 3:
2025-05-08 00:00:12,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:12,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:13,047 - INFO - Response - Page 3:
2025-05-08 00:00:13,248 - INFO - 第 3 页获取到 100 条记录
2025-05-08 00:00:13,248 - INFO - Request Parameters - Page 4:
2025-05-08 00:00:13,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:13,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:13,739 - INFO - Response - Page 4:
2025-05-08 00:00:13,941 - INFO - 第 4 页获取到 100 条记录
2025-05-08 00:00:13,941 - INFO - Request Parameters - Page 5:
2025-05-08 00:00:13,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:13,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:14,416 - INFO - Response - Page 5:
2025-05-08 00:00:14,617 - INFO - 第 5 页获取到 100 条记录
2025-05-08 00:00:14,617 - INFO - Request Parameters - Page 6:
2025-05-08 00:00:14,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:14,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:15,033 - INFO - Response - Page 6:
2025-05-08 00:00:15,234 - INFO - 第 6 页获取到 100 条记录
2025-05-08 00:00:15,234 - INFO - Request Parameters - Page 7:
2025-05-08 00:00:15,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:15,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:15,686 - INFO - Response - Page 7:
2025-05-08 00:00:15,886 - INFO - 第 7 页获取到 70 条记录
2025-05-08 00:00:15,886 - INFO - 查询完成，共获取到 670 条记录
2025-05-08 00:00:15,886 - INFO - 获取到 670 条表单数据
2025-05-08 00:00:15,898 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-08 00:00:15,909 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 00:00:15,909 - INFO - 开始处理日期: 2025-03
2025-05-08 00:00:15,909 - INFO - Request Parameters - Page 1:
2025-05-08 00:00:15,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:15,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:16,449 - INFO - Response - Page 1:
2025-05-08 00:00:16,650 - INFO - 第 1 页获取到 100 条记录
2025-05-08 00:00:16,650 - INFO - Request Parameters - Page 2:
2025-05-08 00:00:16,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:16,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:17,135 - INFO - Response - Page 2:
2025-05-08 00:00:17,337 - INFO - 第 2 页获取到 100 条记录
2025-05-08 00:00:17,337 - INFO - Request Parameters - Page 3:
2025-05-08 00:00:17,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:17,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:17,815 - INFO - Response - Page 3:
2025-05-08 00:00:18,016 - INFO - 第 3 页获取到 100 条记录
2025-05-08 00:00:18,016 - INFO - Request Parameters - Page 4:
2025-05-08 00:00:18,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:18,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:18,502 - INFO - Response - Page 4:
2025-05-08 00:00:18,702 - INFO - 第 4 页获取到 100 条记录
2025-05-08 00:00:18,702 - INFO - Request Parameters - Page 5:
2025-05-08 00:00:18,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:18,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:19,105 - INFO - Response - Page 5:
2025-05-08 00:00:19,306 - INFO - 第 5 页获取到 100 条记录
2025-05-08 00:00:19,306 - INFO - Request Parameters - Page 6:
2025-05-08 00:00:19,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:19,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:19,777 - INFO - Response - Page 6:
2025-05-08 00:00:19,978 - INFO - 第 6 页获取到 100 条记录
2025-05-08 00:00:19,978 - INFO - Request Parameters - Page 7:
2025-05-08 00:00:19,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:19,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:20,431 - INFO - Response - Page 7:
2025-05-08 00:00:20,631 - INFO - 第 7 页获取到 61 条记录
2025-05-08 00:00:20,631 - INFO - 查询完成，共获取到 661 条记录
2025-05-08 00:00:20,631 - INFO - 获取到 661 条表单数据
2025-05-08 00:00:20,644 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-08 00:00:20,657 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 00:00:20,657 - INFO - 开始处理日期: 2025-04
2025-05-08 00:00:20,657 - INFO - Request Parameters - Page 1:
2025-05-08 00:00:20,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:20,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:21,197 - INFO - Response - Page 1:
2025-05-08 00:00:21,398 - INFO - 第 1 页获取到 100 条记录
2025-05-08 00:00:21,398 - INFO - Request Parameters - Page 2:
2025-05-08 00:00:21,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:21,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:21,894 - INFO - Response - Page 2:
2025-05-08 00:00:22,095 - INFO - 第 2 页获取到 100 条记录
2025-05-08 00:00:22,095 - INFO - Request Parameters - Page 3:
2025-05-08 00:00:22,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:22,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:22,552 - INFO - Response - Page 3:
2025-05-08 00:00:22,753 - INFO - 第 3 页获取到 100 条记录
2025-05-08 00:00:22,753 - INFO - Request Parameters - Page 4:
2025-05-08 00:00:22,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:22,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:23,233 - INFO - Response - Page 4:
2025-05-08 00:00:23,433 - INFO - 第 4 页获取到 100 条记录
2025-05-08 00:00:23,433 - INFO - Request Parameters - Page 5:
2025-05-08 00:00:23,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:23,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:23,990 - INFO - Response - Page 5:
2025-05-08 00:00:24,191 - INFO - 第 5 页获取到 100 条记录
2025-05-08 00:00:24,191 - INFO - Request Parameters - Page 6:
2025-05-08 00:00:24,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:24,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:24,664 - INFO - Response - Page 6:
2025-05-08 00:00:24,865 - INFO - 第 6 页获取到 100 条记录
2025-05-08 00:00:24,865 - INFO - Request Parameters - Page 7:
2025-05-08 00:00:24,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:24,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:25,261 - INFO - Response - Page 7:
2025-05-08 00:00:25,461 - INFO - 第 7 页获取到 30 条记录
2025-05-08 00:00:25,461 - INFO - 查询完成，共获取到 630 条记录
2025-05-08 00:00:25,461 - INFO - 获取到 630 条表单数据
2025-05-08 00:00:25,473 - INFO - 当前日期 2025-04 有 630 条MySQL数据需要处理
2025-05-08 00:00:25,475 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-05-08 00:00:25,971 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-05-08 00:00:25,971 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80632.88, 'new_value': 80659.92}, {'field': 'offline_amount', 'old_value': 56409.49, 'new_value': 56409.51}, {'field': 'total_amount', 'old_value': 137042.37, 'new_value': 137069.43}]
2025-05-08 00:00:25,981 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-08 00:00:25,981 - INFO - 开始处理日期: 2025-05
2025-05-08 00:00:25,981 - INFO - Request Parameters - Page 1:
2025-05-08 00:00:25,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:25,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:26,526 - INFO - Response - Page 1:
2025-05-08 00:00:26,726 - INFO - 第 1 页获取到 100 条记录
2025-05-08 00:00:26,726 - INFO - Request Parameters - Page 2:
2025-05-08 00:00:26,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:26,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:27,185 - INFO - Response - Page 2:
2025-05-08 00:00:27,385 - INFO - 第 2 页获取到 100 条记录
2025-05-08 00:00:27,385 - INFO - Request Parameters - Page 3:
2025-05-08 00:00:27,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:27,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:27,813 - INFO - Response - Page 3:
2025-05-08 00:00:28,013 - INFO - 第 3 页获取到 100 条记录
2025-05-08 00:00:28,013 - INFO - Request Parameters - Page 4:
2025-05-08 00:00:28,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:28,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:28,506 - INFO - Response - Page 4:
2025-05-08 00:00:28,706 - INFO - 第 4 页获取到 100 条记录
2025-05-08 00:00:28,706 - INFO - Request Parameters - Page 5:
2025-05-08 00:00:28,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:28,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:29,249 - INFO - Response - Page 5:
2025-05-08 00:00:29,449 - INFO - 第 5 页获取到 100 条记录
2025-05-08 00:00:29,449 - INFO - Request Parameters - Page 6:
2025-05-08 00:00:29,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:29,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:29,955 - INFO - Response - Page 6:
2025-05-08 00:00:30,157 - INFO - 第 6 页获取到 100 条记录
2025-05-08 00:00:30,157 - INFO - Request Parameters - Page 7:
2025-05-08 00:00:30,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 00:00:30,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 00:00:30,517 - INFO - Response - Page 7:
2025-05-08 00:00:30,717 - INFO - 第 7 页获取到 21 条记录
2025-05-08 00:00:30,717 - INFO - 查询完成，共获取到 621 条记录
2025-05-08 00:00:30,717 - INFO - 获取到 621 条表单数据
2025-05-08 00:00:30,730 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-08 00:00:30,730 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-08 00:00:31,138 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-08 00:00:31,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15504.78, 'new_value': 17907.68}, {'field': 'total_amount', 'old_value': 15504.78, 'new_value': 17907.68}, {'field': 'order_count', 'old_value': 36, 'new_value': 40}]
2025-05-08 00:00:31,139 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-08 00:00:31,605 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-08 00:00:31,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5100.0, 'new_value': 10200.0}, {'field': 'total_amount', 'old_value': 5100.0, 'new_value': 10200.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-08 00:00:31,605 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-08 00:00:32,065 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-08 00:00:32,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35822.0, 'new_value': 42603.0}, {'field': 'offline_amount', 'old_value': 24268.28, 'new_value': 28790.28}, {'field': 'total_amount', 'old_value': 60090.28, 'new_value': 71393.28}, {'field': 'order_count', 'old_value': 1244, 'new_value': 1498}]
2025-05-08 00:00:32,066 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-08 00:00:32,521 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-08 00:00:32,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1220.0, 'new_value': 4120.0}, {'field': 'total_amount', 'old_value': 7980.0, 'new_value': 10880.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 109}]
2025-05-08 00:00:32,521 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-08 00:00:32,870 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-08 00:00:32,871 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 528.0, 'new_value': 689.9}, {'field': 'total_amount', 'old_value': 24033.9, 'new_value': 24195.8}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-05-08 00:00:32,871 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-08 00:00:33,259 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-08 00:00:33,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 279828.0, 'new_value': 314397.0}, {'field': 'offline_amount', 'old_value': 84345.0, 'new_value': 102005.0}, {'field': 'total_amount', 'old_value': 364173.0, 'new_value': 416402.0}, {'field': 'order_count', 'old_value': 382, 'new_value': 451}]
2025-05-08 00:00:33,259 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-08 00:00:33,699 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-08 00:00:33,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1599.0, 'new_value': 3898.0}, {'field': 'total_amount', 'old_value': 1599.0, 'new_value': 3898.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-08 00:00:33,699 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-08 00:00:34,154 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-08 00:00:34,154 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3971.45, 'new_value': 4030.45}, {'field': 'offline_amount', 'old_value': 48468.0, 'new_value': 48916.0}, {'field': 'total_amount', 'old_value': 52439.45, 'new_value': 52946.45}, {'field': 'order_count', 'old_value': 1023, 'new_value': 1030}]
2025-05-08 00:00:34,154 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-08 00:00:34,688 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-08 00:00:34,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19948.0, 'new_value': 23938.0}, {'field': 'offline_amount', 'old_value': 26717.0, 'new_value': 31699.0}, {'field': 'total_amount', 'old_value': 46665.0, 'new_value': 55637.0}, {'field': 'order_count', 'old_value': 1128, 'new_value': 1315}]
2025-05-08 00:00:34,688 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-08 00:00:35,093 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-08 00:00:35,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79480.0, 'new_value': 84815.0}, {'field': 'total_amount', 'old_value': 79480.0, 'new_value': 84815.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-08 00:00:35,094 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-08 00:00:35,528 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-08 00:00:35,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98650.89, 'new_value': 103150.89}, {'field': 'total_amount', 'old_value': 98650.89, 'new_value': 103150.89}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-08 00:00:35,528 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-08 00:00:35,906 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-08 00:00:35,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98640.13, 'new_value': 101390.13}, {'field': 'total_amount', 'old_value': 138000.13, 'new_value': 140750.13}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-08 00:00:35,907 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-08 00:00:36,360 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-08 00:00:36,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12732.8, 'new_value': 12832.7}, {'field': 'total_amount', 'old_value': 33184.4, 'new_value': 33284.3}, {'field': 'order_count', 'old_value': 493, 'new_value': 494}]
2025-05-08 00:00:36,360 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-08 00:00:36,802 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-08 00:00:36,802 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2598.0, 'new_value': 4405.2}, {'field': 'offline_amount', 'old_value': 27016.2, 'new_value': 29228.3}, {'field': 'total_amount', 'old_value': 29614.2, 'new_value': 33633.5}, {'field': 'order_count', 'old_value': 65, 'new_value': 76}]
2025-05-08 00:00:36,802 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-08 00:00:37,225 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-08 00:00:37,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4383.76, 'new_value': 7081.41}, {'field': 'total_amount', 'old_value': 4383.76, 'new_value': 7081.41}, {'field': 'order_count', 'old_value': 369, 'new_value': 573}]
2025-05-08 00:00:37,225 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-08 00:00:37,602 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-08 00:00:37,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11884.0, 'new_value': 13931.0}, {'field': 'total_amount', 'old_value': 14228.0, 'new_value': 16275.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 61}]
2025-05-08 00:00:37,602 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-08 00:00:38,015 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-08 00:00:38,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28199.38, 'new_value': 29474.88}, {'field': 'total_amount', 'old_value': 28199.38, 'new_value': 29474.88}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-05-08 00:00:38,015 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-08 00:00:38,495 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-08 00:00:38,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 689.0, 'new_value': 1069.0}, {'field': 'total_amount', 'old_value': 689.0, 'new_value': 1069.0}, {'field': 'order_count', 'old_value': 290, 'new_value': 291}]
2025-05-08 00:00:38,495 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-08 00:00:38,844 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-08 00:00:38,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15149.6, 'new_value': 16474.0}, {'field': 'total_amount', 'old_value': 15149.6, 'new_value': 16474.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 88}]
2025-05-08 00:00:38,845 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-08 00:00:39,206 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-08 00:00:39,206 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12066.97, 'new_value': 13524.89}, {'field': 'offline_amount', 'old_value': 167554.67, 'new_value': 191078.67}, {'field': 'total_amount', 'old_value': 179621.64, 'new_value': 204603.56}, {'field': 'order_count', 'old_value': 733, 'new_value': 838}]
2025-05-08 00:00:39,206 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-08 00:00:39,627 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-08 00:00:39,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21559.0, 'new_value': 23557.0}, {'field': 'total_amount', 'old_value': 25231.32, 'new_value': 27229.32}, {'field': 'order_count', 'old_value': 414, 'new_value': 416}]
2025-05-08 00:00:39,628 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-08 00:00:40,046 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-08 00:00:40,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17954.12, 'new_value': 21968.05}, {'field': 'offline_amount', 'old_value': 8938.69, 'new_value': 10644.59}, {'field': 'total_amount', 'old_value': 26892.81, 'new_value': 32612.64}, {'field': 'order_count', 'old_value': 822, 'new_value': 1034}]
2025-05-08 00:00:40,046 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-08 00:00:40,431 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-08 00:00:40,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5207.05, 'new_value': 5354.05}, {'field': 'total_amount', 'old_value': 5207.05, 'new_value': 5354.05}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-05-08 00:00:40,432 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-08 00:00:40,850 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-08 00:00:40,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11802.0, 'new_value': 12939.0}, {'field': 'total_amount', 'old_value': 11802.0, 'new_value': 12939.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-08 00:00:40,850 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-08 00:00:41,279 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-08 00:00:41,279 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13996.48, 'new_value': 15907.69}, {'field': 'offline_amount', 'old_value': 33735.23, 'new_value': 36504.55}, {'field': 'total_amount', 'old_value': 47731.71, 'new_value': 52412.24}, {'field': 'order_count', 'old_value': 1525, 'new_value': 1696}]
2025-05-08 00:00:41,280 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-08 00:00:41,682 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-08 00:00:41,682 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20586.0, 'new_value': 26485.0}, {'field': 'total_amount', 'old_value': 45741.0, 'new_value': 51640.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-08 00:00:41,682 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-08 00:00:42,034 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-08 00:00:42,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143277.0, 'new_value': 150871.0}, {'field': 'total_amount', 'old_value': 143277.0, 'new_value': 150871.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 55}]
2025-05-08 00:00:42,035 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-08 00:00:42,479 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-08 00:00:42,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44093.83, 'new_value': 51109.83}, {'field': 'total_amount', 'old_value': 44953.38, 'new_value': 51969.38}, {'field': 'order_count', 'old_value': 189, 'new_value': 211}]
2025-05-08 00:00:42,480 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-08 00:00:42,957 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-08 00:00:42,957 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48681.27, 'new_value': 55803.28}, {'field': 'offline_amount', 'old_value': 156109.78, 'new_value': 162666.87}, {'field': 'total_amount', 'old_value': 204791.05, 'new_value': 218470.15}, {'field': 'order_count', 'old_value': 1322, 'new_value': 1478}]
2025-05-08 00:00:42,957 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-08 00:00:43,372 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-08 00:00:43,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15595.9, 'new_value': 16203.9}, {'field': 'total_amount', 'old_value': 15595.9, 'new_value': 16203.9}, {'field': 'order_count', 'old_value': 71, 'new_value': 73}]
2025-05-08 00:00:43,372 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-08 00:00:43,808 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-08 00:00:43,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36407.0, 'new_value': 44970.0}, {'field': 'total_amount', 'old_value': 36408.0, 'new_value': 44971.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-08 00:00:43,808 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-08 00:00:44,184 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-08 00:00:44,184 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50864.49, 'new_value': 50894.39}, {'field': 'total_amount', 'old_value': 50864.49, 'new_value': 50894.39}, {'field': 'order_count', 'old_value': 242, 'new_value': 245}]
2025-05-08 00:00:44,184 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-08 00:00:44,569 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-08 00:00:44,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22834.0, 'new_value': 26214.0}, {'field': 'total_amount', 'old_value': 22963.0, 'new_value': 26343.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-08 00:00:44,570 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-08 00:00:45,003 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-08 00:00:45,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2989.0, 'new_value': 3542.9}, {'field': 'offline_amount', 'old_value': 9756.0, 'new_value': 10756.0}, {'field': 'total_amount', 'old_value': 12745.0, 'new_value': 14298.9}, {'field': 'order_count', 'old_value': 14, 'new_value': 19}]
2025-05-08 00:00:45,003 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-08 00:00:45,427 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-08 00:00:45,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57403.1, 'new_value': 60394.7}, {'field': 'total_amount', 'old_value': 57403.1, 'new_value': 60394.7}, {'field': 'order_count', 'old_value': 115, 'new_value': 121}]
2025-05-08 00:00:45,427 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-08 00:00:45,828 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-08 00:00:45,828 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76219.62, 'new_value': 81952.73}, {'field': 'offline_amount', 'old_value': 34130.82, 'new_value': 36152.34}, {'field': 'total_amount', 'old_value': 110350.44, 'new_value': 118105.07}, {'field': 'order_count', 'old_value': 438, 'new_value': 469}]
2025-05-08 00:00:45,828 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-08 00:00:46,249 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-08 00:00:46,250 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107029.42, 'new_value': 112999.34}, {'field': 'total_amount', 'old_value': 110758.64, 'new_value': 116728.56}, {'field': 'order_count', 'old_value': 668, 'new_value': 734}]
2025-05-08 00:00:46,250 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-08 00:00:46,672 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-08 00:00:46,672 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4675.31, 'new_value': 5580.3}, {'field': 'offline_amount', 'old_value': 91451.26, 'new_value': 93969.91}, {'field': 'total_amount', 'old_value': 96126.57, 'new_value': 99550.21}, {'field': 'order_count', 'old_value': 460, 'new_value': 477}]
2025-05-08 00:00:46,673 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-08 00:00:47,099 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-08 00:00:47,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9182.23, 'new_value': 9995.35}, {'field': 'offline_amount', 'old_value': 175629.91, 'new_value': 184668.51}, {'field': 'total_amount', 'old_value': 184812.14, 'new_value': 194663.86}, {'field': 'order_count', 'old_value': 1162, 'new_value': 1238}]
2025-05-08 00:00:47,100 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-08 00:00:47,479 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-08 00:00:47,479 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14581.0, 'new_value': 15175.0}, {'field': 'total_amount', 'old_value': 14581.0, 'new_value': 15175.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-05-08 00:00:47,479 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-08 00:00:47,931 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-08 00:00:47,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 235.0}, {'field': 'offline_amount', 'old_value': 19831.0, 'new_value': 19857.0}, {'field': 'total_amount', 'old_value': 19831.0, 'new_value': 20092.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 79}]
2025-05-08 00:00:47,931 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-08 00:00:48,335 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-08 00:00:48,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130934.6, 'new_value': 134303.6}, {'field': 'total_amount', 'old_value': 130934.6, 'new_value': 134303.6}, {'field': 'order_count', 'old_value': 1465, 'new_value': 1499}]
2025-05-08 00:00:48,335 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-08 00:00:48,716 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-08 00:00:48,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 586038.68, 'new_value': 625933.17}, {'field': 'total_amount', 'old_value': 586038.68, 'new_value': 625933.17}, {'field': 'order_count', 'old_value': 3958, 'new_value': 4451}]
2025-05-08 00:00:48,717 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-08 00:00:49,192 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-08 00:00:49,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4410.21, 'new_value': 16064.87}, {'field': 'total_amount', 'old_value': 203587.74, 'new_value': 215242.4}, {'field': 'order_count', 'old_value': 769, 'new_value': 804}]
2025-05-08 00:00:49,192 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-08 00:00:49,610 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-08 00:00:49,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2823.0, 'new_value': 7983.0}, {'field': 'total_amount', 'old_value': 2823.0, 'new_value': 7983.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-08 00:00:49,610 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-08 00:00:50,043 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-08 00:00:50,044 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27860.0, 'new_value': 30740.0}, {'field': 'total_amount', 'old_value': 27860.0, 'new_value': 30740.0}, {'field': 'order_count', 'old_value': 1203, 'new_value': 1417}]
2025-05-08 00:00:50,045 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-08 00:00:50,520 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-08 00:00:50,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18325.8, 'new_value': 19137.2}, {'field': 'total_amount', 'old_value': 18847.0, 'new_value': 19658.4}, {'field': 'order_count', 'old_value': 58, 'new_value': 62}]
2025-05-08 00:00:50,520 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-08 00:00:50,915 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-08 00:00:50,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46913.0, 'new_value': 82469.0}, {'field': 'total_amount', 'old_value': 46913.0, 'new_value': 82469.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 33}]
2025-05-08 00:00:50,915 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-08 00:00:51,338 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-08 00:00:51,339 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18305.2, 'new_value': 21504.71}, {'field': 'offline_amount', 'old_value': 80113.48, 'new_value': 86061.07}, {'field': 'total_amount', 'old_value': 98418.68, 'new_value': 107565.78}, {'field': 'order_count', 'old_value': 1207, 'new_value': 1281}]
2025-05-08 00:00:51,339 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-08 00:00:51,759 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-08 00:00:51,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 550.0}, {'field': 'offline_amount', 'old_value': 8221.02, 'new_value': 8487.31}, {'field': 'total_amount', 'old_value': 8221.02, 'new_value': 9037.31}, {'field': 'order_count', 'old_value': 155, 'new_value': 171}]
2025-05-08 00:00:51,760 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-08 00:00:52,177 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-08 00:00:52,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10339.21, 'new_value': 12154.51}, {'field': 'total_amount', 'old_value': 10339.21, 'new_value': 12154.51}, {'field': 'order_count', 'old_value': 41, 'new_value': 48}]
2025-05-08 00:00:52,177 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-08 00:00:52,664 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-08 00:00:52,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41632.0, 'new_value': 43931.0}, {'field': 'total_amount', 'old_value': 50556.6, 'new_value': 52855.6}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-08 00:00:52,665 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-08 00:00:53,086 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-08 00:00:53,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96655.0, 'new_value': 98366.0}, {'field': 'total_amount', 'old_value': 96655.0, 'new_value': 98366.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-08 00:00:53,087 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-08 00:00:53,518 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-08 00:00:53,518 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2494.0, 'new_value': 2918.0}, {'field': 'total_amount', 'old_value': 2494.0, 'new_value': 2918.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 21}]
2025-05-08 00:00:53,519 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-08 00:00:53,972 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-08 00:00:53,972 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4617.8, 'new_value': 4973.7}, {'field': 'total_amount', 'old_value': 5818.8, 'new_value': 6174.7}, {'field': 'order_count', 'old_value': 32, 'new_value': 35}]
2025-05-08 00:00:53,972 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-08 00:00:54,399 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-08 00:00:54,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111593.0, 'new_value': 111843.0}, {'field': 'total_amount', 'old_value': 111593.0, 'new_value': 111843.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-08 00:00:54,400 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-08 00:00:54,868 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-08 00:00:54,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111429.61, 'new_value': 116976.85}, {'field': 'total_amount', 'old_value': 111429.61, 'new_value': 116976.85}, {'field': 'order_count', 'old_value': 831, 'new_value': 902}]
2025-05-08 00:00:54,869 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-08 00:00:55,304 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-08 00:00:55,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40885.0, 'new_value': 41185.0}, {'field': 'total_amount', 'old_value': 40885.0, 'new_value': 41185.0}, {'field': 'order_count', 'old_value': 185, 'new_value': 187}]
2025-05-08 00:00:55,304 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-08 00:00:55,751 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-08 00:00:55,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10063.0, 'new_value': 10657.0}, {'field': 'total_amount', 'old_value': 10315.0, 'new_value': 10909.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 98}]
2025-05-08 00:00:55,751 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-08 00:00:56,153 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-08 00:00:56,153 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19397.07, 'new_value': 22917.09}, {'field': 'offline_amount', 'old_value': 51064.32, 'new_value': 61187.85}, {'field': 'total_amount', 'old_value': 70461.39, 'new_value': 84104.94}, {'field': 'order_count', 'old_value': 2950, 'new_value': 3611}]
2025-05-08 00:00:56,154 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-08 00:00:56,563 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-08 00:00:56,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14807.57, 'new_value': 17350.78}, {'field': 'offline_amount', 'old_value': 146344.3, 'new_value': 155066.02}, {'field': 'total_amount', 'old_value': 161151.87, 'new_value': 172416.8}, {'field': 'order_count', 'old_value': 640, 'new_value': 693}]
2025-05-08 00:00:56,563 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-08 00:00:56,908 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-08 00:00:56,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241825.37, 'new_value': 257750.91}, {'field': 'total_amount', 'old_value': 241825.37, 'new_value': 257750.91}, {'field': 'order_count', 'old_value': 1503, 'new_value': 1630}]
2025-05-08 00:00:56,908 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-08 00:00:57,332 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-08 00:00:57,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59403.4, 'new_value': 60925.2}, {'field': 'total_amount', 'old_value': 59403.4, 'new_value': 60925.2}, {'field': 'order_count', 'old_value': 100, 'new_value': 104}]
2025-05-08 00:00:57,332 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-08 00:00:57,855 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-08 00:00:57,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65238.0, 'new_value': 66206.0}, {'field': 'total_amount', 'old_value': 65238.0, 'new_value': 66206.0}, {'field': 'order_count', 'old_value': 2103, 'new_value': 2131}]
2025-05-08 00:00:57,855 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-08 00:00:58,245 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-08 00:00:58,246 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17805.67, 'new_value': 17813.67}, {'field': 'offline_amount', 'old_value': 150784.29, 'new_value': 160456.29}, {'field': 'total_amount', 'old_value': 168589.96, 'new_value': 178269.96}, {'field': 'order_count', 'old_value': 987, 'new_value': 1011}]
2025-05-08 00:00:58,246 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-08 00:00:58,651 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-08 00:00:58,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117090.82, 'new_value': 120611.74}, {'field': 'total_amount', 'old_value': 125221.36, 'new_value': 128742.28}, {'field': 'order_count', 'old_value': 220, 'new_value': 228}]
2025-05-08 00:00:58,651 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-08 00:00:59,048 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-08 00:00:59,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36712.0, 'new_value': 38743.0}, {'field': 'offline_amount', 'old_value': 33786.42, 'new_value': 33987.32}, {'field': 'total_amount', 'old_value': 70498.42, 'new_value': 72730.32}, {'field': 'order_count', 'old_value': 464, 'new_value': 479}]
2025-05-08 00:00:59,048 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-08 00:00:59,497 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-08 00:00:59,497 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79392.56, 'new_value': 86218.25}, {'field': 'offline_amount', 'old_value': 440948.23, 'new_value': 466939.73}, {'field': 'total_amount', 'old_value': 520340.79, 'new_value': 553157.98}, {'field': 'order_count', 'old_value': 2456, 'new_value': 2643}]
2025-05-08 00:00:59,497 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-08 00:00:59,902 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-08 00:00:59,903 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1000.0, 'new_value': 4000.0}, {'field': 'offline_amount', 'old_value': 88526.0, 'new_value': 105409.0}, {'field': 'total_amount', 'old_value': 89526.0, 'new_value': 109409.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 65}]
2025-05-08 00:00:59,903 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-08 00:01:00,343 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-08 00:01:00,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132867.6, 'new_value': 135795.6}, {'field': 'offline_amount', 'old_value': 206548.4, 'new_value': 209840.4}, {'field': 'total_amount', 'old_value': 339416.0, 'new_value': 345636.0}, {'field': 'order_count', 'old_value': 2752, 'new_value': 2797}]
2025-05-08 00:01:00,343 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-08 00:01:00,778 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-08 00:01:00,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8647.2, 'new_value': 10072.2}, {'field': 'offline_amount', 'old_value': 124165.82, 'new_value': 130294.82}, {'field': 'total_amount', 'old_value': 132813.02, 'new_value': 140367.02}, {'field': 'order_count', 'old_value': 774, 'new_value': 807}]
2025-05-08 00:01:00,779 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-08 00:01:01,137 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-08 00:01:01,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12161.0, 'new_value': 13650.0}, {'field': 'total_amount', 'old_value': 12161.0, 'new_value': 13650.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-05-08 00:01:01,137 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-08 00:01:01,550 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-08 00:01:01,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12085.0, 'new_value': 12290.0}, {'field': 'total_amount', 'old_value': 12085.0, 'new_value': 12290.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 71}]
2025-05-08 00:01:01,551 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-08 00:01:01,969 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-08 00:01:01,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17166.0, 'new_value': 18308.0}, {'field': 'total_amount', 'old_value': 17166.0, 'new_value': 18308.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 00:01:01,970 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-08 00:01:02,397 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-08 00:01:02,397 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3792.1, 'new_value': 4369.0}, {'field': 'offline_amount', 'old_value': 42945.7, 'new_value': 48824.1}, {'field': 'total_amount', 'old_value': 46737.8, 'new_value': 53193.1}, {'field': 'order_count', 'old_value': 1378, 'new_value': 1627}]
2025-05-08 00:01:02,398 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-08 00:01:02,828 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-08 00:01:02,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63013.0, 'new_value': 66459.9}, {'field': 'total_amount', 'old_value': 63013.0, 'new_value': 66459.9}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-08 00:01:02,829 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-08 00:01:03,170 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-08 00:01:03,170 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129077.0, 'new_value': 140656.0}, {'field': 'total_amount', 'old_value': 129575.0, 'new_value': 141154.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 31}]
2025-05-08 00:01:03,171 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-08 00:01:03,568 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-08 00:01:03,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15306.0, 'new_value': 18846.0}, {'field': 'total_amount', 'old_value': 19305.0, 'new_value': 22845.0}, {'field': 'order_count', 'old_value': 1238, 'new_value': 1538}]
2025-05-08 00:01:03,569 - INFO - 日期 2025-05 处理完成 - 更新: 78 条，插入: 0 条，错误: 0 条
2025-05-08 00:01:03,569 - INFO - 数据同步完成！更新: 79 条，插入: 0 条，错误: 0 条
2025-05-08 00:01:03,570 - INFO - =================同步完成====================
2025-05-08 03:00:04,196 - INFO - =================使用默认全量同步=============
2025-05-08 03:00:05,411 - INFO - MySQL查询成功，共获取 3264 条记录
2025-05-08 03:00:05,412 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-08 03:00:05,438 - INFO - 开始处理日期: 2025-01
2025-05-08 03:00:05,441 - INFO - Request Parameters - Page 1:
2025-05-08 03:00:05,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:05,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:06,515 - INFO - Response - Page 1:
2025-05-08 03:00:06,715 - INFO - 第 1 页获取到 100 条记录
2025-05-08 03:00:06,715 - INFO - Request Parameters - Page 2:
2025-05-08 03:00:06,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:06,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:07,475 - INFO - Response - Page 2:
2025-05-08 03:00:07,675 - INFO - 第 2 页获取到 100 条记录
2025-05-08 03:00:07,675 - INFO - Request Parameters - Page 3:
2025-05-08 03:00:07,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:07,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:08,257 - INFO - Response - Page 3:
2025-05-08 03:00:08,457 - INFO - 第 3 页获取到 100 条记录
2025-05-08 03:00:08,457 - INFO - Request Parameters - Page 4:
2025-05-08 03:00:08,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:08,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:08,935 - INFO - Response - Page 4:
2025-05-08 03:00:09,135 - INFO - 第 4 页获取到 100 条记录
2025-05-08 03:00:09,135 - INFO - Request Parameters - Page 5:
2025-05-08 03:00:09,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:09,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:09,591 - INFO - Response - Page 5:
2025-05-08 03:00:09,791 - INFO - 第 5 页获取到 100 条记录
2025-05-08 03:00:09,791 - INFO - Request Parameters - Page 6:
2025-05-08 03:00:09,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:09,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:10,290 - INFO - Response - Page 6:
2025-05-08 03:00:10,490 - INFO - 第 6 页获取到 100 条记录
2025-05-08 03:00:10,490 - INFO - Request Parameters - Page 7:
2025-05-08 03:00:10,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:10,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:10,951 - INFO - Response - Page 7:
2025-05-08 03:00:11,151 - INFO - 第 7 页获取到 82 条记录
2025-05-08 03:00:11,151 - INFO - 查询完成，共获取到 682 条记录
2025-05-08 03:00:11,151 - INFO - 获取到 682 条表单数据
2025-05-08 03:00:11,163 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-08 03:00:11,175 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 03:00:11,175 - INFO - 开始处理日期: 2025-02
2025-05-08 03:00:11,175 - INFO - Request Parameters - Page 1:
2025-05-08 03:00:11,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:11,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:11,696 - INFO - Response - Page 1:
2025-05-08 03:00:11,896 - INFO - 第 1 页获取到 100 条记录
2025-05-08 03:00:11,896 - INFO - Request Parameters - Page 2:
2025-05-08 03:00:11,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:11,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:12,417 - INFO - Response - Page 2:
2025-05-08 03:00:12,618 - INFO - 第 2 页获取到 100 条记录
2025-05-08 03:00:12,618 - INFO - Request Parameters - Page 3:
2025-05-08 03:00:12,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:12,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:13,233 - INFO - Response - Page 3:
2025-05-08 03:00:13,433 - INFO - 第 3 页获取到 100 条记录
2025-05-08 03:00:13,433 - INFO - Request Parameters - Page 4:
2025-05-08 03:00:13,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:13,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:13,926 - INFO - Response - Page 4:
2025-05-08 03:00:14,126 - INFO - 第 4 页获取到 100 条记录
2025-05-08 03:00:14,126 - INFO - Request Parameters - Page 5:
2025-05-08 03:00:14,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:14,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:14,669 - INFO - Response - Page 5:
2025-05-08 03:00:14,869 - INFO - 第 5 页获取到 100 条记录
2025-05-08 03:00:14,869 - INFO - Request Parameters - Page 6:
2025-05-08 03:00:14,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:14,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:15,392 - INFO - Response - Page 6:
2025-05-08 03:00:15,592 - INFO - 第 6 页获取到 100 条记录
2025-05-08 03:00:15,592 - INFO - Request Parameters - Page 7:
2025-05-08 03:00:15,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:15,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:16,076 - INFO - Response - Page 7:
2025-05-08 03:00:16,277 - INFO - 第 7 页获取到 70 条记录
2025-05-08 03:00:16,277 - INFO - 查询完成，共获取到 670 条记录
2025-05-08 03:00:16,279 - INFO - 获取到 670 条表单数据
2025-05-08 03:00:16,296 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-08 03:00:16,314 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 03:00:16,314 - INFO - 开始处理日期: 2025-03
2025-05-08 03:00:16,315 - INFO - Request Parameters - Page 1:
2025-05-08 03:00:16,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:16,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:16,816 - INFO - Response - Page 1:
2025-05-08 03:00:17,017 - INFO - 第 1 页获取到 100 条记录
2025-05-08 03:00:17,017 - INFO - Request Parameters - Page 2:
2025-05-08 03:00:17,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:17,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:17,465 - INFO - Response - Page 2:
2025-05-08 03:00:17,666 - INFO - 第 2 页获取到 100 条记录
2025-05-08 03:00:17,666 - INFO - Request Parameters - Page 3:
2025-05-08 03:00:17,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:17,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:18,259 - INFO - Response - Page 3:
2025-05-08 03:00:18,460 - INFO - 第 3 页获取到 100 条记录
2025-05-08 03:00:18,460 - INFO - Request Parameters - Page 4:
2025-05-08 03:00:18,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:18,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:18,910 - INFO - Response - Page 4:
2025-05-08 03:00:19,112 - INFO - 第 4 页获取到 100 条记录
2025-05-08 03:00:19,112 - INFO - Request Parameters - Page 5:
2025-05-08 03:00:19,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:19,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:19,580 - INFO - Response - Page 5:
2025-05-08 03:00:19,780 - INFO - 第 5 页获取到 100 条记录
2025-05-08 03:00:19,780 - INFO - Request Parameters - Page 6:
2025-05-08 03:00:19,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:19,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:20,251 - INFO - Response - Page 6:
2025-05-08 03:00:20,451 - INFO - 第 6 页获取到 100 条记录
2025-05-08 03:00:20,451 - INFO - Request Parameters - Page 7:
2025-05-08 03:00:20,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:20,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:20,861 - INFO - Response - Page 7:
2025-05-08 03:00:21,061 - INFO - 第 7 页获取到 61 条记录
2025-05-08 03:00:21,061 - INFO - 查询完成，共获取到 661 条记录
2025-05-08 03:00:21,061 - INFO - 获取到 661 条表单数据
2025-05-08 03:00:21,074 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-08 03:00:21,085 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 03:00:21,085 - INFO - 开始处理日期: 2025-04
2025-05-08 03:00:21,085 - INFO - Request Parameters - Page 1:
2025-05-08 03:00:21,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:21,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:21,607 - INFO - Response - Page 1:
2025-05-08 03:00:21,808 - INFO - 第 1 页获取到 100 条记录
2025-05-08 03:00:21,808 - INFO - Request Parameters - Page 2:
2025-05-08 03:00:21,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:21,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:22,257 - INFO - Response - Page 2:
2025-05-08 03:00:22,457 - INFO - 第 2 页获取到 100 条记录
2025-05-08 03:00:22,457 - INFO - Request Parameters - Page 3:
2025-05-08 03:00:22,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:22,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:22,963 - INFO - Response - Page 3:
2025-05-08 03:00:23,164 - INFO - 第 3 页获取到 100 条记录
2025-05-08 03:00:23,164 - INFO - Request Parameters - Page 4:
2025-05-08 03:00:23,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:23,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:23,645 - INFO - Response - Page 4:
2025-05-08 03:00:23,845 - INFO - 第 4 页获取到 100 条记录
2025-05-08 03:00:23,845 - INFO - Request Parameters - Page 5:
2025-05-08 03:00:23,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:23,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:24,335 - INFO - Response - Page 5:
2025-05-08 03:00:24,535 - INFO - 第 5 页获取到 100 条记录
2025-05-08 03:00:24,535 - INFO - Request Parameters - Page 6:
2025-05-08 03:00:24,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:24,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:25,035 - INFO - Response - Page 6:
2025-05-08 03:00:25,235 - INFO - 第 6 页获取到 100 条记录
2025-05-08 03:00:25,235 - INFO - Request Parameters - Page 7:
2025-05-08 03:00:25,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:25,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:25,592 - INFO - Response - Page 7:
2025-05-08 03:00:25,793 - INFO - 第 7 页获取到 30 条记录
2025-05-08 03:00:25,793 - INFO - 查询完成，共获取到 630 条记录
2025-05-08 03:00:25,793 - INFO - 获取到 630 条表单数据
2025-05-08 03:00:25,805 - INFO - 当前日期 2025-04 有 630 条MySQL数据需要处理
2025-05-08 03:00:25,817 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 03:00:25,817 - INFO - 开始处理日期: 2025-05
2025-05-08 03:00:25,818 - INFO - Request Parameters - Page 1:
2025-05-08 03:00:25,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:25,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:26,290 - INFO - Response - Page 1:
2025-05-08 03:00:26,491 - INFO - 第 1 页获取到 100 条记录
2025-05-08 03:00:26,491 - INFO - Request Parameters - Page 2:
2025-05-08 03:00:26,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:26,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:27,012 - INFO - Response - Page 2:
2025-05-08 03:00:27,212 - INFO - 第 2 页获取到 100 条记录
2025-05-08 03:00:27,212 - INFO - Request Parameters - Page 3:
2025-05-08 03:00:27,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:27,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:27,730 - INFO - Response - Page 3:
2025-05-08 03:00:27,931 - INFO - 第 3 页获取到 100 条记录
2025-05-08 03:00:27,931 - INFO - Request Parameters - Page 4:
2025-05-08 03:00:27,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:27,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:28,413 - INFO - Response - Page 4:
2025-05-08 03:00:28,615 - INFO - 第 4 页获取到 100 条记录
2025-05-08 03:00:28,615 - INFO - Request Parameters - Page 5:
2025-05-08 03:00:28,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:28,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:29,220 - INFO - Response - Page 5:
2025-05-08 03:00:29,420 - INFO - 第 5 页获取到 100 条记录
2025-05-08 03:00:29,420 - INFO - Request Parameters - Page 6:
2025-05-08 03:00:29,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:29,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:29,898 - INFO - Response - Page 6:
2025-05-08 03:00:30,099 - INFO - 第 6 页获取到 100 条记录
2025-05-08 03:00:30,099 - INFO - Request Parameters - Page 7:
2025-05-08 03:00:30,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 03:00:30,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 03:00:30,398 - INFO - Response - Page 7:
2025-05-08 03:00:30,598 - INFO - 第 7 页获取到 21 条记录
2025-05-08 03:00:30,598 - INFO - 查询完成，共获取到 621 条记录
2025-05-08 03:00:30,598 - INFO - 获取到 621 条表单数据
2025-05-08 03:00:30,610 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-08 03:00:30,621 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 03:00:30,621 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 03:00:30,623 - INFO - =================同步完成====================
2025-05-08 06:00:03,924 - INFO - =================使用默认全量同步=============
2025-05-08 06:00:05,142 - INFO - MySQL查询成功，共获取 3287 条记录
2025-05-08 06:00:05,143 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-08 06:00:05,172 - INFO - 开始处理日期: 2025-01
2025-05-08 06:00:05,175 - INFO - Request Parameters - Page 1:
2025-05-08 06:00:05,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:05,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:06,272 - INFO - Response - Page 1:
2025-05-08 06:00:06,473 - INFO - 第 1 页获取到 100 条记录
2025-05-08 06:00:06,473 - INFO - Request Parameters - Page 2:
2025-05-08 06:00:06,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:06,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:07,111 - INFO - Response - Page 2:
2025-05-08 06:00:07,312 - INFO - 第 2 页获取到 100 条记录
2025-05-08 06:00:07,312 - INFO - Request Parameters - Page 3:
2025-05-08 06:00:07,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:07,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:07,823 - INFO - Response - Page 3:
2025-05-08 06:00:08,023 - INFO - 第 3 页获取到 100 条记录
2025-05-08 06:00:08,023 - INFO - Request Parameters - Page 4:
2025-05-08 06:00:08,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:08,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:08,528 - INFO - Response - Page 4:
2025-05-08 06:00:08,728 - INFO - 第 4 页获取到 100 条记录
2025-05-08 06:00:08,728 - INFO - Request Parameters - Page 5:
2025-05-08 06:00:08,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:08,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:09,444 - INFO - Response - Page 5:
2025-05-08 06:00:09,644 - INFO - 第 5 页获取到 100 条记录
2025-05-08 06:00:09,644 - INFO - Request Parameters - Page 6:
2025-05-08 06:00:09,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:09,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:10,176 - INFO - Response - Page 6:
2025-05-08 06:00:10,376 - INFO - 第 6 页获取到 100 条记录
2025-05-08 06:00:10,376 - INFO - Request Parameters - Page 7:
2025-05-08 06:00:10,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:10,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:10,855 - INFO - Response - Page 7:
2025-05-08 06:00:11,056 - INFO - 第 7 页获取到 82 条记录
2025-05-08 06:00:11,056 - INFO - 查询完成，共获取到 682 条记录
2025-05-08 06:00:11,056 - INFO - 获取到 682 条表单数据
2025-05-08 06:00:11,068 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-08 06:00:11,080 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 06:00:11,080 - INFO - 开始处理日期: 2025-02
2025-05-08 06:00:11,080 - INFO - Request Parameters - Page 1:
2025-05-08 06:00:11,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:11,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:11,599 - INFO - Response - Page 1:
2025-05-08 06:00:11,800 - INFO - 第 1 页获取到 100 条记录
2025-05-08 06:00:11,800 - INFO - Request Parameters - Page 2:
2025-05-08 06:00:11,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:11,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:12,255 - INFO - Response - Page 2:
2025-05-08 06:00:12,456 - INFO - 第 2 页获取到 100 条记录
2025-05-08 06:00:12,456 - INFO - Request Parameters - Page 3:
2025-05-08 06:00:12,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:12,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:12,973 - INFO - Response - Page 3:
2025-05-08 06:00:13,174 - INFO - 第 3 页获取到 100 条记录
2025-05-08 06:00:13,174 - INFO - Request Parameters - Page 4:
2025-05-08 06:00:13,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:13,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:13,686 - INFO - Response - Page 4:
2025-05-08 06:00:13,886 - INFO - 第 4 页获取到 100 条记录
2025-05-08 06:00:13,886 - INFO - Request Parameters - Page 5:
2025-05-08 06:00:13,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:13,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:14,389 - INFO - Response - Page 5:
2025-05-08 06:00:14,590 - INFO - 第 5 页获取到 100 条记录
2025-05-08 06:00:14,590 - INFO - Request Parameters - Page 6:
2025-05-08 06:00:14,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:14,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:15,132 - INFO - Response - Page 6:
2025-05-08 06:00:15,333 - INFO - 第 6 页获取到 100 条记录
2025-05-08 06:00:15,333 - INFO - Request Parameters - Page 7:
2025-05-08 06:00:15,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:15,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:15,838 - INFO - Response - Page 7:
2025-05-08 06:00:16,039 - INFO - 第 7 页获取到 70 条记录
2025-05-08 06:00:16,039 - INFO - 查询完成，共获取到 670 条记录
2025-05-08 06:00:16,039 - INFO - 获取到 670 条表单数据
2025-05-08 06:00:16,050 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-08 06:00:16,062 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 06:00:16,062 - INFO - 开始处理日期: 2025-03
2025-05-08 06:00:16,063 - INFO - Request Parameters - Page 1:
2025-05-08 06:00:16,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:16,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:16,561 - INFO - Response - Page 1:
2025-05-08 06:00:16,761 - INFO - 第 1 页获取到 100 条记录
2025-05-08 06:00:16,761 - INFO - Request Parameters - Page 2:
2025-05-08 06:00:16,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:16,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:17,249 - INFO - Response - Page 2:
2025-05-08 06:00:17,449 - INFO - 第 2 页获取到 100 条记录
2025-05-08 06:00:17,449 - INFO - Request Parameters - Page 3:
2025-05-08 06:00:17,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:17,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:17,910 - INFO - Response - Page 3:
2025-05-08 06:00:18,110 - INFO - 第 3 页获取到 100 条记录
2025-05-08 06:00:18,110 - INFO - Request Parameters - Page 4:
2025-05-08 06:00:18,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:18,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:18,551 - INFO - Response - Page 4:
2025-05-08 06:00:18,752 - INFO - 第 4 页获取到 100 条记录
2025-05-08 06:00:18,752 - INFO - Request Parameters - Page 5:
2025-05-08 06:00:18,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:18,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:19,312 - INFO - Response - Page 5:
2025-05-08 06:00:19,512 - INFO - 第 5 页获取到 100 条记录
2025-05-08 06:00:19,512 - INFO - Request Parameters - Page 6:
2025-05-08 06:00:19,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:19,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:20,001 - INFO - Response - Page 6:
2025-05-08 06:00:20,202 - INFO - 第 6 页获取到 100 条记录
2025-05-08 06:00:20,202 - INFO - Request Parameters - Page 7:
2025-05-08 06:00:20,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:20,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:20,634 - INFO - Response - Page 7:
2025-05-08 06:00:20,835 - INFO - 第 7 页获取到 61 条记录
2025-05-08 06:00:20,835 - INFO - 查询完成，共获取到 661 条记录
2025-05-08 06:00:20,835 - INFO - 获取到 661 条表单数据
2025-05-08 06:00:20,847 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-08 06:00:20,859 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 06:00:20,859 - INFO - 开始处理日期: 2025-04
2025-05-08 06:00:20,859 - INFO - Request Parameters - Page 1:
2025-05-08 06:00:20,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:20,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:21,355 - INFO - Response - Page 1:
2025-05-08 06:00:21,555 - INFO - 第 1 页获取到 100 条记录
2025-05-08 06:00:21,555 - INFO - Request Parameters - Page 2:
2025-05-08 06:00:21,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:21,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:22,123 - INFO - Response - Page 2:
2025-05-08 06:00:22,324 - INFO - 第 2 页获取到 100 条记录
2025-05-08 06:00:22,324 - INFO - Request Parameters - Page 3:
2025-05-08 06:00:22,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:22,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:22,787 - INFO - Response - Page 3:
2025-05-08 06:00:22,988 - INFO - 第 3 页获取到 100 条记录
2025-05-08 06:00:22,988 - INFO - Request Parameters - Page 4:
2025-05-08 06:00:22,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:22,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:23,464 - INFO - Response - Page 4:
2025-05-08 06:00:23,665 - INFO - 第 4 页获取到 100 条记录
2025-05-08 06:00:23,665 - INFO - Request Parameters - Page 5:
2025-05-08 06:00:23,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:23,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:24,217 - INFO - Response - Page 5:
2025-05-08 06:00:24,419 - INFO - 第 5 页获取到 100 条记录
2025-05-08 06:00:24,419 - INFO - Request Parameters - Page 6:
2025-05-08 06:00:24,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:24,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:24,881 - INFO - Response - Page 6:
2025-05-08 06:00:25,082 - INFO - 第 6 页获取到 100 条记录
2025-05-08 06:00:25,082 - INFO - Request Parameters - Page 7:
2025-05-08 06:00:25,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:25,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:25,440 - INFO - Response - Page 7:
2025-05-08 06:00:25,641 - INFO - 第 7 页获取到 30 条记录
2025-05-08 06:00:25,641 - INFO - 查询完成，共获取到 630 条记录
2025-05-08 06:00:25,641 - INFO - 获取到 630 条表单数据
2025-05-08 06:00:25,652 - INFO - 当前日期 2025-04 有 653 条MySQL数据需要处理
2025-05-08 06:00:25,664 - INFO - 开始批量插入 23 条新记录
2025-05-08 06:00:25,825 - INFO - 批量插入响应状态码: 200
2025-05-08 06:00:25,825 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 07 May 2025 21:59:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1116', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2D2A597A-A9DA-7C6E-8F62-8905BE2E7714', 'x-acs-trace-id': '01eeeac9e34d59d57d44081331381778', 'etag': '1UWjip1Wx2M7xghyOY749pQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-08 06:00:25,825 - INFO - 批量插入响应体: {'result': ['FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMM5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMN5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMO5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMP5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMQ5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMR5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMS5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMT5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMU5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMV5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMW5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMX5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMY5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMZ5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM06', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM16', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM26', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM36', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM46', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM56', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM66', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM76', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM86']}
2025-05-08 06:00:25,825 - INFO - 批量插入表单数据成功，批次 1，共 23 条记录
2025-05-08 06:00:25,825 - INFO - 成功插入的数据ID: ['FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMM5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMN5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMO5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMP5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMQ5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMR5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMS5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMT5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMU5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMV5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMW5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMX5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMY5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAMZ5', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM06', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM16', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM26', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM36', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM46', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM56', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM66', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM76', 'FINST-X2F66HC12U7V5D3QAP2ED5V7WBHU2Q9PEHEAM86']
2025-05-08 06:00:28,829 - INFO - 批量插入完成，共 23 条记录
2025-05-08 06:00:28,829 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 23 条，错误: 0 条
2025-05-08 06:00:28,829 - INFO - 开始处理日期: 2025-05
2025-05-08 06:00:28,829 - INFO - Request Parameters - Page 1:
2025-05-08 06:00:28,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:28,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:29,286 - INFO - Response - Page 1:
2025-05-08 06:00:29,486 - INFO - 第 1 页获取到 100 条记录
2025-05-08 06:00:29,486 - INFO - Request Parameters - Page 2:
2025-05-08 06:00:29,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:29,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:30,009 - INFO - Response - Page 2:
2025-05-08 06:00:30,210 - INFO - 第 2 页获取到 100 条记录
2025-05-08 06:00:30,211 - INFO - Request Parameters - Page 3:
2025-05-08 06:00:30,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:30,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:30,740 - INFO - Response - Page 3:
2025-05-08 06:00:30,941 - INFO - 第 3 页获取到 100 条记录
2025-05-08 06:00:30,941 - INFO - Request Parameters - Page 4:
2025-05-08 06:00:30,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:30,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:31,403 - INFO - Response - Page 4:
2025-05-08 06:00:31,604 - INFO - 第 4 页获取到 100 条记录
2025-05-08 06:00:31,604 - INFO - Request Parameters - Page 5:
2025-05-08 06:00:31,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:31,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:32,063 - INFO - Response - Page 5:
2025-05-08 06:00:32,264 - INFO - 第 5 页获取到 100 条记录
2025-05-08 06:00:32,264 - INFO - Request Parameters - Page 6:
2025-05-08 06:00:32,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:32,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:32,782 - INFO - Response - Page 6:
2025-05-08 06:00:32,983 - INFO - 第 6 页获取到 100 条记录
2025-05-08 06:00:32,983 - INFO - Request Parameters - Page 7:
2025-05-08 06:00:32,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 06:00:32,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 06:00:33,327 - INFO - Response - Page 7:
2025-05-08 06:00:33,528 - INFO - 第 7 页获取到 21 条记录
2025-05-08 06:00:33,528 - INFO - 查询完成，共获取到 621 条记录
2025-05-08 06:00:33,528 - INFO - 获取到 621 条表单数据
2025-05-08 06:00:33,541 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-08 06:00:33,552 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 06:00:33,552 - INFO - 数据同步完成！更新: 0 条，插入: 23 条，错误: 0 条
2025-05-08 06:00:33,553 - INFO - =================同步完成====================
2025-05-08 09:00:01,879 - INFO - =================使用默认全量同步=============
2025-05-08 09:00:03,110 - INFO - MySQL查询成功，共获取 3287 条记录
2025-05-08 09:00:03,111 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-08 09:00:03,137 - INFO - 开始处理日期: 2025-01
2025-05-08 09:00:03,140 - INFO - Request Parameters - Page 1:
2025-05-08 09:00:03,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:03,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:04,317 - INFO - Response - Page 1:
2025-05-08 09:00:04,518 - INFO - 第 1 页获取到 100 条记录
2025-05-08 09:00:04,518 - INFO - Request Parameters - Page 2:
2025-05-08 09:00:04,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:04,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:05,133 - INFO - Response - Page 2:
2025-05-08 09:00:05,333 - INFO - 第 2 页获取到 100 条记录
2025-05-08 09:00:05,333 - INFO - Request Parameters - Page 3:
2025-05-08 09:00:05,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:05,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:05,862 - INFO - Response - Page 3:
2025-05-08 09:00:06,063 - INFO - 第 3 页获取到 100 条记录
2025-05-08 09:00:06,063 - INFO - Request Parameters - Page 4:
2025-05-08 09:00:06,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:06,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:06,589 - INFO - Response - Page 4:
2025-05-08 09:00:06,789 - INFO - 第 4 页获取到 100 条记录
2025-05-08 09:00:06,789 - INFO - Request Parameters - Page 5:
2025-05-08 09:00:06,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:06,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:07,585 - INFO - Response - Page 5:
2025-05-08 09:00:07,785 - INFO - 第 5 页获取到 100 条记录
2025-05-08 09:00:07,786 - INFO - Request Parameters - Page 6:
2025-05-08 09:00:07,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:07,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:08,319 - INFO - Response - Page 6:
2025-05-08 09:00:08,520 - INFO - 第 6 页获取到 100 条记录
2025-05-08 09:00:08,520 - INFO - Request Parameters - Page 7:
2025-05-08 09:00:08,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:08,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:08,952 - INFO - Response - Page 7:
2025-05-08 09:00:09,152 - INFO - 第 7 页获取到 82 条记录
2025-05-08 09:00:09,152 - INFO - 查询完成，共获取到 682 条记录
2025-05-08 09:00:09,152 - INFO - 获取到 682 条表单数据
2025-05-08 09:00:09,164 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-08 09:00:09,176 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 09:00:09,176 - INFO - 开始处理日期: 2025-02
2025-05-08 09:00:09,176 - INFO - Request Parameters - Page 1:
2025-05-08 09:00:09,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:09,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:09,670 - INFO - Response - Page 1:
2025-05-08 09:00:09,871 - INFO - 第 1 页获取到 100 条记录
2025-05-08 09:00:09,871 - INFO - Request Parameters - Page 2:
2025-05-08 09:00:09,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:09,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:10,412 - INFO - Response - Page 2:
2025-05-08 09:00:10,613 - INFO - 第 2 页获取到 100 条记录
2025-05-08 09:00:10,613 - INFO - Request Parameters - Page 3:
2025-05-08 09:00:10,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:10,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:11,081 - INFO - Response - Page 3:
2025-05-08 09:00:11,281 - INFO - 第 3 页获取到 100 条记录
2025-05-08 09:00:11,281 - INFO - Request Parameters - Page 4:
2025-05-08 09:00:11,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:11,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:11,789 - INFO - Response - Page 4:
2025-05-08 09:00:11,989 - INFO - 第 4 页获取到 100 条记录
2025-05-08 09:00:11,989 - INFO - Request Parameters - Page 5:
2025-05-08 09:00:11,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:11,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:12,512 - INFO - Response - Page 5:
2025-05-08 09:00:12,712 - INFO - 第 5 页获取到 100 条记录
2025-05-08 09:00:12,712 - INFO - Request Parameters - Page 6:
2025-05-08 09:00:12,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:12,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:13,232 - INFO - Response - Page 6:
2025-05-08 09:00:13,433 - INFO - 第 6 页获取到 100 条记录
2025-05-08 09:00:13,433 - INFO - Request Parameters - Page 7:
2025-05-08 09:00:13,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:13,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:13,897 - INFO - Response - Page 7:
2025-05-08 09:00:14,097 - INFO - 第 7 页获取到 70 条记录
2025-05-08 09:00:14,097 - INFO - 查询完成，共获取到 670 条记录
2025-05-08 09:00:14,097 - INFO - 获取到 670 条表单数据
2025-05-08 09:00:14,109 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-08 09:00:14,120 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 09:00:14,120 - INFO - 开始处理日期: 2025-03
2025-05-08 09:00:14,120 - INFO - Request Parameters - Page 1:
2025-05-08 09:00:14,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:14,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:14,650 - INFO - Response - Page 1:
2025-05-08 09:00:14,850 - INFO - 第 1 页获取到 100 条记录
2025-05-08 09:00:14,850 - INFO - Request Parameters - Page 2:
2025-05-08 09:00:14,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:14,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:15,349 - INFO - Response - Page 2:
2025-05-08 09:00:15,549 - INFO - 第 2 页获取到 100 条记录
2025-05-08 09:00:15,549 - INFO - Request Parameters - Page 3:
2025-05-08 09:00:15,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:15,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:16,026 - INFO - Response - Page 3:
2025-05-08 09:00:16,227 - INFO - 第 3 页获取到 100 条记录
2025-05-08 09:00:16,227 - INFO - Request Parameters - Page 4:
2025-05-08 09:00:16,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:16,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:16,733 - INFO - Response - Page 4:
2025-05-08 09:00:16,933 - INFO - 第 4 页获取到 100 条记录
2025-05-08 09:00:16,933 - INFO - Request Parameters - Page 5:
2025-05-08 09:00:16,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:16,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:17,474 - INFO - Response - Page 5:
2025-05-08 09:00:17,674 - INFO - 第 5 页获取到 100 条记录
2025-05-08 09:00:17,674 - INFO - Request Parameters - Page 6:
2025-05-08 09:00:17,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:17,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:18,187 - INFO - Response - Page 6:
2025-05-08 09:00:18,387 - INFO - 第 6 页获取到 100 条记录
2025-05-08 09:00:18,387 - INFO - Request Parameters - Page 7:
2025-05-08 09:00:18,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:18,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:18,898 - INFO - Response - Page 7:
2025-05-08 09:00:19,099 - INFO - 第 7 页获取到 61 条记录
2025-05-08 09:00:19,099 - INFO - 查询完成，共获取到 661 条记录
2025-05-08 09:00:19,099 - INFO - 获取到 661 条表单数据
2025-05-08 09:00:19,111 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-08 09:00:19,122 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 09:00:19,122 - INFO - 开始处理日期: 2025-04
2025-05-08 09:00:19,122 - INFO - Request Parameters - Page 1:
2025-05-08 09:00:19,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:19,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:19,702 - INFO - Response - Page 1:
2025-05-08 09:00:19,902 - INFO - 第 1 页获取到 100 条记录
2025-05-08 09:00:19,902 - INFO - Request Parameters - Page 2:
2025-05-08 09:00:19,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:19,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:20,358 - INFO - Response - Page 2:
2025-05-08 09:00:20,559 - INFO - 第 2 页获取到 100 条记录
2025-05-08 09:00:20,559 - INFO - Request Parameters - Page 3:
2025-05-08 09:00:20,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:20,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:21,085 - INFO - Response - Page 3:
2025-05-08 09:00:21,285 - INFO - 第 3 页获取到 100 条记录
2025-05-08 09:00:21,285 - INFO - Request Parameters - Page 4:
2025-05-08 09:00:21,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:21,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:21,770 - INFO - Response - Page 4:
2025-05-08 09:00:21,970 - INFO - 第 4 页获取到 100 条记录
2025-05-08 09:00:21,970 - INFO - Request Parameters - Page 5:
2025-05-08 09:00:21,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:21,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:22,410 - INFO - Response - Page 5:
2025-05-08 09:00:22,611 - INFO - 第 5 页获取到 100 条记录
2025-05-08 09:00:22,611 - INFO - Request Parameters - Page 6:
2025-05-08 09:00:22,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:22,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:23,102 - INFO - Response - Page 6:
2025-05-08 09:00:23,303 - INFO - 第 6 页获取到 100 条记录
2025-05-08 09:00:23,303 - INFO - Request Parameters - Page 7:
2025-05-08 09:00:23,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:23,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:23,701 - INFO - Response - Page 7:
2025-05-08 09:00:23,901 - INFO - 第 7 页获取到 53 条记录
2025-05-08 09:00:23,901 - INFO - 查询完成，共获取到 653 条记录
2025-05-08 09:00:23,901 - INFO - 获取到 653 条表单数据
2025-05-08 09:00:23,914 - INFO - 当前日期 2025-04 有 653 条MySQL数据需要处理
2025-05-08 09:00:23,915 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-05-08 09:00:24,487 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-05-08 09:00:24,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2000000.0, 'new_value': 2850000.0}, {'field': 'total_amount', 'old_value': 2000000.0, 'new_value': 2850000.0}, {'field': 'order_count', 'old_value': 498, 'new_value': 30}]
2025-05-08 09:00:24,491 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-05-08 09:00:24,905 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-05-08 09:00:24,905 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 257725.98, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 91255.94, 'new_value': 348961.41}, {'field': 'total_amount', 'old_value': 348981.92, 'new_value': 348961.41}]
2025-05-08 09:00:24,906 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-05-08 09:00:25,364 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-05-08 09:00:25,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 255646.0, 'new_value': 355646.0}, {'field': 'total_amount', 'old_value': 255646.0, 'new_value': 355646.0}]
2025-05-08 09:00:25,365 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-05-08 09:00:25,925 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-05-08 09:00:25,925 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1266366.0, 'new_value': 1966366.0}, {'field': 'total_amount', 'old_value': 1266366.0, 'new_value': 1966366.0}]
2025-05-08 09:00:25,928 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-05-08 09:00:26,450 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-05-08 09:00:26,450 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15874.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 244331.0, 'new_value': 500205.0}, {'field': 'total_amount', 'old_value': 260205.0, 'new_value': 500205.0}]
2025-05-08 09:00:26,454 - INFO - 日期 2025-04 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-05-08 09:00:26,454 - INFO - 开始处理日期: 2025-05
2025-05-08 09:00:26,454 - INFO - Request Parameters - Page 1:
2025-05-08 09:00:26,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:26,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:27,051 - INFO - Response - Page 1:
2025-05-08 09:00:27,251 - INFO - 第 1 页获取到 100 条记录
2025-05-08 09:00:27,251 - INFO - Request Parameters - Page 2:
2025-05-08 09:00:27,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:27,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:27,780 - INFO - Response - Page 2:
2025-05-08 09:00:27,980 - INFO - 第 2 页获取到 100 条记录
2025-05-08 09:00:27,980 - INFO - Request Parameters - Page 3:
2025-05-08 09:00:27,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:27,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:28,471 - INFO - Response - Page 3:
2025-05-08 09:00:28,671 - INFO - 第 3 页获取到 100 条记录
2025-05-08 09:00:28,671 - INFO - Request Parameters - Page 4:
2025-05-08 09:00:28,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:28,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:29,307 - INFO - Response - Page 4:
2025-05-08 09:00:29,507 - INFO - 第 4 页获取到 100 条记录
2025-05-08 09:00:29,507 - INFO - Request Parameters - Page 5:
2025-05-08 09:00:29,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:29,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:29,969 - INFO - Response - Page 5:
2025-05-08 09:00:30,169 - INFO - 第 5 页获取到 100 条记录
2025-05-08 09:00:30,169 - INFO - Request Parameters - Page 6:
2025-05-08 09:00:30,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:30,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:30,788 - INFO - Response - Page 6:
2025-05-08 09:00:30,988 - INFO - 第 6 页获取到 100 条记录
2025-05-08 09:00:30,988 - INFO - Request Parameters - Page 7:
2025-05-08 09:00:30,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 09:00:30,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 09:00:31,861 - INFO - Response - Page 7:
2025-05-08 09:00:32,062 - INFO - 第 7 页获取到 21 条记录
2025-05-08 09:00:32,062 - INFO - 查询完成，共获取到 621 条记录
2025-05-08 09:00:32,062 - INFO - 获取到 621 条表单数据
2025-05-08 09:00:32,074 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-08 09:00:32,075 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-08 09:00:32,594 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-08 09:00:32,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2600000.0, 'new_value': 3200000.0}, {'field': 'total_amount', 'old_value': 2700000.0, 'new_value': 3300000.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-05-08 09:00:32,595 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-08 09:00:33,061 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-08 09:00:33,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 500.0, 'new_value': 1099.0}, {'field': 'total_amount', 'old_value': 500.0, 'new_value': 1099.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-08 09:00:33,062 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-08 09:00:33,594 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-08 09:00:33,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20122.0, 'new_value': 22636.0}, {'field': 'total_amount', 'old_value': 20122.0, 'new_value': 22636.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 181}]
2025-05-08 09:00:33,595 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-08 09:00:34,019 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-08 09:00:34,019 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7200.0, 'new_value': 24516.0}, {'field': 'offline_amount', 'old_value': 17478.0, 'new_value': 19260.0}, {'field': 'total_amount', 'old_value': 24678.0, 'new_value': 43776.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 15}]
2025-05-08 09:00:34,019 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-08 09:00:34,500 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-08 09:00:34,501 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 450.0, 'new_value': 600.0}, {'field': 'offline_amount', 'old_value': 7208.0, 'new_value': 8691.0}, {'field': 'total_amount', 'old_value': 7658.0, 'new_value': 9291.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 36}]
2025-05-08 09:00:34,501 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-08 09:00:34,979 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-08 09:00:34,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2361.11, 'new_value': 2370.62}, {'field': 'offline_amount', 'old_value': 31914.03, 'new_value': 33845.63}, {'field': 'total_amount', 'old_value': 34275.14, 'new_value': 36216.25}, {'field': 'order_count', 'old_value': 764, 'new_value': 814}]
2025-05-08 09:00:34,980 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-08 09:00:35,337 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-08 09:00:35,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46368.51, 'new_value': 56351.02}, {'field': 'total_amount', 'old_value': 47901.94, 'new_value': 57884.45}, {'field': 'order_count', 'old_value': 344, 'new_value': 402}]
2025-05-08 09:00:35,339 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-08 09:00:35,890 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-08 09:00:35,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34712.0, 'new_value': 40711.0}, {'field': 'total_amount', 'old_value': 58334.48, 'new_value': 64333.48}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-08 09:00:35,890 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-08 09:00:36,371 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-08 09:00:36,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281764.0, 'new_value': 311465.0}, {'field': 'total_amount', 'old_value': 281764.0, 'new_value': 311465.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 44}]
2025-05-08 09:00:36,372 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-08 09:00:36,847 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-08 09:00:36,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18340.84, 'new_value': 20372.84}, {'field': 'offline_amount', 'old_value': 303629.25, 'new_value': 349426.61}, {'field': 'total_amount', 'old_value': 321970.09, 'new_value': 369799.45}, {'field': 'order_count', 'old_value': 2244, 'new_value': 2646}]
2025-05-08 09:00:36,847 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-08 09:00:37,285 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-08 09:00:37,285 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26565.58, 'new_value': 28934.18}, {'field': 'total_amount', 'old_value': 26565.58, 'new_value': 28934.18}, {'field': 'order_count', 'old_value': 144, 'new_value': 158}]
2025-05-08 09:00:37,286 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-08 09:00:37,727 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-08 09:00:37,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8377.9, 'new_value': 9058.8}, {'field': 'offline_amount', 'old_value': 6768.8, 'new_value': 7836.7}, {'field': 'total_amount', 'old_value': 15146.7, 'new_value': 16895.5}, {'field': 'order_count', 'old_value': 77, 'new_value': 85}]
2025-05-08 09:00:37,727 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-08 09:00:38,259 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-08 09:00:38,260 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107376.0, 'new_value': 121420.0}, {'field': 'total_amount', 'old_value': 107376.0, 'new_value': 121420.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 97}]
2025-05-08 09:00:38,260 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-08 09:00:38,780 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-08 09:00:38,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3646.06, 'new_value': 4280.27}, {'field': 'offline_amount', 'old_value': 61945.3, 'new_value': 71970.34}, {'field': 'total_amount', 'old_value': 65591.36, 'new_value': 76250.61}, {'field': 'order_count', 'old_value': 3326, 'new_value': 3963}]
2025-05-08 09:00:38,781 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-08 09:00:39,241 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-08 09:00:39,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14572.23, 'new_value': 16486.41}, {'field': 'offline_amount', 'old_value': 8404.0, 'new_value': 9576.0}, {'field': 'total_amount', 'old_value': 22976.23, 'new_value': 26062.41}, {'field': 'order_count', 'old_value': 289, 'new_value': 324}]
2025-05-08 09:00:39,242 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-08 09:00:39,761 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-08 09:00:39,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105667.9, 'new_value': 109798.0}, {'field': 'total_amount', 'old_value': 105667.9, 'new_value': 109798.0}, {'field': 'order_count', 'old_value': 183, 'new_value': 193}]
2025-05-08 09:00:39,762 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-08 09:00:40,232 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-08 09:00:40,232 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 3933, 'new_value': 7454}]
2025-05-08 09:00:40,232 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-08 09:00:40,750 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-08 09:00:40,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6985.2, 'new_value': 11421.2}, {'field': 'total_amount', 'old_value': 6985.2, 'new_value': 11421.2}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 09:00:40,751 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-08 09:00:41,223 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-08 09:00:41,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34589.0, 'new_value': 38842.0}, {'field': 'total_amount', 'old_value': 34589.0, 'new_value': 38842.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 47}]
2025-05-08 09:00:41,224 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-08 09:00:41,770 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-08 09:00:41,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133137.55, 'new_value': 141110.87}, {'field': 'total_amount', 'old_value': 133137.55, 'new_value': 141110.87}, {'field': 'order_count', 'old_value': 597, 'new_value': 641}]
2025-05-08 09:00:41,771 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-08 09:00:42,168 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-08 09:00:42,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29285.7, 'new_value': 29923.2}, {'field': 'total_amount', 'old_value': 29285.7, 'new_value': 29923.2}, {'field': 'order_count', 'old_value': 76, 'new_value': 78}]
2025-05-08 09:00:42,169 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-08 09:00:42,633 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-08 09:00:42,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15032.0, 'new_value': 18503.0}, {'field': 'total_amount', 'old_value': 15032.0, 'new_value': 18503.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-08 09:00:42,634 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-08 09:00:43,094 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-08 09:00:43,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 322150.55, 'new_value': 337697.55}, {'field': 'total_amount', 'old_value': 322150.55, 'new_value': 337697.55}, {'field': 'order_count', 'old_value': 776, 'new_value': 821}]
2025-05-08 09:00:43,095 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-08 09:00:43,642 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-08 09:00:43,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118469.0, 'new_value': 124939.0}, {'field': 'total_amount', 'old_value': 118909.0, 'new_value': 125379.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 74}]
2025-05-08 09:00:43,643 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-08 09:00:44,056 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-08 09:00:44,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63919.0, 'new_value': 69259.0}, {'field': 'total_amount', 'old_value': 63919.0, 'new_value': 69259.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 112}]
2025-05-08 09:00:44,057 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-08 09:00:44,527 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-08 09:00:44,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200234.0, 'new_value': 224857.0}, {'field': 'total_amount', 'old_value': 200234.0, 'new_value': 224857.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 54}]
2025-05-08 09:00:44,528 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-08 09:00:45,009 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-08 09:00:45,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26995.0, 'new_value': 28302.0}, {'field': 'total_amount', 'old_value': 26995.0, 'new_value': 28302.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 70}]
2025-05-08 09:00:45,010 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-08 09:00:45,455 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-08 09:00:45,455 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11921.0, 'new_value': 14018.7}, {'field': 'offline_amount', 'old_value': 89068.42, 'new_value': 96226.32}, {'field': 'total_amount', 'old_value': 100989.42, 'new_value': 110245.02}, {'field': 'order_count', 'old_value': 598, 'new_value': 658}]
2025-05-08 09:00:45,455 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-08 09:00:46,020 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-08 09:00:46,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41234.0, 'new_value': 41996.0}, {'field': 'total_amount', 'old_value': 41234.0, 'new_value': 41996.0}, {'field': 'order_count', 'old_value': 1238, 'new_value': 1261}]
2025-05-08 09:00:46,021 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-08 09:00:46,536 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-08 09:00:46,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 481533.0, 'new_value': 492407.1}, {'field': 'total_amount', 'old_value': 481533.0, 'new_value': 492407.1}, {'field': 'order_count', 'old_value': 549, 'new_value': 567}]
2025-05-08 09:00:46,537 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-08 09:00:46,956 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-08 09:00:46,956 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354047.0, 'new_value': 363226.0}, {'field': 'total_amount', 'old_value': 354047.0, 'new_value': 363226.0}, {'field': 'order_count', 'old_value': 351, 'new_value': 379}]
2025-05-08 09:00:46,956 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-08 09:00:47,445 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-08 09:00:47,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210788.0, 'new_value': 230316.42}, {'field': 'total_amount', 'old_value': 210788.0, 'new_value': 230316.42}, {'field': 'order_count', 'old_value': 1445, 'new_value': 1641}]
2025-05-08 09:00:47,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-08 09:00:47,922 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-08 09:00:47,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5925.0, 'new_value': 7324.0}, {'field': 'total_amount', 'old_value': 5925.0, 'new_value': 7324.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 37}]
2025-05-08 09:00:47,923 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-08 09:00:48,398 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-08 09:00:48,398 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35783.4, 'new_value': 40057.0}, {'field': 'offline_amount', 'old_value': 28851.3, 'new_value': 32458.4}, {'field': 'total_amount', 'old_value': 64634.7, 'new_value': 72515.4}, {'field': 'order_count', 'old_value': 1567, 'new_value': 1738}]
2025-05-08 09:00:48,399 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-08 09:00:48,872 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-08 09:00:48,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 980000.0, 'new_value': 1030000.0}, {'field': 'total_amount', 'old_value': 980000.0, 'new_value': 1030000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 09:00:48,872 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-08 09:00:49,320 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-08 09:00:49,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62366.8, 'new_value': 66157.8}, {'field': 'total_amount', 'old_value': 62366.8, 'new_value': 66157.8}, {'field': 'order_count', 'old_value': 747, 'new_value': 822}]
2025-05-08 09:00:49,320 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-08 09:00:49,713 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-08 09:00:49,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92258.9, 'new_value': 97359.8}, {'field': 'total_amount', 'old_value': 92258.9, 'new_value': 97359.8}, {'field': 'order_count', 'old_value': 498, 'new_value': 531}]
2025-05-08 09:00:49,713 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-08 09:00:50,265 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-08 09:00:50,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14302.1, 'new_value': 15529.43}, {'field': 'total_amount', 'old_value': 14302.1, 'new_value': 15529.43}, {'field': 'order_count', 'old_value': 53, 'new_value': 58}]
2025-05-08 09:00:50,265 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-08 09:00:50,711 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-08 09:00:50,711 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8756.71, 'new_value': 10711.02}, {'field': 'offline_amount', 'old_value': 355660.5, 'new_value': 390793.29}, {'field': 'total_amount', 'old_value': 364417.21, 'new_value': 401504.31}, {'field': 'order_count', 'old_value': 1457, 'new_value': 1629}]
2025-05-08 09:00:50,712 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-08 09:00:51,233 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-08 09:00:51,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14570.7, 'new_value': 16168.2}, {'field': 'total_amount', 'old_value': 14570.7, 'new_value': 16168.2}, {'field': 'order_count', 'old_value': 57, 'new_value': 62}]
2025-05-08 09:00:51,234 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-08 09:00:51,692 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-08 09:00:51,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59750.67, 'new_value': 68022.82}, {'field': 'offline_amount', 'old_value': 248713.22, 'new_value': 264949.79}, {'field': 'total_amount', 'old_value': 308463.89, 'new_value': 332972.61}, {'field': 'order_count', 'old_value': 1471, 'new_value': 1638}]
2025-05-08 09:00:51,693 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-08 09:00:52,124 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-08 09:00:52,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 395000.0, 'new_value': 400000.0}, {'field': 'total_amount', 'old_value': 395000.0, 'new_value': 400000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 09:00:52,124 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-08 09:00:52,588 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-08 09:00:52,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 375000.0, 'new_value': 380000.0}, {'field': 'total_amount', 'old_value': 375000.0, 'new_value': 380000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 09:00:52,588 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-08 09:00:53,057 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-08 09:00:53,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2098674.0, 'new_value': 2148674.0}, {'field': 'total_amount', 'old_value': 2098674.0, 'new_value': 2148674.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 09:00:53,058 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-08 09:00:53,498 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-08 09:00:53,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23545.0, 'new_value': 26865.0}, {'field': 'total_amount', 'old_value': 23545.0, 'new_value': 26865.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 121}]
2025-05-08 09:00:53,498 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-08 09:00:53,910 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-08 09:00:53,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5679.0, 'new_value': 5778.0}, {'field': 'total_amount', 'old_value': 5679.0, 'new_value': 5778.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 119}]
2025-05-08 09:00:53,911 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-08 09:00:54,328 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-08 09:00:54,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70109.5, 'new_value': 72915.2}, {'field': 'total_amount', 'old_value': 92596.9, 'new_value': 95402.6}, {'field': 'order_count', 'old_value': 536, 'new_value': 575}]
2025-05-08 09:00:54,328 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-08 09:00:54,749 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-08 09:00:54,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 775574.0, 'new_value': 825363.0}, {'field': 'total_amount', 'old_value': 775574.0, 'new_value': 825363.0}, {'field': 'order_count', 'old_value': 2913, 'new_value': 3104}]
2025-05-08 09:00:54,750 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-08 09:00:55,187 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-08 09:00:55,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117969.26, 'new_value': 120983.88}, {'field': 'total_amount', 'old_value': 117969.26, 'new_value': 120983.88}, {'field': 'order_count', 'old_value': 615, 'new_value': 632}]
2025-05-08 09:00:55,187 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-08 09:00:55,661 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-08 09:00:55,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10036.03, 'new_value': 11742.58}, {'field': 'offline_amount', 'old_value': 18563.4, 'new_value': 19224.3}, {'field': 'total_amount', 'old_value': 28599.43, 'new_value': 30966.88}, {'field': 'order_count', 'old_value': 2079, 'new_value': 2285}]
2025-05-08 09:00:55,662 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-08 09:00:56,123 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-08 09:00:56,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151505.71, 'new_value': 155554.71}, {'field': 'total_amount', 'old_value': 151505.71, 'new_value': 155554.71}]
2025-05-08 09:00:56,123 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-08 09:00:56,548 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-08 09:00:56,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24757.0, 'new_value': 26686.0}, {'field': 'total_amount', 'old_value': 24757.0, 'new_value': 26686.0}, {'field': 'order_count', 'old_value': 1034, 'new_value': 1131}]
2025-05-08 09:00:56,548 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-08 09:00:57,006 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-08 09:00:57,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150981.0, 'new_value': 160099.0}, {'field': 'total_amount', 'old_value': 150981.0, 'new_value': 160099.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-08 09:00:57,007 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-08 09:00:57,578 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-08 09:00:57,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17091.0, 'new_value': 19529.0}, {'field': 'total_amount', 'old_value': 17356.0, 'new_value': 19794.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 161}]
2025-05-08 09:00:57,578 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-08 09:00:58,102 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-08 09:00:58,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1836.0, 'new_value': 1886.0}, {'field': 'offline_amount', 'old_value': 4955.0, 'new_value': 5740.0}, {'field': 'total_amount', 'old_value': 6791.0, 'new_value': 7626.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 44}]
2025-05-08 09:00:58,103 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-08 09:00:58,684 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-08 09:00:58,684 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21518.0, 'new_value': 23185.0}, {'field': 'offline_amount', 'old_value': 69730.0, 'new_value': 81040.0}, {'field': 'total_amount', 'old_value': 91248.0, 'new_value': 104225.0}, {'field': 'order_count', 'old_value': 395, 'new_value': 448}]
2025-05-08 09:00:58,685 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-08 09:00:59,101 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-08 09:00:59,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149344.3, 'new_value': 154374.2}, {'field': 'total_amount', 'old_value': 149344.3, 'new_value': 154374.2}, {'field': 'order_count', 'old_value': 3240, 'new_value': 3347}]
2025-05-08 09:00:59,101 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-08 09:00:59,520 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-08 09:00:59,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 329567.68, 'new_value': 348755.47}, {'field': 'total_amount', 'old_value': 329567.68, 'new_value': 348755.47}, {'field': 'order_count', 'old_value': 1301, 'new_value': 1366}]
2025-05-08 09:00:59,520 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-08 09:01:00,104 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-08 09:01:00,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45488.67, 'new_value': 48879.23}, {'field': 'total_amount', 'old_value': 45488.67, 'new_value': 48879.23}, {'field': 'order_count', 'old_value': 3057, 'new_value': 3294}]
2025-05-08 09:01:00,105 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-08 09:01:00,542 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-08 09:01:00,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 239765.0, 'new_value': 245548.0}, {'field': 'total_amount', 'old_value': 239765.0, 'new_value': 245548.0}, {'field': 'order_count', 'old_value': 5375, 'new_value': 5509}]
2025-05-08 09:01:00,543 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-08 09:01:01,025 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-08 09:01:01,025 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34501.0, 'new_value': 36672.0}, {'field': 'total_amount', 'old_value': 34501.0, 'new_value': 36672.0}, {'field': 'order_count', 'old_value': 2391, 'new_value': 2558}]
2025-05-08 09:01:01,026 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-08 09:01:01,419 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-08 09:01:01,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18939.65, 'new_value': 21708.11}, {'field': 'total_amount', 'old_value': 18939.65, 'new_value': 21708.11}, {'field': 'order_count', 'old_value': 232, 'new_value': 312}]
2025-05-08 09:01:01,420 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-08 09:01:01,889 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-08 09:01:01,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20857.97, 'new_value': 24340.46}, {'field': 'offline_amount', 'old_value': 146541.5, 'new_value': 153086.2}, {'field': 'total_amount', 'old_value': 167399.47, 'new_value': 177426.66}, {'field': 'order_count', 'old_value': 937, 'new_value': 1041}]
2025-05-08 09:01:01,890 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-08 09:01:02,334 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-08 09:01:02,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11797.0, 'new_value': 17944.0}, {'field': 'total_amount', 'old_value': 11797.0, 'new_value': 17944.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-05-08 09:01:02,334 - INFO - 日期 2025-05 处理完成 - 更新: 64 条，插入: 0 条，错误: 0 条
2025-05-08 09:01:02,334 - INFO - 数据同步完成！更新: 69 条，插入: 0 条，错误: 0 条
2025-05-08 09:01:02,336 - INFO - =================同步完成====================
2025-05-08 12:00:01,879 - INFO - =================使用默认全量同步=============
2025-05-08 12:00:03,107 - INFO - MySQL查询成功，共获取 3287 条记录
2025-05-08 12:00:03,107 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-08 12:00:03,135 - INFO - 开始处理日期: 2025-01
2025-05-08 12:00:03,138 - INFO - Request Parameters - Page 1:
2025-05-08 12:00:03,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:03,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:03,993 - INFO - Response - Page 1:
2025-05-08 12:00:04,193 - INFO - 第 1 页获取到 100 条记录
2025-05-08 12:00:04,193 - INFO - Request Parameters - Page 2:
2025-05-08 12:00:04,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:04,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:05,066 - INFO - Response - Page 2:
2025-05-08 12:00:05,267 - INFO - 第 2 页获取到 100 条记录
2025-05-08 12:00:05,267 - INFO - Request Parameters - Page 3:
2025-05-08 12:00:05,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:05,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:05,788 - INFO - Response - Page 3:
2025-05-08 12:00:05,989 - INFO - 第 3 页获取到 100 条记录
2025-05-08 12:00:05,989 - INFO - Request Parameters - Page 4:
2025-05-08 12:00:05,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:05,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:06,473 - INFO - Response - Page 4:
2025-05-08 12:00:06,673 - INFO - 第 4 页获取到 100 条记录
2025-05-08 12:00:06,673 - INFO - Request Parameters - Page 5:
2025-05-08 12:00:06,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:06,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:07,274 - INFO - Response - Page 5:
2025-05-08 12:00:07,474 - INFO - 第 5 页获取到 100 条记录
2025-05-08 12:00:07,474 - INFO - Request Parameters - Page 6:
2025-05-08 12:00:07,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:07,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:07,924 - INFO - Response - Page 6:
2025-05-08 12:00:08,125 - INFO - 第 6 页获取到 100 条记录
2025-05-08 12:00:08,125 - INFO - Request Parameters - Page 7:
2025-05-08 12:00:08,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:08,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:08,610 - INFO - Response - Page 7:
2025-05-08 12:00:08,810 - INFO - 第 7 页获取到 82 条记录
2025-05-08 12:00:08,810 - INFO - 查询完成，共获取到 682 条记录
2025-05-08 12:00:08,810 - INFO - 获取到 682 条表单数据
2025-05-08 12:00:08,823 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-08 12:00:08,835 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 12:00:08,835 - INFO - 开始处理日期: 2025-02
2025-05-08 12:00:08,836 - INFO - Request Parameters - Page 1:
2025-05-08 12:00:08,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:08,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:09,374 - INFO - Response - Page 1:
2025-05-08 12:00:09,574 - INFO - 第 1 页获取到 100 条记录
2025-05-08 12:00:09,574 - INFO - Request Parameters - Page 2:
2025-05-08 12:00:09,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:09,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:10,005 - INFO - Response - Page 2:
2025-05-08 12:00:10,206 - INFO - 第 2 页获取到 100 条记录
2025-05-08 12:00:10,206 - INFO - Request Parameters - Page 3:
2025-05-08 12:00:10,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:10,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:10,710 - INFO - Response - Page 3:
2025-05-08 12:00:10,910 - INFO - 第 3 页获取到 100 条记录
2025-05-08 12:00:10,910 - INFO - Request Parameters - Page 4:
2025-05-08 12:00:10,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:10,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:11,359 - INFO - Response - Page 4:
2025-05-08 12:00:11,559 - INFO - 第 4 页获取到 100 条记录
2025-05-08 12:00:11,559 - INFO - Request Parameters - Page 5:
2025-05-08 12:00:11,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:11,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:12,085 - INFO - Response - Page 5:
2025-05-08 12:00:12,287 - INFO - 第 5 页获取到 100 条记录
2025-05-08 12:00:12,287 - INFO - Request Parameters - Page 6:
2025-05-08 12:00:12,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:12,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:12,754 - INFO - Response - Page 6:
2025-05-08 12:00:12,955 - INFO - 第 6 页获取到 100 条记录
2025-05-08 12:00:12,955 - INFO - Request Parameters - Page 7:
2025-05-08 12:00:12,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:12,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:13,409 - INFO - Response - Page 7:
2025-05-08 12:00:13,609 - INFO - 第 7 页获取到 70 条记录
2025-05-08 12:00:13,609 - INFO - 查询完成，共获取到 670 条记录
2025-05-08 12:00:13,609 - INFO - 获取到 670 条表单数据
2025-05-08 12:00:13,622 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-08 12:00:13,633 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 12:00:13,633 - INFO - 开始处理日期: 2025-03
2025-05-08 12:00:13,633 - INFO - Request Parameters - Page 1:
2025-05-08 12:00:13,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:13,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:14,150 - INFO - Response - Page 1:
2025-05-08 12:00:14,350 - INFO - 第 1 页获取到 100 条记录
2025-05-08 12:00:14,350 - INFO - Request Parameters - Page 2:
2025-05-08 12:00:14,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:14,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:14,837 - INFO - Response - Page 2:
2025-05-08 12:00:15,039 - INFO - 第 2 页获取到 100 条记录
2025-05-08 12:00:15,039 - INFO - Request Parameters - Page 3:
2025-05-08 12:00:15,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:15,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:15,526 - INFO - Response - Page 3:
2025-05-08 12:00:15,726 - INFO - 第 3 页获取到 100 条记录
2025-05-08 12:00:15,726 - INFO - Request Parameters - Page 4:
2025-05-08 12:00:15,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:15,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:16,246 - INFO - Response - Page 4:
2025-05-08 12:00:16,446 - INFO - 第 4 页获取到 100 条记录
2025-05-08 12:00:16,446 - INFO - Request Parameters - Page 5:
2025-05-08 12:00:16,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:16,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:16,939 - INFO - Response - Page 5:
2025-05-08 12:00:17,139 - INFO - 第 5 页获取到 100 条记录
2025-05-08 12:00:17,139 - INFO - Request Parameters - Page 6:
2025-05-08 12:00:17,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:17,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:17,635 - INFO - Response - Page 6:
2025-05-08 12:00:17,835 - INFO - 第 6 页获取到 100 条记录
2025-05-08 12:00:17,835 - INFO - Request Parameters - Page 7:
2025-05-08 12:00:17,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:17,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:18,314 - INFO - Response - Page 7:
2025-05-08 12:00:18,514 - INFO - 第 7 页获取到 61 条记录
2025-05-08 12:00:18,514 - INFO - 查询完成，共获取到 661 条记录
2025-05-08 12:00:18,514 - INFO - 获取到 661 条表单数据
2025-05-08 12:00:18,527 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-08 12:00:18,538 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 12:00:18,538 - INFO - 开始处理日期: 2025-04
2025-05-08 12:00:18,538 - INFO - Request Parameters - Page 1:
2025-05-08 12:00:18,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:18,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:19,055 - INFO - Response - Page 1:
2025-05-08 12:00:19,255 - INFO - 第 1 页获取到 100 条记录
2025-05-08 12:00:19,255 - INFO - Request Parameters - Page 2:
2025-05-08 12:00:19,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:19,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:19,823 - INFO - Response - Page 2:
2025-05-08 12:00:20,023 - INFO - 第 2 页获取到 100 条记录
2025-05-08 12:00:20,023 - INFO - Request Parameters - Page 3:
2025-05-08 12:00:20,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:20,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:20,479 - INFO - Response - Page 3:
2025-05-08 12:00:20,679 - INFO - 第 3 页获取到 100 条记录
2025-05-08 12:00:20,679 - INFO - Request Parameters - Page 4:
2025-05-08 12:00:20,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:20,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:21,156 - INFO - Response - Page 4:
2025-05-08 12:00:21,356 - INFO - 第 4 页获取到 100 条记录
2025-05-08 12:00:21,356 - INFO - Request Parameters - Page 5:
2025-05-08 12:00:21,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:21,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:21,830 - INFO - Response - Page 5:
2025-05-08 12:00:22,031 - INFO - 第 5 页获取到 100 条记录
2025-05-08 12:00:22,031 - INFO - Request Parameters - Page 6:
2025-05-08 12:00:22,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:22,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:22,513 - INFO - Response - Page 6:
2025-05-08 12:00:22,713 - INFO - 第 6 页获取到 100 条记录
2025-05-08 12:00:22,713 - INFO - Request Parameters - Page 7:
2025-05-08 12:00:22,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:22,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:23,202 - INFO - Response - Page 7:
2025-05-08 12:00:23,402 - INFO - 第 7 页获取到 53 条记录
2025-05-08 12:00:23,402 - INFO - 查询完成，共获取到 653 条记录
2025-05-08 12:00:23,402 - INFO - 获取到 653 条表单数据
2025-05-08 12:00:23,414 - INFO - 当前日期 2025-04 有 653 条MySQL数据需要处理
2025-05-08 12:00:23,417 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-05-08 12:00:23,987 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-05-08 12:00:23,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 283097.04, 'new_value': 283383.24}, {'field': 'total_amount', 'old_value': 283130.04, 'new_value': 283383.24}]
2025-05-08 12:00:23,999 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-08 12:00:23,999 - INFO - 开始处理日期: 2025-05
2025-05-08 12:00:23,999 - INFO - Request Parameters - Page 1:
2025-05-08 12:00:23,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:23,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:24,457 - INFO - Response - Page 1:
2025-05-08 12:00:24,657 - INFO - 第 1 页获取到 100 条记录
2025-05-08 12:00:24,657 - INFO - Request Parameters - Page 2:
2025-05-08 12:00:24,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:24,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:25,111 - INFO - Response - Page 2:
2025-05-08 12:00:25,311 - INFO - 第 2 页获取到 100 条记录
2025-05-08 12:00:25,311 - INFO - Request Parameters - Page 3:
2025-05-08 12:00:25,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:25,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:25,795 - INFO - Response - Page 3:
2025-05-08 12:00:25,996 - INFO - 第 3 页获取到 100 条记录
2025-05-08 12:00:25,996 - INFO - Request Parameters - Page 4:
2025-05-08 12:00:25,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:25,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:26,502 - INFO - Response - Page 4:
2025-05-08 12:00:26,703 - INFO - 第 4 页获取到 100 条记录
2025-05-08 12:00:26,703 - INFO - Request Parameters - Page 5:
2025-05-08 12:00:26,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:26,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:27,162 - INFO - Response - Page 5:
2025-05-08 12:00:27,362 - INFO - 第 5 页获取到 100 条记录
2025-05-08 12:00:27,362 - INFO - Request Parameters - Page 6:
2025-05-08 12:00:27,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:27,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:27,797 - INFO - Response - Page 6:
2025-05-08 12:00:27,997 - INFO - 第 6 页获取到 100 条记录
2025-05-08 12:00:27,997 - INFO - Request Parameters - Page 7:
2025-05-08 12:00:27,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 12:00:27,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 12:00:28,348 - INFO - Response - Page 7:
2025-05-08 12:00:28,548 - INFO - 第 7 页获取到 21 条记录
2025-05-08 12:00:28,548 - INFO - 查询完成，共获取到 621 条记录
2025-05-08 12:00:28,548 - INFO - 获取到 621 条表单数据
2025-05-08 12:00:28,560 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-08 12:00:28,560 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-08 12:00:29,096 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-08 12:00:29,096 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 445.0, 'new_value': 590.0}, {'field': 'offline_amount', 'old_value': 15575.0, 'new_value': 17810.0}, {'field': 'total_amount', 'old_value': 16020.0, 'new_value': 18400.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 211}]
2025-05-08 12:00:29,096 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-08 12:00:29,601 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-08 12:00:29,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99751.0, 'new_value': 119423.0}, {'field': 'total_amount', 'old_value': 99751.0, 'new_value': 119423.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 84}]
2025-05-08 12:00:29,601 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-08 12:00:30,070 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-08 12:00:30,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4770.0, 'new_value': 7155.0}, {'field': 'total_amount', 'old_value': 4770.0, 'new_value': 7155.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 27}]
2025-05-08 12:00:30,070 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-08 12:00:30,496 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-08 12:00:30,496 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31441.61, 'new_value': 38315.11}, {'field': 'total_amount', 'old_value': 31441.61, 'new_value': 38315.11}, {'field': 'order_count', 'old_value': 1671, 'new_value': 1961}]
2025-05-08 12:00:30,496 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-08 12:00:30,994 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-08 12:00:30,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6143.0, 'new_value': 6937.0}, {'field': 'total_amount', 'old_value': 9040.0, 'new_value': 9834.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-08 12:00:30,994 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-08 12:00:31,473 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-08 12:00:31,473 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5662.37, 'new_value': 6988.0}, {'field': 'offline_amount', 'old_value': 3935.2, 'new_value': 4636.75}, {'field': 'total_amount', 'old_value': 9597.57, 'new_value': 11624.75}, {'field': 'order_count', 'old_value': 443, 'new_value': 546}]
2025-05-08 12:00:31,474 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-08 12:00:31,997 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-08 12:00:31,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56940.0, 'new_value': 69800.0}, {'field': 'total_amount', 'old_value': 56940.0, 'new_value': 69800.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 34}]
2025-05-08 12:00:31,998 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-08 12:00:32,428 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-08 12:00:32,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77001.0, 'new_value': 78001.0}, {'field': 'total_amount', 'old_value': 85200.0, 'new_value': 86200.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-08 12:00:32,428 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-08 12:00:32,868 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-08 12:00:32,868 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110861.0, 'new_value': 138429.0}, {'field': 'offline_amount', 'old_value': 74885.0, 'new_value': 85441.0}, {'field': 'total_amount', 'old_value': 185746.0, 'new_value': 223870.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 233}]
2025-05-08 12:00:32,868 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-08 12:00:33,431 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-08 12:00:33,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33908.0, 'new_value': 37168.0}, {'field': 'total_amount', 'old_value': 33908.0, 'new_value': 37168.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 37}]
2025-05-08 12:00:33,431 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-08 12:00:33,927 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-08 12:00:33,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7358.1, 'new_value': 9913.55}, {'field': 'offline_amount', 'old_value': 18841.41, 'new_value': 20980.36}, {'field': 'total_amount', 'old_value': 26199.51, 'new_value': 30893.91}, {'field': 'order_count', 'old_value': 621, 'new_value': 743}]
2025-05-08 12:00:33,928 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-08 12:00:34,369 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-08 12:00:34,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 198.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 198.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-08 12:00:34,370 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-08 12:00:34,908 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-08 12:00:34,908 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 111, 'new_value': 116}]
2025-05-08 12:00:34,909 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-08 12:00:35,440 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-08 12:00:35,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49776.0, 'new_value': 50748.0}, {'field': 'total_amount', 'old_value': 49776.0, 'new_value': 50748.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-08 12:00:35,440 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-08 12:00:36,013 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-08 12:00:36,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15151.91, 'new_value': 18139.18}, {'field': 'offline_amount', 'old_value': 27548.8, 'new_value': 32845.5}, {'field': 'total_amount', 'old_value': 42700.71, 'new_value': 50984.68}, {'field': 'order_count', 'old_value': 580, 'new_value': 672}]
2025-05-08 12:00:36,013 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-08 12:00:36,503 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-08 12:00:36,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7037.0, 'new_value': 7976.0}, {'field': 'offline_amount', 'old_value': 9301.0, 'new_value': 9688.0}, {'field': 'total_amount', 'old_value': 16338.0, 'new_value': 17664.0}, {'field': 'order_count', 'old_value': 771, 'new_value': 850}]
2025-05-08 12:00:36,503 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-08 12:00:36,922 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-08 12:00:36,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25499.8, 'new_value': 115019.7}, {'field': 'offline_amount', 'old_value': 124045.2, 'new_value': 127788.0}, {'field': 'total_amount', 'old_value': 149545.0, 'new_value': 242807.7}, {'field': 'order_count', 'old_value': 1268, 'new_value': 1333}]
2025-05-08 12:00:36,923 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-08 12:00:37,367 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-08 12:00:37,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23958.0, 'new_value': 28013.0}, {'field': 'total_amount', 'old_value': 23958.0, 'new_value': 28013.0}, {'field': 'order_count', 'old_value': 1221, 'new_value': 1458}]
2025-05-08 12:00:37,368 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-08 12:00:37,841 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-08 12:00:37,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11287.0, 'new_value': 17287.0}, {'field': 'total_amount', 'old_value': 11287.0, 'new_value': 17287.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 34}]
2025-05-08 12:00:37,841 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-08 12:00:38,361 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-08 12:00:38,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103964.0, 'new_value': 120454.0}, {'field': 'total_amount', 'old_value': 103964.0, 'new_value': 120454.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 120}]
2025-05-08 12:00:38,362 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-08 12:00:38,860 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-08 12:00:38,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1237.55, 'new_value': 1492.64}, {'field': 'offline_amount', 'old_value': 24948.15, 'new_value': 27318.47}, {'field': 'total_amount', 'old_value': 26185.7, 'new_value': 28811.11}, {'field': 'order_count', 'old_value': 928, 'new_value': 1048}]
2025-05-08 12:00:38,860 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-08 12:00:39,335 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-08 12:00:39,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30311.75, 'new_value': 36334.58}, {'field': 'total_amount', 'old_value': 30311.75, 'new_value': 36334.58}, {'field': 'order_count', 'old_value': 329, 'new_value': 398}]
2025-05-08 12:00:39,336 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-08 12:00:39,853 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-08 12:00:39,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3920.22, 'new_value': 4380.95}, {'field': 'offline_amount', 'old_value': 12921.37, 'new_value': 14249.47}, {'field': 'total_amount', 'old_value': 16841.59, 'new_value': 18630.42}, {'field': 'order_count', 'old_value': 257, 'new_value': 294}]
2025-05-08 12:00:39,854 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-08 12:00:40,314 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-08 12:00:40,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77388.1, 'new_value': 94622.4}, {'field': 'total_amount', 'old_value': 78645.5, 'new_value': 95879.8}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-08 12:00:40,314 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-08 12:00:40,774 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-08 12:00:40,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2376.0, 'new_value': 5675.0}, {'field': 'total_amount', 'old_value': 4865.0, 'new_value': 8164.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-08 12:00:40,774 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-08 12:00:41,232 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-08 12:00:41,232 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50371.0, 'new_value': 56024.0}, {'field': 'total_amount', 'old_value': 50371.0, 'new_value': 56024.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 113}]
2025-05-08 12:00:41,233 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-08 12:00:41,658 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-08 12:00:41,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13632.0, 'new_value': 23972.53}, {'field': 'total_amount', 'old_value': 13632.0, 'new_value': 23972.53}, {'field': 'order_count', 'old_value': 37, 'new_value': 49}]
2025-05-08 12:00:41,658 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-08 12:00:42,230 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-08 12:00:42,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64766.63, 'new_value': 68703.48}, {'field': 'offline_amount', 'old_value': 11777.9, 'new_value': 12475.14}, {'field': 'total_amount', 'old_value': 76544.53, 'new_value': 81178.62}, {'field': 'order_count', 'old_value': 274, 'new_value': 294}]
2025-05-08 12:00:42,230 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-08 12:00:42,668 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-08 12:00:42,668 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67914.0, 'new_value': 71642.0}, {'field': 'offline_amount', 'old_value': 20128.2, 'new_value': 22495.85}, {'field': 'total_amount', 'old_value': 88042.2, 'new_value': 94137.85}, {'field': 'order_count', 'old_value': 527, 'new_value': 561}]
2025-05-08 12:00:42,668 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-08 12:00:43,091 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-08 12:00:43,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11383.36, 'new_value': 13182.49}, {'field': 'offline_amount', 'old_value': 15696.65, 'new_value': 16991.69}, {'field': 'total_amount', 'old_value': 27080.01, 'new_value': 30174.18}, {'field': 'order_count', 'old_value': 1290, 'new_value': 1467}]
2025-05-08 12:00:43,091 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-08 12:00:43,495 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-08 12:00:43,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1932.0, 'new_value': 2530.0}, {'field': 'total_amount', 'old_value': 2932.0, 'new_value': 3530.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 62}]
2025-05-08 12:00:43,495 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-08 12:00:44,005 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-08 12:00:44,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20292.86, 'new_value': 24851.07}, {'field': 'total_amount', 'old_value': 20292.86, 'new_value': 24851.07}, {'field': 'order_count', 'old_value': 498, 'new_value': 627}]
2025-05-08 12:00:44,005 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-08 12:00:44,499 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-08 12:00:44,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42622.0, 'new_value': 44358.0}, {'field': 'total_amount', 'old_value': 42622.0, 'new_value': 44358.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-08 12:00:44,499 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-08 12:00:45,015 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-08 12:00:45,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45048.0, 'new_value': 48626.0}, {'field': 'offline_amount', 'old_value': 19512.65, 'new_value': 21674.55}, {'field': 'total_amount', 'old_value': 64560.65, 'new_value': 70300.55}, {'field': 'order_count', 'old_value': 441, 'new_value': 488}]
2025-05-08 12:00:45,016 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-08 12:00:45,487 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-08 12:00:45,487 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12152.34, 'new_value': 15142.02}, {'field': 'offline_amount', 'old_value': 138479.44, 'new_value': 153179.16}, {'field': 'total_amount', 'old_value': 150631.78, 'new_value': 168321.18}, {'field': 'order_count', 'old_value': 471, 'new_value': 529}]
2025-05-08 12:00:45,488 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-08 12:00:45,975 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-08 12:00:45,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2474.74, 'new_value': 3180.18}, {'field': 'offline_amount', 'old_value': 36119.48, 'new_value': 40477.42}, {'field': 'total_amount', 'old_value': 38594.22, 'new_value': 43657.6}, {'field': 'order_count', 'old_value': 600, 'new_value': 674}]
2025-05-08 12:00:45,975 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-08 12:00:46,419 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-08 12:00:46,419 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3066.07, 'new_value': 3849.03}, {'field': 'offline_amount', 'old_value': 45196.21, 'new_value': 53058.91}, {'field': 'total_amount', 'old_value': 48262.28, 'new_value': 56907.94}, {'field': 'order_count', 'old_value': 501, 'new_value': 612}]
2025-05-08 12:00:46,420 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-08 12:00:46,942 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-08 12:00:46,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54640.0, 'new_value': 65359.0}, {'field': 'total_amount', 'old_value': 54640.0, 'new_value': 65359.0}, {'field': 'order_count', 'old_value': 272, 'new_value': 323}]
2025-05-08 12:00:46,942 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-08 12:00:47,407 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-08 12:00:47,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60126.44, 'new_value': 63715.6}, {'field': 'total_amount', 'old_value': 60126.44, 'new_value': 63715.6}, {'field': 'order_count', 'old_value': 185, 'new_value': 198}]
2025-05-08 12:00:47,407 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-08 12:00:47,905 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-08 12:00:47,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35584.0, 'new_value': 40391.0}, {'field': 'total_amount', 'old_value': 35584.0, 'new_value': 40391.0}, {'field': 'order_count', 'old_value': 815, 'new_value': 943}]
2025-05-08 12:00:47,905 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-08 12:00:48,399 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-08 12:00:48,399 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19912.94, 'new_value': 23908.96}, {'field': 'offline_amount', 'old_value': 227545.22, 'new_value': 253814.32}, {'field': 'total_amount', 'old_value': 247458.16, 'new_value': 277723.28}, {'field': 'order_count', 'old_value': 715, 'new_value': 810}]
2025-05-08 12:00:48,399 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-08 12:00:48,849 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-08 12:00:48,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6678.0, 'new_value': 7046.0}, {'field': 'total_amount', 'old_value': 6678.0, 'new_value': 7046.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-08 12:00:48,849 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-08 12:00:49,265 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-08 12:00:49,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8287.23, 'new_value': 8805.25}, {'field': 'total_amount', 'old_value': 8287.23, 'new_value': 8805.25}, {'field': 'order_count', 'old_value': 25, 'new_value': 32}]
2025-05-08 12:00:49,265 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-08 12:00:49,695 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-08 12:00:49,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35617.0, 'new_value': 42584.0}, {'field': 'total_amount', 'old_value': 35617.0, 'new_value': 42584.0}, {'field': 'order_count', 'old_value': 1203, 'new_value': 1466}]
2025-05-08 12:00:49,695 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-08 12:00:50,229 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-08 12:00:50,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189236.53, 'new_value': 215118.31}, {'field': 'total_amount', 'old_value': 189236.53, 'new_value': 215118.31}, {'field': 'order_count', 'old_value': 1193, 'new_value': 1422}]
2025-05-08 12:00:50,229 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-08 12:00:50,715 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-08 12:00:50,715 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11225.0, 'new_value': 11810.0}, {'field': 'offline_amount', 'old_value': 125680.0, 'new_value': 131915.0}, {'field': 'total_amount', 'old_value': 136905.0, 'new_value': 143725.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-05-08 12:00:50,716 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-08 12:00:51,153 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-08 12:00:51,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29628.55, 'new_value': 34586.36}, {'field': 'total_amount', 'old_value': 29628.55, 'new_value': 34586.36}, {'field': 'order_count', 'old_value': 1105, 'new_value': 1296}]
2025-05-08 12:00:51,153 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-08 12:00:51,624 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-08 12:00:51,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5813.32, 'new_value': 7483.02}, {'field': 'total_amount', 'old_value': 5813.32, 'new_value': 7483.02}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-05-08 12:00:51,625 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-08 12:00:52,051 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-08 12:00:52,051 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53016.06, 'new_value': 65022.12}, {'field': 'total_amount', 'old_value': 53698.06, 'new_value': 65704.12}, {'field': 'order_count', 'old_value': 580, 'new_value': 747}]
2025-05-08 12:00:52,051 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-08 12:00:52,456 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-08 12:00:52,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43215.0, 'new_value': 43648.0}, {'field': 'total_amount', 'old_value': 43215.0, 'new_value': 43648.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-05-08 12:00:52,457 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-08 12:00:52,879 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-08 12:00:52,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118493.0, 'new_value': 133447.0}, {'field': 'total_amount', 'old_value': 118493.0, 'new_value': 133447.0}, {'field': 'order_count', 'old_value': 183, 'new_value': 210}]
2025-05-08 12:00:52,880 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-08 12:00:53,305 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-08 12:00:53,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47331.12, 'new_value': 53169.28}, {'field': 'total_amount', 'old_value': 47331.12, 'new_value': 53169.28}, {'field': 'order_count', 'old_value': 240, 'new_value': 271}]
2025-05-08 12:00:53,306 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-08 12:00:53,734 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-08 12:00:53,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5855.0, 'new_value': 9842.0}, {'field': 'total_amount', 'old_value': 5855.0, 'new_value': 9842.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 40}]
2025-05-08 12:00:53,734 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-08 12:00:54,201 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-08 12:00:54,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 65.55}, {'field': 'offline_amount', 'old_value': 15492.1, 'new_value': 15591.1}, {'field': 'total_amount', 'old_value': 15492.1, 'new_value': 15656.65}, {'field': 'order_count', 'old_value': 143, 'new_value': 146}]
2025-05-08 12:00:54,201 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-08 12:00:54,683 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-08 12:00:54,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18878.32, 'new_value': 21509.4}, {'field': 'total_amount', 'old_value': 18878.32, 'new_value': 21509.4}, {'field': 'order_count', 'old_value': 823, 'new_value': 959}]
2025-05-08 12:00:54,684 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-08 12:00:55,115 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-08 12:00:55,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 585990.6, 'new_value': 631336.9}, {'field': 'total_amount', 'old_value': 585990.6, 'new_value': 631336.9}, {'field': 'order_count', 'old_value': 984, 'new_value': 1069}]
2025-05-08 12:00:55,115 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-08 12:00:55,516 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-08 12:00:55,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35082.25, 'new_value': 38563.25}, {'field': 'offline_amount', 'old_value': 18717.0, 'new_value': 21781.0}, {'field': 'total_amount', 'old_value': 53799.25, 'new_value': 60344.25}, {'field': 'order_count', 'old_value': 293, 'new_value': 333}]
2025-05-08 12:00:55,516 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-08 12:00:55,981 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-08 12:00:55,981 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12708.8, 'new_value': 15493.8}, {'field': 'total_amount', 'old_value': 12708.8, 'new_value': 15493.8}, {'field': 'order_count', 'old_value': 45, 'new_value': 55}]
2025-05-08 12:00:55,981 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-08 12:00:56,446 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-08 12:00:56,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11607.0, 'new_value': 17141.0}, {'field': 'total_amount', 'old_value': 26754.0, 'new_value': 32288.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 14}]
2025-05-08 12:00:56,446 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-08 12:00:56,975 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-08 12:00:56,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9860.0, 'new_value': 9918.0}, {'field': 'total_amount', 'old_value': 9860.0, 'new_value': 9918.0}, {'field': 'order_count', 'old_value': 170, 'new_value': 171}]
2025-05-08 12:00:56,976 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-08 12:00:57,491 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-08 12:00:57,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1432.8, 'new_value': 1544.8}, {'field': 'offline_amount', 'old_value': 3917.76, 'new_value': 4408.76}, {'field': 'total_amount', 'old_value': 5350.56, 'new_value': 5953.56}, {'field': 'order_count', 'old_value': 182, 'new_value': 205}]
2025-05-08 12:00:57,491 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-08 12:00:57,948 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-08 12:00:57,948 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43253.89, 'new_value': 49720.61}, {'field': 'offline_amount', 'old_value': 30651.63, 'new_value': 38085.63}, {'field': 'total_amount', 'old_value': 73905.52, 'new_value': 87806.24}, {'field': 'order_count', 'old_value': 615, 'new_value': 733}]
2025-05-08 12:00:57,948 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-08 12:00:58,469 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-08 12:00:58,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6999.0, 'new_value': 13997.0}, {'field': 'total_amount', 'old_value': 6999.0, 'new_value': 13997.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-08 12:00:58,470 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-08 12:00:58,916 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-08 12:00:58,916 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19423.06, 'new_value': 21847.08}, {'field': 'total_amount', 'old_value': 19423.06, 'new_value': 21847.08}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-08 12:00:58,916 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-08 12:00:59,360 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-08 12:00:59,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116915.9, 'new_value': 121265.9}, {'field': 'offline_amount', 'old_value': 22667.0, 'new_value': 23561.0}, {'field': 'total_amount', 'old_value': 139582.9, 'new_value': 144826.9}, {'field': 'order_count', 'old_value': 170, 'new_value': 179}]
2025-05-08 12:00:59,361 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-08 12:00:59,825 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-08 12:00:59,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8271.0, 'new_value': 9357.0}, {'field': 'total_amount', 'old_value': 8271.0, 'new_value': 9357.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-05-08 12:00:59,825 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-08 12:01:00,413 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-08 12:01:00,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10594.0, 'new_value': 21262.0}, {'field': 'total_amount', 'old_value': 10594.0, 'new_value': 21262.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-05-08 12:01:00,413 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-08 12:01:00,866 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-08 12:01:00,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33750.2, 'new_value': 37602.2}, {'field': 'total_amount', 'old_value': 39288.2, 'new_value': 43140.2}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-08 12:01:00,867 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-08 12:01:01,272 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-08 12:01:01,272 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9898.82, 'new_value': 11906.68}, {'field': 'offline_amount', 'old_value': 33317.67, 'new_value': 35346.9}, {'field': 'total_amount', 'old_value': 43216.49, 'new_value': 47253.58}, {'field': 'order_count', 'old_value': 1532, 'new_value': 1689}]
2025-05-08 12:01:01,272 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-08 12:01:01,758 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-08 12:01:01,758 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9068.0, 'new_value': 12590.0}, {'field': 'offline_amount', 'old_value': 53775.0, 'new_value': 57819.0}, {'field': 'total_amount', 'old_value': 62843.0, 'new_value': 70409.0}, {'field': 'order_count', 'old_value': 1119, 'new_value': 1317}]
2025-05-08 12:01:01,758 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-08 12:01:02,185 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-08 12:01:02,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3434.52, 'new_value': 4016.22}, {'field': 'offline_amount', 'old_value': 75481.7, 'new_value': 85168.3}, {'field': 'total_amount', 'old_value': 78916.22, 'new_value': 89184.52}, {'field': 'order_count', 'old_value': 537, 'new_value': 648}]
2025-05-08 12:01:02,185 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-08 12:01:02,613 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-08 12:01:02,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3998.0, 'new_value': 4154.0}, {'field': 'total_amount', 'old_value': 4540.0, 'new_value': 4696.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 12:01:02,613 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-08 12:01:03,058 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-08 12:01:03,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19972.0, 'new_value': 27049.0}, {'field': 'total_amount', 'old_value': 19972.0, 'new_value': 27049.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-08 12:01:03,059 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-08 12:01:03,578 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-08 12:01:03,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43234.7, 'new_value': 46637.58}, {'field': 'offline_amount', 'old_value': 40171.45, 'new_value': 42460.45}, {'field': 'total_amount', 'old_value': 83406.15, 'new_value': 89098.03}, {'field': 'order_count', 'old_value': 766, 'new_value': 838}]
2025-05-08 12:01:03,578 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-08 12:01:04,035 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-08 12:01:04,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 372548.02, 'new_value': 375246.02}, {'field': 'offline_amount', 'old_value': 90783.91, 'new_value': 90798.91}, {'field': 'total_amount', 'old_value': 463331.93, 'new_value': 466044.93}, {'field': 'order_count', 'old_value': 4656, 'new_value': 4671}]
2025-05-08 12:01:04,035 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-08 12:01:04,595 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-08 12:01:04,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17999.0, 'new_value': 20999.0}, {'field': 'offline_amount', 'old_value': 795.55, 'new_value': 888.35}, {'field': 'total_amount', 'old_value': 18794.55, 'new_value': 21887.35}, {'field': 'order_count', 'old_value': 57, 'new_value': 61}]
2025-05-08 12:01:04,595 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-08 12:01:05,180 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-08 12:01:05,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21497.66, 'new_value': 23385.38}, {'field': 'total_amount', 'old_value': 21497.66, 'new_value': 23385.38}, {'field': 'order_count', 'old_value': 588, 'new_value': 644}]
2025-05-08 12:01:05,180 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-08 12:01:05,602 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-08 12:01:05,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19462.0, 'new_value': 55662.0}, {'field': 'total_amount', 'old_value': 19462.0, 'new_value': 55662.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 12}]
2025-05-08 12:01:05,602 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-08 12:01:05,970 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-08 12:01:05,970 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24457.16, 'new_value': 29414.81}, {'field': 'offline_amount', 'old_value': 123151.45, 'new_value': 134160.45}, {'field': 'total_amount', 'old_value': 147608.61, 'new_value': 163575.26}, {'field': 'order_count', 'old_value': 852, 'new_value': 898}]
2025-05-08 12:01:05,971 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-08 12:01:06,445 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-08 12:01:06,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74962.29, 'new_value': 92865.35}, {'field': 'total_amount', 'old_value': 74962.29, 'new_value': 92865.35}, {'field': 'order_count', 'old_value': 172, 'new_value': 199}]
2025-05-08 12:01:06,446 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-08 12:01:06,874 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-08 12:01:06,874 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4460.76, 'new_value': 5094.97}, {'field': 'offline_amount', 'old_value': 143364.87, 'new_value': 155286.67}, {'field': 'total_amount', 'old_value': 147825.63, 'new_value': 160381.64}, {'field': 'order_count', 'old_value': 539, 'new_value': 600}]
2025-05-08 12:01:06,874 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-08 12:01:07,316 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-08 12:01:07,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30054.0, 'new_value': 33022.0}, {'field': 'offline_amount', 'old_value': 18303.0, 'new_value': 18841.25}, {'field': 'total_amount', 'old_value': 48357.0, 'new_value': 51863.25}, {'field': 'order_count', 'old_value': 58, 'new_value': 63}]
2025-05-08 12:01:07,317 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-08 12:01:07,779 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-08 12:01:07,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36823.83, 'new_value': 37970.05}, {'field': 'total_amount', 'old_value': 36823.83, 'new_value': 37970.05}, {'field': 'order_count', 'old_value': 1378, 'new_value': 1433}]
2025-05-08 12:01:07,779 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-08 12:01:08,273 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-08 12:01:08,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10205.0, 'new_value': 22876.0}, {'field': 'total_amount', 'old_value': 10205.0, 'new_value': 22876.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 12:01:08,273 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-08 12:01:08,680 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-08 12:01:08,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13646.12, 'new_value': 17340.36}, {'field': 'total_amount', 'old_value': 13646.12, 'new_value': 17340.36}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-05-08 12:01:08,680 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-08 12:01:09,183 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-08 12:01:09,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4612.1, 'new_value': 5061.8}, {'field': 'total_amount', 'old_value': 7893.14, 'new_value': 8342.84}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-05-08 12:01:09,183 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-08 12:01:09,620 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-08 12:01:09,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40856.97, 'new_value': 43471.5}, {'field': 'total_amount', 'old_value': 40856.97, 'new_value': 43471.5}, {'field': 'order_count', 'old_value': 242, 'new_value': 255}]
2025-05-08 12:01:09,621 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-08 12:01:10,139 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-08 12:01:10,139 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 538.45, 'new_value': 714.69}, {'field': 'offline_amount', 'old_value': 7910.88, 'new_value': 8682.88}, {'field': 'total_amount', 'old_value': 8449.33, 'new_value': 9397.57}, {'field': 'order_count', 'old_value': 372, 'new_value': 409}]
2025-05-08 12:01:10,139 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-08 12:01:10,535 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-08 12:01:10,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20684.0, 'new_value': 23493.0}, {'field': 'total_amount', 'old_value': 20684.0, 'new_value': 23493.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 14}]
2025-05-08 12:01:10,536 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-08 12:01:11,146 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-08 12:01:11,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1778.0, 'new_value': 1828.0}, {'field': 'total_amount', 'old_value': 1778.0, 'new_value': 1828.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 12:01:11,147 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-08 12:01:11,582 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-08 12:01:11,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39174.2, 'new_value': 42659.4}, {'field': 'total_amount', 'old_value': 39174.2, 'new_value': 42659.4}, {'field': 'order_count', 'old_value': 112, 'new_value': 120}]
2025-05-08 12:01:11,583 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-08 12:01:12,094 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-08 12:01:12,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35352.09, 'new_value': 41335.54}, {'field': 'offline_amount', 'old_value': 132657.08, 'new_value': 138638.8}, {'field': 'total_amount', 'old_value': 168009.17, 'new_value': 179974.34}, {'field': 'order_count', 'old_value': 3805, 'new_value': 4224}]
2025-05-08 12:01:12,094 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-08 12:01:12,523 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-08 12:01:12,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16150.0, 'new_value': 16648.0}, {'field': 'total_amount', 'old_value': 16150.0, 'new_value': 16648.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-08 12:01:12,524 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-08 12:01:12,975 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-08 12:01:12,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1928.76, 'new_value': 2268.69}, {'field': 'offline_amount', 'old_value': 2745.0, 'new_value': 5717.0}, {'field': 'total_amount', 'old_value': 4673.76, 'new_value': 7985.69}, {'field': 'order_count', 'old_value': 53, 'new_value': 63}]
2025-05-08 12:01:12,975 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-08 12:01:13,550 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-08 12:01:13,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14716.0, 'new_value': 15481.0}, {'field': 'total_amount', 'old_value': 14716.0, 'new_value': 15481.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-08 12:01:13,551 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-08 12:01:14,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-08 12:01:14,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18712.71, 'new_value': 19063.02}, {'field': 'total_amount', 'old_value': 18712.71, 'new_value': 19063.02}, {'field': 'order_count', 'old_value': 147, 'new_value': 155}]
2025-05-08 12:01:14,062 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-08 12:01:14,545 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-08 12:01:14,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83.0, 'new_value': 191.5}, {'field': 'total_amount', 'old_value': 83.0, 'new_value': 191.5}, {'field': 'order_count', 'old_value': 11, 'new_value': 19}]
2025-05-08 12:01:14,545 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-08 12:01:14,966 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-08 12:01:14,966 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1525.0, 'new_value': 1662.0}, {'field': 'offline_amount', 'old_value': 7217.4, 'new_value': 7975.4}, {'field': 'total_amount', 'old_value': 8742.4, 'new_value': 9637.4}, {'field': 'order_count', 'old_value': 329, 'new_value': 369}]
2025-05-08 12:01:14,967 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-08 12:01:15,409 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-08 12:01:15,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39205.78, 'new_value': 41363.78}, {'field': 'total_amount', 'old_value': 39205.78, 'new_value': 41363.78}, {'field': 'order_count', 'old_value': 103, 'new_value': 113}]
2025-05-08 12:01:15,409 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-08 12:01:15,898 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-08 12:01:15,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35620.0, 'new_value': 39000.0}, {'field': 'total_amount', 'old_value': 35620.0, 'new_value': 39000.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-08 12:01:15,898 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-08 12:01:16,339 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-08 12:01:16,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10899.0, 'new_value': 22897.0}, {'field': 'total_amount', 'old_value': 10899.0, 'new_value': 22897.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-08 12:01:16,339 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-08 12:01:16,824 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-08 12:01:16,824 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3937.56, 'new_value': 5082.18}, {'field': 'offline_amount', 'old_value': 7391.42, 'new_value': 8733.29}, {'field': 'total_amount', 'old_value': 11328.98, 'new_value': 13815.47}, {'field': 'order_count', 'old_value': 468, 'new_value': 573}]
2025-05-08 12:01:16,824 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-08 12:01:17,375 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-08 12:01:17,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59059.0, 'new_value': 64961.9}, {'field': 'total_amount', 'old_value': 59059.0, 'new_value': 64961.9}, {'field': 'order_count', 'old_value': 210, 'new_value': 233}]
2025-05-08 12:01:17,375 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-08 12:01:17,796 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-08 12:01:17,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176210.0, 'new_value': 203295.0}, {'field': 'total_amount', 'old_value': 176210.0, 'new_value': 203295.0}, {'field': 'order_count', 'old_value': 836, 'new_value': 937}]
2025-05-08 12:01:17,797 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-08 12:01:18,230 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-08 12:01:18,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13187.9, 'new_value': 13426.3}, {'field': 'offline_amount', 'old_value': 90234.7, 'new_value': 94248.9}, {'field': 'total_amount', 'old_value': 103422.6, 'new_value': 107675.2}, {'field': 'order_count', 'old_value': 735, 'new_value': 784}]
2025-05-08 12:01:18,230 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-08 12:01:18,650 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-08 12:01:18,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2686.0, 'new_value': 2785.0}, {'field': 'total_amount', 'old_value': 2686.0, 'new_value': 2785.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-08 12:01:18,651 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-08 12:01:19,108 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-08 12:01:19,108 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65144.85, 'new_value': 76958.67}, {'field': 'total_amount', 'old_value': 65144.85, 'new_value': 76958.67}, {'field': 'order_count', 'old_value': 1707, 'new_value': 2024}]
2025-05-08 12:01:19,108 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-08 12:01:19,535 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-08 12:01:19,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41308.11, 'new_value': 46924.67}, {'field': 'total_amount', 'old_value': 41308.11, 'new_value': 46924.67}, {'field': 'order_count', 'old_value': 190, 'new_value': 217}]
2025-05-08 12:01:19,536 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-08 12:01:19,981 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-08 12:01:19,982 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5426.02, 'new_value': 6367.92}, {'field': 'offline_amount', 'old_value': 9691.24, 'new_value': 11709.68}, {'field': 'total_amount', 'old_value': 15117.26, 'new_value': 18077.6}, {'field': 'order_count', 'old_value': 547, 'new_value': 656}]
2025-05-08 12:01:19,982 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-08 12:01:20,509 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-08 12:01:20,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11404.0, 'new_value': 14947.0}, {'field': 'total_amount', 'old_value': 13812.0, 'new_value': 17355.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 71}]
2025-05-08 12:01:20,509 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-08 12:01:21,025 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-08 12:01:21,025 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18996.0, 'new_value': 27760.0}, {'field': 'total_amount', 'old_value': 18996.0, 'new_value': 27760.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-08 12:01:21,025 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-08 12:01:21,570 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-08 12:01:21,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10139.7, 'new_value': 10371.7}, {'field': 'offline_amount', 'old_value': 20709.76, 'new_value': 21619.76}, {'field': 'total_amount', 'old_value': 30849.46, 'new_value': 31991.46}, {'field': 'order_count', 'old_value': 319, 'new_value': 335}]
2025-05-08 12:01:21,570 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-08 12:01:22,016 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-08 12:01:22,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48025.0, 'new_value': 48469.0}, {'field': 'total_amount', 'old_value': 48025.0, 'new_value': 48469.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 37}]
2025-05-08 12:01:22,016 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-08 12:01:22,662 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-08 12:01:22,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32407.2, 'new_value': 34665.0}, {'field': 'offline_amount', 'old_value': 29221.67, 'new_value': 32771.54}, {'field': 'total_amount', 'old_value': 61628.87, 'new_value': 67436.54}, {'field': 'order_count', 'old_value': 424, 'new_value': 454}]
2025-05-08 12:01:22,663 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-08 12:01:23,069 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-08 12:01:23,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3521.52}, {'field': 'total_amount', 'old_value': 42854.96, 'new_value': 46376.48}, {'field': 'order_count', 'old_value': 7454, 'new_value': 4213}]
2025-05-08 12:01:23,070 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-08 12:01:23,551 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-08 12:01:23,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2389.78, 'new_value': 2698.78}, {'field': 'offline_amount', 'old_value': 26376.34, 'new_value': 27710.86}, {'field': 'total_amount', 'old_value': 28766.12, 'new_value': 30409.64}, {'field': 'order_count', 'old_value': 933, 'new_value': 984}]
2025-05-08 12:01:23,552 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-08 12:01:24,024 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-08 12:01:24,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3252.04, 'new_value': 4070.05}, {'field': 'offline_amount', 'old_value': 8007.07, 'new_value': 9767.71}, {'field': 'total_amount', 'old_value': 11259.11, 'new_value': 13837.76}, {'field': 'order_count', 'old_value': 587, 'new_value': 727}]
2025-05-08 12:01:24,024 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-08 12:01:24,455 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-08 12:01:24,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57057.5, 'new_value': 57715.5}, {'field': 'total_amount', 'old_value': 57057.5, 'new_value': 57715.5}, {'field': 'order_count', 'old_value': 526, 'new_value': 537}]
2025-05-08 12:01:24,456 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-08 12:01:24,963 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-08 12:01:24,963 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1862.38, 'new_value': 2134.78}, {'field': 'offline_amount', 'old_value': 5315.7, 'new_value': 5626.54}, {'field': 'total_amount', 'old_value': 7178.08, 'new_value': 7761.32}, {'field': 'order_count', 'old_value': 472, 'new_value': 525}]
2025-05-08 12:01:24,963 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-08 12:01:25,412 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-08 12:01:25,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18389.0, 'new_value': 19666.0}, {'field': 'total_amount', 'old_value': 18389.0, 'new_value': 19666.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-05-08 12:01:25,412 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-08 12:01:25,887 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-08 12:01:25,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1600571.0, 'new_value': 1811467.0}, {'field': 'total_amount', 'old_value': 1600571.0, 'new_value': 1811467.0}, {'field': 'order_count', 'old_value': 25218, 'new_value': 29106}]
2025-05-08 12:01:25,887 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-08 12:01:26,384 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-08 12:01:26,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22534.0, 'new_value': 23401.0}, {'field': 'total_amount', 'old_value': 22534.0, 'new_value': 23401.0}, {'field': 'order_count', 'old_value': 147, 'new_value': 153}]
2025-05-08 12:01:26,384 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-08 12:01:26,815 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-08 12:01:26,815 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15287.33, 'new_value': 16483.23}, {'field': 'offline_amount', 'old_value': 150337.39, 'new_value': 155572.24}, {'field': 'total_amount', 'old_value': 165624.72, 'new_value': 172055.47}, {'field': 'order_count', 'old_value': 1264, 'new_value': 1328}]
2025-05-08 12:01:26,815 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-08 12:01:27,326 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-08 12:01:27,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3995.0}, {'field': 'offline_amount', 'old_value': 162415.0, 'new_value': 169111.0}, {'field': 'total_amount', 'old_value': 162415.0, 'new_value': 173106.0}, {'field': 'order_count', 'old_value': 1246, 'new_value': 1337}]
2025-05-08 12:01:27,326 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-08 12:01:27,826 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-08 12:01:27,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13607.0, 'new_value': 16471.0}, {'field': 'total_amount', 'old_value': 13607.0, 'new_value': 16471.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-05-08 12:01:27,826 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-08 12:01:28,375 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-08 12:01:28,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37626.17, 'new_value': 40717.07}, {'field': 'total_amount', 'old_value': 37626.17, 'new_value': 40717.07}, {'field': 'order_count', 'old_value': 1063, 'new_value': 1156}]
2025-05-08 12:01:28,375 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-08 12:01:28,896 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-08 12:01:28,896 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36229.19, 'new_value': 43590.69}, {'field': 'offline_amount', 'old_value': 69020.12, 'new_value': 77101.62}, {'field': 'total_amount', 'old_value': 105249.31, 'new_value': 120692.31}, {'field': 'order_count', 'old_value': 1022, 'new_value': 1164}]
2025-05-08 12:01:28,897 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-08 12:01:29,266 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-08 12:01:29,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13399.38, 'new_value': 14234.18}, {'field': 'total_amount', 'old_value': 13399.38, 'new_value': 14234.18}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-08 12:01:29,266 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-08 12:01:29,792 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-08 12:01:29,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26706.1, 'new_value': 29078.13}, {'field': 'offline_amount', 'old_value': 17508.9, 'new_value': 17854.7}, {'field': 'total_amount', 'old_value': 44215.0, 'new_value': 46932.83}, {'field': 'order_count', 'old_value': 2942, 'new_value': 3130}]
2025-05-08 12:01:29,793 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-08 12:01:30,230 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-08 12:01:30,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41396.26, 'new_value': 44535.26}, {'field': 'total_amount', 'old_value': 41396.26, 'new_value': 44535.26}, {'field': 'order_count', 'old_value': 70, 'new_value': 75}]
2025-05-08 12:01:30,231 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-08 12:01:30,732 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-08 12:01:30,733 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46668.0, 'new_value': 57600.0}, {'field': 'total_amount', 'old_value': 46668.0, 'new_value': 57600.0}, {'field': 'order_count', 'old_value': 3889, 'new_value': 4800}]
2025-05-08 12:01:30,733 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-08 12:01:31,169 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-08 12:01:31,170 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36402.3, 'new_value': 42730.3}, {'field': 'total_amount', 'old_value': 36402.3, 'new_value': 42730.3}, {'field': 'order_count', 'old_value': 352, 'new_value': 372}]
2025-05-08 12:01:31,170 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-08 12:01:31,695 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-08 12:01:31,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24932.16, 'new_value': 30117.07}, {'field': 'offline_amount', 'old_value': 82608.68, 'new_value': 87244.42}, {'field': 'total_amount', 'old_value': 107540.84, 'new_value': 117361.49}, {'field': 'order_count', 'old_value': 3225, 'new_value': 3620}]
2025-05-08 12:01:31,695 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-08 12:01:32,167 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-08 12:01:32,167 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44570.6, 'new_value': 47718.77}, {'field': 'offline_amount', 'old_value': 97626.4, 'new_value': 101811.56}, {'field': 'total_amount', 'old_value': 142197.0, 'new_value': 149530.33}, {'field': 'order_count', 'old_value': 1164, 'new_value': 1333}]
2025-05-08 12:01:32,167 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-08 12:01:32,700 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-08 12:01:32,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2780.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2780.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-08 12:01:32,700 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-08 12:01:33,196 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-08 12:01:33,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18151.1, 'new_value': 18889.1}, {'field': 'total_amount', 'old_value': 18151.1, 'new_value': 18889.1}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-08 12:01:33,196 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-08 12:01:33,696 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-08 12:01:33,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12183.2, 'new_value': 14044.2}, {'field': 'total_amount', 'old_value': 12183.2, 'new_value': 14044.2}, {'field': 'order_count', 'old_value': 589, 'new_value': 686}]
2025-05-08 12:01:33,697 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-08 12:01:34,137 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-08 12:01:34,137 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92416.42, 'new_value': 98463.19}, {'field': 'offline_amount', 'old_value': 5517.7, 'new_value': 5827.3}, {'field': 'total_amount', 'old_value': 97934.12, 'new_value': 104290.49}, {'field': 'order_count', 'old_value': 3607, 'new_value': 3848}]
2025-05-08 12:01:34,137 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-08 12:01:34,650 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-08 12:01:34,650 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8805.2, 'new_value': 11655.2}, {'field': 'offline_amount', 'old_value': 34668.8, 'new_value': 38910.8}, {'field': 'total_amount', 'old_value': 43474.0, 'new_value': 50566.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 72}]
2025-05-08 12:01:34,650 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-08 12:01:35,133 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-08 12:01:35,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11813.75, 'new_value': 13331.1}, {'field': 'total_amount', 'old_value': 11813.75, 'new_value': 13331.1}, {'field': 'order_count', 'old_value': 497, 'new_value': 576}]
2025-05-08 12:01:35,133 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-08 12:01:35,621 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-08 12:01:35,622 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3524.91, 'new_value': 3919.87}, {'field': 'offline_amount', 'old_value': 69027.78, 'new_value': 79265.3}, {'field': 'total_amount', 'old_value': 72552.69, 'new_value': 83185.17}, {'field': 'order_count', 'old_value': 861, 'new_value': 1018}]
2025-05-08 12:01:35,622 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-08 12:01:36,042 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-08 12:01:36,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19079.0, 'new_value': 19340.0}, {'field': 'total_amount', 'old_value': 19079.0, 'new_value': 19340.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 90}]
2025-05-08 12:01:36,042 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-08 12:01:36,513 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-08 12:01:36,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 8817.99}, {'field': 'offline_amount', 'old_value': 111977.0, 'new_value': 123633.0}, {'field': 'total_amount', 'old_value': 111977.0, 'new_value': 132450.99}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-05-08 12:01:36,513 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-08 12:01:37,046 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-08 12:01:37,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35891.94, 'new_value': 39878.1}, {'field': 'offline_amount', 'old_value': 91305.3, 'new_value': 96242.82}, {'field': 'total_amount', 'old_value': 127197.24, 'new_value': 136120.92}, {'field': 'order_count', 'old_value': 1432, 'new_value': 1528}]
2025-05-08 12:01:37,046 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-08 12:01:37,495 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-08 12:01:37,496 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4519.0, 'new_value': 5348.0}, {'field': 'offline_amount', 'old_value': 42278.0, 'new_value': 52078.0}, {'field': 'total_amount', 'old_value': 46797.0, 'new_value': 57426.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 59}]
2025-05-08 12:01:37,496 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-08 12:01:37,968 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-08 12:01:37,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38539.55, 'new_value': 44037.85}, {'field': 'total_amount', 'old_value': 73825.55, 'new_value': 79323.85}, {'field': 'order_count', 'old_value': 1765, 'new_value': 1931}]
2025-05-08 12:01:37,968 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-08 12:01:38,394 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-08 12:01:38,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29514.5, 'new_value': 31097.0}, {'field': 'total_amount', 'old_value': 29514.5, 'new_value': 31097.0}, {'field': 'order_count', 'old_value': 230, 'new_value': 241}]
2025-05-08 12:01:38,394 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-08 12:01:38,894 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-08 12:01:38,894 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 9377.0}, {'field': 'offline_amount', 'old_value': 93672.19, 'new_value': 97501.99}, {'field': 'total_amount', 'old_value': 93672.19, 'new_value': 106878.99}, {'field': 'order_count', 'old_value': 3519, 'new_value': 3998}]
2025-05-08 12:01:38,894 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-08 12:01:39,277 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-08 12:01:39,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5806.0, 'new_value': 6890.0}, {'field': 'total_amount', 'old_value': 5806.0, 'new_value': 6890.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 13}]
2025-05-08 12:01:39,278 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-08 12:01:39,730 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-08 12:01:39,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7442.0, 'new_value': 8399.0}, {'field': 'total_amount', 'old_value': 7442.0, 'new_value': 8399.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 92}]
2025-05-08 12:01:39,730 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-08 12:01:40,192 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-08 12:01:40,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12972.16, 'new_value': 15228.89}, {'field': 'offline_amount', 'old_value': 9214.34, 'new_value': 10074.53}, {'field': 'total_amount', 'old_value': 22186.5, 'new_value': 25303.42}, {'field': 'order_count', 'old_value': 1242, 'new_value': 1434}]
2025-05-08 12:01:40,192 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-08 12:01:40,648 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-08 12:01:40,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25954.4, 'new_value': 26190.4}, {'field': 'total_amount', 'old_value': 25954.4, 'new_value': 26190.4}]
2025-05-08 12:01:40,648 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-08 12:01:41,081 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-08 12:01:41,081 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33813.74, 'new_value': 40917.74}, {'field': 'total_amount', 'old_value': 47022.46, 'new_value': 54126.46}, {'field': 'order_count', 'old_value': 2718, 'new_value': 3078}]
2025-05-08 12:01:41,081 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-08 12:01:41,573 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-08 12:01:41,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13412.0, 'new_value': 13887.0}, {'field': 'total_amount', 'old_value': 13412.0, 'new_value': 13887.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 128}]
2025-05-08 12:01:41,573 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-08 12:01:42,108 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-08 12:01:42,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4790.78, 'new_value': 5090.78}, {'field': 'total_amount', 'old_value': 4790.78, 'new_value': 5090.78}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-08 12:01:42,109 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-08 12:01:42,657 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-08 12:01:42,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125816.45, 'new_value': 132557.21}, {'field': 'total_amount', 'old_value': 125816.45, 'new_value': 132557.21}, {'field': 'order_count', 'old_value': 448, 'new_value': 477}]
2025-05-08 12:01:42,657 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-08 12:01:43,098 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-08 12:01:43,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72870.4, 'new_value': 82580.5}, {'field': 'total_amount', 'old_value': 72870.4, 'new_value': 82580.5}, {'field': 'order_count', 'old_value': 1866, 'new_value': 2180}]
2025-05-08 12:01:43,098 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-08 12:01:43,617 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-08 12:01:43,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9262.75, 'new_value': 10741.36}, {'field': 'total_amount', 'old_value': 9262.75, 'new_value': 10741.36}, {'field': 'order_count', 'old_value': 1162, 'new_value': 1353}]
2025-05-08 12:01:43,617 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-08 12:01:44,178 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-08 12:01:44,178 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6098.66, 'new_value': 6806.04}, {'field': 'offline_amount', 'old_value': 9331.8, 'new_value': 10537.4}, {'field': 'total_amount', 'old_value': 15430.46, 'new_value': 17343.44}, {'field': 'order_count', 'old_value': 629, 'new_value': 726}]
2025-05-08 12:01:44,178 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-08 12:01:44,643 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-08 12:01:44,644 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21919.66, 'new_value': 23439.43}, {'field': 'offline_amount', 'old_value': 50756.8, 'new_value': 52104.7}, {'field': 'total_amount', 'old_value': 72676.46, 'new_value': 75544.13}, {'field': 'order_count', 'old_value': 673, 'new_value': 718}]
2025-05-08 12:01:44,644 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-08 12:01:45,070 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-08 12:01:45,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6214.0, 'new_value': 10566.0}, {'field': 'total_amount', 'old_value': 11415.0, 'new_value': 15767.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 40}]
2025-05-08 12:01:45,071 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-08 12:01:45,513 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-08 12:01:45,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 408032.66, 'new_value': 433668.57}, {'field': 'offline_amount', 'old_value': 43958.45, 'new_value': 73893.3}, {'field': 'total_amount', 'old_value': 451991.11, 'new_value': 507561.87}, {'field': 'order_count', 'old_value': 1571, 'new_value': 1832}]
2025-05-08 12:01:45,513 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-08 12:01:45,922 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-08 12:01:45,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16952.0, 'new_value': 27188.0}, {'field': 'total_amount', 'old_value': 16952.0, 'new_value': 27188.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-08 12:01:45,922 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-08 12:01:46,397 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-08 12:01:46,397 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6098.6, 'new_value': 6549.6}, {'field': 'offline_amount', 'old_value': 19243.9, 'new_value': 19342.9}, {'field': 'total_amount', 'old_value': 25342.5, 'new_value': 25892.5}, {'field': 'order_count', 'old_value': 53, 'new_value': 60}]
2025-05-08 12:01:46,397 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-08 12:01:46,890 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-08 12:01:46,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143844.4, 'new_value': 157801.92}, {'field': 'total_amount', 'old_value': 143844.4, 'new_value': 157801.92}, {'field': 'order_count', 'old_value': 732, 'new_value': 809}]
2025-05-08 12:01:46,890 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-08 12:01:47,335 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-08 12:01:47,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3209.0, 'new_value': 3365.0}, {'field': 'total_amount', 'old_value': 3307.0, 'new_value': 3463.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-05-08 12:01:47,335 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-08 12:01:47,765 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-08 12:01:47,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10670.0, 'new_value': 11451.0}, {'field': 'total_amount', 'old_value': 10670.0, 'new_value': 11451.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-05-08 12:01:47,766 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-08 12:01:48,187 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-08 12:01:48,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116907.0, 'new_value': 117057.0}, {'field': 'total_amount', 'old_value': 116907.0, 'new_value': 117057.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 61}]
2025-05-08 12:01:48,187 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-08 12:01:48,694 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-08 12:01:48,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28575.0, 'new_value': 30165.0}, {'field': 'total_amount', 'old_value': 28575.0, 'new_value': 30165.0}, {'field': 'order_count', 'old_value': 188, 'new_value': 203}]
2025-05-08 12:01:48,695 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-08 12:01:49,105 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-08 12:01:49,105 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35456.5, 'new_value': 36298.1}, {'field': 'offline_amount', 'old_value': 53639.0, 'new_value': 54673.2}, {'field': 'total_amount', 'old_value': 89095.5, 'new_value': 90971.3}, {'field': 'order_count', 'old_value': 1781, 'new_value': 1820}]
2025-05-08 12:01:49,105 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-08 12:01:49,505 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-08 12:01:49,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158435.63, 'new_value': 169219.64}, {'field': 'total_amount', 'old_value': 158435.63, 'new_value': 169219.64}, {'field': 'order_count', 'old_value': 2000, 'new_value': 2200}]
2025-05-08 12:01:49,506 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-08 12:01:50,054 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-08 12:01:50,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37730.0, 'new_value': 43990.0}, {'field': 'total_amount', 'old_value': 37730.0, 'new_value': 43990.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 12:01:50,055 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-08 12:01:50,596 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-08 12:01:50,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27004.03, 'new_value': 29033.59}, {'field': 'total_amount', 'old_value': 27004.03, 'new_value': 29033.59}, {'field': 'order_count', 'old_value': 848, 'new_value': 916}]
2025-05-08 12:01:50,597 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-08 12:01:51,136 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-08 12:01:51,136 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2169.44, 'new_value': 2532.92}, {'field': 'offline_amount', 'old_value': 10195.6, 'new_value': 10838.1}, {'field': 'total_amount', 'old_value': 12365.04, 'new_value': 13371.02}, {'field': 'order_count', 'old_value': 445, 'new_value': 478}]
2025-05-08 12:01:51,137 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-08 12:01:51,622 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-08 12:01:51,622 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7421.33, 'new_value': 9204.59}, {'field': 'offline_amount', 'old_value': 15246.72, 'new_value': 15263.3}, {'field': 'total_amount', 'old_value': 22668.05, 'new_value': 24467.89}, {'field': 'order_count', 'old_value': 89, 'new_value': 98}]
2025-05-08 12:01:51,622 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-08 12:01:52,091 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-08 12:01:52,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54685.56, 'new_value': 57345.72}, {'field': 'total_amount', 'old_value': 54685.56, 'new_value': 57345.72}, {'field': 'order_count', 'old_value': 1360, 'new_value': 1438}]
2025-05-08 12:01:52,091 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-08 12:01:52,505 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-08 12:01:52,505 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11572.01, 'new_value': 12870.96}, {'field': 'offline_amount', 'old_value': 114655.91, 'new_value': 119991.18}, {'field': 'total_amount', 'old_value': 126227.92, 'new_value': 132862.14}, {'field': 'order_count', 'old_value': 3063, 'new_value': 3182}]
2025-05-08 12:01:52,505 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-08 12:01:52,888 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-08 12:01:52,888 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1959.77, 'new_value': 2302.7}, {'field': 'offline_amount', 'old_value': 31282.5, 'new_value': 35552.8}, {'field': 'total_amount', 'old_value': 33242.27, 'new_value': 37855.5}, {'field': 'order_count', 'old_value': 2821, 'new_value': 3021}]
2025-05-08 12:01:52,888 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-08 12:01:53,370 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-08 12:01:53,371 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118834.02, 'new_value': 126386.52}, {'field': 'offline_amount', 'old_value': 4875.0, 'new_value': 4881.0}, {'field': 'total_amount', 'old_value': 123709.02, 'new_value': 131267.52}, {'field': 'order_count', 'old_value': 814, 'new_value': 907}]
2025-05-08 12:01:53,371 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-08 12:01:53,827 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-08 12:01:53,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5022.0, 'new_value': 5321.0}, {'field': 'total_amount', 'old_value': 5022.0, 'new_value': 5321.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-08 12:01:53,827 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-08 12:01:54,280 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-08 12:01:54,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161633.0, 'new_value': 176692.1}, {'field': 'total_amount', 'old_value': 161633.0, 'new_value': 176692.1}, {'field': 'order_count', 'old_value': 714, 'new_value': 758}]
2025-05-08 12:01:54,281 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-08 12:01:54,736 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-08 12:01:54,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5000.1, 'new_value': 5297.1}, {'field': 'total_amount', 'old_value': 5000.1, 'new_value': 5297.1}, {'field': 'order_count', 'old_value': 170, 'new_value': 201}]
2025-05-08 12:01:54,736 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-08 12:01:55,163 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-08 12:01:55,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51954.42, 'new_value': 59230.26}, {'field': 'offline_amount', 'old_value': 18728.59, 'new_value': 19537.18}, {'field': 'total_amount', 'old_value': 70683.01, 'new_value': 78767.44}, {'field': 'order_count', 'old_value': 4445, 'new_value': 5031}]
2025-05-08 12:01:55,163 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-08 12:01:55,576 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-08 12:01:55,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5788.97, 'new_value': 6149.47}, {'field': 'offline_amount', 'old_value': 46387.52, 'new_value': 48876.02}, {'field': 'total_amount', 'old_value': 52176.49, 'new_value': 55025.49}, {'field': 'order_count', 'old_value': 1416, 'new_value': 1510}]
2025-05-08 12:01:55,576 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-08 12:01:56,105 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-08 12:01:56,105 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3042.5, 'new_value': 3249.5}, {'field': 'offline_amount', 'old_value': 13081.7, 'new_value': 13959.9}, {'field': 'total_amount', 'old_value': 16124.2, 'new_value': 17209.4}, {'field': 'order_count', 'old_value': 160, 'new_value': 175}]
2025-05-08 12:01:56,106 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-08 12:01:56,680 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-08 12:01:56,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51986.6, 'new_value': 58540.88}, {'field': 'total_amount', 'old_value': 51986.6, 'new_value': 58540.88}, {'field': 'order_count', 'old_value': 2098, 'new_value': 2456}]
2025-05-08 12:01:56,681 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-08 12:01:57,170 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-08 12:01:57,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4066.0, 'new_value': 11618.0}, {'field': 'total_amount', 'old_value': 7330.0, 'new_value': 14882.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 24}]
2025-05-08 12:01:57,171 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-08 12:01:57,584 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-08 12:01:57,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75308.0, 'new_value': 79447.0}, {'field': 'total_amount', 'old_value': 75308.0, 'new_value': 79447.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 76}]
2025-05-08 12:01:57,585 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-08 12:01:58,013 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-08 12:01:58,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270171.44, 'new_value': 279175.34}, {'field': 'total_amount', 'old_value': 270171.44, 'new_value': 279175.34}, {'field': 'order_count', 'old_value': 5590, 'new_value': 5766}]
2025-05-08 12:01:58,013 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-08 12:01:58,508 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-08 12:01:58,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80899.18, 'new_value': 88239.13}, {'field': 'total_amount', 'old_value': 80899.18, 'new_value': 88239.13}, {'field': 'order_count', 'old_value': 3344, 'new_value': 3685}]
2025-05-08 12:01:58,509 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-08 12:01:59,013 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-08 12:01:59,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223915.0, 'new_value': 261807.0}, {'field': 'total_amount', 'old_value': 223915.0, 'new_value': 261807.0}, {'field': 'order_count', 'old_value': 420, 'new_value': 496}]
2025-05-08 12:01:59,013 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-08 12:01:59,465 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-08 12:01:59,465 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6612.75, 'new_value': 8107.48}, {'field': 'total_amount', 'old_value': 6612.75, 'new_value': 8107.48}, {'field': 'order_count', 'old_value': 242, 'new_value': 310}]
2025-05-08 12:01:59,465 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-08 12:01:59,826 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-08 12:01:59,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209401.9, 'new_value': 234561.0}, {'field': 'total_amount', 'old_value': 209401.9, 'new_value': 234561.0}, {'field': 'order_count', 'old_value': 1438, 'new_value': 1642}]
2025-05-08 12:01:59,826 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-08 12:02:00,266 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-08 12:02:00,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89125.0, 'new_value': 92468.0}, {'field': 'total_amount', 'old_value': 89125.0, 'new_value': 92468.0}, {'field': 'order_count', 'old_value': 249, 'new_value': 259}]
2025-05-08 12:02:00,267 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-08 12:02:00,736 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-08 12:02:00,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 462.77, 'new_value': 470.93}, {'field': 'offline_amount', 'old_value': 11936.56, 'new_value': 12086.53}, {'field': 'total_amount', 'old_value': 12399.33, 'new_value': 12557.46}, {'field': 'order_count', 'old_value': 432, 'new_value': 438}]
2025-05-08 12:02:00,736 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-08 12:02:01,190 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-08 12:02:01,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310974.0, 'new_value': 341836.0}, {'field': 'total_amount', 'old_value': 310974.0, 'new_value': 341836.0}, {'field': 'order_count', 'old_value': 1426, 'new_value': 1585}]
2025-05-08 12:02:01,190 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-08 12:02:01,650 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-08 12:02:01,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 388.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 388.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-08 12:02:01,651 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-08 12:02:02,107 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-08 12:02:02,107 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2217.39, 'new_value': 2542.69}, {'field': 'offline_amount', 'old_value': 127510.3, 'new_value': 137826.9}, {'field': 'total_amount', 'old_value': 129727.69, 'new_value': 140369.59}, {'field': 'order_count', 'old_value': 5851, 'new_value': 6278}]
2025-05-08 12:02:02,107 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-08 12:02:02,568 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-08 12:02:02,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51410.7, 'new_value': 59219.8}, {'field': 'total_amount', 'old_value': 51410.7, 'new_value': 59219.8}, {'field': 'order_count', 'old_value': 286, 'new_value': 327}]
2025-05-08 12:02:02,569 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-08 12:02:03,010 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-08 12:02:03,010 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19117.65, 'new_value': 20840.1}, {'field': 'offline_amount', 'old_value': 31234.37, 'new_value': 32890.6}, {'field': 'total_amount', 'old_value': 50352.02, 'new_value': 53730.7}, {'field': 'order_count', 'old_value': 2283, 'new_value': 2457}]
2025-05-08 12:02:03,010 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-08 12:02:03,462 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-08 12:02:03,463 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125468.31, 'new_value': 135016.45}, {'field': 'total_amount', 'old_value': 125468.31, 'new_value': 135016.45}, {'field': 'order_count', 'old_value': 5012, 'new_value': 5448}]
2025-05-08 12:02:03,463 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-08 12:02:03,946 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-08 12:02:03,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5429.55, 'new_value': 6855.28}, {'field': 'offline_amount', 'old_value': 78757.8, 'new_value': 82954.5}, {'field': 'total_amount', 'old_value': 84187.35, 'new_value': 89809.78}, {'field': 'order_count', 'old_value': 2505, 'new_value': 2702}]
2025-05-08 12:02:03,946 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-08 12:02:04,429 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-08 12:02:04,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139051.23, 'new_value': 147127.85}, {'field': 'total_amount', 'old_value': 139051.23, 'new_value': 147127.85}, {'field': 'order_count', 'old_value': 907, 'new_value': 980}]
2025-05-08 12:02:04,429 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-08 12:02:04,935 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-08 12:02:04,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46564.0, 'new_value': 48824.0}, {'field': 'total_amount', 'old_value': 61768.0, 'new_value': 64028.0}, {'field': 'order_count', 'old_value': 1266, 'new_value': 1322}]
2025-05-08 12:02:04,935 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-08 12:02:05,457 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-08 12:02:05,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52255.27, 'new_value': 56867.94}, {'field': 'total_amount', 'old_value': 52255.27, 'new_value': 56867.94}, {'field': 'order_count', 'old_value': 730, 'new_value': 793}]
2025-05-08 12:02:05,458 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-08 12:02:06,022 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-08 12:02:06,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2414.0, 'new_value': 2832.0}, {'field': 'total_amount', 'old_value': 2414.0, 'new_value': 2832.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-08 12:02:06,023 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-08 12:02:06,479 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-08 12:02:06,479 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8594.0, 'new_value': 9602.0}, {'field': 'total_amount', 'old_value': 8594.0, 'new_value': 9602.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 12:02:06,480 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-08 12:02:06,968 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-08 12:02:06,968 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19422.17, 'new_value': 22682.86}, {'field': 'offline_amount', 'old_value': 30261.91, 'new_value': 34026.7}, {'field': 'total_amount', 'old_value': 49684.08, 'new_value': 56709.56}, {'field': 'order_count', 'old_value': 1991, 'new_value': 2304}]
2025-05-08 12:02:06,969 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-08 12:02:07,416 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-08 12:02:07,416 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77117.25, 'new_value': 80864.15}, {'field': 'total_amount', 'old_value': 96290.68, 'new_value': 100037.58}, {'field': 'order_count', 'old_value': 2026, 'new_value': 2087}]
2025-05-08 12:02:07,416 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-08 12:02:07,953 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-08 12:02:07,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21434.0, 'new_value': 23059.0}, {'field': 'total_amount', 'old_value': 21434.0, 'new_value': 23059.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 46}]
2025-05-08 12:02:07,954 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-08 12:02:08,373 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-08 12:02:08,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212571.0, 'new_value': 229867.0}, {'field': 'total_amount', 'old_value': 212571.0, 'new_value': 229867.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 28}]
2025-05-08 12:02:08,373 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-08 12:02:08,888 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-08 12:02:08,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28884.06, 'new_value': 30403.62}, {'field': 'total_amount', 'old_value': 28884.06, 'new_value': 30403.62}, {'field': 'order_count', 'old_value': 1626, 'new_value': 1733}]
2025-05-08 12:02:08,888 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-08 12:02:09,417 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-08 12:02:09,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11830.0, 'new_value': 12430.0}, {'field': 'total_amount', 'old_value': 11830.0, 'new_value': 12430.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-08 12:02:09,417 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-08 12:02:09,913 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-08 12:02:09,913 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15952.0, 'new_value': 17551.6}, {'field': 'offline_amount', 'old_value': 15360.0, 'new_value': 16662.12}, {'field': 'total_amount', 'old_value': 31312.0, 'new_value': 34213.72}, {'field': 'order_count', 'old_value': 579, 'new_value': 650}]
2025-05-08 12:02:09,913 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-08 12:02:10,397 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-08 12:02:10,397 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4246.0, 'new_value': 4680.0}, {'field': 'offline_amount', 'old_value': 67406.0, 'new_value': 77343.0}, {'field': 'total_amount', 'old_value': 71652.0, 'new_value': 82023.0}, {'field': 'order_count', 'old_value': 346, 'new_value': 399}]
2025-05-08 12:02:10,398 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-08 12:02:10,910 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-08 12:02:10,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9500.0, 'new_value': 27900.0}, {'field': 'total_amount', 'old_value': 9500.0, 'new_value': 27900.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-08 12:02:10,910 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-08 12:02:11,415 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-08 12:02:11,415 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 12:02:11,415 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-08 12:02:11,903 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-08 12:02:11,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11136.2, 'new_value': 11482.2}, {'field': 'total_amount', 'old_value': 11136.2, 'new_value': 11482.2}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-05-08 12:02:11,903 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-08 12:02:12,416 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-08 12:02:12,417 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 999.0, 'new_value': 1698.0}, {'field': 'offline_amount', 'old_value': 500.0, 'new_value': 899.0}, {'field': 'total_amount', 'old_value': 1499.0, 'new_value': 2597.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 12}]
2025-05-08 12:02:12,417 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-08 12:02:12,871 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-08 12:02:12,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78862.0, 'new_value': 91972.0}, {'field': 'total_amount', 'old_value': 92637.0, 'new_value': 105747.0}, {'field': 'order_count', 'old_value': 1806, 'new_value': 2136}]
2025-05-08 12:02:12,872 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-08 12:02:13,317 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-08 12:02:13,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30335.71, 'new_value': 32495.22}, {'field': 'offline_amount', 'old_value': 60853.63, 'new_value': 64464.39}, {'field': 'total_amount', 'old_value': 91189.34, 'new_value': 96959.61}, {'field': 'order_count', 'old_value': 1392, 'new_value': 1526}]
2025-05-08 12:02:13,318 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-08 12:02:13,798 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-08 12:02:13,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221997.85, 'new_value': 238485.78}, {'field': 'total_amount', 'old_value': 221997.85, 'new_value': 238485.78}, {'field': 'order_count', 'old_value': 2193, 'new_value': 2462}]
2025-05-08 12:02:13,798 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-08 12:02:14,219 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-08 12:02:14,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80174.0, 'new_value': 82555.0}, {'field': 'total_amount', 'old_value': 80174.0, 'new_value': 82555.0}, {'field': 'order_count', 'old_value': 1285, 'new_value': 1321}]
2025-05-08 12:02:14,219 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-08 12:02:14,717 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-08 12:02:14,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65592.46, 'new_value': 68832.27}, {'field': 'total_amount', 'old_value': 65592.46, 'new_value': 68832.27}, {'field': 'order_count', 'old_value': 2671, 'new_value': 2830}]
2025-05-08 12:02:14,717 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-08 12:02:15,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-08 12:02:15,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103190.0, 'new_value': 125648.0}, {'field': 'total_amount', 'old_value': 103190.0, 'new_value': 125648.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 122}]
2025-05-08 12:02:15,166 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-08 12:02:15,581 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-08 12:02:15,582 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82642.82, 'new_value': 87420.15}, {'field': 'offline_amount', 'old_value': 52687.54, 'new_value': 55922.54}, {'field': 'total_amount', 'old_value': 135330.36, 'new_value': 143342.69}, {'field': 'order_count', 'old_value': 1177, 'new_value': 1282}]
2025-05-08 12:02:15,582 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-08 12:02:16,071 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-08 12:02:16,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73072.29, 'new_value': 78471.49}, {'field': 'total_amount', 'old_value': 73072.29, 'new_value': 78471.49}, {'field': 'order_count', 'old_value': 519, 'new_value': 561}]
2025-05-08 12:02:16,071 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-08 12:02:16,568 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-08 12:02:16,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 821.8, 'new_value': 921.8}, {'field': 'offline_amount', 'old_value': 36938.4, 'new_value': 38542.3}, {'field': 'total_amount', 'old_value': 37760.2, 'new_value': 39464.1}, {'field': 'order_count', 'old_value': 225, 'new_value': 235}]
2025-05-08 12:02:16,568 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-08 12:02:17,128 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-08 12:02:17,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35194.19, 'new_value': 38648.52}, {'field': 'total_amount', 'old_value': 54233.79, 'new_value': 57688.12}, {'field': 'order_count', 'old_value': 1638, 'new_value': 1721}]
2025-05-08 12:02:17,128 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-08 12:02:17,587 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-08 12:02:17,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21668.0, 'new_value': 22268.0}, {'field': 'total_amount', 'old_value': 21668.0, 'new_value': 22268.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-08 12:02:17,588 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-08 12:02:18,055 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-08 12:02:18,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55214.5, 'new_value': 59825.5}, {'field': 'offline_amount', 'old_value': 4536.0, 'new_value': 4903.0}, {'field': 'total_amount', 'old_value': 59750.5, 'new_value': 64728.5}, {'field': 'order_count', 'old_value': 2282, 'new_value': 2591}]
2025-05-08 12:02:18,055 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-08 12:02:18,568 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-08 12:02:18,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20603.0, 'new_value': 24130.0}, {'field': 'offline_amount', 'old_value': 269397.0, 'new_value': 311770.0}, {'field': 'total_amount', 'old_value': 290000.0, 'new_value': 335900.0}, {'field': 'order_count', 'old_value': 6209, 'new_value': 7402}]
2025-05-08 12:02:18,568 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-08 12:02:19,016 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-08 12:02:19,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121617.92, 'new_value': 130309.65}, {'field': 'total_amount', 'old_value': 121617.92, 'new_value': 130309.65}, {'field': 'order_count', 'old_value': 408, 'new_value': 444}]
2025-05-08 12:02:19,017 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-08 12:02:19,505 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-08 12:02:19,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43232.0, 'new_value': 50338.0}, {'field': 'total_amount', 'old_value': 55042.0, 'new_value': 62148.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 76}]
2025-05-08 12:02:19,505 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-08 12:02:20,063 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-08 12:02:20,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11914.91, 'new_value': 12914.91}, {'field': 'offline_amount', 'old_value': 11878.39, 'new_value': 13984.49}, {'field': 'total_amount', 'old_value': 23793.3, 'new_value': 26899.4}, {'field': 'order_count', 'old_value': 999, 'new_value': 1149}]
2025-05-08 12:02:20,063 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-08 12:02:20,515 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-08 12:02:20,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5234.14, 'new_value': 6097.14}, {'field': 'offline_amount', 'old_value': 5994.5, 'new_value': 6390.5}, {'field': 'total_amount', 'old_value': 11228.64, 'new_value': 12487.64}, {'field': 'order_count', 'old_value': 551, 'new_value': 606}]
2025-05-08 12:02:20,515 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-08 12:02:20,925 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-08 12:02:20,925 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89302.0, 'new_value': 100260.0}, {'field': 'total_amount', 'old_value': 89302.0, 'new_value': 100260.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 166}]
2025-05-08 12:02:20,925 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-08 12:02:21,407 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-08 12:02:21,407 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21189.77, 'new_value': 26690.89}, {'field': 'offline_amount', 'old_value': 61770.9, 'new_value': 66856.35}, {'field': 'total_amount', 'old_value': 82960.67, 'new_value': 93547.24}, {'field': 'order_count', 'old_value': 2482, 'new_value': 2816}]
2025-05-08 12:02:21,407 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-08 12:02:21,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-08 12:02:21,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39465.22, 'new_value': 46028.78}, {'field': 'offline_amount', 'old_value': 19030.99, 'new_value': 20315.31}, {'field': 'total_amount', 'old_value': 58496.21, 'new_value': 66344.09}, {'field': 'order_count', 'old_value': 3174, 'new_value': 3621}]
2025-05-08 12:02:21,931 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-08 12:02:22,370 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-08 12:02:22,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72594.63, 'new_value': 76866.88}, {'field': 'offline_amount', 'old_value': 183000.0, 'new_value': 186000.0}, {'field': 'total_amount', 'old_value': 255594.63, 'new_value': 262866.88}, {'field': 'order_count', 'old_value': 454, 'new_value': 479}]
2025-05-08 12:02:22,370 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-08 12:02:22,844 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-08 12:02:22,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56173.15, 'new_value': 61393.56}, {'field': 'total_amount', 'old_value': 56173.15, 'new_value': 61393.56}, {'field': 'order_count', 'old_value': 2747, 'new_value': 3059}]
2025-05-08 12:02:22,845 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-08 12:02:23,300 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-08 12:02:23,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49334.1, 'new_value': 51633.1}, {'field': 'total_amount', 'old_value': 49334.1, 'new_value': 51633.1}, {'field': 'order_count', 'old_value': 227, 'new_value': 239}]
2025-05-08 12:02:23,301 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-08 12:02:23,799 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-08 12:02:23,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64782.7, 'new_value': 67575.7}, {'field': 'total_amount', 'old_value': 64782.7, 'new_value': 67575.7}, {'field': 'order_count', 'old_value': 1770, 'new_value': 1850}]
2025-05-08 12:02:23,799 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-08 12:02:24,305 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-08 12:02:24,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6071.0, 'new_value': 6472.0}, {'field': 'total_amount', 'old_value': 7652.0, 'new_value': 8053.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 35}]
2025-05-08 12:02:24,305 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-08 12:02:24,776 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-08 12:02:24,776 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35270.2, 'new_value': 38582.8}, {'field': 'total_amount', 'old_value': 35270.2, 'new_value': 38582.8}, {'field': 'order_count', 'old_value': 155, 'new_value': 170}]
2025-05-08 12:02:24,776 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-08 12:02:25,218 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-08 12:02:25,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10783.05, 'new_value': 12890.96}, {'field': 'offline_amount', 'old_value': 8934.24, 'new_value': 9350.92}, {'field': 'total_amount', 'old_value': 19717.29, 'new_value': 22241.88}, {'field': 'order_count', 'old_value': 839, 'new_value': 947}]
2025-05-08 12:02:25,219 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-08 12:02:25,605 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-08 12:02:25,605 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4807.21, 'new_value': 5251.35}, {'field': 'offline_amount', 'old_value': 13354.7, 'new_value': 13778.2}, {'field': 'total_amount', 'old_value': 18161.91, 'new_value': 19029.55}, {'field': 'order_count', 'old_value': 718, 'new_value': 759}]
2025-05-08 12:02:25,605 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-08 12:02:26,117 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-08 12:02:26,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27738.0, 'new_value': 32345.0}, {'field': 'total_amount', 'old_value': 28901.76, 'new_value': 33508.76}, {'field': 'order_count', 'old_value': 131, 'new_value': 152}]
2025-05-08 12:02:26,117 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-08 12:02:26,555 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-08 12:02:26,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198950.0, 'new_value': 230270.0}, {'field': 'total_amount', 'old_value': 198950.0, 'new_value': 230270.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 141}]
2025-05-08 12:02:26,556 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-08 12:02:27,035 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-08 12:02:27,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262506.01, 'new_value': 277199.93}, {'field': 'total_amount', 'old_value': 262506.01, 'new_value': 277199.93}, {'field': 'order_count', 'old_value': 1854, 'new_value': 2012}]
2025-05-08 12:02:27,035 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-08 12:02:27,501 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-08 12:02:27,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161897.0, 'new_value': 170616.0}, {'field': 'total_amount', 'old_value': 161897.0, 'new_value': 170616.0}, {'field': 'order_count', 'old_value': 1176, 'new_value': 1268}]
2025-05-08 12:02:27,501 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-08 12:02:27,867 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-08 12:02:27,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1370.0, 'new_value': 1979.0}, {'field': 'total_amount', 'old_value': 5196.0, 'new_value': 5805.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 53}]
2025-05-08 12:02:27,867 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-08 12:02:28,348 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-08 12:02:28,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14106.9, 'new_value': 17043.46}, {'field': 'offline_amount', 'old_value': 9056.85, 'new_value': 11507.85}, {'field': 'total_amount', 'old_value': 23163.75, 'new_value': 28551.31}, {'field': 'order_count', 'old_value': 1284, 'new_value': 1550}]
2025-05-08 12:02:28,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-08 12:02:28,794 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-08 12:02:28,795 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1117.0, 'new_value': 1252.0}, {'field': 'offline_amount', 'old_value': 12342.8, 'new_value': 12745.8}, {'field': 'total_amount', 'old_value': 13459.8, 'new_value': 13997.8}, {'field': 'order_count', 'old_value': 489, 'new_value': 507}]
2025-05-08 12:02:28,795 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-08 12:02:29,227 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-08 12:02:29,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19622.65, 'new_value': 25365.66}, {'field': 'offline_amount', 'old_value': 24220.24, 'new_value': 29299.86}, {'field': 'total_amount', 'old_value': 43842.89, 'new_value': 54665.52}, {'field': 'order_count', 'old_value': 1105, 'new_value': 1383}]
2025-05-08 12:02:29,227 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-08 12:02:29,713 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-08 12:02:29,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36994.0, 'new_value': 39993.0}, {'field': 'total_amount', 'old_value': 36994.0, 'new_value': 39993.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 12:02:29,713 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-08 12:02:30,117 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-08 12:02:30,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-08 12:02:30,117 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-08 12:02:30,574 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-08 12:02:30,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266228.0, 'new_value': 293349.0}, {'field': 'total_amount', 'old_value': 266228.0, 'new_value': 293349.0}, {'field': 'order_count', 'old_value': 323, 'new_value': 359}]
2025-05-08 12:02:30,574 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-08 12:02:31,116 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-08 12:02:31,116 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5398.3, 'new_value': 5950.3}, {'field': 'offline_amount', 'old_value': 81213.0, 'new_value': 84141.0}, {'field': 'total_amount', 'old_value': 86611.3, 'new_value': 90091.3}, {'field': 'order_count', 'old_value': 168, 'new_value': 178}]
2025-05-08 12:02:31,116 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-08 12:02:31,557 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-08 12:02:31,557 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12464.05, 'new_value': 14120.3}, {'field': 'offline_amount', 'old_value': 23079.0, 'new_value': 26795.0}, {'field': 'total_amount', 'old_value': 35543.05, 'new_value': 40915.3}, {'field': 'order_count', 'old_value': 453, 'new_value': 508}]
2025-05-08 12:02:31,557 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-08 12:02:32,005 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-08 12:02:32,005 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38250.0, 'new_value': 41805.0}, {'field': 'offline_amount', 'old_value': 27363.0, 'new_value': 29371.0}, {'field': 'total_amount', 'old_value': 65613.0, 'new_value': 71176.0}, {'field': 'order_count', 'old_value': 781, 'new_value': 871}]
2025-05-08 12:02:32,005 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-08 12:02:32,506 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-08 12:02:32,507 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3534.7, 'new_value': 3722.4}, {'field': 'offline_amount', 'old_value': 5007.12, 'new_value': 5773.95}, {'field': 'total_amount', 'old_value': 8541.82, 'new_value': 9496.35}, {'field': 'order_count', 'old_value': 99, 'new_value': 112}]
2025-05-08 12:02:32,507 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-08 12:02:32,982 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-08 12:02:32,982 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3822.82, 'new_value': 4094.42}, {'field': 'offline_amount', 'old_value': 40180.0, 'new_value': 44677.0}, {'field': 'total_amount', 'old_value': 44002.82, 'new_value': 48771.42}, {'field': 'order_count', 'old_value': 17, 'new_value': 20}]
2025-05-08 12:02:32,982 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-08 12:02:33,469 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-08 12:02:33,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16954.42, 'new_value': 21726.19}, {'field': 'total_amount', 'old_value': 16954.42, 'new_value': 21726.19}, {'field': 'order_count', 'old_value': 84, 'new_value': 114}]
2025-05-08 12:02:33,469 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-08 12:02:33,887 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-08 12:02:33,887 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5368.02, 'new_value': 6122.82}, {'field': 'offline_amount', 'old_value': 6905.0, 'new_value': 9785.0}, {'field': 'total_amount', 'old_value': 12273.02, 'new_value': 15907.82}, {'field': 'order_count', 'old_value': 55, 'new_value': 65}]
2025-05-08 12:02:33,887 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-08 12:02:34,452 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-08 12:02:34,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19529.0, 'new_value': 21006.0}, {'field': 'total_amount', 'old_value': 19794.0, 'new_value': 21271.0}]
2025-05-08 12:02:34,452 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-08 12:02:34,906 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-08 12:02:34,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64050.3, 'new_value': 69924.5}, {'field': 'total_amount', 'old_value': 64050.3, 'new_value': 69924.5}, {'field': 'order_count', 'old_value': 328, 'new_value': 363}]
2025-05-08 12:02:34,906 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-08 12:02:35,297 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-08 12:02:35,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1993.0, 'new_value': 2291.0}, {'field': 'offline_amount', 'old_value': 11271.57, 'new_value': 13260.57}, {'field': 'total_amount', 'old_value': 13264.57, 'new_value': 15551.57}, {'field': 'order_count', 'old_value': 122, 'new_value': 145}]
2025-05-08 12:02:35,297 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-08 12:02:35,850 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-08 12:02:35,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44700.68, 'new_value': 49990.38}, {'field': 'total_amount', 'old_value': 44700.68, 'new_value': 49990.38}, {'field': 'order_count', 'old_value': 153, 'new_value': 176}]
2025-05-08 12:02:35,850 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-08 12:02:36,355 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-08 12:02:36,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3435.0, 'new_value': 5308.0}, {'field': 'total_amount', 'old_value': 3435.0, 'new_value': 5308.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-08 12:02:36,356 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-08 12:02:36,796 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-08 12:02:36,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 431441.0, 'new_value': 451955.0}, {'field': 'total_amount', 'old_value': 431441.0, 'new_value': 451955.0}, {'field': 'order_count', 'old_value': 1742, 'new_value': 1846}]
2025-05-08 12:02:36,796 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-08 12:02:37,207 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-08 12:02:37,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5365476.0, 'new_value': 5684603.0}, {'field': 'total_amount', 'old_value': 5365476.0, 'new_value': 5684603.0}, {'field': 'order_count', 'old_value': 15322, 'new_value': 16320}]
2025-05-08 12:02:37,208 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-08 12:02:37,821 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-08 12:02:37,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1180990.95, 'new_value': 1262497.76}, {'field': 'total_amount', 'old_value': 1180990.95, 'new_value': 1262497.76}, {'field': 'order_count', 'old_value': 2008, 'new_value': 2174}]
2025-05-08 12:02:37,821 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-08 12:02:38,292 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-08 12:02:38,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37923.92, 'new_value': 44786.03}, {'field': 'total_amount', 'old_value': 37923.92, 'new_value': 44786.03}, {'field': 'order_count', 'old_value': 2556, 'new_value': 3048}]
2025-05-08 12:02:38,293 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-08 12:02:38,824 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-08 12:02:38,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69425.0, 'new_value': 84198.0}, {'field': 'total_amount', 'old_value': 69425.0, 'new_value': 84198.0}, {'field': 'order_count', 'old_value': 1228, 'new_value': 1553}]
2025-05-08 12:02:38,824 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-08 12:02:39,202 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-08 12:02:39,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72992.79, 'new_value': 84803.5}, {'field': 'offline_amount', 'old_value': 70775.9, 'new_value': 75321.79}, {'field': 'total_amount', 'old_value': 143768.69, 'new_value': 160125.29}, {'field': 'order_count', 'old_value': 5274, 'new_value': 5995}]
2025-05-08 12:02:39,203 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-08 12:02:39,630 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-08 12:02:39,630 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12853.95, 'new_value': 14514.78}, {'field': 'offline_amount', 'old_value': 22551.39, 'new_value': 24525.39}, {'field': 'total_amount', 'old_value': 35405.34, 'new_value': 39040.17}, {'field': 'order_count', 'old_value': 798, 'new_value': 879}]
2025-05-08 12:02:39,630 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-08 12:02:40,135 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-08 12:02:40,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88935.0, 'new_value': 105830.0}, {'field': 'total_amount', 'old_value': 88935.0, 'new_value': 105830.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 114}]
2025-05-08 12:02:40,136 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-08 12:02:40,632 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-08 12:02:40,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4048.0, 'new_value': 17520.0}, {'field': 'total_amount', 'old_value': 4048.0, 'new_value': 17520.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-05-08 12:02:40,632 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-08 12:02:41,031 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-08 12:02:41,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36479.6, 'new_value': 49504.96}, {'field': 'total_amount', 'old_value': 85359.3, 'new_value': 98384.66}, {'field': 'order_count', 'old_value': 78, 'new_value': 97}]
2025-05-08 12:02:41,031 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-08 12:02:41,486 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-08 12:02:41,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12878.6, 'new_value': 15151.6}, {'field': 'total_amount', 'old_value': 12878.6, 'new_value': 15151.6}, {'field': 'order_count', 'old_value': 72, 'new_value': 85}]
2025-05-08 12:02:41,486 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-08 12:02:41,982 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-08 12:02:41,982 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46321.0, 'new_value': 52009.0}, {'field': 'offline_amount', 'old_value': 31453.0, 'new_value': 32759.0}, {'field': 'total_amount', 'old_value': 77774.0, 'new_value': 84768.0}, {'field': 'order_count', 'old_value': 263, 'new_value': 292}]
2025-05-08 12:02:41,982 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-08 12:02:42,494 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-08 12:02:42,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26342.0, 'new_value': 32865.0}, {'field': 'total_amount', 'old_value': 26342.0, 'new_value': 32865.0}, {'field': 'order_count', 'old_value': 1873, 'new_value': 2390}]
2025-05-08 12:02:42,494 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-08 12:02:43,050 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-08 12:02:43,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17112.0, 'new_value': 18592.0}, {'field': 'total_amount', 'old_value': 17112.0, 'new_value': 18592.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-08 12:02:43,051 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-08 12:02:43,515 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-08 12:02:43,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19.8, 'new_value': 39.6}, {'field': 'offline_amount', 'old_value': 32427.2, 'new_value': 33403.2}, {'field': 'total_amount', 'old_value': 32447.0, 'new_value': 33442.8}, {'field': 'order_count', 'old_value': 533, 'new_value': 541}]
2025-05-08 12:02:43,516 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-08 12:02:44,070 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-08 12:02:44,070 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4579.3, 'new_value': 5173.3}, {'field': 'offline_amount', 'old_value': 17294.4, 'new_value': 19353.9}, {'field': 'total_amount', 'old_value': 21873.7, 'new_value': 24527.2}, {'field': 'order_count', 'old_value': 799, 'new_value': 897}]
2025-05-08 12:02:44,071 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-08 12:02:44,539 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-08 12:02:44,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1412352.39, 'new_value': 1426528.39}, {'field': 'total_amount', 'old_value': 1412352.39, 'new_value': 1426528.39}, {'field': 'order_count', 'old_value': 27447, 'new_value': 30704}]
2025-05-08 12:02:44,540 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-08 12:02:44,976 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-08 12:02:44,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7188.32, 'new_value': 8492.32}, {'field': 'total_amount', 'old_value': 7188.32, 'new_value': 8492.32}, {'field': 'order_count', 'old_value': 28, 'new_value': 34}]
2025-05-08 12:02:44,976 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-08 12:02:45,641 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-08 12:02:45,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179154.91, 'new_value': 191741.31}, {'field': 'total_amount', 'old_value': 179154.91, 'new_value': 191741.31}, {'field': 'order_count', 'old_value': 953, 'new_value': 1078}]
2025-05-08 12:02:45,642 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-08 12:02:46,137 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-08 12:02:46,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37210.02, 'new_value': 47455.54}, {'field': 'total_amount', 'old_value': 37210.02, 'new_value': 47455.54}, {'field': 'order_count', 'old_value': 832, 'new_value': 992}]
2025-05-08 12:02:46,137 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-08 12:02:46,610 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-08 12:02:46,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69961.0, 'new_value': 84682.0}, {'field': 'total_amount', 'old_value': 69961.0, 'new_value': 84682.0}, {'field': 'order_count', 'old_value': 1566, 'new_value': 1867}]
2025-05-08 12:02:46,611 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-08 12:02:47,056 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-08 12:02:47,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26354.71, 'new_value': 28372.67}, {'field': 'total_amount', 'old_value': 26354.71, 'new_value': 28372.67}, {'field': 'order_count', 'old_value': 356, 'new_value': 390}]
2025-05-08 12:02:47,057 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-08 12:02:47,499 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-08 12:02:47,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88222.0, 'new_value': 94302.0}, {'field': 'total_amount', 'old_value': 88222.0, 'new_value': 94302.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 24}]
2025-05-08 12:02:47,499 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-08 12:02:48,008 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-08 12:02:48,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39469.71, 'new_value': 44093.15}, {'field': 'total_amount', 'old_value': 39469.71, 'new_value': 44093.15}, {'field': 'order_count', 'old_value': 922, 'new_value': 1056}]
2025-05-08 12:02:48,008 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-08 12:02:48,473 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-08 12:02:48,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7237.0, 'new_value': 8152.0}, {'field': 'total_amount', 'old_value': 7237.0, 'new_value': 8152.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 32}]
2025-05-08 12:02:48,473 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-08 12:02:48,901 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-08 12:02:48,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26767.0, 'new_value': 29770.0}, {'field': 'total_amount', 'old_value': 26767.0, 'new_value': 29770.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 143}]
2025-05-08 12:02:48,901 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-08 12:02:49,330 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-08 12:02:49,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12022.55, 'new_value': 16381.12}, {'field': 'total_amount', 'old_value': 21763.45, 'new_value': 26122.02}, {'field': 'order_count', 'old_value': 1352, 'new_value': 1647}]
2025-05-08 12:02:49,331 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-08 12:02:49,708 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-08 12:02:49,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16107.33, 'new_value': 23690.61}, {'field': 'total_amount', 'old_value': 29247.51, 'new_value': 36830.79}, {'field': 'order_count', 'old_value': 1817, 'new_value': 2314}]
2025-05-08 12:02:49,708 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-08 12:02:50,096 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-08 12:02:50,096 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316816.89, 'new_value': 348696.22}, {'field': 'total_amount', 'old_value': 316816.89, 'new_value': 348696.22}, {'field': 'order_count', 'old_value': 966, 'new_value': 1073}]
2025-05-08 12:02:50,097 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-08 12:02:50,543 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-08 12:02:50,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31303.0, 'new_value': 39834.0}, {'field': 'total_amount', 'old_value': 31303.0, 'new_value': 39834.0}, {'field': 'order_count', 'old_value': 1094, 'new_value': 1409}]
2025-05-08 12:02:50,544 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-08 12:02:50,995 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-08 12:02:50,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200968.56, 'new_value': 216364.27}, {'field': 'total_amount', 'old_value': 200968.56, 'new_value': 216364.27}, {'field': 'order_count', 'old_value': 875, 'new_value': 974}]
2025-05-08 12:02:50,995 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-08 12:02:51,445 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-08 12:02:51,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 309792.71, 'new_value': 336947.31}, {'field': 'total_amount', 'old_value': 309792.71, 'new_value': 336947.31}, {'field': 'order_count', 'old_value': 1090, 'new_value': 1189}]
2025-05-08 12:02:51,445 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-08 12:02:51,915 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-08 12:02:51,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215043.29, 'new_value': 232140.43}, {'field': 'total_amount', 'old_value': 215043.29, 'new_value': 232140.43}, {'field': 'order_count', 'old_value': 699, 'new_value': 755}]
2025-05-08 12:02:51,915 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-08 12:02:52,584 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-08 12:02:52,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8171.0, 'new_value': 9223.82}, {'field': 'total_amount', 'old_value': 8344.1, 'new_value': 9396.92}, {'field': 'order_count', 'old_value': 45, 'new_value': 59}]
2025-05-08 12:02:52,585 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-08 12:02:53,055 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-08 12:02:53,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95857.55, 'new_value': 103568.2}, {'field': 'total_amount', 'old_value': 95857.55, 'new_value': 103568.2}, {'field': 'order_count', 'old_value': 270, 'new_value': 287}]
2025-05-08 12:02:53,056 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-08 12:02:53,483 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-08 12:02:53,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18155.0, 'new_value': 19237.0}, {'field': 'total_amount', 'old_value': 21545.0, 'new_value': 22627.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-08 12:02:53,483 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-08 12:02:53,929 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-08 12:02:53,929 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22023.0, 'new_value': 28836.0}, {'field': 'offline_amount', 'old_value': 11513.0, 'new_value': 16805.0}, {'field': 'total_amount', 'old_value': 33536.0, 'new_value': 45641.0}, {'field': 'order_count', 'old_value': 1404, 'new_value': 1968}]
2025-05-08 12:02:53,929 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-08 12:02:54,350 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-08 12:02:54,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24729.0, 'new_value': 29729.0}, {'field': 'total_amount', 'old_value': 24729.0, 'new_value': 29729.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-05-08 12:02:54,351 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-08 12:02:54,873 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-08 12:02:54,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38036.0, 'new_value': 44195.0}, {'field': 'total_amount', 'old_value': 38036.0, 'new_value': 44195.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 188}]
2025-05-08 12:02:54,873 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-08 12:02:55,336 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-08 12:02:55,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27185.0, 'new_value': 36785.0}, {'field': 'total_amount', 'old_value': 27185.0, 'new_value': 36785.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 86}]
2025-05-08 12:02:55,337 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-08 12:02:55,826 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-08 12:02:55,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8537.0, 'new_value': 11403.0}, {'field': 'total_amount', 'old_value': 8537.0, 'new_value': 11403.0}, {'field': 'order_count', 'old_value': 166, 'new_value': 216}]
2025-05-08 12:02:55,826 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-08 12:02:56,207 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-08 12:02:56,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26121.0, 'new_value': 37597.0}, {'field': 'total_amount', 'old_value': 26121.0, 'new_value': 37597.0}, {'field': 'order_count', 'old_value': 2603, 'new_value': 3891}]
2025-05-08 12:02:56,207 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-08 12:02:56,649 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-08 12:02:56,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45979.0, 'new_value': 50552.0}, {'field': 'total_amount', 'old_value': 45979.0, 'new_value': 50552.0}, {'field': 'order_count', 'old_value': 328, 'new_value': 375}]
2025-05-08 12:02:56,649 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-08 12:02:57,304 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-08 12:02:57,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 580.0, 'new_value': 1080.0}, {'field': 'total_amount', 'old_value': 580.0, 'new_value': 1080.0}, {'field': 'order_count', 'old_value': 580, 'new_value': 581}]
2025-05-08 12:02:57,304 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-08 12:02:57,728 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-08 12:02:57,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2190.6, 'new_value': 3728.6}, {'field': 'total_amount', 'old_value': 2190.6, 'new_value': 3728.6}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-08 12:02:57,728 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-08 12:02:58,108 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-08 12:02:58,108 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7220.0, 'new_value': 8150.0}, {'field': 'offline_amount', 'old_value': 16047.0, 'new_value': 16204.0}, {'field': 'total_amount', 'old_value': 23267.0, 'new_value': 24354.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 45}]
2025-05-08 12:02:58,108 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-08 12:02:58,572 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-08 12:02:58,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15963.95, 'new_value': 18252.95}, {'field': 'total_amount', 'old_value': 15963.95, 'new_value': 18252.95}, {'field': 'order_count', 'old_value': 236, 'new_value': 267}]
2025-05-08 12:02:58,572 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-08 12:02:59,113 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-08 12:02:59,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37400.41, 'new_value': 41317.29}, {'field': 'offline_amount', 'old_value': 160904.7, 'new_value': 177214.66}, {'field': 'total_amount', 'old_value': 198305.11, 'new_value': 218531.95}, {'field': 'order_count', 'old_value': 475, 'new_value': 526}]
2025-05-08 12:02:59,114 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-08 12:02:59,555 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-08 12:02:59,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16953.47, 'new_value': 20841.87}, {'field': 'offline_amount', 'old_value': 163149.24, 'new_value': 196101.43}, {'field': 'total_amount', 'old_value': 178228.38, 'new_value': 215068.97}, {'field': 'order_count', 'old_value': 870, 'new_value': 1074}]
2025-05-08 12:02:59,556 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-08 12:03:00,104 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-08 12:03:00,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21390.0, 'new_value': 25848.0}, {'field': 'total_amount', 'old_value': 21390.0, 'new_value': 25848.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 84}]
2025-05-08 12:03:00,104 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-08 12:03:00,533 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-08 12:03:00,533 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 207.0, 'new_value': 652.9}, {'field': 'total_amount', 'old_value': 207.0, 'new_value': 652.9}, {'field': 'order_count', 'old_value': 3, 'new_value': 8}]
2025-05-08 12:03:00,533 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-08 12:03:01,015 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-08 12:03:01,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4781148.72, 'new_value': 5446296.58}, {'field': 'total_amount', 'old_value': 4781148.72, 'new_value': 5446296.58}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 12:03:01,015 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-08 12:03:01,411 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-08 12:03:01,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46229.4, 'new_value': 52529.38}, {'field': 'total_amount', 'old_value': 46229.4, 'new_value': 52529.38}, {'field': 'order_count', 'old_value': 4697, 'new_value': 5368}]
2025-05-08 12:03:01,412 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-08 12:03:01,884 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-08 12:03:01,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6608.0, 'new_value': 7454.0}, {'field': 'total_amount', 'old_value': 6608.0, 'new_value': 7454.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-05-08 12:03:01,884 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-08 12:03:02,372 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-08 12:03:02,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3662.71, 'new_value': 4349.67}, {'field': 'total_amount', 'old_value': 3662.71, 'new_value': 4349.67}, {'field': 'order_count', 'old_value': 38, 'new_value': 76}]
2025-05-08 12:03:02,372 - INFO - 日期 2025-05 处理完成 - 更新: 325 条，插入: 0 条，错误: 0 条
2025-05-08 12:03:02,372 - INFO - 数据同步完成！更新: 326 条，插入: 0 条，错误: 0 条
2025-05-08 12:03:02,374 - INFO - =================同步完成====================
2025-05-08 15:00:01,902 - INFO - =================使用默认全量同步=============
2025-05-08 15:00:03,133 - INFO - MySQL查询成功，共获取 3287 条记录
2025-05-08 15:00:03,134 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-08 15:00:03,162 - INFO - 开始处理日期: 2025-01
2025-05-08 15:00:03,165 - INFO - Request Parameters - Page 1:
2025-05-08 15:00:03,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:03,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:04,141 - INFO - Response - Page 1:
2025-05-08 15:00:04,341 - INFO - 第 1 页获取到 100 条记录
2025-05-08 15:00:04,341 - INFO - Request Parameters - Page 2:
2025-05-08 15:00:04,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:04,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:05,286 - INFO - Response - Page 2:
2025-05-08 15:00:05,486 - INFO - 第 2 页获取到 100 条记录
2025-05-08 15:00:05,486 - INFO - Request Parameters - Page 3:
2025-05-08 15:00:05,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:05,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:06,076 - INFO - Response - Page 3:
2025-05-08 15:00:06,276 - INFO - 第 3 页获取到 100 条记录
2025-05-08 15:00:06,276 - INFO - Request Parameters - Page 4:
2025-05-08 15:00:06,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:06,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:06,779 - INFO - Response - Page 4:
2025-05-08 15:00:06,979 - INFO - 第 4 页获取到 100 条记录
2025-05-08 15:00:06,979 - INFO - Request Parameters - Page 5:
2025-05-08 15:00:06,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:06,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:07,464 - INFO - Response - Page 5:
2025-05-08 15:00:07,666 - INFO - 第 5 页获取到 100 条记录
2025-05-08 15:00:07,666 - INFO - Request Parameters - Page 6:
2025-05-08 15:00:07,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:07,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:08,194 - INFO - Response - Page 6:
2025-05-08 15:00:08,394 - INFO - 第 6 页获取到 100 条记录
2025-05-08 15:00:08,394 - INFO - Request Parameters - Page 7:
2025-05-08 15:00:08,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:08,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:08,837 - INFO - Response - Page 7:
2025-05-08 15:00:09,038 - INFO - 第 7 页获取到 82 条记录
2025-05-08 15:00:09,038 - INFO - 查询完成，共获取到 682 条记录
2025-05-08 15:00:09,038 - INFO - 获取到 682 条表单数据
2025-05-08 15:00:09,049 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-08 15:00:09,060 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 15:00:09,060 - INFO - 开始处理日期: 2025-02
2025-05-08 15:00:09,060 - INFO - Request Parameters - Page 1:
2025-05-08 15:00:09,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:09,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:09,539 - INFO - Response - Page 1:
2025-05-08 15:00:09,739 - INFO - 第 1 页获取到 100 条记录
2025-05-08 15:00:09,739 - INFO - Request Parameters - Page 2:
2025-05-08 15:00:09,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:09,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:10,281 - INFO - Response - Page 2:
2025-05-08 15:00:10,481 - INFO - 第 2 页获取到 100 条记录
2025-05-08 15:00:10,481 - INFO - Request Parameters - Page 3:
2025-05-08 15:00:10,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:10,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:11,077 - INFO - Response - Page 3:
2025-05-08 15:00:11,278 - INFO - 第 3 页获取到 100 条记录
2025-05-08 15:00:11,278 - INFO - Request Parameters - Page 4:
2025-05-08 15:00:11,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:11,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:11,705 - INFO - Response - Page 4:
2025-05-08 15:00:11,906 - INFO - 第 4 页获取到 100 条记录
2025-05-08 15:00:11,906 - INFO - Request Parameters - Page 5:
2025-05-08 15:00:11,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:11,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:12,406 - INFO - Response - Page 5:
2025-05-08 15:00:12,606 - INFO - 第 5 页获取到 100 条记录
2025-05-08 15:00:12,606 - INFO - Request Parameters - Page 6:
2025-05-08 15:00:12,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:12,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:13,100 - INFO - Response - Page 6:
2025-05-08 15:00:13,300 - INFO - 第 6 页获取到 100 条记录
2025-05-08 15:00:13,300 - INFO - Request Parameters - Page 7:
2025-05-08 15:00:13,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:13,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:13,751 - INFO - Response - Page 7:
2025-05-08 15:00:13,952 - INFO - 第 7 页获取到 70 条记录
2025-05-08 15:00:13,952 - INFO - 查询完成，共获取到 670 条记录
2025-05-08 15:00:13,952 - INFO - 获取到 670 条表单数据
2025-05-08 15:00:13,963 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-08 15:00:13,975 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 15:00:13,975 - INFO - 开始处理日期: 2025-03
2025-05-08 15:00:13,975 - INFO - Request Parameters - Page 1:
2025-05-08 15:00:13,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:13,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:14,445 - INFO - Response - Page 1:
2025-05-08 15:00:14,645 - INFO - 第 1 页获取到 100 条记录
2025-05-08 15:00:14,645 - INFO - Request Parameters - Page 2:
2025-05-08 15:00:14,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:14,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:15,066 - INFO - Response - Page 2:
2025-05-08 15:00:15,267 - INFO - 第 2 页获取到 100 条记录
2025-05-08 15:00:15,267 - INFO - Request Parameters - Page 3:
2025-05-08 15:00:15,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:15,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:15,941 - INFO - Response - Page 3:
2025-05-08 15:00:16,142 - INFO - 第 3 页获取到 100 条记录
2025-05-08 15:00:16,142 - INFO - Request Parameters - Page 4:
2025-05-08 15:00:16,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:16,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:16,618 - INFO - Response - Page 4:
2025-05-08 15:00:16,818 - INFO - 第 4 页获取到 100 条记录
2025-05-08 15:00:16,818 - INFO - Request Parameters - Page 5:
2025-05-08 15:00:16,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:16,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:17,377 - INFO - Response - Page 5:
2025-05-08 15:00:17,578 - INFO - 第 5 页获取到 100 条记录
2025-05-08 15:00:17,578 - INFO - Request Parameters - Page 6:
2025-05-08 15:00:17,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:17,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:18,083 - INFO - Response - Page 6:
2025-05-08 15:00:18,284 - INFO - 第 6 页获取到 100 条记录
2025-05-08 15:00:18,284 - INFO - Request Parameters - Page 7:
2025-05-08 15:00:18,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:18,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:18,748 - INFO - Response - Page 7:
2025-05-08 15:00:18,948 - INFO - 第 7 页获取到 61 条记录
2025-05-08 15:00:18,948 - INFO - 查询完成，共获取到 661 条记录
2025-05-08 15:00:18,948 - INFO - 获取到 661 条表单数据
2025-05-08 15:00:18,959 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-08 15:00:18,971 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 15:00:18,971 - INFO - 开始处理日期: 2025-04
2025-05-08 15:00:18,971 - INFO - Request Parameters - Page 1:
2025-05-08 15:00:18,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:18,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:19,452 - INFO - Response - Page 1:
2025-05-08 15:00:19,653 - INFO - 第 1 页获取到 100 条记录
2025-05-08 15:00:19,653 - INFO - Request Parameters - Page 2:
2025-05-08 15:00:19,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:19,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:20,101 - INFO - Response - Page 2:
2025-05-08 15:00:20,301 - INFO - 第 2 页获取到 100 条记录
2025-05-08 15:00:20,301 - INFO - Request Parameters - Page 3:
2025-05-08 15:00:20,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:20,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:20,839 - INFO - Response - Page 3:
2025-05-08 15:00:21,040 - INFO - 第 3 页获取到 100 条记录
2025-05-08 15:00:21,040 - INFO - Request Parameters - Page 4:
2025-05-08 15:00:21,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:21,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:21,574 - INFO - Response - Page 4:
2025-05-08 15:00:21,774 - INFO - 第 4 页获取到 100 条记录
2025-05-08 15:00:21,774 - INFO - Request Parameters - Page 5:
2025-05-08 15:00:21,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:21,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:22,383 - INFO - Response - Page 5:
2025-05-08 15:00:22,583 - INFO - 第 5 页获取到 100 条记录
2025-05-08 15:00:22,583 - INFO - Request Parameters - Page 6:
2025-05-08 15:00:22,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:22,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:23,211 - INFO - Response - Page 6:
2025-05-08 15:00:23,413 - INFO - 第 6 页获取到 100 条记录
2025-05-08 15:00:23,413 - INFO - Request Parameters - Page 7:
2025-05-08 15:00:23,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:23,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:23,810 - INFO - Response - Page 7:
2025-05-08 15:00:24,010 - INFO - 第 7 页获取到 53 条记录
2025-05-08 15:00:24,010 - INFO - 查询完成，共获取到 653 条记录
2025-05-08 15:00:24,010 - INFO - 获取到 653 条表单数据
2025-05-08 15:00:24,022 - INFO - 当前日期 2025-04 有 653 条MySQL数据需要处理
2025-05-08 15:00:24,023 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M44
2025-05-08 15:00:24,473 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M44
2025-05-08 15:00:24,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20951.0, 'new_value': 20969.0}, {'field': 'total_amount', 'old_value': 21052.0, 'new_value': 21070.0}]
2025-05-08 15:00:24,474 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-05-08 15:00:24,992 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-05-08 15:00:24,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44749.0, 'new_value': 44746.0}, {'field': 'total_amount', 'old_value': 44999.0, 'new_value': 44996.0}]
2025-05-08 15:00:24,998 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVH
2025-05-08 15:00:25,571 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVH
2025-05-08 15:00:25,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86030.84, 'new_value': 84272.0}, {'field': 'total_amount', 'old_value': 86030.84, 'new_value': 84272.0}]
2025-05-08 15:00:25,574 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-05-08 15:00:26,002 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-05-08 15:00:26,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16597395.0, 'new_value': 16597340.0}, {'field': 'total_amount', 'old_value': 16597395.0, 'new_value': 16597340.0}]
2025-05-08 15:00:26,003 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-05-08 15:00:26,488 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-05-08 15:00:26,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1441623.0, 'new_value': 1430919.0}, {'field': 'total_amount', 'old_value': 1441623.0, 'new_value': 1430919.0}]
2025-05-08 15:00:26,490 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-05-08 15:00:26,933 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-05-08 15:00:26,933 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94110.48, 'new_value': 90353.3}, {'field': 'offline_amount', 'old_value': 96663.59, 'new_value': 100459.01}, {'field': 'total_amount', 'old_value': 190774.07, 'new_value': 190812.31}]
2025-05-08 15:00:26,934 - INFO - 日期 2025-04 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-05-08 15:00:26,934 - INFO - 开始处理日期: 2025-05
2025-05-08 15:00:26,935 - INFO - Request Parameters - Page 1:
2025-05-08 15:00:26,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:26,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:27,435 - INFO - Response - Page 1:
2025-05-08 15:00:27,635 - INFO - 第 1 页获取到 100 条记录
2025-05-08 15:00:27,635 - INFO - Request Parameters - Page 2:
2025-05-08 15:00:27,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:27,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:28,157 - INFO - Response - Page 2:
2025-05-08 15:00:28,357 - INFO - 第 2 页获取到 100 条记录
2025-05-08 15:00:28,357 - INFO - Request Parameters - Page 3:
2025-05-08 15:00:28,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:28,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:28,865 - INFO - Response - Page 3:
2025-05-08 15:00:29,065 - INFO - 第 3 页获取到 100 条记录
2025-05-08 15:00:29,065 - INFO - Request Parameters - Page 4:
2025-05-08 15:00:29,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:29,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:29,659 - INFO - Response - Page 4:
2025-05-08 15:00:29,859 - INFO - 第 4 页获取到 100 条记录
2025-05-08 15:00:29,859 - INFO - Request Parameters - Page 5:
2025-05-08 15:00:29,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:29,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:30,288 - INFO - Response - Page 5:
2025-05-08 15:00:30,489 - INFO - 第 5 页获取到 100 条记录
2025-05-08 15:00:30,489 - INFO - Request Parameters - Page 6:
2025-05-08 15:00:30,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:30,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:30,992 - INFO - Response - Page 6:
2025-05-08 15:00:31,192 - INFO - 第 6 页获取到 100 条记录
2025-05-08 15:00:31,192 - INFO - Request Parameters - Page 7:
2025-05-08 15:00:31,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 15:00:31,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 15:00:31,559 - INFO - Response - Page 7:
2025-05-08 15:00:31,760 - INFO - 第 7 页获取到 21 条记录
2025-05-08 15:00:31,760 - INFO - 查询完成，共获取到 621 条记录
2025-05-08 15:00:31,760 - INFO - 获取到 621 条表单数据
2025-05-08 15:00:31,773 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-08 15:00:31,773 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-08 15:00:32,218 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-08 15:00:32,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6268.3, 'new_value': 8754.7}, {'field': 'total_amount', 'old_value': 6268.3, 'new_value': 8754.7}, {'field': 'order_count', 'old_value': 39, 'new_value': 47}]
2025-05-08 15:00:32,218 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-08 15:00:32,746 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-08 15:00:32,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6318.0, 'new_value': 8460.68}, {'field': 'total_amount', 'old_value': 6318.0, 'new_value': 8460.68}, {'field': 'order_count', 'old_value': 1076, 'new_value': 1527}]
2025-05-08 15:00:32,747 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-08 15:00:33,308 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-08 15:00:33,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33413.0, 'new_value': 37269.0}, {'field': 'total_amount', 'old_value': 43113.0, 'new_value': 46969.0}, {'field': 'order_count', 'old_value': 469, 'new_value': 538}]
2025-05-08 15:00:33,310 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-08 15:00:33,718 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-08 15:00:33,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28239.07, 'new_value': 31881.0}, {'field': 'total_amount', 'old_value': 28239.07, 'new_value': 31881.0}, {'field': 'order_count', 'old_value': 1559, 'new_value': 1795}]
2025-05-08 15:00:33,719 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-08 15:00:34,163 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-08 15:00:34,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4575.0, 'new_value': 5455.0}, {'field': 'total_amount', 'old_value': 6332.0, 'new_value': 7212.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 37}]
2025-05-08 15:00:34,164 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-08 15:00:34,783 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-08 15:00:34,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56174.0, 'new_value': 56341.0}, {'field': 'total_amount', 'old_value': 56174.0, 'new_value': 56341.0}, {'field': 'order_count', 'old_value': 1018, 'new_value': 8391}]
2025-05-08 15:00:34,785 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-08 15:00:35,322 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-08 15:00:35,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2694.28, 'new_value': 3014.68}, {'field': 'offline_amount', 'old_value': 31873.91, 'new_value': 35793.32}, {'field': 'total_amount', 'old_value': 34568.19, 'new_value': 38808.0}, {'field': 'order_count', 'old_value': 842, 'new_value': 980}]
2025-05-08 15:00:35,325 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-08 15:00:35,812 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-08 15:00:35,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117763.0, 'new_value': 129021.0}, {'field': 'total_amount', 'old_value': 117763.0, 'new_value': 129021.0}, {'field': 'order_count', 'old_value': 2728, 'new_value': 3114}]
2025-05-08 15:00:35,816 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-08 15:00:36,241 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-08 15:00:36,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27250.7, 'new_value': 31692.1}, {'field': 'offline_amount', 'old_value': 14191.8, 'new_value': 14836.8}, {'field': 'total_amount', 'old_value': 41442.5, 'new_value': 46528.9}, {'field': 'order_count', 'old_value': 3486, 'new_value': 3912}]
2025-05-08 15:00:36,242 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-08 15:00:36,742 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-08 15:00:36,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7844.61, 'new_value': 8659.99}, {'field': 'offline_amount', 'old_value': 8102.9, 'new_value': 8866.2}, {'field': 'total_amount', 'old_value': 15947.51, 'new_value': 17526.19}, {'field': 'order_count', 'old_value': 706, 'new_value': 783}]
2025-05-08 15:00:36,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-08 15:00:37,159 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-08 15:00:37,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74502.0, 'new_value': 79539.0}, {'field': 'total_amount', 'old_value': 74502.0, 'new_value': 79539.0}, {'field': 'order_count', 'old_value': 143, 'new_value': 156}]
2025-05-08 15:00:37,160 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-08 15:00:37,559 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-08 15:00:37,559 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 739.0, 'new_value': 1126.0}, {'field': 'offline_amount', 'old_value': 3346.0, 'new_value': 4631.0}, {'field': 'total_amount', 'old_value': 4085.0, 'new_value': 5757.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 145}]
2025-05-08 15:00:37,560 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-08 15:00:38,140 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-08 15:00:38,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45931.73, 'new_value': 51760.23}, {'field': 'total_amount', 'old_value': 45931.73, 'new_value': 51760.23}, {'field': 'order_count', 'old_value': 3257, 'new_value': 3809}]
2025-05-08 15:00:38,140 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-08 15:00:38,578 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-08 15:00:38,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8424.4, 'new_value': 9715.75}, {'field': 'total_amount', 'old_value': 8424.4, 'new_value': 9715.75}, {'field': 'order_count', 'old_value': 388, 'new_value': 457}]
2025-05-08 15:00:38,578 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMDQ
2025-05-08 15:00:39,068 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMDQ
2025-05-08 15:00:39,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13138.0, 'new_value': 14137.0}, {'field': 'total_amount', 'old_value': 13138.0, 'new_value': 14137.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-08 15:00:39,068 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-08 15:00:39,515 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-08 15:00:39,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21447.06, 'new_value': 25080.33}, {'field': 'total_amount', 'old_value': 21447.06, 'new_value': 25080.33}, {'field': 'order_count', 'old_value': 1982, 'new_value': 2297}]
2025-05-08 15:00:39,516 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-08 15:00:39,958 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-08 15:00:39,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7483.0, 'new_value': 10988.0}, {'field': 'total_amount', 'old_value': 7483.0, 'new_value': 10988.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-08 15:00:39,958 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-08 15:00:40,495 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-08 15:00:40,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4190.0, 'new_value': 5490.0}, {'field': 'offline_amount', 'old_value': 6590.0, 'new_value': 7890.0}, {'field': 'total_amount', 'old_value': 10780.0, 'new_value': 13380.0}, {'field': 'order_count', 'old_value': 4198, 'new_value': 5528}]
2025-05-08 15:00:40,496 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-08 15:00:40,962 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-08 15:00:40,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29729.0, 'new_value': 31196.0}, {'field': 'total_amount', 'old_value': 29729.0, 'new_value': 31196.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 127}]
2025-05-08 15:00:40,963 - INFO - 日期 2025-05 处理完成 - 更新: 19 条，插入: 0 条，错误: 0 条
2025-05-08 15:00:40,963 - INFO - 数据同步完成！更新: 25 条，插入: 0 条，错误: 0 条
2025-05-08 15:00:40,965 - INFO - =================同步完成====================
2025-05-08 18:00:02,023 - INFO - =================使用默认全量同步=============
2025-05-08 18:00:03,271 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-08 18:00:03,272 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-08 18:00:03,299 - INFO - 开始处理日期: 2025-01
2025-05-08 18:00:03,302 - INFO - Request Parameters - Page 1:
2025-05-08 18:00:03,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:03,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:04,309 - INFO - Response - Page 1:
2025-05-08 18:00:04,509 - INFO - 第 1 页获取到 100 条记录
2025-05-08 18:00:04,509 - INFO - Request Parameters - Page 2:
2025-05-08 18:00:04,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:04,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:05,437 - INFO - Response - Page 2:
2025-05-08 18:00:05,637 - INFO - 第 2 页获取到 100 条记录
2025-05-08 18:00:05,637 - INFO - Request Parameters - Page 3:
2025-05-08 18:00:05,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:05,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:06,135 - INFO - Response - Page 3:
2025-05-08 18:00:06,335 - INFO - 第 3 页获取到 100 条记录
2025-05-08 18:00:06,335 - INFO - Request Parameters - Page 4:
2025-05-08 18:00:06,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:06,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:06,864 - INFO - Response - Page 4:
2025-05-08 18:00:07,065 - INFO - 第 4 页获取到 100 条记录
2025-05-08 18:00:07,065 - INFO - Request Parameters - Page 5:
2025-05-08 18:00:07,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:07,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:07,591 - INFO - Response - Page 5:
2025-05-08 18:00:07,791 - INFO - 第 5 页获取到 100 条记录
2025-05-08 18:00:07,791 - INFO - Request Parameters - Page 6:
2025-05-08 18:00:07,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:07,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:08,265 - INFO - Response - Page 6:
2025-05-08 18:00:08,465 - INFO - 第 6 页获取到 100 条记录
2025-05-08 18:00:08,465 - INFO - Request Parameters - Page 7:
2025-05-08 18:00:08,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:08,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:08,908 - INFO - Response - Page 7:
2025-05-08 18:00:09,108 - INFO - 第 7 页获取到 82 条记录
2025-05-08 18:00:09,108 - INFO - 查询完成，共获取到 682 条记录
2025-05-08 18:00:09,108 - INFO - 获取到 682 条表单数据
2025-05-08 18:00:09,119 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-08 18:00:09,131 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 18:00:09,132 - INFO - 开始处理日期: 2025-02
2025-05-08 18:00:09,132 - INFO - Request Parameters - Page 1:
2025-05-08 18:00:09,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:09,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:09,637 - INFO - Response - Page 1:
2025-05-08 18:00:09,837 - INFO - 第 1 页获取到 100 条记录
2025-05-08 18:00:09,837 - INFO - Request Parameters - Page 2:
2025-05-08 18:00:09,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:09,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:10,312 - INFO - Response - Page 2:
2025-05-08 18:00:10,512 - INFO - 第 2 页获取到 100 条记录
2025-05-08 18:00:10,512 - INFO - Request Parameters - Page 3:
2025-05-08 18:00:10,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:10,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:10,973 - INFO - Response - Page 3:
2025-05-08 18:00:11,173 - INFO - 第 3 页获取到 100 条记录
2025-05-08 18:00:11,173 - INFO - Request Parameters - Page 4:
2025-05-08 18:00:11,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:11,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:11,690 - INFO - Response - Page 4:
2025-05-08 18:00:11,890 - INFO - 第 4 页获取到 100 条记录
2025-05-08 18:00:11,890 - INFO - Request Parameters - Page 5:
2025-05-08 18:00:11,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:11,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:12,347 - INFO - Response - Page 5:
2025-05-08 18:00:12,547 - INFO - 第 5 页获取到 100 条记录
2025-05-08 18:00:12,547 - INFO - Request Parameters - Page 6:
2025-05-08 18:00:12,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:12,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:13,111 - INFO - Response - Page 6:
2025-05-08 18:00:13,311 - INFO - 第 6 页获取到 100 条记录
2025-05-08 18:00:13,311 - INFO - Request Parameters - Page 7:
2025-05-08 18:00:13,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:13,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:13,778 - INFO - Response - Page 7:
2025-05-08 18:00:13,978 - INFO - 第 7 页获取到 70 条记录
2025-05-08 18:00:13,978 - INFO - 查询完成，共获取到 670 条记录
2025-05-08 18:00:13,978 - INFO - 获取到 670 条表单数据
2025-05-08 18:00:13,991 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-08 18:00:14,002 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 18:00:14,002 - INFO - 开始处理日期: 2025-03
2025-05-08 18:00:14,002 - INFO - Request Parameters - Page 1:
2025-05-08 18:00:14,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:14,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:14,485 - INFO - Response - Page 1:
2025-05-08 18:00:14,685 - INFO - 第 1 页获取到 100 条记录
2025-05-08 18:00:14,685 - INFO - Request Parameters - Page 2:
2025-05-08 18:00:14,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:14,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:15,200 - INFO - Response - Page 2:
2025-05-08 18:00:15,401 - INFO - 第 2 页获取到 100 条记录
2025-05-08 18:00:15,401 - INFO - Request Parameters - Page 3:
2025-05-08 18:00:15,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:15,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:15,916 - INFO - Response - Page 3:
2025-05-08 18:00:16,116 - INFO - 第 3 页获取到 100 条记录
2025-05-08 18:00:16,116 - INFO - Request Parameters - Page 4:
2025-05-08 18:00:16,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:16,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:16,587 - INFO - Response - Page 4:
2025-05-08 18:00:16,787 - INFO - 第 4 页获取到 100 条记录
2025-05-08 18:00:16,787 - INFO - Request Parameters - Page 5:
2025-05-08 18:00:16,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:16,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:17,246 - INFO - Response - Page 5:
2025-05-08 18:00:17,447 - INFO - 第 5 页获取到 100 条记录
2025-05-08 18:00:17,447 - INFO - Request Parameters - Page 6:
2025-05-08 18:00:17,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:17,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:17,935 - INFO - Response - Page 6:
2025-05-08 18:00:18,135 - INFO - 第 6 页获取到 100 条记录
2025-05-08 18:00:18,135 - INFO - Request Parameters - Page 7:
2025-05-08 18:00:18,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:18,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:18,523 - INFO - Response - Page 7:
2025-05-08 18:00:18,723 - INFO - 第 7 页获取到 61 条记录
2025-05-08 18:00:18,723 - INFO - 查询完成，共获取到 661 条记录
2025-05-08 18:00:18,724 - INFO - 获取到 661 条表单数据
2025-05-08 18:00:18,737 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-08 18:00:18,748 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 18:00:18,748 - INFO - 开始处理日期: 2025-04
2025-05-08 18:00:18,748 - INFO - Request Parameters - Page 1:
2025-05-08 18:00:18,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:18,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:19,275 - INFO - Response - Page 1:
2025-05-08 18:00:19,475 - INFO - 第 1 页获取到 100 条记录
2025-05-08 18:00:19,475 - INFO - Request Parameters - Page 2:
2025-05-08 18:00:19,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:19,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:20,016 - INFO - Response - Page 2:
2025-05-08 18:00:20,216 - INFO - 第 2 页获取到 100 条记录
2025-05-08 18:00:20,216 - INFO - Request Parameters - Page 3:
2025-05-08 18:00:20,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:20,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:20,698 - INFO - Response - Page 3:
2025-05-08 18:00:20,899 - INFO - 第 3 页获取到 100 条记录
2025-05-08 18:00:20,899 - INFO - Request Parameters - Page 4:
2025-05-08 18:00:20,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:20,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:21,383 - INFO - Response - Page 4:
2025-05-08 18:00:21,584 - INFO - 第 4 页获取到 100 条记录
2025-05-08 18:00:21,584 - INFO - Request Parameters - Page 5:
2025-05-08 18:00:21,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:21,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:22,088 - INFO - Response - Page 5:
2025-05-08 18:00:22,288 - INFO - 第 5 页获取到 100 条记录
2025-05-08 18:00:22,288 - INFO - Request Parameters - Page 6:
2025-05-08 18:00:22,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:22,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:22,776 - INFO - Response - Page 6:
2025-05-08 18:00:22,976 - INFO - 第 6 页获取到 100 条记录
2025-05-08 18:00:22,976 - INFO - Request Parameters - Page 7:
2025-05-08 18:00:22,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:22,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:23,489 - INFO - Response - Page 7:
2025-05-08 18:00:23,689 - INFO - 第 7 页获取到 53 条记录
2025-05-08 18:00:23,689 - INFO - 查询完成，共获取到 653 条记录
2025-05-08 18:00:23,690 - INFO - 获取到 653 条表单数据
2025-05-08 18:00:23,711 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-08 18:00:23,711 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-05-08 18:00:24,215 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-05-08 18:00:24,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32035.5, 'new_value': 51614.0}, {'field': 'total_amount', 'old_value': 32035.5, 'new_value': 51614.0}]
2025-05-08 18:00:24,216 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-05-08 18:00:24,667 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-05-08 18:00:24,667 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9155.73, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 28209.41, 'new_value': 31277.41}, {'field': 'total_amount', 'old_value': 37365.14, 'new_value': 31277.41}, {'field': 'order_count', 'old_value': 7195, 'new_value': 49}]
2025-05-08 18:00:24,668 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-05-08 18:00:25,135 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-05-08 18:00:25,135 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59364.0, 'new_value': 54094.0}, {'field': 'offline_amount', 'old_value': 163170.0, 'new_value': 167941.0}, {'field': 'total_amount', 'old_value': 222534.0, 'new_value': 222035.0}]
2025-05-08 18:00:25,136 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-05-08 18:00:25,556 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-05-08 18:00:25,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91818.0, 'new_value': 91836.0}, {'field': 'total_amount', 'old_value': 91818.0, 'new_value': 91836.0}]
2025-05-08 18:00:25,556 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-05-08 18:00:25,997 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-05-08 18:00:25,997 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120428.9, 'new_value': 87732.0}, {'field': 'offline_amount', 'old_value': 199390.25, 'new_value': 231955.18}, {'field': 'total_amount', 'old_value': 319819.15, 'new_value': 319687.18}]
2025-05-08 18:00:25,999 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-05-08 18:00:26,447 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-05-08 18:00:26,447 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117982.3, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 338590.61, 'new_value': 456654.35}, {'field': 'total_amount', 'old_value': 456572.91, 'new_value': 456654.35}]
2025-05-08 18:00:26,448 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-05-08 18:00:26,905 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-05-08 18:00:26,905 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69004.64, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 87116.12, 'new_value': 153878.83}, {'field': 'total_amount', 'old_value': 156120.76, 'new_value': 153878.83}]
2025-05-08 18:00:26,907 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-05-08 18:00:27,471 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-05-08 18:00:27,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104996.88, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 70345.67, 'new_value': 175397.94}, {'field': 'total_amount', 'old_value': 175342.55, 'new_value': 175397.94}]
2025-05-08 18:00:27,472 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-05-08 18:00:27,935 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-05-08 18:00:27,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44762.89, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 275654.16, 'new_value': 323969.77}, {'field': 'total_amount', 'old_value': 320417.05, 'new_value': 323969.77}]
2025-05-08 18:00:27,935 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-05-08 18:00:28,474 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-05-08 18:00:28,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15758.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 317920.53, 'new_value': 330108.68}, {'field': 'total_amount', 'old_value': 333679.33, 'new_value': 330108.68}]
2025-05-08 18:00:28,476 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MII
2025-05-08 18:00:28,977 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MII
2025-05-08 18:00:28,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83351.19, 'new_value': 83351.17}, {'field': 'total_amount', 'old_value': 98075.41, 'new_value': 98075.39}]
2025-05-08 18:00:28,977 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-05-08 18:00:29,457 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-05-08 18:00:29,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93561.5, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 186546.5, 'new_value': 280618.0}, {'field': 'total_amount', 'old_value': 280108.0, 'new_value': 280618.0}]
2025-05-08 18:00:29,457 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-05-08 18:00:29,966 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-05-08 18:00:29,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183332.94, 'new_value': 183332.63}, {'field': 'total_amount', 'old_value': 183332.94, 'new_value': 183332.63}]
2025-05-08 18:00:29,967 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-05-08 18:00:30,430 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-05-08 18:00:30,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11457.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 447484.43, 'new_value': 459507.08}, {'field': 'total_amount', 'old_value': 458941.43, 'new_value': 459507.08}]
2025-05-08 18:00:30,431 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-05-08 18:00:30,934 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-05-08 18:00:30,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153664.0, 'new_value': 153343.9}, {'field': 'total_amount', 'old_value': 153664.0, 'new_value': 153343.9}]
2025-05-08 18:00:30,934 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-05-08 18:00:31,413 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-05-08 18:00:31,413 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3151.23, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 16084.94, 'new_value': 19236.2}, {'field': 'total_amount', 'old_value': 19236.17, 'new_value': 19236.2}]
2025-05-08 18:00:31,414 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-05-08 18:00:31,918 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-05-08 18:00:31,918 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12067.0, 'new_value': 226.0}, {'field': 'offline_amount', 'old_value': 92588.0, 'new_value': 167120.0}, {'field': 'total_amount', 'old_value': 104655.0, 'new_value': 167346.0}]
2025-05-08 18:00:31,919 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-05-08 18:00:32,392 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-05-08 18:00:32,393 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6666.48, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 60115.8, 'new_value': 72229.5}, {'field': 'total_amount', 'old_value': 66782.28, 'new_value': 72229.5}]
2025-05-08 18:00:32,393 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-05-08 18:00:32,864 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-05-08 18:00:32,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 756290.41, 'new_value': 689946.67}, {'field': 'total_amount', 'old_value': 756290.41, 'new_value': 689946.67}]
2025-05-08 18:00:32,865 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-05-08 18:00:33,373 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-05-08 18:00:33,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39557.6, 'new_value': 39558.1}, {'field': 'total_amount', 'old_value': 103619.79, 'new_value': 103620.29}]
2025-05-08 18:00:33,377 - INFO - 开始批量插入 1 条新记录
2025-05-08 18:00:33,535 - INFO - 批量插入响应状态码: 200
2025-05-08 18:00:33,536 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 08 May 2025 10:00:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '09855856-5624-79B9-A3A5-576A53D8D676', 'x-acs-trace-id': 'f1eae63fa3c664f86e54ee5598a45fd5', 'etag': '6mfA2Wpx1qRRsY0ti1UrIWg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-08 18:00:33,536 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1O09V1NST7I659D19SZJF3QYV47FAMH2']}
2025-05-08 18:00:33,536 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-08 18:00:33,536 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1O09V1NST7I659D19SZJF3QYV47FAMH2']
2025-05-08 18:00:36,537 - INFO - 批量插入完成，共 1 条记录
2025-05-08 18:00:36,537 - INFO - 日期 2025-04 处理完成 - 更新: 20 条，插入: 1 条，错误: 0 条
2025-05-08 18:00:36,537 - INFO - 开始处理日期: 2025-05
2025-05-08 18:00:36,537 - INFO - Request Parameters - Page 1:
2025-05-08 18:00:36,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:36,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:37,023 - INFO - Response - Page 1:
2025-05-08 18:00:37,223 - INFO - 第 1 页获取到 100 条记录
2025-05-08 18:00:37,223 - INFO - Request Parameters - Page 2:
2025-05-08 18:00:37,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:37,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:37,806 - INFO - Response - Page 2:
2025-05-08 18:00:38,006 - INFO - 第 2 页获取到 100 条记录
2025-05-08 18:00:38,006 - INFO - Request Parameters - Page 3:
2025-05-08 18:00:38,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:38,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:38,496 - INFO - Response - Page 3:
2025-05-08 18:00:38,697 - INFO - 第 3 页获取到 100 条记录
2025-05-08 18:00:38,697 - INFO - Request Parameters - Page 4:
2025-05-08 18:00:38,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:38,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:39,254 - INFO - Response - Page 4:
2025-05-08 18:00:39,454 - INFO - 第 4 页获取到 100 条记录
2025-05-08 18:00:39,454 - INFO - Request Parameters - Page 5:
2025-05-08 18:00:39,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:39,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:39,910 - INFO - Response - Page 5:
2025-05-08 18:00:40,110 - INFO - 第 5 页获取到 100 条记录
2025-05-08 18:00:40,110 - INFO - Request Parameters - Page 6:
2025-05-08 18:00:40,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:40,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:40,621 - INFO - Response - Page 6:
2025-05-08 18:00:40,822 - INFO - 第 6 页获取到 100 条记录
2025-05-08 18:00:40,822 - INFO - Request Parameters - Page 7:
2025-05-08 18:00:40,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 18:00:40,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 18:00:41,161 - INFO - Response - Page 7:
2025-05-08 18:00:41,361 - INFO - 第 7 页获取到 21 条记录
2025-05-08 18:00:41,361 - INFO - 查询完成，共获取到 621 条记录
2025-05-08 18:00:41,361 - INFO - 获取到 621 条表单数据
2025-05-08 18:00:41,373 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-08 18:00:41,374 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-08 18:00:41,846 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-08 18:00:41,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192578.08, 'new_value': 215011.08}, {'field': 'total_amount', 'old_value': 192578.08, 'new_value': 215011.08}, {'field': 'order_count', 'old_value': 607, 'new_value': 660}]
2025-05-08 18:00:41,853 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-08 18:00:42,329 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-08 18:00:42,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88.87, 'new_value': 147.32}, {'field': 'offline_amount', 'old_value': 161126.06, 'new_value': 182241.76}, {'field': 'total_amount', 'old_value': 161214.93, 'new_value': 182389.08}, {'field': 'order_count', 'old_value': 340, 'new_value': 402}]
2025-05-08 18:00:42,332 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-08 18:00:42,801 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-08 18:00:42,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41730.64, 'new_value': 47374.44}, {'field': 'offline_amount', 'old_value': 96921.32, 'new_value': 103540.92}, {'field': 'total_amount', 'old_value': 138651.96, 'new_value': 150915.36}, {'field': 'order_count', 'old_value': 1048, 'new_value': 1152}]
2025-05-08 18:00:42,804 - INFO - 日期 2025-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-08 18:00:42,804 - INFO - 数据同步完成！更新: 23 条，插入: 1 条，错误: 0 条
2025-05-08 18:00:42,806 - INFO - =================同步完成====================
2025-05-08 21:00:01,975 - INFO - =================使用默认全量同步=============
2025-05-08 21:00:03,215 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-08 21:00:03,216 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-08 21:00:03,244 - INFO - 开始处理日期: 2025-01
2025-05-08 21:00:03,247 - INFO - Request Parameters - Page 1:
2025-05-08 21:00:03,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:03,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:04,306 - INFO - Response - Page 1:
2025-05-08 21:00:04,507 - INFO - 第 1 页获取到 100 条记录
2025-05-08 21:00:04,507 - INFO - Request Parameters - Page 2:
2025-05-08 21:00:04,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:04,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:05,288 - INFO - Response - Page 2:
2025-05-08 21:00:05,488 - INFO - 第 2 页获取到 100 条记录
2025-05-08 21:00:05,488 - INFO - Request Parameters - Page 3:
2025-05-08 21:00:05,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:05,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:05,967 - INFO - Response - Page 3:
2025-05-08 21:00:06,168 - INFO - 第 3 页获取到 100 条记录
2025-05-08 21:00:06,168 - INFO - Request Parameters - Page 4:
2025-05-08 21:00:06,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:06,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:06,645 - INFO - Response - Page 4:
2025-05-08 21:00:06,846 - INFO - 第 4 页获取到 100 条记录
2025-05-08 21:00:06,846 - INFO - Request Parameters - Page 5:
2025-05-08 21:00:06,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:06,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:07,347 - INFO - Response - Page 5:
2025-05-08 21:00:07,547 - INFO - 第 5 页获取到 100 条记录
2025-05-08 21:00:07,547 - INFO - Request Parameters - Page 6:
2025-05-08 21:00:07,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:07,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:08,132 - INFO - Response - Page 6:
2025-05-08 21:00:08,332 - INFO - 第 6 页获取到 100 条记录
2025-05-08 21:00:08,332 - INFO - Request Parameters - Page 7:
2025-05-08 21:00:08,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:08,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:08,782 - INFO - Response - Page 7:
2025-05-08 21:00:08,983 - INFO - 第 7 页获取到 82 条记录
2025-05-08 21:00:08,983 - INFO - 查询完成，共获取到 682 条记录
2025-05-08 21:00:08,983 - INFO - 获取到 682 条表单数据
2025-05-08 21:00:08,995 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-08 21:00:09,007 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 21:00:09,007 - INFO - 开始处理日期: 2025-02
2025-05-08 21:00:09,008 - INFO - Request Parameters - Page 1:
2025-05-08 21:00:09,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:09,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:09,501 - INFO - Response - Page 1:
2025-05-08 21:00:09,702 - INFO - 第 1 页获取到 100 条记录
2025-05-08 21:00:09,702 - INFO - Request Parameters - Page 2:
2025-05-08 21:00:09,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:09,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:10,226 - INFO - Response - Page 2:
2025-05-08 21:00:10,426 - INFO - 第 2 页获取到 100 条记录
2025-05-08 21:00:10,426 - INFO - Request Parameters - Page 3:
2025-05-08 21:00:10,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:10,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:11,019 - INFO - Response - Page 3:
2025-05-08 21:00:11,219 - INFO - 第 3 页获取到 100 条记录
2025-05-08 21:00:11,219 - INFO - Request Parameters - Page 4:
2025-05-08 21:00:11,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:11,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:11,765 - INFO - Response - Page 4:
2025-05-08 21:00:11,965 - INFO - 第 4 页获取到 100 条记录
2025-05-08 21:00:11,965 - INFO - Request Parameters - Page 5:
2025-05-08 21:00:11,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:11,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:12,460 - INFO - Response - Page 5:
2025-05-08 21:00:12,660 - INFO - 第 5 页获取到 100 条记录
2025-05-08 21:00:12,660 - INFO - Request Parameters - Page 6:
2025-05-08 21:00:12,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:12,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:13,210 - INFO - Response - Page 6:
2025-05-08 21:00:13,410 - INFO - 第 6 页获取到 100 条记录
2025-05-08 21:00:13,410 - INFO - Request Parameters - Page 7:
2025-05-08 21:00:13,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:13,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:13,867 - INFO - Response - Page 7:
2025-05-08 21:00:14,067 - INFO - 第 7 页获取到 70 条记录
2025-05-08 21:00:14,067 - INFO - 查询完成，共获取到 670 条记录
2025-05-08 21:00:14,067 - INFO - 获取到 670 条表单数据
2025-05-08 21:00:14,080 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-08 21:00:14,093 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-08 21:00:14,093 - INFO - 开始处理日期: 2025-03
2025-05-08 21:00:14,094 - INFO - Request Parameters - Page 1:
2025-05-08 21:00:14,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:14,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:14,583 - INFO - Response - Page 1:
2025-05-08 21:00:14,783 - INFO - 第 1 页获取到 100 条记录
2025-05-08 21:00:14,783 - INFO - Request Parameters - Page 2:
2025-05-08 21:00:14,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:14,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:15,267 - INFO - Response - Page 2:
2025-05-08 21:00:15,467 - INFO - 第 2 页获取到 100 条记录
2025-05-08 21:00:15,467 - INFO - Request Parameters - Page 3:
2025-05-08 21:00:15,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:15,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:16,077 - INFO - Response - Page 3:
2025-05-08 21:00:16,277 - INFO - 第 3 页获取到 100 条记录
2025-05-08 21:00:16,277 - INFO - Request Parameters - Page 4:
2025-05-08 21:00:16,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:16,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:16,777 - INFO - Response - Page 4:
2025-05-08 21:00:16,978 - INFO - 第 4 页获取到 100 条记录
2025-05-08 21:00:16,978 - INFO - Request Parameters - Page 5:
2025-05-08 21:00:16,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:16,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:17,461 - INFO - Response - Page 5:
2025-05-08 21:00:17,661 - INFO - 第 5 页获取到 100 条记录
2025-05-08 21:00:17,661 - INFO - Request Parameters - Page 6:
2025-05-08 21:00:17,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:17,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:18,179 - INFO - Response - Page 6:
2025-05-08 21:00:18,379 - INFO - 第 6 页获取到 100 条记录
2025-05-08 21:00:18,379 - INFO - Request Parameters - Page 7:
2025-05-08 21:00:18,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:18,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:18,889 - INFO - Response - Page 7:
2025-05-08 21:00:19,090 - INFO - 第 7 页获取到 61 条记录
2025-05-08 21:00:19,090 - INFO - 查询完成，共获取到 661 条记录
2025-05-08 21:00:19,090 - INFO - 获取到 661 条表单数据
2025-05-08 21:00:19,103 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-08 21:00:19,105 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81QMVUWDNIBFB2Y8QJ3KD43UR2FMW9MT1
2025-05-08 21:00:19,548 - INFO - 更新表单数据成功: FINST-WBF66B81QMVUWDNIBFB2Y8QJ3KD43UR2FMW9MT1
2025-05-08 21:00:19,548 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 170936.58, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 27384.0, 'new_value': 197369.81}, {'field': 'total_amount', 'old_value': 198320.58, 'new_value': 197369.81}, {'field': 'order_count', 'old_value': 5234, 'new_value': 4499}]
2025-05-08 21:00:19,558 - INFO - 日期 2025-03 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-08 21:00:19,558 - INFO - 开始处理日期: 2025-04
2025-05-08 21:00:19,558 - INFO - Request Parameters - Page 1:
2025-05-08 21:00:19,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:19,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:20,188 - INFO - Response - Page 1:
2025-05-08 21:00:20,389 - INFO - 第 1 页获取到 100 条记录
2025-05-08 21:00:20,389 - INFO - Request Parameters - Page 2:
2025-05-08 21:00:20,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:20,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:20,891 - INFO - Response - Page 2:
2025-05-08 21:00:21,091 - INFO - 第 2 页获取到 100 条记录
2025-05-08 21:00:21,091 - INFO - Request Parameters - Page 3:
2025-05-08 21:00:21,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:21,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:21,686 - INFO - Response - Page 3:
2025-05-08 21:00:21,887 - INFO - 第 3 页获取到 100 条记录
2025-05-08 21:00:21,887 - INFO - Request Parameters - Page 4:
2025-05-08 21:00:21,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:21,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:22,354 - INFO - Response - Page 4:
2025-05-08 21:00:22,555 - INFO - 第 4 页获取到 100 条记录
2025-05-08 21:00:22,555 - INFO - Request Parameters - Page 5:
2025-05-08 21:00:22,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:22,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:23,013 - INFO - Response - Page 5:
2025-05-08 21:00:23,213 - INFO - 第 5 页获取到 100 条记录
2025-05-08 21:00:23,213 - INFO - Request Parameters - Page 6:
2025-05-08 21:00:23,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:23,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:23,806 - INFO - Response - Page 6:
2025-05-08 21:00:24,006 - INFO - 第 6 页获取到 100 条记录
2025-05-08 21:00:24,006 - INFO - Request Parameters - Page 7:
2025-05-08 21:00:24,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:24,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:24,416 - INFO - Response - Page 7:
2025-05-08 21:00:24,616 - INFO - 第 7 页获取到 54 条记录
2025-05-08 21:00:24,616 - INFO - 查询完成，共获取到 654 条记录
2025-05-08 21:00:24,616 - INFO - 获取到 654 条表单数据
2025-05-08 21:00:24,628 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-08 21:00:24,632 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-05-08 21:00:25,107 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-05-08 21:00:25,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23731.38, 'new_value': 23681.38}, {'field': 'total_amount', 'old_value': 26881.38, 'new_value': 26831.38}]
2025-05-08 21:00:25,107 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-05-08 21:00:25,598 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-05-08 21:00:25,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 130981.0}, {'field': 'total_amount', 'old_value': 702860.0, 'new_value': 833841.0}]
2025-05-08 21:00:25,599 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-05-08 21:00:25,999 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-05-08 21:00:26,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41611.94, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 318058.03, 'new_value': 367320.8}, {'field': 'total_amount', 'old_value': 359669.97, 'new_value': 367320.8}, {'field': 'order_count', 'old_value': 10835, 'new_value': 30}]
2025-05-08 21:00:26,000 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-05-08 21:00:26,485 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-05-08 21:00:26,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 614547.17, 'new_value': 621405.77}, {'field': 'total_amount', 'old_value': 614547.17, 'new_value': 621405.77}, {'field': 'order_count', 'old_value': 6198, 'new_value': 30}]
2025-05-08 21:00:26,491 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-05-08 21:00:26,926 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-05-08 21:00:26,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186258.9, 'new_value': 202133.58}, {'field': 'total_amount', 'old_value': 186258.9, 'new_value': 202133.58}, {'field': 'order_count', 'old_value': 19836, 'new_value': 30}]
2025-05-08 21:00:26,930 - INFO - 日期 2025-04 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-05-08 21:00:26,930 - INFO - 开始处理日期: 2025-05
2025-05-08 21:00:26,930 - INFO - Request Parameters - Page 1:
2025-05-08 21:00:26,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:26,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:27,469 - INFO - Response - Page 1:
2025-05-08 21:00:27,669 - INFO - 第 1 页获取到 100 条记录
2025-05-08 21:00:27,669 - INFO - Request Parameters - Page 2:
2025-05-08 21:00:27,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:27,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:28,230 - INFO - Response - Page 2:
2025-05-08 21:00:28,431 - INFO - 第 2 页获取到 100 条记录
2025-05-08 21:00:28,431 - INFO - Request Parameters - Page 3:
2025-05-08 21:00:28,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:28,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:28,907 - INFO - Response - Page 3:
2025-05-08 21:00:29,107 - INFO - 第 3 页获取到 100 条记录
2025-05-08 21:00:29,107 - INFO - Request Parameters - Page 4:
2025-05-08 21:00:29,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:29,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:29,561 - INFO - Response - Page 4:
2025-05-08 21:00:29,763 - INFO - 第 4 页获取到 100 条记录
2025-05-08 21:00:29,763 - INFO - Request Parameters - Page 5:
2025-05-08 21:00:29,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:29,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:30,272 - INFO - Response - Page 5:
2025-05-08 21:00:30,473 - INFO - 第 5 页获取到 100 条记录
2025-05-08 21:00:30,473 - INFO - Request Parameters - Page 6:
2025-05-08 21:00:30,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:30,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:30,965 - INFO - Response - Page 6:
2025-05-08 21:00:31,165 - INFO - 第 6 页获取到 100 条记录
2025-05-08 21:00:31,165 - INFO - Request Parameters - Page 7:
2025-05-08 21:00:31,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-08 21:00:31,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-08 21:00:31,519 - INFO - Response - Page 7:
2025-05-08 21:00:31,720 - INFO - 第 7 页获取到 21 条记录
2025-05-08 21:00:31,720 - INFO - 查询完成，共获取到 621 条记录
2025-05-08 21:00:31,720 - INFO - 获取到 621 条表单数据
2025-05-08 21:00:31,731 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-08 21:00:31,731 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-08 21:00:32,181 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-08 21:00:32,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7373.83, 'new_value': 11496.13}, {'field': 'total_amount', 'old_value': 10458.44, 'new_value': 14580.74}, {'field': 'order_count', 'old_value': 353, 'new_value': 504}]
2025-05-08 21:00:32,182 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-08 21:00:32,669 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-08 21:00:32,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31507.42, 'new_value': 37482.28}, {'field': 'offline_amount', 'old_value': 3513.35, 'new_value': 4115.35}, {'field': 'total_amount', 'old_value': 35020.77, 'new_value': 41597.63}, {'field': 'order_count', 'old_value': 1172, 'new_value': 1393}]
2025-05-08 21:00:32,670 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-08 21:00:33,281 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-08 21:00:33,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4405.2, 'new_value': 5081.2}, {'field': 'offline_amount', 'old_value': 29228.3, 'new_value': 33372.9}, {'field': 'total_amount', 'old_value': 33633.5, 'new_value': 38454.1}, {'field': 'order_count', 'old_value': 76, 'new_value': 87}]
2025-05-08 21:00:33,283 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-08 21:00:33,680 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-08 21:00:33,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15706.05, 'new_value': 22118.14}, {'field': 'offline_amount', 'old_value': 19004.74, 'new_value': 23575.62}, {'field': 'total_amount', 'old_value': 34710.79, 'new_value': 45693.76}, {'field': 'order_count', 'old_value': 1011, 'new_value': 1430}]
2025-05-08 21:00:33,681 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-08 21:00:34,233 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-08 21:00:34,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44647.3, 'new_value': 56167.71}, {'field': 'total_amount', 'old_value': 44647.3, 'new_value': 56167.71}, {'field': 'order_count', 'old_value': 772, 'new_value': 1040}]
2025-05-08 21:00:34,233 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-08 21:00:34,722 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-08 21:00:34,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120959.42, 'new_value': 135720.45}, {'field': 'total_amount', 'old_value': 120959.42, 'new_value': 135720.45}, {'field': 'order_count', 'old_value': 1166, 'new_value': 1325}]
2025-05-08 21:00:34,723 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-08 21:00:35,206 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-08 21:00:35,206 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13214.99, 'new_value': 15083.75}, {'field': 'offline_amount', 'old_value': 58708.31, 'new_value': 66554.14}, {'field': 'total_amount', 'old_value': 71923.3, 'new_value': 81637.89}, {'field': 'order_count', 'old_value': 2066, 'new_value': 2386}]
2025-05-08 21:00:35,208 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-08 21:00:35,642 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-08 21:00:35,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23405.07, 'new_value': 28707.48}, {'field': 'total_amount', 'old_value': 23405.07, 'new_value': 28707.48}, {'field': 'order_count', 'old_value': 649, 'new_value': 770}]
2025-05-08 21:00:35,642 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-08 21:00:36,127 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-08 21:00:36,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34858.72, 'new_value': 45924.72}, {'field': 'total_amount', 'old_value': 34858.72, 'new_value': 45924.72}, {'field': 'order_count', 'old_value': 1260, 'new_value': 1750}]
2025-05-08 21:00:36,128 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-08 21:00:36,613 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-08 21:00:36,613 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7682.46, 'new_value': 8788.5}, {'field': 'offline_amount', 'old_value': 18588.39, 'new_value': 20022.89}, {'field': 'total_amount', 'old_value': 26270.85, 'new_value': 28811.39}, {'field': 'order_count', 'old_value': 215, 'new_value': 240}]
2025-05-08 21:00:36,618 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-08 21:00:37,030 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-08 21:00:37,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 8.0}, {'field': 'offline_amount', 'old_value': 25974.01, 'new_value': 30819.01}, {'field': 'total_amount', 'old_value': 25974.01, 'new_value': 30827.01}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-08 21:00:37,033 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-08 21:00:37,500 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-08 21:00:37,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17183.0, 'new_value': 24467.0}, {'field': 'total_amount', 'old_value': 17183.0, 'new_value': 24467.0}, {'field': 'order_count', 'old_value': 1517, 'new_value': 2358}]
2025-05-08 21:00:37,501 - INFO - 日期 2025-05 处理完成 - 更新: 12 条，插入: 0 条，错误: 0 条
2025-05-08 21:00:37,501 - INFO - 数据同步完成！更新: 18 条，插入: 0 条，错误: 0 条
2025-05-08 21:00:37,504 - INFO - =================同步完成====================
