2025-06-12 08:00:03,354 - INFO - ==================================================
2025-06-12 08:00:03,354 - INFO - 程序启动 - 版本 v1.0.0
2025-06-12 08:00:03,354 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250612.log
2025-06-12 08:00:03,354 - INFO - ==================================================
2025-06-12 08:00:03,354 - INFO - 程序入口点: __main__
2025-06-12 08:00:03,354 - INFO - ==================================================
2025-06-12 08:00:03,354 - INFO - 程序启动 - 版本 v1.0.1
2025-06-12 08:00:03,354 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250612.log
2025-06-12 08:00:03,354 - INFO - ==================================================
2025-06-12 08:00:03,354 - INFO - MySQL数据库连接成功
2025-06-12 08:00:03,666 - INFO - MySQL数据库连接成功
2025-06-12 08:00:03,666 - INFO - sales_data表已存在，无需创建
2025-06-12 08:00:03,666 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-12 08:00:03,666 - INFO - DataSyncManager初始化完成
2025-06-12 08:00:03,666 - INFO - 开始更新店铺映射表...
2025-06-12 08:00:03,666 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-05 至 2025-06-11
2025-06-12 08:00:03,666 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:03,666 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:03,666 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D242338C5A94B698AD6B87114674D6C0'}
2025-06-12 08:00:05,119 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:05,119 - INFO - 日期 ******** 获取到 327 条店铺记录
2025-06-12 08:00:05,635 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:05,635 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:05,635 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CB4499176A6E8ECCF756EF33238871D4'}
2025-06-12 08:00:07,354 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:07,354 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-12 08:00:07,869 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:07,869 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:07,869 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DA45E7AC3E4D1599D2AA9157D3EC569A'}
2025-06-12 08:00:09,541 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:09,541 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-12 08:00:10,041 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:10,041 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:10,041 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FDAE3575FA5BEE1D592C46277439A163'}
2025-06-12 08:00:11,119 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:11,119 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-12 08:00:11,635 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:11,635 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:11,635 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4AC40BECD5ADE77B5465FF606D07C30E'}
2025-06-12 08:00:12,744 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:12,744 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-12 08:00:13,244 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:13,244 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:13,244 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0484A9B701212D222537331B4807C3E1'}
2025-06-12 08:00:14,244 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:14,244 - INFO - 日期 ******** 获取到 327 条店铺记录
2025-06-12 08:00:14,760 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:14,760 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:14,760 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9A64B805DBC27AF4330CEA8214DC9C9F'}
2025-06-12 08:00:15,931 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:15,931 - INFO - 日期 ******** 获取到 331 条店铺记录
2025-06-12 08:00:16,650 - INFO - 店铺映射表更新完成，总计: 331条，成功: 331条 (更新: 326条, 插入: 5条)
2025-06-12 08:00:16,650 - INFO - 店铺映射表更新完成
2025-06-12 08:00:16,650 - INFO - 未提供日期参数，使用默认值
2025-06-12 08:00:16,650 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-12 08:00:16,650 - INFO - 开始综合数据同步流程...
2025-06-12 08:00:16,650 - INFO - 当前错误日期列表为空
2025-06-12 08:00:16,650 - INFO - 正在获取数衍平台日销售数据...
2025-06-12 08:00:16,650 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-05 至 2025-06-11
2025-06-12 08:00:16,650 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:16,650 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:16,650 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '217D0A04EC67ABE3129BBF504844C70F'}
2025-06-12 08:00:17,556 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:17,556 - INFO - 日期 ******** 获取到 327 条店铺记录
2025-06-12 08:00:18,072 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:18,072 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:18,072 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '45617EAE58BD887C4BC00EBF46A509D2'}
2025-06-12 08:00:19,244 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:19,244 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-12 08:00:19,759 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:19,759 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:19,759 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E099F9CA5E0A49EB6DF52E1420594D13'}
2025-06-12 08:00:20,994 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:20,994 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-12 08:00:21,509 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:21,509 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:21,509 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '05345128ACCAF60EA79F371A16C0D0A0'}
2025-06-12 08:00:22,619 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:22,619 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-12 08:00:23,119 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:23,119 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:23,119 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5CC46498DF958ECD5290A2CD2F3ECF89'}
2025-06-12 08:00:24,181 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:24,181 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-12 08:00:24,712 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:24,712 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:24,712 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A10076108C6D25D195EAE9A38D0CEE38'}
2025-06-12 08:00:25,603 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:25,603 - INFO - 日期 ******** 获取到 327 条店铺记录
2025-06-12 08:00:26,119 - INFO - 查询日期 ******** 的店铺信息
2025-06-12 08:00:26,119 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:26,119 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F997864B198E38C1F32C226FC96298E9'}
2025-06-12 08:00:27,040 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:27,040 - INFO - 日期 ******** 获取到 331 条店铺记录
2025-06-12 08:00:27,744 - INFO - 店铺映射表更新完成，总计: 331条，成功: 331条 (更新: 331条, 插入: 0条)
2025-06-12 08:00:27,744 - INFO - 查询数衍平台数据，时间段为: 2025-04-12, 2025-06-11
2025-06-12 08:00:27,744 - INFO - 正在获取********至********的数据
2025-06-12 08:00:27,744 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:27,744 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '66210EED7C0EC9FCBFA3E306626815A7'}
2025-06-12 08:00:30,165 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:30,181 - INFO - 过滤后保留 440 条记录
2025-06-12 08:00:32,196 - INFO - 正在获取********至********的数据
2025-06-12 08:00:32,196 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:32,196 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '686D34072586E93FF5E5B7E1ED580B82'}
2025-06-12 08:00:34,400 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:34,415 - INFO - 过滤后保留 429 条记录
2025-06-12 08:00:36,431 - INFO - 正在获取********至********的数据
2025-06-12 08:00:36,431 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:36,431 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C402067A22B71A8F36E3DF69D575343B'}
2025-06-12 08:00:38,087 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:38,103 - INFO - 过滤后保留 429 条记录
2025-06-12 08:00:40,118 - INFO - 正在获取********至********的数据
2025-06-12 08:00:40,118 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:40,118 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3369CE9B603B5E1EA90FBEA357C63261'}
2025-06-12 08:00:41,696 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:41,696 - INFO - 过滤后保留 432 条记录
2025-06-12 08:00:43,712 - INFO - 正在获取********至********的数据
2025-06-12 08:00:43,712 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:43,712 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '20E417B3BEC9D877081105DA5B62FB3A'}
2025-06-12 08:00:45,305 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:45,305 - INFO - 过滤后保留 429 条记录
2025-06-12 08:00:47,321 - INFO - 正在获取********至********的数据
2025-06-12 08:00:47,321 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:47,321 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D116D3E40B5F41853D0A64EAE228E2E9'}
2025-06-12 08:00:48,915 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:48,915 - INFO - 过滤后保留 419 条记录
2025-06-12 08:00:50,930 - INFO - 正在获取********至********的数据
2025-06-12 08:00:50,930 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:50,930 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '578BEF71245C0AF67573BFFE8A2D0C1C'}
2025-06-12 08:00:52,461 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:52,461 - INFO - 过滤后保留 416 条记录
2025-06-12 08:00:54,477 - INFO - 正在获取********至********的数据
2025-06-12 08:00:54,477 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:54,477 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F2F39267952CB8AF540240F5E59319E3'}
2025-06-12 08:00:55,961 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:55,977 - INFO - 过滤后保留 434 条记录
2025-06-12 08:00:57,993 - INFO - 正在获取********至********的数据
2025-06-12 08:00:57,993 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:00:57,993 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '723B2BF9FD001A9CC480D91F64E6615F'}
2025-06-12 08:00:59,399 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:00:59,414 - INFO - 过滤后保留 424 条记录
2025-06-12 08:01:01,430 - INFO - 正在获取********至********的数据
2025-06-12 08:01:01,430 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:01,430 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3D0EB8B70FCB761392204AD6B0B935A6'}
2025-06-12 08:01:02,914 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:02,914 - INFO - 过滤后保留 433 条记录
2025-06-12 08:01:04,914 - INFO - 正在获取********至********的数据
2025-06-12 08:01:04,914 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:04,914 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5F94C436590D33D60551BD947828BEF6'}
2025-06-12 08:01:06,586 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:06,586 - INFO - 过滤后保留 424 条记录
2025-06-12 08:01:08,602 - INFO - 正在获取********至********的数据
2025-06-12 08:01:08,602 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:08,602 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0EE649DB2C6E3336D6F4BB3E3793E179'}
2025-06-12 08:01:10,195 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:10,195 - INFO - 过滤后保留 428 条记录
2025-06-12 08:01:12,226 - INFO - 正在获取********至********的数据
2025-06-12 08:01:12,226 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:12,226 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D316E2B77B8EF876B74B5A8B8FCE3DD7'}
2025-06-12 08:01:13,883 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:13,883 - INFO - 过滤后保留 411 条记录
2025-06-12 08:01:15,898 - INFO - 正在获取********至********的数据
2025-06-12 08:01:15,898 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:15,898 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '446D2CEE81596F8A7EB0C71F6482482A'}
2025-06-12 08:01:17,304 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:17,320 - INFO - 过滤后保留 423 条记录
2025-06-12 08:01:19,336 - INFO - 正在获取********至********的数据
2025-06-12 08:01:19,336 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:19,336 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B39FA5D4CC8696584B7BC856C9547DBE'}
2025-06-12 08:01:20,929 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:20,929 - INFO - 过滤后保留 436 条记录
2025-06-12 08:01:22,945 - INFO - 正在获取********至********的数据
2025-06-12 08:01:22,945 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:22,945 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1A1FC71DD1D9DBEC01A0767444B81B62'}
2025-06-12 08:01:24,429 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:24,445 - INFO - 过滤后保留 425 条记录
2025-06-12 08:01:26,460 - INFO - 正在获取********至********的数据
2025-06-12 08:01:26,460 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:26,460 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6C743218AA71304A72134AB39AC44F8C'}
2025-06-12 08:01:27,867 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:27,867 - INFO - 过滤后保留 417 条记录
2025-06-12 08:01:29,882 - INFO - 正在获取********至********的数据
2025-06-12 08:01:29,882 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:29,882 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '41B1BED8906CA5BDCAFE6307A32639F2'}
2025-06-12 08:01:31,366 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:31,366 - INFO - 过滤后保留 425 条记录
2025-06-12 08:01:33,382 - INFO - 正在获取********至********的数据
2025-06-12 08:01:33,382 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:33,382 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CCC41E6EE3D7601FBDE669778F3FD1B7'}
2025-06-12 08:01:34,866 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:34,882 - INFO - 过滤后保留 426 条记录
2025-06-12 08:01:36,898 - INFO - 正在获取********至********的数据
2025-06-12 08:01:36,898 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:36,898 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C9C6DD0981207D24B485CB4172B21F25'}
2025-06-12 08:01:38,257 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:38,257 - INFO - 过滤后保留 421 条记录
2025-06-12 08:01:40,272 - INFO - 正在获取********至********的数据
2025-06-12 08:01:40,272 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:40,272 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DC263D18C045E1F3719C9753FD2A7990'}
2025-06-12 08:01:41,772 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:41,788 - INFO - 过滤后保留 416 条记录
2025-06-12 08:01:43,804 - INFO - 正在获取********至********的数据
2025-06-12 08:01:43,804 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:43,804 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6FFE90DA77D4CACF7AD999B624912420'}
2025-06-12 08:01:45,272 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:45,272 - INFO - 过滤后保留 424 条记录
2025-06-12 08:01:47,288 - INFO - 正在获取********至********的数据
2025-06-12 08:01:47,288 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:47,288 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '98B0AC35BB539C820B4084991305CC3E'}
2025-06-12 08:01:48,772 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:48,772 - INFO - 过滤后保留 410 条记录
2025-06-12 08:01:50,788 - INFO - 正在获取********至********的数据
2025-06-12 08:01:50,788 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:50,788 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6040281F917FEC254E1DD22F51670FA9'}
2025-06-12 08:01:52,397 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:52,397 - INFO - 过滤后保留 414 条记录
2025-06-12 08:01:54,413 - INFO - 正在获取********至********的数据
2025-06-12 08:01:54,413 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:54,413 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A51347CD94A017EE12C9D873ED2C2FD7'}
2025-06-12 08:01:55,913 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:55,913 - INFO - 过滤后保留 417 条记录
2025-06-12 08:01:57,928 - INFO - 正在获取********至********的数据
2025-06-12 08:01:57,928 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:01:57,928 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8758BEF61039437134B83319037A604D'}
2025-06-12 08:01:59,366 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:01:59,366 - INFO - 过滤后保留 407 条记录
2025-06-12 08:02:01,381 - INFO - 正在获取********至********的数据
2025-06-12 08:02:01,381 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:02:01,381 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C7D034CCADDC7D14851B144F80C4970F'}
2025-06-12 08:02:02,647 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:02:02,647 - INFO - 过滤后保留 400 条记录
2025-06-12 08:02:04,647 - INFO - 正在获取********至********的数据
2025-06-12 08:02:04,647 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:02:04,647 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0DB57D96E6D314ED34439EE8BCDE2E1C'}
2025-06-12 08:02:05,990 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:02:05,990 - INFO - 过滤后保留 408 条记录
2025-06-12 08:02:07,990 - INFO - 正在获取********至********的数据
2025-06-12 08:02:07,990 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:02:07,990 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B32CB105338CBCDEC33BBB6C1F1761EA'}
2025-06-12 08:02:09,271 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:02:09,287 - INFO - 过滤后保留 408 条记录
2025-06-12 08:02:11,303 - INFO - 正在获取********至********的数据
2025-06-12 08:02:11,303 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:02:11,303 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A9E25F418309DCC707F9EF2D8ABC5188'}
2025-06-12 08:02:12,615 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:02:12,631 - INFO - 过滤后保留 401 条记录
2025-06-12 08:02:14,646 - INFO - 正在获取********至********的数据
2025-06-12 08:02:14,646 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-12 08:02:14,646 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5E41AE79E5F0C73F10995D3475721CD2'}
2025-06-12 08:02:15,490 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-12 08:02:15,490 - INFO - 过滤后保留 201 条记录
2025-06-12 08:02:17,506 - INFO - 开始保存数据到MySQL数据库，共 12827 条记录待处理
2025-06-12 08:02:17,740 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-04-12
2025-06-12 08:02:17,740 - INFO - 变更字段: recommend_amount: 10823.66 -> 40027.05, daily_bill_amount: 10823.66 -> 40027.05, amount: 2766 -> 2840, count: 32 -> 33, instore_amount: 1873.2 -> 1947.2, instore_count: 20 -> 21
2025-06-12 08:02:18,302 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=BF554F536BF14762AEB7110E7BD583B7, sale_time=2025-04-16
2025-06-12 08:02:18,302 - INFO - 变更字段: amount: 27542 -> 29068, count: 48 -> 49, instore_amount: 27542.52 -> 29068.52, instore_count: 48 -> 49
2025-06-12 08:02:19,302 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-04-20
2025-06-12 08:02:19,302 - INFO - 变更字段: amount: 2394 -> 3711, count: 31 -> 36, instore_amount: 1954.33 -> 3270.63, instore_count: 22 -> 27
2025-06-12 08:02:21,177 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-05-01
2025-06-12 08:02:21,177 - INFO - 变更字段: amount: 4397 -> 4465, count: 31 -> 32, instore_amount: 4134.36 -> 4203.06, instore_count: 27 -> 28
2025-06-12 08:02:21,240 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-01
2025-06-12 08:02:21,240 - INFO - 变更字段: amount: 52131 -> 88698, count: 1168 -> 1872, instore_amount: 49827.34 -> 86393.5, instore_count: 1123 -> 1827
2025-06-12 08:02:21,615 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-03
2025-06-12 08:02:21,615 - INFO - 变更字段: recommend_amount: 0.0 -> 47931.55, daily_bill_amount: 0.0 -> 47931.55, amount: 34246 -> 65126, count: 643 -> 1306, instore_amount: 32241.25 -> 63342.67, instore_count: 606 -> 1269
2025-06-12 08:02:21,615 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-02
2025-06-12 08:02:21,630 - INFO - 变更字段: amount: 48380 -> 82114, count: 976 -> 1623, instore_amount: 46340.32 -> 80073.76, instore_count: 931 -> 1578
2025-06-12 08:02:22,005 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-05
2025-06-12 08:02:22,005 - INFO - 变更字段: amount: 27012 -> 50069, count: 429 -> 962, instore_amount: 24985.65 -> 48042.93, instore_count: 393 -> 926
2025-06-12 08:02:22,005 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-04
2025-06-12 08:02:22,005 - INFO - 变更字段: amount: 43817 -> 73415, count: 739 -> 1335, instore_amount: 41991.19 -> 71588.92, instore_count: 700 -> 1296
2025-06-12 08:02:22,380 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-07
2025-06-12 08:02:22,380 - INFO - 变更字段: amount: 5182 -> 12523, count: 80 -> 236, instore_amount: 3655.68 -> 10997.23, instore_count: 51 -> 207
2025-06-12 08:02:22,380 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-06
2025-06-12 08:02:22,380 - INFO - 变更字段: recommend_amount: 0.0 -> 8932.97, daily_bill_amount: 0.0 -> 8932.97, amount: 3291 -> 11240, count: 63 -> 211, instore_amount: 2059.39 -> 10058.76, instore_count: 39 -> 187
2025-06-12 08:02:22,771 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-09
2025-06-12 08:02:22,771 - INFO - 变更字段: amount: 11000 -> 25987, count: 166 -> 435, instore_amount: 9894.1 -> 24880.51, instore_count: 139 -> 408
2025-06-12 08:02:22,771 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-08
2025-06-12 08:02:22,771 - INFO - 变更字段: amount: 13752 -> 22578, count: 192 -> 363, instore_amount: 12212.73 -> 21038.29, instore_count: 159 -> 330
2025-06-12 08:02:23,162 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-11
2025-06-12 08:02:23,162 - INFO - 变更字段: recommend_amount: 0.0 -> 47513.51, daily_bill_amount: 0.0 -> 47513.51, amount: 29100 -> 61608, count: 588 -> 1281, instore_amount: 27987.9 -> 60661.76, instore_count: 568 -> 1261
2025-06-12 08:02:23,162 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-10
2025-06-12 08:02:23,162 - INFO - 变更字段: recommend_amount: 46159.01 -> 46158.96, daily_bill_amount: 46159.01 -> 46158.96, amount: 31423 -> 60864, count: 621 -> 1247, instore_amount: 30200.6 -> 59642.35, instore_count: 601 -> 1227
2025-06-12 08:02:23,552 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-13
2025-06-12 08:02:23,552 - INFO - 变更字段: amount: 7350 -> 16327, count: 148 -> 331, instore_amount: 5985.93 -> 14963.34, instore_count: 126 -> 309
2025-06-12 08:02:23,552 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-12
2025-06-12 08:02:23,552 - INFO - 变更字段: recommend_amount: 0.0 -> 12762.89, daily_bill_amount: 0.0 -> 12762.89, amount: 5043 -> 15370, count: 93 -> 308, instore_amount: 5011.05 -> 15350.74, instore_count: 92 -> 307
2025-06-12 08:02:23,943 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-15
2025-06-12 08:02:23,943 - INFO - 变更字段: recommend_amount: 10941.65 -> 12839.7, daily_bill_amount: 10941.65 -> 12839.7, amount: 3378 -> 14477, count: 65 -> 256, instore_amount: 3378.6 -> 14477.98, instore_count: 65 -> 256
2025-06-12 08:02:23,943 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-14
2025-06-12 08:02:23,943 - INFO - 变更字段: amount: 5905 -> 17599, count: 92 -> 258, instore_amount: 5072.92 -> 16767.7, instore_count: 77 -> 243
2025-06-12 08:02:24,334 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-17
2025-06-12 08:02:24,334 - INFO - 变更字段: recommend_amount: 0.0 -> 48044.36, daily_bill_amount: 0.0 -> 48044.36, amount: 25575 -> 59563, count: 476 -> 1118, instore_amount: 25575.56 -> 59599.27, instore_count: 476 -> 1118
2025-06-12 08:02:24,334 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-16
2025-06-12 08:02:24,334 - INFO - 变更字段: amount: 7750 -> 20878, count: 184 -> 439, instore_amount: 7765.07 -> 20893.81, instore_count: 184 -> 439
2025-06-12 08:02:24,724 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-19
2025-06-12 08:02:24,724 - INFO - 变更字段: recommend_amount: 0.0 -> 11085.43, daily_bill_amount: 0.0 -> 11085.43, amount: 4279 -> 14649, count: 84 -> 274, instore_amount: 2095.87 -> 12505.0, instore_count: 45 -> 235
2025-06-12 08:02:24,724 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-18
2025-06-12 08:02:24,724 - INFO - 变更字段: amount: 24018 -> 52321, count: 467 -> 1091, instore_amount: 23941.48 -> 52244.92, instore_count: 466 -> 1090
2025-06-12 08:02:25,099 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-21
2025-06-12 08:02:25,099 - INFO - 变更字段: recommend_amount: 0.0 -> 13132.14, daily_bill_amount: 0.0 -> 13132.14, amount: 6056 -> 16408, count: 108 -> 311, instore_amount: 5230.24 -> 15893.45, instore_count: 87 -> 290
2025-06-12 08:02:25,099 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-20
2025-06-12 08:02:25,115 - INFO - 变更字段: amount: 9596 -> 23487, count: 158 -> 393, instore_amount: 7198.79 -> 21089.83, instore_count: 115 -> 350
2025-06-12 08:02:25,490 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-23
2025-06-12 08:02:25,490 - INFO - 变更字段: recommend_amount: 0.0 -> 16912.63, daily_bill_amount: 0.0 -> 16912.63, amount: 8802 -> 22117, count: 114 -> 376, instore_amount: 7448.92 -> 20764.1, instore_count: 87 -> 349
2025-06-12 08:02:25,490 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-22
2025-06-12 08:02:25,490 - INFO - 变更字段: amount: 2820 -> 13587, count: 44 -> 230, instore_amount: 1846.04 -> 12612.61, instore_count: 26 -> 212
2025-06-12 08:02:25,865 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-25
2025-06-12 08:02:25,865 - INFO - 变更字段: recommend_amount: 0.0 -> 40289.84, daily_bill_amount: 0.0 -> 40289.84, amount: 19175 -> 48948, count: 371 -> 966, instore_amount: 18414.88 -> 48255.77, instore_count: 357 -> 952
2025-06-12 08:02:25,865 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-24
2025-06-12 08:02:25,865 - INFO - 变更字段: amount: 25577 -> 58071, count: 513 -> 1154, instore_amount: 24523.67 -> 57017.19, instore_count: 491 -> 1132
2025-06-12 08:02:26,224 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-27
2025-06-12 08:02:26,224 - INFO - 变更字段: recommend_amount: 0.0 -> 11608.18, daily_bill_amount: 0.0 -> 11608.18, amount: 3196 -> 13652, count: 47 -> 263, instore_amount: 2386.33 -> 12841.96, instore_count: 32 -> 248
2025-06-12 08:02:26,240 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-26
2025-06-12 08:02:26,240 - INFO - 变更字段: amount: 1787 -> 13113, count: 45 -> 240, instore_amount: 999.85 -> 12325.53, instore_count: 33 -> 228
2025-06-12 08:02:26,615 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-29
2025-06-12 08:02:26,615 - INFO - 变更字段: recommend_amount: 0.0 -> 10818.74, daily_bill_amount: 0.0 -> 10818.74, amount: 4215 -> 13357, count: 83 -> 243, instore_amount: 3648.87 -> 12740.15, instore_count: 72 -> 231, online_amount: 566.57 -> 616.97, online_count: 11 -> 12
2025-06-12 08:02:26,615 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-28
2025-06-12 08:02:26,615 - INFO - 变更字段: recommend_amount: 0.0 -> 8945.11, daily_bill_amount: 0.0 -> 8945.11, amount: 4336 -> 11021, count: 65 -> 200, instore_amount: 3667.96 -> 10352.49, instore_count: 51 -> 186
2025-06-12 08:02:27,005 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-31
2025-06-12 08:02:27,005 - INFO - 变更字段: amount: 47185 -> 84746, count: 837 -> 1498, instore_amount: 45578.92 -> 83139.56, instore_count: 800 -> 1461
2025-06-12 08:02:27,005 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-30
2025-06-12 08:02:27,005 - INFO - 变更字段: recommend_amount: 0.0 -> 24490.49, daily_bill_amount: 0.0 -> 24490.49, amount: 12204 -> 30462, count: 223 -> 576, instore_amount: 12277.65 -> 30535.6, instore_count: 221 -> 574
2025-06-12 08:02:27,333 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-06-01
2025-06-12 08:02:27,333 - INFO - 变更字段: amount: 6861 -> 9073, count: 75 -> 96, instore_amount: 4973.2 -> 7185.42, instore_count: 52 -> 73
2025-06-12 08:02:27,333 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-02
2025-06-12 08:02:27,349 - INFO - 变更字段: recommend_amount: 11336.6 -> 11186.6, daily_bill_amount: 11336.6 -> 11186.6, amount: 10503 -> 10483, instore_amount: 10882.4 -> 10862.1
2025-06-12 08:02:27,349 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-01
2025-06-12 08:02:27,349 - INFO - 变更字段: recommend_amount: 21867.2 -> 21767.2, daily_bill_amount: 21867.2 -> 21767.2, amount: 21867 -> 21807, instore_amount: 21867.2 -> 21807.2
2025-06-12 08:02:27,396 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-02
2025-06-12 08:02:27,396 - INFO - 变更字段: amount: 24565 -> 49797, count: 453 -> 965, instore_amount: 23381.37 -> 48614.03, instore_count: 423 -> 935
2025-06-12 08:02:27,396 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-01
2025-06-12 08:02:27,396 - INFO - 变更字段: amount: 94039 -> 136441, count: 1549 -> 2356, instore_amount: 90254.3 -> 132656.95, instore_count: 1483 -> 2290
2025-06-12 08:02:27,708 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-04
2025-06-12 08:02:27,708 - INFO - 变更字段: amount: 6026 -> 6006, instore_amount: 6026.7 -> 6006.7
2025-06-12 08:02:27,724 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-03
2025-06-12 08:02:27,724 - INFO - 变更字段: amount: 5932 -> 5912, instore_amount: 6281.0 -> 6261.0
2025-06-12 08:02:27,755 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-04
2025-06-12 08:02:27,755 - INFO - 变更字段: amount: 3802 -> 15250, count: 72 -> 248, instore_amount: 2073.4 -> 13521.6, instore_count: 43 -> 219
2025-06-12 08:02:27,771 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-03
2025-06-12 08:02:27,771 - INFO - 变更字段: recommend_amount: 0.0 -> 9978.22, daily_bill_amount: 0.0 -> 9978.22, amount: 4235 -> 12913, count: 71 -> 240, instore_amount: 3076.64 -> 11763.24, instore_count: 45 -> 214
2025-06-12 08:02:28,146 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-06
2025-06-12 08:02:28,146 - INFO - 变更字段: amount: 7163 -> 22270, count: 191 -> 507, instore_amount: 5531.72 -> 20747.56, instore_count: 149 -> 465
2025-06-12 08:02:28,146 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-05
2025-06-12 08:02:28,146 - INFO - 变更字段: recommend_amount: 0.0 -> 12855.15, daily_bill_amount: 0.0 -> 12855.15, amount: 7926 -> 17129, count: 129 -> 303, instore_amount: 7423.42 -> 16657.8, instore_count: 121 -> 295
2025-06-12 08:02:28,333 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-06-07
2025-06-12 08:02:28,333 - INFO - 变更字段: recommend_amount: 2900.2 -> 3353.8, amount: 2900 -> 3353, count: 84 -> 90, instore_amount: 2731.0 -> 3145.6, instore_count: 79 -> 84, online_amount: 169.2 -> 208.2, online_count: 5 -> 6
2025-06-12 08:02:28,349 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTA575PTKJ7Q2OVBN4IS72001D34, sale_time=2025-06-08
2025-06-12 08:02:28,349 - INFO - 变更字段: recommend_amount: 3795.35 -> 3775.35, amount: 3795 -> 3775, online_amount: 415.51 -> 395.51
2025-06-12 08:02:28,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=101F34500A0D43DF833463DEFB95F423, sale_time=2025-06-08
2025-06-12 08:02:28,411 - INFO - 变更字段: recommend_amount: 0.0 -> 9114.63, daily_bill_amount: 0.0 -> 9114.63
2025-06-12 08:02:28,490 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-08
2025-06-12 08:02:28,490 - INFO - 变更字段: recommend_amount: 13679.1 -> 13659.1, daily_bill_amount: 13679.1 -> 13659.1, amount: 13678 -> 13668, instore_amount: 13678.6 -> 13668.6
2025-06-12 08:02:28,536 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-08
2025-06-12 08:02:28,536 - INFO - 变更字段: amount: 14784 -> 42318, count: 327 -> 952, instore_amount: 12937.72 -> 40574.29, instore_count: 286 -> 911
2025-06-12 08:02:28,536 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-07
2025-06-12 08:02:28,536 - INFO - 变更字段: recommend_amount: 0.0 -> 45886.46, daily_bill_amount: 0.0 -> 45886.46, amount: 26381 -> 59380, count: 502 -> 1154, instore_amount: 24829.61 -> 57828.03, instore_count: 469 -> 1121
2025-06-12 08:02:28,630 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-10
2025-06-12 08:02:28,630 - INFO - 变更字段: recommend_amount: 0.0 -> 3605.9, daily_bill_amount: 0.0 -> 3605.9
2025-06-12 08:02:28,646 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6692283A183D432BAE322E1032539CE8, sale_time=2025-06-10
2025-06-12 08:02:28,646 - INFO - 变更字段: daily_bill_amount: 0.0 -> 15572.0
2025-06-12 08:02:28,646 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-10
2025-06-12 08:02:28,646 - INFO - 变更字段: amount: 9 -> 46, count: 1 -> 2, instore_amount: 9.9 -> 46.65, instore_count: 1 -> 2
2025-06-12 08:02:28,661 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HVORJ88U7D2IL1AIB692RTFU8001185, sale_time=2025-06-10
2025-06-12 08:02:28,661 - INFO - 变更字段: amount: 5059 -> 5759, count: 47 -> 48, instore_amount: 4963.54 -> 5663.54, instore_count: 41 -> 42
2025-06-12 08:02:28,693 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-06-10
2025-06-12 08:02:28,693 - INFO - 变更字段: amount: 2353 -> 2354, count: 134 -> 135, instore_amount: 1327.9 -> 1328.91, instore_count: 71 -> 72
2025-06-12 08:02:28,708 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-06-10
2025-06-12 08:02:28,708 - INFO - 变更字段: amount: 975 -> 987, count: 35 -> 36, online_amount: 574.8 -> 586.6, online_count: 25 -> 26
2025-06-12 08:02:28,724 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-06-09
2025-06-12 08:02:28,724 - INFO - 变更字段: recommend_amount: 8916.3 -> 9078.3, amount: 8916 -> 9078, count: 278 -> 280, instore_amount: 8689.0 -> 8828.0, instore_count: 265 -> 266, online_amount: 628.0 -> 651.0, online_count: 13 -> 14
2025-06-12 08:02:28,740 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-10
2025-06-12 08:02:28,740 - INFO - 变更字段: recommend_amount: 0.0 -> 8647.0, daily_bill_amount: 0.0 -> 8647.0, amount: 124 -> 360, count: 1 -> 2, instore_amount: 124.5 -> 360.5, instore_count: 1 -> 2
2025-06-12 08:02:28,755 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-06-10
2025-06-12 08:02:28,755 - INFO - 变更字段: recommend_amount: 0.0 -> 4036.2, daily_bill_amount: 0.0 -> 4036.2
2025-06-12 08:02:28,755 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-10
2025-06-12 08:02:28,755 - INFO - 变更字段: recommend_amount: 2573.4 -> 2580.5, amount: 2573 -> 2580, count: 135 -> 136, online_amount: 2110.4 -> 2117.5, online_count: 113 -> 114
2025-06-12 08:02:28,771 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-10
2025-06-12 08:02:28,771 - INFO - 变更字段: recommend_amount: 2234.34 -> 2249.74, amount: 2234 -> 2249, instore_amount: 2246.82 -> 2262.22
2025-06-12 08:02:28,786 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-10
2025-06-12 08:02:28,786 - INFO - 变更字段: recommend_amount: 7034.37 -> 7077.97, amount: 7034 -> 7077, count: 145 -> 147, instore_amount: 5859.8 -> 5903.4, instore_count: 124 -> 126
2025-06-12 08:02:28,786 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-06-10
2025-06-12 08:02:28,786 - INFO - 变更字段: amount: 2451 -> 2564, count: 132 -> 134, online_amount: 2108.8 -> 2222.0, online_count: 96 -> 98
2025-06-12 08:02:28,786 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=101F34500A0D43DF833463DEFB95F423, sale_time=2025-06-10
2025-06-12 08:02:28,786 - INFO - 变更字段: recommend_amount: 0.0 -> 9103.78, daily_bill_amount: 0.0 -> 9103.78
2025-06-12 08:02:28,786 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=101F34500A0D43DF833463DEFB95F423, sale_time=2025-06-09
2025-06-12 08:02:28,786 - INFO - 变更字段: recommend_amount: 0.0 -> 8436.53, daily_bill_amount: 0.0 -> 8436.53
2025-06-12 08:02:28,802 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-06-10
2025-06-12 08:02:28,802 - INFO - 变更字段: amount: 1390 -> 2780, count: 1 -> 2, instore_amount: 1390.0 -> 2780.0, instore_count: 1 -> 2
2025-06-12 08:02:28,818 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEBHLLHVNF0I86N3H2U102001F98, sale_time=2025-06-09
2025-06-12 08:02:28,818 - INFO - 变更字段: amount: 3363 -> 3598, count: 9 -> 10, instore_amount: 3363.1 -> 3598.1, instore_count: 9 -> 10
2025-06-12 08:02:28,818 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-10
2025-06-12 08:02:28,818 - INFO - 变更字段: recommend_amount: 0.0 -> 16142.67, daily_bill_amount: 0.0 -> 16142.67
2025-06-12 08:02:28,818 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-06-10
2025-06-12 08:02:28,818 - INFO - 变更字段: amount: -2361 -> -2354, count: 11 -> 12, online_amount: 244.3 -> 251.02, online_count: 8 -> 9
2025-06-12 08:02:28,818 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-06-10
2025-06-12 08:02:28,818 - INFO - 变更字段: amount: 16250 -> 16529, count: 129 -> 131, instore_amount: 14693.73 -> 14972.53, instore_count: 79 -> 81
2025-06-12 08:02:28,818 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-10
2025-06-12 08:02:28,818 - INFO - 变更字段: amount: 24342 -> 24552, count: 204 -> 205, instore_amount: 11879.1 -> 12089.1, instore_count: 78 -> 79
2025-06-12 08:02:28,849 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-06-10
2025-06-12 08:02:28,849 - INFO - 变更字段: amount: 3425 -> 3461, count: 158 -> 160, instore_amount: 1485.01 -> 1498.01, instore_count: 96 -> 97, online_amount: 1975.4 -> 1998.4, online_count: 62 -> 63
2025-06-12 08:02:28,849 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-06-10
2025-06-12 08:02:28,849 - INFO - 变更字段: amount: 6514 -> 6546, count: 328 -> 330, online_amount: 4584.48 -> 4616.58, online_count: 209 -> 211
2025-06-12 08:02:28,849 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-06-10
2025-06-12 08:02:28,849 - INFO - 变更字段: recommend_amount: 0.0 -> 2463.84, daily_bill_amount: 0.0 -> 2463.84
2025-06-12 08:02:28,865 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJP082LC00I86N3H2U1JM001ESS, sale_time=2025-06-10
2025-06-12 08:02:28,865 - INFO - 变更字段: recommend_amount: 971.2 -> 1288.0, amount: 971 -> 1288, count: 4 -> 5, instore_amount: 971.2 -> 1288.0, instore_count: 4 -> 5
2025-06-12 08:02:28,880 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-06-09
2025-06-12 08:02:28,880 - INFO - 变更字段: recommend_amount: 10614.36 -> 13600.34, daily_bill_amount: 10614.36 -> 13600.34
2025-06-12 08:02:28,880 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ, sale_time=2025-06-10
2025-06-12 08:02:28,880 - INFO - 变更字段: recommend_amount: 506.0 -> 705.0, amount: 506 -> 705, count: 7 -> 8, instore_amount: 506.0 -> 705.0, instore_count: 7 -> 8
2025-06-12 08:02:28,880 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-10
2025-06-12 08:02:28,880 - INFO - 变更字段: amount: 3159 -> 3059, instore_amount: 3159.6 -> 3059.6
2025-06-12 08:02:28,880 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-09
2025-06-12 08:02:28,880 - INFO - 变更字段: recommend_amount: 7905.5 -> 7835.5, daily_bill_amount: 7905.5 -> 7835.5, amount: 7905 -> 5161, instore_amount: 7905.5 -> 5161.7
2025-06-12 08:02:28,896 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9G31FV3GL0I86N3H2U190001EI6, sale_time=2025-06-10
2025-06-12 08:02:28,896 - INFO - 变更字段: amount: 7398 -> 57092, count: 2 -> 6, instore_amount: 7398.0 -> 57092.0, instore_count: 2 -> 6
2025-06-12 08:02:28,896 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EG92S1SB0I86N3H2U188001EHE, sale_time=2025-06-10
2025-06-12 08:02:28,896 - INFO - 变更字段: recommend_amount: 0.0 -> 827.0, daily_bill_amount: 0.0 -> 827.0
2025-06-12 08:02:28,896 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-06-10
2025-06-12 08:02:28,896 - INFO - 变更字段: recommend_amount: 0.0 -> 3045.9, daily_bill_amount: 0.0 -> 3045.9
2025-06-12 08:02:28,911 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVBEGSM760I86N3H2U12H001EBN, sale_time=2025-06-10
2025-06-12 08:02:28,911 - INFO - 变更字段: recommend_amount: 53704.55 -> 62171.37, amount: 53704 -> 62171, count: 12 -> 27, instore_amount: 53704.55 -> 62171.37, instore_count: 12 -> 27
2025-06-12 08:02:28,911 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-06-10
2025-06-12 08:02:28,911 - INFO - 变更字段: amount: 3471 -> 3449, instore_amount: 1564.31 -> 1751.31, instore_count: 74 -> 75, online_amount: 2134.8 -> 1947.8, online_count: 40 -> 39
2025-06-12 08:02:28,911 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-10
2025-06-12 08:02:28,911 - INFO - 变更字段: amount: 3250 -> 3261, count: 193 -> 200, online_amount: 3142.62 -> 3153.84, online_count: 183 -> 190
2025-06-12 08:02:28,911 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-09
2025-06-12 08:02:28,911 - INFO - 变更字段: amount: 3344 -> 3345, count: 228 -> 229, online_amount: 3337.44 -> 3338.44, online_count: 220 -> 221
2025-06-12 08:02:28,911 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-06-10
2025-06-12 08:02:28,911 - INFO - 变更字段: amount: 5903 -> 5881
2025-06-12 08:02:28,927 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-10
2025-06-12 08:02:28,927 - INFO - 变更字段: instore_amount: 3691.98 -> 3700.48, instore_count: 233 -> 234, online_amount: 1441.4 -> 1432.9, online_count: 94 -> 93
2025-06-12 08:02:28,943 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-10
2025-06-12 08:02:28,943 - INFO - 变更字段: recommend_amount: 0.0 -> 12719.56, daily_bill_amount: 0.0 -> 12719.56, amount: 4765 -> 15758, count: 84 -> 284, instore_amount: 3653.8 -> 14667.66, instore_count: 68 -> 267, online_amount: 1111.74 -> 1130.44, online_count: 16 -> 17
2025-06-12 08:02:28,943 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-09
2025-06-12 08:02:28,943 - INFO - 变更字段: amount: 9068 -> 21723, count: 144 -> 357, instore_amount: 8437.2 -> 21091.97, instore_count: 131 -> 344
2025-06-12 08:02:28,943 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-06-10
2025-06-12 08:02:28,943 - INFO - 变更字段: daily_bill_amount: 14243.8 -> 20902.5
2025-06-12 08:02:28,943 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-06-10
2025-06-12 08:02:28,943 - INFO - 变更字段: amount: 3609 -> 3802, count: 72 -> 73, online_amount: 974.98 -> 1167.69, online_count: 17 -> 18
2025-06-12 08:02:28,943 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDMFCJQF4F0I86N3H2U1UC001E7I, sale_time=2025-06-10
2025-06-12 08:02:28,943 - INFO - 变更字段: recommend_amount: 3753.3 -> 3785.18, amount: 3753 -> 3785, count: 48 -> 49, online_amount: 2103.82 -> 2135.7, online_count: 29 -> 30
2025-06-12 08:02:28,958 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-06-10
2025-06-12 08:02:28,958 - INFO - 变更字段: recommend_amount: 9093.61 -> 10001.63, amount: 9093 -> 10001, count: 348 -> 405, online_amount: 9258.52 -> 10196.84, online_count: 348 -> 405
2025-06-12 08:02:28,958 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-06-09
2025-06-12 08:02:28,958 - INFO - 变更字段: recommend_amount: 7566.77 -> 7813.37, amount: 7566 -> 7813, count: 325 -> 326, online_amount: 7926.47 -> 8173.07, online_count: 325 -> 326
2025-06-12 08:02:28,958 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-06-10
2025-06-12 08:02:28,958 - INFO - 变更字段: amount: 13039 -> 13076, count: 112 -> 113, online_amount: 1672.0 -> 1709.0, online_count: 62 -> 63
2025-06-12 08:02:28,974 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MR50JEM3SR7Q2OVAE57DM4001Q85, sale_time=2025-06-10
2025-06-12 08:02:28,974 - INFO - 变更字段: amount: 9081 -> 9545, count: 102 -> 104, instore_amount: 2903.4 -> 3367.4, instore_count: 38 -> 40
2025-06-12 08:02:28,974 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-06-10
2025-06-12 08:02:28,974 - INFO - 变更字段: recommend_amount: 6066.07 -> 6109.47, amount: 6066 -> 6109, count: 327 -> 330, instore_amount: 2509.32 -> 2552.72, instore_count: 142 -> 145
2025-06-12 08:02:28,974 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-10
2025-06-12 08:02:28,974 - INFO - 变更字段: recommend_amount: 0.0 -> 2888.9, daily_bill_amount: 0.0 -> 2888.9, amount: 678 -> 714, count: 18 -> 19, instore_amount: 438.07 -> 473.97, instore_count: 12 -> 13
2025-06-12 08:02:28,990 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK, sale_time=2025-06-10
2025-06-12 08:02:28,990 - INFO - 变更字段: amount: 8264 -> 8559, count: 49 -> 50, instore_amount: 8245.7 -> 8540.7, instore_count: 44 -> 45
2025-06-12 08:02:29,005 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-06-10
2025-06-12 08:02:29,005 - INFO - 变更字段: amount: 22297 -> 24311, count: 95 -> 96, instore_amount: 21130.93 -> 23144.93, instore_count: 49 -> 50
2025-06-12 08:02:29,021 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-10
2025-06-12 08:02:29,021 - INFO - 变更字段: amount: 28372 -> 30247, count: 167 -> 170, instore_amount: 26631.55 -> 28506.55, instore_count: 139 -> 142
2025-06-12 08:02:29,052 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-10
2025-06-12 08:02:29,052 - INFO - 变更字段: amount: 1069 -> 1065
2025-06-12 08:02:29,302 - INFO - MySQL数据保存完成，统计信息：
2025-06-12 08:02:29,302 - INFO - - 总记录数: 12827
2025-06-12 08:02:29,318 - INFO - - 成功插入: 206
2025-06-12 08:02:29,318 - INFO - - 成功更新: 105
2025-06-12 08:02:29,318 - INFO - - 无需更新: 12516
2025-06-12 08:02:29,318 - INFO - - 处理失败: 0
2025-06-12 08:02:29,318 - INFO - 成功获取数衍平台数据，共 12827 条记录
2025-06-12 08:02:29,318 - INFO - 正在更新MySQL月度汇总数据...
2025-06-12 08:02:29,349 - INFO - 月度数据表清空完成
2025-06-12 08:02:29,661 - INFO - 月度汇总数据更新完成，处理了 1410 条汇总记录
2025-06-12 08:02:29,661 - INFO - 成功更新月度汇总数据，共 1410 条记录
2025-06-12 08:02:29,661 - INFO - 正在获取宜搭日销售表单数据...
2025-06-12 08:02:29,661 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-12 00:00:00 至 2025-06-11 23:59:59
2025-06-12 08:02:29,661 - INFO - 查询分段 1: 2025-04-12 至 2025-04-13
2025-06-12 08:02:29,661 - INFO - 查询日期范围: 2025-04-12 至 2025-04-13，使用分页查询，每页 100 条记录
2025-06-12 08:02:29,661 - INFO - Request Parameters - Page 1:
2025-06-12 08:02:29,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:29,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200661, 1744473600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:33,786 - INFO - API请求耗时: 4125ms
2025-06-12 08:02:33,786 - INFO - Response - Page 1
2025-06-12 08:02:33,786 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:02:34,286 - INFO - Request Parameters - Page 2:
2025-06-12 08:02:34,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:34,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200661, 1744473600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:40,317 - INFO - API请求耗时: 6031ms
2025-06-12 08:02:40,317 - INFO - Response - Page 2
2025-06-12 08:02:40,317 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:02:40,833 - INFO - Request Parameters - Page 3:
2025-06-12 08:02:40,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:40,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200661, 1744473600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:41,520 - INFO - API请求耗时: 687ms
2025-06-12 08:02:41,520 - INFO - Response - Page 3
2025-06-12 08:02:41,520 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:02:42,036 - INFO - Request Parameters - Page 4:
2025-06-12 08:02:42,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:42,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200661, 1744473600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:42,739 - INFO - API请求耗时: 703ms
2025-06-12 08:02:42,739 - INFO - Response - Page 4
2025-06-12 08:02:42,739 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:02:43,239 - INFO - Request Parameters - Page 5:
2025-06-12 08:02:43,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:43,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200661, 1744473600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:43,786 - INFO - API请求耗时: 547ms
2025-06-12 08:02:43,786 - INFO - Response - Page 5
2025-06-12 08:02:43,786 - INFO - 第 5 页获取到 38 条记录
2025-06-12 08:02:43,786 - INFO - 查询完成，共获取到 438 条记录
2025-06-12 08:02:43,786 - INFO - 分段 1 查询成功，获取到 438 条记录
2025-06-12 08:02:44,786 - INFO - 查询分段 2: 2025-04-14 至 2025-04-15
2025-06-12 08:02:44,786 - INFO - 查询日期范围: 2025-04-14 至 2025-04-15，使用分页查询，每页 100 条记录
2025-06-12 08:02:44,786 - INFO - Request Parameters - Page 1:
2025-06-12 08:02:44,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:44,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000661, 1744646400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:45,583 - INFO - API请求耗时: 797ms
2025-06-12 08:02:45,583 - INFO - Response - Page 1
2025-06-12 08:02:45,583 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:02:46,098 - INFO - Request Parameters - Page 2:
2025-06-12 08:02:46,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:46,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000661, 1744646400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:46,786 - INFO - API请求耗时: 687ms
2025-06-12 08:02:46,786 - INFO - Response - Page 2
2025-06-12 08:02:46,786 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:02:47,302 - INFO - Request Parameters - Page 3:
2025-06-12 08:02:47,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:47,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000661, 1744646400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:48,020 - INFO - API请求耗时: 719ms
2025-06-12 08:02:48,020 - INFO - Response - Page 3
2025-06-12 08:02:48,020 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:02:48,536 - INFO - Request Parameters - Page 4:
2025-06-12 08:02:48,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:48,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000661, 1744646400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:49,161 - INFO - API请求耗时: 625ms
2025-06-12 08:02:49,161 - INFO - Response - Page 4
2025-06-12 08:02:49,161 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:02:49,676 - INFO - Request Parameters - Page 5:
2025-06-12 08:02:49,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:49,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000661, 1744646400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:50,380 - INFO - API请求耗时: 703ms
2025-06-12 08:02:50,380 - INFO - Response - Page 5
2025-06-12 08:02:50,380 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:02:50,895 - INFO - Request Parameters - Page 6:
2025-06-12 08:02:50,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:50,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000661, 1744646400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:51,551 - INFO - API请求耗时: 656ms
2025-06-12 08:02:51,551 - INFO - Response - Page 6
2025-06-12 08:02:51,551 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:02:52,051 - INFO - Request Parameters - Page 7:
2025-06-12 08:02:52,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:52,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000661, 1744646400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:52,676 - INFO - API请求耗时: 625ms
2025-06-12 08:02:52,676 - INFO - Response - Page 7
2025-06-12 08:02:52,676 - INFO - 第 7 页获取到 42 条记录
2025-06-12 08:02:52,676 - INFO - 查询完成，共获取到 642 条记录
2025-06-12 08:02:52,676 - INFO - 分段 2 查询成功，获取到 642 条记录
2025-06-12 08:02:53,676 - INFO - 查询分段 3: 2025-04-16 至 2025-04-17
2025-06-12 08:02:53,676 - INFO - 查询日期范围: 2025-04-16 至 2025-04-17，使用分页查询，每页 100 条记录
2025-06-12 08:02:53,676 - INFO - Request Parameters - Page 1:
2025-06-12 08:02:53,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:53,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800661, 1744819200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:54,504 - INFO - API请求耗时: 828ms
2025-06-12 08:02:54,504 - INFO - Response - Page 1
2025-06-12 08:02:54,504 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:02:55,036 - INFO - Request Parameters - Page 2:
2025-06-12 08:02:55,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:55,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800661, 1744819200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:55,707 - INFO - API请求耗时: 672ms
2025-06-12 08:02:55,707 - INFO - Response - Page 2
2025-06-12 08:02:55,707 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:02:56,207 - INFO - Request Parameters - Page 3:
2025-06-12 08:02:56,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:56,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800661, 1744819200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:56,911 - INFO - API请求耗时: 703ms
2025-06-12 08:02:56,911 - INFO - Response - Page 3
2025-06-12 08:02:56,911 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:02:57,411 - INFO - Request Parameters - Page 4:
2025-06-12 08:02:57,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:57,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800661, 1744819200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:58,082 - INFO - API请求耗时: 672ms
2025-06-12 08:02:58,082 - INFO - Response - Page 4
2025-06-12 08:02:58,082 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:02:58,582 - INFO - Request Parameters - Page 5:
2025-06-12 08:02:58,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:58,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800661, 1744819200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:02:59,348 - INFO - API请求耗时: 766ms
2025-06-12 08:02:59,348 - INFO - Response - Page 5
2025-06-12 08:02:59,348 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:02:59,848 - INFO - Request Parameters - Page 6:
2025-06-12 08:02:59,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:02:59,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800661, 1744819200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:00,598 - INFO - API请求耗时: 750ms
2025-06-12 08:03:00,598 - INFO - Response - Page 6
2025-06-12 08:03:00,598 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:03:01,114 - INFO - Request Parameters - Page 7:
2025-06-12 08:03:01,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:01,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800661, 1744819200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:01,754 - INFO - API请求耗时: 641ms
2025-06-12 08:03:01,754 - INFO - Response - Page 7
2025-06-12 08:03:01,754 - INFO - 第 7 页获取到 57 条记录
2025-06-12 08:03:01,754 - INFO - 查询完成，共获取到 657 条记录
2025-06-12 08:03:01,754 - INFO - 分段 3 查询成功，获取到 657 条记录
2025-06-12 08:03:02,770 - INFO - 查询分段 4: 2025-04-18 至 2025-04-19
2025-06-12 08:03:02,770 - INFO - 查询日期范围: 2025-04-18 至 2025-04-19，使用分页查询，每页 100 条记录
2025-06-12 08:03:02,770 - INFO - Request Parameters - Page 1:
2025-06-12 08:03:02,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:02,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600661, 1744992000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:03,426 - INFO - API请求耗时: 656ms
2025-06-12 08:03:03,426 - INFO - Response - Page 1
2025-06-12 08:03:03,426 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:03:03,942 - INFO - Request Parameters - Page 2:
2025-06-12 08:03:03,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:03,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600661, 1744992000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:04,660 - INFO - API请求耗时: 719ms
2025-06-12 08:03:04,660 - INFO - Response - Page 2
2025-06-12 08:03:04,660 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:03:05,160 - INFO - Request Parameters - Page 3:
2025-06-12 08:03:05,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:05,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600661, 1744992000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:05,863 - INFO - API请求耗时: 703ms
2025-06-12 08:03:05,863 - INFO - Response - Page 3
2025-06-12 08:03:05,863 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:03:06,379 - INFO - Request Parameters - Page 4:
2025-06-12 08:03:06,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:06,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600661, 1744992000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:07,160 - INFO - API请求耗时: 781ms
2025-06-12 08:03:07,160 - INFO - Response - Page 4
2025-06-12 08:03:07,160 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:03:07,676 - INFO - Request Parameters - Page 5:
2025-06-12 08:03:07,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:07,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600661, 1744992000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:08,348 - INFO - API请求耗时: 672ms
2025-06-12 08:03:08,348 - INFO - Response - Page 5
2025-06-12 08:03:08,348 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:03:08,848 - INFO - Request Parameters - Page 6:
2025-06-12 08:03:08,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:08,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600661, 1744992000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:09,488 - INFO - API请求耗时: 641ms
2025-06-12 08:03:09,488 - INFO - Response - Page 6
2025-06-12 08:03:09,488 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:03:10,004 - INFO - Request Parameters - Page 7:
2025-06-12 08:03:10,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:10,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600661, 1744992000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:10,566 - INFO - API请求耗时: 562ms
2025-06-12 08:03:10,566 - INFO - Response - Page 7
2025-06-12 08:03:10,566 - INFO - 第 7 页获取到 45 条记录
2025-06-12 08:03:10,566 - INFO - 查询完成，共获取到 645 条记录
2025-06-12 08:03:10,566 - INFO - 分段 4 查询成功，获取到 645 条记录
2025-06-12 08:03:11,566 - INFO - 查询分段 5: 2025-04-20 至 2025-04-21
2025-06-12 08:03:11,566 - INFO - 查询日期范围: 2025-04-20 至 2025-04-21，使用分页查询，每页 100 条记录
2025-06-12 08:03:11,566 - INFO - Request Parameters - Page 1:
2025-06-12 08:03:11,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:11,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400661, 1745164800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:12,238 - INFO - API请求耗时: 672ms
2025-06-12 08:03:12,238 - INFO - Response - Page 1
2025-06-12 08:03:12,238 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:03:12,754 - INFO - Request Parameters - Page 2:
2025-06-12 08:03:12,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:12,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400661, 1745164800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:13,394 - INFO - API请求耗时: 641ms
2025-06-12 08:03:13,394 - INFO - Response - Page 2
2025-06-12 08:03:13,394 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:03:13,910 - INFO - Request Parameters - Page 3:
2025-06-12 08:03:13,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:13,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400661, 1745164800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:14,660 - INFO - API请求耗时: 750ms
2025-06-12 08:03:14,660 - INFO - Response - Page 3
2025-06-12 08:03:14,660 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:03:15,160 - INFO - Request Parameters - Page 4:
2025-06-12 08:03:15,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:15,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400661, 1745164800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:15,879 - INFO - API请求耗时: 719ms
2025-06-12 08:03:15,894 - INFO - Response - Page 4
2025-06-12 08:03:15,894 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:03:16,410 - INFO - Request Parameters - Page 5:
2025-06-12 08:03:16,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:16,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400661, 1745164800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:17,129 - INFO - API请求耗时: 719ms
2025-06-12 08:03:17,129 - INFO - Response - Page 5
2025-06-12 08:03:17,129 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:03:17,629 - INFO - Request Parameters - Page 6:
2025-06-12 08:03:17,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:17,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400661, 1745164800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:18,519 - INFO - API请求耗时: 891ms
2025-06-12 08:03:18,519 - INFO - Response - Page 6
2025-06-12 08:03:18,519 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:03:19,019 - INFO - Request Parameters - Page 7:
2025-06-12 08:03:19,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:19,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400661, 1745164800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:19,550 - INFO - API请求耗时: 531ms
2025-06-12 08:03:19,550 - INFO - Response - Page 7
2025-06-12 08:03:19,550 - INFO - 第 7 页获取到 39 条记录
2025-06-12 08:03:19,550 - INFO - 查询完成，共获取到 639 条记录
2025-06-12 08:03:19,550 - INFO - 分段 5 查询成功，获取到 639 条记录
2025-06-12 08:03:20,566 - INFO - 查询分段 6: 2025-04-22 至 2025-04-23
2025-06-12 08:03:20,566 - INFO - 查询日期范围: 2025-04-22 至 2025-04-23，使用分页查询，每页 100 条记录
2025-06-12 08:03:20,566 - INFO - Request Parameters - Page 1:
2025-06-12 08:03:20,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:20,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200661, 1745337600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:21,269 - INFO - API请求耗时: 703ms
2025-06-12 08:03:21,269 - INFO - Response - Page 1
2025-06-12 08:03:21,269 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:03:21,785 - INFO - Request Parameters - Page 2:
2025-06-12 08:03:21,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:21,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200661, 1745337600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:22,519 - INFO - API请求耗时: 734ms
2025-06-12 08:03:22,519 - INFO - Response - Page 2
2025-06-12 08:03:22,519 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:03:23,035 - INFO - Request Parameters - Page 3:
2025-06-12 08:03:23,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:23,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200661, 1745337600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:23,644 - INFO - API请求耗时: 609ms
2025-06-12 08:03:23,644 - INFO - Response - Page 3
2025-06-12 08:03:23,644 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:03:24,144 - INFO - Request Parameters - Page 4:
2025-06-12 08:03:24,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:24,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200661, 1745337600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:24,863 - INFO - API请求耗时: 719ms
2025-06-12 08:03:24,863 - INFO - Response - Page 4
2025-06-12 08:03:24,863 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:03:25,363 - INFO - Request Parameters - Page 5:
2025-06-12 08:03:25,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:25,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200661, 1745337600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:26,160 - INFO - API请求耗时: 797ms
2025-06-12 08:03:26,160 - INFO - Response - Page 5
2025-06-12 08:03:26,160 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:03:26,675 - INFO - Request Parameters - Page 6:
2025-06-12 08:03:26,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:26,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200661, 1745337600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:27,441 - INFO - API请求耗时: 766ms
2025-06-12 08:03:27,441 - INFO - Response - Page 6
2025-06-12 08:03:27,441 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:03:27,956 - INFO - Request Parameters - Page 7:
2025-06-12 08:03:27,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:27,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200661, 1745337600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:28,503 - INFO - API请求耗时: 547ms
2025-06-12 08:03:28,503 - INFO - Response - Page 7
2025-06-12 08:03:28,503 - INFO - 第 7 页获取到 21 条记录
2025-06-12 08:03:28,503 - INFO - 查询完成，共获取到 621 条记录
2025-06-12 08:03:28,503 - INFO - 分段 6 查询成功，获取到 621 条记录
2025-06-12 08:03:29,519 - INFO - 查询分段 7: 2025-04-24 至 2025-04-25
2025-06-12 08:03:29,519 - INFO - 查询日期范围: 2025-04-24 至 2025-04-25，使用分页查询，每页 100 条记录
2025-06-12 08:03:29,519 - INFO - Request Parameters - Page 1:
2025-06-12 08:03:29,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:29,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000661, 1745510400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:30,160 - INFO - API请求耗时: 641ms
2025-06-12 08:03:30,160 - INFO - Response - Page 1
2025-06-12 08:03:30,160 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:03:30,675 - INFO - Request Parameters - Page 2:
2025-06-12 08:03:30,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:30,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000661, 1745510400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:31,441 - INFO - API请求耗时: 766ms
2025-06-12 08:03:31,441 - INFO - Response - Page 2
2025-06-12 08:03:31,441 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:03:31,956 - INFO - Request Parameters - Page 3:
2025-06-12 08:03:31,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:31,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000661, 1745510400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:32,613 - INFO - API请求耗时: 656ms
2025-06-12 08:03:32,613 - INFO - Response - Page 3
2025-06-12 08:03:32,613 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:03:33,128 - INFO - Request Parameters - Page 4:
2025-06-12 08:03:33,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:33,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000661, 1745510400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:33,784 - INFO - API请求耗时: 656ms
2025-06-12 08:03:33,800 - INFO - Response - Page 4
2025-06-12 08:03:33,800 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:03:34,316 - INFO - Request Parameters - Page 5:
2025-06-12 08:03:34,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:34,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000661, 1745510400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:35,097 - INFO - API请求耗时: 781ms
2025-06-12 08:03:35,097 - INFO - Response - Page 5
2025-06-12 08:03:35,097 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:03:35,612 - INFO - Request Parameters - Page 6:
2025-06-12 08:03:35,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:35,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000661, 1745510400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:36,253 - INFO - API请求耗时: 641ms
2025-06-12 08:03:36,253 - INFO - Response - Page 6
2025-06-12 08:03:36,253 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:03:36,753 - INFO - Request Parameters - Page 7:
2025-06-12 08:03:36,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:36,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000661, 1745510400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:37,300 - INFO - API请求耗时: 547ms
2025-06-12 08:03:37,300 - INFO - Response - Page 7
2025-06-12 08:03:37,300 - INFO - 第 7 页获取到 27 条记录
2025-06-12 08:03:37,300 - INFO - 查询完成，共获取到 627 条记录
2025-06-12 08:03:37,300 - INFO - 分段 7 查询成功，获取到 627 条记录
2025-06-12 08:03:38,300 - INFO - 查询分段 8: 2025-04-26 至 2025-04-27
2025-06-12 08:03:38,300 - INFO - 查询日期范围: 2025-04-26 至 2025-04-27，使用分页查询，每页 100 条记录
2025-06-12 08:03:38,300 - INFO - Request Parameters - Page 1:
2025-06-12 08:03:38,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:38,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800661, 1745683200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:39,065 - INFO - API请求耗时: 766ms
2025-06-12 08:03:39,065 - INFO - Response - Page 1
2025-06-12 08:03:39,065 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:03:39,581 - INFO - Request Parameters - Page 2:
2025-06-12 08:03:39,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:39,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800661, 1745683200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:40,315 - INFO - API请求耗时: 734ms
2025-06-12 08:03:40,315 - INFO - Response - Page 2
2025-06-12 08:03:40,315 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:03:40,815 - INFO - Request Parameters - Page 3:
2025-06-12 08:03:40,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:40,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800661, 1745683200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:41,534 - INFO - API请求耗时: 719ms
2025-06-12 08:03:41,534 - INFO - Response - Page 3
2025-06-12 08:03:41,534 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:03:42,050 - INFO - Request Parameters - Page 4:
2025-06-12 08:03:42,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:42,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800661, 1745683200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:42,722 - INFO - API请求耗时: 672ms
2025-06-12 08:03:42,722 - INFO - Response - Page 4
2025-06-12 08:03:42,722 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:03:43,222 - INFO - Request Parameters - Page 5:
2025-06-12 08:03:43,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:43,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800661, 1745683200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:43,847 - INFO - API请求耗时: 625ms
2025-06-12 08:03:43,847 - INFO - Response - Page 5
2025-06-12 08:03:43,847 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:03:44,347 - INFO - Request Parameters - Page 6:
2025-06-12 08:03:44,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:44,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800661, 1745683200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:45,065 - INFO - API请求耗时: 719ms
2025-06-12 08:03:45,065 - INFO - Response - Page 6
2025-06-12 08:03:45,065 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:03:45,565 - INFO - Request Parameters - Page 7:
2025-06-12 08:03:45,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:45,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800661, 1745683200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:46,143 - INFO - API请求耗时: 578ms
2025-06-12 08:03:46,143 - INFO - Response - Page 7
2025-06-12 08:03:46,143 - INFO - 第 7 页获取到 48 条记录
2025-06-12 08:03:46,143 - INFO - 查询完成，共获取到 648 条记录
2025-06-12 08:03:46,143 - INFO - 分段 8 查询成功，获取到 648 条记录
2025-06-12 08:03:47,159 - INFO - 查询分段 9: 2025-04-28 至 2025-04-29
2025-06-12 08:03:47,159 - INFO - 查询日期范围: 2025-04-28 至 2025-04-29，使用分页查询，每页 100 条记录
2025-06-12 08:03:47,159 - INFO - Request Parameters - Page 1:
2025-06-12 08:03:47,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:47,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600661, 1745856000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:47,846 - INFO - API请求耗时: 687ms
2025-06-12 08:03:47,846 - INFO - Response - Page 1
2025-06-12 08:03:47,846 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:03:48,362 - INFO - Request Parameters - Page 2:
2025-06-12 08:03:48,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:48,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600661, 1745856000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:49,003 - INFO - API请求耗时: 641ms
2025-06-12 08:03:49,003 - INFO - Response - Page 2
2025-06-12 08:03:49,003 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:03:49,518 - INFO - Request Parameters - Page 3:
2025-06-12 08:03:49,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:49,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600661, 1745856000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:50,221 - INFO - API请求耗时: 703ms
2025-06-12 08:03:50,221 - INFO - Response - Page 3
2025-06-12 08:03:50,221 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:03:50,737 - INFO - Request Parameters - Page 4:
2025-06-12 08:03:50,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:50,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600661, 1745856000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:51,440 - INFO - API请求耗时: 703ms
2025-06-12 08:03:51,440 - INFO - Response - Page 4
2025-06-12 08:03:51,440 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:03:51,940 - INFO - Request Parameters - Page 5:
2025-06-12 08:03:51,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:51,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600661, 1745856000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:52,690 - INFO - API请求耗时: 750ms
2025-06-12 08:03:52,690 - INFO - Response - Page 5
2025-06-12 08:03:52,690 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:03:53,206 - INFO - Request Parameters - Page 6:
2025-06-12 08:03:53,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:53,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600661, 1745856000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:53,862 - INFO - API请求耗时: 656ms
2025-06-12 08:03:53,862 - INFO - Response - Page 6
2025-06-12 08:03:53,862 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:03:54,377 - INFO - Request Parameters - Page 7:
2025-06-12 08:03:54,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:54,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600661, 1745856000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:54,893 - INFO - API请求耗时: 516ms
2025-06-12 08:03:54,893 - INFO - Response - Page 7
2025-06-12 08:03:54,893 - INFO - 第 7 页获取到 36 条记录
2025-06-12 08:03:54,893 - INFO - 查询完成，共获取到 636 条记录
2025-06-12 08:03:54,893 - INFO - 分段 9 查询成功，获取到 636 条记录
2025-06-12 08:03:55,893 - INFO - 查询分段 10: 2025-04-30 至 2025-05-01
2025-06-12 08:03:55,893 - INFO - 查询日期范围: 2025-04-30 至 2025-05-01，使用分页查询，每页 100 条记录
2025-06-12 08:03:55,893 - INFO - Request Parameters - Page 1:
2025-06-12 08:03:55,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:55,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400661, 1746028800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:56,565 - INFO - API请求耗时: 672ms
2025-06-12 08:03:56,565 - INFO - Response - Page 1
2025-06-12 08:03:56,565 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:03:57,065 - INFO - Request Parameters - Page 2:
2025-06-12 08:03:57,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:57,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400661, 1746028800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:57,737 - INFO - API请求耗时: 672ms
2025-06-12 08:03:57,737 - INFO - Response - Page 2
2025-06-12 08:03:57,737 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:03:58,237 - INFO - Request Parameters - Page 3:
2025-06-12 08:03:58,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:58,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400661, 1746028800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:03:58,877 - INFO - API请求耗时: 641ms
2025-06-12 08:03:58,877 - INFO - Response - Page 3
2025-06-12 08:03:58,877 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:03:59,393 - INFO - Request Parameters - Page 4:
2025-06-12 08:03:59,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:03:59,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400661, 1746028800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:00,096 - INFO - API请求耗时: 703ms
2025-06-12 08:04:00,096 - INFO - Response - Page 4
2025-06-12 08:04:00,096 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:04:00,612 - INFO - Request Parameters - Page 5:
2025-06-12 08:04:00,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:00,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400661, 1746028800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:01,299 - INFO - API请求耗时: 687ms
2025-06-12 08:04:01,315 - INFO - Response - Page 5
2025-06-12 08:04:01,315 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:04:01,830 - INFO - Request Parameters - Page 6:
2025-06-12 08:04:01,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:01,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400661, 1746028800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:02,627 - INFO - API请求耗时: 797ms
2025-06-12 08:04:02,627 - INFO - Response - Page 6
2025-06-12 08:04:02,627 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:04:03,127 - INFO - Request Parameters - Page 7:
2025-06-12 08:04:03,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:03,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400661, 1746028800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:03,752 - INFO - API请求耗时: 625ms
2025-06-12 08:04:03,752 - INFO - Response - Page 7
2025-06-12 08:04:03,752 - INFO - 第 7 页获取到 48 条记录
2025-06-12 08:04:03,752 - INFO - 查询完成，共获取到 648 条记录
2025-06-12 08:04:03,752 - INFO - 分段 10 查询成功，获取到 648 条记录
2025-06-12 08:04:04,752 - INFO - 查询分段 11: 2025-05-02 至 2025-05-03
2025-06-12 08:04:04,752 - INFO - 查询日期范围: 2025-05-02 至 2025-05-03，使用分页查询，每页 100 条记录
2025-06-12 08:04:04,752 - INFO - Request Parameters - Page 1:
2025-06-12 08:04:04,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:04,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200661, 1746201600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:05,486 - INFO - API请求耗时: 734ms
2025-06-12 08:04:05,486 - INFO - Response - Page 1
2025-06-12 08:04:05,486 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:04:05,987 - INFO - Request Parameters - Page 2:
2025-06-12 08:04:05,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:05,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200661, 1746201600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:06,705 - INFO - API请求耗时: 719ms
2025-06-12 08:04:06,705 - INFO - Response - Page 2
2025-06-12 08:04:06,721 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:04:07,221 - INFO - Request Parameters - Page 3:
2025-06-12 08:04:07,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:07,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200661, 1746201600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:07,971 - INFO - API请求耗时: 750ms
2025-06-12 08:04:07,971 - INFO - Response - Page 3
2025-06-12 08:04:07,971 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:04:08,471 - INFO - Request Parameters - Page 4:
2025-06-12 08:04:08,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:08,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200661, 1746201600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:09,080 - INFO - API请求耗时: 609ms
2025-06-12 08:04:09,096 - INFO - Response - Page 4
2025-06-12 08:04:09,096 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:04:09,596 - INFO - Request Parameters - Page 5:
2025-06-12 08:04:09,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:09,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200661, 1746201600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:10,299 - INFO - API请求耗时: 703ms
2025-06-12 08:04:10,299 - INFO - Response - Page 5
2025-06-12 08:04:10,314 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:04:10,830 - INFO - Request Parameters - Page 6:
2025-06-12 08:04:10,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:10,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200661, 1746201600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:11,549 - INFO - API请求耗时: 719ms
2025-06-12 08:04:11,549 - INFO - Response - Page 6
2025-06-12 08:04:11,549 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:04:12,049 - INFO - Request Parameters - Page 7:
2025-06-12 08:04:12,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:12,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200661, 1746201600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:12,580 - INFO - API请求耗时: 531ms
2025-06-12 08:04:12,580 - INFO - Response - Page 7
2025-06-12 08:04:12,580 - INFO - 第 7 页获取到 39 条记录
2025-06-12 08:04:12,580 - INFO - 查询完成，共获取到 639 条记录
2025-06-12 08:04:12,580 - INFO - 分段 11 查询成功，获取到 639 条记录
2025-06-12 08:04:13,596 - INFO - 查询分段 12: 2025-05-04 至 2025-05-05
2025-06-12 08:04:13,596 - INFO - 查询日期范围: 2025-05-04 至 2025-05-05，使用分页查询，每页 100 条记录
2025-06-12 08:04:13,596 - INFO - Request Parameters - Page 1:
2025-06-12 08:04:13,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:13,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000661, 1746374400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:14,346 - INFO - API请求耗时: 750ms
2025-06-12 08:04:14,346 - INFO - Response - Page 1
2025-06-12 08:04:14,346 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:04:14,861 - INFO - Request Parameters - Page 2:
2025-06-12 08:04:14,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:14,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000661, 1746374400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:15,517 - INFO - API请求耗时: 656ms
2025-06-12 08:04:15,517 - INFO - Response - Page 2
2025-06-12 08:04:15,517 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:04:16,033 - INFO - Request Parameters - Page 3:
2025-06-12 08:04:16,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:16,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000661, 1746374400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:16,752 - INFO - API请求耗时: 719ms
2025-06-12 08:04:16,752 - INFO - Response - Page 3
2025-06-12 08:04:16,752 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:04:17,267 - INFO - Request Parameters - Page 4:
2025-06-12 08:04:17,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:17,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000661, 1746374400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:17,970 - INFO - API请求耗时: 703ms
2025-06-12 08:04:17,970 - INFO - Response - Page 4
2025-06-12 08:04:17,986 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:04:18,502 - INFO - Request Parameters - Page 5:
2025-06-12 08:04:18,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:18,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000661, 1746374400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:19,142 - INFO - API请求耗时: 641ms
2025-06-12 08:04:19,142 - INFO - Response - Page 5
2025-06-12 08:04:19,142 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:04:19,658 - INFO - Request Parameters - Page 6:
2025-06-12 08:04:19,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:19,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000661, 1746374400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:20,486 - INFO - API请求耗时: 828ms
2025-06-12 08:04:20,486 - INFO - Response - Page 6
2025-06-12 08:04:20,486 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:04:21,002 - INFO - Request Parameters - Page 7:
2025-06-12 08:04:21,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:21,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000661, 1746374400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:21,564 - INFO - API请求耗时: 562ms
2025-06-12 08:04:21,564 - INFO - Response - Page 7
2025-06-12 08:04:21,564 - INFO - 第 7 页获取到 39 条记录
2025-06-12 08:04:21,564 - INFO - 查询完成，共获取到 639 条记录
2025-06-12 08:04:21,564 - INFO - 分段 12 查询成功，获取到 639 条记录
2025-06-12 08:04:22,564 - INFO - 查询分段 13: 2025-05-06 至 2025-05-07
2025-06-12 08:04:22,564 - INFO - 查询日期范围: 2025-05-06 至 2025-05-07，使用分页查询，每页 100 条记录
2025-06-12 08:04:22,564 - INFO - Request Parameters - Page 1:
2025-06-12 08:04:22,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:22,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800661, 1746547200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:23,267 - INFO - API请求耗时: 703ms
2025-06-12 08:04:23,267 - INFO - Response - Page 1
2025-06-12 08:04:23,267 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:04:23,767 - INFO - Request Parameters - Page 2:
2025-06-12 08:04:23,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:23,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800661, 1746547200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:24,470 - INFO - API请求耗时: 703ms
2025-06-12 08:04:24,470 - INFO - Response - Page 2
2025-06-12 08:04:24,486 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:04:25,002 - INFO - Request Parameters - Page 3:
2025-06-12 08:04:25,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:25,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800661, 1746547200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:25,689 - INFO - API请求耗时: 687ms
2025-06-12 08:04:25,689 - INFO - Response - Page 3
2025-06-12 08:04:25,689 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:04:26,189 - INFO - Request Parameters - Page 4:
2025-06-12 08:04:26,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:26,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800661, 1746547200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:26,892 - INFO - API请求耗时: 703ms
2025-06-12 08:04:26,892 - INFO - Response - Page 4
2025-06-12 08:04:26,892 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:04:27,408 - INFO - Request Parameters - Page 5:
2025-06-12 08:04:27,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:27,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800661, 1746547200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:28,048 - INFO - API请求耗时: 641ms
2025-06-12 08:04:28,048 - INFO - Response - Page 5
2025-06-12 08:04:28,048 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:04:28,548 - INFO - Request Parameters - Page 6:
2025-06-12 08:04:28,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:28,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800661, 1746547200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:29,298 - INFO - API请求耗时: 750ms
2025-06-12 08:04:29,298 - INFO - Response - Page 6
2025-06-12 08:04:29,298 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:04:29,798 - INFO - Request Parameters - Page 7:
2025-06-12 08:04:29,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:29,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800661, 1746547200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:30,361 - INFO - API请求耗时: 562ms
2025-06-12 08:04:30,361 - INFO - Response - Page 7
2025-06-12 08:04:30,361 - INFO - 第 7 页获取到 21 条记录
2025-06-12 08:04:30,361 - INFO - 查询完成，共获取到 621 条记录
2025-06-12 08:04:30,361 - INFO - 分段 13 查询成功，获取到 621 条记录
2025-06-12 08:04:31,376 - INFO - 查询分段 14: 2025-05-08 至 2025-05-09
2025-06-12 08:04:31,376 - INFO - 查询日期范围: 2025-05-08 至 2025-05-09，使用分页查询，每页 100 条记录
2025-06-12 08:04:31,376 - INFO - Request Parameters - Page 1:
2025-06-12 08:04:31,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:31,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600661, 1746720000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:32,079 - INFO - API请求耗时: 703ms
2025-06-12 08:04:32,079 - INFO - Response - Page 1
2025-06-12 08:04:32,079 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:04:32,595 - INFO - Request Parameters - Page 2:
2025-06-12 08:04:32,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:32,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600661, 1746720000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:33,329 - INFO - API请求耗时: 734ms
2025-06-12 08:04:33,329 - INFO - Response - Page 2
2025-06-12 08:04:33,329 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:04:33,829 - INFO - Request Parameters - Page 3:
2025-06-12 08:04:33,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:33,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600661, 1746720000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:34,564 - INFO - API请求耗时: 734ms
2025-06-12 08:04:34,564 - INFO - Response - Page 3
2025-06-12 08:04:34,564 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:04:35,064 - INFO - Request Parameters - Page 4:
2025-06-12 08:04:35,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:35,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600661, 1746720000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:35,751 - INFO - API请求耗时: 687ms
2025-06-12 08:04:35,751 - INFO - Response - Page 4
2025-06-12 08:04:35,767 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:04:36,267 - INFO - Request Parameters - Page 5:
2025-06-12 08:04:36,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:36,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600661, 1746720000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:37,001 - INFO - API请求耗时: 734ms
2025-06-12 08:04:37,001 - INFO - Response - Page 5
2025-06-12 08:04:37,001 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:04:37,501 - INFO - Request Parameters - Page 6:
2025-06-12 08:04:37,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:37,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600661, 1746720000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:38,173 - INFO - API请求耗时: 672ms
2025-06-12 08:04:38,173 - INFO - Response - Page 6
2025-06-12 08:04:38,173 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:04:38,689 - INFO - Request Parameters - Page 7:
2025-06-12 08:04:38,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:38,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600661, 1746720000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:39,282 - INFO - API请求耗时: 594ms
2025-06-12 08:04:39,282 - INFO - Response - Page 7
2025-06-12 08:04:39,282 - INFO - 第 7 页获取到 45 条记录
2025-06-12 08:04:39,282 - INFO - 查询完成，共获取到 645 条记录
2025-06-12 08:04:39,282 - INFO - 分段 14 查询成功，获取到 645 条记录
2025-06-12 08:04:40,298 - INFO - 查询分段 15: 2025-05-10 至 2025-05-11
2025-06-12 08:04:40,298 - INFO - 查询日期范围: 2025-05-10 至 2025-05-11，使用分页查询，每页 100 条记录
2025-06-12 08:04:40,298 - INFO - Request Parameters - Page 1:
2025-06-12 08:04:40,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:40,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400661, 1746892800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:40,923 - INFO - API请求耗时: 625ms
2025-06-12 08:04:40,923 - INFO - Response - Page 1
2025-06-12 08:04:40,923 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:04:41,423 - INFO - Request Parameters - Page 2:
2025-06-12 08:04:41,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:41,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400661, 1746892800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:42,157 - INFO - API请求耗时: 734ms
2025-06-12 08:04:42,157 - INFO - Response - Page 2
2025-06-12 08:04:42,157 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:04:42,673 - INFO - Request Parameters - Page 3:
2025-06-12 08:04:42,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:42,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400661, 1746892800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:43,345 - INFO - API请求耗时: 672ms
2025-06-12 08:04:43,345 - INFO - Response - Page 3
2025-06-12 08:04:43,345 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:04:43,860 - INFO - Request Parameters - Page 4:
2025-06-12 08:04:43,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:43,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400661, 1746892800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:44,501 - INFO - API请求耗时: 641ms
2025-06-12 08:04:44,501 - INFO - Response - Page 4
2025-06-12 08:04:44,501 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:04:45,016 - INFO - Request Parameters - Page 5:
2025-06-12 08:04:45,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:45,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400661, 1746892800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:45,688 - INFO - API请求耗时: 672ms
2025-06-12 08:04:45,688 - INFO - Response - Page 5
2025-06-12 08:04:45,688 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:04:46,188 - INFO - Request Parameters - Page 6:
2025-06-12 08:04:46,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:46,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400661, 1746892800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:46,954 - INFO - API请求耗时: 766ms
2025-06-12 08:04:46,954 - INFO - Response - Page 6
2025-06-12 08:04:46,954 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:04:47,454 - INFO - Request Parameters - Page 7:
2025-06-12 08:04:47,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:47,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400661, 1746892800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:48,016 - INFO - API请求耗时: 562ms
2025-06-12 08:04:48,016 - INFO - Response - Page 7
2025-06-12 08:04:48,016 - INFO - 第 7 页获取到 54 条记录
2025-06-12 08:04:48,016 - INFO - 查询完成，共获取到 654 条记录
2025-06-12 08:04:48,016 - INFO - 分段 15 查询成功，获取到 654 条记录
2025-06-12 08:04:49,016 - INFO - 查询分段 16: 2025-05-12 至 2025-05-13
2025-06-12 08:04:49,016 - INFO - 查询日期范围: 2025-05-12 至 2025-05-13，使用分页查询，每页 100 条记录
2025-06-12 08:04:49,016 - INFO - Request Parameters - Page 1:
2025-06-12 08:04:49,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:49,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200661, 1747065600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:49,719 - INFO - API请求耗时: 703ms
2025-06-12 08:04:49,719 - INFO - Response - Page 1
2025-06-12 08:04:49,719 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:04:50,235 - INFO - Request Parameters - Page 2:
2025-06-12 08:04:50,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:50,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200661, 1747065600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:50,907 - INFO - API请求耗时: 672ms
2025-06-12 08:04:50,907 - INFO - Response - Page 2
2025-06-12 08:04:50,907 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:04:51,423 - INFO - Request Parameters - Page 3:
2025-06-12 08:04:51,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:51,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200661, 1747065600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:52,079 - INFO - API请求耗时: 656ms
2025-06-12 08:04:52,079 - INFO - Response - Page 3
2025-06-12 08:04:52,079 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:04:52,594 - INFO - Request Parameters - Page 4:
2025-06-12 08:04:52,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:52,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200661, 1747065600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:53,235 - INFO - API请求耗时: 641ms
2025-06-12 08:04:53,235 - INFO - Response - Page 4
2025-06-12 08:04:53,235 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:04:53,751 - INFO - Request Parameters - Page 5:
2025-06-12 08:04:53,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:53,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200661, 1747065600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:54,501 - INFO - API请求耗时: 750ms
2025-06-12 08:04:54,501 - INFO - Response - Page 5
2025-06-12 08:04:54,501 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:04:55,001 - INFO - Request Parameters - Page 6:
2025-06-12 08:04:55,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:55,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200661, 1747065600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:55,626 - INFO - API请求耗时: 625ms
2025-06-12 08:04:55,626 - INFO - Response - Page 6
2025-06-12 08:04:55,626 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:04:56,141 - INFO - Request Parameters - Page 7:
2025-06-12 08:04:56,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:56,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200661, 1747065600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:56,641 - INFO - API请求耗时: 500ms
2025-06-12 08:04:56,641 - INFO - Response - Page 7
2025-06-12 08:04:56,641 - INFO - 第 7 页获取到 30 条记录
2025-06-12 08:04:56,641 - INFO - 查询完成，共获取到 630 条记录
2025-06-12 08:04:56,641 - INFO - 分段 16 查询成功，获取到 630 条记录
2025-06-12 08:04:57,657 - INFO - 查询分段 17: 2025-05-14 至 2025-05-15
2025-06-12 08:04:57,657 - INFO - 查询日期范围: 2025-05-14 至 2025-05-15，使用分页查询，每页 100 条记录
2025-06-12 08:04:57,657 - INFO - Request Parameters - Page 1:
2025-06-12 08:04:57,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:57,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000661, 1747238400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:58,344 - INFO - API请求耗时: 687ms
2025-06-12 08:04:58,344 - INFO - Response - Page 1
2025-06-12 08:04:58,344 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:04:58,844 - INFO - Request Parameters - Page 2:
2025-06-12 08:04:58,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:04:58,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000661, 1747238400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:04:59,485 - INFO - API请求耗时: 641ms
2025-06-12 08:04:59,485 - INFO - Response - Page 2
2025-06-12 08:04:59,485 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:05:00,000 - INFO - Request Parameters - Page 3:
2025-06-12 08:05:00,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:00,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000661, 1747238400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:00,641 - INFO - API请求耗时: 641ms
2025-06-12 08:05:00,641 - INFO - Response - Page 3
2025-06-12 08:05:00,641 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:05:01,157 - INFO - Request Parameters - Page 4:
2025-06-12 08:05:01,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:01,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000661, 1747238400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:01,828 - INFO - API请求耗时: 672ms
2025-06-12 08:05:01,828 - INFO - Response - Page 4
2025-06-12 08:05:01,828 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:05:02,344 - INFO - Request Parameters - Page 5:
2025-06-12 08:05:02,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:02,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000661, 1747238400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:03,032 - INFO - API请求耗时: 687ms
2025-06-12 08:05:03,032 - INFO - Response - Page 5
2025-06-12 08:05:03,032 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:05:03,547 - INFO - Request Parameters - Page 6:
2025-06-12 08:05:03,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:03,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000661, 1747238400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:04,250 - INFO - API请求耗时: 703ms
2025-06-12 08:05:04,250 - INFO - Response - Page 6
2025-06-12 08:05:04,250 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:05:04,750 - INFO - Request Parameters - Page 7:
2025-06-12 08:05:04,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:04,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000661, 1747238400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:05,235 - INFO - API请求耗时: 484ms
2025-06-12 08:05:05,235 - INFO - Response - Page 7
2025-06-12 08:05:05,235 - INFO - 第 7 页获取到 30 条记录
2025-06-12 08:05:05,235 - INFO - 查询完成，共获取到 630 条记录
2025-06-12 08:05:05,235 - INFO - 分段 17 查询成功，获取到 630 条记录
2025-06-12 08:05:06,250 - INFO - 查询分段 18: 2025-05-16 至 2025-05-17
2025-06-12 08:05:06,250 - INFO - 查询日期范围: 2025-05-16 至 2025-05-17，使用分页查询，每页 100 条记录
2025-06-12 08:05:06,250 - INFO - Request Parameters - Page 1:
2025-06-12 08:05:06,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:06,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800661, 1747411200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:07,063 - INFO - API请求耗时: 812ms
2025-06-12 08:05:07,063 - INFO - Response - Page 1
2025-06-12 08:05:07,063 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:05:07,563 - INFO - Request Parameters - Page 2:
2025-06-12 08:05:07,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:07,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800661, 1747411200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:08,234 - INFO - API请求耗时: 672ms
2025-06-12 08:05:08,234 - INFO - Response - Page 2
2025-06-12 08:05:08,234 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:05:08,750 - INFO - Request Parameters - Page 3:
2025-06-12 08:05:08,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:08,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800661, 1747411200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:09,484 - INFO - API请求耗时: 734ms
2025-06-12 08:05:09,484 - INFO - Response - Page 3
2025-06-12 08:05:09,484 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:05:09,984 - INFO - Request Parameters - Page 4:
2025-06-12 08:05:09,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:09,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800661, 1747411200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:10,609 - INFO - API请求耗时: 625ms
2025-06-12 08:05:10,609 - INFO - Response - Page 4
2025-06-12 08:05:10,609 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:05:11,109 - INFO - Request Parameters - Page 5:
2025-06-12 08:05:11,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:11,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800661, 1747411200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:11,766 - INFO - API请求耗时: 656ms
2025-06-12 08:05:11,766 - INFO - Response - Page 5
2025-06-12 08:05:11,766 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:05:12,266 - INFO - Request Parameters - Page 6:
2025-06-12 08:05:12,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:12,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800661, 1747411200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:12,953 - INFO - API请求耗时: 687ms
2025-06-12 08:05:12,953 - INFO - Response - Page 6
2025-06-12 08:05:12,953 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:05:13,453 - INFO - Request Parameters - Page 7:
2025-06-12 08:05:13,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:13,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800661, 1747411200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:14,000 - INFO - API请求耗时: 547ms
2025-06-12 08:05:14,000 - INFO - Response - Page 7
2025-06-12 08:05:14,000 - INFO - 第 7 页获取到 45 条记录
2025-06-12 08:05:14,000 - INFO - 查询完成，共获取到 645 条记录
2025-06-12 08:05:14,000 - INFO - 分段 18 查询成功，获取到 645 条记录
2025-06-12 08:05:15,016 - INFO - 查询分段 19: 2025-05-18 至 2025-05-19
2025-06-12 08:05:15,016 - INFO - 查询日期范围: 2025-05-18 至 2025-05-19，使用分页查询，每页 100 条记录
2025-06-12 08:05:15,016 - INFO - Request Parameters - Page 1:
2025-06-12 08:05:15,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:15,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600661, 1747584000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:15,703 - INFO - API请求耗时: 687ms
2025-06-12 08:05:15,703 - INFO - Response - Page 1
2025-06-12 08:05:15,703 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:05:16,219 - INFO - Request Parameters - Page 2:
2025-06-12 08:05:16,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:16,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600661, 1747584000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:16,969 - INFO - API请求耗时: 750ms
2025-06-12 08:05:16,969 - INFO - Response - Page 2
2025-06-12 08:05:16,969 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:05:17,484 - INFO - Request Parameters - Page 3:
2025-06-12 08:05:17,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:17,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600661, 1747584000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:18,187 - INFO - API请求耗时: 703ms
2025-06-12 08:05:18,187 - INFO - Response - Page 3
2025-06-12 08:05:18,187 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:05:18,687 - INFO - Request Parameters - Page 4:
2025-06-12 08:05:18,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:18,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600661, 1747584000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:19,469 - INFO - API请求耗时: 781ms
2025-06-12 08:05:19,484 - INFO - Response - Page 4
2025-06-12 08:05:19,484 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:05:20,000 - INFO - Request Parameters - Page 5:
2025-06-12 08:05:20,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:20,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600661, 1747584000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:20,609 - INFO - API请求耗时: 609ms
2025-06-12 08:05:20,609 - INFO - Response - Page 5
2025-06-12 08:05:20,609 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:05:21,109 - INFO - Request Parameters - Page 6:
2025-06-12 08:05:21,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:21,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600661, 1747584000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:21,828 - INFO - API请求耗时: 719ms
2025-06-12 08:05:21,828 - INFO - Response - Page 6
2025-06-12 08:05:21,828 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:05:22,328 - INFO - Request Parameters - Page 7:
2025-06-12 08:05:22,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:22,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600661, 1747584000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:22,781 - INFO - API请求耗时: 453ms
2025-06-12 08:05:22,781 - INFO - Response - Page 7
2025-06-12 08:05:22,781 - INFO - 第 7 页获取到 30 条记录
2025-06-12 08:05:22,781 - INFO - 查询完成，共获取到 630 条记录
2025-06-12 08:05:22,781 - INFO - 分段 19 查询成功，获取到 630 条记录
2025-06-12 08:05:23,796 - INFO - 查询分段 20: 2025-05-20 至 2025-05-21
2025-06-12 08:05:23,796 - INFO - 查询日期范围: 2025-05-20 至 2025-05-21，使用分页查询，每页 100 条记录
2025-06-12 08:05:23,796 - INFO - Request Parameters - Page 1:
2025-06-12 08:05:23,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:23,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400661, 1747756800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:24,406 - INFO - API请求耗时: 609ms
2025-06-12 08:05:24,406 - INFO - Response - Page 1
2025-06-12 08:05:24,406 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:05:24,921 - INFO - Request Parameters - Page 2:
2025-06-12 08:05:24,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:24,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400661, 1747756800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:25,609 - INFO - API请求耗时: 687ms
2025-06-12 08:05:25,609 - INFO - Response - Page 2
2025-06-12 08:05:25,609 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:05:26,125 - INFO - Request Parameters - Page 3:
2025-06-12 08:05:26,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:26,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400661, 1747756800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:26,812 - INFO - API请求耗时: 687ms
2025-06-12 08:05:26,812 - INFO - Response - Page 3
2025-06-12 08:05:26,812 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:05:27,312 - INFO - Request Parameters - Page 4:
2025-06-12 08:05:27,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:27,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400661, 1747756800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:27,937 - INFO - API请求耗时: 625ms
2025-06-12 08:05:27,937 - INFO - Response - Page 4
2025-06-12 08:05:27,953 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:05:28,468 - INFO - Request Parameters - Page 5:
2025-06-12 08:05:28,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:28,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400661, 1747756800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:29,171 - INFO - API请求耗时: 703ms
2025-06-12 08:05:29,171 - INFO - Response - Page 5
2025-06-12 08:05:29,171 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:05:29,671 - INFO - Request Parameters - Page 6:
2025-06-12 08:05:29,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:29,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400661, 1747756800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:30,390 - INFO - API请求耗时: 719ms
2025-06-12 08:05:30,390 - INFO - Response - Page 6
2025-06-12 08:05:30,390 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:05:30,906 - INFO - Request Parameters - Page 7:
2025-06-12 08:05:30,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:30,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400661, 1747756800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:31,390 - INFO - API请求耗时: 484ms
2025-06-12 08:05:31,406 - INFO - Response - Page 7
2025-06-12 08:05:31,406 - INFO - 第 7 页获取到 24 条记录
2025-06-12 08:05:31,406 - INFO - 查询完成，共获取到 624 条记录
2025-06-12 08:05:31,406 - INFO - 分段 20 查询成功，获取到 624 条记录
2025-06-12 08:05:32,406 - INFO - 查询分段 21: 2025-05-22 至 2025-05-23
2025-06-12 08:05:32,406 - INFO - 查询日期范围: 2025-05-22 至 2025-05-23，使用分页查询，每页 100 条记录
2025-06-12 08:05:32,406 - INFO - Request Parameters - Page 1:
2025-06-12 08:05:32,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:32,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200661, 1747929600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:33,093 - INFO - API请求耗时: 687ms
2025-06-12 08:05:33,093 - INFO - Response - Page 1
2025-06-12 08:05:33,093 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:05:33,593 - INFO - Request Parameters - Page 2:
2025-06-12 08:05:33,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:33,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200661, 1747929600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:34,234 - INFO - API请求耗时: 641ms
2025-06-12 08:05:34,234 - INFO - Response - Page 2
2025-06-12 08:05:34,234 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:05:34,734 - INFO - Request Parameters - Page 3:
2025-06-12 08:05:34,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:34,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200661, 1747929600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:35,468 - INFO - API请求耗时: 734ms
2025-06-12 08:05:35,468 - INFO - Response - Page 3
2025-06-12 08:05:35,468 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:05:35,984 - INFO - Request Parameters - Page 4:
2025-06-12 08:05:35,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:35,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200661, 1747929600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:36,749 - INFO - API请求耗时: 766ms
2025-06-12 08:05:36,749 - INFO - Response - Page 4
2025-06-12 08:05:36,749 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:05:37,265 - INFO - Request Parameters - Page 5:
2025-06-12 08:05:37,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:37,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200661, 1747929600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:37,937 - INFO - API请求耗时: 672ms
2025-06-12 08:05:37,937 - INFO - Response - Page 5
2025-06-12 08:05:37,937 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:05:38,452 - INFO - Request Parameters - Page 6:
2025-06-12 08:05:38,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:38,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200661, 1747929600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:39,171 - INFO - API请求耗时: 719ms
2025-06-12 08:05:39,171 - INFO - Response - Page 6
2025-06-12 08:05:39,171 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:05:39,671 - INFO - Request Parameters - Page 7:
2025-06-12 08:05:39,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:39,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200661, 1747929600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:40,155 - INFO - API请求耗时: 484ms
2025-06-12 08:05:40,155 - INFO - Response - Page 7
2025-06-12 08:05:40,155 - INFO - 第 7 页获取到 24 条记录
2025-06-12 08:05:40,155 - INFO - 查询完成，共获取到 624 条记录
2025-06-12 08:05:40,155 - INFO - 分段 21 查询成功，获取到 624 条记录
2025-06-12 08:05:41,171 - INFO - 查询分段 22: 2025-05-24 至 2025-05-25
2025-06-12 08:05:41,171 - INFO - 查询日期范围: 2025-05-24 至 2025-05-25，使用分页查询，每页 100 条记录
2025-06-12 08:05:41,171 - INFO - Request Parameters - Page 1:
2025-06-12 08:05:41,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:41,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000661, 1748102400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:41,858 - INFO - API请求耗时: 687ms
2025-06-12 08:05:41,858 - INFO - Response - Page 1
2025-06-12 08:05:41,858 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:05:42,374 - INFO - Request Parameters - Page 2:
2025-06-12 08:05:42,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:42,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000661, 1748102400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:43,093 - INFO - API请求耗时: 719ms
2025-06-12 08:05:43,093 - INFO - Response - Page 2
2025-06-12 08:05:43,108 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:05:43,624 - INFO - Request Parameters - Page 3:
2025-06-12 08:05:43,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:43,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000661, 1748102400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:44,311 - INFO - API请求耗时: 687ms
2025-06-12 08:05:44,311 - INFO - Response - Page 3
2025-06-12 08:05:44,311 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:05:44,811 - INFO - Request Parameters - Page 4:
2025-06-12 08:05:44,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:44,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000661, 1748102400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:45,530 - INFO - API请求耗时: 719ms
2025-06-12 08:05:45,530 - INFO - Response - Page 4
2025-06-12 08:05:45,530 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:05:46,030 - INFO - Request Parameters - Page 5:
2025-06-12 08:05:46,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:46,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000661, 1748102400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:46,655 - INFO - API请求耗时: 625ms
2025-06-12 08:05:46,655 - INFO - Response - Page 5
2025-06-12 08:05:46,671 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:05:47,186 - INFO - Request Parameters - Page 6:
2025-06-12 08:05:47,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:47,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000661, 1748102400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:47,874 - INFO - API请求耗时: 687ms
2025-06-12 08:05:47,874 - INFO - Response - Page 6
2025-06-12 08:05:47,874 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:05:48,389 - INFO - Request Parameters - Page 7:
2025-06-12 08:05:48,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:48,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000661, 1748102400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:48,905 - INFO - API请求耗时: 516ms
2025-06-12 08:05:48,921 - INFO - Response - Page 7
2025-06-12 08:05:48,921 - INFO - 第 7 页获取到 27 条记录
2025-06-12 08:05:48,921 - INFO - 查询完成，共获取到 627 条记录
2025-06-12 08:05:48,921 - INFO - 分段 22 查询成功，获取到 627 条记录
2025-06-12 08:05:49,936 - INFO - 查询分段 23: 2025-05-26 至 2025-05-27
2025-06-12 08:05:49,936 - INFO - 查询日期范围: 2025-05-26 至 2025-05-27，使用分页查询，每页 100 条记录
2025-06-12 08:05:49,936 - INFO - Request Parameters - Page 1:
2025-06-12 08:05:49,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:49,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800661, 1748275200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:50,624 - INFO - API请求耗时: 687ms
2025-06-12 08:05:50,624 - INFO - Response - Page 1
2025-06-12 08:05:50,624 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:05:51,124 - INFO - Request Parameters - Page 2:
2025-06-12 08:05:51,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:51,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800661, 1748275200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:51,796 - INFO - API请求耗时: 672ms
2025-06-12 08:05:51,796 - INFO - Response - Page 2
2025-06-12 08:05:51,796 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:05:52,296 - INFO - Request Parameters - Page 3:
2025-06-12 08:05:52,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:52,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800661, 1748275200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:52,983 - INFO - API请求耗时: 687ms
2025-06-12 08:05:52,983 - INFO - Response - Page 3
2025-06-12 08:05:52,983 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:05:53,499 - INFO - Request Parameters - Page 4:
2025-06-12 08:05:53,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:53,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800661, 1748275200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:54,186 - INFO - API请求耗时: 687ms
2025-06-12 08:05:54,186 - INFO - Response - Page 4
2025-06-12 08:05:54,186 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:05:54,686 - INFO - Request Parameters - Page 5:
2025-06-12 08:05:54,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:54,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800661, 1748275200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:55,405 - INFO - API请求耗时: 719ms
2025-06-12 08:05:55,405 - INFO - Response - Page 5
2025-06-12 08:05:55,405 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:05:55,905 - INFO - Request Parameters - Page 6:
2025-06-12 08:05:55,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:55,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800661, 1748275200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:56,811 - INFO - API请求耗时: 906ms
2025-06-12 08:05:56,811 - INFO - Response - Page 6
2025-06-12 08:05:56,811 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:05:57,327 - INFO - Request Parameters - Page 7:
2025-06-12 08:05:57,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:57,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800661, 1748275200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:57,827 - INFO - API请求耗时: 500ms
2025-06-12 08:05:57,827 - INFO - Response - Page 7
2025-06-12 08:05:57,827 - INFO - 第 7 页获取到 15 条记录
2025-06-12 08:05:57,827 - INFO - 查询完成，共获取到 615 条记录
2025-06-12 08:05:57,827 - INFO - 分段 23 查询成功，获取到 615 条记录
2025-06-12 08:05:58,842 - INFO - 查询分段 24: 2025-05-28 至 2025-05-29
2025-06-12 08:05:58,842 - INFO - 查询日期范围: 2025-05-28 至 2025-05-29，使用分页查询，每页 100 条记录
2025-06-12 08:05:58,842 - INFO - Request Parameters - Page 1:
2025-06-12 08:05:58,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:58,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600661, 1748448000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:05:59,483 - INFO - API请求耗时: 641ms
2025-06-12 08:05:59,483 - INFO - Response - Page 1
2025-06-12 08:05:59,483 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:05:59,983 - INFO - Request Parameters - Page 2:
2025-06-12 08:05:59,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:05:59,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600661, 1748448000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:00,655 - INFO - API请求耗时: 672ms
2025-06-12 08:06:00,655 - INFO - Response - Page 2
2025-06-12 08:06:00,655 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:06:01,155 - INFO - Request Parameters - Page 3:
2025-06-12 08:06:01,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:01,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600661, 1748448000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:01,858 - INFO - API请求耗时: 703ms
2025-06-12 08:06:01,858 - INFO - Response - Page 3
2025-06-12 08:06:01,858 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:06:02,358 - INFO - Request Parameters - Page 4:
2025-06-12 08:06:02,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:02,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600661, 1748448000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:03,092 - INFO - API请求耗时: 734ms
2025-06-12 08:06:03,092 - INFO - Response - Page 4
2025-06-12 08:06:03,092 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:06:03,592 - INFO - Request Parameters - Page 5:
2025-06-12 08:06:03,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:03,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600661, 1748448000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:04,342 - INFO - API请求耗时: 750ms
2025-06-12 08:06:04,342 - INFO - Response - Page 5
2025-06-12 08:06:04,358 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:06:04,873 - INFO - Request Parameters - Page 6:
2025-06-12 08:06:04,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:04,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600661, 1748448000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:05,623 - INFO - API请求耗时: 750ms
2025-06-12 08:06:05,623 - INFO - Response - Page 6
2025-06-12 08:06:05,623 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:06:06,123 - INFO - Request Parameters - Page 7:
2025-06-12 08:06:06,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:06,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600661, 1748448000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:06,639 - INFO - API请求耗时: 516ms
2025-06-12 08:06:06,639 - INFO - Response - Page 7
2025-06-12 08:06:06,639 - INFO - 第 7 页获取到 18 条记录
2025-06-12 08:06:06,639 - INFO - 查询完成，共获取到 618 条记录
2025-06-12 08:06:06,639 - INFO - 分段 24 查询成功，获取到 618 条记录
2025-06-12 08:06:07,670 - INFO - 查询分段 25: 2025-05-30 至 2025-05-31
2025-06-12 08:06:07,670 - INFO - 查询日期范围: 2025-05-30 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-12 08:06:07,670 - INFO - Request Parameters - Page 1:
2025-06-12 08:06:07,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:07,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400661, 1748620800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:08,373 - INFO - API请求耗时: 703ms
2025-06-12 08:06:08,373 - INFO - Response - Page 1
2025-06-12 08:06:08,373 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:06:08,873 - INFO - Request Parameters - Page 2:
2025-06-12 08:06:08,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:08,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400661, 1748620800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:09,670 - INFO - API请求耗时: 797ms
2025-06-12 08:06:09,670 - INFO - Response - Page 2
2025-06-12 08:06:09,670 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:06:10,186 - INFO - Request Parameters - Page 3:
2025-06-12 08:06:10,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:10,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400661, 1748620800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:10,842 - INFO - API请求耗时: 656ms
2025-06-12 08:06:10,842 - INFO - Response - Page 3
2025-06-12 08:06:10,842 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:06:11,357 - INFO - Request Parameters - Page 4:
2025-06-12 08:06:11,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:11,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400661, 1748620800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:11,998 - INFO - API请求耗时: 641ms
2025-06-12 08:06:11,998 - INFO - Response - Page 4
2025-06-12 08:06:11,998 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:06:12,514 - INFO - Request Parameters - Page 5:
2025-06-12 08:06:12,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:12,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400661, 1748620800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:13,217 - INFO - API请求耗时: 703ms
2025-06-12 08:06:13,217 - INFO - Response - Page 5
2025-06-12 08:06:13,217 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:06:13,717 - INFO - Request Parameters - Page 6:
2025-06-12 08:06:13,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:13,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400661, 1748620800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:14,420 - INFO - API请求耗时: 703ms
2025-06-12 08:06:14,420 - INFO - Response - Page 6
2025-06-12 08:06:14,420 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:06:14,920 - INFO - Request Parameters - Page 7:
2025-06-12 08:06:14,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:14,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400661, 1748620800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:15,420 - INFO - API请求耗时: 500ms
2025-06-12 08:06:15,420 - INFO - Response - Page 7
2025-06-12 08:06:15,420 - INFO - 第 7 页获取到 27 条记录
2025-06-12 08:06:15,420 - INFO - 查询完成，共获取到 627 条记录
2025-06-12 08:06:15,420 - INFO - 分段 25 查询成功，获取到 627 条记录
2025-06-12 08:06:16,420 - INFO - 查询分段 26: 2025-06-01 至 2025-06-02
2025-06-12 08:06:16,420 - INFO - 查询日期范围: 2025-06-01 至 2025-06-02，使用分页查询，每页 100 条记录
2025-06-12 08:06:16,420 - INFO - Request Parameters - Page 1:
2025-06-12 08:06:16,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:16,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200661, 1748793600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:17,185 - INFO - API请求耗时: 766ms
2025-06-12 08:06:17,185 - INFO - Response - Page 1
2025-06-12 08:06:17,185 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:06:17,685 - INFO - Request Parameters - Page 2:
2025-06-12 08:06:17,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:17,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200661, 1748793600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:18,326 - INFO - API请求耗时: 641ms
2025-06-12 08:06:18,326 - INFO - Response - Page 2
2025-06-12 08:06:18,326 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:06:18,826 - INFO - Request Parameters - Page 3:
2025-06-12 08:06:18,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:18,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200661, 1748793600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:19,560 - INFO - API请求耗时: 734ms
2025-06-12 08:06:19,560 - INFO - Response - Page 3
2025-06-12 08:06:19,560 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:06:20,076 - INFO - Request Parameters - Page 4:
2025-06-12 08:06:20,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:20,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200661, 1748793600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:20,810 - INFO - API请求耗时: 734ms
2025-06-12 08:06:20,810 - INFO - Response - Page 4
2025-06-12 08:06:20,810 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:06:21,326 - INFO - Request Parameters - Page 5:
2025-06-12 08:06:21,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:21,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200661, 1748793600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:21,982 - INFO - API请求耗时: 656ms
2025-06-12 08:06:21,982 - INFO - Response - Page 5
2025-06-12 08:06:21,982 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:06:22,482 - INFO - Request Parameters - Page 6:
2025-06-12 08:06:22,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:22,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200661, 1748793600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:23,107 - INFO - API请求耗时: 625ms
2025-06-12 08:06:23,107 - INFO - Response - Page 6
2025-06-12 08:06:23,107 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:06:23,623 - INFO - Request Parameters - Page 7:
2025-06-12 08:06:23,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:23,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200661, 1748793600661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:23,966 - INFO - API请求耗时: 344ms
2025-06-12 08:06:23,966 - INFO - Response - Page 7
2025-06-12 08:06:23,966 - INFO - 第 7 页获取到 3 条记录
2025-06-12 08:06:23,966 - INFO - 查询完成，共获取到 603 条记录
2025-06-12 08:06:23,966 - INFO - 分段 26 查询成功，获取到 603 条记录
2025-06-12 08:06:24,966 - INFO - 查询分段 27: 2025-06-03 至 2025-06-04
2025-06-12 08:06:24,966 - INFO - 查询日期范围: 2025-06-03 至 2025-06-04，使用分页查询，每页 100 条记录
2025-06-12 08:06:24,966 - INFO - Request Parameters - Page 1:
2025-06-12 08:06:24,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:24,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000661, 1748966400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:25,670 - INFO - API请求耗时: 703ms
2025-06-12 08:06:25,670 - INFO - Response - Page 1
2025-06-12 08:06:25,670 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:06:26,185 - INFO - Request Parameters - Page 2:
2025-06-12 08:06:26,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:26,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000661, 1748966400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:26,873 - INFO - API请求耗时: 687ms
2025-06-12 08:06:26,873 - INFO - Response - Page 2
2025-06-12 08:06:26,873 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:06:27,373 - INFO - Request Parameters - Page 3:
2025-06-12 08:06:27,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:27,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000661, 1748966400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:28,060 - INFO - API请求耗时: 687ms
2025-06-12 08:06:28,060 - INFO - Response - Page 3
2025-06-12 08:06:28,060 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:06:28,560 - INFO - Request Parameters - Page 4:
2025-06-12 08:06:28,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:28,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000661, 1748966400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:29,263 - INFO - API请求耗时: 703ms
2025-06-12 08:06:29,263 - INFO - Response - Page 4
2025-06-12 08:06:29,263 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:06:29,779 - INFO - Request Parameters - Page 5:
2025-06-12 08:06:29,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:29,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000661, 1748966400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:30,451 - INFO - API请求耗时: 672ms
2025-06-12 08:06:30,451 - INFO - Response - Page 5
2025-06-12 08:06:30,451 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:06:30,966 - INFO - Request Parameters - Page 6:
2025-06-12 08:06:30,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:30,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000661, 1748966400661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:31,669 - INFO - API请求耗时: 703ms
2025-06-12 08:06:31,669 - INFO - Response - Page 6
2025-06-12 08:06:31,685 - INFO - 第 6 页获取到 96 条记录
2025-06-12 08:06:31,685 - INFO - 查询完成，共获取到 596 条记录
2025-06-12 08:06:31,685 - INFO - 分段 27 查询成功，获取到 596 条记录
2025-06-12 08:06:32,701 - INFO - 查询分段 28: 2025-06-05 至 2025-06-06
2025-06-12 08:06:32,701 - INFO - 查询日期范围: 2025-06-05 至 2025-06-06，使用分页查询，每页 100 条记录
2025-06-12 08:06:32,701 - INFO - Request Parameters - Page 1:
2025-06-12 08:06:32,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:32,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800661, 1749139200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:33,357 - INFO - API请求耗时: 656ms
2025-06-12 08:06:33,357 - INFO - Response - Page 1
2025-06-12 08:06:33,357 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:06:33,857 - INFO - Request Parameters - Page 2:
2025-06-12 08:06:33,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:33,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800661, 1749139200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:34,497 - INFO - API请求耗时: 641ms
2025-06-12 08:06:34,497 - INFO - Response - Page 2
2025-06-12 08:06:34,497 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:06:34,997 - INFO - Request Parameters - Page 3:
2025-06-12 08:06:34,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:34,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800661, 1749139200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:35,685 - INFO - API请求耗时: 687ms
2025-06-12 08:06:35,685 - INFO - Response - Page 3
2025-06-12 08:06:35,685 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:06:36,185 - INFO - Request Parameters - Page 4:
2025-06-12 08:06:36,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:36,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800661, 1749139200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:36,904 - INFO - API请求耗时: 719ms
2025-06-12 08:06:36,904 - INFO - Response - Page 4
2025-06-12 08:06:36,904 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:06:37,404 - INFO - Request Parameters - Page 5:
2025-06-12 08:06:37,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:37,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800661, 1749139200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:38,169 - INFO - API请求耗时: 766ms
2025-06-12 08:06:38,169 - INFO - Response - Page 5
2025-06-12 08:06:38,169 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:06:38,669 - INFO - Request Parameters - Page 6:
2025-06-12 08:06:38,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:38,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800661, 1749139200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:39,357 - INFO - API请求耗时: 687ms
2025-06-12 08:06:39,357 - INFO - Response - Page 6
2025-06-12 08:06:39,357 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:06:39,872 - INFO - Request Parameters - Page 7:
2025-06-12 08:06:39,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:39,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800661, 1749139200661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:40,372 - INFO - API请求耗时: 500ms
2025-06-12 08:06:40,372 - INFO - Response - Page 7
2025-06-12 08:06:40,372 - INFO - 第 7 页获取到 13 条记录
2025-06-12 08:06:40,372 - INFO - 查询完成，共获取到 613 条记录
2025-06-12 08:06:40,372 - INFO - 分段 28 查询成功，获取到 613 条记录
2025-06-12 08:06:41,388 - INFO - 查询分段 29: 2025-06-07 至 2025-06-08
2025-06-12 08:06:41,388 - INFO - 查询日期范围: 2025-06-07 至 2025-06-08，使用分页查询，每页 100 条记录
2025-06-12 08:06:41,388 - INFO - Request Parameters - Page 1:
2025-06-12 08:06:41,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:41,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600661, 1749312000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:42,091 - INFO - API请求耗时: 703ms
2025-06-12 08:06:42,091 - INFO - Response - Page 1
2025-06-12 08:06:42,091 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:06:42,591 - INFO - Request Parameters - Page 2:
2025-06-12 08:06:42,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:42,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600661, 1749312000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:43,278 - INFO - API请求耗时: 687ms
2025-06-12 08:06:43,278 - INFO - Response - Page 2
2025-06-12 08:06:43,278 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:06:43,794 - INFO - Request Parameters - Page 3:
2025-06-12 08:06:43,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:43,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600661, 1749312000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:44,450 - INFO - API请求耗时: 656ms
2025-06-12 08:06:44,450 - INFO - Response - Page 3
2025-06-12 08:06:44,450 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:06:44,966 - INFO - Request Parameters - Page 4:
2025-06-12 08:06:44,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:44,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600661, 1749312000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:45,638 - INFO - API请求耗时: 672ms
2025-06-12 08:06:45,638 - INFO - Response - Page 4
2025-06-12 08:06:45,638 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:06:46,138 - INFO - Request Parameters - Page 5:
2025-06-12 08:06:46,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:46,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600661, 1749312000661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:46,528 - INFO - API请求耗时: 391ms
2025-06-12 08:06:46,528 - INFO - Response - Page 5
2025-06-12 08:06:46,528 - INFO - 第 5 页获取到 5 条记录
2025-06-12 08:06:46,528 - INFO - 查询完成，共获取到 405 条记录
2025-06-12 08:06:46,528 - INFO - 分段 29 查询成功，获取到 405 条记录
2025-06-12 08:06:47,544 - INFO - 查询分段 30: 2025-06-09 至 2025-06-10
2025-06-12 08:06:47,544 - INFO - 查询日期范围: 2025-06-09 至 2025-06-10，使用分页查询，每页 100 条记录
2025-06-12 08:06:47,544 - INFO - Request Parameters - Page 1:
2025-06-12 08:06:47,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:47,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400661, 1749484800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:48,341 - INFO - API请求耗时: 797ms
2025-06-12 08:06:48,341 - INFO - Response - Page 1
2025-06-12 08:06:48,341 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:06:48,841 - INFO - Request Parameters - Page 2:
2025-06-12 08:06:48,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:48,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400661, 1749484800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:49,497 - INFO - API请求耗时: 656ms
2025-06-12 08:06:49,497 - INFO - Response - Page 2
2025-06-12 08:06:49,497 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:06:50,012 - INFO - Request Parameters - Page 3:
2025-06-12 08:06:50,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:50,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400661, 1749484800661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:50,512 - INFO - API请求耗时: 500ms
2025-06-12 08:06:50,512 - INFO - Response - Page 3
2025-06-12 08:06:50,512 - INFO - 第 3 页获取到 2 条记录
2025-06-12 08:06:50,512 - INFO - 查询完成，共获取到 202 条记录
2025-06-12 08:06:50,512 - INFO - 分段 30 查询成功，获取到 202 条记录
2025-06-12 08:06:51,512 - INFO - 查询分段 31: 2025-06-11 至 2025-06-11
2025-06-12 08:06:51,512 - INFO - 查询日期范围: 2025-06-11 至 2025-06-11，使用分页查询，每页 100 条记录
2025-06-12 08:06:51,512 - INFO - Request Parameters - Page 1:
2025-06-12 08:06:51,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:06:51,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749571200661, 1749657599661], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:06:51,825 - INFO - API请求耗时: 312ms
2025-06-12 08:06:51,825 - INFO - Response - Page 1
2025-06-12 08:06:51,825 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:06:51,825 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:06:51,825 - WARNING - 分段 31 查询返回空数据
2025-06-12 08:06:52,841 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 18088 条记录，失败 0 次
2025-06-12 08:06:52,841 - INFO - 成功获取宜搭日销售表单数据，共 18088 条记录
2025-06-12 08:06:52,841 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-12 08:06:52,841 - INFO - 开始对比和同步日销售数据...
2025-06-12 08:06:53,341 - INFO - 成功创建宜搭日销售数据索引，共 6307 条记录
2025-06-12 08:06:53,341 - INFO - 开始处理数衍数据，共 12827 条记录
2025-06-12 08:06:55,294 - INFO - 更新表单数据成功: FINST-80B66291BN4W41PVEO13U73FHO3N2A3MJONBMY2
2025-06-12 08:06:55,294 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_********, 变更字段: [{'field': 'amount', 'old_value': 4397.21, 'new_value': 4465.91}, {'field': 'count', 'old_value': 31, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 4134.36, 'new_value': 4203.06}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 28}]
2025-06-12 08:06:55,856 - INFO - 更新表单数据成功: FINST-80B66291BN4W41PVEO13U73FHO3N2A3MJONBMI4
2025-06-12 08:06:55,856 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 52131.93, 'new_value': 88698.09}, {'field': 'count', 'old_value': 1168, 'new_value': 1872}, {'field': 'instoreAmount', 'old_value': 49827.34, 'new_value': 86393.5}, {'field': 'instoreCount', 'old_value': 1123, 'new_value': 1827}]
2025-06-12 08:06:56,575 - INFO - 更新表单数据成功: FINST-MLF669B1CC2WOCXW72HLTDMKOM1M3GNZJONBMX6
2025-06-12 08:06:56,575 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 47931.55}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 47931.55}, {'field': 'amount', 'old_value': 34246.46, 'new_value': 65126.579999999994}, {'field': 'count', 'old_value': 643, 'new_value': 1306}, {'field': 'instoreAmount', 'old_value': 32241.25, 'new_value': 63342.67}, {'field': 'instoreCount', 'old_value': 606, 'new_value': 1269}]
2025-06-12 08:06:57,200 - INFO - 更新表单数据成功: FINST-LLF66F714T2W4MRADVHVXAL5N8H52EDAKONBM731
2025-06-12 08:06:57,200 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 27012.64, 'new_value': 50069.920000000006}, {'field': 'count', 'old_value': 429, 'new_value': 962}, {'field': 'instoreAmount', 'old_value': 24985.65, 'new_value': 48042.93}, {'field': 'instoreCount', 'old_value': 393, 'new_value': 926}]
2025-06-12 08:06:57,778 - INFO - 更新表单数据成功: FINST-LLF668810P4WMXJW807ZN90Z7UL62A2LKONBMW2
2025-06-12 08:06:57,793 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 5182.05, 'new_value': 12523.6}, {'field': 'count', 'old_value': 80, 'new_value': 236}, {'field': 'instoreAmount', 'old_value': 3655.68, 'new_value': 10997.23}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 207}]
2025-06-12 08:06:58,340 - INFO - 更新表单数据成功: FINST-F3G66Q61BP4WCYL86ZYRNA7T5YWT3FOVKONBM14
2025-06-12 08:06:58,340 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 11000.72, 'new_value': 25987.13}, {'field': 'count', 'old_value': 166, 'new_value': 435}, {'field': 'instoreAmount', 'old_value': 9894.1, 'new_value': 24880.51}, {'field': 'instoreCount', 'old_value': 139, 'new_value': 408}]
2025-06-12 08:06:58,965 - INFO - 更新表单数据成功: FINST-AEF66BC1MQ2WLWP1CCKHHCZR5AQU33X8LONBMJ01
2025-06-12 08:06:58,965 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 47513.51}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 47513.51}, {'field': 'amount', 'old_value': 29100.54, 'new_value': 61608.4}, {'field': 'count', 'old_value': 588, 'new_value': 1281}, {'field': 'instoreAmount', 'old_value': 27987.9, 'new_value': 60661.76}, {'field': 'instoreCount', 'old_value': 568, 'new_value': 1261}]
2025-06-12 08:06:59,590 - INFO - 更新表单数据成功: FINST-90E66JD16A2W33GN6R0KS5N43LIF33SJLONBMET
2025-06-12 08:06:59,590 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 7350.45, 'new_value': 16327.86}, {'field': 'count', 'old_value': 148, 'new_value': 331}, {'field': 'instoreAmount', 'old_value': 5985.93, 'new_value': 14963.34}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 309}]
2025-06-12 08:07:00,231 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T36KULONBMQ3
2025-06-12 08:07:00,231 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10941.65, 'new_value': 12839.7}, {'field': 'dailyBillAmount', 'old_value': 10941.65, 'new_value': 12839.7}, {'field': 'amount', 'old_value': 3378.6, 'new_value': 14477.98}, {'field': 'count', 'old_value': 65, 'new_value': 256}, {'field': 'instoreAmount', 'old_value': 3378.6, 'new_value': 14477.98}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 256}]
2025-06-12 08:07:00,840 - INFO - 更新表单数据成功: FINST-2HF66O615A2WVEHJAD4YR7HFDH1H2ID5MONBMTW
2025-06-12 08:07:00,840 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 48044.36}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 48044.36}, {'field': 'amount', 'old_value': 25575.56, 'new_value': 59563.369999999995}, {'field': 'count', 'old_value': 476, 'new_value': 1118}, {'field': 'instoreAmount', 'old_value': 25575.56, 'new_value': 59599.27}, {'field': 'instoreCount', 'old_value': 476, 'new_value': 1118}]
2025-06-12 08:07:01,450 - INFO - 更新表单数据成功: FINST-1T666B91BT1WLX9QDJTQLAST92PC2CPIMONBM1A1
2025-06-12 08:07:01,450 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 11085.43}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11085.43}, {'field': 'amount', 'old_value': 4279.9, 'new_value': 14649.230000000001}, {'field': 'count', 'old_value': 84, 'new_value': 274}, {'field': 'instoreAmount', 'old_value': 2095.87, 'new_value': 12505.0}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 235}]
2025-06-12 08:07:02,106 - INFO - 更新表单数据成功: FINST-OLC66Z61HD2W9CBS99BTP5OKDIR72JDTMONBMD51
2025-06-12 08:07:02,106 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13132.14}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13132.14}, {'field': 'amount', 'old_value': 6056.1, 'new_value': 16408.01}, {'field': 'count', 'old_value': 108, 'new_value': 311}, {'field': 'instoreAmount', 'old_value': 5230.24, 'new_value': 15893.45}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 290}]
2025-06-12 08:07:02,715 - INFO - 更新表单数据成功: FINST-XRF66A81YK4W29989SZTAD6FS81F3A34NONBMT4
2025-06-12 08:07:02,715 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16912.63}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16912.63}, {'field': 'amount', 'old_value': 8802.2, 'new_value': 22117.38}, {'field': 'count', 'old_value': 114, 'new_value': 376}, {'field': 'instoreAmount', 'old_value': 7448.92, 'new_value': 20764.1}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 349}]
2025-06-12 08:07:03,278 - INFO - 更新表单数据成功: FINST-RI766091YT2WHLYIDD4CED8TS17K2MRENONBM391
2025-06-12 08:07:03,278 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 40289.84}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 40289.84}, {'field': 'amount', 'old_value': 19175.71, 'new_value': 48948.799999999996}, {'field': 'count', 'old_value': 371, 'new_value': 966}, {'field': 'instoreAmount', 'old_value': 18414.88, 'new_value': 48255.77}, {'field': 'instoreCount', 'old_value': 357, 'new_value': 952}]
2025-06-12 08:07:03,918 - INFO - 更新表单数据成功: FINST-8LC66GC1HN4WE69K7XLO7CVFE4WO3JKPNONBMX3
2025-06-12 08:07:03,918 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 11608.18}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11608.18}, {'field': 'amount', 'old_value': 3196.4, 'new_value': 13652.03}, {'field': 'count', 'old_value': 47, 'new_value': 263}, {'field': 'instoreAmount', 'old_value': 2386.33, 'new_value': 12841.96}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 248}]
2025-06-12 08:07:04,543 - INFO - 更新表单数据成功: FINST-DIC66I91SB2WOQNF8NDQSDMMU2R239C0OONBM8U
2025-06-12 08:07:04,543 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10818.74}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10818.74}, {'field': 'amount', 'old_value': 4215.44, 'new_value': 13357.12}, {'field': 'count', 'old_value': 83, 'new_value': 243}, {'field': 'instoreAmount', 'old_value': 3648.87, 'new_value': 12740.15}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 231}, {'field': 'onlineAmount', 'old_value': 566.57, 'new_value': 616.97}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 12}]
2025-06-12 08:07:05,168 - INFO - 更新表单数据成功: FINST-YPE66RB1A92WXO35CW6BYC1G3YDL2KPDOONBMS7
2025-06-12 08:07:05,168 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 47185.399999999994, 'new_value': 84746.04}, {'field': 'count', 'old_value': 837, 'new_value': 1498}, {'field': 'instoreAmount', 'old_value': 45578.92, 'new_value': 83139.56}, {'field': 'instoreCount', 'old_value': 800, 'new_value': 1461}]
2025-06-12 08:07:05,746 - INFO - 更新表单数据成功: FINST-2PF66CD1RQ2WO5F2A2M0EAR552RN2VFGOONBMX7
2025-06-12 08:07:05,746 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2780.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2780.7}]
2025-06-12 08:07:06,199 - INFO - 更新表单数据成功: FINST-2PF66CD1RQ2WO5F2A2M0EAR552RN2VFGOONBMQ9
2025-06-12 08:07:06,199 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10592.1, 'new_value': 10729.4}, {'field': 'amount', 'old_value': 10592.1, 'new_value': 10729.4}, {'field': 'count', 'old_value': 289, 'new_value': 290}, {'field': 'instoreAmount', 'old_value': 10023.3, 'new_value': 10160.6}, {'field': 'instoreCount', 'old_value': 275, 'new_value': 276}]
2025-06-12 08:07:06,793 - INFO - 更新表单数据成功: FINST-8SG66JA1VK2W5DZX7OAIT480NXMJ2HRLOONBMP2
2025-06-12 08:07:06,793 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11336.6, 'new_value': 11186.6}, {'field': 'dailyBillAmount', 'old_value': 11336.6, 'new_value': 11186.6}, {'field': 'amount', 'old_value': 10503.4, 'new_value': 10483.1}, {'field': 'instoreAmount', 'old_value': 10882.4, 'new_value': 10862.1}]
2025-06-12 08:07:07,246 - INFO - 更新表单数据成功: FINST-OJ666W71WN4WKGLPD8UWVC1P0OT13OEOOONBM01
2025-06-12 08:07:07,246 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 24565.29, 'new_value': 49797.95}, {'field': 'count', 'old_value': 453, 'new_value': 965}, {'field': 'instoreAmount', 'old_value': 23381.37, 'new_value': 48614.03}, {'field': 'instoreCount', 'old_value': 423, 'new_value': 935}]
2025-06-12 08:07:07,793 - INFO - 更新表单数据成功: FINST-YPE66RB1TQ2WWOMBFQYQ37T04J9C3IGWOONBM53
2025-06-12 08:07:07,793 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'amount', 'old_value': 6026.7, 'new_value': 6006.7}, {'field': 'instoreAmount', 'old_value': 6026.7, 'new_value': 6006.7}]
2025-06-12 08:07:08,246 - INFO - 更新表单数据成功: FINST-8PF66V710E4WU1QB8TOECCDV1HM2342ZOONBMV2
2025-06-12 08:07:08,246 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 3802.25, 'new_value': 15250.45}, {'field': 'count', 'old_value': 72, 'new_value': 248}, {'field': 'instoreAmount', 'old_value': 2073.4, 'new_value': 13521.6}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 219}]
2025-06-12 08:07:08,746 - INFO - 更新表单数据成功: FINST-8PF66V710E4WU1QB8TOECCDV1HM2342ZOONBMZ2
2025-06-12 08:07:08,746 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_********, 变更字段: [{'field': 'amount', 'old_value': 2801.44, 'new_value': 2811.3399999999997}, {'field': 'count', 'old_value': 59, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 1802.1, 'new_value': 1812.0}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 43}]
2025-06-12 08:07:09,277 - INFO - 更新表单数据成功: FINST-XMC66R919C3WDD4OC4X5L8NF6SG92YR1PONBM2N
2025-06-12 08:07:09,277 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2425.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2425.5}]
2025-06-12 08:07:09,824 - INFO - 更新表单数据成功: FINST-W3B66L71M82WFNUFB1B5M90MNLYJ3LP9PONBMES
2025-06-12 08:07:09,824 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 7163.16, 'new_value': 22270.1}, {'field': 'count', 'old_value': 191, 'new_value': 507}, {'field': 'instoreAmount', 'old_value': 5531.72, 'new_value': 20747.56}, {'field': 'instoreCount', 'old_value': 149, 'new_value': 465}]
2025-06-12 08:07:10,356 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3M4N2COBMKA
2025-06-12 08:07:10,356 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 935.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 935.0}]
2025-06-12 08:07:10,871 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBMKB
2025-06-12 08:07:10,871 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3795.35, 'new_value': 3775.35}, {'field': 'amount', 'old_value': 3795.35, 'new_value': 3775.35}, {'field': 'onlineAmount', 'old_value': 415.51, 'new_value': 395.51}]
2025-06-12 08:07:11,277 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBM3C
2025-06-12 08:07:11,277 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1872.33, 'new_value': 1874.33}, {'field': 'amount', 'old_value': 1872.33, 'new_value': 1874.33}, {'field': 'instoreAmount', 'old_value': 1931.72, 'new_value': 1933.72}]
2025-06-12 08:07:11,793 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBMCC
2025-06-12 08:07:11,793 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9114.63}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9114.63}]
2025-06-12 08:07:12,402 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMKD
2025-06-12 08:07:12,402 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 13679.1, 'new_value': 13659.1}, {'field': 'dailyBillAmount', 'old_value': 13679.1, 'new_value': 13659.1}, {'field': 'amount', 'old_value': 13678.6, 'new_value': 13668.6}, {'field': 'instoreAmount', 'old_value': 13678.6, 'new_value': 13668.6}]
2025-06-12 08:07:13,449 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBM6E
2025-06-12 08:07:13,449 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'instoreAmount', 'old_value': 4017.2, 'new_value': 4024.2}, {'field': 'instoreCount', 'old_value': 267, 'new_value': 269}, {'field': 'onlineAmount', 'old_value': 2157.0, 'new_value': 2150.0}, {'field': 'onlineCount', 'old_value': 160, 'new_value': 158}]
2025-06-12 08:07:13,918 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBM9E
2025-06-12 08:07:13,918 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 14784.52, 'new_value': 42318.19}, {'field': 'count', 'old_value': 327, 'new_value': 952}, {'field': 'instoreAmount', 'old_value': 12937.72, 'new_value': 40574.29}, {'field': 'instoreCount', 'old_value': 286, 'new_value': 911}]
2025-06-12 08:07:14,355 - INFO - 更新表单数据成功: FINST-2PF662C1ZP2WVTKI8DZDZ6F5YNB72MMDKRPBM2Y
2025-06-12 08:07:14,355 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 654.25, 'new_value': 687.15}, {'field': 'amount', 'old_value': 654.25, 'new_value': 687.15}, {'field': 'count', 'old_value': 52, 'new_value': 54}, {'field': 'onlineAmount', 'old_value': 736.85, 'new_value': 769.75}, {'field': 'onlineCount', 'old_value': 52, 'new_value': 54}]
2025-06-12 08:07:14,840 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA24H117RBMLD
2025-06-12 08:07:14,840 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3605.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3605.9}]
2025-06-12 08:07:15,355 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMTD
2025-06-12 08:07:15,355 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_********, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15572.0}]
2025-06-12 08:07:15,809 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMWD
2025-06-12 08:07:15,809 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'amount', 'old_value': 9.9, 'new_value': 46.65}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 9.9, 'new_value': 46.65}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-12 08:07:16,293 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMXD
2025-06-12 08:07:16,293 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_********, 变更字段: [{'field': 'amount', 'old_value': 5059.64, 'new_value': 5759.64}, {'field': 'count', 'old_value': 47, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 4963.54, 'new_value': 5663.54}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 42}]
2025-06-12 08:07:16,746 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMGE
2025-06-12 08:07:16,746 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_********, 变更字段: [{'field': 'amount', 'old_value': 2353.92, 'new_value': 2354.9300000000003}, {'field': 'count', 'old_value': 134, 'new_value': 135}, {'field': 'instoreAmount', 'old_value': 1327.9, 'new_value': 1328.91}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 72}]
2025-06-12 08:07:17,262 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMPE
2025-06-12 08:07:17,262 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_********, 变更字段: [{'field': 'amount', 'old_value': 975.9, 'new_value': 987.7}, {'field': 'count', 'old_value': 35, 'new_value': 36}, {'field': 'onlineAmount', 'old_value': 574.8, 'new_value': 586.6}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 26}]
2025-06-12 08:07:17,699 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBM3F
2025-06-12 08:07:17,699 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8647.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8647.0}, {'field': 'amount', 'old_value': 124.5, 'new_value': 360.5}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 124.5, 'new_value': 360.5}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-12 08:07:18,058 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMFF
2025-06-12 08:07:18,058 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4036.2}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4036.2}]
2025-06-12 08:07:18,527 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMIF
2025-06-12 08:07:18,527 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2573.4, 'new_value': 2580.5}, {'field': 'amount', 'old_value': 2573.4, 'new_value': 2580.5}, {'field': 'count', 'old_value': 135, 'new_value': 136}, {'field': 'onlineAmount', 'old_value': 2110.4, 'new_value': 2117.5}, {'field': 'onlineCount', 'old_value': 113, 'new_value': 114}]
2025-06-12 08:07:18,980 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMMF
2025-06-12 08:07:18,980 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2234.34, 'new_value': 2249.74}, {'field': 'amount', 'old_value': 2234.34, 'new_value': 2249.74}, {'field': 'instoreAmount', 'old_value': 2246.82, 'new_value': 2262.22}]
2025-06-12 08:07:19,418 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMTF
2025-06-12 08:07:19,418 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7034.37, 'new_value': 7077.97}, {'field': 'amount', 'old_value': 7034.37, 'new_value': 7077.97}, {'field': 'count', 'old_value': 145, 'new_value': 147}, {'field': 'instoreAmount', 'old_value': 5859.8, 'new_value': 5903.4}, {'field': 'instoreCount', 'old_value': 124, 'new_value': 126}]
2025-06-12 08:07:19,840 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMUF
2025-06-12 08:07:19,840 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'amount', 'old_value': 2451.62, 'new_value': 2564.82}, {'field': 'count', 'old_value': 132, 'new_value': 134}, {'field': 'onlineAmount', 'old_value': 2108.8, 'new_value': 2222.0}, {'field': 'onlineCount', 'old_value': 96, 'new_value': 98}]
2025-06-12 08:07:20,293 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMWF
2025-06-12 08:07:20,293 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9103.78}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9103.78}]
2025-06-12 08:07:20,746 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA26H117RBM3G
2025-06-12 08:07:20,746 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'amount', 'old_value': 1390.0, 'new_value': 2780.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 1390.0, 'new_value': 2780.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-12 08:07:21,183 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA26H117RBM7G
2025-06-12 08:07:21,183 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16142.67}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16142.67}]
2025-06-12 08:07:21,715 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA26H117RBM8G
2025-06-12 08:07:21,715 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_********, 变更字段: [{'field': 'amount', 'old_value': -2361.7, 'new_value': -2354.98}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'onlineAmount', 'old_value': 244.3, 'new_value': 251.02}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 9}]
2025-06-12 08:07:22,136 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMVF
2025-06-12 08:07:22,136 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_********, 变更字段: [{'field': 'amount', 'old_value': 16250.86, 'new_value': 16529.66}, {'field': 'count', 'old_value': 129, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 14693.73, 'new_value': 14972.53}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 81}]
2025-06-12 08:07:22,621 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMXF
2025-06-12 08:07:22,621 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 24342.37, 'new_value': 24552.37}, {'field': 'count', 'old_value': 204, 'new_value': 205}, {'field': 'instoreAmount', 'old_value': 11879.1, 'new_value': 12089.1}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 79}]
2025-06-12 08:07:23,074 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBM6G
2025-06-12 08:07:23,074 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_********, 变更字段: [{'field': 'amount', 'old_value': 3425.81, 'new_value': 3461.81}, {'field': 'count', 'old_value': 158, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 1485.01, 'new_value': 1498.01}, {'field': 'instoreCount', 'old_value': 96, 'new_value': 97}, {'field': 'onlineAmount', 'old_value': 1975.4, 'new_value': 1998.4}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 63}]
2025-06-12 08:07:23,511 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBM7G
2025-06-12 08:07:23,511 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'amount', 'old_value': 6514.74, 'new_value': 6546.839999999999}, {'field': 'count', 'old_value': 328, 'new_value': 330}, {'field': 'onlineAmount', 'old_value': 4584.48, 'new_value': 4616.58}, {'field': 'onlineCount', 'old_value': 209, 'new_value': 211}]
2025-06-12 08:07:23,933 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMAG
2025-06-12 08:07:23,933 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2463.84}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2463.84}]
2025-06-12 08:07:24,402 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMFG
2025-06-12 08:07:24,402 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 971.2, 'new_value': 1288.0}, {'field': 'amount', 'old_value': 971.2, 'new_value': 1288.0}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 971.2, 'new_value': 1288.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-06-12 08:07:24,871 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMKG
2025-06-12 08:07:24,871 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 506.0, 'new_value': 705.0}, {'field': 'amount', 'old_value': 506.0, 'new_value': 705.0}, {'field': 'count', 'old_value': 7, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 506.0, 'new_value': 705.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}]
2025-06-12 08:07:25,277 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMNG
2025-06-12 08:07:25,277 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'amount', 'old_value': 3159.6, 'new_value': 3059.6}, {'field': 'instoreAmount', 'old_value': 3159.6, 'new_value': 3059.6}]
2025-06-12 08:07:25,636 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMPG
2025-06-12 08:07:25,636 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_********, 变更字段: [{'field': 'amount', 'old_value': 7398.0, 'new_value': 57092.0}, {'field': 'count', 'old_value': 2, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 7398.0, 'new_value': 57092.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 6}]
2025-06-12 08:07:26,183 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMSG
2025-06-12 08:07:26,183 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 827.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 827.0}]
2025-06-12 08:07:26,605 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMTG
2025-06-12 08:07:26,605 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3045.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3045.9}]
2025-06-12 08:07:27,089 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMXG
2025-06-12 08:07:27,089 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 53704.55, 'new_value': 62171.37}, {'field': 'amount', 'old_value': 53704.55, 'new_value': 62171.37}, {'field': 'count', 'old_value': 12, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 53704.55, 'new_value': 62171.37}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 27}]
2025-06-12 08:07:27,527 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMYG
2025-06-12 08:07:27,527 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_********, 变更字段: [{'field': 'amount', 'old_value': 3471.11, 'new_value': 3449.21}, {'field': 'instoreAmount', 'old_value': 1564.31, 'new_value': 1751.31}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 75}, {'field': 'onlineAmount', 'old_value': 2134.8, 'new_value': 1947.8}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 39}]
2025-06-12 08:07:28,011 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMZG
2025-06-12 08:07:28,011 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 3250.02, 'new_value': 3261.2400000000002}, {'field': 'count', 'old_value': 193, 'new_value': 200}, {'field': 'onlineAmount', 'old_value': 3142.62, 'new_value': 3153.84}, {'field': 'onlineCount', 'old_value': 183, 'new_value': 190}]
2025-06-12 08:07:28,464 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBM0H
2025-06-12 08:07:28,464 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_********, 变更字段: [{'field': 'amount', 'old_value': 5903.110000000001, 'new_value': 5881.01}]
2025-06-12 08:07:28,949 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBM6H
2025-06-12 08:07:28,949 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'instoreAmount', 'old_value': 3691.98, 'new_value': 3700.48}, {'field': 'instoreCount', 'old_value': 233, 'new_value': 234}, {'field': 'onlineAmount', 'old_value': 1441.4, 'new_value': 1432.9}, {'field': 'onlineCount', 'old_value': 94, 'new_value': 93}]
2025-06-12 08:07:29,402 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBM9H
2025-06-12 08:07:29,402 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 12719.56}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12719.56}, {'field': 'amount', 'old_value': 4765.54, 'new_value': 15758.1}, {'field': 'count', 'old_value': 84, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 3653.8, 'new_value': 14667.66}, {'field': 'instoreCount', 'old_value': 68, 'new_value': 267}, {'field': 'onlineAmount', 'old_value': 1111.74, 'new_value': 1130.44}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 17}]
2025-06-12 08:07:29,824 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMAH
2025-06-12 08:07:29,824 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_********, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 14243.8, 'new_value': 20902.5}]
2025-06-12 08:07:30,292 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMBH
2025-06-12 08:07:30,292 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_********, 变更字段: [{'field': 'amount', 'old_value': 3609.42, 'new_value': 3802.13}, {'field': 'count', 'old_value': 72, 'new_value': 73}, {'field': 'onlineAmount', 'old_value': 974.98, 'new_value': 1167.69}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 18}]
2025-06-12 08:07:30,730 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMDH
2025-06-12 08:07:30,730 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3753.3, 'new_value': 3785.18}, {'field': 'amount', 'old_value': 3753.3, 'new_value': 3785.18}, {'field': 'count', 'old_value': 48, 'new_value': 49}, {'field': 'onlineAmount', 'old_value': 2103.82, 'new_value': 2135.7}, {'field': 'onlineCount', 'old_value': 29, 'new_value': 30}]
2025-06-12 08:07:31,214 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMFH
2025-06-12 08:07:31,214 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9093.61, 'new_value': 10001.63}, {'field': 'amount', 'old_value': 9093.61, 'new_value': 10001.630000000001}, {'field': 'count', 'old_value': 348, 'new_value': 405}, {'field': 'onlineAmount', 'old_value': 9258.52, 'new_value': 10196.84}, {'field': 'onlineCount', 'old_value': 348, 'new_value': 405}]
2025-06-12 08:07:31,777 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMGH
2025-06-12 08:07:31,777 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_********, 变更字段: [{'field': 'amount', 'old_value': 13039.45, 'new_value': 13076.45}, {'field': 'count', 'old_value': 112, 'new_value': 113}, {'field': 'onlineAmount', 'old_value': 1672.0, 'new_value': 1709.0}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 63}]
2025-06-12 08:07:32,199 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBMMH
2025-06-12 08:07:32,199 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_********, 变更字段: [{'field': 'amount', 'old_value': 9081.9, 'new_value': 9545.9}, {'field': 'count', 'old_value': 102, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 2903.4, 'new_value': 3367.4}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 40}]
2025-06-12 08:07:32,605 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBMRH
2025-06-12 08:07:32,605 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6066.07, 'new_value': 6109.47}, {'field': 'amount', 'old_value': 6066.07, 'new_value': 6109.469999999999}, {'field': 'count', 'old_value': 327, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 2509.32, 'new_value': 2552.72}, {'field': 'instoreCount', 'old_value': 142, 'new_value': 145}]
2025-06-12 08:07:33,042 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBMSH
2025-06-12 08:07:33,042 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2888.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2888.9}, {'field': 'amount', 'old_value': 678.97, 'new_value': 714.87}, {'field': 'count', 'old_value': 18, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 438.07, 'new_value': 473.97}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-06-12 08:07:33,480 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBMZH
2025-06-12 08:07:33,480 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_********, 变更字段: [{'field': 'amount', 'old_value': 8264.32, 'new_value': 8559.32}, {'field': 'count', 'old_value': 49, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 8245.7, 'new_value': 8540.7}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 45}]
2025-06-12 08:07:33,902 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBM1I
2025-06-12 08:07:33,902 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'amount', 'old_value': 22297.63, 'new_value': 24311.63}, {'field': 'count', 'old_value': 95, 'new_value': 96}, {'field': 'instoreAmount', 'old_value': 21130.93, 'new_value': 23144.93}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 50}]
2025-06-12 08:07:34,324 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBM9I
2025-06-12 08:07:34,324 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'amount', 'old_value': 28372.46, 'new_value': 30247.46}, {'field': 'count', 'old_value': 167, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 26631.55, 'new_value': 28506.55}, {'field': 'instoreCount', 'old_value': 139, 'new_value': 142}]
2025-06-12 08:07:34,745 - INFO - 更新表单数据成功: FINST-90E66JD16L6WV3PBBQJOE68SVXJP3OM617RBMJ2
2025-06-12 08:07:34,745 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_********, 变更字段: [{'field': 'amount', 'old_value': 1069.48, 'new_value': 1065.58}]
2025-06-12 08:07:34,964 - INFO - 正在批量插入每日数据，批次 1/66，共 100 条记录
2025-06-12 08:07:35,480 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-12 08:07:38,495 - INFO - 正在批量插入每日数据，批次 2/66，共 100 条记录
2025-06-12 08:07:38,902 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-12 08:07:41,917 - INFO - 正在批量插入每日数据，批次 3/66，共 100 条记录
2025-06-12 08:07:42,464 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-12 08:07:45,464 - INFO - 正在批量插入每日数据，批次 4/66，共 100 条记录
2025-06-12 08:07:45,948 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-12 08:07:48,964 - INFO - 正在批量插入每日数据，批次 5/66，共 100 条记录
2025-06-12 08:07:49,354 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-12 08:07:52,370 - INFO - 正在批量插入每日数据，批次 6/66，共 100 条记录
2025-06-12 08:07:52,885 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-12 08:07:55,901 - INFO - 正在批量插入每日数据，批次 7/66，共 100 条记录
2025-06-12 08:07:56,432 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-12 08:07:59,448 - INFO - 正在批量插入每日数据，批次 8/66，共 100 条记录
2025-06-12 08:07:59,979 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-12 08:08:02,995 - INFO - 正在批量插入每日数据，批次 9/66，共 100 条记录
2025-06-12 08:08:03,463 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-12 08:08:06,479 - INFO - 正在批量插入每日数据，批次 10/66，共 100 条记录
2025-06-12 08:08:06,854 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-12 08:08:09,869 - INFO - 正在批量插入每日数据，批次 11/66，共 100 条记录
2025-06-12 08:08:10,354 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-12 08:08:13,369 - INFO - 正在批量插入每日数据，批次 12/66，共 100 条记录
2025-06-12 08:08:13,791 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-12 08:08:16,807 - INFO - 正在批量插入每日数据，批次 13/66，共 100 条记录
2025-06-12 08:08:17,244 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-12 08:08:20,260 - INFO - 正在批量插入每日数据，批次 14/66，共 100 条记录
2025-06-12 08:08:20,666 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-12 08:08:23,681 - INFO - 正在批量插入每日数据，批次 15/66，共 100 条记录
2025-06-12 08:08:24,166 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-12 08:08:27,181 - INFO - 正在批量插入每日数据，批次 16/66，共 100 条记录
2025-06-12 08:08:27,603 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-12 08:08:30,619 - INFO - 正在批量插入每日数据，批次 17/66，共 100 条记录
2025-06-12 08:08:31,025 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-12 08:08:34,040 - INFO - 正在批量插入每日数据，批次 18/66，共 100 条记录
2025-06-12 08:08:34,493 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-12 08:08:37,509 - INFO - 正在批量插入每日数据，批次 19/66，共 100 条记录
2025-06-12 08:08:38,040 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-12 08:08:41,056 - INFO - 正在批量插入每日数据，批次 20/66，共 100 条记录
2025-06-12 08:08:41,556 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-12 08:08:44,571 - INFO - 正在批量插入每日数据，批次 21/66，共 100 条记录
2025-06-12 08:08:44,946 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-12 08:08:47,962 - INFO - 正在批量插入每日数据，批次 22/66，共 100 条记录
2025-06-12 08:08:48,431 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-12 08:08:51,446 - INFO - 正在批量插入每日数据，批次 23/66，共 100 条记录
2025-06-12 08:08:51,930 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-12 08:08:54,946 - INFO - 正在批量插入每日数据，批次 24/66，共 100 条记录
2025-06-12 08:08:55,337 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-12 08:08:58,352 - INFO - 正在批量插入每日数据，批次 25/66，共 100 条记录
2025-06-12 08:08:58,805 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-12 08:09:01,821 - INFO - 正在批量插入每日数据，批次 26/66，共 100 条记录
2025-06-12 08:09:02,321 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-12 08:09:05,336 - INFO - 正在批量插入每日数据，批次 27/66，共 100 条记录
2025-06-12 08:09:05,758 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-12 08:09:08,774 - INFO - 正在批量插入每日数据，批次 28/66，共 100 条记录
2025-06-12 08:09:09,164 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-12 08:09:12,180 - INFO - 正在批量插入每日数据，批次 29/66，共 100 条记录
2025-06-12 08:09:12,586 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-12 08:09:15,602 - INFO - 正在批量插入每日数据，批次 30/66，共 100 条记录
2025-06-12 08:09:16,039 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-12 08:09:19,055 - INFO - 正在批量插入每日数据，批次 31/66，共 100 条记录
2025-06-12 08:09:19,508 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-12 08:09:22,523 - INFO - 正在批量插入每日数据，批次 32/66，共 100 条记录
2025-06-12 08:09:22,961 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-12 08:09:25,976 - INFO - 正在批量插入每日数据，批次 33/66，共 100 条记录
2025-06-12 08:09:26,445 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-12 08:09:29,460 - INFO - 正在批量插入每日数据，批次 34/66，共 100 条记录
2025-06-12 08:09:29,835 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-12 08:09:32,851 - INFO - 正在批量插入每日数据，批次 35/66，共 100 条记录
2025-06-12 08:09:33,179 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-12 08:09:36,195 - INFO - 正在批量插入每日数据，批次 36/66，共 100 条记录
2025-06-12 08:09:36,663 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-12 08:09:39,679 - INFO - 正在批量插入每日数据，批次 37/66，共 100 条记录
2025-06-12 08:09:40,038 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-12 08:09:43,054 - INFO - 正在批量插入每日数据，批次 38/66，共 100 条记录
2025-06-12 08:09:43,476 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-12 08:09:46,491 - INFO - 正在批量插入每日数据，批次 39/66，共 100 条记录
2025-06-12 08:09:46,913 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-12 08:09:49,929 - INFO - 正在批量插入每日数据，批次 40/66，共 100 条记录
2025-06-12 08:09:50,413 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-12 08:09:53,428 - INFO - 正在批量插入每日数据，批次 41/66，共 100 条记录
2025-06-12 08:09:53,897 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-12 08:09:56,913 - INFO - 正在批量插入每日数据，批次 42/66，共 100 条记录
2025-06-12 08:09:57,350 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-12 08:10:00,350 - INFO - 正在批量插入每日数据，批次 43/66，共 100 条记录
2025-06-12 08:10:00,709 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-12 08:10:03,725 - INFO - 正在批量插入每日数据，批次 44/66，共 100 条记录
2025-06-12 08:10:04,225 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-12 08:10:07,241 - INFO - 正在批量插入每日数据，批次 45/66，共 100 条记录
2025-06-12 08:10:07,647 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-12 08:10:10,662 - INFO - 正在批量插入每日数据，批次 46/66，共 100 条记录
2025-06-12 08:10:11,053 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-12 08:10:14,068 - INFO - 正在批量插入每日数据，批次 47/66，共 100 条记录
2025-06-12 08:10:14,600 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-12 08:10:17,615 - INFO - 正在批量插入每日数据，批次 48/66，共 100 条记录
2025-06-12 08:10:18,146 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-12 08:10:21,162 - INFO - 正在批量插入每日数据，批次 49/66，共 100 条记录
2025-06-12 08:10:21,678 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-12 08:10:24,693 - INFO - 正在批量插入每日数据，批次 50/66，共 100 条记录
2025-06-12 08:10:25,131 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-12 08:10:28,146 - INFO - 正在批量插入每日数据，批次 51/66，共 100 条记录
2025-06-12 08:10:28,677 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-12 08:10:31,693 - INFO - 正在批量插入每日数据，批次 52/66，共 100 条记录
2025-06-12 08:10:32,083 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-12 08:10:35,099 - INFO - 正在批量插入每日数据，批次 53/66，共 100 条记录
2025-06-12 08:10:35,474 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-12 08:10:38,490 - INFO - 正在批量插入每日数据，批次 54/66，共 100 条记录
2025-06-12 08:10:38,927 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-12 08:10:41,943 - INFO - 正在批量插入每日数据，批次 55/66，共 100 条记录
2025-06-12 08:10:42,380 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-12 08:10:45,396 - INFO - 正在批量插入每日数据，批次 56/66，共 100 条记录
2025-06-12 08:10:45,833 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-12 08:10:48,849 - INFO - 正在批量插入每日数据，批次 57/66，共 100 条记录
2025-06-12 08:10:49,224 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-12 08:10:52,239 - INFO - 正在批量插入每日数据，批次 58/66，共 100 条记录
2025-06-12 08:10:52,708 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-12 08:10:55,723 - INFO - 正在批量插入每日数据，批次 59/66，共 100 条记录
2025-06-12 08:10:56,145 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-12 08:10:59,161 - INFO - 正在批量插入每日数据，批次 60/66，共 100 条记录
2025-06-12 08:10:59,583 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-12 08:11:02,598 - INFO - 正在批量插入每日数据，批次 61/66，共 100 条记录
2025-06-12 08:11:03,067 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-12 08:11:06,082 - INFO - 正在批量插入每日数据，批次 62/66，共 100 条记录
2025-06-12 08:11:06,551 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-12 08:11:09,567 - INFO - 正在批量插入每日数据，批次 63/66，共 100 条记录
2025-06-12 08:11:09,926 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-12 08:11:12,942 - INFO - 正在批量插入每日数据，批次 64/66，共 100 条记录
2025-06-12 08:11:13,363 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-12 08:11:16,379 - INFO - 正在批量插入每日数据，批次 65/66，共 100 条记录
2025-06-12 08:11:16,832 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-12 08:11:19,863 - INFO - 正在批量插入每日数据，批次 66/66，共 20 条记录
2025-06-12 08:11:20,066 - INFO - 批量插入每日数据成功，批次 66，20 条记录
2025-06-12 08:11:23,082 - INFO - 批量插入每日数据完成: 总计 6520 条，成功 6520 条，失败 0 条
2025-06-12 08:11:23,082 - INFO - 批量插入日销售数据完成，共 6520 条记录
2025-06-12 08:11:23,082 - INFO - 日销售数据同步完成！更新: 79 条，插入: 6520 条，错误: 0 条，跳过: 6228 条
2025-06-12 08:11:23,082 - INFO - 正在获取宜搭月销售表单数据...
2025-06-12 08:11:23,082 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-12 08:11:23,082 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-12 08:11:23,082 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-12 08:11:23,082 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:23,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:23,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:23,676 - INFO - API请求耗时: 594ms
2025-06-12 08:11:23,691 - INFO - Response - Page 1
2025-06-12 08:11:23,691 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:11:23,691 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:11:23,691 - WARNING - 月度分段 1 查询返回空数据
2025-06-12 08:11:23,691 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-12 08:11:23,691 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-12 08:11:23,691 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:23,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:23,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:23,894 - INFO - API请求耗时: 203ms
2025-06-12 08:11:23,894 - INFO - Response - Page 1
2025-06-12 08:11:23,894 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:11:23,894 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:11:23,894 - WARNING - 单月查询返回空数据: 2024-06
2025-06-12 08:11:24,410 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-12 08:11:24,410 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:24,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:24,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:24,629 - INFO - API请求耗时: 219ms
2025-06-12 08:11:24,629 - INFO - Response - Page 1
2025-06-12 08:11:24,629 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:11:24,629 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:11:24,629 - WARNING - 单月查询返回空数据: 2024-07
2025-06-12 08:11:25,144 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-12 08:11:25,144 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:25,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:25,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:25,660 - INFO - API请求耗时: 516ms
2025-06-12 08:11:25,660 - INFO - Response - Page 1
2025-06-12 08:11:25,660 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:11:25,660 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:11:25,660 - WARNING - 单月查询返回空数据: 2024-08
2025-06-12 08:11:27,191 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-12 08:11:27,191 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-12 08:11:27,191 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:27,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:27,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:27,410 - INFO - API请求耗时: 219ms
2025-06-12 08:11:27,410 - INFO - Response - Page 1
2025-06-12 08:11:27,410 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:11:27,410 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:11:27,410 - WARNING - 月度分段 2 查询返回空数据
2025-06-12 08:11:27,410 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-12 08:11:27,410 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-12 08:11:27,410 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:27,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:27,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:27,613 - INFO - API请求耗时: 203ms
2025-06-12 08:11:27,613 - INFO - Response - Page 1
2025-06-12 08:11:27,613 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:11:27,613 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:11:27,613 - WARNING - 单月查询返回空数据: 2024-09
2025-06-12 08:11:28,129 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-12 08:11:28,129 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:28,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:28,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:28,332 - INFO - API请求耗时: 203ms
2025-06-12 08:11:28,332 - INFO - Response - Page 1
2025-06-12 08:11:28,332 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:11:28,332 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:11:28,332 - WARNING - 单月查询返回空数据: 2024-10
2025-06-12 08:11:28,847 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-12 08:11:28,847 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:28,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:28,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:29,066 - INFO - API请求耗时: 219ms
2025-06-12 08:11:29,066 - INFO - Response - Page 1
2025-06-12 08:11:29,066 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-12 08:11:29,066 - INFO - 查询完成，共获取到 0 条记录
2025-06-12 08:11:29,066 - WARNING - 单月查询返回空数据: 2024-11
2025-06-12 08:11:30,597 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-12 08:11:30,597 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-12 08:11:30,597 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:30,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:30,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:31,253 - INFO - API请求耗时: 656ms
2025-06-12 08:11:31,253 - INFO - Response - Page 1
2025-06-12 08:11:31,253 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:11:31,753 - INFO - Request Parameters - Page 2:
2025-06-12 08:11:31,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:31,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:32,269 - INFO - API请求耗时: 516ms
2025-06-12 08:11:32,269 - INFO - Response - Page 2
2025-06-12 08:11:32,269 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:11:32,785 - INFO - Request Parameters - Page 3:
2025-06-12 08:11:32,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:32,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:33,316 - INFO - API请求耗时: 531ms
2025-06-12 08:11:33,316 - INFO - Response - Page 3
2025-06-12 08:11:33,316 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:11:33,831 - INFO - Request Parameters - Page 4:
2025-06-12 08:11:33,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:33,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:34,410 - INFO - API请求耗时: 578ms
2025-06-12 08:11:34,410 - INFO - Response - Page 4
2025-06-12 08:11:34,410 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:11:34,910 - INFO - Request Parameters - Page 5:
2025-06-12 08:11:34,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:34,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:35,456 - INFO - API请求耗时: 547ms
2025-06-12 08:11:35,456 - INFO - Response - Page 5
2025-06-12 08:11:35,456 - INFO - 第 5 页获取到 94 条记录
2025-06-12 08:11:35,456 - INFO - 查询完成，共获取到 494 条记录
2025-06-12 08:11:35,456 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-12 08:11:36,472 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-12 08:11:36,472 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-12 08:11:36,472 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:36,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:36,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:37,019 - INFO - API请求耗时: 547ms
2025-06-12 08:11:37,019 - INFO - Response - Page 1
2025-06-12 08:11:37,019 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:11:37,534 - INFO - Request Parameters - Page 2:
2025-06-12 08:11:37,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:37,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:38,050 - INFO - API请求耗时: 516ms
2025-06-12 08:11:38,050 - INFO - Response - Page 2
2025-06-12 08:11:38,050 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:11:38,550 - INFO - Request Parameters - Page 3:
2025-06-12 08:11:38,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:38,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:39,159 - INFO - API请求耗时: 609ms
2025-06-12 08:11:39,159 - INFO - Response - Page 3
2025-06-12 08:11:39,159 - INFO - 第 3 页获取到 100 条记录
2025-06-12 08:11:39,659 - INFO - Request Parameters - Page 4:
2025-06-12 08:11:39,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:39,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:40,253 - INFO - API请求耗时: 594ms
2025-06-12 08:11:40,253 - INFO - Response - Page 4
2025-06-12 08:11:40,253 - INFO - 第 4 页获取到 100 条记录
2025-06-12 08:11:40,769 - INFO - Request Parameters - Page 5:
2025-06-12 08:11:40,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:40,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:41,331 - INFO - API请求耗时: 562ms
2025-06-12 08:11:41,331 - INFO - Response - Page 5
2025-06-12 08:11:41,331 - INFO - 第 5 页获取到 100 条记录
2025-06-12 08:11:41,847 - INFO - Request Parameters - Page 6:
2025-06-12 08:11:41,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:41,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:42,519 - INFO - API请求耗时: 672ms
2025-06-12 08:11:42,519 - INFO - Response - Page 6
2025-06-12 08:11:42,519 - INFO - 第 6 页获取到 100 条记录
2025-06-12 08:11:43,034 - INFO - Request Parameters - Page 7:
2025-06-12 08:11:43,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:43,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:43,581 - INFO - API请求耗时: 547ms
2025-06-12 08:11:43,581 - INFO - Response - Page 7
2025-06-12 08:11:43,581 - INFO - 第 7 页获取到 98 条记录
2025-06-12 08:11:43,581 - INFO - 查询完成，共获取到 698 条记录
2025-06-12 08:11:43,581 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-12 08:11:44,581 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-12 08:11:44,581 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-12 08:11:44,581 - INFO - Request Parameters - Page 1:
2025-06-12 08:11:44,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:44,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:45,128 - INFO - API请求耗时: 547ms
2025-06-12 08:11:45,128 - INFO - Response - Page 1
2025-06-12 08:11:45,128 - INFO - 第 1 页获取到 100 条记录
2025-06-12 08:11:45,628 - INFO - Request Parameters - Page 2:
2025-06-12 08:11:45,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:45,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:46,206 - INFO - API请求耗时: 578ms
2025-06-12 08:11:46,206 - INFO - Response - Page 2
2025-06-12 08:11:46,206 - INFO - 第 2 页获取到 100 条记录
2025-06-12 08:11:46,706 - INFO - Request Parameters - Page 3:
2025-06-12 08:11:46,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 08:11:46,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 08:11:47,003 - INFO - API请求耗时: 297ms
2025-06-12 08:11:47,003 - INFO - Response - Page 3
2025-06-12 08:11:47,003 - INFO - 第 3 页获取到 11 条记录
2025-06-12 08:11:47,003 - INFO - 查询完成，共获取到 211 条记录
2025-06-12 08:11:47,003 - INFO - 月度分段 5 查询成功，获取到 211 条记录
2025-06-12 08:11:48,019 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1403 条记录，失败 0 次
2025-06-12 08:11:48,019 - INFO - 成功获取宜搭月销售表单数据，共 1403 条记录
2025-06-12 08:11:48,019 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-12 08:11:48,019 - INFO - 正在从MySQL获取月度汇总数据...
2025-06-12 08:11:48,065 - INFO - 成功获取MySQL月度汇总数据，共 1410 条记录
2025-06-12 08:11:48,784 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250612.xlsx
2025-06-12 08:11:48,847 - INFO - 成功创建宜搭月销售数据索引，共 1403 条记录
2025-06-12 08:11:48,878 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:48,878 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:49,362 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-12 08:11:49,362 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 270344.66, 'new_value': 297874.27}, {'field': 'dailyBillAmount', 'old_value': 270344.66, 'new_value': 297874.27}, {'field': 'amount', 'old_value': 202166.96, 'new_value': 222187.36}, {'field': 'count', 'old_value': 988, 'new_value': 1083}, {'field': 'instoreAmount', 'old_value': 202166.96, 'new_value': 222187.36}, {'field': 'instoreCount', 'old_value': 988, 'new_value': 1083}]
2025-06-12 08:11:49,362 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:49,800 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-12 08:11:49,815 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 190463.72, 'new_value': 203958.36}, {'field': 'dailyBillAmount', 'old_value': 190463.72, 'new_value': 203958.36}, {'field': 'amount', 'old_value': 293847.1, 'new_value': 312848.1}, {'field': 'count', 'old_value': 1086, 'new_value': 1172}, {'field': 'instoreAmount', 'old_value': 292524.0, 'new_value': 311525.0}, {'field': 'instoreCount', 'old_value': 1077, 'new_value': 1163}]
2025-06-12 08:11:49,815 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:50,253 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-12 08:11:50,253 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22986.4, 'new_value': 23355.1}, {'field': 'dailyBillAmount', 'old_value': 22986.4, 'new_value': 23355.1}, {'field': 'amount', 'old_value': 27722.0, 'new_value': 28548.4}, {'field': 'count', 'old_value': 122, 'new_value': 135}, {'field': 'instoreAmount', 'old_value': 10977.9, 'new_value': 11228.3}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 13}, {'field': 'onlineAmount', 'old_value': 18715.5, 'new_value': 19291.5}, {'field': 'onlineCount', 'old_value': 111, 'new_value': 122}]
2025-06-12 08:11:50,268 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:50,784 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-12 08:11:50,784 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'count', 'old_value': 47, 'new_value': 48}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 48}]
2025-06-12 08:11:50,784 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:51,300 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-12 08:11:51,300 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 257446.39, 'new_value': 278339.35}, {'field': 'dailyBillAmount', 'old_value': 257446.39, 'new_value': 278339.35}, {'field': 'amount', 'old_value': 52120.45, 'new_value': 56165.44}, {'field': 'count', 'old_value': 262, 'new_value': 290}, {'field': 'instoreAmount', 'old_value': 52120.45, 'new_value': 56165.44}, {'field': 'instoreCount', 'old_value': 262, 'new_value': 290}]
2025-06-12 08:11:51,300 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:51,722 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-12 08:11:51,722 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 285953.31, 'new_value': 312139.34}, {'field': 'dailyBillAmount', 'old_value': 285953.31, 'new_value': 312139.34}, {'field': 'amount', 'old_value': 167989.22, 'new_value': 181269.17}, {'field': 'count', 'old_value': 1210, 'new_value': 1320}, {'field': 'instoreAmount', 'old_value': 150868.33, 'new_value': 162529.73}, {'field': 'instoreCount', 'old_value': 645, 'new_value': 701}, {'field': 'onlineAmount', 'old_value': 19521.45, 'new_value': 21158.95}, {'field': 'onlineCount', 'old_value': 565, 'new_value': 619}]
2025-06-12 08:11:51,722 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:52,206 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-12 08:11:52,206 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 79576.53, 'new_value': 86266.82}, {'field': 'dailyBillAmount', 'old_value': 79576.53, 'new_value': 86266.82}, {'field': 'amount', 'old_value': 2338.4, 'new_value': 2473.2}, {'field': 'count', 'old_value': 29, 'new_value': 32}, {'field': 'onlineAmount', 'old_value': 2338.4, 'new_value': 2473.2}, {'field': 'onlineCount', 'old_value': 29, 'new_value': 32}]
2025-06-12 08:11:52,222 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:52,628 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-12 08:11:52,628 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 192592.98, 'new_value': 209454.63}, {'field': 'dailyBillAmount', 'old_value': 192592.98, 'new_value': 209454.63}, {'field': 'amount', 'old_value': 109133.5, 'new_value': 122309.0}, {'field': 'count', 'old_value': 1019, 'new_value': 1156}, {'field': 'instoreAmount', 'old_value': 49584.9, 'new_value': 55451.4}, {'field': 'instoreCount', 'old_value': 405, 'new_value': 460}, {'field': 'onlineAmount', 'old_value': 59669.8, 'new_value': 66979.7}, {'field': 'onlineCount', 'old_value': 614, 'new_value': 696}]
2025-06-12 08:11:52,628 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:53,065 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-12 08:11:53,065 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 117811.97, 'new_value': 126616.93}, {'field': 'dailyBillAmount', 'old_value': 117811.97, 'new_value': 126616.93}, {'field': 'amount', 'old_value': 119455.44, 'new_value': 128321.14}, {'field': 'count', 'old_value': 778, 'new_value': 835}, {'field': 'instoreAmount', 'old_value': 110031.13, 'new_value': 118336.53}, {'field': 'instoreCount', 'old_value': 650, 'new_value': 698}, {'field': 'onlineAmount', 'old_value': 9643.21, 'new_value': 10203.51}, {'field': 'onlineCount', 'old_value': 128, 'new_value': 137}]
2025-06-12 08:11:53,159 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-12 08:11:53,581 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62SI3NXX9M5A
2025-06-12 08:11:53,581 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-04, 变更字段: [{'field': 'amount', 'old_value': 1216755.32, 'new_value': 1218280.8}, {'field': 'count', 'old_value': 1530, 'new_value': 1531}, {'field': 'instoreAmount', 'old_value': 1216755.32, 'new_value': 1218281.32}, {'field': 'instoreCount', 'old_value': 1530, 'new_value': 1531}]
2025-06-12 08:11:53,612 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:54,112 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMIJ
2025-06-12 08:11:54,112 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33686.2, 'new_value': 34540.3}, {'field': 'amount', 'old_value': 33686.2, 'new_value': 34540.3}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 33686.2, 'new_value': 34540.3}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-06-12 08:11:54,128 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:54,565 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-12 08:11:54,565 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 338059.8, 'new_value': 362840.32}, {'field': 'count', 'old_value': 447, 'new_value': 489}, {'field': 'instoreAmount', 'old_value': 338059.8, 'new_value': 362840.32}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 489}]
2025-06-12 08:11:54,565 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:55,034 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-12 08:11:55,034 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 143801.84, 'new_value': 156587.84}, {'field': 'dailyBillAmount', 'old_value': 143801.84, 'new_value': 156587.84}, {'field': 'amount', 'old_value': 155777.84, 'new_value': 168563.84}, {'field': 'count', 'old_value': 495, 'new_value': 541}, {'field': 'instoreAmount', 'old_value': 155777.84, 'new_value': 168563.84}, {'field': 'instoreCount', 'old_value': 495, 'new_value': 541}]
2025-06-12 08:11:55,034 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:55,503 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSJ
2025-06-12 08:11:55,503 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11050.93, 'new_value': 12833.53}, {'field': 'dailyBillAmount', 'old_value': 11050.93, 'new_value': 12833.53}, {'field': 'amount', 'old_value': 11050.93, 'new_value': 12833.53}, {'field': 'count', 'old_value': 16, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 11776.56, 'new_value': 13559.16}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 18}]
2025-06-12 08:11:55,503 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:55,940 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-12 08:11:55,940 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'amount', 'old_value': 27665.61, 'new_value': 39379.62}, {'field': 'count', 'old_value': 188, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 27665.61, 'new_value': 39379.62}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 219}]
2025-06-12 08:11:55,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:56,456 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-12 08:11:56,456 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75703.2, 'new_value': 82082.2}, {'field': 'amount', 'old_value': 75703.2, 'new_value': 82082.2}, {'field': 'count', 'old_value': 385, 'new_value': 422}, {'field': 'instoreAmount', 'old_value': 75703.2, 'new_value': 82082.2}, {'field': 'instoreCount', 'old_value': 385, 'new_value': 422}]
2025-06-12 08:11:56,456 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:56,831 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-12 08:11:56,831 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 117112.0, 'new_value': 128158.0}, {'field': 'amount', 'old_value': 117112.0, 'new_value': 128158.0}, {'field': 'count', 'old_value': 23, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 117112.0, 'new_value': 128158.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 27}]
2025-06-12 08:11:56,831 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:57,221 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-12 08:11:57,221 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15128.73, 'new_value': 15861.95}, {'field': 'amount', 'old_value': 15128.73, 'new_value': 15861.95}, {'field': 'count', 'old_value': 139, 'new_value': 147}, {'field': 'instoreAmount', 'old_value': 15128.73, 'new_value': 15861.95}, {'field': 'instoreCount', 'old_value': 139, 'new_value': 147}]
2025-06-12 08:11:57,221 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:57,690 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-12 08:11:57,690 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 124081.9, 'new_value': 134341.8}, {'field': 'dailyBillAmount', 'old_value': 81736.45, 'new_value': 97308.45}, {'field': 'amount', 'old_value': 124081.9, 'new_value': 134341.8}, {'field': 'count', 'old_value': 201, 'new_value': 222}, {'field': 'instoreAmount', 'old_value': 124081.9, 'new_value': 134341.8}, {'field': 'instoreCount', 'old_value': 201, 'new_value': 222}]
2025-06-12 08:11:57,690 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:58,174 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-12 08:11:58,174 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15255.35, 'new_value': 16993.72}, {'field': 'dailyBillAmount', 'old_value': 15255.35, 'new_value': 16993.72}, {'field': 'amount', 'old_value': 17193.14, 'new_value': 19131.21}, {'field': 'count', 'old_value': 487, 'new_value': 542}, {'field': 'instoreAmount', 'old_value': 17193.14, 'new_value': 19131.21}, {'field': 'instoreCount', 'old_value': 487, 'new_value': 542}]
2025-06-12 08:11:58,174 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:58,659 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-12 08:11:58,659 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 78756.58, 'new_value': 85870.31}, {'field': 'dailyBillAmount', 'old_value': 69208.58, 'new_value': 76322.31}, {'field': 'amount', 'old_value': 78756.58, 'new_value': 85870.31}, {'field': 'count', 'old_value': 331, 'new_value': 371}, {'field': 'instoreAmount', 'old_value': 78756.58, 'new_value': 85870.31}, {'field': 'instoreCount', 'old_value': 331, 'new_value': 371}]
2025-06-12 08:11:58,659 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:59,081 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-12 08:11:59,081 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1760.0, 'new_value': 1796.1}, {'field': 'count', 'old_value': 20, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 1760.9, 'new_value': 1797.65}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 21}]
2025-06-12 08:11:59,096 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:59,518 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-12 08:11:59,518 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 60140.46, 'new_value': 67088.1}, {'field': 'dailyBillAmount', 'old_value': 60140.46, 'new_value': 67088.1}, {'field': 'amount', 'old_value': 30381.58, 'new_value': 34997.14}, {'field': 'count', 'old_value': 331, 'new_value': 371}, {'field': 'instoreAmount', 'old_value': 29205.3, 'new_value': 33559.2}, {'field': 'instoreCount', 'old_value': 278, 'new_value': 310}, {'field': 'onlineAmount', 'old_value': 1810.49, 'new_value': 2072.79}, {'field': 'onlineCount', 'old_value': 53, 'new_value': 61}]
2025-06-12 08:11:59,518 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:11:59,940 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-12 08:11:59,940 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 78223.96, 'new_value': 85214.3}, {'field': 'dailyBillAmount', 'old_value': 78223.96, 'new_value': 85214.3}, {'field': 'amount', 'old_value': 94018.6, 'new_value': 100874.5}, {'field': 'count', 'old_value': 521, 'new_value': 565}, {'field': 'instoreAmount', 'old_value': 94018.6, 'new_value': 100874.5}, {'field': 'instoreCount', 'old_value': 521, 'new_value': 565}]
2025-06-12 08:11:59,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:00,424 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-12 08:12:00,424 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 129799.31, 'new_value': 140317.78}, {'field': 'dailyBillAmount', 'old_value': 129799.31, 'new_value': 140317.78}, {'field': 'amount', 'old_value': 71219.54, 'new_value': 76835.34}, {'field': 'count', 'old_value': 287, 'new_value': 318}, {'field': 'instoreAmount', 'old_value': 72401.2, 'new_value': 78242.2}, {'field': 'instoreCount', 'old_value': 287, 'new_value': 318}]
2025-06-12 08:12:00,424 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:00,878 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-12 08:12:00,878 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 140753.77, 'new_value': 154196.38}, {'field': 'dailyBillAmount', 'old_value': 140753.77, 'new_value': 154196.38}, {'field': 'amount', 'old_value': 64721.23, 'new_value': 70570.16}, {'field': 'count', 'old_value': 674, 'new_value': 722}, {'field': 'instoreAmount', 'old_value': 31392.08, 'new_value': 34703.91}, {'field': 'instoreCount', 'old_value': 238, 'new_value': 254}, {'field': 'onlineAmount', 'old_value': 33329.15, 'new_value': 35866.25}, {'field': 'onlineCount', 'old_value': 436, 'new_value': 468}]
2025-06-12 08:12:00,878 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:01,377 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-12 08:12:01,377 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45532.82, 'new_value': 48147.82}, {'field': 'dailyBillAmount', 'old_value': 45532.82, 'new_value': 48147.82}, {'field': 'amount', 'old_value': 38417.98, 'new_value': 38813.98}, {'field': 'count', 'old_value': 161, 'new_value': 163}, {'field': 'instoreAmount', 'old_value': 38263.3, 'new_value': 38659.3}, {'field': 'instoreCount', 'old_value': 158, 'new_value': 160}]
2025-06-12 08:12:01,377 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:01,831 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-12 08:12:01,831 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26605.38, 'new_value': 29827.93}, {'field': 'amount', 'old_value': 26605.38, 'new_value': 29827.93}, {'field': 'count', 'old_value': 1236, 'new_value': 1367}, {'field': 'instoreAmount', 'old_value': 28147.9, 'new_value': 31591.46}, {'field': 'instoreCount', 'old_value': 1236, 'new_value': 1367}]
2025-06-12 08:12:01,831 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:02,237 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAI
2025-06-12 08:12:02,237 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42248.41, 'new_value': 78753.48}, {'field': 'dailyBillAmount', 'old_value': 42248.41, 'new_value': 78753.48}]
2025-06-12 08:12:02,237 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:02,721 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-12 08:12:02,721 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 57076.0, 'new_value': 60624.0}, {'field': 'count', 'old_value': 76, 'new_value': 79}, {'field': 'instoreAmount', 'old_value': 57076.0, 'new_value': 60624.0}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 79}]
2025-06-12 08:12:02,721 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:03,221 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-12 08:12:03,221 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 432734.23, 'new_value': 463739.32}, {'field': 'dailyBillAmount', 'old_value': 432734.23, 'new_value': 463739.32}, {'field': 'amount', 'old_value': -194589.06, 'new_value': -209247.64}, {'field': 'count', 'old_value': 410, 'new_value': 457}, {'field': 'instoreAmount', 'old_value': 234166.4, 'new_value': 254008.34}, {'field': 'instoreCount', 'old_value': 410, 'new_value': 457}]
2025-06-12 08:12:03,221 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:03,690 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-12 08:12:03,690 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 145493.0, 'new_value': 158628.0}, {'field': 'amount', 'old_value': 145493.0, 'new_value': 158628.0}, {'field': 'count', 'old_value': 544, 'new_value': 579}, {'field': 'instoreAmount', 'old_value': 145493.0, 'new_value': 158628.0}, {'field': 'instoreCount', 'old_value': 544, 'new_value': 579}]
2025-06-12 08:12:03,690 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:04,159 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-12 08:12:04,159 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 149916.02, 'new_value': 163877.76}, {'field': 'dailyBillAmount', 'old_value': 137750.42, 'new_value': 150558.36}, {'field': 'amount', 'old_value': 149916.02, 'new_value': 163877.76}, {'field': 'count', 'old_value': 474, 'new_value': 527}, {'field': 'instoreAmount', 'old_value': 149916.02, 'new_value': 163877.76}, {'field': 'instoreCount', 'old_value': 474, 'new_value': 527}]
2025-06-12 08:12:04,159 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:04,659 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-12 08:12:04,659 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58642.65, 'new_value': 67289.65}, {'field': 'dailyBillAmount', 'old_value': 58642.65, 'new_value': 67289.65}, {'field': 'amount', 'old_value': 5691.2, 'new_value': 5926.7}, {'field': 'count', 'old_value': 19, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 6013.83, 'new_value': 6249.83}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 20}]
2025-06-12 08:12:04,659 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:05,096 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-12 08:12:05,096 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46348.7, 'new_value': 49954.6}, {'field': 'dailyBillAmount', 'old_value': 46348.7, 'new_value': 49954.6}, {'field': 'amount', 'old_value': 14071.1, 'new_value': 15004.1}, {'field': 'count', 'old_value': 65, 'new_value': 68}, {'field': 'instoreAmount', 'old_value': 14071.1, 'new_value': 15004.1}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 68}]
2025-06-12 08:12:05,112 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:05,596 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-12 08:12:05,596 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 57610.15, 'new_value': 64498.33}, {'field': 'amount', 'old_value': 57610.15, 'new_value': 64497.98}, {'field': 'count', 'old_value': 1887, 'new_value': 2127}, {'field': 'instoreAmount', 'old_value': 51401.38, 'new_value': 57841.05}, {'field': 'instoreCount', 'old_value': 1731, 'new_value': 1956}, {'field': 'onlineAmount', 'old_value': 6208.77, 'new_value': 6657.28}, {'field': 'onlineCount', 'old_value': 156, 'new_value': 171}]
2025-06-12 08:12:05,596 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:06,002 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-12 08:12:06,002 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 102769.83, 'new_value': 107415.83}, {'field': 'dailyBillAmount', 'old_value': 102728.0, 'new_value': 107374.0}, {'field': 'amount', 'old_value': 81752.19, 'new_value': 83968.19}, {'field': 'count', 'old_value': 90, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 80936.0, 'new_value': 83152.0}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 92}]
2025-06-12 08:12:06,002 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:06,471 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-12 08:12:06,471 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 169821.69, 'new_value': 188455.67}, {'field': 'dailyBillAmount', 'old_value': 169821.69, 'new_value': 188455.67}, {'field': 'amount', 'old_value': 169821.69, 'new_value': 188455.67}, {'field': 'count', 'old_value': 168, 'new_value': 188}, {'field': 'instoreAmount', 'old_value': 169821.69, 'new_value': 188455.67}, {'field': 'instoreCount', 'old_value': 168, 'new_value': 188}]
2025-06-12 08:12:06,471 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:06,924 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-12 08:12:06,924 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35156.5, 'new_value': 38854.5}, {'field': 'dailyBillAmount', 'old_value': 35156.5, 'new_value': 38854.5}, {'field': 'amount', 'old_value': 39883.6, 'new_value': 43581.6}, {'field': 'count', 'old_value': 118, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 39883.6, 'new_value': 43581.6}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 129}]
2025-06-12 08:12:06,924 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:07,487 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDJ
2025-06-12 08:12:07,487 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10828.0, 'new_value': 11275.0}, {'field': 'dailyBillAmount', 'old_value': 10828.0, 'new_value': 11275.0}, {'field': 'amount', 'old_value': 10828.0, 'new_value': 11275.0}, {'field': 'count', 'old_value': 32, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 11215.0, 'new_value': 12260.0}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 36}]
2025-06-12 08:12:07,487 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:07,924 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-12 08:12:07,924 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 81404.23, 'new_value': 86976.04}, {'field': 'dailyBillAmount', 'old_value': 81404.23, 'new_value': 86976.04}, {'field': 'amount', 'old_value': 105736.7, 'new_value': 113052.6}, {'field': 'count', 'old_value': 560, 'new_value': 595}, {'field': 'instoreAmount', 'old_value': 106192.7, 'new_value': 113617.6}, {'field': 'instoreCount', 'old_value': 560, 'new_value': 595}]
2025-06-12 08:12:07,924 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:08,299 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-12 08:12:08,299 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67545.35, 'new_value': 76645.3}, {'field': 'dailyBillAmount', 'old_value': 67545.35, 'new_value': 76645.3}, {'field': 'amount', 'old_value': 7774.99, 'new_value': 10073.71}, {'field': 'count', 'old_value': 591, 'new_value': 739}, {'field': 'instoreAmount', 'old_value': 8989.19, 'new_value': 11325.41}, {'field': 'instoreCount', 'old_value': 591, 'new_value': 739}]
2025-06-12 08:12:08,299 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:08,768 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOI
2025-06-12 08:12:08,768 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55953.21, 'new_value': 56568.81}, {'field': 'amount', 'old_value': 55952.81, 'new_value': 56567.31}, {'field': 'count', 'old_value': 1561, 'new_value': 1569}, {'field': 'instoreAmount', 'old_value': 53485.65, 'new_value': 54039.25}, {'field': 'instoreCount', 'old_value': 1480, 'new_value': 1486}, {'field': 'onlineAmount', 'old_value': 3583.22, 'new_value': 3645.22}, {'field': 'onlineCount', 'old_value': 81, 'new_value': 83}]
2025-06-12 08:12:08,768 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:09,268 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-12 08:12:09,268 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 102604.66, 'new_value': 108944.83}, {'field': 'dailyBillAmount', 'old_value': 102604.66, 'new_value': 108944.83}, {'field': 'amount', 'old_value': 102872.93, 'new_value': 109213.1}, {'field': 'count', 'old_value': 277, 'new_value': 295}, {'field': 'instoreAmount', 'old_value': 102872.93, 'new_value': 109213.1}, {'field': 'instoreCount', 'old_value': 277, 'new_value': 295}]
2025-06-12 08:12:09,283 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:09,674 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-12 08:12:09,674 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 98904.25, 'new_value': 105171.2}, {'field': 'dailyBillAmount', 'old_value': 98904.25, 'new_value': 105171.2}, {'field': 'amount', 'old_value': 28892.7, 'new_value': 29308.7}, {'field': 'count', 'old_value': 71, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 28892.7, 'new_value': 29308.7}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 72}]
2025-06-12 08:12:09,674 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:10,065 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-12 08:12:10,080 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 141085.18, 'new_value': 151443.55}, {'field': 'dailyBillAmount', 'old_value': 141085.18, 'new_value': 151443.55}, {'field': 'amount', 'old_value': 55003.4, 'new_value': 60371.8}, {'field': 'count', 'old_value': 219, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 55003.4, 'new_value': 60371.8}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 240}]
2025-06-12 08:12:10,080 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:10,471 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-12 08:12:10,471 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38992.83, 'new_value': 43046.65}, {'field': 'dailyBillAmount', 'old_value': 38992.83, 'new_value': 43046.65}, {'field': 'amount', 'old_value': 8092.27, 'new_value': 9003.49}, {'field': 'count', 'old_value': 303, 'new_value': 340}, {'field': 'instoreAmount', 'old_value': 2397.0, 'new_value': 2673.7}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 67}, {'field': 'onlineAmount', 'old_value': 5803.39, 'new_value': 6492.91}, {'field': 'onlineCount', 'old_value': 243, 'new_value': 273}]
2025-06-12 08:12:10,471 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:10,924 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-12 08:12:10,940 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 63425.56, 'new_value': 70380.0}, {'field': 'dailyBillAmount', 'old_value': 63425.56, 'new_value': 70380.0}, {'field': 'amount', 'old_value': 12236.06, 'new_value': 13056.48}, {'field': 'count', 'old_value': 309, 'new_value': 341}, {'field': 'instoreAmount', 'old_value': 9836.43, 'new_value': 10474.33}, {'field': 'instoreCount', 'old_value': 250, 'new_value': 275}, {'field': 'onlineAmount', 'old_value': 2423.78, 'new_value': 2644.4}, {'field': 'onlineCount', 'old_value': 59, 'new_value': 66}]
2025-06-12 08:12:10,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:11,643 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-12 08:12:11,643 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12580.69, 'new_value': 12635.69}, {'field': 'dailyBillAmount', 'old_value': 12580.69, 'new_value': 12635.69}, {'field': 'amount', 'old_value': 9846.6, 'new_value': 9901.6}, {'field': 'count', 'old_value': 362, 'new_value': 372}, {'field': 'instoreAmount', 'old_value': 9946.6, 'new_value': 10001.6}, {'field': 'instoreCount', 'old_value': 362, 'new_value': 372}]
2025-06-12 08:12:11,643 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:12,127 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-12 08:12:12,127 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'amount', 'old_value': 11217.21, 'new_value': 12585.42}, {'field': 'count', 'old_value': 601, 'new_value': 649}, {'field': 'instoreAmount', 'old_value': 3709.87, 'new_value': 4177.87}, {'field': 'instoreCount', 'old_value': 150, 'new_value': 162}, {'field': 'onlineAmount', 'old_value': 7781.84, 'new_value': 8682.05}, {'field': 'onlineCount', 'old_value': 451, 'new_value': 487}]
2025-06-12 08:12:12,127 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:12,580 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-12 08:12:12,580 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67161.0, 'new_value': 73592.8}, {'field': 'dailyBillAmount', 'old_value': 67161.0, 'new_value': 73592.8}, {'field': 'amount', 'old_value': 80150.0, 'new_value': 87450.8}, {'field': 'count', 'old_value': 297, 'new_value': 322}, {'field': 'instoreAmount', 'old_value': 81376.0, 'new_value': 88676.8}, {'field': 'instoreCount', 'old_value': 297, 'new_value': 322}]
2025-06-12 08:12:12,580 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:12,971 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-12 08:12:12,971 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38076.92, 'new_value': 40348.2}, {'field': 'dailyBillAmount', 'old_value': 38076.92, 'new_value': 40348.2}, {'field': 'amount', 'old_value': 39288.88, 'new_value': 41670.94}, {'field': 'count', 'old_value': 194, 'new_value': 205}, {'field': 'instoreAmount', 'old_value': 37232.52, 'new_value': 39543.52}, {'field': 'instoreCount', 'old_value': 171, 'new_value': 181}, {'field': 'onlineAmount', 'old_value': 2056.36, 'new_value': 2127.42}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 24}]
2025-06-12 08:12:12,971 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:13,408 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-12 08:12:13,408 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 61770.49, 'new_value': 68261.09}, {'field': 'dailyBillAmount', 'old_value': 60598.1, 'new_value': 66989.16}, {'field': 'amount', 'old_value': 61770.49, 'new_value': 68261.09}, {'field': 'count', 'old_value': 797, 'new_value': 883}, {'field': 'instoreAmount', 'old_value': 59387.12, 'new_value': 65437.12}, {'field': 'instoreCount', 'old_value': 763, 'new_value': 842}, {'field': 'onlineAmount', 'old_value': 2437.37, 'new_value': 2877.97}, {'field': 'onlineCount', 'old_value': 34, 'new_value': 41}]
2025-06-12 08:12:13,408 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:13,846 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-12 08:12:13,846 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30170.94, 'new_value': 37220.94}, {'field': 'amount', 'old_value': 30170.94, 'new_value': 37220.94}, {'field': 'count', 'old_value': 49, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 30170.94, 'new_value': 37220.94}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 53}]
2025-06-12 08:12:13,846 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:14,283 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-12 08:12:14,299 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 57620.45, 'new_value': 61170.4}, {'field': 'dailyBillAmount', 'old_value': 57620.45, 'new_value': 61170.4}, {'field': 'amount', 'old_value': 35099.01, 'new_value': 37385.27}, {'field': 'count', 'old_value': 872, 'new_value': 935}, {'field': 'instoreAmount', 'old_value': 30218.19, 'new_value': 32009.59}, {'field': 'instoreCount', 'old_value': 736, 'new_value': 788}, {'field': 'onlineAmount', 'old_value': 5754.82, 'new_value': 6304.68}, {'field': 'onlineCount', 'old_value': 136, 'new_value': 147}]
2025-06-12 08:12:14,299 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:14,893 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-12 08:12:14,893 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34056.1, 'new_value': 35237.7}, {'field': 'dailyBillAmount', 'old_value': 30019.5, 'new_value': 31201.1}, {'field': 'amount', 'old_value': 34056.1, 'new_value': 34989.7}, {'field': 'count', 'old_value': 106, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 36999.5, 'new_value': 37933.1}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 115}]
2025-06-12 08:12:14,893 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:15,377 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-12 08:12:15,377 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23246.0, 'new_value': 24115.0}, {'field': 'dailyBillAmount', 'old_value': 23246.0, 'new_value': 24115.0}, {'field': 'amount', 'old_value': 25177.0, 'new_value': 26747.0}, {'field': 'count', 'old_value': 45, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 25962.0, 'new_value': 27831.0}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 50}]
2025-06-12 08:12:15,377 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:15,986 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-12 08:12:15,986 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36735.54, 'new_value': 38983.94}, {'field': 'dailyBillAmount', 'old_value': 36735.54, 'new_value': 38983.94}, {'field': 'amount', 'old_value': 34303.54, 'new_value': 36435.94}, {'field': 'count', 'old_value': 132, 'new_value': 142}, {'field': 'instoreAmount', 'old_value': 34542.54, 'new_value': 36674.94}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 142}]
2025-06-12 08:12:15,986 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:16,580 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-12 08:12:16,580 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 110001.5, 'new_value': 115947.5}, {'field': 'dailyBillAmount', 'old_value': 110001.5, 'new_value': 115947.5}, {'field': 'amount', 'old_value': 44761.0, 'new_value': 47302.0}, {'field': 'count', 'old_value': 129, 'new_value': 138}, {'field': 'instoreAmount', 'old_value': 44761.0, 'new_value': 47302.0}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 138}]
2025-06-12 08:12:16,580 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:17,018 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-12 08:12:17,018 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32823.3, 'new_value': 35341.7}, {'field': 'amount', 'old_value': 32823.3, 'new_value': 35341.7}, {'field': 'count', 'old_value': 776, 'new_value': 836}, {'field': 'instoreAmount', 'old_value': 33259.6, 'new_value': 35778.0}, {'field': 'instoreCount', 'old_value': 776, 'new_value': 836}]
2025-06-12 08:12:17,018 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:17,502 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-12 08:12:17,502 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 20436.81, 'new_value': 22254.32}, {'field': 'count', 'old_value': 251, 'new_value': 275}, {'field': 'instoreAmount', 'old_value': 20436.81, 'new_value': 22254.32}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 275}]
2025-06-12 08:12:17,502 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:18,064 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-12 08:12:18,064 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 14124.21, 'new_value': 14983.96}, {'field': 'count', 'old_value': 572, 'new_value': 616}, {'field': 'onlineAmount', 'old_value': 8694.92, 'new_value': 9555.48}, {'field': 'onlineCount', 'old_value': 389, 'new_value': 433}]
2025-06-12 08:12:18,064 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:18,471 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-12 08:12:18,471 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33554.99, 'new_value': 36771.39}, {'field': 'dailyBillAmount', 'old_value': 33554.99, 'new_value': 36771.39}, {'field': 'amount', 'old_value': 34611.13, 'new_value': 37807.68}, {'field': 'count', 'old_value': 1943, 'new_value': 2113}, {'field': 'instoreAmount', 'old_value': 19115.36, 'new_value': 20887.99}, {'field': 'instoreCount', 'old_value': 1006, 'new_value': 1086}, {'field': 'onlineAmount', 'old_value': 16148.73, 'new_value': 17573.58}, {'field': 'onlineCount', 'old_value': 937, 'new_value': 1027}]
2025-06-12 08:12:18,471 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:18,877 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-12 08:12:18,893 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22742.0, 'new_value': 25245.0}, {'field': 'dailyBillAmount', 'old_value': 22742.0, 'new_value': 25245.0}, {'field': 'amount', 'old_value': 22707.0, 'new_value': 24866.0}, {'field': 'count', 'old_value': 440, 'new_value': 484}, {'field': 'instoreAmount', 'old_value': 22707.0, 'new_value': 24866.0}, {'field': 'instoreCount', 'old_value': 440, 'new_value': 484}]
2025-06-12 08:12:18,893 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:19,393 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-12 08:12:19,393 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 110910.3, 'new_value': 119308.7}, {'field': 'dailyBillAmount', 'old_value': 110910.3, 'new_value': 119308.7}, {'field': 'amount', 'old_value': 79104.75, 'new_value': 83111.35}, {'field': 'count', 'old_value': 209, 'new_value': 221}, {'field': 'instoreAmount', 'old_value': 79333.7, 'new_value': 83340.3}, {'field': 'instoreCount', 'old_value': 209, 'new_value': 221}]
2025-06-12 08:12:19,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:19,861 - INFO - 更新表单数据成功: FINST-L8D665C1C82WOKI6BGFCGBTDWGJP22VFUWMBM9U
2025-06-12 08:12:19,861 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1493.0, 'new_value': 1532.0}, {'field': 'amount', 'old_value': 1493.0, 'new_value': 1532.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 1493.0, 'new_value': 1532.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-12 08:12:19,861 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:20,314 - INFO - 更新表单数据成功: FINST-90D66XA12PZVBSKCD49BM87RNO2X30KE17HBMG6
2025-06-12 08:12:20,314 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2891.32, 'new_value': 3154.72}, {'field': 'count', 'old_value': 6, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 2891.32, 'new_value': 3154.72}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 8}]
2025-06-12 08:12:20,314 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:20,799 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-12 08:12:20,799 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 71631.51, 'new_value': 77505.67}, {'field': 'dailyBillAmount', 'old_value': 71631.51, 'new_value': 77505.67}, {'field': 'amount', 'old_value': 36415.56, 'new_value': 39072.66}, {'field': 'count', 'old_value': 1626, 'new_value': 1757}, {'field': 'instoreAmount', 'old_value': 37542.31, 'new_value': 40251.61}, {'field': 'instoreCount', 'old_value': 1626, 'new_value': 1757}]
2025-06-12 08:12:20,799 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:21,283 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-12 08:12:21,283 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 209283.1, 'new_value': 226206.4}, {'field': 'dailyBillAmount', 'old_value': 209283.1, 'new_value': 226206.4}, {'field': 'amount', 'old_value': 209283.1, 'new_value': 226206.4}, {'field': 'count', 'old_value': 262, 'new_value': 283}, {'field': 'instoreAmount', 'old_value': 209283.1, 'new_value': 226206.4}, {'field': 'instoreCount', 'old_value': 262, 'new_value': 283}]
2025-06-12 08:12:21,392 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-12 08:12:21,877 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MVA
2025-06-12 08:12:21,877 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 440050.9, 'new_value': 469254.29}, {'field': 'dailyBillAmount', 'old_value': 440050.9, 'new_value': 469254.29}, {'field': 'amount', 'old_value': 47349.3, 'new_value': 48739.41}, {'field': 'count', 'old_value': 539, 'new_value': 545}, {'field': 'instoreAmount', 'old_value': 37360.23, 'new_value': 38750.53}, {'field': 'instoreCount', 'old_value': 373, 'new_value': 379}]
2025-06-12 08:12:21,892 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-12 08:12:22,408 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-06-12 08:12:22,408 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 61612.62, 'new_value': 61680.41}, {'field': 'count', 'old_value': 634, 'new_value': 635}, {'field': 'instoreAmount', 'old_value': 48681.23, 'new_value': 48749.93}, {'field': 'instoreCount', 'old_value': 441, 'new_value': 442}]
2025-06-12 08:12:22,439 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-12 08:12:22,939 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-06-12 08:12:22,939 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 551384.61, 'new_value': 855750.45}, {'field': 'dailyBillAmount', 'old_value': 551384.61, 'new_value': 855750.45}, {'field': 'amount', 'old_value': 527596.1, 'new_value': 1114375.0}, {'field': 'count', 'old_value': 9882, 'new_value': 21396}, {'field': 'instoreAmount', 'old_value': 493444.75, 'new_value': 1081091.68}, {'field': 'instoreCount', 'old_value': 9213, 'new_value': 20726}, {'field': 'onlineAmount', 'old_value': 36077.77, 'new_value': 36128.17}, {'field': 'onlineCount', 'old_value': 669, 'new_value': 670}]
2025-06-12 08:12:22,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:22,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:22,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:22,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:22,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:22,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:22,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:23,377 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-12 08:12:23,377 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10839.0, 'new_value': 15001.0}, {'field': 'amount', 'old_value': 10839.0, 'new_value': 15001.0}, {'field': 'count', 'old_value': 21, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 10839.0, 'new_value': 15001.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 25}]
2025-06-12 08:12:23,392 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:23,830 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMLK
2025-06-12 08:12:23,830 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 208488.0, 'new_value': 232984.0}, {'field': 'dailyBillAmount', 'old_value': 208488.0, 'new_value': 232984.0}, {'field': 'amount', 'old_value': 204436.0, 'new_value': 254130.0}, {'field': 'count', 'old_value': 30, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 204436.0, 'new_value': 254130.0}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 34}]
2025-06-12 08:12:23,830 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:24,330 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-12 08:12:24,330 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24535.0, 'new_value': 28334.0}, {'field': 'amount', 'old_value': 24535.0, 'new_value': 28334.0}, {'field': 'count', 'old_value': 61, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 24535.0, 'new_value': 28334.0}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 67}]
2025-06-12 08:12:24,330 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:24,783 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-12 08:12:24,783 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'amount', 'old_value': 93694.0, 'new_value': 100614.0}, {'field': 'count', 'old_value': 85, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 100515.0, 'new_value': 107435.0}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 91}]
2025-06-12 08:12:24,783 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:25,267 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-12 08:12:25,267 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19163.0, 'new_value': 21688.0}, {'field': 'dailyBillAmount', 'old_value': 10326.0, 'new_value': 11124.0}, {'field': 'amount', 'old_value': 17575.0, 'new_value': 20100.0}, {'field': 'count', 'old_value': 25, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 17575.0, 'new_value': 20100.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 28}]
2025-06-12 08:12:25,267 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:25,736 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-12 08:12:25,736 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20476.0, 'new_value': 21981.0}, {'field': 'dailyBillAmount', 'old_value': 20476.0, 'new_value': 21981.0}, {'field': 'amount', 'old_value': 7729.0, 'new_value': 8028.0}, {'field': 'count', 'old_value': 22, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 7729.0, 'new_value': 8028.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 23}]
2025-06-12 08:12:25,752 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:26,174 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-12 08:12:26,174 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 60582.1, 'new_value': 63628.0}, {'field': 'dailyBillAmount', 'old_value': 60582.1, 'new_value': 63628.0}, {'field': 'amount', 'old_value': 63982.01, 'new_value': 66479.91}, {'field': 'count', 'old_value': 153, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 65900.81, 'new_value': 68398.71}, {'field': 'instoreCount', 'old_value': 153, 'new_value': 160}]
2025-06-12 08:12:26,174 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:26,627 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-12 08:12:26,627 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41454.79, 'new_value': 43395.84}, {'field': 'dailyBillAmount', 'old_value': 41454.79, 'new_value': 43395.84}, {'field': 'amount', 'old_value': 43832.66, 'new_value': 45968.4}, {'field': 'count', 'old_value': 1519, 'new_value': 1604}, {'field': 'instoreAmount', 'old_value': 43767.66, 'new_value': 45892.79}, {'field': 'instoreCount', 'old_value': 1515, 'new_value': 1599}, {'field': 'onlineAmount', 'old_value': 124.9, 'new_value': 135.51}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-06-12 08:12:26,627 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:27,142 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-12 08:12:27,142 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 180578.43, 'new_value': 196721.1}, {'field': 'dailyBillAmount', 'old_value': 180578.43, 'new_value': 196721.1}, {'field': 'amount', 'old_value': 159172.04, 'new_value': 168882.04}, {'field': 'count', 'old_value': 713, 'new_value': 757}, {'field': 'instoreAmount', 'old_value': 159172.04, 'new_value': 168882.04}, {'field': 'instoreCount', 'old_value': 713, 'new_value': 757}]
2025-06-12 08:12:27,142 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:27,564 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-12 08:12:27,564 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26535.0, 'new_value': 28759.09}, {'field': 'dailyBillAmount', 'old_value': 26535.0, 'new_value': 28759.09}, {'field': 'amount', 'old_value': 31110.04, 'new_value': 33780.21}, {'field': 'count', 'old_value': 1109, 'new_value': 1216}, {'field': 'instoreAmount', 'old_value': 11035.48, 'new_value': 11791.75}, {'field': 'instoreCount', 'old_value': 380, 'new_value': 403}, {'field': 'onlineAmount', 'old_value': 20291.96, 'new_value': 22298.46}, {'field': 'onlineCount', 'old_value': 729, 'new_value': 813}]
2025-06-12 08:12:27,564 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:28,002 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMCK
2025-06-12 08:12:28,002 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 80926.75, 'new_value': 89393.57}, {'field': 'amount', 'old_value': 80926.35, 'new_value': 89392.8}, {'field': 'count', 'old_value': 29, 'new_value': 44}, {'field': 'instoreAmount', 'old_value': 80926.75, 'new_value': 89393.57}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 44}]
2025-06-12 08:12:28,002 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:28,533 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-12 08:12:28,533 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75124.04, 'new_value': 79609.06}, {'field': 'dailyBillAmount', 'old_value': 75124.04, 'new_value': 79609.06}, {'field': 'amount', 'old_value': 63536.44, 'new_value': 67936.4}, {'field': 'count', 'old_value': 1948, 'new_value': 2061}, {'field': 'instoreAmount', 'old_value': 34234.82, 'new_value': 36213.79}, {'field': 'instoreCount', 'old_value': 1412, 'new_value': 1468}, {'field': 'onlineAmount', 'old_value': 33066.1, 'new_value': 35811.2}, {'field': 'onlineCount', 'old_value': 536, 'new_value': 593}]
2025-06-12 08:12:28,533 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:28,955 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-12 08:12:28,955 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'amount', 'old_value': 40966.2, 'new_value': 44213.8}, {'field': 'count', 'old_value': 2920, 'new_value': 3147}, {'field': 'instoreAmount', 'old_value': 2456.5, 'new_value': 2684.5}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 181}, {'field': 'onlineAmount', 'old_value': 40078.15, 'new_value': 43285.39}, {'field': 'onlineCount', 'old_value': 2751, 'new_value': 2966}]
2025-06-12 08:12:28,955 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:29,377 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-12 08:12:29,377 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 65404.86, 'new_value': 71050.84}, {'field': 'count', 'old_value': 3343, 'new_value': 3571}, {'field': 'instoreAmount', 'old_value': 68513.04, 'new_value': 74245.13}, {'field': 'instoreCount', 'old_value': 3330, 'new_value': 3558}]
2025-06-12 08:12:29,377 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:29,798 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-12 08:12:29,798 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49628.78, 'new_value': 51156.08}, {'field': 'amount', 'old_value': 49627.76, 'new_value': 51155.06}, {'field': 'count', 'old_value': 1245, 'new_value': 1311}, {'field': 'instoreAmount', 'old_value': 47277.91, 'new_value': 48654.41}, {'field': 'instoreCount', 'old_value': 1216, 'new_value': 1279}, {'field': 'onlineAmount', 'old_value': 2358.17, 'new_value': 2508.97}, {'field': 'onlineCount', 'old_value': 29, 'new_value': 32}]
2025-06-12 08:12:29,798 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:30,283 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-12 08:12:30,283 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 61199.56, 'new_value': 66084.56}, {'field': 'dailyBillAmount', 'old_value': 61199.56, 'new_value': 66084.56}, {'field': 'amount', 'old_value': 41487.69, 'new_value': 45433.57}, {'field': 'count', 'old_value': 1960, 'new_value': 2125}, {'field': 'instoreAmount', 'old_value': 7097.15, 'new_value': 7397.52}, {'field': 'instoreCount', 'old_value': 466, 'new_value': 484}, {'field': 'onlineAmount', 'old_value': 35284.11, 'new_value': 39036.82}, {'field': 'onlineCount', 'old_value': 1494, 'new_value': 1641}]
2025-06-12 08:12:30,283 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:30,783 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-12 08:12:30,783 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59232.86, 'new_value': 63573.2}, {'field': 'dailyBillAmount', 'old_value': 59232.86, 'new_value': 63573.2}, {'field': 'amount', 'old_value': 12208.92, 'new_value': 13117.32}, {'field': 'count', 'old_value': 399, 'new_value': 435}, {'field': 'instoreAmount', 'old_value': 12414.66, 'new_value': 13323.06}, {'field': 'instoreCount', 'old_value': 399, 'new_value': 435}]
2025-06-12 08:12:30,783 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:31,220 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-12 08:12:31,220 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44424.26, 'new_value': 47038.26}, {'field': 'amount', 'old_value': 44423.86, 'new_value': 47037.86}, {'field': 'count', 'old_value': 2547, 'new_value': 2703}, {'field': 'instoreAmount', 'old_value': 20609.02, 'new_value': 21404.52}, {'field': 'instoreCount', 'old_value': 1416, 'new_value': 1474}, {'field': 'onlineAmount', 'old_value': 23815.24, 'new_value': 25633.74}, {'field': 'onlineCount', 'old_value': 1131, 'new_value': 1229}]
2025-06-12 08:12:31,220 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:31,642 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-12 08:12:31,642 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 100171.01, 'new_value': 106165.5}, {'field': 'dailyBillAmount', 'old_value': 100171.01, 'new_value': 106165.5}, {'field': 'amount', 'old_value': 92539.69, 'new_value': 97688.79}, {'field': 'count', 'old_value': 2834, 'new_value': 2993}, {'field': 'instoreAmount', 'old_value': 92833.49, 'new_value': 98029.59}, {'field': 'instoreCount', 'old_value': 2834, 'new_value': 2993}]
2025-06-12 08:12:31,642 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:32,126 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-12 08:12:32,126 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50608.21, 'new_value': 56668.75}, {'field': 'dailyBillAmount', 'old_value': 50608.21, 'new_value': 56668.75}, {'field': 'amount', 'old_value': 59567.34, 'new_value': 64433.65}, {'field': 'count', 'old_value': 3888, 'new_value': 4193}, {'field': 'instoreAmount', 'old_value': 44105.49, 'new_value': 47672.57}, {'field': 'instoreCount', 'old_value': 2631, 'new_value': 2849}, {'field': 'onlineAmount', 'old_value': 16781.55, 'new_value': 18190.39}, {'field': 'onlineCount', 'old_value': 1257, 'new_value': 1344}]
2025-06-12 08:12:32,126 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:32,595 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-12 08:12:32,595 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -4687.22, 'new_value': -5077.28}, {'field': 'count', 'old_value': 43, 'new_value': 45}, {'field': 'onlineAmount', 'old_value': 687.0, 'new_value': 738.0}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 33}]
2025-06-12 08:12:32,595 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:33,001 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-12 08:12:33,001 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 205539.51, 'new_value': 228971.91}, {'field': 'amount', 'old_value': 205539.51, 'new_value': 228971.91}, {'field': 'count', 'old_value': 4664, 'new_value': 5211}, {'field': 'instoreAmount', 'old_value': 148407.56, 'new_value': 165414.36}, {'field': 'instoreCount', 'old_value': 3107, 'new_value': 3481}, {'field': 'onlineAmount', 'old_value': 57131.95, 'new_value': 63557.55}, {'field': 'onlineCount', 'old_value': 1557, 'new_value': 1730}]
2025-06-12 08:12:33,001 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:33,392 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-12 08:12:33,392 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 220705.35, 'new_value': 313328.97}, {'field': 'dailyBillAmount', 'old_value': 220705.35, 'new_value': 313328.97}, {'field': 'amount', 'old_value': 196732.02, 'new_value': 404827.77}, {'field': 'count', 'old_value': 3522, 'new_value': 7591}, {'field': 'instoreAmount', 'old_value': 181599.18, 'new_value': 389971.9}, {'field': 'instoreCount', 'old_value': 3218, 'new_value': 7286}, {'field': 'onlineAmount', 'old_value': 15769.84, 'new_value': 15788.54}, {'field': 'onlineCount', 'old_value': 304, 'new_value': 305}]
2025-06-12 08:12:33,408 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:33,876 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-12 08:12:33,876 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 225840.8, 'new_value': 242039.16}, {'field': 'dailyBillAmount', 'old_value': 225840.8, 'new_value': 242039.16}, {'field': 'amount', 'old_value': 224231.62, 'new_value': 239130.43}, {'field': 'count', 'old_value': 2362, 'new_value': 2567}, {'field': 'instoreAmount', 'old_value': 176976.38, 'new_value': 185996.28}, {'field': 'instoreCount', 'old_value': 884, 'new_value': 928}, {'field': 'onlineAmount', 'old_value': 48413.06, 'new_value': 54337.77}, {'field': 'onlineCount', 'old_value': 1478, 'new_value': 1639}]
2025-06-12 08:12:33,876 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:34,298 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-12 08:12:34,298 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 123614.77, 'new_value': 130780.94}, {'field': 'dailyBillAmount', 'old_value': 123614.77, 'new_value': 130780.94}, {'field': 'amount', 'old_value': 62192.19, 'new_value': 66378.76}, {'field': 'count', 'old_value': 1342, 'new_value': 1415}, {'field': 'instoreAmount', 'old_value': 53221.89, 'new_value': 56448.83}, {'field': 'instoreCount', 'old_value': 1155, 'new_value': 1212}, {'field': 'onlineAmount', 'old_value': 9829.24, 'new_value': 10789.0}, {'field': 'onlineCount', 'old_value': 187, 'new_value': 203}]
2025-06-12 08:12:34,298 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:34,783 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-12 08:12:34,783 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 73700.78, 'new_value': 76296.6}, {'field': 'amount', 'old_value': 73700.78, 'new_value': 76296.42}, {'field': 'count', 'old_value': 814, 'new_value': 859}, {'field': 'instoreAmount', 'old_value': 50758.1, 'new_value': 51537.27}, {'field': 'instoreCount', 'old_value': 487, 'new_value': 501}, {'field': 'onlineAmount', 'old_value': 24595.36, 'new_value': 26439.51}, {'field': 'onlineCount', 'old_value': 327, 'new_value': 358}]
2025-06-12 08:12:34,783 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:35,189 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-12 08:12:35,189 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 106000.3, 'new_value': 123422.8}, {'field': 'dailyBillAmount', 'old_value': 160760.3, 'new_value': 167419.0}, {'field': 'amount', 'old_value': 106000.3, 'new_value': 123422.8}, {'field': 'count', 'old_value': 408, 'new_value': 444}, {'field': 'instoreAmount', 'old_value': 108313.9, 'new_value': 125736.4}, {'field': 'instoreCount', 'old_value': 408, 'new_value': 444}]
2025-06-12 08:12:35,189 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:35,611 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-12 08:12:35,611 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11691.5, 'new_value': 12451.6}, {'field': 'amount', 'old_value': 11691.5, 'new_value': 12451.6}, {'field': 'count', 'old_value': 39, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 11691.5, 'new_value': 12451.6}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 42}]
2025-06-12 08:12:35,611 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:36,064 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-12 08:12:36,064 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25587.0, 'new_value': 30134.0}, {'field': 'amount', 'old_value': 25587.0, 'new_value': 30134.0}, {'field': 'count', 'old_value': 38, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 26385.0, 'new_value': 30932.0}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 42}]
2025-06-12 08:12:36,064 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:36,486 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-12 08:12:36,486 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 393293.1, 'new_value': 396279.08}, {'field': 'dailyBillAmount', 'old_value': 393293.1, 'new_value': 396279.08}, {'field': 'amount', 'old_value': 25344.35, 'new_value': 29306.96}, {'field': 'count', 'old_value': 271, 'new_value': 313}, {'field': 'instoreAmount', 'old_value': 20085.75, 'new_value': 23595.53}, {'field': 'instoreCount', 'old_value': 194, 'new_value': 231}, {'field': 'onlineAmount', 'old_value': 5377.43, 'new_value': 5830.7}, {'field': 'onlineCount', 'old_value': 77, 'new_value': 82}]
2025-06-12 08:12:36,486 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:36,939 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-12 08:12:36,939 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3437.0, 'new_value': 3765.0}, {'field': 'dailyBillAmount', 'old_value': 3437.0, 'new_value': 3765.0}, {'field': 'amount', 'old_value': 18348.0, 'new_value': 19986.0}, {'field': 'count', 'old_value': 48, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 18546.0, 'new_value': 20184.0}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 53}]
2025-06-12 08:12:36,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:37,423 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-12 08:12:37,423 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12645.7, 'new_value': 13598.9}, {'field': 'amount', 'old_value': 12645.7, 'new_value': 13598.9}, {'field': 'count', 'old_value': 80, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 12753.7, 'new_value': 13706.9}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 87}]
2025-06-12 08:12:37,423 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:37,829 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-12 08:12:37,829 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8562.0, 'new_value': 10900.0}, {'field': 'dailyBillAmount', 'old_value': 8562.0, 'new_value': 10900.0}, {'field': 'amount', 'old_value': 8557.0, 'new_value': 10257.0}, {'field': 'count', 'old_value': 38, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 9087.0, 'new_value': 10787.0}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 42}]
2025-06-12 08:12:37,829 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:38,392 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMRK
2025-06-12 08:12:38,392 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3776.0, 'new_value': 4075.0}, {'field': 'amount', 'old_value': 3776.0, 'new_value': 4075.0}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 3776.0, 'new_value': 4075.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-06-12 08:12:38,392 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:38,845 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-12 08:12:38,845 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23681.2, 'new_value': 26780.7}, {'field': 'dailyBillAmount', 'old_value': 23681.2, 'new_value': 26780.7}, {'field': 'amount', 'old_value': 21895.53, 'new_value': 24888.13}, {'field': 'count', 'old_value': 211, 'new_value': 236}, {'field': 'instoreAmount', 'old_value': 20089.6, 'new_value': 22821.6}, {'field': 'instoreCount', 'old_value': 186, 'new_value': 208}, {'field': 'onlineAmount', 'old_value': 1946.43, 'new_value': 2207.03}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 28}]
2025-06-12 08:12:38,845 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:39,298 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-12 08:12:39,298 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 136476.16, 'new_value': 149846.4}, {'field': 'dailyBillAmount', 'old_value': 136476.16, 'new_value': 149846.4}, {'field': 'amount', 'old_value': 10568.07, 'new_value': 11133.78}, {'field': 'count', 'old_value': 319, 'new_value': 339}, {'field': 'instoreAmount', 'old_value': 11663.14, 'new_value': 12335.55}, {'field': 'instoreCount', 'old_value': 319, 'new_value': 339}]
2025-06-12 08:12:39,298 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:39,736 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-12 08:12:39,736 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 96834.2, 'new_value': 102447.8}, {'field': 'dailyBillAmount', 'old_value': 96834.2, 'new_value': 102447.8}, {'field': 'amount', 'old_value': 96000.5, 'new_value': 98907.1}, {'field': 'count', 'old_value': 123, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 96977.5, 'new_value': 100729.7}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 132}]
2025-06-12 08:12:39,736 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:40,095 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-12 08:12:40,095 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14489.0, 'new_value': 18989.0}, {'field': 'dailyBillAmount', 'old_value': 14489.0, 'new_value': 18989.0}, {'field': 'amount', 'old_value': 14571.0, 'new_value': 19133.0}, {'field': 'count', 'old_value': 33, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 16206.0, 'new_value': 20768.0}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 36}]
2025-06-12 08:12:40,111 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:40,736 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-12 08:12:40,736 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 99480.99, 'new_value': 102464.51}, {'field': 'dailyBillAmount', 'old_value': 88381.39, 'new_value': 90489.39}, {'field': 'amount', 'old_value': 96296.72, 'new_value': 99280.24}, {'field': 'count', 'old_value': 492, 'new_value': 499}, {'field': 'instoreAmount', 'old_value': 97294.98, 'new_value': 100278.5}, {'field': 'instoreCount', 'old_value': 492, 'new_value': 499}]
2025-06-12 08:12:40,736 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:41,220 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-12 08:12:41,220 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 99598.37, 'new_value': 104060.37}, {'field': 'dailyBillAmount', 'old_value': 99598.37, 'new_value': 104060.37}, {'field': 'amount', 'old_value': 99003.37, 'new_value': 103465.37}, {'field': 'count', 'old_value': 576, 'new_value': 607}, {'field': 'instoreAmount', 'old_value': 99003.37, 'new_value': 103465.37}, {'field': 'instoreCount', 'old_value': 576, 'new_value': 607}]
2025-06-12 08:12:41,220 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:41,657 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-12 08:12:41,657 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32325.41, 'new_value': 37609.02}, {'field': 'dailyBillAmount', 'old_value': 32325.41, 'new_value': 37609.02}, {'field': 'amount', 'old_value': 17426.98, 'new_value': 18137.27}, {'field': 'count', 'old_value': 1618, 'new_value': 1698}, {'field': 'instoreAmount', 'old_value': 17884.96, 'new_value': 18633.96}, {'field': 'instoreCount', 'old_value': 1618, 'new_value': 1698}]
2025-06-12 08:12:41,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:42,079 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-12 08:12:42,079 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 76720.8, 'new_value': 79752.6}, {'field': 'amount', 'old_value': 76720.8, 'new_value': 79752.6}, {'field': 'count', 'old_value': 486, 'new_value': 510}, {'field': 'instoreAmount', 'old_value': 76720.8, 'new_value': 79752.6}, {'field': 'instoreCount', 'old_value': 486, 'new_value': 510}]
2025-06-12 08:12:42,079 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:42,579 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-12 08:12:42,579 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 62908.5, 'new_value': 70487.56}, {'field': 'count', 'old_value': 2609, 'new_value': 2965}, {'field': 'instoreAmount', 'old_value': 20533.61, 'new_value': 23156.81}, {'field': 'instoreCount', 'old_value': 865, 'new_value': 1005}, {'field': 'onlineAmount', 'old_value': 43400.48, 'new_value': 48539.58}, {'field': 'onlineCount', 'old_value': 1744, 'new_value': 1960}]
2025-06-12 08:12:42,579 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:43,001 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-12 08:12:43,001 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30330.24, 'new_value': 32749.53}, {'field': 'dailyBillAmount', 'old_value': 30330.24, 'new_value': 32749.53}, {'field': 'amount', 'old_value': 46455.73, 'new_value': 50395.68}, {'field': 'count', 'old_value': 2338, 'new_value': 2489}, {'field': 'instoreAmount', 'old_value': 24806.76, 'new_value': 26215.52}, {'field': 'instoreCount', 'old_value': 1405, 'new_value': 1494}, {'field': 'onlineAmount', 'old_value': 22404.27, 'new_value': 25009.27}, {'field': 'onlineCount', 'old_value': 933, 'new_value': 995}]
2025-06-12 08:12:43,017 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:43,532 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-12 08:12:43,532 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21694.0, 'new_value': 22882.0}, {'field': 'amount', 'old_value': 21694.0, 'new_value': 22882.0}, {'field': 'count', 'old_value': 18, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 21694.0, 'new_value': 22882.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 20}]
2025-06-12 08:12:43,532 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:44,001 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-12 08:12:44,001 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45221.1, 'new_value': 47860.7}, {'field': 'dailyBillAmount', 'old_value': 45221.1, 'new_value': 47860.7}, {'field': 'amount', 'old_value': 24400.17, 'new_value': 26210.36}, {'field': 'count', 'old_value': 1735, 'new_value': 1877}, {'field': 'instoreAmount', 'old_value': 3892.7, 'new_value': 3944.1}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 174}, {'field': 'onlineAmount', 'old_value': 20507.47, 'new_value': 22266.26}, {'field': 'onlineCount', 'old_value': 1566, 'new_value': 1703}]
2025-06-12 08:12:44,001 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:44,548 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-12 08:12:44,548 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 123173.01, 'new_value': 131884.24}, {'field': 'dailyBillAmount', 'old_value': 123173.01, 'new_value': 131884.24}, {'field': 'amount', 'old_value': 115930.3, 'new_value': 124831.9}, {'field': 'count', 'old_value': 1058, 'new_value': 1160}, {'field': 'instoreAmount', 'old_value': 95535.39, 'new_value': 102213.99}, {'field': 'instoreCount', 'old_value': 658, 'new_value': 716}, {'field': 'onlineAmount', 'old_value': 20394.91, 'new_value': 22617.91}, {'field': 'onlineCount', 'old_value': 400, 'new_value': 444}]
2025-06-12 08:12:44,548 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:44,985 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-12 08:12:44,985 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 155486.83, 'new_value': 163116.44}, {'field': 'dailyBillAmount', 'old_value': 155486.83, 'new_value': 163116.44}, {'field': 'amount', 'old_value': 136203.86, 'new_value': 142058.56}, {'field': 'count', 'old_value': 779, 'new_value': 821}, {'field': 'instoreAmount', 'old_value': 139892.86, 'new_value': 146038.46}, {'field': 'instoreCount', 'old_value': 779, 'new_value': 821}]
2025-06-12 08:12:44,985 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:45,454 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-12 08:12:45,454 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 383351.2, 'new_value': 412474.92}, {'field': 'dailyBillAmount', 'old_value': 383351.2, 'new_value': 412474.92}, {'field': 'amount', 'old_value': 421747.71, 'new_value': 455060.25}, {'field': 'count', 'old_value': 2422, 'new_value': 2645}, {'field': 'instoreAmount', 'old_value': 314363.61, 'new_value': 337519.19}, {'field': 'instoreCount', 'old_value': 1257, 'new_value': 1364}, {'field': 'onlineAmount', 'old_value': 109590.16, 'new_value': 119926.86}, {'field': 'onlineCount', 'old_value': 1165, 'new_value': 1281}]
2025-06-12 08:12:45,454 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:45,939 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-12 08:12:45,939 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 124260.65, 'new_value': 129821.27}, {'field': 'dailyBillAmount', 'old_value': 124260.65, 'new_value': 129821.27}, {'field': 'amount', 'old_value': 176597.82, 'new_value': 184918.16}, {'field': 'count', 'old_value': 881, 'new_value': 927}, {'field': 'instoreAmount', 'old_value': 165079.79, 'new_value': 172178.59}, {'field': 'instoreCount', 'old_value': 668, 'new_value': 695}, {'field': 'onlineAmount', 'old_value': 11811.88, 'new_value': 13033.42}, {'field': 'onlineCount', 'old_value': 213, 'new_value': 232}]
2025-06-12 08:12:45,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:46,282 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-12 08:12:46,282 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 151855.97, 'new_value': 159833.96}, {'field': 'dailyBillAmount', 'old_value': 151855.97, 'new_value': 159833.96}, {'field': 'amount', 'old_value': 144639.2, 'new_value': 152252.0}, {'field': 'count', 'old_value': 646, 'new_value': 676}, {'field': 'instoreAmount', 'old_value': 146509.0, 'new_value': 154121.8}, {'field': 'instoreCount', 'old_value': 646, 'new_value': 676}]
2025-06-12 08:12:46,298 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:46,876 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-12 08:12:46,876 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 324187.94, 'new_value': 341267.88}, {'field': 'amount', 'old_value': 324187.94, 'new_value': 341267.88}, {'field': 'count', 'old_value': 2556, 'new_value': 2762}, {'field': 'instoreAmount', 'old_value': 324187.94, 'new_value': 341267.88}, {'field': 'instoreCount', 'old_value': 2556, 'new_value': 2762}]
2025-06-12 08:12:46,876 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:47,329 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-12 08:12:47,329 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 302856.62, 'new_value': 323829.57}, {'field': 'dailyBillAmount', 'old_value': 302856.62, 'new_value': 323829.57}, {'field': 'amount', 'old_value': 348691.59, 'new_value': 371999.36}, {'field': 'count', 'old_value': 2467, 'new_value': 2657}, {'field': 'instoreAmount', 'old_value': 198324.6, 'new_value': 208101.5}, {'field': 'instoreCount', 'old_value': 1093, 'new_value': 1155}, {'field': 'onlineAmount', 'old_value': 155657.6, 'new_value': 169652.7}, {'field': 'onlineCount', 'old_value': 1374, 'new_value': 1502}]
2025-06-12 08:12:47,329 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:47,829 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-12 08:12:47,829 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 187456.26, 'new_value': 195758.76}, {'field': 'dailyBillAmount', 'old_value': 187456.26, 'new_value': 195758.76}, {'field': 'amount', 'old_value': 185921.98, 'new_value': 193272.96}, {'field': 'count', 'old_value': 1913, 'new_value': 2027}, {'field': 'instoreAmount', 'old_value': 134871.56, 'new_value': 137730.26}, {'field': 'instoreCount', 'old_value': 922, 'new_value': 948}, {'field': 'onlineAmount', 'old_value': 51621.9, 'new_value': 56396.73}, {'field': 'onlineCount', 'old_value': 991, 'new_value': 1079}]
2025-06-12 08:12:47,829 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:48,298 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-12 08:12:48,298 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 215770.41, 'new_value': 229240.72}, {'field': 'dailyBillAmount', 'old_value': 215770.41, 'new_value': 229240.72}, {'field': 'amount', 'old_value': 216628.04, 'new_value': 230389.81}, {'field': 'count', 'old_value': 1755, 'new_value': 1889}, {'field': 'instoreAmount', 'old_value': 196198.57, 'new_value': 207921.69}, {'field': 'instoreCount', 'old_value': 1038, 'new_value': 1112}, {'field': 'onlineAmount', 'old_value': 20579.21, 'new_value': 22618.52}, {'field': 'onlineCount', 'old_value': 717, 'new_value': 777}]
2025-06-12 08:12:48,298 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:48,720 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-12 08:12:48,720 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 157091.12, 'new_value': 161408.62}, {'field': 'dailyBillAmount', 'old_value': 157091.12, 'new_value': 161408.62}, {'field': 'amount', 'old_value': -108455.48, 'new_value': -111434.5}, {'field': 'count', 'old_value': 320, 'new_value': 351}, {'field': 'instoreAmount', 'old_value': 2585.0, 'new_value': 2694.0}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 117}, {'field': 'onlineAmount', 'old_value': 4980.32, 'new_value': 5736.32}, {'field': 'onlineCount', 'old_value': 207, 'new_value': 234}]
2025-06-12 08:12:48,720 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:49,204 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-12 08:12:49,204 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 198641.43, 'new_value': 210527.84}, {'field': 'dailyBillAmount', 'old_value': 198641.43, 'new_value': 210527.84}, {'field': 'amount', 'old_value': 54944.0, 'new_value': 56818.1}, {'field': 'count', 'old_value': 236, 'new_value': 244}, {'field': 'instoreAmount', 'old_value': 55112.7, 'new_value': 56986.9}, {'field': 'instoreCount', 'old_value': 226, 'new_value': 234}]
2025-06-12 08:12:49,204 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:49,642 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-12 08:12:49,642 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 133654.23, 'new_value': 142583.61}, {'field': 'dailyBillAmount', 'old_value': 133654.23, 'new_value': 142583.61}, {'field': 'amount', 'old_value': 130885.66, 'new_value': 139074.88}, {'field': 'count', 'old_value': 796, 'new_value': 867}, {'field': 'instoreAmount', 'old_value': 125028.41, 'new_value': 132489.24}, {'field': 'instoreCount', 'old_value': 634, 'new_value': 683}, {'field': 'onlineAmount', 'old_value': 5915.31, 'new_value': 6669.93}, {'field': 'onlineCount', 'old_value': 162, 'new_value': 184}]
2025-06-12 08:12:49,642 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:50,126 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-12 08:12:50,126 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 137289.8, 'new_value': 144624.87}, {'field': 'dailyBillAmount', 'old_value': 137289.8, 'new_value': 144624.87}, {'field': 'amount', 'old_value': 58501.11, 'new_value': 61692.92}, {'field': 'count', 'old_value': 817, 'new_value': 869}, {'field': 'instoreAmount', 'old_value': 39970.04, 'new_value': 41381.38}, {'field': 'instoreCount', 'old_value': 220, 'new_value': 226}, {'field': 'onlineAmount', 'old_value': 18532.0, 'new_value': 20312.47}, {'field': 'onlineCount', 'old_value': 597, 'new_value': 643}]
2025-06-12 08:12:50,126 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:50,563 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMK1
2025-06-12 08:12:50,563 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-06, 变更字段: [{'field': 'amount', 'old_value': 12930.0, 'new_value': 18523.0}, {'field': 'count', 'old_value': 9, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 12930.0, 'new_value': 18523.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 12}]
2025-06-12 08:12:50,563 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:51,001 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-12 08:12:51,001 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56744.11, 'new_value': 61797.72}, {'field': 'amount', 'old_value': 56742.67, 'new_value': 61796.28}, {'field': 'count', 'old_value': 2676, 'new_value': 2903}, {'field': 'instoreAmount', 'old_value': 15276.33, 'new_value': 16417.26}, {'field': 'instoreCount', 'old_value': 637, 'new_value': 681}, {'field': 'onlineAmount', 'old_value': 42680.23, 'new_value': 46661.03}, {'field': 'onlineCount', 'old_value': 2039, 'new_value': 2222}]
2025-06-12 08:12:51,001 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:51,438 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-12 08:12:51,438 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16929.0, 'new_value': 18782.0}, {'field': 'amount', 'old_value': 16929.0, 'new_value': 18782.0}, {'field': 'count', 'old_value': 70, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 16929.0, 'new_value': 18782.0}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 73}]
2025-06-12 08:12:51,438 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:51,860 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-12 08:12:51,860 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 155629.8, 'new_value': 164485.7}, {'field': 'dailyBillAmount', 'old_value': 155629.8, 'new_value': 164485.7}, {'field': 'amount', 'old_value': 61300.49, 'new_value': 64736.99}, {'field': 'count', 'old_value': 1198, 'new_value': 1270}, {'field': 'instoreAmount', 'old_value': 61712.1, 'new_value': 65157.7}, {'field': 'instoreCount', 'old_value': 1198, 'new_value': 1270}]
2025-06-12 08:12:51,860 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:52,298 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSS
2025-06-12 08:12:52,298 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 71728.12, 'new_value': 74996.12}, {'field': 'amount', 'old_value': 71728.12, 'new_value': 74996.12}, {'field': 'count', 'old_value': 1673, 'new_value': 1759}, {'field': 'instoreAmount', 'old_value': 71728.12, 'new_value': 74996.12}, {'field': 'instoreCount', 'old_value': 1673, 'new_value': 1759}]
2025-06-12 08:12:52,298 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:52,782 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-12 08:12:52,782 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16501.17, 'new_value': 17317.15}, {'field': 'amount', 'old_value': 16500.6, 'new_value': 17316.58}, {'field': 'count', 'old_value': 1019, 'new_value': 1067}, {'field': 'instoreAmount', 'old_value': 6633.38, 'new_value': 6780.38}, {'field': 'instoreCount', 'old_value': 346, 'new_value': 354}, {'field': 'onlineAmount', 'old_value': 10133.17, 'new_value': 10802.15}, {'field': 'onlineCount', 'old_value': 673, 'new_value': 713}]
2025-06-12 08:12:52,782 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:53,251 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-12 08:12:53,251 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49855.0, 'new_value': 50435.0}, {'field': 'amount', 'old_value': 49855.0, 'new_value': 50435.0}, {'field': 'count', 'old_value': 283, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 53915.0, 'new_value': 54495.0}, {'field': 'instoreCount', 'old_value': 283, 'new_value': 284}]
2025-06-12 08:12:53,266 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:53,766 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-12 08:12:53,766 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 54669.6, 'new_value': 61061.33}, {'field': 'dailyBillAmount', 'old_value': 54745.34, 'new_value': 61085.82}, {'field': 'amount', 'old_value': 54668.88, 'new_value': 61060.14}, {'field': 'count', 'old_value': 3032, 'new_value': 3404}, {'field': 'instoreAmount', 'old_value': 26613.68, 'new_value': 29943.24}, {'field': 'instoreCount', 'old_value': 1426, 'new_value': 1619}, {'field': 'onlineAmount', 'old_value': 28401.46, 'new_value': 31514.9}, {'field': 'onlineCount', 'old_value': 1606, 'new_value': 1785}]
2025-06-12 08:12:53,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:54,235 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-12 08:12:54,235 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28450.71, 'new_value': 30255.0}, {'field': 'amount', 'old_value': 28450.71, 'new_value': 30255.0}, {'field': 'count', 'old_value': 1798, 'new_value': 1939}, {'field': 'instoreAmount', 'old_value': 15170.92, 'new_value': 15978.82}, {'field': 'instoreCount', 'old_value': 854, 'new_value': 922}, {'field': 'onlineAmount', 'old_value': 14658.83, 'new_value': 15696.9}, {'field': 'onlineCount', 'old_value': 944, 'new_value': 1017}]
2025-06-12 08:12:54,235 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:54,735 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-12 08:12:54,735 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7686.54, 'new_value': 18952.35}, {'field': 'dailyBillAmount', 'old_value': 7686.54, 'new_value': 18952.35}, {'field': 'amount', 'old_value': 71853.02, 'new_value': 79349.94}, {'field': 'count', 'old_value': 730, 'new_value': 822}, {'field': 'instoreAmount', 'old_value': 71894.92, 'new_value': 79391.84}, {'field': 'instoreCount', 'old_value': 730, 'new_value': 822}]
2025-06-12 08:12:54,735 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:55,173 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-12 08:12:55,173 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41511.55, 'new_value': 45458.36}, {'field': 'dailyBillAmount', 'old_value': 43380.42, 'new_value': 47543.58}, {'field': 'amount', 'old_value': 41511.55, 'new_value': 45458.36}, {'field': 'count', 'old_value': 1085, 'new_value': 1200}, {'field': 'instoreAmount', 'old_value': 38082.15, 'new_value': 41700.73}, {'field': 'instoreCount', 'old_value': 806, 'new_value': 895}, {'field': 'onlineAmount', 'old_value': 3459.7, 'new_value': 3803.93}, {'field': 'onlineCount', 'old_value': 279, 'new_value': 305}]
2025-06-12 08:12:55,173 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:55,610 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-12 08:12:55,610 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 69133.17, 'new_value': 73521.03}, {'field': 'dailyBillAmount', 'old_value': 55549.8, 'new_value': 59147.2}, {'field': 'amount', 'old_value': 69133.17, 'new_value': 73521.03}, {'field': 'count', 'old_value': 1025, 'new_value': 1111}, {'field': 'instoreAmount', 'old_value': 65268.4, 'new_value': 69229.4}, {'field': 'instoreCount', 'old_value': 848, 'new_value': 913}, {'field': 'onlineAmount', 'old_value': 4014.77, 'new_value': 4441.63}, {'field': 'onlineCount', 'old_value': 177, 'new_value': 198}]
2025-06-12 08:12:55,610 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:56,032 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-12 08:12:56,032 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12209.74, 'new_value': 13193.64}, {'field': 'amount', 'old_value': 12209.74, 'new_value': 13193.64}, {'field': 'count', 'old_value': 545, 'new_value': 587}, {'field': 'instoreAmount', 'old_value': 10769.74, 'new_value': 11613.94}, {'field': 'instoreCount', 'old_value': 498, 'new_value': 535}, {'field': 'onlineAmount', 'old_value': 1474.4, 'new_value': 1614.1}, {'field': 'onlineCount', 'old_value': 47, 'new_value': 52}]
2025-06-12 08:12:56,032 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:56,438 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-12 08:12:56,438 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 153444.94, 'new_value': 162412.12}, {'field': 'dailyBillAmount', 'old_value': 153444.94, 'new_value': 162412.12}, {'field': 'amount', 'old_value': 202483.1, 'new_value': 216072.66}, {'field': 'count', 'old_value': 1875, 'new_value': 2056}, {'field': 'instoreAmount', 'old_value': 191283.52, 'new_value': 203190.7}, {'field': 'instoreCount', 'old_value': 1260, 'new_value': 1365}, {'field': 'onlineAmount', 'old_value': 15404.49, 'new_value': 17197.17}, {'field': 'onlineCount', 'old_value': 615, 'new_value': 691}]
2025-06-12 08:12:56,438 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:56,907 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-12 08:12:56,907 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49091.52, 'new_value': 51980.42}, {'field': 'dailyBillAmount', 'old_value': 49091.52, 'new_value': 51980.42}, {'field': 'amount', 'old_value': 15669.07, 'new_value': 16980.35}, {'field': 'count', 'old_value': 262, 'new_value': 290}, {'field': 'instoreAmount', 'old_value': 10850.6, 'new_value': 11851.36}, {'field': 'instoreCount', 'old_value': 163, 'new_value': 184}, {'field': 'onlineAmount', 'old_value': 4947.85, 'new_value': 5259.24}, {'field': 'onlineCount', 'old_value': 99, 'new_value': 106}]
2025-06-12 08:12:56,954 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:57,391 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-12 08:12:57,391 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 306161.63, 'new_value': 328625.48}, {'field': 'dailyBillAmount', 'old_value': 306161.63, 'new_value': 328625.48}, {'field': 'amount', 'old_value': 220555.4, 'new_value': 237885.4}, {'field': 'count', 'old_value': 1513, 'new_value': 1632}, {'field': 'instoreAmount', 'old_value': 155767.3, 'new_value': 165089.5}, {'field': 'instoreCount', 'old_value': 1207, 'new_value': 1289}, {'field': 'onlineAmount', 'old_value': 64788.1, 'new_value': 72795.9}, {'field': 'onlineCount', 'old_value': 306, 'new_value': 343}]
2025-06-12 08:12:57,391 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:57,860 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-12 08:12:57,860 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 412834.6, 'new_value': 450433.5}, {'field': 'amount', 'old_value': 412834.6, 'new_value': 450433.5}, {'field': 'count', 'old_value': 1418, 'new_value': 1544}, {'field': 'instoreAmount', 'old_value': 412691.6, 'new_value': 450290.5}, {'field': 'instoreCount', 'old_value': 1417, 'new_value': 1543}]
2025-06-12 08:12:57,860 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:58,266 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-12 08:12:58,266 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 210953.62, 'new_value': 229815.03}, {'field': 'dailyBillAmount', 'old_value': 186939.25, 'new_value': 202344.71}, {'field': 'amount', 'old_value': 210953.62, 'new_value': 229815.03}, {'field': 'count', 'old_value': 1379, 'new_value': 1545}, {'field': 'instoreAmount', 'old_value': 191593.6, 'new_value': 207499.61}, {'field': 'instoreCount', 'old_value': 815, 'new_value': 890}, {'field': 'onlineAmount', 'old_value': 19570.9, 'new_value': 22560.29}, {'field': 'onlineCount', 'old_value': 564, 'new_value': 655}]
2025-06-12 08:12:58,266 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:58,657 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-12 08:12:58,657 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 152724.27, 'new_value': 170056.36}, {'field': 'dailyBillAmount', 'old_value': 138621.21, 'new_value': 155951.3}, {'field': 'amount', 'old_value': 152724.27, 'new_value': 170056.36}, {'field': 'count', 'old_value': 488, 'new_value': 539}, {'field': 'instoreAmount', 'old_value': 138508.3, 'new_value': 155415.7}, {'field': 'instoreCount', 'old_value': 364, 'new_value': 408}, {'field': 'onlineAmount', 'old_value': 14382.8, 'new_value': 14807.49}, {'field': 'onlineCount', 'old_value': 124, 'new_value': 131}]
2025-06-12 08:12:58,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:59,094 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-12 08:12:59,094 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 84943.64, 'new_value': 96579.08}, {'field': 'dailyBillAmount', 'old_value': 84943.64, 'new_value': 96579.08}, {'field': 'amount', 'old_value': 10902.15, 'new_value': 11957.02}, {'field': 'count', 'old_value': 395, 'new_value': 437}, {'field': 'instoreAmount', 'old_value': 12203.93, 'new_value': 13391.97}, {'field': 'instoreCount', 'old_value': 395, 'new_value': 437}]
2025-06-12 08:12:59,094 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:12:59,626 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-12 08:12:59,626 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41029.87, 'new_value': 43966.07}, {'field': 'dailyBillAmount', 'old_value': 20076.3, 'new_value': 20881.9}, {'field': 'amount', 'old_value': 41029.87, 'new_value': 43966.07}, {'field': 'count', 'old_value': 1054, 'new_value': 1137}, {'field': 'instoreAmount', 'old_value': 21695.03, 'new_value': 22658.91}, {'field': 'instoreCount', 'old_value': 556, 'new_value': 588}, {'field': 'onlineAmount', 'old_value': 20130.36, 'new_value': 22102.68}, {'field': 'onlineCount', 'old_value': 498, 'new_value': 549}]
2025-06-12 08:12:59,626 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:00,126 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-12 08:13:00,126 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2511.22, 'new_value': 2761.39}, {'field': 'count', 'old_value': 116, 'new_value': 129}, {'field': 'onlineAmount', 'old_value': 2565.29, 'new_value': 2833.54}, {'field': 'onlineCount', 'old_value': 116, 'new_value': 129}]
2025-06-12 08:13:00,126 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:00,547 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-12 08:13:00,547 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46840.48, 'new_value': 52591.77}, {'field': 'dailyBillAmount', 'old_value': 46840.48, 'new_value': 52591.77}, {'field': 'amount', 'old_value': 2726.58, 'new_value': 2999.29}, {'field': 'count', 'old_value': 107, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 2726.58, 'new_value': 2999.29}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 117}]
2025-06-12 08:13:00,547 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:01,047 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-12 08:13:01,047 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62886.8, 'new_value': 70555.8}, {'field': 'dailyBillAmount', 'old_value': 62886.8, 'new_value': 70555.8}, {'field': 'amount', 'old_value': 61638.0, 'new_value': 69229.0}, {'field': 'count', 'old_value': 104, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 61638.0, 'new_value': 69229.0}, {'field': 'instoreCount', 'old_value': 104, 'new_value': 115}]
2025-06-12 08:13:01,047 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:01,532 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-12 08:13:01,532 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72872.11, 'new_value': 80118.92}, {'field': 'dailyBillAmount', 'old_value': 69254.91, 'new_value': 76501.72}, {'field': 'amount', 'old_value': 50089.37, 'new_value': 55253.18}, {'field': 'count', 'old_value': 1526, 'new_value': 1639}, {'field': 'instoreAmount', 'old_value': 11746.16, 'new_value': 13176.06}, {'field': 'instoreCount', 'old_value': 242, 'new_value': 269}, {'field': 'onlineAmount', 'old_value': 38638.94, 'new_value': 42372.85}, {'field': 'onlineCount', 'old_value': 1284, 'new_value': 1370}]
2025-06-12 08:13:01,532 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:01,985 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-12 08:13:01,985 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 141521.56, 'new_value': 159989.43}, {'field': 'amount', 'old_value': 141521.56, 'new_value': 159989.43}, {'field': 'count', 'old_value': 1534, 'new_value': 1746}, {'field': 'instoreAmount', 'old_value': 132181.4, 'new_value': 149505.5}, {'field': 'instoreCount', 'old_value': 1219, 'new_value': 1393}, {'field': 'onlineAmount', 'old_value': 10947.42, 'new_value': 12130.59}, {'field': 'onlineCount', 'old_value': 315, 'new_value': 353}]
2025-06-12 08:13:01,985 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:02,391 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-12 08:13:02,391 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 7864.91, 'new_value': 8745.92}, {'field': 'count', 'old_value': 398, 'new_value': 446}, {'field': 'onlineAmount', 'old_value': 7923.54, 'new_value': 8804.55}, {'field': 'onlineCount', 'old_value': 398, 'new_value': 446}]
2025-06-12 08:13:02,391 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:02,829 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-12 08:13:02,829 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 137661.46, 'new_value': 149500.17}, {'field': 'dailyBillAmount', 'old_value': 137661.46, 'new_value': 149500.17}, {'field': 'amount', 'old_value': 140936.33, 'new_value': 152248.99}, {'field': 'count', 'old_value': 3996, 'new_value': 4346}, {'field': 'instoreAmount', 'old_value': 131392.42, 'new_value': 141946.92}, {'field': 'instoreCount', 'old_value': 3431, 'new_value': 3733}, {'field': 'onlineAmount', 'old_value': 12136.23, 'new_value': 13097.64}, {'field': 'onlineCount', 'old_value': 565, 'new_value': 613}]
2025-06-12 08:13:02,829 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:03,266 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-12 08:13:03,266 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29312.87, 'new_value': 32839.66}, {'field': 'amount', 'old_value': 29312.87, 'new_value': 32839.66}, {'field': 'count', 'old_value': 285, 'new_value': 315}, {'field': 'instoreAmount', 'old_value': 29629.03, 'new_value': 33155.82}, {'field': 'instoreCount', 'old_value': 285, 'new_value': 315}]
2025-06-12 08:13:03,266 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:03,719 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-12 08:13:03,719 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7960.3, 'new_value': 8223.52}, {'field': 'amount', 'old_value': 7960.3, 'new_value': 8223.52}, {'field': 'count', 'old_value': 170, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 7970.08, 'new_value': 8233.3}, {'field': 'instoreCount', 'old_value': 170, 'new_value': 183}]
2025-06-12 08:13:03,719 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:04,141 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-12 08:13:04,141 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52287.03, 'new_value': 58453.23}, {'field': 'dailyBillAmount', 'old_value': 52287.03, 'new_value': 58453.23}, {'field': 'amount', 'old_value': 48951.18, 'new_value': 59823.78}, {'field': 'count', 'old_value': 56, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 51345.05, 'new_value': 62217.65}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 60}]
2025-06-12 08:13:04,141 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:04,594 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-12 08:13:04,594 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 293759.7, 'new_value': 321448.61}, {'field': 'amount', 'old_value': 293759.7, 'new_value': 321448.61}, {'field': 'count', 'old_value': 1813, 'new_value': 2009}, {'field': 'instoreAmount', 'old_value': 266205.1, 'new_value': 290285.55}, {'field': 'instoreCount', 'old_value': 961, 'new_value': 1052}, {'field': 'onlineAmount', 'old_value': 27590.63, 'new_value': 31199.09}, {'field': 'onlineCount', 'old_value': 852, 'new_value': 957}]
2025-06-12 08:13:04,594 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:05,032 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-12 08:13:05,032 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 292163.79, 'new_value': 324497.66}, {'field': 'dailyBillAmount', 'old_value': 292163.79, 'new_value': 324497.66}, {'field': 'amount', 'old_value': 279773.48, 'new_value': 310966.33}, {'field': 'count', 'old_value': 1432, 'new_value': 1594}, {'field': 'instoreAmount', 'old_value': 256283.87, 'new_value': 284910.99}, {'field': 'instoreCount', 'old_value': 1169, 'new_value': 1297}, {'field': 'onlineAmount', 'old_value': 23905.71, 'new_value': 26586.95}, {'field': 'onlineCount', 'old_value': 263, 'new_value': 297}]
2025-06-12 08:13:05,032 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:05,500 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-12 08:13:05,500 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 210312.42, 'new_value': 228036.14}, {'field': 'dailyBillAmount', 'old_value': 210312.42, 'new_value': 228036.14}, {'field': 'amount', 'old_value': 20996.3, 'new_value': 22172.2}, {'field': 'count', 'old_value': 107, 'new_value': 113}, {'field': 'instoreAmount', 'old_value': 20996.3, 'new_value': 22172.2}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 113}]
2025-06-12 08:13:05,500 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:06,016 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-12 08:13:06,016 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 88686.45, 'new_value': 98856.92}, {'field': 'dailyBillAmount', 'old_value': 88686.45, 'new_value': 98856.92}, {'field': 'amount', 'old_value': 56784.37, 'new_value': 63480.45}, {'field': 'count', 'old_value': 1498, 'new_value': 1667}, {'field': 'instoreAmount', 'old_value': 38356.37, 'new_value': 41833.17}, {'field': 'instoreCount', 'old_value': 763, 'new_value': 838}, {'field': 'onlineAmount', 'old_value': 26732.9, 'new_value': 30686.78}, {'field': 'onlineCount', 'old_value': 735, 'new_value': 829}]
2025-06-12 08:13:06,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:06,422 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-12 08:13:06,422 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 60952.44, 'new_value': 69115.61}, {'field': 'amount', 'old_value': 60952.44, 'new_value': 69115.61}, {'field': 'count', 'old_value': 2950, 'new_value': 3340}, {'field': 'instoreAmount', 'old_value': 61867.74, 'new_value': 70131.77}, {'field': 'instoreCount', 'old_value': 2950, 'new_value': 3340}]
2025-06-12 08:13:06,422 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:06,938 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-12 08:13:06,938 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23475.54, 'new_value': 26387.89}, {'field': 'dailyBillAmount', 'old_value': 23475.54, 'new_value': 26387.89}, {'field': 'amount', 'old_value': 15119.35, 'new_value': 16902.87}, {'field': 'count', 'old_value': 714, 'new_value': 802}, {'field': 'instoreAmount', 'old_value': 6326.58, 'new_value': 6849.17}, {'field': 'instoreCount', 'old_value': 200, 'new_value': 215}, {'field': 'onlineAmount', 'old_value': 8843.96, 'new_value': 10104.89}, {'field': 'onlineCount', 'old_value': 514, 'new_value': 587}]
2025-06-12 08:13:06,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:07,375 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-12 08:13:07,375 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26430.25, 'new_value': 29002.39}, {'field': 'amount', 'old_value': 26430.25, 'new_value': 29002.39}, {'field': 'count', 'old_value': 873, 'new_value': 970}, {'field': 'instoreAmount', 'old_value': 12667.39, 'new_value': 14115.69}, {'field': 'instoreCount', 'old_value': 549, 'new_value': 623}, {'field': 'onlineAmount', 'old_value': 13870.91, 'new_value': 14994.75}, {'field': 'onlineCount', 'old_value': 324, 'new_value': 347}]
2025-06-12 08:13:07,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:07,797 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-12 08:13:07,797 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16309.67, 'new_value': 17870.28}, {'field': 'amount', 'old_value': 16309.67, 'new_value': 17870.28}, {'field': 'count', 'old_value': 419, 'new_value': 469}, {'field': 'instoreAmount', 'old_value': 13093.3, 'new_value': 14163.5}, {'field': 'instoreCount', 'old_value': 350, 'new_value': 387}, {'field': 'onlineAmount', 'old_value': 3564.57, 'new_value': 4054.98}, {'field': 'onlineCount', 'old_value': 69, 'new_value': 82}]
2025-06-12 08:13:07,813 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:08,297 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-12 08:13:08,297 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 99946.97, 'new_value': 106567.6}, {'field': 'dailyBillAmount', 'old_value': 99946.97, 'new_value': 106567.6}, {'field': 'amount', 'old_value': 105749.6, 'new_value': 112826.6}, {'field': 'count', 'old_value': 778, 'new_value': 852}, {'field': 'instoreAmount', 'old_value': 106423.6, 'new_value': 113500.6}, {'field': 'instoreCount', 'old_value': 778, 'new_value': 852}]
2025-06-12 08:13:08,297 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:08,750 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-12 08:13:08,750 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 148036.02, 'new_value': 161359.57}, {'field': 'dailyBillAmount', 'old_value': 148036.02, 'new_value': 161359.57}, {'field': 'amount', 'old_value': 236688.33, 'new_value': 259160.59}, {'field': 'count', 'old_value': 409, 'new_value': 463}, {'field': 'instoreAmount', 'old_value': 233880.63, 'new_value': 255726.39}, {'field': 'instoreCount', 'old_value': 393, 'new_value': 444}, {'field': 'onlineAmount', 'old_value': 2807.7, 'new_value': 3434.2}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 19}]
2025-06-12 08:13:08,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:09,219 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-12 08:13:09,219 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 357390.51, 'new_value': 392486.4}, {'field': 'dailyBillAmount', 'old_value': 357390.51, 'new_value': 392486.4}, {'field': 'amount', 'old_value': 393990.33, 'new_value': 429090.48}, {'field': 'count', 'old_value': 1728, 'new_value': 1901}, {'field': 'instoreAmount', 'old_value': 393990.33, 'new_value': 429090.48}, {'field': 'instoreCount', 'old_value': 1728, 'new_value': 1901}]
2025-06-12 08:13:09,235 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:09,657 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-12 08:13:09,657 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 109507.27, 'new_value': 120262.23}, {'field': 'dailyBillAmount', 'old_value': 109507.27, 'new_value': 120262.23}, {'field': 'amount', 'old_value': 108114.3, 'new_value': 118902.94}, {'field': 'count', 'old_value': 569, 'new_value': 627}, {'field': 'instoreAmount', 'old_value': 103084.3, 'new_value': 113446.9}, {'field': 'instoreCount', 'old_value': 480, 'new_value': 530}, {'field': 'onlineAmount', 'old_value': 6296.7, 'new_value': 6723.06}, {'field': 'onlineCount', 'old_value': 89, 'new_value': 97}]
2025-06-12 08:13:09,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:10,078 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-12 08:13:10,078 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 709478.13, 'new_value': 785897.83}, {'field': 'dailyBillAmount', 'old_value': 709478.13, 'new_value': 785897.83}, {'field': 'amount', 'old_value': 620432.0, 'new_value': 678579.0}, {'field': 'count', 'old_value': 1475, 'new_value': 1603}, {'field': 'instoreAmount', 'old_value': 651812.0, 'new_value': 710270.0}, {'field': 'instoreCount', 'old_value': 1475, 'new_value': 1603}]
2025-06-12 08:13:10,078 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:10,531 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-12 08:13:10,531 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 444087.1, 'new_value': 487180.1}, {'field': 'amount', 'old_value': 444087.1, 'new_value': 487180.1}, {'field': 'count', 'old_value': 1423, 'new_value': 1562}, {'field': 'instoreAmount', 'old_value': 446032.1, 'new_value': 489125.1}, {'field': 'instoreCount', 'old_value': 1423, 'new_value': 1562}]
2025-06-12 08:13:10,531 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:11,063 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-12 08:13:11,063 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 370909.7, 'new_value': 416980.42}, {'field': 'dailyBillAmount', 'old_value': 370909.7, 'new_value': 416980.42}, {'field': 'amount', 'old_value': 257148.18, 'new_value': 286018.46}, {'field': 'count', 'old_value': 992, 'new_value': 1112}, {'field': 'instoreAmount', 'old_value': 251430.99, 'new_value': 278888.2}, {'field': 'instoreCount', 'old_value': 582, 'new_value': 648}, {'field': 'onlineAmount', 'old_value': 12197.7, 'new_value': 13863.2}, {'field': 'onlineCount', 'old_value': 410, 'new_value': 464}]
2025-06-12 08:13:11,063 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:11,547 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-12 08:13:11,547 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 269432.79, 'new_value': 301873.87}, {'field': 'dailyBillAmount', 'old_value': 269432.79, 'new_value': 301873.87}, {'field': 'amount', 'old_value': 251595.04, 'new_value': 279541.12}, {'field': 'count', 'old_value': 726, 'new_value': 819}, {'field': 'instoreAmount', 'old_value': 258145.95, 'new_value': 288134.55}, {'field': 'instoreCount', 'old_value': 590, 'new_value': 666}, {'field': 'onlineAmount', 'old_value': 3594.37, 'new_value': 4004.33}, {'field': 'onlineCount', 'old_value': 136, 'new_value': 153}]
2025-06-12 08:13:11,610 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:12,078 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-12 08:13:12,078 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 71518.49, 'new_value': 79924.16}, {'field': 'dailyBillAmount', 'old_value': 71518.49, 'new_value': 79924.16}, {'field': 'amount', 'old_value': 75652.55, 'new_value': 84290.75}, {'field': 'count', 'old_value': 502, 'new_value': 558}, {'field': 'instoreAmount', 'old_value': 71580.0, 'new_value': 79883.0}, {'field': 'instoreCount', 'old_value': 436, 'new_value': 485}, {'field': 'onlineAmount', 'old_value': 4131.55, 'new_value': 4466.75}, {'field': 'onlineCount', 'old_value': 66, 'new_value': 73}]
2025-06-12 08:13:12,078 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:12,578 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-12 08:13:12,578 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49898.8, 'new_value': 55333.8}, {'field': 'dailyBillAmount', 'old_value': 49898.8, 'new_value': 55333.8}, {'field': 'amount', 'old_value': 60571.0, 'new_value': 66686.0}, {'field': 'count', 'old_value': 250, 'new_value': 272}, {'field': 'instoreAmount', 'old_value': 60571.0, 'new_value': 66686.0}, {'field': 'instoreCount', 'old_value': 250, 'new_value': 272}]
2025-06-12 08:13:12,578 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:12,953 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-12 08:13:12,953 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3230.0, 'new_value': 8207.0}, {'field': 'dailyBillAmount', 'old_value': 3230.0, 'new_value': 8207.0}, {'field': 'amount', 'old_value': 23211.2, 'new_value': 24014.0}, {'field': 'count', 'old_value': 118, 'new_value': 123}, {'field': 'instoreAmount', 'old_value': 23211.2, 'new_value': 24014.0}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 123}]
2025-06-12 08:13:12,953 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:13,391 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-12 08:13:13,391 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40489.0, 'new_value': 43971.0}, {'field': 'amount', 'old_value': 40489.0, 'new_value': 43971.0}, {'field': 'count', 'old_value': 447, 'new_value': 494}, {'field': 'instoreAmount', 'old_value': 40489.0, 'new_value': 43971.0}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 494}]
2025-06-12 08:13:13,391 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:13,813 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-12 08:13:13,813 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11519.96, 'new_value': 13291.28}, {'field': 'dailyBillAmount', 'old_value': 11519.96, 'new_value': 13291.28}, {'field': 'amount', 'old_value': 1435.66, 'new_value': 1827.66}, {'field': 'count', 'old_value': 65, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 1691.86, 'new_value': 2083.86}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 85}]
2025-06-12 08:13:13,813 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:14,297 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-12 08:13:14,297 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'amount', 'old_value': 16131.0, 'new_value': 17399.0}, {'field': 'count', 'old_value': 81, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 16131.0, 'new_value': 17399.0}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 86}]
2025-06-12 08:13:14,297 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:14,781 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-12 08:13:14,781 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28779.8, 'new_value': 31364.95}, {'field': 'dailyBillAmount', 'old_value': 28779.8, 'new_value': 31364.95}, {'field': 'amount', 'old_value': 21970.35, 'new_value': 24476.76}, {'field': 'count', 'old_value': 685, 'new_value': 774}, {'field': 'instoreAmount', 'old_value': 21576.55, 'new_value': 23817.96}, {'field': 'instoreCount', 'old_value': 665, 'new_value': 741}, {'field': 'onlineAmount', 'old_value': 393.8, 'new_value': 658.8}, {'field': 'onlineCount', 'old_value': 20, 'new_value': 33}]
2025-06-12 08:13:14,781 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:15,234 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-12 08:13:15,234 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35681.73, 'new_value': 39880.05}, {'field': 'dailyBillAmount', 'old_value': 35681.73, 'new_value': 39880.05}, {'field': 'amount', 'old_value': 34891.8, 'new_value': 39016.1}, {'field': 'count', 'old_value': 172, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 34589.1, 'new_value': 38651.4}, {'field': 'instoreCount', 'old_value': 160, 'new_value': 170}, {'field': 'onlineAmount', 'old_value': 382.7, 'new_value': 444.7}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 13}]
2025-06-12 08:13:15,234 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:15,656 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-12 08:13:15,656 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66792.85, 'new_value': 103672.37}, {'field': 'dailyBillAmount', 'old_value': 66792.85, 'new_value': 103672.37}, {'field': 'amount', 'old_value': 73616.5, 'new_value': 82729.5}, {'field': 'count', 'old_value': 485, 'new_value': 538}, {'field': 'instoreAmount', 'old_value': 74658.0, 'new_value': 83974.0}, {'field': 'instoreCount', 'old_value': 468, 'new_value': 520}, {'field': 'onlineAmount', 'old_value': 816.5, 'new_value': 843.5}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 18}]
2025-06-12 08:13:15,656 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:16,172 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM0U
2025-06-12 08:13:16,172 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13947.9, 'new_value': 15145.0}, {'field': 'dailyBillAmount', 'old_value': 13947.9, 'new_value': 15145.0}]
2025-06-12 08:13:16,172 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:16,750 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-12 08:13:16,750 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 34450.06, 'new_value': 37699.25}, {'field': 'count', 'old_value': 1900, 'new_value': 2060}, {'field': 'instoreAmount', 'old_value': 4861.86, 'new_value': 5793.27}, {'field': 'instoreCount', 'old_value': 432, 'new_value': 476}, {'field': 'onlineAmount', 'old_value': 31092.4, 'new_value': 33411.0}, {'field': 'onlineCount', 'old_value': 1468, 'new_value': 1584}]
2025-06-12 08:13:16,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:17,234 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-12 08:13:17,234 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 85310.03, 'new_value': 91701.17}, {'field': 'amount', 'old_value': 85309.19, 'new_value': 91699.36}, {'field': 'count', 'old_value': 1579, 'new_value': 1721}, {'field': 'instoreAmount', 'old_value': 70102.66, 'new_value': 75694.05}, {'field': 'instoreCount', 'old_value': 1311, 'new_value': 1437}, {'field': 'onlineAmount', 'old_value': 15207.37, 'new_value': 16007.12}, {'field': 'onlineCount', 'old_value': 268, 'new_value': 284}]
2025-06-12 08:13:17,234 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:17,688 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-12 08:13:17,688 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13913.0, 'new_value': 15703.8}, {'field': 'dailyBillAmount', 'old_value': 8269.0, 'new_value': 8491.3}, {'field': 'amount', 'old_value': 13913.0, 'new_value': 15703.8}, {'field': 'count', 'old_value': 86, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 13913.0, 'new_value': 15703.8}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 99}]
2025-06-12 08:13:17,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:18,094 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-12 08:13:18,094 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'amount', 'old_value': 10264.6, 'new_value': 10230.4}, {'field': 'count', 'old_value': 99, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 10432.6, 'new_value': 10566.4}, {'field': 'instoreCount', 'old_value': 99, 'new_value': 103}]
2025-06-12 08:13:18,094 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:18,516 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-12 08:13:18,516 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49823.3, 'new_value': 56550.58}, {'field': 'dailyBillAmount', 'old_value': 49823.3, 'new_value': 56550.58}, {'field': 'amount', 'old_value': 36551.91, 'new_value': 42176.72}, {'field': 'count', 'old_value': 330, 'new_value': 379}, {'field': 'instoreAmount', 'old_value': 36551.91, 'new_value': 42176.72}, {'field': 'instoreCount', 'old_value': 330, 'new_value': 379}]
2025-06-12 08:13:18,516 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:18,937 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-12 08:13:18,937 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 92437.7, 'new_value': 98701.2}, {'field': 'dailyBillAmount', 'old_value': 92437.7, 'new_value': 98701.2}, {'field': 'amount', 'old_value': 68809.28, 'new_value': 73630.46}, {'field': 'count', 'old_value': 2303, 'new_value': 2504}, {'field': 'instoreAmount', 'old_value': 65041.33, 'new_value': 70036.71}, {'field': 'instoreCount', 'old_value': 2195, 'new_value': 2383}, {'field': 'onlineAmount', 'old_value': 4122.95, 'new_value': 4540.95}, {'field': 'onlineCount', 'old_value': 108, 'new_value': 121}]
2025-06-12 08:13:18,937 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:19,422 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-12 08:13:19,422 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21984.7, 'new_value': 23430.5}, {'field': 'dailyBillAmount', 'old_value': 21984.7, 'new_value': 23430.5}, {'field': 'amount', 'old_value': 20510.0, 'new_value': 21955.8}, {'field': 'count', 'old_value': 123, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 21037.5, 'new_value': 22483.3}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 133}]
2025-06-12 08:13:19,422 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:19,844 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-12 08:13:19,844 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24901.89, 'new_value': 27992.26}, {'field': 'dailyBillAmount', 'old_value': 24901.89, 'new_value': 27992.26}]
2025-06-12 08:13:19,859 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:20,297 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-12 08:13:20,297 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20194.55, 'new_value': 21880.97}, {'field': 'amount', 'old_value': 20192.67, 'new_value': 21878.35}, {'field': 'count', 'old_value': 1251, 'new_value': 1365}, {'field': 'instoreAmount', 'old_value': 20389.99, 'new_value': 22137.59}, {'field': 'instoreCount', 'old_value': 1251, 'new_value': 1365}]
2025-06-12 08:13:20,312 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:20,781 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-12 08:13:20,781 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30802.15, 'new_value': 34042.03}, {'field': 'dailyBillAmount', 'old_value': 30802.15, 'new_value': 34042.03}, {'field': 'amount', 'old_value': 31374.03, 'new_value': 34738.2}, {'field': 'count', 'old_value': 1577, 'new_value': 1744}, {'field': 'instoreAmount', 'old_value': 28588.2, 'new_value': 31522.2}, {'field': 'instoreCount', 'old_value': 1423, 'new_value': 1574}, {'field': 'onlineAmount', 'old_value': 2984.62, 'new_value': 3414.79}, {'field': 'onlineCount', 'old_value': 154, 'new_value': 170}]
2025-06-12 08:13:20,781 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:21,250 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-12 08:13:21,250 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23178.2, 'new_value': 24851.73}, {'field': 'amount', 'old_value': 23178.2, 'new_value': 24851.73}, {'field': 'count', 'old_value': 1097, 'new_value': 1180}, {'field': 'instoreAmount', 'old_value': 13374.13, 'new_value': 14578.66}, {'field': 'instoreCount', 'old_value': 666, 'new_value': 727}, {'field': 'onlineAmount', 'old_value': 9841.89, 'new_value': 10310.89}, {'field': 'onlineCount', 'old_value': 431, 'new_value': 453}]
2025-06-12 08:13:21,250 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:21,687 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-12 08:13:21,687 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18380.6, 'new_value': 19679.77}, {'field': 'dailyBillAmount', 'old_value': 18380.6, 'new_value': 19679.77}, {'field': 'amount', 'old_value': 12800.89, 'new_value': 13889.51}, {'field': 'count', 'old_value': 483, 'new_value': 530}, {'field': 'instoreAmount', 'old_value': 12947.29, 'new_value': 14035.91}, {'field': 'instoreCount', 'old_value': 483, 'new_value': 530}]
2025-06-12 08:13:21,687 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:22,125 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-12 08:13:22,125 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26841.53, 'new_value': 28975.9}, {'field': 'amount', 'old_value': 26841.01, 'new_value': 28974.88}, {'field': 'count', 'old_value': 1562, 'new_value': 1682}, {'field': 'instoreAmount', 'old_value': 5545.53, 'new_value': 6072.44}, {'field': 'instoreCount', 'old_value': 271, 'new_value': 293}, {'field': 'onlineAmount', 'old_value': 21907.69, 'new_value': 23760.79}, {'field': 'onlineCount', 'old_value': 1291, 'new_value': 1389}]
2025-06-12 08:13:22,125 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-12 08:13:22,625 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-12 08:13:22,625 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20685.0, 'new_value': 22532.0}, {'field': 'dailyBillAmount', 'old_value': 20685.0, 'new_value': 22532.0}]
2025-06-12 08:13:22,625 - WARNING - 批量插入月度数据失败，将在 2 秒后重试 (尝试 1/3): Object of type Decimal is not JSON serializable
2025-06-12 08:13:24,640 - WARNING - 批量插入月度数据失败，将在 4 秒后重试 (尝试 2/3): Object of type Decimal is not JSON serializable
2025-06-12 08:13:28,656 - ERROR - 批量插入月度数据失败达到最大重试次数，跳过当前批次: Object of type Decimal is not JSON serializable
2025-06-12 08:13:31,671 - INFO - 批量插入月度数据完成: 总计 7 条，成功 0 条，失败 7 条
2025-06-12 08:13:31,671 - INFO - 批量插入月销售数据完成，共 7 条记录
2025-06-12 08:13:31,671 - INFO - 月销售数据同步完成！更新: 202 条，插入: 7 条，错误: 0 条，跳过: 1201 条
2025-06-12 08:13:31,671 - INFO - 综合数据同步流程完成！
2025-06-12 08:13:31,734 - INFO - 综合数据同步完成
2025-06-12 08:13:31,734 - INFO - MySQL数据库连接已关闭
2025-06-12 08:13:31,734 - INFO - ==================================================
2025-06-12 08:13:31,734 - INFO - 程序退出
2025-06-12 08:13:31,734 - INFO - ==================================================
