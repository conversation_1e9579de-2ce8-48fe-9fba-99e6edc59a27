2025-07-29 01:30:33,487 - INFO - 使用默认增量同步（当天更新数据）
2025-07-29 01:30:33,487 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-29 01:30:33,487 - INFO - 查询参数: ('2025-07-29',)
2025-07-29 01:30:33,580 - INFO - MySQL查询成功，增量数据（日期: 2025-07-29），共获取 0 条记录
2025-07-29 01:30:33,580 - ERROR - 未获取到MySQL数据
2025-07-29 01:31:33,596 - INFO - 开始同步昨天与今天的销售数据: 2025-07-28 至 2025-07-29
2025-07-29 01:31:33,596 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-29 01:31:33,596 - INFO - 查询参数: ('2025-07-28', '2025-07-29')
2025-07-29 01:31:33,752 - INFO - MySQL查询成功，时间段: 2025-07-28 至 2025-07-29，共获取 94 条记录
2025-07-29 01:31:33,752 - INFO - 获取到 1 个日期需要处理: ['2025-07-28']
2025-07-29 01:31:33,752 - INFO - 开始处理日期: 2025-07-28
2025-07-29 01:31:33,752 - INFO - Request Parameters - Page 1:
2025-07-29 01:31:33,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 01:31:33,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 01:31:41,877 - ERROR - 处理日期 2025-07-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2C583599-E497-7B52-9EFB-70F88C35D7A2 Response: {'code': 'ServiceUnavailable', 'requestid': '2C583599-E497-7B52-9EFB-70F88C35D7A2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2C583599-E497-7B52-9EFB-70F88C35D7A2)
2025-07-29 01:31:41,877 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-29 01:31:41,877 - INFO - 同步完成
2025-07-29 04:30:33,913 - INFO - 使用默认增量同步（当天更新数据）
2025-07-29 04:30:33,913 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-29 04:30:33,913 - INFO - 查询参数: ('2025-07-29',)
2025-07-29 04:30:34,069 - INFO - MySQL查询成功，增量数据（日期: 2025-07-29），共获取 1 条记录
2025-07-29 04:30:34,069 - INFO - 获取到 1 个日期需要处理: ['2025-07-28']
2025-07-29 04:30:34,069 - INFO - 开始处理日期: 2025-07-28
2025-07-29 04:30:34,085 - INFO - Request Parameters - Page 1:
2025-07-29 04:30:34,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 04:30:34,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 04:30:42,194 - ERROR - 处理日期 2025-07-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3474BFBA-E242-7F13-8928-26009A5ECC5A Response: {'code': 'ServiceUnavailable', 'requestid': '3474BFBA-E242-7F13-8928-26009A5ECC5A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3474BFBA-E242-7F13-8928-26009A5ECC5A)
2025-07-29 04:30:42,194 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-29 04:31:42,209 - INFO - 开始同步昨天与今天的销售数据: 2025-07-28 至 2025-07-29
2025-07-29 04:31:42,209 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-29 04:31:42,209 - INFO - 查询参数: ('2025-07-28', '2025-07-29')
2025-07-29 04:31:42,366 - INFO - MySQL查询成功，时间段: 2025-07-28 至 2025-07-29，共获取 95 条记录
2025-07-29 04:31:42,366 - INFO - 获取到 1 个日期需要处理: ['2025-07-28']
2025-07-29 04:31:42,366 - INFO - 开始处理日期: 2025-07-28
2025-07-29 04:31:42,366 - INFO - Request Parameters - Page 1:
2025-07-29 04:31:42,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 04:31:42,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 04:31:43,162 - INFO - Response - Page 1:
2025-07-29 04:31:43,162 - INFO - 第 1 页获取到 40 条记录
2025-07-29 04:31:43,678 - INFO - 查询完成，共获取到 40 条记录
2025-07-29 04:31:43,678 - INFO - 获取到 40 条表单数据
2025-07-29 04:31:43,678 - INFO - 当前日期 2025-07-28 有 93 条MySQL数据需要处理
2025-07-29 04:31:43,678 - INFO - 开始批量插入 53 条新记录
2025-07-29 04:31:43,928 - INFO - 批量插入响应状态码: 200
2025-07-29 04:31:43,928 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 20:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '62D3C2E1-3FFD-7D88-A2AE-B62EA30204CB', 'x-acs-trace-id': '85864dc998600872aa3f53c4cc835988', 'etag': '2p3/iKUE8/04H2nay253cBQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 04:31:43,928 - INFO - 批量插入响应体: {'result': ['FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMBI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMCI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMDI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMEI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMFI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMGI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMHI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMII', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMJI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMKI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMLI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMMI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMNI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMOI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMPI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMQI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMRI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMSI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMTI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMUI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMVI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMWI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMXI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMYI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMZI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDM0J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDM1J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDM2J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM3J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM4J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM5J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM6J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM7J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM8J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM9J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMAJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMBJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMCJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMDJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMEJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMFJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMGJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMHJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMIJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMJJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMKJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMLJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMMJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMNJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMOJ']}
2025-07-29 04:31:43,928 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-29 04:31:43,928 - INFO - 成功插入的数据ID: ['FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMBI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMCI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMDI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMEI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMFI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMGI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMHI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMII', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMJI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMKI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMLI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMMI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMNI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMOI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMPI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMQI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMRI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMSI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMTI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMUI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMVI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMWI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMXI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMYI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDMZI', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDM0J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDM1J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2TQ1DKNDM2J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM3J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM4J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM5J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM6J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM7J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM8J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDM9J', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMAJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMBJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMCJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMDJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMEJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMFJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMGJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMHJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMIJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMJJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMKJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMLJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMMJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMNJ', 'FINST-V4G66WC1LGJX3V6A6VV39884S4FI2UQ1DKNDMOJ']
2025-07-29 04:31:49,084 - INFO - 批量插入响应状态码: 200
2025-07-29 04:31:49,084 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 20:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D8B869FB-D97F-7BA2-83AA-9B5543EDE525', 'x-acs-trace-id': 'e087247e385420aa7e1a53b1046406a1', 'etag': '1pqoV+CbKNZFSriJ8J3Pl6A6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 04:31:49,084 - INFO - 批量插入响应体: {'result': ['FINST-B9C660C1AEJX7LQ28QS4MB464CF336Q5DKNDMYC', 'FINST-B9C660C1AEJX7LQ28QS4MB464CF336Q5DKNDMZC', 'FINST-B9C660C1AEJX7LQ28QS4MB464CF336Q5DKNDM0D']}
2025-07-29 04:31:49,084 - INFO - 批量插入表单数据成功，批次 2，共 3 条记录
2025-07-29 04:31:49,084 - INFO - 成功插入的数据ID: ['FINST-B9C660C1AEJX7LQ28QS4MB464CF336Q5DKNDMYC', 'FINST-B9C660C1AEJX7LQ28QS4MB464CF336Q5DKNDMZC', 'FINST-B9C660C1AEJX7LQ28QS4MB464CF336Q5DKNDM0D']
2025-07-29 04:31:54,100 - INFO - 批量插入完成，共 53 条记录
2025-07-29 04:31:54,100 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 53 条，错误: 0 条
2025-07-29 04:31:54,100 - INFO - 数据同步完成！更新: 0 条，插入: 53 条，错误: 0 条
2025-07-29 04:31:54,100 - INFO - 同步完成
2025-07-29 07:30:33,667 - INFO - 使用默认增量同步（当天更新数据）
2025-07-29 07:30:33,667 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-29 07:30:33,667 - INFO - 查询参数: ('2025-07-29',)
2025-07-29 07:30:33,824 - INFO - MySQL查询成功，增量数据（日期: 2025-07-29），共获取 5 条记录
2025-07-29 07:30:33,824 - INFO - 获取到 1 个日期需要处理: ['2025-07-28']
2025-07-29 07:30:33,824 - INFO - 开始处理日期: 2025-07-28
2025-07-29 07:30:33,824 - INFO - Request Parameters - Page 1:
2025-07-29 07:30:33,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 07:30:33,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 07:30:41,949 - ERROR - 处理日期 2025-07-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8DA2C56D-86D8-795C-AF38-4D2AA1DE5B09 Response: {'code': 'ServiceUnavailable', 'requestid': '8DA2C56D-86D8-795C-AF38-4D2AA1DE5B09', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8DA2C56D-86D8-795C-AF38-4D2AA1DE5B09)
2025-07-29 07:30:41,949 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-29 07:31:41,964 - INFO - 开始同步昨天与今天的销售数据: 2025-07-28 至 2025-07-29
2025-07-29 07:31:41,964 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-29 07:31:41,964 - INFO - 查询参数: ('2025-07-28', '2025-07-29')
2025-07-29 07:31:42,120 - INFO - MySQL查询成功，时间段: 2025-07-28 至 2025-07-29，共获取 116 条记录
2025-07-29 07:31:42,120 - INFO - 获取到 1 个日期需要处理: ['2025-07-28']
2025-07-29 07:31:42,120 - INFO - 开始处理日期: 2025-07-28
2025-07-29 07:31:42,120 - INFO - Request Parameters - Page 1:
2025-07-29 07:31:42,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 07:31:42,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 07:31:42,870 - INFO - Response - Page 1:
2025-07-29 07:31:42,870 - INFO - 第 1 页获取到 50 条记录
2025-07-29 07:31:43,370 - INFO - Request Parameters - Page 2:
2025-07-29 07:31:43,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 07:31:43,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 07:31:44,026 - INFO - Response - Page 2:
2025-07-29 07:31:44,026 - INFO - 第 2 页获取到 43 条记录
2025-07-29 07:31:44,542 - INFO - 查询完成，共获取到 93 条记录
2025-07-29 07:31:44,542 - INFO - 获取到 93 条表单数据
2025-07-29 07:31:44,542 - INFO - 当前日期 2025-07-28 有 113 条MySQL数据需要处理
2025-07-29 07:31:44,542 - INFO - 开始批量插入 20 条新记录
2025-07-29 07:31:44,776 - INFO - 批量插入响应状态码: 200
2025-07-29 07:31:44,776 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 23:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '972', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '70373D5A-C512-7616-946F-5F0B50B3AF23', 'x-acs-trace-id': 'ebddee9409fc4439ea3aa315d1ae5055', 'etag': '92We3lpTOgIxGuU+HEYwSpw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 07:31:44,776 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMIE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMJE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMKE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMLE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMME', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMNE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMOE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMPE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMQE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMRE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMSE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMTE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMUE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMVE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMWE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMXE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMYE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMZE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDM0F', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDM1F']}
2025-07-29 07:31:44,776 - INFO - 批量插入表单数据成功，批次 1，共 20 条记录
2025-07-29 07:31:44,776 - INFO - 成功插入的数据ID: ['FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMIE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMJE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMKE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMLE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMME', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMNE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMOE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMPE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMQE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMRE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMSE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMTE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMUE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMVE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMWE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMXE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMYE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDMZE', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDM0F', 'FINST-AAG66KB1UEJX22P2E5DASDC214DX2BSJSQNDM1F']
2025-07-29 07:31:49,792 - INFO - 批量插入完成，共 20 条记录
2025-07-29 07:31:49,792 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 20 条，错误: 0 条
2025-07-29 07:31:49,792 - INFO - 数据同步完成！更新: 0 条，插入: 20 条，错误: 0 条
2025-07-29 07:31:49,792 - INFO - 同步完成
2025-07-29 10:30:33,867 - INFO - 使用默认增量同步（当天更新数据）
2025-07-29 10:30:33,867 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-29 10:30:33,867 - INFO - 查询参数: ('2025-07-29',)
2025-07-29 10:30:34,038 - INFO - MySQL查询成功，增量数据（日期: 2025-07-29），共获取 132 条记录
2025-07-29 10:30:34,038 - INFO - 获取到 4 个日期需要处理: ['2025-07-24', '2025-07-27', '2025-07-28', '2025-07-29']
2025-07-29 10:30:34,038 - INFO - 开始处理日期: 2025-07-24
2025-07-29 10:30:34,038 - INFO - Request Parameters - Page 1:
2025-07-29 10:30:34,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:30:34,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:30:42,163 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FB513D37-3163-7C73-8E9D-1D1C34045CA6 Response: {'code': 'ServiceUnavailable', 'requestid': 'FB513D37-3163-7C73-8E9D-1D1C34045CA6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FB513D37-3163-7C73-8E9D-1D1C34045CA6)
2025-07-29 10:30:42,163 - INFO - 开始处理日期: 2025-07-27
2025-07-29 10:30:42,163 - INFO - Request Parameters - Page 1:
2025-07-29 10:30:42,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:30:42,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:30:42,898 - INFO - Response - Page 1:
2025-07-29 10:30:42,898 - INFO - 第 1 页获取到 50 条记录
2025-07-29 10:30:43,398 - INFO - Request Parameters - Page 2:
2025-07-29 10:30:43,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:30:43,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:30:44,195 - INFO - Response - Page 2:
2025-07-29 10:30:44,195 - INFO - 第 2 页获取到 50 条记录
2025-07-29 10:30:44,710 - INFO - Request Parameters - Page 3:
2025-07-29 10:30:44,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:30:44,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:30:45,492 - INFO - Response - Page 3:
2025-07-29 10:30:45,492 - INFO - 第 3 页获取到 50 条记录
2025-07-29 10:30:45,992 - INFO - Request Parameters - Page 4:
2025-07-29 10:30:45,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:30:45,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:30:54,101 - ERROR - 处理日期 2025-07-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DA29F0DF-BC0E-7BCD-BE22-FA01D609D81D Response: {'code': 'ServiceUnavailable', 'requestid': 'DA29F0DF-BC0E-7BCD-BE22-FA01D609D81D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DA29F0DF-BC0E-7BCD-BE22-FA01D609D81D)
2025-07-29 10:30:54,101 - INFO - 开始处理日期: 2025-07-28
2025-07-29 10:30:54,101 - INFO - Request Parameters - Page 1:
2025-07-29 10:30:54,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:30:54,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:30:58,960 - INFO - Response - Page 1:
2025-07-29 10:30:58,960 - INFO - 第 1 页获取到 50 条记录
2025-07-29 10:30:59,476 - INFO - Request Parameters - Page 2:
2025-07-29 10:30:59,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:30:59,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:31:00,210 - INFO - Response - Page 2:
2025-07-29 10:31:00,210 - INFO - 第 2 页获取到 50 条记录
2025-07-29 10:31:00,726 - INFO - Request Parameters - Page 3:
2025-07-29 10:31:00,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:31:00,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:31:01,304 - INFO - Response - Page 3:
2025-07-29 10:31:01,304 - INFO - 第 3 页获取到 13 条记录
2025-07-29 10:31:01,820 - INFO - 查询完成，共获取到 113 条记录
2025-07-29 10:31:01,820 - INFO - 获取到 113 条表单数据
2025-07-29 10:31:01,820 - INFO - 当前日期 2025-07-28 有 124 条MySQL数据需要处理
2025-07-29 10:31:01,820 - INFO - 开始批量插入 119 条新记录
2025-07-29 10:31:02,101 - INFO - 批量插入响应状态码: 200
2025-07-29 10:31:02,101 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BC3B426B-35D1-7934-A41E-8966F4DF3066', 'x-acs-trace-id': 'db55c522368b336e31cb395d9c559944', 'etag': '2UBMAopPgZuIclrev5ftfow2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:31:02,101 - INFO - 批量插入响应体: {'result': ['FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMXE2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMYE2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMZE2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM0F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM1F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM2F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM3F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM4F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM5F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM6F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM7F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM8F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM9F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMAF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMBF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMCF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMDF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMEF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMFF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMGF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMHF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMIF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMJF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMKF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMLF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMMF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMNF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMOF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMPF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMQF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMRF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMSF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMTF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMUF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMVF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMWF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMXF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMYF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMZF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM0G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM1G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM2G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM3G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM4G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM5G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM6G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM7G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM8G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM9G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMAG2']}
2025-07-29 10:31:02,101 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-29 10:31:02,101 - INFO - 成功插入的数据ID: ['FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMXE2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMYE2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMZE2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM0F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM1F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM2F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM3F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM4F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM5F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM6F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM7F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM8F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM9F2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMAF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMBF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMCF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMDF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMEF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMFF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMGF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMHF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMIF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMJF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMKF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMLF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMMF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMNF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMOF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMPF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMQF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMRF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMSF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMTF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMUF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMVF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMWF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMXF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMYF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMZF2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM0G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM1G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM2G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM3G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM4G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM5G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM6G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM7G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM8G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDM9G2', 'FINST-90E66JD1JJHXAXMLBCO7HDSNC6GB3Y847XNDMAG2']
2025-07-29 10:31:07,351 - INFO - 批量插入响应状态码: 200
2025-07-29 10:31:07,351 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E7D866D4-B8B1-707A-A0EF-884C95F2CE32', 'x-acs-trace-id': 'bf581692ab60ad969212f01fad39b97a', 'etag': '2XNC0FMvLwAhhyTdOjCBD+Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:31:07,351 - INFO - 批量插入响应体: {'result': ['FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM53', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM63', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM73', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM83', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM93', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMA3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMB3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMC3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMD3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDME3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMF3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMG3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMH3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMI3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMJ3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMK3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDML3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMM3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMN3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMO3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMP3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMQ3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMR3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMS3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMT3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMU3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMV3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMW3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMX3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMY3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMZ3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM04', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM14', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM24', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM34', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM44', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM54', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM64', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM74', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM84', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM94', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMA4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMB4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMC4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMD4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDME4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMF4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMG4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMH4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMI4']}
2025-07-29 10:31:07,351 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-29 10:31:07,351 - INFO - 成功插入的数据ID: ['FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM53', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM63', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM73', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM83', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM93', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMA3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMB3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMC3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMD3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDME3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMF3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMG3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMH3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMI3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMJ3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMK3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDML3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMM3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMN3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMO3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMP3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMQ3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMR3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMS3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMT3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMU3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMV3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMW3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMX3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMY3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMZ3', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM04', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM14', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM24', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM34', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM44', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM54', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM64', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM74', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM84', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM94', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMA4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMB4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMC4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMD4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDME4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMF4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMG4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMH4', 'FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDMI4']
2025-07-29 10:31:12,569 - INFO - 批量插入响应状态码: 200
2025-07-29 10:31:12,569 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:31:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '924', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8598BB34-F1EF-774A-A3A6-A418612BC935', 'x-acs-trace-id': 'd0c9b00ead3cda6c66a3f81c8722fbfc', 'etag': '9G5hBUoYLG6IezafwNbp0kw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:31:12,569 - INFO - 批量插入响应体: {'result': ['FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMJ1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMK1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDML1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMM1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMN1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMO1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMP1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMQ1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMR1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMS1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMT1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMU1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMV1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMW1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMX1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMY1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMZ1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDM02', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDM12']}
2025-07-29 10:31:12,569 - INFO - 批量插入表单数据成功，批次 3，共 19 条记录
2025-07-29 10:31:12,569 - INFO - 成功插入的数据ID: ['FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMJ1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMK1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDML1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMM1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMN1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMO1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMP1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMQ1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMR1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMS1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMT1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMU1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMV1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMW1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMX1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMY1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMZ1', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDM02', 'FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDM12']
2025-07-29 10:31:17,585 - INFO - 批量插入完成，共 119 条记录
2025-07-29 10:31:17,585 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 119 条，错误: 0 条
2025-07-29 10:31:17,585 - INFO - 开始处理日期: 2025-07-29
2025-07-29 10:31:17,585 - INFO - Request Parameters - Page 1:
2025-07-29 10:31:17,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:31:17,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:31:18,023 - INFO - Response - Page 1:
2025-07-29 10:31:18,023 - INFO - 查询完成，共获取到 0 条记录
2025-07-29 10:31:18,023 - INFO - 获取到 0 条表单数据
2025-07-29 10:31:18,023 - INFO - 当前日期 2025-07-29 有 3 条MySQL数据需要处理
2025-07-29 10:31:18,023 - INFO - 开始批量插入 3 条新记录
2025-07-29 10:31:18,179 - INFO - 批量插入响应状态码: 200
2025-07-29 10:31:18,194 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:31:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AB969E0D-B1FA-7A06-83B9-646EC73B7583', 'x-acs-trace-id': 'f046c8823b516e7c34143bc9c6a0b605', 'etag': '12PaKNGvWIEi/tBBV+Qdf6w3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:31:18,194 - INFO - 批量插入响应体: {'result': ['FINST-G9G669D1L8KXL8W4EA0MT9RWIRSJ2XNG7XNDM7', 'FINST-G9G669D1L8KXL8W4EA0MT9RWIRSJ2XNG7XNDM8', 'FINST-G9G669D1L8KXL8W4EA0MT9RWIRSJ2XNG7XNDM9']}
2025-07-29 10:31:18,194 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-29 10:31:18,194 - INFO - 成功插入的数据ID: ['FINST-G9G669D1L8KXL8W4EA0MT9RWIRSJ2XNG7XNDM7', 'FINST-G9G669D1L8KXL8W4EA0MT9RWIRSJ2XNG7XNDM8', 'FINST-G9G669D1L8KXL8W4EA0MT9RWIRSJ2XNG7XNDM9']
2025-07-29 10:31:23,210 - INFO - 批量插入完成，共 3 条记录
2025-07-29 10:31:23,210 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-29 10:31:23,210 - INFO - 数据同步完成！更新: 0 条，插入: 122 条，错误: 2 条
2025-07-29 10:32:23,225 - INFO - 开始同步昨天与今天的销售数据: 2025-07-28 至 2025-07-29
2025-07-29 10:32:23,225 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-29 10:32:23,225 - INFO - 查询参数: ('2025-07-28', '2025-07-29')
2025-07-29 10:32:23,397 - INFO - MySQL查询成功，时间段: 2025-07-28 至 2025-07-29，共获取 495 条记录
2025-07-29 10:32:23,397 - INFO - 获取到 2 个日期需要处理: ['2025-07-28', '2025-07-29']
2025-07-29 10:32:23,397 - INFO - 开始处理日期: 2025-07-28
2025-07-29 10:32:23,397 - INFO - Request Parameters - Page 1:
2025-07-29 10:32:23,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:32:23,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:32:24,147 - INFO - Response - Page 1:
2025-07-29 10:32:24,147 - INFO - 第 1 页获取到 50 条记录
2025-07-29 10:32:24,647 - INFO - Request Parameters - Page 2:
2025-07-29 10:32:24,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:32:24,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:32:25,303 - INFO - Response - Page 2:
2025-07-29 10:32:25,303 - INFO - 第 2 页获取到 50 条记录
2025-07-29 10:32:25,819 - INFO - Request Parameters - Page 3:
2025-07-29 10:32:25,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:32:25,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:32:26,522 - INFO - Response - Page 3:
2025-07-29 10:32:26,522 - INFO - 第 3 页获取到 50 条记录
2025-07-29 10:32:27,038 - INFO - Request Parameters - Page 4:
2025-07-29 10:32:27,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:32:27,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:32:27,772 - INFO - Response - Page 4:
2025-07-29 10:32:27,772 - INFO - 第 4 页获取到 50 条记录
2025-07-29 10:32:28,288 - INFO - Request Parameters - Page 5:
2025-07-29 10:32:28,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:32:28,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:32:28,975 - INFO - Response - Page 5:
2025-07-29 10:32:28,975 - INFO - 第 5 页获取到 32 条记录
2025-07-29 10:32:29,491 - INFO - 查询完成，共获取到 232 条记录
2025-07-29 10:32:29,491 - INFO - 获取到 232 条表单数据
2025-07-29 10:32:29,491 - INFO - 当前日期 2025-07-28 有 478 条MySQL数据需要处理
2025-07-29 10:32:29,491 - INFO - 开始批量插入 246 条新记录
2025-07-29 10:32:29,725 - INFO - 批量插入响应状态码: 200
2025-07-29 10:32:29,725 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2389', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E1963F82-004D-79F4-A4D7-4E36E558BFF2', 'x-acs-trace-id': '04c3e3f9e173cf15d3e66004c7831a43', 'etag': '2anG/lU/lK0iZcJNuDpcLsg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:32:29,725 - INFO - 批量插入响应体: {'result': ['FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMD', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDME', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMF', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMG', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMH', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMI', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMJ', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMK', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDML', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMM', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMN', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMO', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMP', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMQ', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMR', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMS', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMT', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMU', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMV', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMW', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMX', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMY', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMZ', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM01', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM11', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM21', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM31', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM41', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM51', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM61', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM71', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM81', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM91', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMA1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMB1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMC1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMD1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDME1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMF1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMG1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMH1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMI1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMJ1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMK1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDML1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMM1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMN1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMO1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMP1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMQ1']}
2025-07-29 10:32:29,725 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-29 10:32:29,725 - INFO - 成功插入的数据ID: ['FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMD', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDME', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMF', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMG', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMH', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMI', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMJ', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMK', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDML', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMM', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMN', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMO', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMP', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMQ', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMR', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMS', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMT', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMU', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMV', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMW', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMX', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMY', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMZ', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM01', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM11', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM21', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM31', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM41', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM51', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM61', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM71', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM81', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDM91', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMA1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMB1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMC1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMD1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDME1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMF1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMG1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMH1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMI1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMJ1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMK1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDML1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMM1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMN1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMO1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMP1', 'FINST-NDC66NB1L8KX36SUBLYNM4U57Y4L34VZ8XNDMQ1']
2025-07-29 10:32:34,944 - INFO - 批量插入响应状态码: 200
2025-07-29 10:32:34,944 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:32:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '55E8A108-48EA-7643-B848-76D32CDF0CD0', 'x-acs-trace-id': 'a141f9c2c6bf58653252a048d0ec625a', 'etag': '2E7tWDYghghXqsjlRyKdvUg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:32:34,944 - INFO - 批量插入响应体: {'result': ['FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMM2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMN2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMO2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMP2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMQ2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMR2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMS2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMT2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMU2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMV2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMW2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMX2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMY2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMZ2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM03', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM13', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM23', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM33', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM43', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM53', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM63', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM73', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM83', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM93', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMA3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMB3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMC3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMD3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDME3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMF3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMG3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMH3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMI3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMJ3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMK3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDML3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMM3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMN3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMO3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMP3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMQ3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMR3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMS3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMT3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMU3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMV3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMW3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMX3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMY3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMZ3']}
2025-07-29 10:32:34,944 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-29 10:32:34,944 - INFO - 成功插入的数据ID: ['FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMM2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMN2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMO2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMP2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMQ2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMR2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMS2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMT2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMU2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB24W39XNDMV2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMW2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMX2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMY2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMZ2', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM03', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM13', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM23', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM33', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM43', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM53', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM63', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM73', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM83', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDM93', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMA3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMB3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMC3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMD3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDME3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMF3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMG3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMH3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMI3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMJ3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMK3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDML3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMM3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMN3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMO3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMP3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMQ3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMR3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMS3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMT3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMU3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMV3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMW3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMX3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMY3', 'FINST-I6E66WA1D8JX0DV58G5MNB8Q76UB25W39XNDMZ3']
2025-07-29 10:32:40,178 - INFO - 批量插入响应状态码: 200
2025-07-29 10:32:40,178 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:32:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2376', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B6E56B80-6B68-7824-8033-B21C8AADF226', 'x-acs-trace-id': 'bc7c840b8db740775542df4aa29500b9', 'etag': '2sVYSkWFkwqolnTixyJOU8g6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:32:40,178 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM0', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM1', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM2', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM3', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM4', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM5', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM6', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM7', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM8', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM9', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMA', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMB', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMC', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMD', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDME', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMF', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMG', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMH', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMI', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMJ', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMK', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDML', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMM', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMN', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMO', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMP', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMQ', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMR', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMS', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMT', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMU', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMV', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMW', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMX', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMY', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMZ', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM01', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM11', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM21', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM31', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM41', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM51', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM61', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM71', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM81', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM91', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMA1', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMB1', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMC1', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMD1']}
2025-07-29 10:32:40,178 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-29 10:32:40,178 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM0', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM1', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM2', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM3', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM4', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM5', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM6', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM7', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM8', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12LX79XNDM9', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMA', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMB', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMC', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMD', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDME', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMF', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMG', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMH', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMI', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMJ', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMK', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDML', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMM', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMN', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMO', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMP', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMQ', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMR', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMS', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMT', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMU', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMV', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMW', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMX', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMY', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMZ', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM01', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM11', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM21', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM31', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM41', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM51', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM61', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM71', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM81', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDM91', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMA1', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMB1', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMC1', 'FINST-7PF66CC1UAKXFG4H9ATHD6S29RN12MX79XNDMD1']
2025-07-29 10:32:45,444 - INFO - 批量插入响应状态码: 200
2025-07-29 10:32:45,444 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:32:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '17AF6113-3315-7302-A7E0-CE8ACF1550D4', 'x-acs-trace-id': '5e73a9942aeeb17d73678edbbebba470', 'etag': '2Kxicy+juzVEJdjxkMKcy+Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:32:45,444 - INFO - 批量插入响应体: {'result': ['FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMNN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMON1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMPN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMQN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMRN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMSN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMTN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMUN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMVN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMWN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMXN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMYN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMZN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM0O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM1O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM2O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM3O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM4O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM5O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM6O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM7O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM8O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM9O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMAO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMBO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMCO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMDO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMEO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMFO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMGO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMHO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMIO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMJO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMKO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMLO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMMO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMNO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMOO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMPO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMQO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMRO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMSO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMTO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMUO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMVO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMWO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMXO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMYO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMZO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM0P1']}
2025-07-29 10:32:45,444 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-29 10:32:45,444 - INFO - 成功插入的数据ID: ['FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMNN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMON1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMPN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMQN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMRN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMSN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMTN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMUN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMVN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMWN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63IZB9XNDMXN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMYN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMZN1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM0O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM1O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM2O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM3O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM4O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM5O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM6O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM7O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM8O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM9O1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMAO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMBO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMCO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMDO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMEO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMFO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMGO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMHO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMIO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMJO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMKO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMLO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMMO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMNO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMOO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMPO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMQO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMRO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMSO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMTO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMUO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMVO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMWO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMXO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMYO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMZO1', 'FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDM0P1']
2025-07-29 10:32:50,709 - INFO - 批量插入响应状态码: 200
2025-07-29 10:32:50,709 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 02:32:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2220', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DFA9F6E2-5AFB-7F52-BB9F-ABA344FC3783', 'x-acs-trace-id': '8c0562cbf4bbf5d95e5c6f546f2db790', 'etag': '2iEHefad1801rodc+XjhCiQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 10:32:50,709 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM2S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM3S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM4S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM5S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM6S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM7S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM8S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM9S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMAS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMBS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMCS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMDS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMES', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMFS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMGS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMHS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMIS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMJS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMKS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMLS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMMS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMNS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMOS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMPS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMQS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMRS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMSS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMTS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMUS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMVS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMWS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMXS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMYS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMZS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM0T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM1T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM2T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM3T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM4T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM5T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM6T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM7T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM8T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM9T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMAT', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMBT']}
2025-07-29 10:32:50,709 - INFO - 批量插入表单数据成功，批次 5，共 46 条记录
2025-07-29 10:32:50,709 - INFO - 成功插入的数据ID: ['FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM2S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM3S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM4S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM5S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM6S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM7S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM8S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM9S', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMAS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMBS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMCS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMDS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMES', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMFS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMGS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMHS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMIS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMJS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMKS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMLS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMMS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMNS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMOS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMPS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMQS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMRS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMSS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMTS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMUS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMVS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMWS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMXS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMYS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMZS', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM0T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM1T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM2T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM3T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM4T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM5T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM6T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM7T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM8T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDM9T', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMAT', 'FINST-7PF66N9116IXJLQ2DRWE3BMXKGXX3X1G9XNDMBT']
2025-07-29 10:32:55,725 - INFO - 批量插入完成，共 246 条记录
2025-07-29 10:32:55,725 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 246 条，错误: 0 条
2025-07-29 10:32:55,725 - INFO - 开始处理日期: 2025-07-29
2025-07-29 10:32:55,725 - INFO - Request Parameters - Page 1:
2025-07-29 10:32:55,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 10:32:55,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 10:32:56,209 - INFO - Response - Page 1:
2025-07-29 10:32:56,209 - INFO - 第 1 页获取到 3 条记录
2025-07-29 10:32:56,725 - INFO - 查询完成，共获取到 3 条记录
2025-07-29 10:32:56,725 - INFO - 获取到 3 条表单数据
2025-07-29 10:32:56,725 - INFO - 当前日期 2025-07-29 有 3 条MySQL数据需要处理
2025-07-29 10:32:56,725 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 10:32:56,725 - INFO - 数据同步完成！更新: 0 条，插入: 246 条，错误: 0 条
2025-07-29 10:32:56,725 - INFO - 同步完成
2025-07-29 13:30:33,699 - INFO - 使用默认增量同步（当天更新数据）
2025-07-29 13:30:33,699 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-29 13:30:33,699 - INFO - 查询参数: ('2025-07-29',)
2025-07-29 13:30:33,871 - INFO - MySQL查询成功，增量数据（日期: 2025-07-29），共获取 144 条记录
2025-07-29 13:30:33,871 - INFO - 获取到 4 个日期需要处理: ['2025-07-24', '2025-07-27', '2025-07-28', '2025-07-29']
2025-07-29 13:30:33,871 - INFO - 开始处理日期: 2025-07-24
2025-07-29 13:30:33,871 - INFO - Request Parameters - Page 1:
2025-07-29 13:30:33,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:30:33,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:30:41,996 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5EBA9D59-479E-7178-BEDD-FDE0E9618157 Response: {'code': 'ServiceUnavailable', 'requestid': '5EBA9D59-479E-7178-BEDD-FDE0E9618157', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5EBA9D59-479E-7178-BEDD-FDE0E9618157)
2025-07-29 13:30:41,996 - INFO - 开始处理日期: 2025-07-27
2025-07-29 13:30:41,996 - INFO - Request Parameters - Page 1:
2025-07-29 13:30:41,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:30:41,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:30:42,902 - INFO - Response - Page 1:
2025-07-29 13:30:42,902 - INFO - 第 1 页获取到 50 条记录
2025-07-29 13:30:43,418 - INFO - Request Parameters - Page 2:
2025-07-29 13:30:43,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:30:43,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:30:51,527 - ERROR - 处理日期 2025-07-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8755C4EF-73CB-7E2A-A9AA-DD7F9A0DFC26 Response: {'code': 'ServiceUnavailable', 'requestid': '8755C4EF-73CB-7E2A-A9AA-DD7F9A0DFC26', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8755C4EF-73CB-7E2A-A9AA-DD7F9A0DFC26)
2025-07-29 13:30:51,527 - INFO - 开始处理日期: 2025-07-28
2025-07-29 13:30:51,543 - INFO - Request Parameters - Page 1:
2025-07-29 13:30:51,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:30:51,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:30:57,012 - INFO - Response - Page 1:
2025-07-29 13:30:57,012 - INFO - 第 1 页获取到 50 条记录
2025-07-29 13:30:57,512 - INFO - Request Parameters - Page 2:
2025-07-29 13:30:57,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:30:57,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:30:58,355 - INFO - Response - Page 2:
2025-07-29 13:30:58,355 - INFO - 第 2 页获取到 50 条记录
2025-07-29 13:30:58,871 - INFO - Request Parameters - Page 3:
2025-07-29 13:30:58,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:30:58,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:30:59,621 - INFO - Response - Page 3:
2025-07-29 13:30:59,621 - INFO - 第 3 页获取到 50 条记录
2025-07-29 13:31:00,137 - INFO - Request Parameters - Page 4:
2025-07-29 13:31:00,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:31:00,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:31:00,840 - INFO - Response - Page 4:
2025-07-29 13:31:00,840 - INFO - 第 4 页获取到 50 条记录
2025-07-29 13:31:01,340 - INFO - Request Parameters - Page 5:
2025-07-29 13:31:01,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:31:01,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:31:02,496 - INFO - Response - Page 5:
2025-07-29 13:31:02,496 - INFO - 第 5 页获取到 50 条记录
2025-07-29 13:31:03,012 - INFO - Request Parameters - Page 6:
2025-07-29 13:31:03,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:31:03,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:31:03,699 - INFO - Response - Page 6:
2025-07-29 13:31:03,699 - INFO - 第 6 页获取到 50 条记录
2025-07-29 13:31:04,215 - INFO - Request Parameters - Page 7:
2025-07-29 13:31:04,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:31:04,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:31:05,059 - INFO - Response - Page 7:
2025-07-29 13:31:05,059 - INFO - 第 7 页获取到 50 条记录
2025-07-29 13:31:05,574 - INFO - Request Parameters - Page 8:
2025-07-29 13:31:05,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:31:05,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:31:06,293 - INFO - Response - Page 8:
2025-07-29 13:31:06,293 - INFO - 第 8 页获取到 50 条记录
2025-07-29 13:31:06,809 - INFO - Request Parameters - Page 9:
2025-07-29 13:31:06,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:31:06,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:31:07,512 - INFO - Response - Page 9:
2025-07-29 13:31:07,512 - INFO - 第 9 页获取到 50 条记录
2025-07-29 13:31:08,012 - INFO - Request Parameters - Page 10:
2025-07-29 13:31:08,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:31:08,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:31:08,715 - INFO - Response - Page 10:
2025-07-29 13:31:08,715 - INFO - 第 10 页获取到 28 条记录
2025-07-29 13:31:09,230 - INFO - 查询完成，共获取到 478 条记录
2025-07-29 13:31:09,230 - INFO - 获取到 478 条表单数据
2025-07-29 13:31:09,230 - INFO - 当前日期 2025-07-28 有 136 条MySQL数据需要处理
2025-07-29 13:31:09,230 - INFO - 开始更新记录 - 表单实例ID: FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMZN1
2025-07-29 13:31:09,809 - INFO - 更新表单数据成功: FINST-DIC66I910JHXVGOLFOFWVDWFXAX63JZB9XNDMZN1
2025-07-29 13:31:09,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1.0, 'new_value': 500.0}, {'field': 'total_amount', 'old_value': 1.0, 'new_value': 500.0}, {'field': 'order_count', 'old_value': 500, 'new_value': 1}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-29 13:31:09,809 - INFO - 开始更新记录 - 表单实例ID: FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMT1
2025-07-29 13:31:10,371 - INFO - 更新表单数据成功: FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMT1
2025-07-29 13:31:10,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3012.6}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3012.6}, {'field': 'order_count', 'old_value': 0, 'new_value': 4}]
2025-07-29 13:31:10,371 - INFO - 开始批量插入 11 条新记录
2025-07-29 13:31:10,543 - INFO - 批量插入响应状态码: 200
2025-07-29 13:31:10,543 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 05:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '540', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F192352B-FE90-7845-BD82-906397C0F569', 'x-acs-trace-id': '858ac10beafe8a0eaa33891a4c0a35fc', 'etag': '5mphjCC64Hk5nkJcpOWYlfQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 13:31:10,543 - INFO - 批量插入响应体: {'result': ['FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMJA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMKA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMLA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMMA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMNA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMOA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMPA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMQA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMRA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMSA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMTA']}
2025-07-29 13:31:10,543 - INFO - 批量插入表单数据成功，批次 1，共 11 条记录
2025-07-29 13:31:10,543 - INFO - 成功插入的数据ID: ['FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMJA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMKA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMLA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMMA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMNA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMOA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMPA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMQA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMRA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMSA', 'FINST-Q86667B1KPJX7NSN6Q6VQ4FJ1C8P3P5SM3ODMTA']
2025-07-29 13:31:15,558 - INFO - 批量插入完成，共 11 条记录
2025-07-29 13:31:15,558 - INFO - 日期 2025-07-28 处理完成 - 更新: 2 条，插入: 11 条，错误: 0 条
2025-07-29 13:31:15,558 - INFO - 开始处理日期: 2025-07-29
2025-07-29 13:31:15,558 - INFO - Request Parameters - Page 1:
2025-07-29 13:31:15,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:31:15,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:31:16,105 - INFO - Response - Page 1:
2025-07-29 13:31:16,105 - INFO - 第 1 页获取到 3 条记录
2025-07-29 13:31:16,621 - INFO - 查询完成，共获取到 3 条记录
2025-07-29 13:31:16,621 - INFO - 获取到 3 条表单数据
2025-07-29 13:31:16,621 - INFO - 当前日期 2025-07-29 有 3 条MySQL数据需要处理
2025-07-29 13:31:16,621 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 13:31:16,621 - INFO - 数据同步完成！更新: 2 条，插入: 11 条，错误: 2 条
2025-07-29 13:32:16,636 - INFO - 开始同步昨天与今天的销售数据: 2025-07-28 至 2025-07-29
2025-07-29 13:32:16,636 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-29 13:32:16,636 - INFO - 查询参数: ('2025-07-28', '2025-07-29')
2025-07-29 13:32:16,792 - INFO - MySQL查询成功，时间段: 2025-07-28 至 2025-07-29，共获取 506 条记录
2025-07-29 13:32:16,808 - INFO - 获取到 2 个日期需要处理: ['2025-07-28', '2025-07-29']
2025-07-29 13:32:16,808 - INFO - 开始处理日期: 2025-07-28
2025-07-29 13:32:16,808 - INFO - Request Parameters - Page 1:
2025-07-29 13:32:16,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:16,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:17,527 - INFO - Response - Page 1:
2025-07-29 13:32:17,527 - INFO - 第 1 页获取到 50 条记录
2025-07-29 13:32:18,042 - INFO - Request Parameters - Page 2:
2025-07-29 13:32:18,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:18,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:18,824 - INFO - Response - Page 2:
2025-07-29 13:32:18,824 - INFO - 第 2 页获取到 50 条记录
2025-07-29 13:32:19,324 - INFO - Request Parameters - Page 3:
2025-07-29 13:32:19,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:19,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:20,042 - INFO - Response - Page 3:
2025-07-29 13:32:20,042 - INFO - 第 3 页获取到 50 条记录
2025-07-29 13:32:20,542 - INFO - Request Parameters - Page 4:
2025-07-29 13:32:20,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:20,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:21,246 - INFO - Response - Page 4:
2025-07-29 13:32:21,246 - INFO - 第 4 页获取到 50 条记录
2025-07-29 13:32:21,761 - INFO - Request Parameters - Page 5:
2025-07-29 13:32:21,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:21,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:22,574 - INFO - Response - Page 5:
2025-07-29 13:32:22,574 - INFO - 第 5 页获取到 50 条记录
2025-07-29 13:32:23,089 - INFO - Request Parameters - Page 6:
2025-07-29 13:32:23,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:23,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:23,746 - INFO - Response - Page 6:
2025-07-29 13:32:23,746 - INFO - 第 6 页获取到 50 条记录
2025-07-29 13:32:24,246 - INFO - Request Parameters - Page 7:
2025-07-29 13:32:24,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:24,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:25,011 - INFO - Response - Page 7:
2025-07-29 13:32:25,011 - INFO - 第 7 页获取到 50 条记录
2025-07-29 13:32:25,527 - INFO - Request Parameters - Page 8:
2025-07-29 13:32:25,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:25,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:26,261 - INFO - Response - Page 8:
2025-07-29 13:32:26,261 - INFO - 第 8 页获取到 50 条记录
2025-07-29 13:32:26,777 - INFO - Request Parameters - Page 9:
2025-07-29 13:32:26,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:26,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:27,496 - INFO - Response - Page 9:
2025-07-29 13:32:27,496 - INFO - 第 9 页获取到 50 条记录
2025-07-29 13:32:28,011 - INFO - Request Parameters - Page 10:
2025-07-29 13:32:28,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:28,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:28,730 - INFO - Response - Page 10:
2025-07-29 13:32:28,730 - INFO - 第 10 页获取到 39 条记录
2025-07-29 13:32:29,230 - INFO - 查询完成，共获取到 489 条记录
2025-07-29 13:32:29,230 - INFO - 获取到 489 条表单数据
2025-07-29 13:32:29,230 - INFO - 当前日期 2025-07-28 有 489 条MySQL数据需要处理
2025-07-29 13:32:29,245 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 13:32:29,245 - INFO - 开始处理日期: 2025-07-29
2025-07-29 13:32:29,245 - INFO - Request Parameters - Page 1:
2025-07-29 13:32:29,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 13:32:29,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 13:32:29,714 - INFO - Response - Page 1:
2025-07-29 13:32:29,714 - INFO - 第 1 页获取到 3 条记录
2025-07-29 13:32:30,214 - INFO - 查询完成，共获取到 3 条记录
2025-07-29 13:32:30,214 - INFO - 获取到 3 条表单数据
2025-07-29 13:32:30,214 - INFO - 当前日期 2025-07-29 有 3 条MySQL数据需要处理
2025-07-29 13:32:30,214 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 13:32:30,214 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 13:32:30,214 - INFO - 同步完成
2025-07-29 16:30:33,697 - INFO - 使用默认增量同步（当天更新数据）
2025-07-29 16:30:33,697 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-29 16:30:33,697 - INFO - 查询参数: ('2025-07-29',)
2025-07-29 16:30:33,853 - INFO - MySQL查询成功，增量数据（日期: 2025-07-29），共获取 149 条记录
2025-07-29 16:30:33,853 - INFO - 获取到 4 个日期需要处理: ['2025-07-24', '2025-07-27', '2025-07-28', '2025-07-29']
2025-07-29 16:30:33,853 - INFO - 开始处理日期: 2025-07-24
2025-07-29 16:30:33,853 - INFO - Request Parameters - Page 1:
2025-07-29 16:30:33,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:30:33,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:30:41,993 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 13589B7F-D0E4-7262-95FD-D77EBE5FC9F8 Response: {'code': 'ServiceUnavailable', 'requestid': '13589B7F-D0E4-7262-95FD-D77EBE5FC9F8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 13589B7F-D0E4-7262-95FD-D77EBE5FC9F8)
2025-07-29 16:30:41,993 - INFO - 开始处理日期: 2025-07-27
2025-07-29 16:30:41,993 - INFO - Request Parameters - Page 1:
2025-07-29 16:30:41,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:30:41,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:30:50,118 - ERROR - 处理日期 2025-07-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B426EF2E-704C-7B40-9F90-CD42CD6BEE75 Response: {'code': 'ServiceUnavailable', 'requestid': 'B426EF2E-704C-7B40-9F90-CD42CD6BEE75', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B426EF2E-704C-7B40-9F90-CD42CD6BEE75)
2025-07-29 16:30:50,118 - INFO - 开始处理日期: 2025-07-28
2025-07-29 16:30:50,118 - INFO - Request Parameters - Page 1:
2025-07-29 16:30:50,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:30:50,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:30:56,306 - INFO - Response - Page 1:
2025-07-29 16:30:56,306 - INFO - 第 1 页获取到 50 条记录
2025-07-29 16:30:56,806 - INFO - Request Parameters - Page 2:
2025-07-29 16:30:56,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:30:56,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:30:57,587 - INFO - Response - Page 2:
2025-07-29 16:30:57,587 - INFO - 第 2 页获取到 50 条记录
2025-07-29 16:30:58,087 - INFO - Request Parameters - Page 3:
2025-07-29 16:30:58,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:30:58,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:30:58,821 - INFO - Response - Page 3:
2025-07-29 16:30:58,821 - INFO - 第 3 页获取到 50 条记录
2025-07-29 16:30:59,321 - INFO - Request Parameters - Page 4:
2025-07-29 16:30:59,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:30:59,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:31:00,149 - INFO - Response - Page 4:
2025-07-29 16:31:00,149 - INFO - 第 4 页获取到 50 条记录
2025-07-29 16:31:00,665 - INFO - Request Parameters - Page 5:
2025-07-29 16:31:00,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:31:00,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:31:01,384 - INFO - Response - Page 5:
2025-07-29 16:31:01,384 - INFO - 第 5 页获取到 50 条记录
2025-07-29 16:31:01,884 - INFO - Request Parameters - Page 6:
2025-07-29 16:31:01,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:31:01,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:31:02,634 - INFO - Response - Page 6:
2025-07-29 16:31:02,634 - INFO - 第 6 页获取到 50 条记录
2025-07-29 16:31:03,134 - INFO - Request Parameters - Page 7:
2025-07-29 16:31:03,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:31:03,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:31:03,915 - INFO - Response - Page 7:
2025-07-29 16:31:03,915 - INFO - 第 7 页获取到 50 条记录
2025-07-29 16:31:04,415 - INFO - Request Parameters - Page 8:
2025-07-29 16:31:04,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:31:04,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:31:05,290 - INFO - Response - Page 8:
2025-07-29 16:31:05,290 - INFO - 第 8 页获取到 50 条记录
2025-07-29 16:31:05,806 - INFO - Request Parameters - Page 9:
2025-07-29 16:31:05,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:31:05,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:31:06,556 - INFO - Response - Page 9:
2025-07-29 16:31:06,556 - INFO - 第 9 页获取到 50 条记录
2025-07-29 16:31:07,071 - INFO - Request Parameters - Page 10:
2025-07-29 16:31:07,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:31:07,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:31:07,868 - INFO - Response - Page 10:
2025-07-29 16:31:07,868 - INFO - 第 10 页获取到 39 条记录
2025-07-29 16:31:08,384 - INFO - 查询完成，共获取到 489 条记录
2025-07-29 16:31:08,384 - INFO - 获取到 489 条表单数据
2025-07-29 16:31:08,384 - INFO - 当前日期 2025-07-28 有 141 条MySQL数据需要处理
2025-07-29 16:31:08,384 - INFO - 开始更新记录 - 表单实例ID: FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMP1
2025-07-29 16:31:08,931 - INFO - 更新表单数据成功: FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMP1
2025-07-29 16:31:08,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 6186.0}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 6186.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 25}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/85feefec068f443badd87a8ce2f27bcd.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=YpbUXicOZuuR113TvnKqbDOOWJ8%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/f9b871d3e4e14818b88aaeeaaac91d09.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=B95xQUAVNLCvXFaR2d3sDKF%2FzXo%3D'}]
2025-07-29 16:31:08,931 - INFO - 开始更新记录 - 表单实例ID: FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMK1
2025-07-29 16:31:09,556 - INFO - 更新表单数据成功: FINST-GNC66E91AEJX0RM178XXMDVL0XO52OBC7XNDMK1
2025-07-29 16:31:09,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1003.0, 'new_value': 1659.0}, {'field': 'total_amount', 'old_value': 1003.0, 'new_value': 1659.0}]
2025-07-29 16:31:09,556 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM54
2025-07-29 16:31:10,149 - INFO - 更新表单数据成功: FINST-1MD668B148JXET4BFMTPV5F3AP1X22B87XNDM54
2025-07-29 16:31:10,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 533659.0, 'new_value': 244022.45}, {'field': 'total_amount', 'old_value': 533659.0, 'new_value': 244022.45}, {'field': 'order_count', 'old_value': 2263, 'new_value': 1197}]
2025-07-29 16:31:10,149 - INFO - 开始批量插入 5 条新记录
2025-07-29 16:31:10,337 - INFO - 批量插入响应状态码: 200
2025-07-29 16:31:10,337 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 08:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B289E4FA-EDBA-7D70-B51B-28B3FC24182E', 'x-acs-trace-id': 'b28d9ccf3e8a6e0398eb5a97f729338a', 'etag': '2SqUugYhYGyAx1PUwKWBKyg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 16:31:10,337 - INFO - 批量插入响应体: {'result': ['FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODMA1', 'FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODMB1', 'FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODMC1', 'FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODMD1', 'FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODME1']}
2025-07-29 16:31:10,337 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-07-29 16:31:10,337 - INFO - 成功插入的数据ID: ['FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODMA1', 'FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODMB1', 'FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODMC1', 'FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODMD1', 'FINST-RI7660918FKXUR1KCDDO54MKEH6J30E92AODME1']
2025-07-29 16:31:15,353 - INFO - 批量插入完成，共 5 条记录
2025-07-29 16:31:15,353 - INFO - 日期 2025-07-28 处理完成 - 更新: 3 条，插入: 5 条，错误: 0 条
2025-07-29 16:31:15,353 - INFO - 开始处理日期: 2025-07-29
2025-07-29 16:31:15,353 - INFO - Request Parameters - Page 1:
2025-07-29 16:31:15,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:31:15,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:31:15,853 - INFO - Response - Page 1:
2025-07-29 16:31:15,853 - INFO - 第 1 页获取到 3 条记录
2025-07-29 16:31:16,353 - INFO - 查询完成，共获取到 3 条记录
2025-07-29 16:31:16,353 - INFO - 获取到 3 条表单数据
2025-07-29 16:31:16,353 - INFO - 当前日期 2025-07-29 有 3 条MySQL数据需要处理
2025-07-29 16:31:16,353 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 16:31:16,353 - INFO - 数据同步完成！更新: 3 条，插入: 5 条，错误: 2 条
2025-07-29 16:32:16,368 - INFO - 开始同步昨天与今天的销售数据: 2025-07-28 至 2025-07-29
2025-07-29 16:32:16,368 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-29 16:32:16,368 - INFO - 查询参数: ('2025-07-28', '2025-07-29')
2025-07-29 16:32:16,540 - INFO - MySQL查询成功，时间段: 2025-07-28 至 2025-07-29，共获取 539 条记录
2025-07-29 16:32:16,540 - INFO - 获取到 2 个日期需要处理: ['2025-07-28', '2025-07-29']
2025-07-29 16:32:16,540 - INFO - 开始处理日期: 2025-07-28
2025-07-29 16:32:16,540 - INFO - Request Parameters - Page 1:
2025-07-29 16:32:16,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:16,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:17,305 - INFO - Response - Page 1:
2025-07-29 16:32:17,305 - INFO - 第 1 页获取到 50 条记录
2025-07-29 16:32:17,805 - INFO - Request Parameters - Page 2:
2025-07-29 16:32:17,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:17,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:18,508 - INFO - Response - Page 2:
2025-07-29 16:32:18,508 - INFO - 第 2 页获取到 50 条记录
2025-07-29 16:32:19,008 - INFO - Request Parameters - Page 3:
2025-07-29 16:32:19,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:19,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:19,805 - INFO - Response - Page 3:
2025-07-29 16:32:19,805 - INFO - 第 3 页获取到 50 条记录
2025-07-29 16:32:20,305 - INFO - Request Parameters - Page 4:
2025-07-29 16:32:20,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:20,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:21,008 - INFO - Response - Page 4:
2025-07-29 16:32:21,008 - INFO - 第 4 页获取到 50 条记录
2025-07-29 16:32:21,524 - INFO - Request Parameters - Page 5:
2025-07-29 16:32:21,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:21,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:22,227 - INFO - Response - Page 5:
2025-07-29 16:32:22,227 - INFO - 第 5 页获取到 50 条记录
2025-07-29 16:32:22,743 - INFO - Request Parameters - Page 6:
2025-07-29 16:32:22,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:22,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:23,446 - INFO - Response - Page 6:
2025-07-29 16:32:23,446 - INFO - 第 6 页获取到 50 条记录
2025-07-29 16:32:23,961 - INFO - Request Parameters - Page 7:
2025-07-29 16:32:23,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:23,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:24,727 - INFO - Response - Page 7:
2025-07-29 16:32:24,727 - INFO - 第 7 页获取到 50 条记录
2025-07-29 16:32:25,227 - INFO - Request Parameters - Page 8:
2025-07-29 16:32:25,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:25,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:25,930 - INFO - Response - Page 8:
2025-07-29 16:32:25,930 - INFO - 第 8 页获取到 50 条记录
2025-07-29 16:32:26,446 - INFO - Request Parameters - Page 9:
2025-07-29 16:32:26,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:26,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:27,165 - INFO - Response - Page 9:
2025-07-29 16:32:27,165 - INFO - 第 9 页获取到 50 条记录
2025-07-29 16:32:27,665 - INFO - Request Parameters - Page 10:
2025-07-29 16:32:27,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:27,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:28,336 - INFO - Response - Page 10:
2025-07-29 16:32:28,336 - INFO - 第 10 页获取到 44 条记录
2025-07-29 16:32:28,836 - INFO - 查询完成，共获取到 494 条记录
2025-07-29 16:32:28,836 - INFO - 获取到 494 条表单数据
2025-07-29 16:32:28,836 - INFO - 当前日期 2025-07-28 有 516 条MySQL数据需要处理
2025-07-29 16:32:28,852 - INFO - 开始批量插入 22 条新记录
2025-07-29 16:32:29,055 - INFO - 批量插入响应状态码: 200
2025-07-29 16:32:29,055 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 08:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1068', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C89D5209-636B-792D-9291-0950C7A9FF47', 'x-acs-trace-id': '67a6f43df90783f803c36a49517867f8', 'etag': '1vunBAnTQudNqdw0eNvP52A8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 16:32:29,055 - INFO - 批量插入响应体: {'result': ['FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMN1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMO1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMP1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMQ1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMR1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMS1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMT1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMU1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMV1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMW1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMX1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMY1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMZ1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM02', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM12', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM22', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM32', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM42', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM52', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM62', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM72', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM82']}
2025-07-29 16:32:29,055 - INFO - 批量插入表单数据成功，批次 1，共 22 条记录
2025-07-29 16:32:29,055 - INFO - 成功插入的数据ID: ['FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMN1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMO1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMP1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMQ1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMR1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMS1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMT1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMU1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMV1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMW1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMX1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMY1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODMZ1', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM02', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM12', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM22', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM32', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM42', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM52', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM62', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM72', 'FINST-AI866781HBKX7I68F0UGO4BVNXXA3W4Y3AODM82']
2025-07-29 16:32:34,071 - INFO - 批量插入完成，共 22 条记录
2025-07-29 16:32:34,071 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 22 条，错误: 0 条
2025-07-29 16:32:34,071 - INFO - 开始处理日期: 2025-07-29
2025-07-29 16:32:34,071 - INFO - Request Parameters - Page 1:
2025-07-29 16:32:34,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 16:32:34,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 16:32:34,618 - INFO - Response - Page 1:
2025-07-29 16:32:34,618 - INFO - 第 1 页获取到 3 条记录
2025-07-29 16:32:35,133 - INFO - 查询完成，共获取到 3 条记录
2025-07-29 16:32:35,133 - INFO - 获取到 3 条表单数据
2025-07-29 16:32:35,133 - INFO - 当前日期 2025-07-29 有 4 条MySQL数据需要处理
2025-07-29 16:32:35,133 - INFO - 开始批量插入 1 条新记录
2025-07-29 16:32:35,305 - INFO - 批量插入响应状态码: 200
2025-07-29 16:32:35,305 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 08:32:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '128F4343-3B0A-7C48-9157-43E1B8DDE690', 'x-acs-trace-id': '6d85297542a5fc247ba1bd2f3e46f1aa', 'etag': '6i65ru1sLBkThImfgiOWn7Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 16:32:35,305 - INFO - 批量插入响应体: {'result': ['FINST-4UG66291Z7JXQCN8A5KB29Q8RLXM2DY24AODMD4']}
2025-07-29 16:32:35,305 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-29 16:32:35,305 - INFO - 成功插入的数据ID: ['FINST-4UG66291Z7JXQCN8A5KB29Q8RLXM2DY24AODMD4']
2025-07-29 16:32:40,321 - INFO - 批量插入完成，共 1 条记录
2025-07-29 16:32:40,321 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-29 16:32:40,321 - INFO - 数据同步完成！更新: 0 条，插入: 23 条，错误: 0 条
2025-07-29 16:32:40,321 - INFO - 同步完成
2025-07-29 19:30:34,265 - INFO - 使用默认增量同步（当天更新数据）
2025-07-29 19:30:34,265 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-29 19:30:34,265 - INFO - 查询参数: ('2025-07-29',)
2025-07-29 19:30:34,421 - INFO - MySQL查询成功，增量数据（日期: 2025-07-29），共获取 150 条记录
2025-07-29 19:30:34,421 - INFO - 获取到 4 个日期需要处理: ['2025-07-24', '2025-07-27', '2025-07-28', '2025-07-29']
2025-07-29 19:30:34,421 - INFO - 开始处理日期: 2025-07-24
2025-07-29 19:30:34,421 - INFO - Request Parameters - Page 1:
2025-07-29 19:30:34,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:30:34,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:30:42,440 - INFO - Response - Page 1:
2025-07-29 19:30:42,440 - INFO - 第 1 页获取到 50 条记录
2025-07-29 19:30:42,955 - INFO - Request Parameters - Page 2:
2025-07-29 19:30:42,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:30:42,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:30:43,690 - INFO - Response - Page 2:
2025-07-29 19:30:43,690 - INFO - 第 2 页获取到 50 条记录
2025-07-29 19:30:44,206 - INFO - Request Parameters - Page 3:
2025-07-29 19:30:44,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:30:44,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:30:52,319 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-BD09-7BAF-A257-86580F4EEC9C Response: {'code': 'ServiceUnavailable', 'requestid': '********-BD09-7BAF-A257-86580F4EEC9C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-BD09-7BAF-A257-86580F4EEC9C)
2025-07-29 19:30:52,319 - INFO - 开始处理日期: 2025-07-27
2025-07-29 19:30:52,334 - INFO - Request Parameters - Page 1:
2025-07-29 19:30:52,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:30:52,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:30:53,022 - INFO - Response - Page 1:
2025-07-29 19:30:53,022 - INFO - 第 1 页获取到 50 条记录
2025-07-29 19:30:53,522 - INFO - Request Parameters - Page 2:
2025-07-29 19:30:53,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:30:53,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:30:57,446 - INFO - Response - Page 2:
2025-07-29 19:30:57,446 - INFO - 第 2 页获取到 50 条记录
2025-07-29 19:30:57,946 - INFO - Request Parameters - Page 3:
2025-07-29 19:30:57,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:30:57,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:30:58,759 - INFO - Response - Page 3:
2025-07-29 19:30:58,759 - INFO - 第 3 页获取到 50 条记录
2025-07-29 19:30:59,274 - INFO - Request Parameters - Page 4:
2025-07-29 19:30:59,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:30:59,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:00,040 - INFO - Response - Page 4:
2025-07-29 19:31:00,040 - INFO - 第 4 页获取到 50 条记录
2025-07-29 19:31:00,541 - INFO - Request Parameters - Page 5:
2025-07-29 19:31:00,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:00,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:01,307 - INFO - Response - Page 5:
2025-07-29 19:31:01,307 - INFO - 第 5 页获取到 50 条记录
2025-07-29 19:31:01,807 - INFO - Request Parameters - Page 6:
2025-07-29 19:31:01,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:01,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:02,573 - INFO - Response - Page 6:
2025-07-29 19:31:02,573 - INFO - 第 6 页获取到 50 条记录
2025-07-29 19:31:03,088 - INFO - Request Parameters - Page 7:
2025-07-29 19:31:03,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:03,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:03,823 - INFO - Response - Page 7:
2025-07-29 19:31:03,823 - INFO - 第 7 页获取到 50 条记录
2025-07-29 19:31:04,339 - INFO - Request Parameters - Page 8:
2025-07-29 19:31:04,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:04,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:05,027 - INFO - Response - Page 8:
2025-07-29 19:31:05,027 - INFO - 第 8 页获取到 50 条记录
2025-07-29 19:31:05,527 - INFO - Request Parameters - Page 9:
2025-07-29 19:31:05,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:05,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:06,340 - INFO - Response - Page 9:
2025-07-29 19:31:06,355 - INFO - 第 9 页获取到 50 条记录
2025-07-29 19:31:06,856 - INFO - Request Parameters - Page 10:
2025-07-29 19:31:06,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:06,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:07,637 - INFO - Response - Page 10:
2025-07-29 19:31:07,637 - INFO - 第 10 页获取到 50 条记录
2025-07-29 19:31:08,153 - INFO - Request Parameters - Page 11:
2025-07-29 19:31:08,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:08,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:08,809 - INFO - Response - Page 11:
2025-07-29 19:31:08,809 - INFO - 第 11 页获取到 42 条记录
2025-07-29 19:31:09,310 - INFO - 查询完成，共获取到 542 条记录
2025-07-29 19:31:09,310 - INFO - 获取到 542 条表单数据
2025-07-29 19:31:09,310 - INFO - 当前日期 2025-07-27 有 1 条MySQL数据需要处理
2025-07-29 19:31:09,310 - INFO - 开始批量插入 1 条新记录
2025-07-29 19:31:09,482 - INFO - 批量插入响应状态码: 200
2025-07-29 19:31:09,482 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 11:31:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'ACE03311-F183-7AFB-9594-91FD0BA06946', 'x-acs-trace-id': '95630c40c38fd15defff5d285f50d372', 'etag': '62RsbVfOaVhLTdVU6FNLs3g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 19:31:09,482 - INFO - 批量插入响应体: {'result': ['FINST-BD766BC17FJXPP4JDP4X753E4OG62EUPHGODMP4']}
2025-07-29 19:31:09,482 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-29 19:31:09,482 - INFO - 成功插入的数据ID: ['FINST-BD766BC17FJXPP4JDP4X753E4OG62EUPHGODMP4']
2025-07-29 19:31:14,499 - INFO - 批量插入完成，共 1 条记录
2025-07-29 19:31:14,499 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-29 19:31:14,499 - INFO - 开始处理日期: 2025-07-28
2025-07-29 19:31:14,499 - INFO - Request Parameters - Page 1:
2025-07-29 19:31:14,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:14,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:15,250 - INFO - Response - Page 1:
2025-07-29 19:31:15,250 - INFO - 第 1 页获取到 50 条记录
2025-07-29 19:31:15,765 - INFO - Request Parameters - Page 2:
2025-07-29 19:31:15,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:15,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:16,422 - INFO - Response - Page 2:
2025-07-29 19:31:16,422 - INFO - 第 2 页获取到 50 条记录
2025-07-29 19:31:16,938 - INFO - Request Parameters - Page 3:
2025-07-29 19:31:16,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:16,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:17,657 - INFO - Response - Page 3:
2025-07-29 19:31:17,657 - INFO - 第 3 页获取到 50 条记录
2025-07-29 19:31:18,173 - INFO - Request Parameters - Page 4:
2025-07-29 19:31:18,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:18,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:18,892 - INFO - Response - Page 4:
2025-07-29 19:31:18,892 - INFO - 第 4 页获取到 50 条记录
2025-07-29 19:31:19,392 - INFO - Request Parameters - Page 5:
2025-07-29 19:31:19,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:19,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:20,095 - INFO - Response - Page 5:
2025-07-29 19:31:20,095 - INFO - 第 5 页获取到 50 条记录
2025-07-29 19:31:20,611 - INFO - Request Parameters - Page 6:
2025-07-29 19:31:20,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:20,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:21,330 - INFO - Response - Page 6:
2025-07-29 19:31:21,330 - INFO - 第 6 页获取到 50 条记录
2025-07-29 19:31:21,846 - INFO - Request Parameters - Page 7:
2025-07-29 19:31:21,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:21,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:22,549 - INFO - Response - Page 7:
2025-07-29 19:31:22,549 - INFO - 第 7 页获取到 50 条记录
2025-07-29 19:31:23,065 - INFO - Request Parameters - Page 8:
2025-07-29 19:31:23,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:23,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:23,831 - INFO - Response - Page 8:
2025-07-29 19:31:23,831 - INFO - 第 8 页获取到 50 条记录
2025-07-29 19:31:24,331 - INFO - Request Parameters - Page 9:
2025-07-29 19:31:24,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:24,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:25,003 - INFO - Response - Page 9:
2025-07-29 19:31:25,003 - INFO - 第 9 页获取到 50 条记录
2025-07-29 19:31:25,504 - INFO - Request Parameters - Page 10:
2025-07-29 19:31:25,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:25,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:26,223 - INFO - Response - Page 10:
2025-07-29 19:31:26,223 - INFO - 第 10 页获取到 50 条记录
2025-07-29 19:31:26,723 - INFO - Request Parameters - Page 11:
2025-07-29 19:31:26,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:26,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:27,317 - INFO - Response - Page 11:
2025-07-29 19:31:27,317 - INFO - 第 11 页获取到 16 条记录
2025-07-29 19:31:27,833 - INFO - 查询完成，共获取到 516 条记录
2025-07-29 19:31:27,833 - INFO - 获取到 516 条表单数据
2025-07-29 19:31:27,833 - INFO - 当前日期 2025-07-28 有 141 条MySQL数据需要处理
2025-07-29 19:31:27,833 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 19:31:27,833 - INFO - 开始处理日期: 2025-07-29
2025-07-29 19:31:27,833 - INFO - Request Parameters - Page 1:
2025-07-29 19:31:27,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:31:27,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:31:28,380 - INFO - Response - Page 1:
2025-07-29 19:31:28,380 - INFO - 第 1 页获取到 4 条记录
2025-07-29 19:31:28,880 - INFO - 查询完成，共获取到 4 条记录
2025-07-29 19:31:28,880 - INFO - 获取到 4 条表单数据
2025-07-29 19:31:28,880 - INFO - 当前日期 2025-07-29 有 4 条MySQL数据需要处理
2025-07-29 19:31:28,880 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 19:31:28,880 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-07-29 19:32:28,919 - INFO - 开始同步昨天与今天的销售数据: 2025-07-28 至 2025-07-29
2025-07-29 19:32:28,919 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-29 19:32:28,919 - INFO - 查询参数: ('2025-07-28', '2025-07-29')
2025-07-29 19:32:29,123 - INFO - MySQL查询成功，时间段: 2025-07-28 至 2025-07-29，共获取 539 条记录
2025-07-29 19:32:29,123 - INFO - 获取到 2 个日期需要处理: ['2025-07-28', '2025-07-29']
2025-07-29 19:32:29,123 - INFO - 开始处理日期: 2025-07-28
2025-07-29 19:32:29,123 - INFO - Request Parameters - Page 1:
2025-07-29 19:32:29,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:29,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:29,920 - INFO - Response - Page 1:
2025-07-29 19:32:29,920 - INFO - 第 1 页获取到 50 条记录
2025-07-29 19:32:30,436 - INFO - Request Parameters - Page 2:
2025-07-29 19:32:30,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:30,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:31,186 - INFO - Response - Page 2:
2025-07-29 19:32:31,186 - INFO - 第 2 页获取到 50 条记录
2025-07-29 19:32:31,686 - INFO - Request Parameters - Page 3:
2025-07-29 19:32:31,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:31,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:32,436 - INFO - Response - Page 3:
2025-07-29 19:32:32,436 - INFO - 第 3 页获取到 50 条记录
2025-07-29 19:32:32,937 - INFO - Request Parameters - Page 4:
2025-07-29 19:32:32,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:32,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:33,624 - INFO - Response - Page 4:
2025-07-29 19:32:33,624 - INFO - 第 4 页获取到 50 条记录
2025-07-29 19:32:34,140 - INFO - Request Parameters - Page 5:
2025-07-29 19:32:34,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:34,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:34,844 - INFO - Response - Page 5:
2025-07-29 19:32:34,844 - INFO - 第 5 页获取到 50 条记录
2025-07-29 19:32:35,344 - INFO - Request Parameters - Page 6:
2025-07-29 19:32:35,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:35,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:36,110 - INFO - Response - Page 6:
2025-07-29 19:32:36,110 - INFO - 第 6 页获取到 50 条记录
2025-07-29 19:32:36,626 - INFO - Request Parameters - Page 7:
2025-07-29 19:32:36,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:36,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:37,345 - INFO - Response - Page 7:
2025-07-29 19:32:37,345 - INFO - 第 7 页获取到 50 条记录
2025-07-29 19:32:37,860 - INFO - Request Parameters - Page 8:
2025-07-29 19:32:37,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:37,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:38,611 - INFO - Response - Page 8:
2025-07-29 19:32:38,611 - INFO - 第 8 页获取到 50 条记录
2025-07-29 19:32:39,111 - INFO - Request Parameters - Page 9:
2025-07-29 19:32:39,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:39,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:39,861 - INFO - Response - Page 9:
2025-07-29 19:32:39,861 - INFO - 第 9 页获取到 50 条记录
2025-07-29 19:32:40,361 - INFO - Request Parameters - Page 10:
2025-07-29 19:32:40,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:40,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:41,096 - INFO - Response - Page 10:
2025-07-29 19:32:41,096 - INFO - 第 10 页获取到 50 条记录
2025-07-29 19:32:41,612 - INFO - Request Parameters - Page 11:
2025-07-29 19:32:41,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:41,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:42,190 - INFO - Response - Page 11:
2025-07-29 19:32:42,190 - INFO - 第 11 页获取到 16 条记录
2025-07-29 19:32:42,706 - INFO - 查询完成，共获取到 516 条记录
2025-07-29 19:32:42,706 - INFO - 获取到 516 条表单数据
2025-07-29 19:32:42,706 - INFO - 当前日期 2025-07-28 有 516 条MySQL数据需要处理
2025-07-29 19:32:42,722 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 19:32:42,722 - INFO - 开始处理日期: 2025-07-29
2025-07-29 19:32:42,722 - INFO - Request Parameters - Page 1:
2025-07-29 19:32:42,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 19:32:42,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 19:32:43,284 - INFO - Response - Page 1:
2025-07-29 19:32:43,284 - INFO - 第 1 页获取到 4 条记录
2025-07-29 19:32:43,785 - INFO - 查询完成，共获取到 4 条记录
2025-07-29 19:32:43,785 - INFO - 获取到 4 条表单数据
2025-07-29 19:32:43,785 - INFO - 当前日期 2025-07-29 有 4 条MySQL数据需要处理
2025-07-29 19:32:43,785 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 19:32:43,785 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 19:32:43,785 - INFO - 同步完成
2025-07-29 22:30:34,201 - INFO - 使用默认增量同步（当天更新数据）
2025-07-29 22:30:34,201 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-29 22:30:34,201 - INFO - 查询参数: ('2025-07-29',)
2025-07-29 22:30:34,373 - INFO - MySQL查询成功，增量数据（日期: 2025-07-29），共获取 189 条记录
2025-07-29 22:30:34,373 - INFO - 获取到 7 个日期需要处理: ['2025-07-22', '2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27', '2025-07-28', '2025-07-29']
2025-07-29 22:30:34,373 - INFO - 开始处理日期: 2025-07-22
2025-07-29 22:30:34,373 - INFO - Request Parameters - Page 1:
2025-07-29 22:30:34,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:30:34,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:30:41,876 - INFO - Response - Page 1:
2025-07-29 22:30:41,876 - INFO - 第 1 页获取到 50 条记录
2025-07-29 22:30:42,392 - INFO - Request Parameters - Page 2:
2025-07-29 22:30:42,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:30:42,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:30:50,504 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FC6B833E-53C2-711A-9E23-2947F47B7E7C Response: {'code': 'ServiceUnavailable', 'requestid': 'FC6B833E-53C2-711A-9E23-2947F47B7E7C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FC6B833E-53C2-711A-9E23-2947F47B7E7C)
2025-07-29 22:30:50,504 - INFO - 开始处理日期: 2025-07-24
2025-07-29 22:30:50,504 - INFO - Request Parameters - Page 1:
2025-07-29 22:30:50,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:30:50,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:30:54,365 - INFO - Response - Page 1:
2025-07-29 22:30:54,365 - INFO - 第 1 页获取到 50 条记录
2025-07-29 22:30:54,865 - INFO - Request Parameters - Page 2:
2025-07-29 22:30:54,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:30:54,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:30:55,569 - INFO - Response - Page 2:
2025-07-29 22:30:55,569 - INFO - 第 2 页获取到 50 条记录
2025-07-29 22:30:56,084 - INFO - Request Parameters - Page 3:
2025-07-29 22:30:56,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:30:56,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:30:56,757 - INFO - Response - Page 3:
2025-07-29 22:30:56,757 - INFO - 第 3 页获取到 50 条记录
2025-07-29 22:30:57,272 - INFO - Request Parameters - Page 4:
2025-07-29 22:30:57,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:30:57,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:30:58,038 - INFO - Response - Page 4:
2025-07-29 22:30:58,038 - INFO - 第 4 页获取到 50 条记录
2025-07-29 22:30:58,554 - INFO - Request Parameters - Page 5:
2025-07-29 22:30:58,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:30:58,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:30:59,258 - INFO - Response - Page 5:
2025-07-29 22:30:59,258 - INFO - 第 5 页获取到 50 条记录
2025-07-29 22:30:59,758 - INFO - Request Parameters - Page 6:
2025-07-29 22:30:59,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:30:59,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:00,492 - INFO - Response - Page 6:
2025-07-29 22:31:00,492 - INFO - 第 6 页获取到 50 条记录
2025-07-29 22:31:00,993 - INFO - Request Parameters - Page 7:
2025-07-29 22:31:00,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:00,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:01,696 - INFO - Response - Page 7:
2025-07-29 22:31:01,696 - INFO - 第 7 页获取到 50 条记录
2025-07-29 22:31:02,212 - INFO - Request Parameters - Page 8:
2025-07-29 22:31:02,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:02,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:02,978 - INFO - Response - Page 8:
2025-07-29 22:31:02,978 - INFO - 第 8 页获取到 50 条记录
2025-07-29 22:31:03,478 - INFO - Request Parameters - Page 9:
2025-07-29 22:31:03,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:03,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:04,244 - INFO - Response - Page 9:
2025-07-29 22:31:04,244 - INFO - 第 9 页获取到 50 条记录
2025-07-29 22:31:04,744 - INFO - Request Parameters - Page 10:
2025-07-29 22:31:04,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:04,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:05,448 - INFO - Response - Page 10:
2025-07-29 22:31:05,448 - INFO - 第 10 页获取到 50 条记录
2025-07-29 22:31:05,948 - INFO - Request Parameters - Page 11:
2025-07-29 22:31:05,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:05,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:06,651 - INFO - Response - Page 11:
2025-07-29 22:31:06,651 - INFO - 第 11 页获取到 50 条记录
2025-07-29 22:31:07,151 - INFO - Request Parameters - Page 12:
2025-07-29 22:31:07,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:07,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:07,683 - INFO - Response - Page 12:
2025-07-29 22:31:07,683 - INFO - 第 12 页获取到 2 条记录
2025-07-29 22:31:08,199 - INFO - 查询完成，共获取到 552 条记录
2025-07-29 22:31:08,199 - INFO - 获取到 552 条表单数据
2025-07-29 22:31:08,199 - INFO - 当前日期 2025-07-24 有 2 条MySQL数据需要处理
2025-07-29 22:31:08,199 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMID
2025-07-29 22:31:08,793 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMID
2025-07-29 22:31:08,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 315.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 789.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1104.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 5}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/745850cb2e734311b463d3828355b3b4.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=23M0plvYahoONC%2FB4mb55KDRaKY%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/c5e0c80524d941a081afb2d0b034e5db.png?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=U1de9zVze6neRfYg3vjSRndvU5U%3D'}]
2025-07-29 22:31:08,793 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMJD
2025-07-29 22:31:09,418 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMJD
2025-07-29 22:31:09,418 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3220.0, 'new_value': 4830.0}, {'field': 'total_amount', 'old_value': 3220.0, 'new_value': 4830.0}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/f1789c9646d24106b937a296d5d69b47.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=zt60Q8uENyqnOxsn0xrd2wLGoKE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/43054f0a7a8143219f464e01bb7c2567.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=%2FFf2y7rVdoYvi%2Bz0KE05YfDzOUg%3D'}]
2025-07-29 22:31:09,418 - INFO - 日期 2025-07-24 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-07-29 22:31:09,418 - INFO - 开始处理日期: 2025-07-25
2025-07-29 22:31:09,418 - INFO - Request Parameters - Page 1:
2025-07-29 22:31:09,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:09,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:10,168 - INFO - Response - Page 1:
2025-07-29 22:31:10,168 - INFO - 第 1 页获取到 50 条记录
2025-07-29 22:31:10,684 - INFO - Request Parameters - Page 2:
2025-07-29 22:31:10,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:10,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:11,356 - INFO - Response - Page 2:
2025-07-29 22:31:11,356 - INFO - 第 2 页获取到 50 条记录
2025-07-29 22:31:11,872 - INFO - Request Parameters - Page 3:
2025-07-29 22:31:11,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:11,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:12,607 - INFO - Response - Page 3:
2025-07-29 22:31:12,607 - INFO - 第 3 页获取到 50 条记录
2025-07-29 22:31:13,122 - INFO - Request Parameters - Page 4:
2025-07-29 22:31:13,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:13,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:13,826 - INFO - Response - Page 4:
2025-07-29 22:31:13,826 - INFO - 第 4 页获取到 50 条记录
2025-07-29 22:31:14,342 - INFO - Request Parameters - Page 5:
2025-07-29 22:31:14,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:14,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:15,076 - INFO - Response - Page 5:
2025-07-29 22:31:15,076 - INFO - 第 5 页获取到 50 条记录
2025-07-29 22:31:15,592 - INFO - Request Parameters - Page 6:
2025-07-29 22:31:15,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:15,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:16,311 - INFO - Response - Page 6:
2025-07-29 22:31:16,311 - INFO - 第 6 页获取到 50 条记录
2025-07-29 22:31:16,811 - INFO - Request Parameters - Page 7:
2025-07-29 22:31:16,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:16,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:17,530 - INFO - Response - Page 7:
2025-07-29 22:31:17,530 - INFO - 第 7 页获取到 50 条记录
2025-07-29 22:31:18,046 - INFO - Request Parameters - Page 8:
2025-07-29 22:31:18,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:18,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:18,750 - INFO - Response - Page 8:
2025-07-29 22:31:18,750 - INFO - 第 8 页获取到 50 条记录
2025-07-29 22:31:19,250 - INFO - Request Parameters - Page 9:
2025-07-29 22:31:19,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:19,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:19,985 - INFO - Response - Page 9:
2025-07-29 22:31:19,985 - INFO - 第 9 页获取到 47 条记录
2025-07-29 22:31:20,485 - INFO - 查询完成，共获取到 447 条记录
2025-07-29 22:31:20,485 - INFO - 获取到 447 条表单数据
2025-07-29 22:31:20,485 - INFO - 当前日期 2025-07-25 有 1 条MySQL数据需要处理
2025-07-29 22:31:20,485 - INFO - 开始批量插入 1 条新记录
2025-07-29 22:31:20,641 - INFO - 批量插入响应状态码: 200
2025-07-29 22:31:20,641 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 14:31:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DBCAF288-E8A6-79CE-9062-A590BC5CB10C', 'x-acs-trace-id': 'f0de1534fd5de6eb511d799eee7c5228', 'etag': '6UQTnp75xMWysxT+1GSH0xw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 22:31:20,641 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1TIHXT9JF9TJ4J66C192D3OHCXMODMBW']}
2025-07-29 22:31:20,641 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-29 22:31:20,641 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1TIHXT9JF9TJ4J66C192D3OHCXMODMBW']
2025-07-29 22:31:25,659 - INFO - 批量插入完成，共 1 条记录
2025-07-29 22:31:25,659 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-29 22:31:25,659 - INFO - 开始处理日期: 2025-07-26
2025-07-29 22:31:25,659 - INFO - Request Parameters - Page 1:
2025-07-29 22:31:25,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:25,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:26,425 - INFO - Response - Page 1:
2025-07-29 22:31:26,425 - INFO - 第 1 页获取到 50 条记录
2025-07-29 22:31:26,940 - INFO - Request Parameters - Page 2:
2025-07-29 22:31:26,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:26,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:27,644 - INFO - Response - Page 2:
2025-07-29 22:31:27,644 - INFO - 第 2 页获取到 50 条记录
2025-07-29 22:31:28,160 - INFO - Request Parameters - Page 3:
2025-07-29 22:31:28,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:28,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:28,910 - INFO - Response - Page 3:
2025-07-29 22:31:28,910 - INFO - 第 3 页获取到 50 条记录
2025-07-29 22:31:29,426 - INFO - Request Parameters - Page 4:
2025-07-29 22:31:29,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:29,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:30,129 - INFO - Response - Page 4:
2025-07-29 22:31:30,129 - INFO - 第 4 页获取到 50 条记录
2025-07-29 22:31:30,645 - INFO - Request Parameters - Page 5:
2025-07-29 22:31:30,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:30,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:31,395 - INFO - Response - Page 5:
2025-07-29 22:31:31,395 - INFO - 第 5 页获取到 50 条记录
2025-07-29 22:31:31,896 - INFO - Request Parameters - Page 6:
2025-07-29 22:31:31,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:31,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:32,646 - INFO - Response - Page 6:
2025-07-29 22:31:32,646 - INFO - 第 6 页获取到 50 条记录
2025-07-29 22:31:33,146 - INFO - Request Parameters - Page 7:
2025-07-29 22:31:33,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:33,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:33,865 - INFO - Response - Page 7:
2025-07-29 22:31:33,865 - INFO - 第 7 页获取到 50 条记录
2025-07-29 22:31:34,381 - INFO - Request Parameters - Page 8:
2025-07-29 22:31:34,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:34,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:35,116 - INFO - Response - Page 8:
2025-07-29 22:31:35,116 - INFO - 第 8 页获取到 50 条记录
2025-07-29 22:31:35,616 - INFO - Request Parameters - Page 9:
2025-07-29 22:31:35,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:35,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:36,366 - INFO - Response - Page 9:
2025-07-29 22:31:36,382 - INFO - 第 9 页获取到 50 条记录
2025-07-29 22:31:36,882 - INFO - Request Parameters - Page 10:
2025-07-29 22:31:36,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:36,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:37,429 - INFO - Response - Page 10:
2025-07-29 22:31:37,429 - INFO - 第 10 页获取到 13 条记录
2025-07-29 22:31:37,929 - INFO - 查询完成，共获取到 463 条记录
2025-07-29 22:31:37,929 - INFO - 获取到 463 条表单数据
2025-07-29 22:31:37,929 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-29 22:31:37,929 - INFO - 开始批量插入 1 条新记录
2025-07-29 22:31:38,117 - INFO - 批量插入响应状态码: 200
2025-07-29 22:31:38,117 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 14:31:33 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '714D08BB-26EE-7C94-93A7-4526E320A0B9', 'x-acs-trace-id': '28b7596927297a6474a9b1633485f8f1', 'etag': '6zBu1MfOvLkyuVVrp075KNw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 22:31:38,117 - INFO - 批量插入响应体: {'result': ['FINST-IQE66ZC1NAKXBJXS8N7OGCKM9DBN2UXPXMODM51']}
2025-07-29 22:31:38,117 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-29 22:31:38,117 - INFO - 成功插入的数据ID: ['FINST-IQE66ZC1NAKXBJXS8N7OGCKM9DBN2UXPXMODM51']
2025-07-29 22:31:43,134 - INFO - 批量插入完成，共 1 条记录
2025-07-29 22:31:43,134 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-29 22:31:43,134 - INFO - 开始处理日期: 2025-07-27
2025-07-29 22:31:43,134 - INFO - Request Parameters - Page 1:
2025-07-29 22:31:43,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:43,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:43,885 - INFO - Response - Page 1:
2025-07-29 22:31:43,885 - INFO - 第 1 页获取到 50 条记录
2025-07-29 22:31:44,401 - INFO - Request Parameters - Page 2:
2025-07-29 22:31:44,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:44,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:45,073 - INFO - Response - Page 2:
2025-07-29 22:31:45,073 - INFO - 第 2 页获取到 50 条记录
2025-07-29 22:31:45,588 - INFO - Request Parameters - Page 3:
2025-07-29 22:31:45,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:45,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:46,370 - INFO - Response - Page 3:
2025-07-29 22:31:46,370 - INFO - 第 3 页获取到 50 条记录
2025-07-29 22:31:46,870 - INFO - Request Parameters - Page 4:
2025-07-29 22:31:46,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:46,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:47,558 - INFO - Response - Page 4:
2025-07-29 22:31:47,558 - INFO - 第 4 页获取到 50 条记录
2025-07-29 22:31:48,074 - INFO - Request Parameters - Page 5:
2025-07-29 22:31:48,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:48,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:48,840 - INFO - Response - Page 5:
2025-07-29 22:31:48,840 - INFO - 第 5 页获取到 50 条记录
2025-07-29 22:31:49,371 - INFO - Request Parameters - Page 6:
2025-07-29 22:31:49,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:49,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:50,075 - INFO - Response - Page 6:
2025-07-29 22:31:50,075 - INFO - 第 6 页获取到 50 条记录
2025-07-29 22:31:50,590 - INFO - Request Parameters - Page 7:
2025-07-29 22:31:50,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:50,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:51,278 - INFO - Response - Page 7:
2025-07-29 22:31:51,278 - INFO - 第 7 页获取到 50 条记录
2025-07-29 22:31:51,794 - INFO - Request Parameters - Page 8:
2025-07-29 22:31:51,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:51,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:52,482 - INFO - Response - Page 8:
2025-07-29 22:31:52,482 - INFO - 第 8 页获取到 50 条记录
2025-07-29 22:31:52,998 - INFO - Request Parameters - Page 9:
2025-07-29 22:31:52,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:52,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:53,764 - INFO - Response - Page 9:
2025-07-29 22:31:53,764 - INFO - 第 9 页获取到 50 条记录
2025-07-29 22:31:54,279 - INFO - Request Parameters - Page 10:
2025-07-29 22:31:54,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:54,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:54,983 - INFO - Response - Page 10:
2025-07-29 22:31:54,983 - INFO - 第 10 页获取到 50 条记录
2025-07-29 22:31:55,483 - INFO - Request Parameters - Page 11:
2025-07-29 22:31:55,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:55,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:56,140 - INFO - Response - Page 11:
2025-07-29 22:31:56,140 - INFO - 第 11 页获取到 43 条记录
2025-07-29 22:31:56,655 - INFO - 查询完成，共获取到 543 条记录
2025-07-29 22:31:56,655 - INFO - 获取到 543 条表单数据
2025-07-29 22:31:56,655 - INFO - 当前日期 2025-07-27 有 1 条MySQL数据需要处理
2025-07-29 22:31:56,655 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 22:31:56,655 - INFO - 开始处理日期: 2025-07-28
2025-07-29 22:31:56,655 - INFO - Request Parameters - Page 1:
2025-07-29 22:31:56,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:56,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:57,374 - INFO - Response - Page 1:
2025-07-29 22:31:57,374 - INFO - 第 1 页获取到 50 条记录
2025-07-29 22:31:57,890 - INFO - Request Parameters - Page 2:
2025-07-29 22:31:57,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:57,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:58,562 - INFO - Response - Page 2:
2025-07-29 22:31:58,578 - INFO - 第 2 页获取到 50 条记录
2025-07-29 22:31:59,078 - INFO - Request Parameters - Page 3:
2025-07-29 22:31:59,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:31:59,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:31:59,719 - INFO - Response - Page 3:
2025-07-29 22:31:59,719 - INFO - 第 3 页获取到 50 条记录
2025-07-29 22:32:00,235 - INFO - Request Parameters - Page 4:
2025-07-29 22:32:00,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:00,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:00,907 - INFO - Response - Page 4:
2025-07-29 22:32:00,907 - INFO - 第 4 页获取到 50 条记录
2025-07-29 22:32:01,423 - INFO - Request Parameters - Page 5:
2025-07-29 22:32:01,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:01,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:02,189 - INFO - Response - Page 5:
2025-07-29 22:32:02,189 - INFO - 第 5 页获取到 50 条记录
2025-07-29 22:32:02,705 - INFO - Request Parameters - Page 6:
2025-07-29 22:32:02,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:02,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:03,439 - INFO - Response - Page 6:
2025-07-29 22:32:03,439 - INFO - 第 6 页获取到 50 条记录
2025-07-29 22:32:03,939 - INFO - Request Parameters - Page 7:
2025-07-29 22:32:03,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:03,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:04,690 - INFO - Response - Page 7:
2025-07-29 22:32:04,690 - INFO - 第 7 页获取到 50 条记录
2025-07-29 22:32:05,190 - INFO - Request Parameters - Page 8:
2025-07-29 22:32:05,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:05,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:05,909 - INFO - Response - Page 8:
2025-07-29 22:32:05,925 - INFO - 第 8 页获取到 50 条记录
2025-07-29 22:32:06,425 - INFO - Request Parameters - Page 9:
2025-07-29 22:32:06,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:06,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:07,097 - INFO - Response - Page 9:
2025-07-29 22:32:07,097 - INFO - 第 9 页获取到 50 条记录
2025-07-29 22:32:07,613 - INFO - Request Parameters - Page 10:
2025-07-29 22:32:07,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:07,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:08,316 - INFO - Response - Page 10:
2025-07-29 22:32:08,316 - INFO - 第 10 页获取到 50 条记录
2025-07-29 22:32:08,816 - INFO - Request Parameters - Page 11:
2025-07-29 22:32:08,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:08,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:09,489 - INFO - Response - Page 11:
2025-07-29 22:32:09,489 - INFO - 第 11 页获取到 16 条记录
2025-07-29 22:32:10,004 - INFO - 查询完成，共获取到 516 条记录
2025-07-29 22:32:10,004 - INFO - 获取到 516 条表单数据
2025-07-29 22:32:10,004 - INFO - 当前日期 2025-07-28 有 141 条MySQL数据需要处理
2025-07-29 22:32:10,004 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 22:32:10,004 - INFO - 开始处理日期: 2025-07-29
2025-07-29 22:32:10,004 - INFO - Request Parameters - Page 1:
2025-07-29 22:32:10,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:32:10,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:32:10,583 - INFO - Response - Page 1:
2025-07-29 22:32:10,583 - INFO - 第 1 页获取到 4 条记录
2025-07-29 22:32:11,099 - INFO - 查询完成，共获取到 4 条记录
2025-07-29 22:32:11,099 - INFO - 获取到 4 条表单数据
2025-07-29 22:32:11,099 - INFO - 当前日期 2025-07-29 有 39 条MySQL数据需要处理
2025-07-29 22:32:11,099 - INFO - 开始批量插入 35 条新记录
2025-07-29 22:32:11,317 - INFO - 批量插入响应状态码: 200
2025-07-29 22:32:11,317 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 14:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1692', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7A3B03AA-7836-7745-99DF-E45C854BFD66', 'x-acs-trace-id': '371ad139b6927a651f91e8a3917c4c24', 'etag': '1ZNmexTDTKrD/qly1NhPCvA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-29 22:32:11,317 - INFO - 批量插入响应体: {'result': ['FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMVD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMWD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMXD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMYD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMZD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM0E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM1E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM2E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM3E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM4E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM5E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM6E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM7E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM8E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM9E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMAE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMBE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMCE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMDE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMEE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMFE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMGE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMHE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMIE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMJE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMKE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMLE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMME', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMNE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMOE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMPE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMQE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMRE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMSE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMTE']}
2025-07-29 22:32:11,317 - INFO - 批量插入表单数据成功，批次 1，共 35 条记录
2025-07-29 22:32:11,317 - INFO - 成功插入的数据ID: ['FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMVD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMWD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMXD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMYD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMZD', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM0E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM1E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM2E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM3E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM4E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM5E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM6E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM7E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM8E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODM9E', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMAE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMBE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMCE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMDE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMEE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMFE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMGE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMHE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMIE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMJE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMKE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMLE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMME', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMNE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMOE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMPE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMQE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMRE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMSE', 'FINST-UW966371E6JXTHOY5FO8144DJZ3S2JKFYMODMTE']
2025-07-29 22:32:16,335 - INFO - 批量插入完成，共 35 条记录
2025-07-29 22:32:16,335 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 35 条，错误: 0 条
2025-07-29 22:32:16,335 - INFO - 数据同步完成！更新: 2 条，插入: 37 条，错误: 1 条
2025-07-29 22:33:16,374 - INFO - 开始同步昨天与今天的销售数据: 2025-07-28 至 2025-07-29
2025-07-29 22:33:16,374 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-29 22:33:16,374 - INFO - 查询参数: ('2025-07-28', '2025-07-29')
2025-07-29 22:33:16,546 - INFO - MySQL查询成功，时间段: 2025-07-28 至 2025-07-29，共获取 574 条记录
2025-07-29 22:33:16,546 - INFO - 获取到 2 个日期需要处理: ['2025-07-28', '2025-07-29']
2025-07-29 22:33:16,546 - INFO - 开始处理日期: 2025-07-28
2025-07-29 22:33:16,546 - INFO - Request Parameters - Page 1:
2025-07-29 22:33:16,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:16,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:17,359 - INFO - Response - Page 1:
2025-07-29 22:33:17,359 - INFO - 第 1 页获取到 50 条记录
2025-07-29 22:33:17,859 - INFO - Request Parameters - Page 2:
2025-07-29 22:33:17,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:17,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:18,610 - INFO - Response - Page 2:
2025-07-29 22:33:18,610 - INFO - 第 2 页获取到 50 条记录
2025-07-29 22:33:19,110 - INFO - Request Parameters - Page 3:
2025-07-29 22:33:19,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:19,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:19,876 - INFO - Response - Page 3:
2025-07-29 22:33:19,876 - INFO - 第 3 页获取到 50 条记录
2025-07-29 22:33:20,376 - INFO - Request Parameters - Page 4:
2025-07-29 22:33:20,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:20,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:21,079 - INFO - Response - Page 4:
2025-07-29 22:33:21,079 - INFO - 第 4 页获取到 50 条记录
2025-07-29 22:33:21,595 - INFO - Request Parameters - Page 5:
2025-07-29 22:33:21,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:21,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:22,361 - INFO - Response - Page 5:
2025-07-29 22:33:22,361 - INFO - 第 5 页获取到 50 条记录
2025-07-29 22:33:22,877 - INFO - Request Parameters - Page 6:
2025-07-29 22:33:22,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:22,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:23,549 - INFO - Response - Page 6:
2025-07-29 22:33:23,549 - INFO - 第 6 页获取到 50 条记录
2025-07-29 22:33:24,065 - INFO - Request Parameters - Page 7:
2025-07-29 22:33:24,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:24,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:24,784 - INFO - Response - Page 7:
2025-07-29 22:33:24,784 - INFO - 第 7 页获取到 50 条记录
2025-07-29 22:33:25,300 - INFO - Request Parameters - Page 8:
2025-07-29 22:33:25,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:25,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:25,941 - INFO - Response - Page 8:
2025-07-29 22:33:25,941 - INFO - 第 8 页获取到 50 条记录
2025-07-29 22:33:26,457 - INFO - Request Parameters - Page 9:
2025-07-29 22:33:26,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:26,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:27,144 - INFO - Response - Page 9:
2025-07-29 22:33:27,144 - INFO - 第 9 页获取到 50 条记录
2025-07-29 22:33:27,660 - INFO - Request Parameters - Page 10:
2025-07-29 22:33:27,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:27,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:28,426 - INFO - Response - Page 10:
2025-07-29 22:33:28,426 - INFO - 第 10 页获取到 50 条记录
2025-07-29 22:33:28,942 - INFO - Request Parameters - Page 11:
2025-07-29 22:33:28,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:28,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:29,520 - INFO - Response - Page 11:
2025-07-29 22:33:29,520 - INFO - 第 11 页获取到 16 条记录
2025-07-29 22:33:30,020 - INFO - 查询完成，共获取到 516 条记录
2025-07-29 22:33:30,020 - INFO - 获取到 516 条表单数据
2025-07-29 22:33:30,020 - INFO - 当前日期 2025-07-28 有 516 条MySQL数据需要处理
2025-07-29 22:33:30,036 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 22:33:30,036 - INFO - 开始处理日期: 2025-07-29
2025-07-29 22:33:30,036 - INFO - Request Parameters - Page 1:
2025-07-29 22:33:30,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-29 22:33:30,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-29 22:33:30,771 - INFO - Response - Page 1:
2025-07-29 22:33:30,771 - INFO - 第 1 页获取到 39 条记录
2025-07-29 22:33:31,287 - INFO - 查询完成，共获取到 39 条记录
2025-07-29 22:33:31,287 - INFO - 获取到 39 条表单数据
2025-07-29 22:33:31,287 - INFO - 当前日期 2025-07-29 有 39 条MySQL数据需要处理
2025-07-29 22:33:31,287 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 22:33:31,287 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-29 22:33:31,287 - INFO - 同步完成
